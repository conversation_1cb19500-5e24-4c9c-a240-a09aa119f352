package cn.casair.service;
import cn.casair.domain.HrArchivesDetail;
import cn.casair.dto.HrArchivesDetailDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 档案明细服务类
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
public interface HrArchivesDetailService extends IService<HrArchivesDetail> {


    /**
     * 创建档案明细
     * @param hrArchivesDetailDTO
     * @return
     */
    HrArchivesDetailDTO createHrArchivesDetail(HrArchivesDetailDTO hrArchivesDetailDTO);

    /**
     * 修改档案明细
     * @param hrArchivesDetailDTO
     * @return
     */
    Optional<HrArchivesDetailDTO> updateHrArchivesDetail(HrArchivesDetailDTO hrArchivesDetailDTO);

    /**
     * 查询档案明细详情
     * @param id
     * @return
     */
    HrArchivesDetailDTO getHrArchivesDetail(String id);

    /**
     * 删除档案明细
     * @param id
     */
    void deleteHrArchivesDetail(String id);

    /**
     * 批量删除档案明细
     * @param ids
     */
    void deleteHrArchivesDetail(List<String> ids);

    /**
     * 分页查询档案明细
     * @param hrArchivesDetailDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrArchivesDetailDTO hrArchivesDetailDTO,Long pageNumber,Long pageSize);
    }
