**简要描述：**

- $!{table.comment} 接口文档

**请求URL：**
- `/${table.entityPath}/get${entity}/{id}  `

**请求方式：**
- GET

**请求示例**

```

```


**参数：**

|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
#foreach($field in ${table.fields})
|${field.propertyName} |是  |${field.propertyType} |${field.comment}   |
#end

**返回示例**

```
{
    "status": 200,
    "msg": "操作成功",
    "data": {
#foreach($field in ${table.fields})
        "${field.propertyName}":"" //${field.comment}
#end
    }
}

```

**返回参数说明**

|参数名|类型|说明|
|:-----  |:-----|-----                           |


**备注**

- 更多返回错误代码请看首页的错误代码描述

