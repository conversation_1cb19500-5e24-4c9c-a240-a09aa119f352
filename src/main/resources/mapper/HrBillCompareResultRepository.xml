<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillCompareResultRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, bill_compare_config_id, system_data, source_data, compare_result, diff_data_nums, status,
        is_delete, created_by, last_modified_by, created_date, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillCompareResult">
        <id column="id" property="id"/>
        <result column="bill_compare_config_id" property="billCompareConfigId"/>
        <result column="system_data" property="systemData"/>
        <result column="source_data" property="sourceData"/>
        <result column="compare_result" property="compareResult"/>
        <result column="diff_data_nums" property="diffDataNums"/>
        <result column="status" property="status"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Find_SQL">
        SELECT
            hbcr.id,
            hbcc.id AS billCompareConfigId,
            hbcr.diff_data_nums,
            hbcc.type,
            hbcc.center_name,
            hb.pay_year,
            hb.pay_monthly,
            hb.bill_type,
            IF(hbcc.type = 5, hbcc.title, hb.title) title,
            IF( hb.title LIKE '全部公司%', '全部公司', IF(hbcc.type = 5,hc2.client_name,hc.client_name)) client_name,
            hbcr.status,
            hbcr.created_date,
            hbcr.lock_status,
            hbcr.current_month_used
        FROM
        hr_bill_compare_result hbcr
        LEFT JOIN hr_bill_compare_config hbcc ON hbcc.id = hbcr.bill_compare_config_id
        LEFT JOIN hr_bill hb ON hb.id = hbcc.bill_id
        LEFT JOIN hr_client hc ON hc.id = hb.client_id
        LEFT JOIN hr_client hc2 ON hbcc.client_id = hc2.id
        WHERE
        hbcr.is_delete = 0
        <if test="params.clientId != null and params.clientId != ''">
            AND hb.client_id = #{params.clientId}
        </if>
        <if test="params.clientIds != null and params.clientIds.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.billCompareConfigIds != null and params.billCompareConfigIds.size()>0">
            AND hbcr.bill_compare_config_id IN
            <foreach collection="params.billCompareConfigIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.paymentDate != null and params.paymentDate != ''">
            AND hb.pay_year = #{params.payYear} AND hb.pay_monthly = #{params.payMonthly}
        </if>
        <if test="params.billType != null">
            AND hb.bill_type = #{params.billType}
        </if>
        <if test="params.type != null">
            AND hbcc.type = #{params.type}
        </if>
        <if test="params.lockStatus != null">
            AND hbcr.lock_status = #{params.lockStatus}
        </if>
        <if test="params.status != null">
            AND hbcr.status = #{params.status}
        </if>
        <choose>
            <when test="params.type == 5">
                <if test="params.speSaveFlag == null">
                    AND hbcc.is_delete = 0
                </if>
                <if test="params.title != null and params.title != ''">
                    AND hbcc.title LIKE CONCAT('%', #{params.title}, '%')
                </if>
            </when>
            <otherwise>
                <if test="params.title != null and params.title != ''">
                    AND hb.title LIKE CONCAT('%', #{params.title}, '%')
                </if>
            </otherwise>
        </choose>
        <if test="params.exportIds != null and params.exportIds.size()>0">
            AND hbcr.id IN
            <foreach collection="params.exportIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.ids != null and params.ids.size()>0">
            AND hbcr.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY hb.pay_year ${params.order}, hb.pay_monthly ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY
                hbcr.created_date DESC
            </otherwise>
        </choose>
    </sql>

    <select id="getBilCompareResultByBillConfigIds" resultType="cn.casair.domain.HrBillCompareResult">
        SELECT *
        FROM hr_bill_compare_result hr
        WHERE hr.bill_compare_config_id IN
        <foreach collection="billCompareConfigIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="findPage" resultType="cn.casair.dto.HrBillCompareResultDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="findList" resultType="cn.casair.dto.HrBillCompareResultDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="getBillCompareResult" resultType="cn.casair.dto.HrBillCompareResultDTO">
        SELECT
            hbcr.*,
            hb.pay_year,
            hb.pay_monthly,
            hbcc.bill_id,
            hbcc.type,
            hb.title
        FROM
            hr_bill_compare_result hbcr
            LEFT JOIN hr_bill_compare_config hbcc ON hbcc.id = hbcr.bill_compare_config_id
            LEFT JOIN hr_bill hb ON hb.id = hbcc.bill_id
        WHERE
            hbcr.is_delete = 0 AND hbcc.is_delete = 0 AND hb.is_delete = 0 AND hbcr.`status` = 1
            AND ((hb.pay_year = #{payYear} AND hb.pay_monthly = #{payMonthly}) OR (hb.pay_year = #{lastPayYear} AND hb.pay_monthly = #{lastPayMonthly}))
            AND hbcc.type IN
            <foreach collection="typeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>

    <select id="getIsUsedBillId" resultType="cn.casair.dto.HrBillCompareConfigDTO">
        SELECT
            hbcc.*
        FROM
            hr_bill_compare_config hbcc
            LEFT JOIN hr_bill_compare_result hbcr ON hbcr.bill_compare_config_id = hbcc.id
            LEFT JOIN hr_bill hb ON hbcc.bill_id = hb.id
        WHERE
            hbcc.is_delete = 0
            AND hb.is_delete = 0
            AND hbcr.is_delete = 0
            AND (hbcr.`status` = 1 OR hbcr.lock_status = 1)
            AND hb.bill_type = #{billType}
            AND hb.pay_year = #{payYear}
            AND hb.pay_monthly = #{payMonthly}
    </select>

    <update id="delByBillIdAndType">
        UPDATE hr_bill_compare_result hr
        INNER JOIN hr_bill_compare_config hc ON hr.bill_compare_config_id = hc.id
        SET hr.is_delete = #{isDelete}
        WHERE  hc.`type` = #{type}
        <if test="billId != null and billId != ''">
            AND hc.bill_id = #{billId}
        </if>
        <if test="billConfigIds != null and billConfigIds.size()>0">
            AND hc.id IN
            <foreach collection="billConfigIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
    </update>
    <update id="updateConfigLockStatus">
        UPDATE hr_bill_compare_result SET lock_status = #{lockStatus}, last_modified_date = now()
        WHERE is_delete = 0 AND bill_compare_config_id IN
        <foreach collection="configIds" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </update>

    <update id="updateByObject">
        UPDATE
            hr_bill_compare_result
        SET status = #{param.status},
            lock_status = #{param.lockStatus},
            current_month_used = #{param.currentMonthUsed},
            last_modified_date = now()
        WHERE id IN
        <foreach collection="param.ids" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </update>

    <delete id="delBatchIds">
        UPDATE hr_bill_compare_result SET is_delete = 1, last_modified_by = #{lastModifiedBy}, last_modified_date = now()
        WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </delete>

</mapper>
