package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.export.ExcelExportService;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.BillEnum;
import cn.casair.common.enums.DynamicFeeTypesEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.EntityUtils;
import cn.casair.common.utils.EnumUtils;
import cn.casair.common.utils.excel.ExcelSheetEntity;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.HrBillCompareConfig;
import cn.casair.domain.HrBillCompareResult;
import cn.casair.domain.HrBillCompareResultDetail;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrCompareHaierDetailExport;
import cn.casair.dto.excel.HrCompareHaierTotalExport;
import cn.casair.mapper.HrBillCompareConfigMapper;
import cn.casair.mapper.HrBillCompareResultMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 对账结果服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class HrBillCompareResultServiceImpl extends ServiceImpl<HrBillCompareResultRepository, HrBillCompareResult> implements HrBillCompareResultService {


    private final HrBillCompareResultRepository hrBillCompareResultRepository;
    private final HrBillCompareResultDetailService hrBillCompareResultDetailService;
    private final HrBillCompareResultMapper hrBillCompareResultMapper;
    private final HrBillCompareConfigRepository hrBillCompareConfigRepository;
    private final HrBillCompareConfigMapper hrBillCompareConfigMapper;
    private final HrAppendixService hrAppendixService;
    private final HrBillRepository hrBillRepository;
    private final HrBillCompareResultDetailRepository hrBillCompareResultDetailRepository;
    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final SysOperLogService sysOperLogService;
    private final HrBillReimbursementApplyService hrBillReimbursementApplyService;
    @Value("${file.temp-path}")
    private String fileTempPath;

    @Override
    public String exportCompareResult(HrBillCompareResultDTO hrBillCompareResultDTO) {
        if (hrBillCompareResultDTO.getBillCompareConfigIds() == null) {
            throw new CommonException("请选择对账结果数据!");
        }
        List<HrBillCompareResult> hrBillCompareResults = this.hrBillCompareResultRepository.getBilCompareResultByBillConfigIds(hrBillCompareResultDTO.getBillCompareConfigIds());
        return this.dealLoopAndDownload(hrBillCompareResults);
    }

    @Override
    public String batchExportCompareResult(HrBillCompareResultDTO hrBillCompareResultDTO) {
        if (hrBillCompareResultDTO.getIds() == null || hrBillCompareResultDTO.getIds().isEmpty()) {
            throw new CommonException("请选择对账结果数据!");
        }
        List<HrBillCompareResult> hrBillCompareResults = this.hrBillCompareResultRepository.selectBatchIds(hrBillCompareResultDTO.getIds());
        return this.dealLoopAndDownload(hrBillCompareResults);
    }

    /**
     * 处理循环与下载
     *
     * @param hrBillCompareResults
     * @return
     */
    private String dealLoopAndDownload(List<HrBillCompareResult> hrBillCompareResults) {
        if (hrBillCompareResults.isEmpty()) {
            throw new CommonException("未查询到相关数据!");
        }
        List<File> fileList = new ArrayList<>();
        hrBillCompareResults.forEach(hrBillCompareResult -> {
            String filePath = this.dealBillCompareResultExport(hrBillCompareResult);
            if (filePath != null) {
                fileList.add(new File(filePath));
            }
        });
        // 压缩下载文件
        return this.hrAppendixService.zipAndUploadFile(fileList, "对账单");
    }

    /**
     * 处理对账结果导出Excel
     *
     * @param hrBillCompareResult
     * @return
     */
    private String dealBillCompareResultExport(HrBillCompareResult hrBillCompareResult) {
        HrBillCompareConfigDTO hrBillCompareConfig = this.hrBillCompareConfigRepository.getBillCompareConfigById(hrBillCompareResult.getBillCompareConfigId());
        // 获取对账结果
        List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailList = this.hrBillCompareResultDetailService.selectHrBillCompareResultDetailByBillCompareResultId(hrBillCompareResult.getId());
        // 制作动态表头
        List<DynamicHeadersDTO> dynamicHeaders = this.hrBillCompareResultDetailService.makeDynamicHeaders(hrBillCompareResultDetailList, hrBillCompareConfig.getType());
        // 处理表头
        List<ExcelExportEntity> colList = new ArrayList<>();
        this.handleExcelExportEntity(colList, dynamicHeaders);
        // 处理数据列表
        List<Map<String, Object>> dynamicData = new LinkedList<>();
        List<String> includeList = dynamicHeaders.stream().map(DynamicHeadersDTO::getKey).collect(Collectors.toList());
        includeList.remove("systemData");
        includeList.remove("sourceData");
        includeList.remove("compareResult");
        hrBillCompareResultDetailList.forEach(resultDetail -> {
            Map<String, Object> valueMap = EntityUtils.object2Map(resultDetail, includeList);
            valueMap.put("paymentDate", resultDetail.getPaymentDate());

            if (resultDetail.getSystemDataObject() != null) {
                this.dealDynamicDataChildrenForExport(valueMap, resultDetail.getSystemDataObject(), "systemData");
            }
            if (resultDetail.getSourceDataObject() != null) {
                this.dealDynamicDataChildrenForExport(valueMap, resultDetail.getSourceDataObject(), "sourceData");
            }
            if (resultDetail.getCompareResultObject() != null) {
                this.dealDynamicDataChildrenForExport(valueMap, resultDetail.getCompareResultObject(), "compareData");
            }
            dynamicData.add(valueMap);
        });

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        ExcelExportService service = new ExcelExportService();
        String title = "";
        BillEnum.GuidePlateType enumByKey = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, hrBillCompareConfig.getType());
        switch (enumByKey) {
            case SOCIAL_SECURITY_GUIDE:
                title = hrBillCompareConfig.getTitle() + "社保对账结果";
                break;
            case MEDICAL_GUIDE:
                title = hrBillCompareConfig.getTitle() + "医保对账结果";
                break;
            case ACCUMULATION_FUND_GUIDE:
                title = hrBillCompareConfig.getTitle() + "公积金对账结果";
                break;
            case PERSONAL_TAX_GUIDE:
                title = hrBillCompareConfig.getTitle() + "个税对账结果";
                break;
            case THIRD_PARTY_BILLS:
                title = hrBillCompareConfig.getTitle() + "第三方对账结果";
                break;
            case SPECIAL_CLIENT_BILL:
                title = hrBillCompareConfig.getTitle() + "海尔对账结果";
                break;
            default:
                throw new CommonException("未知的对账类型!");
        }
        service.createSheetForMap(workbook, new ExportParams(title, "sheet1"), colList, dynamicData);
        // 设置颜色
        // this.parseWorkBookColor(workbook, cellItems, compareResultList);
        return ExcelUtils.downLoadExcelToLocal(title, workbook, fileTempPath);
    }

    /**
     * 处理导出动态数据列
     *
     * @param valueMap
     * @param dataObject
     * @param topKey
     */
    public void dealDynamicDataChildrenForExport(Map<String, Object> valueMap, HrBillCompareHeaderDTO dataObject, String topKey) {
        List<Map<String, Object>> secondMapDataList = new LinkedList<>();
        Map<String, Object> secondMap = new HashMap<>();

        List<Map<String, Object>> unitPartMapList = new LinkedList<>();
        List<HrBillCompareHeaderDTO.ComparePart> unitPart = dataObject.getUnitPart();
        Map<String, Object> thirdMap = new HashMap<>();
        unitPart.forEach(part -> thirdMap.put(topKey + "Unit" + part.getFieldKey(), part.getFieldValue()));
        unitPartMapList.add(thirdMap);
        secondMap.put(topKey + "Unit", unitPartMapList);

        List<Map<String, Object>> personalPartMapList = new LinkedList<>();
        List<HrBillCompareHeaderDTO.ComparePart> personalPart = dataObject.getPersonalPart();
        personalPart.forEach(part -> thirdMap.put(topKey + "Personal" + part.getFieldKey(), part.getFieldValue()));
        personalPartMapList.add(thirdMap);
        secondMap.put(topKey + "Personal", personalPartMapList);


        secondMap.put(topKey + "PersonalTax", dataObject.getPersonalTax());
        secondMap.put(topKey + "UnitLateFee", dataObject.getUnitLateFee());
        secondMap.put(topKey + "Total", dataObject.getTotal());
        secondMap.put(topKey + "TotalAmount", dataObject.getTotalAmount());
        secondMapDataList.add(secondMap);

        valueMap.put(topKey, secondMapDataList);
    }

    /**
     * 处理导出excel表头
     *
     * @param colList
     * @param dynamicHeaders
     */
    @Override
    public void handleExcelExportEntity(List<ExcelExportEntity> colList, List<DynamicHeadersDTO> dynamicHeaders) {
        dynamicHeaders.forEach(firstHeader -> {
            ExcelExportEntity firstEntity = new ExcelExportEntity();
            firstEntity.setName(firstHeader.getTitle());
            firstEntity.setKey(firstHeader.getKey());
            firstEntity.setWidth(20);
            firstEntity.setNeedMerge(true);
            if (firstHeader.getChildren() != null) {
                List<ExcelExportEntity> secondEntities = new ArrayList<>();
                firstHeader.getChildren().forEach(secondHeader -> {
                    ExcelExportEntity secondEntity = new ExcelExportEntity();
                    secondEntity.setName(secondHeader.getTitle());
                    secondEntity.setKey(secondHeader.getKey());
                    secondEntity.setNeedMerge(true);
                    if (secondHeader.getChildren() != null) {
                        List<ExcelExportEntity> thirdEntities = new ArrayList<>();
                        secondHeader.getChildren().forEach(thirdHeader -> {
                            ExcelExportEntity thirdEntity = new ExcelExportEntity();
                            thirdEntity.setName(thirdHeader.getTitle());
                            thirdEntity.setKey(thirdHeader.getKey());
                            thirdEntities.add(thirdEntity);
                        });
                        secondEntity.setList(thirdEntities);
                    }
                    secondEntities.add(secondEntity);
                });
                firstEntity.setList(secondEntities);
            }
            colList.add(firstEntity);
        });
    }

    /**
     * 创建对账结果
     *
     * @param hrBillCompareResultDTO
     * @return
     */
    @Override
    public HrBillCompareResultDTO createHrBillCompareResult(HrBillCompareResultDTO hrBillCompareResultDTO) {
        log.info("Create new HrBillCompareResult:{}", hrBillCompareResultDTO);

        HrBillCompareResult hrBillCompareResult = this.hrBillCompareResultMapper.toEntity(hrBillCompareResultDTO);
        this.hrBillCompareResultRepository.insert(hrBillCompareResult);
        return this.hrBillCompareResultMapper.toDto(hrBillCompareResult);
    }

    /**
     * 修改对账结果
     *
     * @param hrBillCompareResultDTO
     * @return
     */
    @Override
    public Optional<HrBillCompareResultDTO> updateHrBillCompareResult(HrBillCompareResultDTO hrBillCompareResultDTO) {
        return Optional.ofNullable(this.hrBillCompareResultRepository.selectById(hrBillCompareResultDTO.getId()))
            .map(roleTemp -> {
                HrBillCompareResult hrBillCompareResult = this.hrBillCompareResultMapper.toEntity(hrBillCompareResultDTO);
                this.hrBillCompareResultRepository.updateById(hrBillCompareResult);
                log.info("Update HrBillCompareResult:{}", hrBillCompareResultDTO);
                return hrBillCompareResultDTO;
            });
    }

    /**
     * 查询对账结果详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillCompareResultDTO getHrBillCompareResult(String id) {
        log.info("Get HrBillCompareResult :{}", id);

        HrBillCompareResult hrBillCompareResult = this.hrBillCompareResultRepository.selectById(id);
        return this.hrBillCompareResultMapper.toDto(hrBillCompareResult);
    }

    /**
     * 删除对账结果
     *
     * @param id
     */
    @Override
    public void deleteHrBillCompareResult(String id) {
        Optional.ofNullable(this.hrBillCompareResultRepository.selectById(id))
            .ifPresent(hrBillCompareResult -> {
                this.hrBillCompareResultRepository.deleteById(id);
                log.info("Delete HrBillCompareResult:{}", hrBillCompareResult);
            });
    }

    /**
     * 批量删除对账结果
     *
     * @param ids
     */
    @Override
    public void deleteHrBillCompareResult(List<String> ids) {
        log.info("Delete HrBillCompareResults:{}", ids);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrBillCompareResult> hrBillCompareResultList = hrBillCompareResultRepository.selectBatchIds(ids);
        List<String> configIds = hrBillCompareResultList.stream().map(HrBillCompareResult::getBillCompareConfigId).collect(Collectors.toList());
        List<HrBillCompareConfig> hrBillCompareConfigs = hrBillCompareConfigRepository.selectBatchIds(configIds);
        List<String> billIds = hrBillCompareConfigs.stream().map(HrBillCompareConfig::getBillId).collect(Collectors.toList());
        HrBillCompareConfig hrBillCompareConfig = hrBillCompareConfigs.get(0);
        String value = "";
        boolean notCalSign = false;
        if (hrBillCompareConfig.getType().equals(BillEnum.GuidePlateType.MEDICAL_GUIDE.getKey())) {
            value = ModuleTypeEnum.MEDICAL_RECONCILIATION.getValue();
            notCalSign = true;
        } else if (hrBillCompareConfig.getType().equals(BillEnum.GuidePlateType.SOCIAL_SECURITY_GUIDE.getKey())) {
            value = ModuleTypeEnum.SOCIAL_RECONCILIATION.getValue();
            notCalSign = true;
        } else if (hrBillCompareConfig.getType().equals(BillEnum.GuidePlateType.ACCUMULATION_FUND_GUIDE.getKey())) {
            value = ModuleTypeEnum.ACCUMULATION_RECONCILIATION.getValue();
            notCalSign = true;
        } else if (hrBillCompareConfig.getType().equals(BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey())) {
            value = ModuleTypeEnum.PERSONAL_TAX_RECONCILIATION.getValue();
        } else {
            value = ModuleTypeEnum.SPECIAL_CLIENT_RECONCILIATION.getValue();
        }
        //返回个税对账存在已使用个税补差的的对账结果
        if (!hrBillCompareConfig.getType().equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            List<HrWelfareCompensationDTO> compensationDTOS = hrWelfareCompensationRepository.getUsedMakeUpByResultId(ids, hrBillCompareConfig.getType(), 1);
            if (compensationDTOS != null && !compensationDTOS.isEmpty()) {
                List<String> title = compensationDTOS.stream().map(HrWelfareCompensationDTO::getTitle).distinct().collect(Collectors.toList());
                throw new CommonException(String.join("，", title) + "存在已使用的补差，不可删除！");
            }
            //删除未使用的补差数据
            hrWelfareCompensationRepository.delByResultId(ids, jwtUserDTO.getUserName());
        }
        if (notCalSign) {
            //删除批量对账账单
            hrBillRepository.delByCompareResult(billIds, "全部公司", jwtUserDTO.getUserName());
        }
        //删除对账配置
        hrBillCompareConfigRepository.delBatchIds(configIds, jwtUserDTO.getUserName());
        //删除对账结果
        this.hrBillCompareResultRepository.delBatchIds(ids, jwtUserDTO.getUserName());
        // 删除报销申请
        this.hrBillReimbursementApplyService.deleteByBillCompareResultId(ids);
        //删除对账结果明细
        hrBillCompareResultDetailRepository.delByResultId(ids);

        this.sysOperLogService.insertSysOperLog(value, BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids), null, null, null, null, null, null);

    }

    /**
     * 分页查询对账结果
     *
     * @param hrBillCompareResultDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrBillCompareResultDTO> findPage(HrBillCompareResultDTO hrBillCompareResultDTO, Long pageNumber, Long pageSize) {
        Page<HrBillCompareResult> page = new Page<>(pageNumber, pageSize);
        return this.hrBillCompareResultRepository.findPage(page, hrBillCompareResultDTO);
    }

    /**
     * 下载海尔对账
     *
     * @param configId
     * @return
     */
    @Override
    public String exportHaierReconciliation(String configId) {
        List<ExcelSheetEntity> excelSheetEntityList = new ArrayList<>();
        HrBillCompareConfigDTO hrBillCompareConfigDTO = hrBillCompareConfigRepository.findById(configId);

        HrBillCompareResultDTO byBillConfigId = hrBillCompareResultRepository.getByBillConfigId(configId);
        List<HrBillCompareResultDetailDTO> list = hrBillCompareResultDetailService.selectHrBillCompareResultDetailByBillCompareResultId(byBillConfigId.getId());
        List<HrCompareHaierDetailExport> detailExportList = new ArrayList<>();
        int serialNo = 0;
        for (HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO : list) {
            ++serialNo;
            HrCompareHaierDetailExport hrCompareHaierDetailExport = new HrCompareHaierDetailExport();
            hrCompareHaierDetailExport.setSerialNo(serialNo)
                .setArea(hrBillCompareResultDetailDTO.getArea())
                .setPaymentDate(hrBillCompareResultDetailDTO.getPaymentDate())
                .setCenterName(hrBillCompareResultDetailDTO.getCenterName())
                .setStaffName(hrBillCompareResultDetailDTO.getStaffName())
                .setIdNo(hrBillCompareResultDetailDTO.getIdNo())
            ;
            this.dealComparePartsSource(hrBillCompareResultDetailDTO, hrCompareHaierDetailExport);
            this.dealComparePartsSystem(hrBillCompareResultDetailDTO, hrCompareHaierDetailExport);
            this.dealComparePartsDiff(hrBillCompareResultDetailDTO, hrCompareHaierDetailExport);
            hrCompareHaierDetailExport.setSourceTotal(CalculateUtils.decimalAddition(hrCompareHaierDetailExport.getTotalSocialSource(), hrCompareHaierDetailExport.getAccumulationFundTotalSource()));
            hrCompareHaierDetailExport.setSystemTotal(CalculateUtils.decimalAddition(hrCompareHaierDetailExport.getTotalSocialSystem(), hrCompareHaierDetailExport.getAccumulationFundTotalSystem()));
            hrCompareHaierDetailExport.setDiffTotal(CalculateUtils.decimalAddition(hrCompareHaierDetailExport.getTotalSocialDiff(), hrCompareHaierDetailExport.getAccumulationFundTotalDiff()));
            detailExportList.add(hrCompareHaierDetailExport);
        }
        excelSheetEntityList.add((new ExcelSheetEntity().setSheetName("系统费用对比").setPojoClass(HrCompareHaierDetailExport.class).setDataList(detailExportList)));
        //查询该地区之前年份
        List<HrBillCompareConfigDTO> hrBillCompareConfigs = hrBillCompareConfigRepository.findLastData(hrBillCompareConfigDTO);
        Map<Integer, List<HrBillCompareConfigDTO>> listMap = hrBillCompareConfigs.stream().collect(Collectors.groupingBy(HrBillCompareConfigDTO::getPayYear));
        for (Integer payYear : listMap.keySet()) {
            List<HrCompareHaierTotalExport> totalExportList = new ArrayList<>();
            List<HrBillCompareConfigDTO> hrBillCompareConfigDTOS = listMap.get(payYear);
            List<String> collect = hrBillCompareConfigDTOS.stream().map(HrBillCompareConfigDTO::getCompareResultId).collect(Collectors.toList());
            List<HrBillCompareResultDetail> resultDetailList = hrBillCompareResultDetailService.list(new QueryWrapper<HrBillCompareResultDetail>().in("bill_compare_result_id", collect).orderByAsc("pay_monthly"));
            Map<Integer, List<HrBillCompareResultDetail>> detailMap = resultDetailList.stream().collect(Collectors.groupingBy(HrBillCompareResultDetail::getPayMonthly));
            Map<Integer, BigDecimal> balanceMap = this.handleBalance(detailMap);
            for (Integer payMonthly : detailMap.keySet()) {
                List<HrBillCompareResultDetail> detailList = detailMap.get(payMonthly);
                BigDecimal systemAmount = detailList.stream().filter(lst -> lst.getSystemTotal() != null).map(HrBillCompareResultDetail::getSystemTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal sourceAmount = detailList.stream().filter(lst -> lst.getSourceTotal() != null).map(HrBillCompareResultDetail::getSourceTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                HrCompareHaierTotalExport totalExport = new HrCompareHaierTotalExport();
                totalExport
                    .setCenterName(hrBillCompareConfigDTO.getCenterName())
                    .setPaymentDate(payYear + "." + (payMonthly < 9 ? "0" + payMonthly : payMonthly))
                    .setSystemAmount(systemAmount)
                    .setSourceAmount(sourceAmount)
                    .setDiffAmount(CalculateUtils.decimalSubtraction(systemAmount, sourceAmount));
                //获取上个月的本期结余
                BigDecimal lastMonthBalance = balanceMap.get(payMonthly - 1) == null ? BigDecimal.ZERO : balanceMap.get(payMonthly - 1);
                totalExport.setLastMonthBalance(lastMonthBalance);
                totalExport.setThisMonthBalance(CalculateUtils.decimalAddition(totalExport.getDiffAmount(), lastMonthBalance));
                StringBuilder sb = new StringBuilder();
                int compare = totalExport.getDiffAmount().compareTo(BigDecimal.ZERO);
                if (compare == -1) {
                    sb.append("本月多支付" + totalExport.getDiffAmount());
                } else if (compare == 1) {
                    sb.append("本月少支付" + totalExport.getDiffAmount());
                }
                int compareTo = lastMonthBalance.compareTo(BigDecimal.ZERO);
                if (compareTo == -1) {
                    sb.append("截至上月共结余" + totalExport.getDiffAmount() + "需下月抵扣");
                } else if (compareTo == 1) {
                    sb.append("截至上月共欠款" + totalExport.getDiffAmount() + "需下月补齐");
                }
                totalExport.setTreatmentMeasures(sb.toString());
                totalExportList.add(totalExport);
            }
            excelSheetEntityList.add((new ExcelSheetEntity().setSheetName("劳务汇总" + payYear).setPojoClass(HrCompareHaierTotalExport.class).setDataList(totalExportList)));
        }
        String fileName = fileTempPath + hrBillCompareConfigDTO.getCenterName() + hrBillCompareConfigDTO.getPaymentDate() + "对账单";
        String sheetsExcel = ExcelUtils.exportMultipleSheetsExcel(excelSheetEntityList, fileName, null);
        return sheetsExcel;
    }

    /**
     * 批量下载海尔对账
     *
     * @param hrBillCompareResultDTO
     * @return
     */
    @Override
    public String batchExportHaier(HrBillCompareResultDTO hrBillCompareResultDTO) {
        hrBillCompareResultDTO.setType(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey());
        List<HrBillCompareResultDTO> list = hrBillCompareResultRepository.findList(hrBillCompareResultDTO);
        List<File> files = new ArrayList<>();
        List<String> configIds = list.stream().map(HrBillCompareResultDTO::getBillCompareConfigId).collect(Collectors.toList());
        String filePath = "";
        for (String configId : configIds) {
            filePath = exportHaierReconciliation(configId);
            files.add(new File(filePath));
        }
        if (files.size() == 1) {
            return hrAppendixService.uploadLocalFile(filePath);
        } else {
            return hrAppendixService.zipAndUploadFile(files, "海尔对账单");
        }
    }

    /**
     * @param detailMap
     * @return
     */
    private Map<Integer, BigDecimal> handleBalance(Map<Integer, List<HrBillCompareResultDetail>> detailMap) {
        //detailMap数据根据月份正数排序，所有需要计算出本期结余，向下计算
        Map<Integer, BigDecimal> hashMap = new HashMap<>();
        for (Integer payMonth : detailMap.keySet()) {
            List<HrBillCompareResultDetail> hrBillCompareResultDetails = detailMap.get(payMonth);
            BigDecimal systemAmount = hrBillCompareResultDetails.stream().filter(lst -> lst.getSystemTotal() != null).map(HrBillCompareResultDetail::getSystemTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal sourceAmount = hrBillCompareResultDetails.stream().filter(lst -> lst.getSourceTotal() != null).map(HrBillCompareResultDetail::getSourceTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal diffAmount = CalculateUtils.decimalSubtraction(systemAmount, sourceAmount);//差异额
            BigDecimal decimal = hashMap.get(payMonth - 1) == null ? BigDecimal.ZERO : hashMap.get(payMonth - 1);
            hashMap.put(payMonth, CalculateUtils.decimalAddition(diffAmount, decimal));
            /*int lastMonth = payMonth + 1;
            List<HrBillCompareResultDetail> lastDetailList = detailMap.get(lastMonth);
            if (CollectionUtils.isNotEmpty(lastDetailList)){
                BigDecimal thisBalance = hashMap.get(payMonth);
                BigDecimal systemAmountLast = lastDetailList.stream().map(HrBillCompareResultDetail::getSystemTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal sourceAmountLast= lastDetailList.stream().map(HrBillCompareResultDetail::getSourceTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal diffAmountLast = CalculateUtils.decimalSubtraction(systemAmountLast,sourceAmountLast);
                BigDecimal lastBalance = CalculateUtils.decimalAddition(diffAmountLast, thisBalance);
                hashMap.put(lastMonth,lastBalance);
            }*/
        }
        return hashMap;
    }

    private void dealComparePartsDiff(HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO, HrCompareHaierDetailExport hrCompareHaierDetailExport) {
        HrBillCompareHeaderDTO compareResultObject = hrBillCompareResultDetailDTO.getCompareResultObject();
        List<HrBillCompareHeaderDTO.ComparePart> comparePartList = new ArrayList<>();
        comparePartList.addAll(compareResultObject.getPensionCompareParts());
        comparePartList.addAll(compareResultObject.getMedicalCompareParts());
        comparePartList.addAll(compareResultObject.getUnemploymentCompareParts());
        comparePartList.addAll(compareResultObject.getInjuryCompareParts());
        comparePartList.addAll(compareResultObject.getMaternityCompareParts());
        comparePartList.addAll(compareResultObject.getSubCompareParts());
        comparePartList.addAll(compareResultObject.getAccumulationFundCompareParts());
        for (HrBillCompareHeaderDTO.ComparePart comparePart : comparePartList) {
            BigDecimal decimal = new BigDecimal(comparePart.getFieldValue());
            DynamicFeeTypesEnum enumByKey = DynamicFeeTypesEnum.getEnumByFieldName(comparePart.getFieldKey());
            if (enumByKey == null) {
                continue;
            }
            switch (enumByKey) {
                case UNIT_PENSION_CARDINAL:
                    hrCompareHaierDetailExport.setUnitPensionCardinalDiff(decimal);
                    break;
                case PERSONAL_PENSION_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalPensionCardinalDiff(decimal);
                    break;
                case UNIT_PENSION:
                    hrCompareHaierDetailExport.setUnitPensionDiff(decimal);
                    break;
                case PERSONAL_PENSION:
                    hrCompareHaierDetailExport.setPersonalPensionDiff(decimal);
                    break;
                case UNIT_LATE_FEE:
                    hrCompareHaierDetailExport.setUnitLateFee(decimal);
                    break;

                case UNIT_UNEMPLOYMENT_CARDINAL:
                    hrCompareHaierDetailExport.setUnitUnemploymentCardinalDiff(decimal);
                    break;
                case PERSONAL_UNEMPLOYMENT_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalUnemploymentCardinalDiff(decimal);
                    break;
                case UNIT_UNEMPLOYMENT:
                    hrCompareHaierDetailExport.setUnitUnemploymentDiff(decimal);
                    break;
                case PERSONAL_UNEMPLOYMENT:
                    hrCompareHaierDetailExport.setPersonalUnemploymentDiff(decimal);
                    break;

                case WORK_INJURY_CARDINAL:
                    hrCompareHaierDetailExport.setWorkInjuryCardinalDiff(decimal);
                    break;
                case PERSONAL_INJURY_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalInjuryCardinalDiff(decimal);
                    break;
                case UNIT_INJURY:
                    hrCompareHaierDetailExport.setWorkInjuryDiff(decimal);
                    break;
                case PERSONAL_INJURY:
                    hrCompareHaierDetailExport.setPersonalInjuryDiff(decimal);
                    break;

                case MEDICAL_INSURANCE_CARDINAL:
                    hrCompareHaierDetailExport.setMedicalInsuranceCardinalDiff(decimal);
                    break;
                case MEDICAL_INSURANCE_CARDINAL_PERSONAL:
                    hrCompareHaierDetailExport.setMedicalInsuranceCardinalPersonalDiff(decimal);
                    break;
                case UNIT_MEDICAL:
                    hrCompareHaierDetailExport.setUnitMedicalDiff(decimal);
                    break;
                case PERSONAL_MEDICAL:
                    hrCompareHaierDetailExport.setPersonalMedicalDiff(decimal);
                    break;
//                case UNIT_LARGE_MEDICAL_EXPENSE_HAIER:
//                    hrCompareHaierDetailExport.setUnitLargeMedicalExpense(decimal);
//                    break;
//                case PERSONAL_LARGE_MEDICAL_EXPENSE_HAIER:
//                    hrCompareHaierDetailExport.setPersonalLargeMedicalExpense(decimal);
//                    break;

                case UNIT_MATERNITY_CARDINAL:
                    hrCompareHaierDetailExport.setUnitMaternityCardinalDiff(decimal);
                    break;
                case PERSONAL_MATERNITY_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalMaternityCardinalDiff(decimal);
                    break;
                case UNIT_MATERNITY:
                    hrCompareHaierDetailExport.setUnitMaternityDiff(decimal);
                    break;
                case PERSONAL_MATERNITY:
                    hrCompareHaierDetailExport.setPersonalMaternityDiff(decimal);
                    break;

                case UNIT_SOCIAL_TOTAL:
                    hrCompareHaierDetailExport.setUnitSocialTotalDiff(decimal);
                    break;
                case PERSONAL_SOCIAL_TOTAL:
                    hrCompareHaierDetailExport.setPersonalSocialTotalDiff(decimal);
                    break;

                case ACCUMULATION_FUND_BASE:
                    hrCompareHaierDetailExport.setAccumulationFundCardinalDiff(decimal);
                    break;
                case UNIT_ACCUMULATION_FUND:
                    hrCompareHaierDetailExport.setUnitAccumulationFundDiff(decimal);
                    break;
                case PERSONAL_ACCUMULATION_FUND:
                    hrCompareHaierDetailExport.setPersonalAccumulationFundDiff(decimal);
                    break;
                default:
                    break;
            }
        }
        hrCompareHaierDetailExport.setTotalSocialDiff(CalculateUtils.decimalAddition(hrCompareHaierDetailExport.getUnitSocialTotalDiff(), hrCompareHaierDetailExport.getPersonalSocialTotalDiff()));
        hrCompareHaierDetailExport.setAccumulationFundTotalDiff(CalculateUtils.decimalAddition(hrCompareHaierDetailExport.getUnitAccumulationFundDiff(), hrCompareHaierDetailExport.getPersonalAccumulationFundDiff()));

    }

    private void dealComparePartsSystem(HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO, HrCompareHaierDetailExport hrCompareHaierDetailExport) {
        HrBillCompareHeaderDTO systemDataObject = hrBillCompareResultDetailDTO.getSystemDataObject();//导盘数据
        List<HrBillCompareHeaderDTO.ComparePart> comparePartList = new ArrayList<>();
        comparePartList.addAll(systemDataObject.getPensionCompareParts());
        comparePartList.addAll(systemDataObject.getMedicalCompareParts());
        comparePartList.addAll(systemDataObject.getUnemploymentCompareParts());
        comparePartList.addAll(systemDataObject.getInjuryCompareParts());
        comparePartList.addAll(systemDataObject.getMaternityCompareParts());
        comparePartList.addAll(systemDataObject.getSubCompareParts());
        comparePartList.addAll(systemDataObject.getAccumulationFundCompareParts());
        for (HrBillCompareHeaderDTO.ComparePart comparePart : comparePartList) {
            BigDecimal decimal = new BigDecimal(comparePart.getFieldValue());
            DynamicFeeTypesEnum enumByKey = DynamicFeeTypesEnum.getEnumByFieldName(comparePart.getFieldKey());
            if (enumByKey == null) {
                continue;
            }
            switch (enumByKey) {
                case UNIT_PENSION_CARDINAL:
                    hrCompareHaierDetailExport.setUnitPensionCardinalSystem(decimal);
                    break;
                case PERSONAL_PENSION_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalPensionCardinalSystem(decimal);
                    break;
                case UNIT_PENSION:
                    hrCompareHaierDetailExport.setUnitPensionSystem(decimal);
                    break;
                case PERSONAL_PENSION:
                    hrCompareHaierDetailExport.setPersonalPensionSystem(decimal);
                    break;
                case UNIT_LATE_FEE:
                    hrCompareHaierDetailExport.setUnitLateFee(decimal);
                    break;

                case UNIT_UNEMPLOYMENT_CARDINAL:
                    hrCompareHaierDetailExport.setUnitUnemploymentCardinalSystem(decimal);
                    break;
                case PERSONAL_UNEMPLOYMENT_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalUnemploymentCardinalSystem(decimal);
                    break;
                case UNIT_UNEMPLOYMENT:
                    hrCompareHaierDetailExport.setUnitUnemploymentSystem(decimal);
                    break;
                case PERSONAL_UNEMPLOYMENT:
                    hrCompareHaierDetailExport.setPersonalUnemploymentSystem(decimal);
                    break;

                case WORK_INJURY_CARDINAL:
                    hrCompareHaierDetailExport.setWorkInjuryCardinalSystem(decimal);
                    break;
                case PERSONAL_INJURY_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalInjuryCardinalSystem(decimal);
                    break;
                case UNIT_INJURY:
                    hrCompareHaierDetailExport.setWorkInjurySystem(decimal);
                    break;
                case PERSONAL_INJURY:
                    hrCompareHaierDetailExport.setPersonalInjurySystem(decimal);
                    break;

                case MEDICAL_INSURANCE_CARDINAL:
                    hrCompareHaierDetailExport.setMedicalInsuranceCardinalSystem(decimal);
                    break;
                case MEDICAL_INSURANCE_CARDINAL_PERSONAL:
                    hrCompareHaierDetailExport.setMedicalInsuranceCardinalPersonalSystem(decimal);
                    break;
                case UNIT_MEDICAL:
                    hrCompareHaierDetailExport.setUnitMedicalSystem(decimal);
                    break;
                case PERSONAL_MEDICAL:
                    hrCompareHaierDetailExport.setPersonalMedicalSystem(decimal);
                    break;
                case UNIT_LARGE_MEDICAL_EXPENSE_HAIER:
                    hrCompareHaierDetailExport.setUnitLargeMedicalExpense(decimal);
                    break;
                case PERSONAL_LARGE_MEDICAL_EXPENSE_HAIER:
                    hrCompareHaierDetailExport.setPersonalLargeMedicalExpense(decimal);
                    break;

                case UNIT_MATERNITY_CARDINAL:
                    hrCompareHaierDetailExport.setUnitMaternityCardinalSystem(decimal);
                    break;
                case PERSONAL_MATERNITY_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalMaternityCardinalSystem(decimal);
                    break;
                case UNIT_MATERNITY:
                    hrCompareHaierDetailExport.setUnitMaternitySystem(decimal);
                    break;
                case PERSONAL_MATERNITY:
                    hrCompareHaierDetailExport.setPersonalMaternitySystem(decimal);
                    break;

                case UNIT_SOCIAL_TOTAL:
                    hrCompareHaierDetailExport.setUnitSocialTotalSystem(decimal);
                    break;
                case PERSONAL_SOCIAL_TOTAL:
                    hrCompareHaierDetailExport.setPersonalSocialTotalSystem(decimal);
                    break;

                case ACCUMULATION_FUND_BASE:
                    hrCompareHaierDetailExport.setAccumulationFundCardinalSystem(decimal);
                    break;
                case UNIT_ACCUMULATION_FUND:
                    hrCompareHaierDetailExport.setUnitAccumulationFundSystem(decimal);
                    break;
                case PERSONAL_ACCUMULATION_FUND:
                    hrCompareHaierDetailExport.setPersonalAccumulationFundSystem(decimal);
                    break;
                default:
                    break;
            }
        }
        hrCompareHaierDetailExport.setTotalSocialSystem(CalculateUtils.decimalAddition(hrCompareHaierDetailExport.getUnitSocialTotalSystem(), hrCompareHaierDetailExport.getPersonalSocialTotalSystem()));
        hrCompareHaierDetailExport.setAccumulationFundTotalSystem(CalculateUtils.decimalAddition(hrCompareHaierDetailExport.getUnitAccumulationFundSystem(), hrCompareHaierDetailExport.getPersonalAccumulationFundSystem()));
    }

    private void dealComparePartsSource(HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO, HrCompareHaierDetailExport hrCompareHaierDetailExport) {
        HrBillCompareHeaderDTO sourceDataObject = hrBillCompareResultDetailDTO.getSourceDataObject();//导盘数据
        List<HrBillCompareHeaderDTO.ComparePart> comparePartList = new ArrayList<>();
        comparePartList.addAll(sourceDataObject.getPensionCompareParts());
        comparePartList.addAll(sourceDataObject.getMedicalCompareParts());
        comparePartList.addAll(sourceDataObject.getUnemploymentCompareParts());
        comparePartList.addAll(sourceDataObject.getInjuryCompareParts());
        comparePartList.addAll(sourceDataObject.getMaternityCompareParts());
        comparePartList.addAll(sourceDataObject.getSubCompareParts());
        comparePartList.addAll(sourceDataObject.getAccumulationFundCompareParts());
        for (HrBillCompareHeaderDTO.ComparePart comparePart : comparePartList) {
            BigDecimal decimal = new BigDecimal(comparePart.getFieldValue());
            DynamicFeeTypesEnum enumByKey = DynamicFeeTypesEnum.getEnumByFieldName(comparePart.getFieldKey());
            if (enumByKey == null) {
                continue;
            }
            switch (enumByKey) {
                case UNIT_PENSION_CARDINAL:
                    hrCompareHaierDetailExport.setUnitPensionCardinalSource(decimal);
                    break;
                case PERSONAL_PENSION_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalPensionCardinalSource(decimal);
                    break;
                case UNIT_PENSION:
                    hrCompareHaierDetailExport.setUnitPensionSource(decimal);
                    break;
                case PERSONAL_PENSION:
                    hrCompareHaierDetailExport.setPersonalPensionSource(decimal);
                    break;

                case UNIT_UNEMPLOYMENT_CARDINAL:
                    hrCompareHaierDetailExport.setUnitUnemploymentCardinalSource(decimal);
                    break;
                case PERSONAL_UNEMPLOYMENT_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalUnemploymentCardinalSource(decimal);
                    break;
                case UNIT_UNEMPLOYMENT:
                    hrCompareHaierDetailExport.setUnitUnemploymentSource(decimal);
                    break;
                case PERSONAL_UNEMPLOYMENT:
                    hrCompareHaierDetailExport.setPersonalUnemploymentSource(decimal);
                    break;

                case WORK_INJURY_CARDINAL:
                    hrCompareHaierDetailExport.setWorkInjuryCardinalSource(decimal);
                    break;
                case PERSONAL_INJURY_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalInjuryCardinalSource(decimal);
                    break;
                case UNIT_INJURY:
                    hrCompareHaierDetailExport.setWorkInjurySource(decimal);
                    break;
                case PERSONAL_INJURY:
                    hrCompareHaierDetailExport.setPersonalInjurySource(decimal);
                    break;

                case MEDICAL_INSURANCE_CARDINAL:
                    hrCompareHaierDetailExport.setMedicalInsuranceCardinalSource(decimal);
                    break;
                case MEDICAL_INSURANCE_CARDINAL_PERSONAL:
                    hrCompareHaierDetailExport.setMedicalInsuranceCardinalPersonalSource(decimal);
                    break;
                case UNIT_MEDICAL:
                    hrCompareHaierDetailExport.setUnitMedicalSource(decimal);
                    break;
                case PERSONAL_MEDICAL:
                    hrCompareHaierDetailExport.setPersonalMedicalSource(decimal);
                    break;

                case UNIT_MATERNITY_CARDINAL:
                    hrCompareHaierDetailExport.setUnitMaternityCardinalSource(decimal);
                    break;
                case PERSONAL_MATERNITY_CARDINAL:
                    hrCompareHaierDetailExport.setPersonalMaternityCardinalSource(decimal);
                    break;
                case UNIT_MATERNITY:
                    hrCompareHaierDetailExport.setUnitMaternitySource(decimal);
                    break;
                case PERSONAL_MATERNITY:
                    hrCompareHaierDetailExport.setPersonalMaternitySource(decimal);
                    break;

                case UNIT_SOCIAL_TOTAL:
                    hrCompareHaierDetailExport.setUnitSocialTotalSource(decimal);
                    break;
                case PERSONAL_SOCIAL_TOTAL:
                    hrCompareHaierDetailExport.setPersonalSocialTotalSource(decimal);
                    break;

                case ACCUMULATION_FUND_BASE:
                    hrCompareHaierDetailExport.setAccumulationFundCardinalSource(decimal);
                    break;
                case UNIT_ACCUMULATION_FUND:
                    hrCompareHaierDetailExport.setUnitAccumulationFundSource(decimal);
                    break;
                case PERSONAL_ACCUMULATION_FUND:
                    hrCompareHaierDetailExport.setPersonalAccumulationFundSource(decimal);
                    break;
                default:
                    break;
            }
        }
        hrCompareHaierDetailExport.setTotalSocialSource(CalculateUtils.decimalAddition(hrCompareHaierDetailExport.getUnitSocialTotalSource(), hrCompareHaierDetailExport.getPersonalSocialTotalSource()));
        hrCompareHaierDetailExport.setAccumulationFundTotalSource(CalculateUtils.decimalAddition(hrCompareHaierDetailExport.getUnitAccumulationFundSource(), hrCompareHaierDetailExport.getPersonalAccumulationFundSource()));
    }
}
