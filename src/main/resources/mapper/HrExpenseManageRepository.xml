<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrExpenseManageRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,client_id,
        expense_type,
        expense_name,
        operate,
        is_delete ,
        created_by ,
        created_date ,
        last_modified_by ,
        last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrExpenseManage">
        <id column="id" property="id"/>
        <id column="client_id" property="clientId"/>
        <result column="expense_type" property="expenseType"/>
        <result column="expense_name" property="expenseName"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Find_SQL">
        SELECT
            hc.client_name,
            hc.unit_number,
            hem.*
        FROM
            hr_expense_manage hem
        LEFT JOIN hr_client hc ON hem.client_id = hc.id
        <where>
            hem.is_delete = 0
            <if test="param.ids != null and param.ids.size() > 0">
                and hem.id in
                <foreach collection="param.ids" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
            <if test="param.clientIdList != null and param.clientIdList.size() > 0">
                and hem.client_id in
                <foreach collection="param.clientIdList" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
            <if test="param.expenseTypeList != null and param.expenseTypeList.size() > 0">
                and hem.expense_type in
                <foreach collection="param.expenseTypeList" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
            <if test="param.expenseName != null and param.expenseName != ''">
                AND hem.expense_name LIKE CONCAT('%',#{param.expenseName},'%')
            </if>
            <if test="param.isDefault != null">
                AND hem.is_default = #{param.isDefault}
            </if>
        </where>
        <if test="param.field == null  and param.order == null ">
            ORDER BY hem.created_date DESC
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            order by ${param.field} ${param.order}
        </if>
    </sql>

    <select id="selectBatch" resultType="cn.casair.dto.excel.HrExpenseManageExport">
        select * from hr_expense_manage where id in
        <foreach collection="ids" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

    <select id="getListByExpenseType" resultType="cn.casair.dto.HrExpenseManageDTO">
        SELECT
            em.*,
            c.item_name AS expenseTypeName
        FROM
            hr_expense_manage em
        LEFT JOIN hr_client hc ON em.client_id = hc.id
        LEFT JOIN code_table c ON em.expense_type = c.item_value AND c.is_delete = 0
        WHERE em.is_delete = 0
        AND c.parent_id = (SELECT id FROM code_table c WHERE c.inner_name = 'expensetype' AND c.is_delete = 0)
        <if test="list != null and list.size() > 0">
            AND em.expense_type IN
            <foreach collection="list" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <choose>
            <when test="clientId != null">
                AND em.client_id = #{clientId}
            </when>
            <otherwise>
                AND em.client_id IS NULL
            </otherwise>
        </choose>
    </select>

    <select id="selectPages" resultType="cn.casair.dto.HrExpenseManageDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="findList" resultType="cn.casair.dto.excel.HrExpenseManageExport">
        <include refid="Find_SQL"/>
    </select>

    <select id="getByExpenseName" resultType="cn.casair.dto.HrExpenseManageDTO">
        SELECT
            *
        FROM
            hr_expense_manage hm
        WHERE
            hm.is_delete = 0
            AND hm.expense_name = #{params.expenseName}
            AND hm.expense_type = #{params.expenseType}
            <if test="clientId != null">
                AND hm.client_id = #{clientId}
            </if>
            <if test="isDefault != null">
                AND hm.is_default = #{isDefault}
            </if>
        LIMIT 1
    </select>

    <select id="getList" resultType="cn.casair.dto.HrExpenseManageDTO">
        <include refid="Find_SQL"/>
    </select>
</mapper>
