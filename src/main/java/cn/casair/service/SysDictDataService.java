package cn.casair.service;
import cn.casair.domain.SysDictData;
import cn.casair.dto.SysDictDataDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 数据字典服务类
 *
 * <AUTHOR>
 * @since 2021-04-29
 */
public interface SysDictDataService extends IService<SysDictData> {


    /**
     * 创建数据字典
     * @param sysDictDataDTO
     * @return
     */
    SysDictDataDTO createSysDictData(SysDictDataDTO sysDictDataDTO);

    /**
     * 修改数据字典
     * @param sysDictDataDTO
     * @return
     */
    Optional<SysDictDataDTO> updateSysDictData(SysDictDataDTO sysDictDataDTO);

    /**
     * 查询数据字典详情
     * @param id
     * @return
     */
    SysDictDataDTO getSysDictData(Long id);

    /**
     * 删除数据字典
     * @param id
     */
    void deleteSysDictData(Long id);

    /**
     * 批量删除数据字典
     * @param ids
     */
    void deleteSysDictData(List<Long> ids);

    /**
     * 分页查询数据字典
     * @param sysDictDataDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(SysDictDataDTO sysDictDataDTO,Long pageNumber,Long pageSize);

    /**
     * 根据字典类型获取字典值
     * @param dictType
     * @return
     */
    List<SysDictDataDTO> selectDictDataByType(String dictType);
}
