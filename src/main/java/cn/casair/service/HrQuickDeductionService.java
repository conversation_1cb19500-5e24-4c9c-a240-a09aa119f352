package cn.casair.service;
import cn.casair.domain.HrQuickDeduction;
import cn.casair.dto.HrQuickDeductionDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 速算扣除数表服务类
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface HrQuickDeductionService extends IService<HrQuickDeduction> {


    /**
     * 创建速算扣除数表
     * @param hrQuickDeductionDTO
     * @return
     */
    HrQuickDeductionDTO createHrQuickDeduction(HrQuickDeductionDTO hrQuickDeductionDTO);

    /**
     * 修改速算扣除数表
     * @param hrQuickDeductionDTO
     * @return
     */
    Optional<HrQuickDeductionDTO> updateHrQuickDeduction(HrQuickDeductionDTO hrQuickDeductionDTO);

    /**
     * 查询速算扣除数表详情
     * @param id
     * @return
     */
    HrQuickDeductionDTO getHrQuickDeduction(String id);

    /**
     * 删除速算扣除数表
     * @param id
     */
    void deleteHrQuickDeduction(String id);

    /**
     * 批量删除速算扣除数表
     * @param ids
     */
    void deleteHrQuickDeduction(List<String> ids);

    /**
     * 分页查询速算扣除数表
     * @param hrQuickDeductionDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrQuickDeductionDTO hrQuickDeductionDTO,Long pageNumber,Long pageSize);

    String exportQuickDeduction(HrQuickDeductionDTO hrQuickDeductionDTO, HttpServletResponse httpServletResponse);

    String importQuickDeduction(MultipartFile file);

    String importQuickDeductionTemplate(HttpServletResponse response);

}
