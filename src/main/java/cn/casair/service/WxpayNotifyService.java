package cn.casair.service;
import cn.casair.domain.WxpayNotify;
import cn.casair.dto.WxpayNotifyDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;

/**
 * 微信支付通知表，V2接口版本服务类
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface WxpayNotifyService extends IService<WxpayNotify> {


    /**
     * 创建微信支付通知表，V2接口版本
     * @param wxpayNotifyDTO
     * @return
     */
    WxpayNotifyDTO createWxpayNotify(WxpayNotifyDTO wxpayNotifyDTO);

    /**
     * 修改微信支付通知表，V2接口版本
     * @param wxpayNotifyDTO
     * @return
     */
    Optional<WxpayNotifyDTO> updateWxpayNotify(WxpayNotifyDTO wxpayNotifyDTO);

    /**
     * 查询微信支付通知表，V2接口版本详情
     * @param id
     * @return
     */
    WxpayNotifyDTO getWxpayNotify(String id);

    /**
     * 删除微信支付通知表，V2接口版本
     * @param id
     */
    void deleteWxpayNotify(String id);

    /**
     * 批量删除微信支付通知表，V2接口版本
     * @param ids
     */
    void deleteWxpayNotify(List<String> ids);

    /**
     * 分页查询微信支付通知表，V2接口版本
     * @param wxpayNotifyDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(WxpayNotifyDTO wxpayNotifyDTO,Long pageNumber,Long pageSize);

    /**
     * 解析回掉函数更改状态
     * @param notifyResult
     */
    String parsing(WxPayOrderNotifyResult notifyResult);
}
