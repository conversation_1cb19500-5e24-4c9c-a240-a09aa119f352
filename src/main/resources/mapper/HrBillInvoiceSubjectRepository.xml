<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillInvoiceSubjectRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, invoce_apply_type, to_loan, subject_sn, subject_content,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillInvoiceSubject">
                    <id column="id" property="id"/>
                    <result column="invoce_apply_type" property="invoceApplyType"/>
                    <result column="to_loan" property="toLoan"/>
                    <result column="subject_sn" property="subjectSn"/>
                    <result column="subject_content" property="subjectContent"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

</mapper>
