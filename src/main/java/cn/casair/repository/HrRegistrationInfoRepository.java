package cn.casair.repository;

import cn.casair.domain.HrRegistrationInfo;
import cn.casair.domain.HrStaffSignCert;
import cn.casair.dto.HrRegistrationInfoDTO;
import cn.casair.dto.HrTalentStaffDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


/**
 * 报名信息表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Repository
public interface HrRegistrationInfoRepository extends BaseMapper<HrRegistrationInfo> {



    void uodateOpenId(String id);

    Integer selectSum();

    HrRegistrationInfoDTO selectInfo(HrRegistrationInfoDTO hrRegistrationInfoDTO);

    Integer selectRecruitmentNum(HrRegistrationInfo hrRegistrationInfo);

    void uodateRecruitmentNum(@Param("stationId")String stationId, @Param("stationName")String stationName, @Param("recruitmentNum")Integer recruitmentNum);

    void updatePhone(String id);

    HrStaffSignCert getCertificateNum(String certificateNum);

    void updateHrStaffSignCert(@Param("param1")HrStaffSignCert hrStaffSignCert);

    /**
     * 查询重复数据
     * @param brochureId 简章ID
     * @param stationName 报名岗位
     * @param hrTalentStaffsDTO 员工信息
     * @return 存在数量
     */
    Integer queryDuplicateData(@Param("brochureId") String brochureId, @Param("stationName") String stationName, @Param("param") HrTalentStaffDTO hrTalentStaffsDTO);

}
