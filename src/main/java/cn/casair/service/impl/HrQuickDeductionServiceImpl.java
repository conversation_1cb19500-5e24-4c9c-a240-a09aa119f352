package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.common.utils.StringUtils;
import cn.casair.domain.HrAccumulationFund;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrQuickDeductionExport;
import cn.casair.service.HrAppendixService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.component.asynchronous.QuickDeductionExpenseComponent;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrQuickDeduction;
import cn.casair.dto.HrQuickDeductionDTO;
import cn.casair.repository.HrQuickDeductionRepository;
import cn.casair.mapper.HrQuickDeductionMapper;
import cn.casair.service.HrQuickDeductionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Optional;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 速算扣除数表服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrQuickDeductionServiceImpl extends ServiceImpl<HrQuickDeductionRepository, HrQuickDeduction> implements HrQuickDeductionService {

    private static final Logger log = LoggerFactory.getLogger(HrQuickDeductionServiceImpl.class);

    private final HrQuickDeductionRepository hrQuickDeductionRepository;

    private final HrQuickDeductionMapper hrQuickDeductionMapper;

    private final RedisCache redisCache;
    private final HrAppendixService hrAppendixService;

    private final SysOperLogService sysOperLogService;
    private final QuickDeductionExpenseComponent quickDeductionExpenseComponent;


    @Value("${file.temp-path}")
    private String fileTempPath;

    @Value("${minio.excelPrefix}")
    private String excelPrefix;

    public HrQuickDeductionServiceImpl(HrQuickDeductionRepository hrQuickDeductionRepository, HrQuickDeductionMapper hrQuickDeductionMapper, RedisCache redisCache, HrAppendixService hrAppendixService, SysOperLogService sysOperLogService, QuickDeductionExpenseComponent quickDeductionExpenseComponent) {
        this.hrQuickDeductionRepository = hrQuickDeductionRepository;
        this.hrQuickDeductionMapper = hrQuickDeductionMapper;
        this.redisCache = redisCache;
        this.hrAppendixService = hrAppendixService;
        this.sysOperLogService = sysOperLogService;
        this.quickDeductionExpenseComponent = quickDeductionExpenseComponent;
    }

    /**
     * 创建速算扣除数表
     *
     * @param hrQuickDeductionDTO
     * @return
     */
    @Override
    public HrQuickDeductionDTO createHrQuickDeduction(HrQuickDeductionDTO hrQuickDeductionDTO) {
        log.info("Create new HrQuickDeduction:{}", hrQuickDeductionDTO);

        HrQuickDeduction hrQuickDeduction = this.hrQuickDeductionMapper.toEntity(hrQuickDeductionDTO);

        BigDecimal taxRate = extracted(1, hrQuickDeduction.getTaxRate());
        hrQuickDeduction.setTaxRate(taxRate);
        this.hrQuickDeductionRepository.insert(hrQuickDeduction);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.QUICK_DEDUCTION.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrQuickDeductionDTO),
            HrQuickDeductionDTO.class,
            null,
            JSON.toJSONString(hrQuickDeductionDTO)
        );
        return this.hrQuickDeductionMapper.toDto(hrQuickDeduction);
    }

    /**
     * 修改速算扣除数表
     *
     * @param hrQuickDeductionDTO
     * @return
     */
    @Override
    public Optional<HrQuickDeductionDTO> updateHrQuickDeduction(HrQuickDeductionDTO hrQuickDeductionDTO) {
        return Optional.ofNullable(this.hrQuickDeductionRepository.selectById(hrQuickDeductionDTO.getId()))
            .map(roleTemp -> {
                HrQuickDeduction hrQuickDeduction = this.hrQuickDeductionMapper.toEntity(hrQuickDeductionDTO);
                this.hrQuickDeductionRepository.updateById(hrQuickDeduction);
                log.info("Update HrQuickDeduction:{}", hrQuickDeductionDTO);
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.QUICK_DEDUCTION.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrQuickDeductionDTO),
                    HrQuickDeductionDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrQuickDeductionDTO),
                    null,
                    HrQuickDeductionDTO.class
                );
                return hrQuickDeductionDTO;
            });
    }

    /**
     * 查询速算扣除数表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrQuickDeductionDTO getHrQuickDeduction(String id) {
        log.info("Get HrQuickDeduction :{}", id);

        HrQuickDeduction hrQuickDeduction = this.hrQuickDeductionRepository.selectById(id);
        return this.hrQuickDeductionMapper.toDto(hrQuickDeduction);
    }

    /**
     * 删除速算扣除数表
     *
     * @param id
     */
    @Override
    public void deleteHrQuickDeduction(String id) {
        Optional.ofNullable(this.hrQuickDeductionRepository.selectById(id))
            .ifPresent(hrQuickDeduction -> {
                this.hrQuickDeductionRepository.deleteById(id);
                log.info("Delete HrQuickDeduction:{}", hrQuickDeduction);
            });
    }

    /**
     * 批量删除速算扣除数表
     *
     * @param ids
     */
    @Override
    public void deleteHrQuickDeduction(List<String> ids) {
        log.info("Delete HrQuickDeductions:{}", ids);
        List<HrQuickDeduction> list = hrQuickDeductionRepository.selectBatchIds(ids);
        this.hrQuickDeductionRepository.deleteBatchIds(ids);
        // 操作日志
        //this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.QUICK_DEDUCTION.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> collect = list.stream().map(HrQuickDeduction::getQuickDeductionSeries).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.QUICK_DEDUCTION.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "速算扣除数，级数: "+JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(list),
            jwtUserDTO
        );
    }

    /**
     * 分页查询速算扣除数表
     *
     * @param hrQuickDeductionDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrQuickDeductionDTO hrQuickDeductionDTO, Long pageNumber, Long pageSize) {
        Page<HrQuickDeduction> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrQuickDeduction> qw = new QueryWrapper<>();
        //排序
        if (StringUtils.isNotBlank(hrQuickDeductionDTO.getOrder()))
        {
            if (hrQuickDeductionDTO.getOrder().equals("DESC"))
            {
                qw.orderBy(StringUtils.isNotBlank(hrQuickDeductionDTO.getField()),false,hrQuickDeductionDTO.getField());
            }
            else {
                qw.orderBy(StringUtils.isNotBlank(hrQuickDeductionDTO.getField()),true,hrQuickDeductionDTO.getField());
            }

        }
        else {
            qw.orderByAsc("quick_deduction_series");
        }

        IPage iPage = this.hrQuickDeductionRepository.selectPage(page, qw);
        List<HrQuickDeductionDTO> list = this.hrQuickDeductionMapper.toDto(iPage.getRecords());

        for (HrQuickDeductionDTO quickDeductionDTO : list) {
            BigDecimal taxRate = extracted(0, quickDeductionDTO.getTaxRate());
            quickDeductionDTO.setTaxRate(taxRate);
        }
        iPage.setRecords(list);
        return iPage;
    }

    @Override
    public String exportQuickDeduction(HrQuickDeductionDTO quickDeductionDTO,HttpServletResponse httpServletResponse) {
        QueryWrapper<HrQuickDeduction> qw = new QueryWrapper<>();
        qw.in(CollectionUtils.isNotEmpty(quickDeductionDTO.getIds()),"id",quickDeductionDTO.getIds());
        List<HrQuickDeduction> list = this.hrQuickDeductionRepository.selectList(qw);
        if (list.isEmpty()) {
            throw new CommonException("未查到数据");
        }
        //List<HrQuickDeductionDTO> hrQuickDeductionDTOS = hrQuickDeductionMapper.toDto(list);
        //重新赋值小数
        List<HrQuickDeductionExport> hrQuickDeductionExports = new ArrayList<>();
        for (HrQuickDeduction hrQuickDeductionDTO : list) {
            HrQuickDeductionExport hrQuickDeductionExport = new HrQuickDeductionExport();
            BeanUtils.copyProperties(hrQuickDeductionDTO,hrQuickDeductionExport);
            String taxRate = extracted(0, hrQuickDeductionDTO.getTaxRate()).toString();
            hrQuickDeductionExport.setTaxRateExcel(taxRate + "%");
            hrQuickDeductionExports.add(hrQuickDeductionExport);
        }
        List<String> ids = list.stream().map(HrQuickDeduction::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(hrQuickDeductionExports, "速算扣除数", HrQuickDeductionExport.class);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.QUICK_DEDUCTION.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids));
        return fileUrl;
    }

    @Override
    public String importQuickDeduction(MultipartFile file) {
        // 设置进度条
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.QUICK_DEDUCTION.getValue()+ RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.quickDeductionExpenseComponent.quickDeduction(inputStream, redisKey, jwtUserDTO);
        return redisKey;
//        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
//
//        ExcelImportResult<HrQuickDeductionDTO> result =
//            ExcelUtils.importExcel(file, 0, 1, true, HrQuickDeductionDTO.class);
//        int size = result.getList().size();
//        if (size == 0 || size > 1000) {
//            throw new CommonException("最少导入一条数据，最多导入1000条数据");
//        }
//        //进度条
//        String key = jwtUserDTO.getId() + "Talent" + redisKey;
//        redisCache.setCacheObject(key, 0, 10, TimeUnit.MINUTES);
//        try {
//            //导入数据
//            this.saveData(result, key);
//        } catch (Exception e) {
//            redisCache.deleteObject(key);
//            throw new CommonException(e.getMessage());
//        }
//        ImportResultDTO resultDTO;
//        try {
//            resultDTO = ImportResultUtils.writeErrorFile("速算扣除数" + System.currentTimeMillis(), HrQuickDeductionDTO.class, result, fileTempPath);
//            // 判断是否需要上传错误文件
//            if (resultDTO.getFailureFileUrl() != null) {
//                String fileUrl = this.hrAppendixService.uploadErrorImportFile(resultDTO.getFailureFileUrl());
//                resultDTO.setFailureFileUrl(fileUrl);
//            }
//        } catch (IOException e) {
//            log.error("速算扣除数导入异常:{}", e.getMessage());
//            return ResponseUtil.buildError(e.getMessage());
//        } finally {
//            redisCache.deleteObject(key);
//        }
//        // 操作日志
//        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.QUICK_DEDUCTION.getValue(), BusinessTypeEnum.IMPORT.getKey(), file, JSON.toJSONString(resultDTO), ImportResultDTO.class);
//        return ResponseUtil.buildSuccess(resultDTO);
    }

    @Override
    public String importQuickDeductionTemplate(HttpServletResponse response) {
//        ExportParams params = new ExportParams(null, "速算扣除数");
//        params.setStyle(ExcelStyleUtils.class);
//        List<HrQuickDeductionExport> list = new ArrayList<>();
//        HrQuickDeductionExport hrQuickDeductionExport = new HrQuickDeductionExport();
//        hrQuickDeductionExport.setQuickDeductionSeries("1");
//        hrQuickDeductionExport.setQuickDeductionNumber(2520);
//        hrQuickDeductionExport.setTaxRateExcel("3%");
//        hrQuickDeductionExport.setMinPayTaxes(36000);
//        hrQuickDeductionExport.setMaxPayTaxes(144000);
//        list.add(hrQuickDeductionExport);
//        Workbook workbook = ExcelExportUtil.exportExcel(params, HrQuickDeductionExport.class, list);
//        ExcelUtils.downLoadExcel("速算扣除数导入模板.xlsx", response, workbook);
        //ExcelUtils.exportExcelWithNoHeader(list, "速算扣除数导入模板", "sheet1", HrQuickDeductionExport.class, response);
        return excelPrefix + "速算扣除数导入模板.xlsx";
    }

    private void saveData(ExcelImportResult<HrQuickDeductionDTO> result, String key) {
        int listSize = result.getList().size();
        int scale = 0;
        //定义级数的集合
        List<String> arrayList = new ArrayList<>();
        ArrayList<HrQuickDeduction> successResult = new ArrayList<>();
        for (HrQuickDeductionDTO hrQuickDeductionDTO : result.getList()) {
            try {
                String quickDeductionSeries = hrQuickDeductionDTO.getQuickDeductionSeries();
                if (arrayList.contains(quickDeductionSeries)){
                    throw new CommonException("级数不可以重复");
                }
                arrayList.add(quickDeductionSeries);
                if (hrQuickDeductionDTO.getMinPayTaxes()!=null&&hrQuickDeductionDTO.getMaxPayTaxes()!=null&&hrQuickDeductionDTO.getMinPayTaxes()>hrQuickDeductionDTO.getMaxPayTaxes()){
                    throw new CommonException("全年应缴纳所得额最小值不能大于全年应缴纳所得额最大值");
                }
                HrQuickDeduction hrQuickDeduction = hrQuickDeductionMapper.toEntity(hrQuickDeductionDTO);
                String taxRateExcel = hrQuickDeductionDTO.getTaxRateExcel();

                BigDecimal taxRate =null;
                //如果包含% 并去掉 在转化成小数
                if (taxRateExcel.contains("%")) {
                    taxRateExcel = taxRateExcel.replace("%", "").replaceAll(" ", "");
                    BigDecimal bigDecimal = null;
                    try {
                        bigDecimal = new BigDecimal(taxRateExcel);
                    } catch (Exception e) {
                        throw new CommonException("个人比例数据格式不正确");
                    }
                    taxRate = extracted(1, bigDecimal);
                    taxRate = importVerify(taxRate.toString(), "个人比例数据格式不正确");
                }
                //如果不包含%判断是否小于1
                else {
                    taxRate = importVerify(taxRateExcel, "个人比例数据格式不正确");
                }
                hrQuickDeduction.setTaxRate(taxRate);
                //save(hrQuickDeduction);
                successResult.add(hrQuickDeduction);
            } catch (Exception e) {
                log.error("速算扣除数异常:{}", e.getMessage());
                hrQuickDeductionDTO.setErrorMsg(e.getMessage());
            } finally {
                scale++;
                int i = CalculateUtils.calculationProgress(scale, listSize);
                redisCache.setCacheObject(key, i, 10, TimeUnit.MINUTES);
            }

        }
        if (result.getList().size()==successResult.size()){
            //先删除所有数据
            QueryWrapper<HrQuickDeduction> qw = new QueryWrapper<>();
            qw.eq("is_delete",0);
            remove(qw);
            saveBatch(successResult);
            redisCache.setCacheObject(key, 100, 10, TimeUnit.MINUTES);
        }
        else {
            for (HrQuickDeductionDTO hrQuickDeductionDTO : result.getList()) {
                if (hrQuickDeductionDTO.getErrorMsg() == null){
                    hrQuickDeductionDTO.setErrorMsg("正常数据");
                }
            }
        }
    }

    /**
     * 处理小数
     *
     * @param type 0 乘法 1除法
     * @param personageScaleExcel
     * @return
     */
    private BigDecimal extracted(int type, BigDecimal personageScaleExcel) {
        BigDecimal bignum = new BigDecimal("100");
        BigDecimal multiply = null;
        if (type == 0) {
            multiply = personageScaleExcel.multiply(bignum).setScale(2, BigDecimal.ROUND_DOWN);
        } else {
            multiply = personageScaleExcel.divide(bignum, 4, BigDecimal.ROUND_DOWN);
        }
        return multiply;
    }

    /**
     * 验证是否大一1
     *
     * @param unitPension
     * @param message
     */
    private BigDecimal importVerify(String unitPension, String message) {
        BigDecimal unitPensionBig = null;
        try {
            unitPensionBig = new BigDecimal(unitPension);
        } catch (Exception e) {
            throw new CommonException(message);
        }
        BigDecimal bigDecimal = new BigDecimal("1.00");
        if (unitPensionBig.compareTo(bigDecimal) == 1) {
            throw new CommonException(message);
        }
        return unitPensionBig;
    }
}
