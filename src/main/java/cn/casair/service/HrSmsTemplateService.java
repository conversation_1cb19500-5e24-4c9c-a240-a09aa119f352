package cn.casair.service;
import cn.casair.domain.HrSmsTemplate;
import cn.casair.dto.HrSmsTemplateDTO;
import cn.casair.dto.excel.HrSmsBatchExport;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
public interface HrSmsTemplateService extends IService<HrSmsTemplate> {


    /**
     * 创建
     * @param hrSmsTemplateDTO
     * @return
     */
    HrSmsTemplateDTO createHrSmsTemplate(HrSmsTemplateDTO hrSmsTemplateDTO);

    /**
     * 修改
     * @param hrSmsTemplateDTO
     * @return
     */
    Optional<HrSmsTemplateDTO> updateHrSmsTemplate(HrSmsTemplateDTO hrSmsTemplateDTO);

    /**
     * 查询详情
     * @param id
     * @return
     */
    HrSmsTemplateDTO getHrSmsTemplate(String id);

    /**
     * 删除
     * @param id
     */
    void deleteHrSmsTemplate(String id);

    /**
     * 批量删除
     * @param ids
     */
    void deleteHrSmsTemplate(List<String> ids);

    /**
     * 分页查询
     * @param hrSmsTemplateDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrSmsTemplateDTO hrSmsTemplateDTO,Long pageNumber,Long pageSize);

    /**
     * 查询所有标题
     * @return
     */
    List<HrSmsTemplate> selectTitle();

    /**
     * 导出
     * @param hrSmsTemplateDTO
     * @param httpServletResponse
     */
    String hrSmsTemplateService(HrSmsTemplateDTO hrSmsTemplateDTO, HttpServletResponse httpServletResponse);

    /**
     *根据短信模板，生成发送短信参数并发动短信
     * @param hashMap key 是 参数索引，重1开始 value是内容
     */
    String hrSmsTemplateSendSms(HashMap<Integer, String> hashMap, String templateId,String phone);

    /**
     * 批量发送短信模板
     * @param httpServletResponse
     * @return
     */
    String batchTemplates(HttpServletResponse httpServletResponse);

    /**
     * 批量导入短信
     * @param file
     * @return
     */
    String importBatch(MultipartFile file);


    /**
     * 导出批量发送短错误数据
     * @param hrSmsBatchTemplateList
     * @param httpServletResponse
     * @return
     */
    String exportSmsBatch(List<HrSmsBatchExport> hrSmsBatchTemplateList, HttpServletResponse httpServletResponse);

    /**
     * 返回短信模板内容
     * @param params
     * @param templateCode
     * @return
     */
    String smsTemplateContent(HashMap<Integer, String> params, String templateCode);

    String hrSmsTemplateSendSms(HashMap<Integer, String> params, String templateCode, String phone, String name);

    /**
     * 发送模板短信
     *
     * @param realName     当前操作人真实姓名
     * @param phoneNumber  发送手机号码
     * @param templateCode 模板编号
     * @param datas        模板数据
     * @return void
     * <AUTHOR>
     * @date 2021/9/14
     **/
    String sendMessage(String realName, String phoneNumber, String templateCode, String[] datas);
}
