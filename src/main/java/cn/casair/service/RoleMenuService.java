package cn.casair.service;

import cn.casair.domain.RoleMenu;
import cn.casair.dto.RoleMenuDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 角色菜单关联表服务类
 *
 * <AUTHOR>
 * @since 2020-05-12
 */
public interface RoleMenuService extends IService<RoleMenu> {


    /**
     * 创建角色菜单关联表
     *
     * @param roleMenuDTO
     * @return
     */
    RoleMenuDTO createRoleMenu(RoleMenuDTO roleMenuDTO);

    /**
     * 修改角色菜单关联表
     *
     * @param roleMenuDTO
     * @return
     */
    Optional<RoleMenuDTO> updateRoleMenu(RoleMenuDTO roleMenuDTO);

    /**
     * 查询角色菜单关联表详情
     *
     * @param id
     * @return
     */
    RoleMenuDTO getRoleMenu(Integer id);

    /**
     * 删除角色菜单关联表
     *
     * @param id
     */
    void deleteRoleMenu(Integer id);

    /**
     * 批量删除角色菜单关联表
     *
     * @param ids
     */
    void deleteRoleMenu(List<Integer> ids);

    /**
     * 分页查询角色菜单关联表
     *
     * @param roleMenuDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(RoleMenuDTO roleMenuDTO, Long pageNumber, Long pageSize);

    /**
     * 根据角色id删除角色和菜单的关联数据
     *
     * @param roleId
     * @return void
     * <AUTHOR> yanglei 2020/5/12 22:56
     */
    void deleteByRoleId(Integer roleId);
}
