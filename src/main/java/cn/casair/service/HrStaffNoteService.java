package cn.casair.service;
import cn.casair.domain.HrStaffNote;
import cn.casair.dto.HrStaffNoteDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 员工标签服务类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
public interface HrStaffNoteService extends IService<HrStaffNote> {


    /**
     * 创建员工标签
     * @param hrStaffNoteDTO
     * @return
     */
    HrStaffNoteDTO createHrStaffNote(HrStaffNoteDTO hrStaffNoteDTO);

    /**
     * 修改员工标签
     * @param hrStaffNoteDTO
     * @return
     */
    Optional<HrStaffNoteDTO> updateHrStaffNote(HrStaffNoteDTO hrStaffNoteDTO);

    /**
     * 查询员工标签详情
     * @param id
     * @return
     */
    HrStaffNoteDTO getHrStaffNote(String id);

    /**
     * 删除员工标签
     * @param id
     */
    void deleteHrStaffNote(String id);

    /**
     * 批量删除员工标签
     * @param ids
     */
    void deleteHrStaffNote(List<String> ids);

    /**
     * 分页查询员工标签
     * @param hrStaffNoteDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrStaffNoteDTO hrStaffNoteDTO,Long pageNumber,Long pageSize);
    }
