package cn.casair.repository;

import cn.casair.domain.SysOperLog;
import cn.casair.dto.SysOperLogDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 操作日志记录数据库操作类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Repository
public interface SysOperLogRepository extends BaseMapper<SysOperLog> {

    /**
     * 分页查询日志列表
     *
     * @param page          分页
     * @param sysOperLogDTO 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.SysOperLogDTO>
     * <AUTHOR>
     * @date 2021/8/31
     **/
    IPage<SysOperLogDTO> selectForPage(Page<SysOperLog> page, @Param("param") SysOperLogDTO sysOperLogDTO);

    /**
     * 不分页获取日志列表
     *
     * @param ids id列表
     * @return java.util.List<cn.casair.dto.SysOperLogDTO>
     * <AUTHOR>
     * @date 2021/8/31
     **/
    List<SysOperLogDTO> selectByIds(@Param("ids") List<String> ids);
}
