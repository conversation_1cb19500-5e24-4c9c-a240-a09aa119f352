package cn.casair.service;

import cn.casair.domain.HrAccumulationFund;
import cn.casair.domain.HrSocialSecurity;
import cn.casair.domain.HrStaffWelfareRecord;
import cn.casair.domain.HrWelfareCompensation;
import cn.casair.dto.HrEmployeeWelfareDTO;
import cn.casair.dto.HrStaffWelfareRecordDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 员工每月福利配置服务类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface HrStaffWelfareRecordService extends IService<HrStaffWelfareRecord> {


    /**
     * 创建员工每月福利配置
     *
     * @param hrStaffWelfareRecordDTO
     * @return
     */
    HrStaffWelfareRecordDTO createHrStaffWelfareRecord(HrStaffWelfareRecordDTO hrStaffWelfareRecordDTO);

    /**
     * 修改员工每月福利配置
     *
     * @param hrStaffWelfareRecordDTO
     * @return
     */
    Optional<HrStaffWelfareRecordDTO> updateHrStaffWelfareRecord(HrStaffWelfareRecordDTO hrStaffWelfareRecordDTO);

    /**
     * 查询员工每月福利配置详情
     *
     * @param id
     * @return
     */
    HrStaffWelfareRecordDTO getHrStaffWelfareRecord(String id);

    /**
     * 删除员工每月福利配置
     *
     * @param id
     */
    void deleteHrStaffWelfareRecord(String id);

    /**
     * 批量删除员工每月福利配置
     *
     * @param ids
     */
    void deleteHrStaffWelfareRecord(List<String> ids);

    /**
     * 分页查询员工每月福利配置
     *
     * @param hrStaffWelfareRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrStaffWelfareRecordDTO hrStaffWelfareRecordDTO, Long pageNumber, Long pageSize);

    /**
     * 定时任务检查并新增员工每月福利配置记录
     *
     * @return void
     * <AUTHOR>
     * @date 2021/11/15
     **/
    void checkAndInsertStaffWelfareRecord();

    /**
     * 查询员工缴费年月福利配置记录
     *
     * @param query
     * @return cn.casair.domain.HrStaffWelfareRecord
     * <AUTHOR>
     * @date 2021/11/15
     **/
    HrStaffWelfareRecord selectByObject(Map<String, Object> query);

    /**
     * 新增或更新员工每月福利配置
     * （员工福利）
     *
     * @param hrStaffWelfareRecord
     * @param hrEmployeeWelfareNew
     * @param payYear
     * @param payMonthly
     * @return void
     * <AUTHOR>
     * @date 2021/11/15
     **/
    void insertOrUpdateStaffWelfareRecord(HrStaffWelfareRecord hrStaffWelfareRecord, HrEmployeeWelfareDTO hrEmployeeWelfareNew, int payYear, int payMonthly);

    /**
     * 新增或更新员工每月福利配置
     * （社保）
     *
     * @param hrStaffWelfareRecord
     * @param hrSocialSecurity
     * @param payYear
     * @param payMonthly
     * @return void
     * <AUTHOR>
     * @date 2021/11/15
     **/
    void insertOrUpdateStaffWelfareRecord(HrEmployeeWelfareDTO employeeWelfareOld, HrStaffWelfareRecord hrStaffWelfareRecord, HrSocialSecurity hrSocialSecurity, int payYear, int payMonthly);

    /**
     * 新增或更新员工每月福利配置
     * （公积金）
     *
     * @param hrStaffWelfareRecord
     * @param hrAccumulationFund
     * @param payYear
     * @param payMonthly
     * @return void
     * <AUTHOR>
     * @date 2021/11/15
     **/
    void insertOrUpdateStaffWelfareRecord(HrEmployeeWelfareDTO employeeWelfareOld, HrStaffWelfareRecord hrStaffWelfareRecord, HrAccumulationFund hrAccumulationFund, int payYear, int payMonthly);

    /**
     * 新增或更新员工每月福利配置
     *
     * @param hrWelfareCompensationList
     * @return void
     * <AUTHOR>
     * @date 2021/12/6
     **/
    void insertOrUpdateStaffWelfareRecord(List<HrWelfareCompensation> hrWelfareCompensationList);
}
