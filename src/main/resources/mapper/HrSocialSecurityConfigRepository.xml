<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrSocialSecurityConfigRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, social_security_id, special_field, alone_cardinal, merge_cardinal,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrSocialSecurityConfig">
        <id column="id" property="id"/>
        <result column="social_security_id" property="socialSecurityId"/>
        <result column="special_field" property="specialField"/>
        <result column="alone_cardinal" property="aloneCardinal"/>
        <result column="merge_cardinal" property="mergeCardinal"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

</mapper>
