package cn.casair.repository;

import cn.casair.domain.HrContractGroup;
import cn.casair.dto.HrContractGroupDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 合同组数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Repository
public interface HrContractGroupRepository extends BaseMapper<HrContractGroup> {

    /**
     * 合同组使用次数+1
     *
     * @param contractGroupId
     * @return int
     * <AUTHOR>
     * @date 2021/9/28
     **/
    int frequencyPlus(@Param("contractGroupId") String contractGroupId);

    IPage<HrContractGroup> selectPageContractGroup(Page<HrContractGroup> page, @Param("param1") HrContractGroupDTO hrContractGroupDTO);

    HrContractGroupDTO selectName(String contractGroupName);

    List<HrContractGroupDTO> selectContractGroupList();
}
