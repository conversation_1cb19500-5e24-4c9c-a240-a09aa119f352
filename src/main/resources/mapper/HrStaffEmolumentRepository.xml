<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffEmolumentRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_id, basic_wage, salary, owned_bank, salary_card_num, bank_card_url, social_security_num, social_security_cardinal, medical_insurance_num, medical_insurance_cardinal,
        accumulation_fund_num, accumulation_fund_cardinal, social_security_cardinal_personal, medical_insurance_cardinal_personal,
        unit_pension_cardinal,unit_unemployment_cardinal,unit_maternity_cardinal,work_injury_cardinal,unit_large_medical_expense,replenish_work_injury_expense,
        personal_pension_cardinal,personal_unemployment_cardinal,personal_maternity_cardinal,personal_large_medical_expense,
        unit_enterprise_annuity,personal_enterprise_annuity,commercial_insurance,
        seniority_wage_base, pay_year, pay_monthly, seniority_pay, is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffEmolument">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="basic_wage" property="basicWage"/>
        <result column="salary" property="salary"/>
        <result column="owned_bank" property="ownedBank"/>
        <result column="salary_card_num" property="salaryCardNum"/>
        <result column="bank_card_url" property="bankCardUrl"/>
        <result column="social_security_num" property="socialSecurityNum"/>
        <result column="social_security_cardinal" property="socialSecurityCardinal"/>
        <result column="medical_insurance_num" property="medicalInsuranceNum"/>
        <result column="medical_insurance_cardinal" property="medicalInsuranceCardinal"/>
        <result column="accumulation_fund_num" property="accumulationFundNum"/>
        <result column="accumulation_fund_cardinal" property="accumulationFundCardinal"/>
        <result column="social_security_cardinal_personal" property="socialSecurityCardinalPersonal"/>
        <result column="medical_insurance_cardinal_personal" property="medicalInsuranceCardinalPersonal"/>
        <result column="seniority_wage_base" property="seniorityWageBase"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="seniority_pay" property="seniorityPay"/>
        <result column="unit_pension_cardinal" property="unitPensionCardinal"/>
        <result column="unit_unemployment_cardinal" property="unitUnemploymentCardinal"/>
        <result column="unit_maternity_cardinal" property="unitMaternityCardinal"/>
        <result column="work_injury_cardinal" property="workInjuryCardinal"/>
        <result column="unit_large_medical_expense" property="unitLargeMedicalExpense"/>
        <result column="replenish_work_injury_expense" property="replenishWorkInjuryExpense"/>
        <result column="personal_pension_cardinal" property="personalPensionCardinal"/>
        <result column="personal_unemployment_cardinal" property="personalUnemploymentCardinal"/>
        <result column="personal_maternity_cardinal" property="personalMaternityCardinal"/>
        <result column="personal_large_medical_expense" property="personalLargeMedicalExpense"/>

        <result column="unit_enterprise_annuity" property="unitEnterpriseAnnuity"/>
        <result column="personal_enterprise_annuity" property="personalEnterpriseAnnuity"/>
        <result column="commercial_insurance" property="commercialInsurance"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getByStaffId" resultType="cn.casair.domain.HrStaffEmolument">
        SELECT
            *
        FROM hr_staff_emolument
        WHERE is_delete = 0
        AND staff_id = #{staffId}
    </select>

    <update id="updateStaffBankInfo">
        UPDATE hr_staff_emolument
        SET owned_bank      = #{ownedBank},
            salary_card_num = #{bankNo}
        WHERE is_delete = 0
          AND staff_id = #{staffId}
    </update>

    <update id="updateByStaffId">
        UPDATE hr_staff_emolument
        <set>
            <if test="basicWage!=null">
                basic_wage = #{basicWage},
            </if>
            <if test="salary!=null">
                salary = #{salary},
            </if>
            <if test="ownedBank!=null and ownedBank!=''">
                owned_bank = #{ownedBank},
            </if>
            <if test="salaryCardNum!=null and salaryCardNum!=''">
                salary_card_num = #{salaryCardNum},
            </if>
            <if test="socialSecurityNum!=null and socialSecurityNum!=''">
                social_security_num = #{socialSecurityNum},
            </if>
            <if test="socialSecurityCardinal!=null">
                social_security_cardinal = #{socialSecurityCardinal},
            </if>
            <if test="medicalInsuranceNum!=null">
                medical_insurance_num = #{medicalInsuranceNum},
            </if>
            <if test="medicalInsuranceCardinal!=null">
                medical_insurance_cardinal = #{medicalInsuranceCardinal},
            </if>
            <if test="accumulationFundNum!=null">
                accumulation_fund_num = #{accumulationFundNum},
            </if>
            <if test="accumulationFundCardinal!=null">
                accumulation_fund_cardinal = #{accumulationFundCardinal},
            </if>
            <if test="socialSecurityCardinalPersonal!=null">
                social_security_cardinal_personal = #{socialSecurityCardinalPersonal},
            </if>
            <if test="medicalInsuranceCardinalPersonal!=null">
                medical_insurance_cardinal_personal = #{medicalInsuranceCardinalPersonal},
            </if>
            <if test="seniorityWageBase!=null">
                seniority_wage_base = #{seniorityWageBase},
            </if>
            <if test="payYear!=null">
                pay_year = #{payYear},
            </if>
            <if test="payMonthly!=null">
                pay_monthly = #{payMonthly},
            </if>
            <if test="seniorityPay!=null">
                seniority_pay = #{seniorityPay},
            </if>
            <if test="unitPensionCardinal != null">
                unit_pension_cardinal = #{unitPensionCardinal},
            </if>
            <if test="unitUnemploymentCardinal != null">
                unit_unemployment_cardinal = #{unitUnemploymentCardinal},
            </if>
            <if test="unitMaternityCardinal != null">
                unit_maternity_cardinal = #{unitMaternityCardinal},
            </if>
            <if test="workInjuryCardinal != null">
                work_injury_cardinal = #{workInjuryCardinal},
            </if>
            <if test="unitLargeMedicalExpense != null">
                unit_large_medical_expense = #{unitLargeMedicalExpense},
            </if>
            <if test="replenishWorkInjuryExpense != null">
                replenish_work_injury_expense = #{replenishWorkInjuryExpense},
            </if>
            <if test="personalPensionCardinal != null">
                personal_pension_cardinal = #{personalPensionCardinal},
            </if>
            <if test="personalUnemploymentCardinal != null">
                personal_unemployment_cardinal = #{personalUnemploymentCardinal},
            </if>
            <if test="personalMaternityCardinal != null">
                personal_maternity_cardinal = #{personalMaternityCardinal},
            </if>
            <if test="personalLargeMedicalExpense != null">
                personal_large_medical_expense = #{personalLargeMedicalExpense},
            </if>
            <if test="unitEnterpriseAnnuity != null">
                unit_enterprise_annuity = #{unitEnterpriseAnnuity},
            </if>
            <if test="personalEnterpriseAnnuity != null">
                personal_enterprise_annuity = #{personalEnterpriseAnnuity},
            </if>
            <if test="commercialInsurance != null">
                commercial_insurance = #{commercialInsurance},
            </if>
        </set>
        WHERE staff_id = #{id}
        AND is_delete = 0
    </update>

    <update id="updateStaffEmolumentByStaffId">
        UPDATE hr_staff_emolument
        <set>
            <if test="basicWage!=null">
                basic_wage = #{basicWage},
            </if>
            <if test="salary!=null">
                salary = #{salary},
            </if>
            <if test="ownedBank!=null and ownedBank!=''">
                owned_bank = #{ownedBank},
            </if>
            <if test="salaryCardNum!=null and salaryCardNum!=''">
                salary_card_num = #{salaryCardNum},
            </if>
            <if test="bankCardUrl!=null and bankCardUrl!=''">
                bank_card_url = #{bankCardUrl},
            </if>
            <if test="socialSecurityNum!=null and socialSecurityNum!=''">
                social_security_num = #{socialSecurityNum},
            </if>
            <if test="socialSecurityCardinal!=null">
                social_security_cardinal = #{socialSecurityCardinal},
            </if>
            <if test="medicalInsuranceNum!=null">
                medical_insurance_num = #{medicalInsuranceNum},
            </if>
            <if test="medicalInsuranceCardinal!=null">
                medical_insurance_cardinal = #{medicalInsuranceCardinal},
            </if>
            <if test="accumulationFundNum!=null">
                accumulation_fund_num = #{accumulationFundNum},
            </if>
            <if test="accumulationFundCardinal!=null">
                accumulation_fund_cardinal = #{accumulationFundCardinal},
            </if>
            <if test="socialSecurityCardinalPersonal!=null">
                social_security_cardinal_personal   = #{socialSecurityCardinalPersonal},
            </if>
            <if test="medicalInsuranceCardinalPersonal!=null">
                medical_insurance_cardinal_personal = #{medicalInsuranceCardinalPersonal},
            </if>
            <if test="seniorityWageBase!=null">
                seniority_wage_base = #{seniorityWageBase},
            </if>
            <if test="payYear!=null">
                pay_year = #{payYear},
            </if>
            <if test="payMonthly!=null">
                pay_monthly = #{payMonthly},
            </if>
            <if test="seniorityPay!=null">
                seniority_pay = #{seniorityPay},
            </if>
            <if test="unitPensionCardinal != null">
                unit_pension_cardinal = #{unitPensionCardinal},
            </if>
            <if test="unitUnemploymentCardinal != null">
                unit_unemployment_cardinal = #{unitUnemploymentCardinal},
            </if>
            <if test="unitMaternityCardinal != null">
                unit_maternity_cardinal = #{unitMaternityCardinal},
            </if>
            <if test="workInjuryCardinal != null">
                work_injury_cardinal = #{workInjuryCardinal},
            </if>
            <if test="unitLargeMedicalExpense != null">
                unit_large_medical_expense = #{unitLargeMedicalExpense},
            </if>
            <if test="replenishWorkInjuryExpense != null">
                replenish_work_injury_expense = #{replenishWorkInjuryExpense},
            </if>
            <if test="personalPensionCardinal != null">
                personal_pension_cardinal = #{personalPensionCardinal},
            </if>
            <if test="personalUnemploymentCardinal != null">
                personal_unemployment_cardinal = #{personalUnemploymentCardinal},
            </if>
            <if test="personalMaternityCardinal != null">
                personal_maternity_cardinal = #{personalMaternityCardinal},
            </if>
            <if test="personalLargeMedicalExpense != null">
                personal_large_medical_expense = #{personalLargeMedicalExpense},
            </if>
            <if test="unitEnterpriseAnnuity != null">
                unit_enterprise_annuity = #{unitEnterpriseAnnuity},
            </if>
            <if test="personalEnterpriseAnnuity != null">
                personal_enterprise_annuity = #{personalEnterpriseAnnuity},
            </if>
            <if test="commercialInsurance != null">
                commercial_insurance = #{commercialInsurance},
            </if>
        </set>
        WHERE staff_id = #{staffId}
        AND is_delete = 0
    </update>

    <update id="rollBackStaffEmolumentByStaffId">
        UPDATE hr_staff_emolument
        <set>
            <if test="socialSecurityCardinal!=null">
                social_security_cardinal = #{socialSecurityCardinal},
            </if>
            <if test="medicalInsuranceCardinal!=null">
                medical_insurance_cardinal = #{medicalInsuranceCardinal},
            </if>
            <if test="accumulationFundCardinal!=null">
                accumulation_fund_cardinal = #{accumulationFundCardinal},
            </if>
        </set>
        WHERE staff_id = #{staffId}
        AND is_delete = 0
    </update>

    <update id="updatePaymentDateByBillId">
        UPDATE hr_staff_emolument
        SET pay_year    = #{payYear},
            pay_monthly = #{payMonthly}
        WHERE is_delete = 0
          AND staff_id IN (
            SELECT staff_id
            FROM hr_bill_detail
            WHERE bill_id = #{billId}
              AND is_delete = 0
              AND is_used = 1
              AND staff_id IS NOT NULL
        )
    </update>

    <update id="updateStaffPaymentDate">
        UPDATE hr_staff_emolument
        SET pay_year    = #{payYear},
            pay_monthly = #{payMonthly}
        WHERE is_delete = 0
          AND staff_id = #{staffId}
    </update>

    <update id="updateWelfareById">
        UPDATE hr_staff_emolument
        SET basic_wage                 = #{params.basicWage},
            salary                     = #{params.salary},
            owned_bank                 = #{params.ownedBank},
            salary_card_num            = #{params.salaryCardNum},
            social_security_num        = #{params.socialSecurityNum},
            social_security_cardinal   = #{params.socialSecurityCardinal},
            medical_insurance_num      = #{params.medicalInsuranceNum},
            medical_insurance_cardinal = #{params.medicalInsuranceCardinal},
            accumulation_fund_num      = #{params.accumulationFundNum},
            accumulation_fund_cardinal = #{params.accumulationFundCardinal},
            social_security_cardinal_personal   = #{params.socialSecurityCardinalPersonal},
            medical_insurance_cardinal_personal = #{params.medicalInsuranceCardinalPersonal},
            unit_pension_cardinal               = #{params.unitPensionCardinal},
            unit_unemployment_cardinal          = #{params.unitUnemploymentCardinal},
            unit_maternity_cardinal             = #{params.unitMaternityCardinal},
            work_injury_cardinal                = #{params.workInjuryCardinal},
            unit_large_medical_expense          = #{params.unitLargeMedicalExpense},
            replenish_work_injury_expense       = #{params.replenishWorkInjuryExpense},
            personal_pension_cardinal           = #{params.personalPensionCardinal},
            personal_unemployment_cardinal      = #{params.personalUnemploymentCardinal},
            personal_large_medical_expense      = #{params.personalLargeMedicalExpense},
            unit_enterprise_annuity             = #{params.unitEnterpriseAnnuity},
            personal_enterprise_annuity         = #{params.personalEnterpriseAnnuity},
            commercial_insurance                = #{params.commercialInsurance},
            seniority_wage_base        = #{params.seniorityWageBase},
            seniority_pay              = #{params.seniorityPay},
            pay_year                   = #{params.payYear},
            pay_monthly                = #{params.payMonthly}
        WHERE staff_id = #{params.id}
          AND is_delete = 0
    </update>

    <update id="updateOldPaymentDate">
        UPDATE
            hr_staff_emolument
        SET
            pay_year                   = #{params.payYear},
            pay_monthly                = #{params.payMonthly}
        WHERE staff_id = #{params.id}
          AND is_delete = 0
    </update>

    <select id="findSocialSecurity" resultType="cn.casair.dto.HrSocialSecurityDTO">
        SELECT
            a.*
        FROM
            hr_social_security a
        LEFT JOIN hr_client b ON a.id = b.social_security_type_id
        LEFT JOIN hr_talent_staff c ON b.id = c.client_id
        WHERE a.is_delete = 0 AND c.id = #{staffId}
    </select>

    <select id="findAccumulationFund" resultType="cn.casair.dto.HrAccumulationFundDTO">
        SELECT
            a.*
        FROM
            hr_accumulation_fund a
        LEFT JOIN hr_client b ON a.id = b.provident_fund_type_id
        LEFT JOIN hr_talent_staff c ON b.id = c.client_id
        WHERE a.is_delete = 0 AND c.id = #{staffId}
    </select>

    <select id="getHrStaffEmolumentByStaffId" resultType="cn.casair.domain.HrStaffEmolument">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_staff_emolument
        WHERE is_delete = 0
          AND staff_id = #{staffId}
    </select>

    <select id="getHrStaffEmolumentListByIds" resultType="cn.casair.domain.HrStaffEmolument">
        SELECT id,
               staff_id,
               social_security_cardinal,
               medical_insurance_cardinal,
               accumulation_fund_cardinal
        FROM hr_staff_emolument
        WHERE is_delete = 0
          AND staff_id IN
        <foreach collection="staffIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="selectByClientId" resultType="cn.casair.dto.HrEmployeeWelfareDTO">
        SELECT
            hts.id,
            hts.client_id,
            hts.`name`,
            hts.staff_status,
            hts.iz_insured,
            hts.certificate_num,
--             hse.social_security_cardinal,
            hse.unit_pension_cardinal,
            hse.unit_unemployment_cardinal,
            hse.unit_maternity_cardinal,
            hse.work_injury_cardinal,
            hse.medical_insurance_cardinal,
            hse.personal_pension_cardinal,
            hse.personal_unemployment_cardinal,
            hse.medical_insurance_cardinal_personal,
            hse.accumulation_fund_cardinal,
            hse.unit_large_medical_expense,
            hse.replenish_work_injury_expense,
            hse.personal_large_medical_expense,
            hse.pay_year,
            hse.pay_monthly,
            hse.personal_maternity_cardinal,
            hse.unit_enterprise_annuity,
            hse.personal_enterprise_annuity,
            hse.commercial_insurance,
            hss.personal_maternity,
            hss.unit_pension,
            hss.unit_medical,
            hss.work_injury,
            hss.unit_unemployment,
            hss.unit_maternity,
            hss.personal_pension,
            hss.personal_medical,
            hss.personal_unemployment,
            haf.unit_scale,
            haf.personage_scale
        FROM
            hr_talent_staff hts
                LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hts.id
                AND hse.is_delete = 0
                LEFT JOIN hr_client hc ON hts.client_id = hc.id
                LEFT JOIN hr_social_security hss ON hss.id = hc.social_security_type_id
                AND hss.is_delete = 0
                LEFT JOIN hr_accumulation_fund haf ON haf.id = hc.provident_fund_type_id
                AND haf.is_delete = 0
        WHERE
            hc.id = #{clientId}
          AND hts.is_delete = 0
          AND hts.iz_default = 0
    </select>

    <select id="selectStaffWelfare" resultType="cn.casair.dto.HrEmployeeWelfareDTO">
        SELECT
            hts.id,
--             hse.social_security_cardinal,
            hse.unit_pension_cardinal,
            hse.unit_unemployment_cardinal,
            hse.unit_maternity_cardinal,
            hse.work_injury_cardinal,
            hse.medical_insurance_cardinal,
            hse.personal_pension_cardinal,
            hse.personal_unemployment_cardinal,
            hse.medical_insurance_cardinal_personal,
            hse.accumulation_fund_cardinal,
            hse.seniority_wage_base,
            hse.seniority_pay,
            hse.unit_large_medical_expense,
            hse.replenish_work_injury_expense,
            hse.personal_large_medical_expense,
            hse.pay_year,
            hse.pay_monthly,
            hse.personal_maternity_cardinal,
            hse.unit_enterprise_annuity,
            hse.personal_enterprise_annuity,
            hse.commercial_insurance,
            hss.personal_maternity,
            hss.name_of_beneficiary,
            hss.receiving_account,
            hss.account_bank,
            hss.unit_pension,
            hss.unit_medical,
            hss.work_injury,
            hss.unit_unemployment,
            hss.unit_maternity,
            hss.personal_pension,
            hss.personal_medical,
            hss.personal_unemployment,
            haf.payee_name,
            haf.payee_account,
            haf.payee_bank,
            haf.unit_scale,
            haf.personage_scale
        FROM
            hr_talent_staff hts
            LEFT JOIN hr_client hc ON hc.id = hts.client_id
            AND hc.is_delete = 0
            LEFT JOIN hr_social_security hss ON hss.id = hc.social_security_type_id
            AND hss.is_delete = 0
            LEFT JOIN hr_accumulation_fund haf ON haf.id = hc.provident_fund_type_id
            AND haf.is_delete = 0
            LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hts.id
            AND hse.is_delete = 0
        WHERE
            hts.is_delete = 0
            AND hts.iz_default = 0
            <if test="staffId!=null and staffId!=''">
                AND hts.id = #{staffId}
            </if>
    </select>

    <select id="getEmolumentByStaffId" resultType="cn.casair.dto.HrStaffEmolumentDTO">
        SELECT hse.id,
               hse.staff_id,
               hse.basic_wage,
               hse.salary,
               hse.owned_bank,
               hse.salary_card_num,
               hse.social_security_num,
--                hse.social_security_cardinal,
               hse.unit_pension_cardinal,
               hse.unit_unemployment_cardinal,
               hse.unit_maternity_cardinal,
               hse.work_injury_cardinal,
               hse.medical_insurance_num,
               hse.medical_insurance_cardinal,
               hse.personal_pension_cardinal,
               hse.personal_unemployment_cardinal,
               hse.medical_insurance_cardinal_personal,
               hse.accumulation_fund_num,
               hse.accumulation_fund_cardinal,
               hse.seniority_wage_base,
               hse.unit_large_medical_expense,
               hse.replenish_work_injury_expense,
               hse.personal_large_medical_expense,
               hse.personal_maternity_cardinal,
               hse.unit_enterprise_annuity,
               hse.personal_enterprise_annuity,
               hse .commercial_insurance,
               hse.pay_year,
               hse.pay_monthly,
               hse.seniority_pay
        FROM hr_staff_emolument hse
        WHERE is_delete = 0
          AND staff_id = #{staffId}
    </select>

</mapper>
