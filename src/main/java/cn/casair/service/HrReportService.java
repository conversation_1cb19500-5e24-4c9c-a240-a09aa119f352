package cn.casair.service;
import cn.casair.domain.HrReport;
import cn.casair.dto.HrReportDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;

/**
 * 报告管理服务类
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
public interface HrReportService extends IService<HrReport> {


    /**
     * 创建报告管理
     * @param hrReportDTO
     * @return
     */
    HrReportDTO createHrReport(HrReportDTO hrReportDTO);

    /**
     * 修改报告管理
     * @param hrReportDTO
     * @return
     */
    HrReportDTO updateHrReport(HrReportDTO hrReportDTO);

    /**
     * 查询报告管理详情
     * @param id
     * @return
     */
    HrReportDTO getHrReport(String id);

    /**
     * 删除报告管理
     * @param id
     */
    void deleteHrReport(String id);

    /**
     * 批量删除报告管理
     * @param ids
     */
    void deleteHrReport(List<String> ids);

    /**
     * 分页查询报告管理
     * @param hrReportDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrReportDTO hrReportDTO,Long pageNumber,Long pageSize);

    ResponseEntity<?> getHrReportBatch(List<HrReportDTO> hrReportDTO, String type);

    String getHrReportDownload(String id);

    String getHrReportExport(HrReportDTO hrReportDTO, HttpServletResponse response);

    IPage findPageSelectWXPaper(HrReportDTO hrReportDTO, Long pageNumber, Long pageSize);
}
