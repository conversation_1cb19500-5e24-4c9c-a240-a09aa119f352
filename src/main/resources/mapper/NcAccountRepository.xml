<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.NcAccountRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, content, accountcode, pk_accasoa, accountname, auxiliary, client_id, sort, debt, type,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.NcAccount">
        <id column="id" property="id"/>
        <result column="content" property="content"/>
        <result column="accountcode" property="accountcode"/>
        <result column="pk_accasoa" property="pkAccasoa"/>
        <result column="accountname" property="accountname"/>
        <result column="auxiliary" property="auxiliary"/>
        <result column="client_id" property="clientId"/>
        <result column="sort" property="sort"/>
        <result column="debt" property="debt"/>
        <result column="type" property="type"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findPage" resultType="cn.casair.dto.NcAccountDTO">
        select na.*,
               hc.client_name
        from nc_account na
        left join hr_client hc on hc.id = na.client_id
        where na.is_delete = 0
        <if test="param.content != null and param.content != ''">
            and na.content like concat('%',#{param.content},'%')
        </if>
        <if test="param.accountcode != null and param.accountcode != ''">
            and na.accountcode like concat('%',#{param.accountcode},'%')
        </if>
        order by na.sort asc, na.created_date desc
    </select>

</mapper>
