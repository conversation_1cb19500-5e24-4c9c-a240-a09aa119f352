package cn.casair.service;

import cn.casair.domain.HrPaperClient;
import cn.casair.dto.HrPaperClientDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 试卷关联客户表服务类
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
public interface HrPaperClientService extends IService<HrPaperClient> {


    /**
     * 创建试卷关联客户表
     * @param hrPaperClientDTO
     * @return
     */
    HrPaperClientDTO createHrPaperClient(HrPaperClientDTO hrPaperClientDTO);

    /**
     * 修改试卷关联客户表
     * @param hrPaperClientDTO
     * @return
     */
    Optional<HrPaperClientDTO> updateHrPaperClient(HrPaperClientDTO hrPaperClientDTO);

    /**
     * 查询试卷关联客户表详情
     * @param id
     * @return
     */
    HrPaperClientDTO getHrPaperClient(String id);

    /**
     * 删除试卷关联客户表
     * @param id
     */
    void deleteHrPaperClient(String id);

    /**
     * 批量删除试卷关联客户表
     * @param ids
     */
    void deleteHrPaperClient(List<String> ids);

    /**
     * 分页查询试卷关联客户表
     * @param hrPaperClientDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrPaperClientDTO hrPaperClientDTO,Long pageNumber,Long pageSize);
    }
