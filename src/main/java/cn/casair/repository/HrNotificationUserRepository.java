package cn.casair.repository;
import cn.casair.domain.HrNotificationUser;
import cn.casair.dto.HrNotificationMessageDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
@Repository
public interface HrNotificationUserRepository extends BaseMapper<HrNotificationUser> {

    void deleteUserid(HrNotificationUser hrNotificationUser);

    HrNotificationMessageDTO selectNotificationUser(@Param("param1")HrNotificationMessageDTO hrNotificationMessageDTO);
}
