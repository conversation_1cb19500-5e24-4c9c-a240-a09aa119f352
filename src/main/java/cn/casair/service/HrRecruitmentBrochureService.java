package cn.casair.service;

import cn.casair.domain.HrRecruitmentBrochure;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrRecruitmentBrochureDTO;
import cn.casair.dto.HrRecruitmentBulletinDTO;
import cn.casair.dto.HrTemplateDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * 招聘简章服务类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
public interface HrRecruitmentBrochureService extends IService<HrRecruitmentBrochure> {


    /**
     * 创建招聘简章
     *
     * @param hrRecruitmentBrochureDTO
     * @return
     */
    HrRecruitmentBrochureDTO createHrRecruitmentBrochure(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO);

    /**
     * 修改招聘简章
     *
     * @param hrRecruitmentBrochureDTO
     * @return
     */
    Optional<HrRecruitmentBrochureDTO> updateHrRecruitmentBrochure(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO);

    /**
     * 查询招聘简章详情
     *
     * @param id
     * @return
     */
    HrRecruitmentBrochureDTO getHrRecruitmentBrochure(String id);

    /**
     * 批量删除招聘简章
     *
     * @param ids
     */
    void deleteHrRecruitmentBrochure(List<String> ids);

    /**
     * 分页查询招聘简章
     *
     * @param hrRecruitmentBrochureDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO, Long pageNumber, Long pageSize);
    /**
     * 小程序查询简章
     *
     * @param
     * @param
     * @param
     * @return
     */
    IPage<HrRecruitmentBrochureDTO> findPageSelectWX(  Long pageNumber, Long pageSize);

    HrRecruitmentBrochureDTO getHrrecruitmenSelect(String id,String staffId);

    HrTemplateDTO getHrrecruitmenTemplateselect(String id);

    /**
     * 客户审核招聘简章
     * @param batchOptDTO
     * @return
     */
    ResponseEntity<?> examineApprove(BatchOptDTO batchOptDTO);

    /**
     * 提交招聘简章
     * @param id 招聘简章ID
     */
    void brochureSubmit(String id);

    /**
     * 导出招聘简章
     * @param hrRecruitmentBrochureDTO
     * @param response
     * @return
     */
    String brochureExport(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO, HttpServletResponse response);

    /**
     * 导出公告概览
     * @param hrRecruitmentBulletinDTO
     * @param response
     */
    String bulletinsExport(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO, HttpServletResponse response);

    IPage<HrRecruitmentBrochureDTO> findPageSelectWXSignUp(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO, Long pageNumber, Long pageSize);

    /**
     * 查询是否存在未入职员工
     * @param id 招聘简章Id
     * @return
     */
    Boolean queryHrRecruitmentBrochure(String id);

    /**
     * 修改招聘状态
     * @param batchOptDTO
     * @return
     */
    void updateRecruitmentEnd(BatchOptDTO batchOptDTO);

    /**
     * 官网查询招聘信息
     * @param hrRecruitmentBrochureDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrRecruitmentBrochureDTO> findPageSelectOW(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO, Long pageNumber, Long pageSize);

    /**
     * 官网招聘信息详情
     * @param recruitmentId
     * @return
     */
    HrRecruitmentBrochureDTO findRecruitmentInfo(String recruitmentId);

}
