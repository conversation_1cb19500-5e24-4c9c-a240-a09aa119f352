package cn.casair.repository;

import cn.casair.domain.HrContract;
import cn.casair.dto.HrContractDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 合同列表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-09
 */
@Repository
public interface HrContractRepository extends BaseMapper<HrContract> {


    /**
     * 获取员工未生效合同列表
     * <p>因系统异常情况导致员工有多条相同合同 需要查询 删除旧数据</p>
     *
     * @param staffId
     * @param clientId
     * @return
     */
    LinkedList<HrContract> getNotEffectiveContractList(@Param("staffId") String staffId, @Param("clientId") String clientId);

    /**
     * 批量插入合同
     *
     * @param hrContractList
     * @return int
     * <AUTHOR>
     * @date 2022/1/25
     **/
    int batchInsert(@Param("list") List<HrContract> hrContractList);

    /**
     * 获取最新的有效合同（即将到期/已到期）
     *
     * @param clientId
     * @param staffId
     * @return cn.casair.domain.HrContract
     * <AUTHOR>
     * @date 2021/11/25
     **/
    HrContract getNewestStaffContract(@Param("clientId") String clientId, @Param("staffId") String staffId);

    /**
     * 获取员工合同列表（无状态）
     *
     * @param staffId
     * @param clientId
     * @return cn.casair.domain.HrContract
     * <AUTHOR>
     * @date 2021/10/21
     **/
    HrContract getContractByStaffIdAndClientId(@Param("staffId") String staffId, @Param("clientId") String clientId);

    /**
     * 获取员工未生效合同信息
     *
     * @param staffId  员工id
     * @param clientId 客户id
     * @return cn.casair.domain.HrContract
     * <AUTHOR>
     * @date 2021/9/13
     **/
    HrContract getNotActiveContract(@Param("staffId") String staffId, @Param("clientId") String clientId);

    /**
     * 根据员工id与客户id获取最新的有效合同
     *
     * @param staffId  员工id
     * @param clientId 客户id
     * @return cn.casair.domain.HrContract
     * <AUTHOR>
     * @date 2021/9/13
     **/
    HrContract selectNewestRecord(@Param("staffId") String staffId, @Param("clientId") String clientId);

    /**
     * 获取最近10个历史合同
     *
     * @param params 客户id
     * @param clientIds
     * @return java.util.List<cn.casair.dto.HrContractDTO>
     * <AUTHOR>
     * @date 2021/9/10
     */
    List<HrContractDTO> historyContract(@Param("params") Map<String, Object> params, @Param("permissionClient") List<String> clientIds);

    /**
     * 获取进需要处理的员工合同列表
     *
     * @return java.util.List<cn.casair.domain.HrContract>
     * <AUTHOR>
     * @date 2021/9/9
     **/
    List<HrContractDTO> getSecheduleTaskContractList();

    /**
     * 获取合同列表
     *
     * @param hrContractDTO
     * @param clientIds
     * @return java.util.List<cn.casair.dto.HrContractDTO>
     * <AUTHOR>
     * @date 2021/9/9
     **/
    List<HrContractDTO> findList(@Param("params") HrContractDTO hrContractDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 合同列表
     *
     * @param page          分页
     * @param hrContractDTO 参数
     * @param clientIds
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrContractDTO>
     * <AUTHOR>
     * @date 2021/9/9
     **/
    IPage<HrContractDTO> findPage(Page<HrContract> page, @Param("params") HrContractDTO hrContractDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 获取员工合同列表
     *
     * @param staffId
     * @return java.util.List<cn.casair.domain.HrContract>
     * <AUTHOR>
     * @date 2021/9/28
     **/
    List<HrContract> getContractlistByStaffId(@Param("staffId") String staffId);

    /**
     * 更新合同签署日期
     *
     * @param contractId
     * @return int
     * <AUTHOR>
     * @date 2021/10/8
     **/
    int updateContractSignDate(@Param("contractId") String contractId);

    List<HrContract> selectStatus(@Param("clientIdList")List<String> clientIdList, @Param("statusList")List<String> statusList);

    /**
     * 小程序查看我的合同
     * @param queryWrapper
     * @return
     */
    List<HrContractDTO> selectContract(@Param(Constants.WRAPPER) QueryWrapper<HrContract> queryWrapper);

    /**
     * 绑定合同查看服务
     * @param contractViewId 合同查看服务ID
     * @param contractId 合同ID
     */
    void updateContractViewId(@Param("contractViewId") String contractViewId, @Param("contractId") String contractId);

    /**
     * 解绑合同查看服务
     * @param contractViewIds 合同查看服务ID
     */
    void updateContractViewNull(@Param("contractViewIds") List<String> contractViewIds);

    /**
     * 获取员工最新的有效的合同
     * @param staffId
     * @return
     */
    HrContractDTO selectNewestStaffContract(@Param("staffId") String staffId);

    /**
     * 根据员工查询每个员工最新的有效合同
     * @param staffIds
     * @return
     */
    List<HrContract> selectLatestContractInfo(@Param("staffIds") List<String> staffIds, @Param("order") String order);

    /**
     * 获取员工最新的续签合同信息
     * @param staffId
     * @return
     */
    HrContractDTO getNewRenewalContract(@Param("staffId") String staffId);

    /**
     * 客服部长手动修改员工入职流程
     * <p>其他方法禁止使用此接口</p>
     *
     * @param hrContract
     * @return
     */
    int manualUpdateStaffContract(@Param("params") HrContract hrContract);

    /**
     * 获取员工最新合同id
     * <p>手动处理方法 其他方法禁止使用此接口</p>
     *
     * @param idNo
     * @return
     */
    @Deprecated
    HrContractDTO getStaffNewestContract(@Param("idNo") String idNo);

    /**
     * 更新员工合同
     * <p>手动处理方法 其他方法禁止使用此接口</p>
     *
     * @param id
     * @param contractEndDate
     * @param state
     */
    @Deprecated
    void updateStaffNewestContract(@Param("id") String id, @Param("contractEndDate") LocalDate contractEndDate, @Param("state") Integer state);
}
