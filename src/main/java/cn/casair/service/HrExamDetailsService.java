package cn.casair.service;
import cn.casair.domain.HrExamDetails;
import cn.casair.dto.HrExamDetailsDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 考试详情服务类
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
public interface HrExamDetailsService extends IService<HrExamDetails> {


    /**
     * 创建考试详情
     * @param hrExamDetailsDTO
     * @return
     */
    HrExamDetailsDTO createHrExamDetails(HrExamDetailsDTO hrExamDetailsDTO);

    /**
     * 修改考试详情
     * @param hrExamDetailsDTO
     * @return
     */
    Optional<HrExamDetailsDTO> updateHrExamDetails(HrExamDetailsDTO hrExamDetailsDTO);

    /**
     * 查询考试详情详情
     * @param id
     * @return
     */
    HrExamDetailsDTO getHrExamDetails(String id);

    /**
     * 删除考试详情
     * @param id
     */
    void deleteHrExamDetails(String id);

    /**
     * 批量删除考试详情
     * @param ids
     */
    void deleteHrExamDetails(List<String> ids);

    /**
     * 分页查询考试详情
     * @param hrExamDetailsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrExamDetailsDTO hrExamDetailsDTO,Long pageNumber,Long pageSize);
    }
