package cn.casair.service.impl;

import cn.casair.cache.RedisCache;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.common.utils.excel.CellItem;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.billl.BillExcelDataDTO;
import cn.casair.dto.billl.BillFieldInfoDTO;
import cn.casair.mapper.HrBillDetailItemsMapper;
import cn.casair.mapper.HrBillDetailMapper;
import cn.casair.mapper.HrBillMapper;
import cn.casair.repository.*;
import cn.casair.service.HrBillDetailItemsService;
import cn.casair.service.HrBillService;
import cn.casair.service.HrSocialSecurityService;
import cn.casair.service.component.asynchronous.HrSalaryBillComponent;
import cn.casair.service.util.SecurityUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 薪酬账单动态费用详情服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class HrBillDetailItemsServiceImpl extends ServiceImpl<HrBillDetailItemsRepository, HrBillDetailItems> implements HrBillDetailItemsService {

    private final RedisCache redisCache;
    private final CodeTableRepository codeTableRepository;
    private final HrQuickDeductionRepository hrQuickDeductionRepository;
    private final HrSpecialDeductionRepository hrSpecialDeductionRepository;
    private final HrBillDetailItemsRepository hrBillDetailItemsRepository;
    private final HrBillDetailItemsMapper hrBillDetailItemsMapper;
    private final HrBillDetailRepository hrBillDetailRepository;
    private final HrBillDynamicFieldsRepository hrBillDynamicFieldsRepository;
    private final HrBillRepository hrBillRepository;
    private final HrBillMapper hrBillMapper;
    private final HrBillService hrBillService;
    private final HrBillDetailMapper hrBillDetailMapper;
    private final HrBillTotalRepository hrBillTotalRepository;
    private final HrClientRepository hrClientRepository;
    private final HrSocialSecurityService hrSocialSecurityService;
    private final HrSalaryBillComponent hrSalaryBillComponent;
    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final HrBillCompareResultRepository hrBillCompareResultRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;



    @Override
    public List<HrBillDetailDTO> calculatePersonalTax(List<HrBillDetailDTO> hrBillDetailList) {
        if (hrBillDetailList == null || hrBillDetailList.isEmpty()){
            throw new CommonException("重新计算员工个税参数缺失！");
        }
        HrBillDetailDTO billDetailDTO = hrBillDetailList.get(0);
        Integer payYear = billDetailDTO.getPayYear();
        Integer payMonthly = billDetailDTO.getPayMonthly();
        Integer personalTaxStart = this.codeTableRepository.getValueByInnerName("personalTaxStart");
        List<Map<String, BigDecimal>> incomeMap = this.hrSpecialDeductionRepository.getSumCurrentIncomeGroupClient(LocalDate.of(payYear, payMonthly, 1));
        // 若为1月 不再获取前两个月的数据
        // 若为2月，只获取第一个月的数据
        // 若大于2月，获取前两个月数据
        int lastMonthly = payMonthly;
        if (payMonthly == 2) {
            // 获取上月月份
            lastMonthly = payMonthly - 1;
        } else if (payMonthly > 2) {
            // 获取上月月份
            lastMonthly = payMonthly - 1;
            // 获取上上月月份
            // beforeLastMonthly = payMonthly - 2;
        }
        List<HrSpecialDeduction> specialDeductionList = this.hrSpecialDeductionRepository.getAllByStartDate(LocalDate.of(payYear, payMonthly, 1),LocalDate.of(payYear, lastMonthly, 1));
        List<HrQuickDeduction> hrQuickDeductions = this.hrQuickDeductionRepository.selectAll();
        hrBillDetailList.forEach(detailDTO -> {
            AtomicReference<BigDecimal> sumCurrentIncome = new AtomicReference<>(BigDecimal.ZERO);
            incomeMap.forEach(map ->{
                sumCurrentIncome.set(map.get(billDetailDTO.getClientId()));
            });
            calculatePersonalIncomeTax(detailDTO, personalTaxStart, sumCurrentIncome.get(), specialDeductionList, hrQuickDeductions);
        });
        return hrBillDetailList;
    }

    /**
     * 重新计算员工个税--全年一次性奖金计税
     * @param hrBillDetailList
     * @return
     */
    @Override
    public List<HrBillDetailDTO> recalculateBonusTax(List<HrBillDetailDTO> hrBillDetailList) {
        hrBillDetailList.forEach(ls->{
            List<HrBillDetailItemsDTO> hrBillDetailItemsList = ls.getHrBillDetailItemsList();
            if (CollectionUtils.isNotEmpty(hrBillDetailItemsList)){
                BigDecimal reduce = hrBillDetailItemsList.stream().filter(tmp->tmp.getExpenseType().equals(DynamicFeeTypesEnum.TAX_EXEMPT_INCOME.getKey())).map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                ls.setTotal(CalculateUtils.decimalSubtraction(ls.getTotal(),reduce));
            }
            this.annualLumpSumBonusTax(ls);
        });
        return hrBillDetailList;
    }

    /**
     * 修改薪酬账单总金额
     * 0.并入综合所得计税
     *      a.总金额=税前应发
     * 1.全年一次性奖金计税
     *      a.总金额=工资增项-|工资减项|
     * @param hrBillDTO
     * @return
     */
    @Override
    public List<HrBillDetailDTO> modifySalaryBill(HrBillDTO hrBillDTO) {
        List<HrBillDetailDTO> billDetailList = hrBillDTO.getBillDetailList();
        HrBillDetailDTO billDetailDTO = billDetailList.get(0);
        Integer payYear = billDetailDTO.getPayYear();
        Integer payMonthly = billDetailDTO.getPayMonthly();
        Integer personalTaxStart = this.codeTableRepository.getValueByInnerName("personalTaxStart");
        List<Map<String, BigDecimal>> incomeMap = this.hrSpecialDeductionRepository.getSumCurrentIncomeGroupClient(LocalDate.of(payYear, payMonthly, 1));
        // 若为1月 不再获取前两个月的数据
        // 若为2月，只获取第一个月的数据
        // 若大于2月，获取前两个月数据
        int lastMonthly = payMonthly;
        if (payMonthly == 2) {
            // 获取上月月份
            lastMonthly = payMonthly - 1;
        } else if (payMonthly > 2) {
            // 获取上月月份
            lastMonthly = payMonthly - 1;
            // 获取上上月月份
            // beforeLastMonthly = payMonthly - 2;
        }
        List<HrSpecialDeduction> specialDeductionList = this.hrSpecialDeductionRepository.getAllByStartDate(LocalDate.of(payYear, payMonthly, 1),LocalDate.of(payYear, lastMonthly, 1));
        List<HrQuickDeduction> hrQuickDeductions = this.hrQuickDeductionRepository.selectAll();
        for (HrBillDetailDTO hrBillDetailDTO : billDetailList) {
            if (hrBillDTO.getTaxCalculationMethod() == 0){
                //重新计税
                AtomicReference<BigDecimal> sumCurrentIncome = new AtomicReference<>(BigDecimal.ZERO);
                incomeMap.forEach(map ->{
                    sumCurrentIncome.set(map.get(billDetailDTO.getClientId()));
                });
                this.calculatePersonalIncomeTax(hrBillDetailDTO, personalTaxStart, sumCurrentIncome.get(), specialDeductionList, hrQuickDeductions);
                hrBillDetailDTO.setTotal(hrBillDetailDTO.getPreTaxSalary());
                //实发工资 = 税前应发-(个税 + 个税补差）
                BigDecimal bigDecimal = CalculateUtils.decimalAddition(hrBillDetailDTO.getPersonalTax(), hrBillDetailDTO.getPersonalTaxMakeUp());
                hrBillDetailDTO.setRealSalary(hrBillDetailDTO.getPreTaxSalary().subtract(bigDecimal));
            }else {
                // 用于前端展示
                BigDecimal total = BigDecimal.ZERO;
                // 用于计算个税
                BigDecimal totalBackups = BigDecimal.ZERO;
                List<HrBillDetailItemsDTO> hrBillDetailItemsList = hrBillDetailDTO.getHrBillDetailItemsList();
                for (HrBillDetailItemsDTO hrBillDetailItemsDTO : hrBillDetailItemsList) {
                    BigDecimal amount = hrBillDetailItemsDTO.getAmount() == null? BigDecimal.ZERO: hrBillDetailItemsDTO.getAmount();
                    DynamicFeeTypesEnum typesEnum = EnumUtils.getEnumByKey(DynamicFeeTypesEnum.class, hrBillDetailItemsDTO.getExpenseType());
                    switch (typesEnum) {
                        case FEE_TYPE_ADD:
                            // 差旅补助不计个税
                            if ("差旅补助".equals(hrBillDetailItemsDTO.getExpenseName())) {
                                total = total.add(amount);
                            } else {
                                total = total.add(amount);
                                totalBackups = totalBackups.add(amount);
                            }
                            break;
                        case FEE_TYPE_REDUCE:
                            total = total.subtract(amount.abs());
                            totalBackups = totalBackups.subtract(amount.abs());
                            break;
                        case TAX_EXEMPT_INCOME:
                            total = total.add(amount);
                            break;
                        default:
                            break;
                    }
                }
                hrBillDetailDTO.setTotal(totalBackups);
                //个税 = (总金额 - 免税收入) * 税率 - 速算扣除数
                this.annualLumpSumBonusTax(hrBillDetailDTO);
                //实发工资 = 总金额 - (个税 + 补差）
                BigDecimal bigDecimal = CalculateUtils.decimalAddition(hrBillDetailDTO.getPersonalTax(), hrBillDetailDTO.getPersonalTaxMakeUp());
                hrBillDetailDTO.setRealSalary(total.subtract(bigDecimal));
                hrBillDetailDTO.setTotal(total);
            }
        }
        return billDetailList;
    }

    /**
     * 创建薪酬账单动态费用详情
     *
     * @param hrBillDetailItemsDTO
     * @return
     */
    @Override
    public HrBillDetailItemsDTO createHrBillDetailItems(HrBillDetailItemsDTO hrBillDetailItemsDTO) {
        log.info("Create new HrBillDetailItems:{}", hrBillDetailItemsDTO);

        HrBillDetailItems hrBillDetailItems = this.hrBillDetailItemsMapper.toEntity(hrBillDetailItemsDTO);
        this.hrBillDetailItemsRepository.insert(hrBillDetailItems);
        return this.hrBillDetailItemsMapper.toDto(hrBillDetailItems);
    }

    /**
     * 修改薪酬账单动态费用详情
     *
     * @param hrBillDetailItemsDTO
     * @return
     */
    @Override
    public Optional<HrBillDetailItemsDTO> updateHrBillDetailItems(HrBillDetailItemsDTO hrBillDetailItemsDTO) {
        return Optional.ofNullable(this.hrBillDetailItemsRepository.selectById(hrBillDetailItemsDTO.getId()))
            .map(roleTemp -> {
                HrBillDetailItems hrBillDetailItems = this.hrBillDetailItemsMapper.toEntity(hrBillDetailItemsDTO);
                this.hrBillDetailItemsRepository.updateById(hrBillDetailItems);
                log.info("Update HrBillDetailItems:{}", hrBillDetailItemsDTO);
                return hrBillDetailItemsDTO;
            });
    }

    /**
     * 查询薪酬账单动态费用详情详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillDetailItemsDTO getHrBillDetailItems(String id) {
        log.info("Get HrBillDetailItems :{}", id);

        HrBillDetailItems hrBillDetailItems = this.hrBillDetailItemsRepository.selectById(id);
        return this.hrBillDetailItemsMapper.toDto(hrBillDetailItems);
    }

    /**
     * 删除薪酬账单动态费用详情
     *
     * @param id
     */
    @Override
    public void deleteHrBillDetailItems(String id) {
        Optional.ofNullable(this.hrBillDetailItemsRepository.selectById(id))
            .ifPresent(hrBillDetailItems -> {
                this.hrBillDetailItemsRepository.deleteById(id);
                log.info("Delete HrBillDetailItems:{}", hrBillDetailItems);
            });
    }

    /**
     * 批量删除薪酬账单动态费用详情
     *
     * @param ids
     */
    @Override
    public void deleteHrBillDetailItems(List<String> ids) {
        log.info("Delete HrBillDetailItemss:{}", ids);
        this.hrBillDetailItemsRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询薪酬账单动态费用详情
     *
     * @param hrBillDetailItemsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillDetailItemsDTO hrBillDetailItemsDTO, Long pageNumber, Long pageSize) {
        Page<HrBillDetailItems> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrBillDetailItems> qw = new QueryWrapper<>(this.hrBillDetailItemsMapper.toEntity(hrBillDetailItemsDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrBillDetailItemsRepository.selectPage(page, qw);
        iPage.setRecords(this.hrBillDetailItemsMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    /**
     * 创建薪酬账单明细
     * <p>政法委薪酬账单</p>
     *
     *
     * @param params 账单id
     */
    @Override
    public void createBillDetails(Map<String, Object> params) {
        log.info("创建薪酬账单明细开始");
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> billIdList;
        String billId = String.valueOf(params.get("billId"));
        if (params.get("billIdList") != null) {
            billIdList = (List<String>) params.get("billIdList");
        } else {
            billIdList = Collections.singletonList(billId);
        }
        // 额外入账人员
        List<String> staffIds = (List<String>) params.get("staffIds");

        if (CollectionUtils.isNotEmpty(billIdList)) {
            hrBillDetailItemsRepository.deleteByBillIdBatch(billIdList);
            hrBillDetailRepository.deleteByBillIdBatch(billIdList);
            hrBillTotalRepository.delete(new QueryWrapper<HrBillTotal>().in("bill_id", billIdList));
        }

        // 获取账单基础信息
        List<HrBill> hrBills = hrBillRepository.selectByIdOrBillNo(billId);
        if (hrBills == null || hrBills.isEmpty()){
            throw new CommonException("未查询到账单信息！");
        }
        HrBill hrBill = hrBills.get(0);
        billId = hrBill.getId();
        HrBillDTO hrBillDTO = this.hrBillRepository.getBillInfoById(billId);
        List<HrBill> hrBillList = hrBillRepository.selectBatchIds(billIdList);
        List<String> clientIdList = hrBillList.stream().map(HrBill::getClientId).distinct().collect(Collectors.toList());
        /*if (params.get("clientIdList") != null) {
            clientIdList = (List<String>) params.get("clientIdList");
        } else {
            clientIdList = Collections.singletonList(hrBillDTO.getClientId());
        }*/

        // 判断特殊客户
        SpecialBillClient specialBillClient = this.judgeSpecialBillClient(clientIdList);

        // 获取账单配置
        HrBillDynamicFieldsDTO dynamicFieldsDTO = this.hrBillDynamicFieldsRepository.getByBillId(billId);
        if (StringUtils.isEmpty(dynamicFieldsDTO.getOriginBillUrl())) {
            throw new CommonException("未查询到薪酬原单,请重新上传!");
        }

        // 解析工作簿的数据,得到map结构的数据
        BillFieldInfoDTO billFieldInfoDTO = dynamicFieldsDTO.getBillFieldInfoDTO();
        Integer dataStartRows = dynamicFieldsDTO.getDataStartRows();
        // flag 标识 0薪酬账单 1中石化账单 2其他账单-客户 3其他账单-员工
        int flag = 0;
        if (hrBillDTO.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            flag = 1;
            dataStartRows = dataStartRows + 1;
        } else if (hrBillDTO.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey())) {
            List<CellItem> cellItems = billFieldInfoDTO.getCellItems();
            if (CollectionUtils.isEmpty(cellItems)) {
                throw new CommonException("未解析到表单,操作失败！");
            }
            List<String> valueList = cellItems.stream().map(CellItem::getValue).collect(Collectors.toList());
            if (valueList.contains(DynamicFeeTypesEnum.CUSTOMER_NAME.getValue())) {
                flag = 2;
            } else {
                flag = 3;
            }
        }

        if (specialBillClient != null) {
            Map<String, List<CellItem>> collect = billFieldInfoDTO.getCellItems().stream().collect(Collectors.groupingBy(CellItem::getValue));
            if (collect.get(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue()) != null) {
                billFieldInfoDTO.getMappingFields().add(new HrExpenseManageDTO()
                    .setExpenseTypeName(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue())
                    .setExpenseName(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue())
                    .setExpenseType(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getKey())
                    .setColIndex(collect.get(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue()).get(0).getFirstCol())
                );
            }
        }

        Map<String, BillExcelDataDTO> excelDataMap = BillParseUtils.parseExcel2MapData(billFieldInfoDTO, dataStartRows, dynamicFieldsDTO.getOriginBillUrl(), dynamicFieldsDTO.getOriginBillUrl(), flag);
        if (excelDataMap.size() == 0) {
            throw new CommonException("薪酬原单中未解析到数据,操作失败！");
        }

        // 获取一个值为0的动态费用列表,作为数据填充使用
        List<HrBillDetailItemsDTO> initDynamicFee = this.getInitDynamicFee(excelDataMap);

        // 获取公司员工信息,携带社保公积金等基础信息
        List<HrBillDetailDTO> hrBillDetailList = hrBillDetailRepository.getStaffParamsByClientId(clientIdList);
        if (hrBillDetailList.isEmpty() && (flag == 0 || flag == 3)) {
            throw new CommonException("该客户以及所属的子客户暂无员工信息,解析失败！");
        }

        QueryWrapper<HrBill> qw = new QueryWrapper<>();
        qw.in("id", billIdList);
        qw.in("client_id", clientIdList);
        List<HrBill> billList = hrBillRepository.selectList(qw);
        List<HrBillDTO> hrBillDTOList = hrBillMapper.toDto(billList);

        hrBillDTO.setMonthlyServiceFee(BigDecimal.ZERO);
        hrBillDTO.setHalfYearlyServiceFee(BigDecimal.ZERO);
        List<HrBillDetail> hrBillDetails = new ArrayList<>();
        List<HrBillDetailItems> hrBillDetailItems = new ArrayList<>();
        if (hrBillDTO.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey())) {
            hrBillRepository.updateOtherBillFlag(flag == 2 ? 1 : 0, billIdList);
        }

        // 中石化账单
        if (flag == 1) {
            List<HrBillSinopecContractDTO> projectContractNoList = hrBillDetailRepository.getProjectContractNo();
            HrExpenseManageDTO hrExpenseManageDTO = billFieldInfoDTO.getMappingFields().stream().filter(lst -> lst.getExpenseType().equals(DynamicFeeTypesEnum.CONTRACT_NO.getKey())).findFirst().orElse(null);
            if (hrExpenseManageDTO != null){
                //修改中石化账单合同编号标识
                hrBillRepository.updateSinopecContractNo(billId, 1);
            }
            for (String idCard : excelDataMap.keySet()) {
                BillExcelDataDTO excelDataDTO = excelDataMap.get(idCard);
                if (excelDataDTO != null) {
                    HrBillDetail hrBillDetail = new HrBillDetail();
                    hrBillDetail.setIsUsed(true).setBillUsed(true)
                        .setBillId(billId)
                        .setType(hrBillDTO.getBillType())
                        .setPayYear(hrBillDTO.getPayYear())
                        .setPayMonthly(hrBillDTO.getPayMonthly())
                        .setCurrentYear(excelDataDTO.getCurrentYear())
                        .setProjectCode(excelDataDTO.getProjectCode())
                        .setProjectName(idCard)
                        .setPeopleNum(excelDataDTO.getPeopleNum())
                        .setSortValue(excelDataDTO.getSortValue());
                    String projectType = excelDataDTO.getProjectType();
                    if (StringUtils.isNotEmpty(projectType) && projectType.contains("国外")){
                        hrBillDetail.setProjectType(2);
                    }else {
                        hrBillDetail.setProjectType(1);
                    }
                    String contractNo = excelDataDTO.getContractNo();
                    if (StringUtils.isNotEmpty(contractNo)){
                        if (projectContractNoList != null && !projectContractNoList.isEmpty()){
                            HrBillSinopecContractDTO hrBillSinopecContractDTO = projectContractNoList.stream().filter(lst -> lst.getProjectName().equals(idCard)).findAny().orElse(null);
                            if (hrBillSinopecContractDTO != null){
                                hrBillSinopecContractDTO.setContractNo(contractNo)
                                    .setLastModifiedBy(jwtUserDTO.getUserName());
                                hrBillDetailRepository.updateSinopecContractNo(contractNo, idCard, jwtUserDTO.getUserName());
                            }else {
                                hrBillDetailRepository.insertSinopecContractNo(RandomUtil.generateId(), contractNo, idCard, jwtUserDTO.getUserName());
                            }
                        }else {
                            hrBillDetailRepository.insertSinopecContractNo(RandomUtil.generateId(), contractNo, idCard, jwtUserDTO.getUserName());
                        }
                    }
                    List<HrBillDetailItemsDTO> dynamicFeeItems = excelDataDTO.getDynamicFeeItems();
                    List<HrBillDetailItemsDTO> collect = dynamicFeeItems.stream().filter(lst -> lst.getExpenseType().equals(DynamicFeeTypesEnum.TOTAL_EXPENSES.getKey())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        hrBillDetail.setTotal(collect.get(0).getAmount());
                    }
                    this.hrBillDetailRepository.insert(hrBillDetail);
                    hrBillDetails.add(hrBillDetail);
                    this.saveDynamicFeeDetail(excelDataDTO, initDynamicFee, hrBillDetail.getId());
                }
            }
        }
        // 其他账单-客户
        else if (flag == 2) {
            for (String idCard : excelDataMap.keySet()) {
                BillExcelDataDTO excelDataDTO = excelDataMap.get(idCard);
                if (excelDataDTO != null) {
                    HrBillDetailDTO hrBillDetailDTO = new HrBillDetailDTO();
                    hrBillDetailDTO.setIsUsed(true).setBillUsed(true)
                        .setBillId(billId)
                        .setType(hrBillDTO.getBillType())
                        .setPayYear(hrBillDTO.getPayYear())
                        .setPayMonthly(hrBillDTO.getPayMonthly())
                        .setName(idCard)
                        .setSortValue(excelDataDTO.getSortValue());
                    List<HrBillDetailItemsDTO> dynamicFeeItems = excelDataDTO.getDynamicFeeItems();
                    List<HrBillDetailItems> hrBillDetailItems1 = hrBillService.fillOtherBillParam(hrBillDetailDTO, dynamicFeeItems, false);
                    hrBillDetailItems.addAll(hrBillDetailItems1);
                    HrBillDetail hrBillDetail = hrBillDetailMapper.toEntity(hrBillDetailDTO);
                    this.hrBillDetailRepository.insert(hrBillDetail);
                    hrBillDetails.add(hrBillDetail);
                    this.saveDynamicFeeDetail(excelDataDTO, initDynamicFee, hrBillDetail.getId());
                }
            }
        }
        else {
            // 判断客户或其次顶级客户是否属于特殊账单客户
            int lastPayYear;
            int lastPayMonthly;
            Map<String, BillExcelDataDTO> specialBillExcelDataMap = new HashMap<>();
            // 上个缴费年月
            if (hrBillDTO.getPayMonthly() > 1) {
                lastPayYear = hrBillDTO.getPayYear();
                lastPayMonthly = hrBillDTO.getPayMonthly() - 1;
            } else {
                lastPayYear = hrBillDTO.getPayYear() - 1;
                lastPayMonthly = 12;
            }
            // 处理特殊处理客户 薪酬账单 组装表头
            this.dealSpecialBillClient(specialBillClient, clientIdList, specialBillExcelDataMap, lastPayYear, lastPayMonthly, dynamicFieldsDTO);
            List<HrSocialSecurityDTO> securityDTOList = hrSocialSecurityService.getClientSocialSecurity(clientIdList);

            List<HrBillDetailItemsDTO> insertHrBillDetailItems = new ArrayList<>();
            Integer personalTaxStart = this.codeTableRepository.getValueByInnerName("personalTaxStart");
            List<Map<String, BigDecimal>> incomeMap = this.hrSpecialDeductionRepository.getSumCurrentIncomeGroupClient(LocalDate.of(hrBillDTO.getPayYear(), hrBillDTO.getPayMonthly(), 1));
            // 若为1月 不再获取前两个月的数据
            // 若为2月，只获取第一个月的数据
            // 若大于2月，获取前两个月数据
            int lastMonthly = hrBillDTO.getPayMonthly();
            if (hrBillDTO.getPayMonthly() == 2) {
                // 获取上月月份
                lastMonthly = hrBillDTO.getPayMonthly() - 1;
            } else if (hrBillDTO.getPayMonthly() > 2) {
                // 获取上月月份
                lastMonthly = hrBillDTO.getPayMonthly() - 1;
                // 获取上上月月份
                // beforeLastMonthly = payMonthly - 2;
            }
            List<HrSpecialDeduction> specialDeductionList = this.hrSpecialDeductionRepository.getAllByStartDate(LocalDate.of(hrBillDTO.getPayYear(), hrBillDTO.getPayMonthly(), 1),LocalDate.of(hrBillDTO.getPayYear(), lastMonthly, 1));
            List<HrQuickDeduction> hrQuickDeductions = this.hrQuickDeductionRepository.selectAll();
            //保障账单
            List<HrBillDetailDTO> billDetailByBill = hrBillDetailRepository.getBillDetailByBill(
                new HrBillDetailDTO().setPayYear(hrBillDTO.getPayYear()).setPayMonthly(hrBillDTO.getPayMonthly()).setBillType(BillEnum.BillType.SECURITY_BILL.getKey()).setBillPurpose(1)
            );
            //薪酬账单
            List<HrBillDetailDTO> hrBillDetailDTOS = hrBillDetailRepository.selectSalaryBill(new HrBillDetailDTO().setPayYear(hrBillDTO.getPayYear()).setPayMonthly(hrBillDTO.getPayMonthly()));
            //上个月薪酬账单
            List<HrBillDetailDTO> lastBillDetailDTOList = hrBillDetailRepository.getSalaryBillBatchClientId(lastPayYear, lastPayMonthly, clientIdList,null);
            //获取未使用的补差
            List<HrWelfareCompensation>  welfareCompensations = hrWelfareCompensationRepository.getUnUsedWelfareCompensation(hrBillDTO.getBillType(), hrBillDTO.getPaymentDate());
            List<Integer> typeList = Arrays.asList(BillEnum.GuidePlateType.SOCIAL_SECURITY_GUIDE.getKey(),
                BillEnum.GuidePlateType.MEDICAL_GUIDE.getKey(),
                BillEnum.GuidePlateType.ACCUMULATION_FUND_GUIDE.getKey(),
                BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey());
            List<HrBillCompareResultDTO> hrBillCompareResultDTOList = hrBillCompareResultRepository.getBillCompareResult(hrBillDTO.getPayYear(), hrBillDTO.getPayMonthly(), lastPayYear, lastPayMonthly, typeList);
            for (HrBillDetailDTO billDetailDTO : hrBillDetailList) {
                AtomicReference<BigDecimal> sumCurrentIncome = new AtomicReference<>(BigDecimal.ZERO);
                incomeMap.forEach(map ->{
                     sumCurrentIncome.set(map.get(billDetailDTO.getClientId()));
                });
                hrSalaryBillComponent.detailDataHandle(hrBillDTO, billDetailDTO, hrBillDTOList, securityDTOList, excelDataMap, specialBillClient,
                    lastPayYear, lastPayMonthly, initDynamicFee, staffIds, specialBillExcelDataMap, hrBillDetailItems, hrBillDetails, insertHrBillDetailItems, jwtUserDTO,
                    personalTaxStart, sumCurrentIncome.get(), specialDeductionList, hrQuickDeductions, billDetailByBill, hrBillDetailDTOS, welfareCompensations, lastBillDetailDTOList,hrBillCompareResultDTOList);
                // futureList.add(result);
            }
            // 等待异步线程执行完毕
           /* for (Future<String> future : futureList) {
                try {
                    future.get(30, TimeUnit.MINUTES);
                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                    log.error("薪酬账单异步线程获取异常:{}", e.getMessage());
                    e.printStackTrace();
                }
            }*/
            if (hrBillDetails.isEmpty() || insertHrBillDetailItems.isEmpty()){
                throw new CommonException("薪酬原单异常！");
            }
            hrBillDetailRepository.batchSave(hrBillDetails);
            insertHrBillDetailItems.forEach(ls ->{
                if (ls.getId() == null){
                    ls.setId(RandomUtil.generateId());
                }
            });
            saveBatch(hrBillDetailItemsMapper.toEntity(insertHrBillDetailItems));
            //查询所有账单明细的费用合计
            // excel中多余的员工存储到明细中，设置为不可用
            this.saveUnUseableStaff(excelDataMap, hrBillDTO);
        }

        // 创建汇总账单
        if (CollectionUtils.isNotEmpty(hrBillDTOList)) {
            for (HrBillDTO dto : hrBillDTOList) {
                List<HrBillDetail> collect = hrBillDetails.stream().filter(ls -> ls.getBillId().equals(dto.getId())).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    BigDecimal billTotal = collect.stream().map(i -> i.getTotal() == null ? BigDecimal.ZERO : i.getTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    this.createHrBillTotal(dto, hrBillDTO.getHalfYearlyServiceFee(), billTotal);
                }
            }
        } else {
            List<HrBillDetail> collect = hrBillDetails.stream().filter(ls -> ls.getBillId().equals(hrBillDTO.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                BigDecimal billTotal = collect.stream().map(i -> i.getTotal() == null ? BigDecimal.ZERO : i.getTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
                this.createHrBillTotal(hrBillDTO, hrBillDTO.getHalfYearlyServiceFee(), billTotal);
            }
        }
    }

    /**
     * 处理特殊处理客户薪酬账单组装表头
     *
     * @param specialBillClient
     * @param clientIdList
     * @param specialBillExcelDataMap
     * @param lastPayYear
     * @param lastPayMonthly
     * @param thisHrBillDynamicFieldsDTO
     */
    @Override
    public void dealSpecialBillClient(SpecialBillClient specialBillClient, List<String> clientIdList, Map<String, BillExcelDataDTO> specialBillExcelDataMap, int lastPayYear, int lastPayMonthly, HrBillDynamicFieldsDTO thisHrBillDynamicFieldsDTO) {
        if (specialBillClient != null) {
            switch (specialBillClient) {
                case SOCIAL_GOVERNANCE:
                case EAST_DISTRICT_PUBLIC_SECURITY:
                    for (String clientId : clientIdList) {
                        List<HrBillDTO> lastHrBillDTOList = this.hrBillRepository.getBillByObject(new HrBillDTO().setClientId(clientId).setPayYear(lastPayYear).setPayMonthly(lastPayMonthly).setBillType(BillEnum.BillType.SALARY_BILL.getKey()).setIsOfficial(1).setBillPurpose(1));
                        if (!lastHrBillDTOList.isEmpty()) {
                            HrBillDTO lastHrBillDTO;
                            if (lastHrBillDTOList.size() > 1) {
                                HrBillDTO hrBillDTO = lastHrBillDTOList.stream().filter(HrBillDTO::getIsChoice).findFirst().orElse(null);
                                if (hrBillDTO == null) {
                                    lastHrBillDTO = lastHrBillDTOList.get(0);
                                } else {
                                    lastHrBillDTO = hrBillDTO;
                                }
                            } else {
                                lastHrBillDTO = lastHrBillDTOList.get(0);
                            }
                            HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO = this.hrBillDynamicFieldsRepository.getByBillId(lastHrBillDTO.getId());
                            if (hrBillDynamicFieldsDTO != null) {
                                // 组装所需要的表头
                                this.assembleHeader(hrBillDynamicFieldsDTO, specialBillExcelDataMap,specialBillClient);
                            }
                        }
                    }
                    break;
                case SECONDARY_POLITICS_LAW_COMMITTEE:
                    // 组装所需要的表头
                    this.assembleHeader(thisHrBillDynamicFieldsDTO, specialBillExcelDataMap, specialBillClient);
                    break;
                case HAIER:break;
                default:break;
            }
        }
    }

    /**
     * 组装表头
     *  @param hrBillDynamicFieldsDTO
     * @param specialBillExcelDataMap
     * @param specialBillClient
     */
    private void assembleHeader(HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO, Map<String, BillExcelDataDTO> specialBillExcelDataMap, SpecialBillClient specialBillClient) {
        List<HrExpenseManageDTO> mappingFields = new ArrayList<>();
        hrBillDynamicFieldsDTO.getBillFieldInfoDTO().getMappingFields().forEach((l -> {
            if (DynamicFeeTypesEnum.NAME.getKey().equals(l.getExpenseType()) || DynamicFeeTypesEnum.ID_CARD.getKey().equals(l.getExpenseType())) {
                mappingFields.add(l);
            }
        }));
        hrBillDynamicFieldsDTO.getBillFieldInfoDTO().getCellItems().forEach(cellItem -> {
            HrExpenseManageDTO hrExpenseManageDTO = new HrExpenseManageDTO();
            if (DynamicFeeTypesEnum.TOTAL_FINANCIAL_ALLOCATION_FOR_THE_MONTH.getValue().equals(cellItem.getValue())) {
                hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.TOTAL_FINANCIAL_ALLOCATION_FOR_THE_MONTH.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.TOTAL_FINANCIAL_ALLOCATION_FOR_THE_MONTH.getValue());
            } else if (DynamicFeeTypesEnum.LABOR_DISPATCH_FEE.getValue().equals(cellItem.getValue())) {
                hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.LABOR_DISPATCH_FEE.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.LABOR_DISPATCH_FEE.getValue());
            } else if (DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue().equals(cellItem.getValue())) {
                hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue());
            } else if (DynamicFeeTypesEnum.CLIENT_SOCIAL_SECURITY_MONTHLY.getValue().equals(cellItem.getValue())) {
                hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.CLIENT_SOCIAL_SECURITY_MONTHLY.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.CLIENT_SOCIAL_SECURITY_MONTHLY.getValue());
            } else if (DynamicFeeTypesEnum.CLIENT_PROVIDENT_FUND_MONTHLY.getValue().equals(cellItem.getValue())) {
                hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.CLIENT_PROVIDENT_FUND_MONTHLY.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.CLIENT_PROVIDENT_FUND_MONTHLY.getValue());
            } else if (DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MONTHLY.getValue().equals(cellItem.getValue())) {
                hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MONTHLY.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MONTHLY.getValue());
            } else if (DynamicFeeTypesEnum.PERSONAL_PROVIDENT_FUND_MONTHLY.getValue().equals(cellItem.getValue())) {
                hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.PERSONAL_PROVIDENT_FUND_MONTHLY.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.PERSONAL_PROVIDENT_FUND_MONTHLY.getValue());
            } else {
                if (specialBillClient.equals(SpecialBillClient.EAST_DISTRICT_PUBLIC_SECURITY)){
                    //东区公安离职人员需要岗位工资
                    if (DynamicFeeTypesEnum.POST_GRADE_SYSTEM.getValue().equals(cellItem.getValue())) {
                        hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.POST_GRADE_SYSTEM.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.POST_GRADE_SYSTEM.getValue());
                    } else if (DynamicFeeTypesEnum.GRADED_WAGES.getValue().equals(cellItem.getValue())) {
                        hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.GRADED_WAGES.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.GRADED_WAGES.getValue());
                    } else if (DynamicFeeTypesEnum.MONTHLY_PERFORMANCE_APPRAISAL.getValue().equals(cellItem.getValue())) {
                        hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.MONTHLY_PERFORMANCE_APPRAISAL.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.MONTHLY_PERFORMANCE_APPRAISAL.getValue());
                    } else if (DynamicFeeTypesEnum.CURRENT_LIFE_AMOUNT.getValue().equals(cellItem.getValue())) {
                        hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.CURRENT_LIFE_AMOUNT.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.CURRENT_LIFE_AMOUNT.getValue());
                    } else if (DynamicFeeTypesEnum.AMOUNT_OF_HEATSTROKE_PREVENTION_FEE.getValue().equals(cellItem.getValue())) {
                        hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.AMOUNT_OF_HEATSTROKE_PREVENTION_FEE.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.AMOUNT_OF_HEATSTROKE_PREVENTION_FEE.getValue());
                    } else if (DynamicFeeTypesEnum.SOCIAL_INSURANCE_MARGIN.getValue().equals(cellItem.getValue())) {
                        hrExpenseManageDTO.setExpenseType(DynamicFeeTypesEnum.SOCIAL_INSURANCE_MARGIN.getKey()).setExpenseTypeName(DynamicFeeTypesEnum.SOCIAL_INSURANCE_MARGIN.getValue());
                    }
                    else {
                        hrExpenseManageDTO = null;
                    }
                }else {
                    hrExpenseManageDTO = null;
                }
            }
            if (hrExpenseManageDTO != null) {
                mappingFields.add(hrExpenseManageDTO.setExpenseName(cellItem.getValue()).setSortValue(cellItem.getOrder()).setColumnLabel(cellItem.getColumnLabel()).setColIndex(cellItem.getFirstCol()));
            }
        });

        // hrBillDynamicFieldsDTO.getBillFieldInfoDTO().setMappingFields(mappingFields);
        BillFieldInfoDTO billFieldInfoDTO = hrBillDynamicFieldsDTO.getBillFieldInfoDTO();
        billFieldInfoDTO.setMappingFields(mappingFields);
        specialBillExcelDataMap.putAll(BillParseUtils.parseExcel2MapData(billFieldInfoDTO, hrBillDynamicFieldsDTO.getDataStartRows(), hrBillDynamicFieldsDTO.getOriginBillUrl(), hrBillDynamicFieldsDTO.getOriginBillUrl(), 3));
    }

    /**
     * 判断账单特殊处理客户
     *
     * @param clientIdList
     */
    public SpecialBillClient judgeSpecialBillClient(List<String> clientIdList) {
        // 初步匹配
        for (String clientId : clientIdList) {
            SpecialBillClient enumByKey = EnumUtils.getEnumByKey(SpecialBillClient.class, clientId);
            if (enumByKey != null) {
                return enumByKey;
            }
        }
        // 二级匹配
        HrClient hrClient = this.hrClientRepository.selectById(clientIdList.get(0));
        return EnumUtils.getEnumByKey(SpecialBillClient.class, hrClient.getParentId());
    }

    /**
     * 添加中石化账单费用项
     *
     * @param hrBillDetailItemsDTO
     * @return
     */
    @Override
    public List<HrBillDetailItems> createBillDetailItems(HrBillDetailItemsDTO hrBillDetailItemsDTO) {
        this.checkParameter(hrBillDetailItemsDTO);

        List<HrBillDetail> hrBillDetailList = hrBillDetailRepository.selectList(new QueryWrapper<HrBillDetail>()
            .eq("is_delete", 0).eq("bill_id", hrBillDetailItemsDTO.getBillId()));
        List<String> billDetailId = hrBillDetailList.stream().map(HrBillDetail::getId).collect(Collectors.toList());
        List<HrBillDetailItems> itemsList = hrBillDetailItemsRepository.selectList(new QueryWrapper<HrBillDetailItems>().eq("is_delete", 0).in("bill_detail_id", billDetailId));
        List<HrBillDetailItems> collect = itemsList.stream().filter(lst -> lst.getExpenseName().equals(hrBillDetailItemsDTO.getExpenseName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)){
            throw new CommonException("费用项名称不能重复！");
        }
        List<HrBillDetailItems> list = new ArrayList<>();
        for (HrBillDetail hrBillDetail : hrBillDetailList) {
            HrBillDetailItems hrBillDetailItems = new HrBillDetailItems();
            hrBillDetailItems.setBillDetailId(hrBillDetail.getId())
                .setExpenseName(hrBillDetailItemsDTO.getExpenseName())
                .setExpenseType(DynamicFeeTypesEnum.OTHER_EXPENSE.getKey())
                .setAmount(BigDecimal.ZERO)
                .setIsDefault(hrBillDetailItemsDTO.getIsDefault());
            this.save(hrBillDetailItems);
            list.add(hrBillDetailItems);
        }
        return list;
    }

    /**
     * 删除中石化账单费用项
     * @param hrBillDetailItemsDTO
     */
    @Override
    public void deleteBillDetailItems(HrBillDetailItemsDTO hrBillDetailItemsDTO) {
        this.checkParameter(hrBillDetailItemsDTO);

        List<HrBillDetail> hrBillDetailList = hrBillDetailRepository.selectList(new QueryWrapper<HrBillDetail>()
            .eq("is_delete", 0).eq("bill_id", hrBillDetailItemsDTO.getBillId()));

        List<String> billDetailId = hrBillDetailList.stream().map(HrBillDetail::getId).collect(Collectors.toList());
        hrBillDetailItemsRepository.deleteBatch(billDetailId,DynamicFeeTypesEnum.OTHER_EXPENSE.getKey(),hrBillDetailItemsDTO.getExpenseName());
    }

    private void checkParameter(HrBillDetailItemsDTO hrBillDetailItemsDTO) {
        // 必填参数校验
        if(StringUtils.isEmpty(hrBillDetailItemsDTO.getExpenseName())) {
            throw new CommonException("费用类型名称不能为空!");
        }
        if(StringUtils.isEmpty(hrBillDetailItemsDTO.getBillId())) {
            throw new CommonException("账单ID不能为空!");
        }
    }

    /**
     *  保存动态费用项明细
     * @param excelDataDTO
     * @param initDynamicFee
     * @param billDetailId
     */
    private void saveDynamicFeeDetail(BillExcelDataDTO excelDataDTO, List<HrBillDetailItemsDTO> initDynamicFee, String billDetailId) {
        List<HrBillDetailItemsDTO> dynamicFeeItems;
        if (excelDataDTO != null && excelDataDTO.getDynamicFeeItems() != null) {
            dynamicFeeItems = excelDataDTO.getDynamicFeeItems();
        } else {
            // 添加空白的费用项
            dynamicFeeItems = new ArrayList<>(initDynamicFee);
        }
        this.saveDetailItems(billDetailId, dynamicFeeItems);
    }

    /**
     *  获取一个值为0的动态费用项列表
     * @param excelDataMap
     * @return
     */
    @Override
    public List<HrBillDetailItemsDTO> getInitDynamicFee(Map<String, BillExcelDataDTO> excelDataMap) {
        Set<String> keySet = excelDataMap.keySet();
        String key = keySet.iterator().next();
        BillExcelDataDTO excelDataDTO = excelDataMap.get(key);
        List<HrBillDetailItemsDTO> dynamicFeeItems = excelDataDTO.getDynamicFeeItems();
        List<HrBillDetailItemsDTO> result = new ArrayList<>();
        dynamicFeeItems.forEach(f -> {
            HrBillDetailItemsDTO param = new HrBillDetailItemsDTO();
            BeanUtils.copyProperties(f, param);
            param.setAmount(BigDecimal.ZERO);
            result.add(param);
        });
        return result;
    }

    /**
     *  生成公司的汇总账单明细
     * @param hrBillDTO
     * @param halfYearlyServiceFee
     */
    private void createHrBillTotal(HrBillDTO hrBillDTO, BigDecimal halfYearlyServiceFee, BigDecimal billTotalNum) {
        if (hrBillDTO.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())){//薪酬账单
            String billId = hrBillDTO.getId();
            HrBillTotal billTotal = this.hrBillTotalRepository.createByBillId(billId);
            billTotal.setBillId(billId)
                .setType(hrBillDTO.getBillType())
                .setPayYear(hrBillDTO.getPayYear())
                .setPayMonthly(hrBillDTO.getPayMonthly())
                .setUnitSubtotal(CalculateUtils.decimalListAddition(billTotal.getUnitPensionTotal(), billTotal.getUnitMedicalTotal(), billTotal.getUnitUnemploymentTotal(), billTotal.getWorkInjuryTotal()))
                .setPersonalSubtotal(CalculateUtils.decimalListAddition(billTotal.getPersonalPensionTotal(), billTotal.getPersonalMedicalTotal(), billTotal.getPersonalUnemploymentTotal()))
                .setLastMonthMakeUp(BigDecimal.ZERO)
                .setServiceFeeTotal(CalculateUtils.decimalListAddition(billTotal.getServiceFeeTotal(), halfYearlyServiceFee));

            // 获取总的费用 = 社保总金额 + 公积金总金额 + 服务费总计 + 上月补差 + 实发总计 + 个税总计 + 其他费用总计
//            BigDecimal total = CalculateUtils.decimalListAddition(billTotal.getSocialSecurityTotal(), billTotal.getAccumulationFundTotal(),
//                billTotal.getServiceFeeTotal(), billTotal.getLastMonthMakeUp(),
//                billTotal.getRealSalaryTotal(), billTotal.getPersonalTaxTotal(),
//                billTotal.getOtherFeeTotal()
//            );
            billTotal.setTotal(billTotalNum);
            this.hrBillTotalRepository.insert(billTotal);
        }else if (hrBillDTO.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey())){//其他账单
            HrBillTotal  billTotal = new HrBillTotal();
            billTotal.setBillId(hrBillDTO.getId())
                .setType(hrBillDTO.getBillType())
                .setPayYear(hrBillDTO.getPayYear())
                .setPayMonthly(hrBillDTO.getPayMonthly())
                .setTotal(billTotalNum);
            List<HrBillDetailDTO> billDetailDTOS = hrBillDetailRepository.getListByBillId(hrBillDTO.getId(), 1);
            if (CollectionUtils.isNotEmpty(billDetailDTOS)){
                List<List<HrBillDetailItemsDTO>> listList = billDetailDTOS.stream().map(HrBillDetailDTO::getHrBillDetailItemsList).collect(Collectors.toList());
                BigDecimal chargeTotal = BigDecimal.ZERO;
                BigDecimal refundTotal = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(listList)){
                    for (List<HrBillDetailItemsDTO> itemsDTOS : listList) {
                        for(HrBillDetailItemsDTO param: itemsDTOS) {
                            DynamicFeeTypesEnum typesEnum = EnumUtils.getEnumByKey(DynamicFeeTypesEnum.class, param.getExpenseType());
                            BigDecimal amount = param.getAmount() == null? BigDecimal.ZERO: param.getAmount();
                            switch (typesEnum) {
                                case CHARGE_ITEM:
                                    chargeTotal = chargeTotal.add(amount);
                                    break;
                                case REFUND_ITEM:
                                    refundTotal = refundTotal.add(amount.abs());
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
                billTotal.setChargeTotal(chargeTotal);
                billTotal.setRefundTotal(refundTotal);
            }
            this.hrBillTotalRepository.insert(billTotal);
        }else  if (hrBillDTO.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
            hrBillService.createSinopecBillTotal(hrBillDTO);
        }
    }

    /**
     *  获取薪酬账单详情
     * @param billId
     */
    @Override
    public HrBillDTO getBillDynamicDetailInfo(String billId) {
        // 获取固定字段薪酬账单详情
        HrBillDTO result = this.hrBillService.getHrBill(billId);

        // 获取账单配置
        HrBillDynamicFieldsDTO dynamicFieldsDTO = this.hrBillDynamicFieldsRepository.getByBillId(billId);
        BillFieldInfoDTO billFieldInfoDTO = dynamicFieldsDTO.getBillFieldInfoDTO();

        // 设置原单
        result.setOriginBillName(dynamicFieldsDTO.getOriginBillName())
            .setOriginBillUrl(dynamicFieldsDTO.getOriginBillUrl());

        // 账单详情中补充动态费用项
        List<HrBillDetailDTO> billDetailList = result.getBillDetailList();

        // 获取动态费用项列表
        List<String> dynamicFees = BillFieldInfoDTO.getDynamicFeeList(billFieldInfoDTO.getMappingFields());
        // 获取动态费用数据列
        List<HashMap<String, Object>> list = this.hrBillDetailItemsRepository.getBillDynamicItemsData(billId, dynamicFees);
        Map<String, HashMap<String, Object>> dataMap = new HashMap<>();
        for(HashMap<String, Object> val: list) {
            String billDetailId = (String)val.get("bill_detail_id");
            dataMap.put(billDetailId, val);
        }

        // 费用动态项,关联到账单详情
        for(HrBillDetailDTO param: billDetailList) {
            String billDetailId = param.getId();
            param.setDynamicFeesMap(dataMap.get(billDetailId));
        }

        result.setBillDetailList(billDetailList);
        return result;

    }

    /**
     *  excel存在但是系统不存在的员工,则存储到工资明细中并且为不可用状态
     * @param excelDataMap
     */
    private void saveUnUseableStaff(Map<String, BillExcelDataDTO> excelDataMap, HrBillDTO hrBillDTO) {
        if(excelDataMap == null || excelDataMap.size() == 0) {
            return;
        }
        List<HrBillDetail> hrBillDetails = new ArrayList<>();
        List<HrBillDetailItems> hrBillDetailItems = new ArrayList<>();
        for(String key: excelDataMap.keySet()) {
            BillExcelDataDTO billExcelDataDTO = excelDataMap.get(key);
            HrBillDetail hrBillDetail = new HrBillDetail()
                .setId(RandomUtil.generateId())
                .setName(billExcelDataDTO.getName())
                .setCertificateNum(billExcelDataDTO.getIdCard())
                .setIsUsed(false)
                .setBillUsed(false)
                .setReason("该客户下不存在该员工!")
                .setPayYear(hrBillDTO.getPayYear())
                .setPayMonthly(hrBillDTO.getPayMonthly())
                .setBillId(hrBillDTO.getId());
            // 如果是 系统内——其他单位的员工，将系统编号、手机号、staff_id 存储到明细中，后续工资发放有用到
            Optional.ofNullable(hrTalentStaffRepository.getStaffInfoByCertificateNum(billExcelDataDTO.getIdCard())).ifPresent(
                staff -> {
                    hrBillDetail.setSystemNum(staff.getSystemNum());
                    hrBillDetail.setPhone(staff.getPhone());
                    hrBillDetail.setStaffId(staff.getId());
                }
            );
            hrBillDetails.add(hrBillDetail);
            List<HrBillDetailItemsDTO> dynamicFeeItemsDTOS = billExcelDataDTO.getDynamicFeeItems();
            if(dynamicFeeItemsDTOS == null || dynamicFeeItemsDTOS.isEmpty()) {
                return;
            }
            dynamicFeeItemsDTOS.forEach(itemDTO ->{
                itemDTO.setId(RandomUtil.generateId()).setBillDetailId(hrBillDetail.getId());
                hrBillDetailItems.add(hrBillDetailItemsMapper.toEntity(itemDTO));
            });

        }
        hrBillDetailRepository.batchSave(hrBillDetails);
        hrBillDetailItems.forEach(ls ->{
            if (ls.getId() == null){
                ls.setId(RandomUtil.generateId());
            }
        });
        saveBatch(hrBillDetailItems);
//        this.hrBillDetailService.saveBatch(hrBillDetails);
        // 保存工资项明细
//        for(HrBillDetail param: hrBillDetails) {
//            BillExcelDataDTO excelDataDTO = excelDataMap.get(param.getCertificateNum());
//            this.saveDetailItems(param.getId(), excelDataDTO.getDynamicFeeItems());
//        }
    }

    /**
     *  保存账单费用项明细
     * @param billDetailId 账单详情id
     * @param dynamicFeeItemsDTOS 动态费用项列表
     */
    private void saveDetailItems(String billDetailId, List<HrBillDetailItemsDTO> dynamicFeeItemsDTOS) {
        if(dynamicFeeItemsDTOS == null || dynamicFeeItemsDTOS.isEmpty()) {
            return;
        }
        for(HrBillDetailItemsDTO itemsDTO: dynamicFeeItemsDTOS) {
            itemsDTO.setBillDetailId(billDetailId);
        }
        this.saveBatch(this.hrBillDetailItemsMapper.toEntity(dynamicFeeItemsDTOS));
    }

    /**
     * 计算个税
     * <p>
     * 以10月为例，个税计算规则如下：
     * A. 10月累计收入额 = 9月累计收入额 + 10月应发工资。
     * B. 10月累计减除费用 = 9月累计减除费用 + (9月累计减除费用 - 8月累计减除费用)「PS：若上月累计减除费用是60000，则直接按60000计算；若小于60000，则正常计算，1月按照5000计算」。
     * C. 10月累计专项扣除 = 9月累计专项扣除 + 10月个人社保小计 + 10月个人公积金小计。
     * D. 10月累计子女教育 = 9月累计子女教育 + (9月累计子女教育 - 8月累计子女教育)。
     * E. 10月累计继续教育 = 9月累计继续教育 + (9月累计继续教育 - 8月累计继续教育)。
     * F. 10月累计住房贷款利息 =   9月累计住房贷款利息 + (9月累计住房贷款利息 - 8月累计住房贷款利息)。
     * G. 10月累计住房租金 = 9月累计住房租金 + (9月累计住房贷款利息 - 8月累计住房租金)。
     * H. 10月累计赡养老人 = 9月累计赡养老人 + (9月累计赡养老人 - 8月累计赡养老人)。
     * I. 10月累计其他扣除 = 9月累计其他扣除 + (9月累计其他扣除 - 8月累计其他扣除)。
     * J. 9月累计免税收入：由个税导盘获得。
     * K. 10月累计应纳税所得额 = A - B - C - D - E - F - G - H - I - J 。
     * L. 10月累计应纳税额 = K x 税率 - 速算扣除数。[税率等级由速算扣除规则决定]
     * M. 10月累计应扣缴税额 = L - 9月累计减免税额。
     * N. 10月个税 = M - 9月累计应扣缴税额。
     * </p>
     * <p>
     * 若专项附加扣除导入的数据中含有B C D E F G H I J当月的数据则直接取用，若没有则根据公式计算得出；A K L M N根据公式计算得出。
     * </p>
     * <p>
     * 若2月份的专项附加扣除数据中所有员工的本期收入都为0，则累计收入额与累计专项扣除数据作为1月份的数据使用，其余作为二月份的数据使用。
     * 若2月份的专项附加扣除数据中有一个员工的本期收入不为0，所有数据均作为二月份的使用。
     * </P>
     *
     * @param detailDTO
     * @return void
     * <AUTHOR>
     * @date 2021/11/7
     */
    @Override
    public void calculatePersonalIncomeTax(HrBillDetailDTO detailDTO, Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions) {

        // 员工id不能为空
        if (StringUtils.isEmpty(detailDTO.getCertificateNum())) {
            return;
        }

        int payYear = detailDTO.getPayYear();
        int payMonthly = detailDTO.getPayMonthly();

        int lastMonthly = 0;
        int beforeLastMonthly = 0;

        // 专项附加扣除数据中所有员工的本期收入
       /* String redisKey = RedisKeyEnum.bill.SUM_CURRENT_INCOME.getValue() + detailDTO.getClientId() + ":" + payMonthly;
        Object redisObject = this.redisCache.getCacheObject(redisKey);
        if (redisObject != null && !"null".equals(String.valueOf(redisObject))) {
            sumCurrentIncome = new BigDecimal(String.valueOf(redisObject));
        } else {
            if (sumCurrentIncome == null) {
                sumCurrentIncome = BigDecimal.ZERO;
            }
            this.redisCache.setCacheObject(redisKey, sumCurrentIncome, 30, TimeUnit.MINUTES);
        }*/
        if (sumCurrentIncome == null) {
            sumCurrentIncome = BigDecimal.ZERO;
        }
        // 若为1月 不再获取前两个月的数据
        // 若为2月，只获取第一个月的数据
        // 若大于2月，获取前两个月数据
        if (payMonthly == 2) {
            // 获取上月月份
            lastMonthly = payMonthly - 1;
        } else if (payMonthly > 2) {
            // 获取上月月份
            lastMonthly = payMonthly - 1;
            // 获取上上月月份
            // beforeLastMonthly = payMonthly - 2;
        }
        HrSpecialDeduction specialDeduction = new HrSpecialDeduction();
        if (specialDeductionList != null && !specialDeductionList.isEmpty()){
            specialDeduction = specialDeductionList.stream().filter(lst -> lst.getCertificateNum().equals(detailDTO.getCertificateNum()) && lst.getStartDate().isEqual(LocalDate.of(payYear, payMonthly, 1))).findFirst().orElse(null);
            if (specialDeduction == null) {
                specialDeduction = new HrSpecialDeduction();
                //计算个税时，若在专项扣除附加项中查不到员工的累计减除费用就默认该员工本月的减除费用为5000元
                specialDeduction.setSubtract(new BigDecimal(5000));
            }
        }

        // 若获取不到前两月的专项扣除记录，计算项按照0计算
        // 获取上月专项扣除
        HrSpecialDeduction lastSpecialDeduction = new HrSpecialDeduction();
        if (lastMonthly != 0) {
            int finalLastMonthly = lastMonthly;
            if (specialDeductionList != null && !specialDeductionList.isEmpty()){
                lastSpecialDeduction = specialDeductionList.stream().filter(lst -> lst.getCertificateNum().equals(detailDTO.getCertificateNum()) && lst.getStartDate().isEqual(LocalDate.of(payYear, finalLastMonthly, 1))).findFirst().orElse(null);
                if (lastSpecialDeduction == null) {
                    lastSpecialDeduction = new HrSpecialDeduction();
                }
            }
            // 若专项附加扣除数据中所有员工的本期收入都为0，则累计收入额与累计专项扣除数据作为1月份的数据使用，其余作为二月份的数据使用。
            if (BigDecimalCompare.of(sumCurrentIncome).eq(BigDecimal.ZERO)) {
                lastSpecialDeduction.setCumulativeIncome(specialDeduction.getCumulativeIncome());
                lastSpecialDeduction.setSpecialDeduction(specialDeduction.getSpecialDeduction());
            }
        }

        // 获取上上月专项扣除
        /*HrSpecialDeduction beforeLastSpecialDeduction = new HrSpecialDeduction();
        if (beforeLastMonthly != 0) {
            beforeLastSpecialDeduction = this.hrSpecialDeductionRepository.getByStaffIdAndStartDate(detailDTO.getStaffId(), LocalDate.of(payYear, beforeLastMonthly, 1));
            if (beforeLastSpecialDeduction == null) {
                beforeLastSpecialDeduction = new HrSpecialDeduction();
            }
        }*/

        if (BigDecimalCompare.of(detailDTO.getSalary()).eq(BigDecimal.ZERO)) {
            detailDTO.setPersonalTax(BigDecimal.ZERO);
        } else {
            // 本月累计收入
            BigDecimal a;
            // 本月累计减除费用
            BigDecimal b;
            // 本月累计专项扣除
            BigDecimal c;
            // 本月累计子女教育
            BigDecimal d;
            // 本月累计继续教育
            BigDecimal e;
            // 本月累计住房贷款利息
            BigDecimal f;
            // 本月累计住房租金
            BigDecimal g;
            // 本月累计赡养老人
            BigDecimal h;
            // 本月累计其他扣除
            BigDecimal i;
            // 上月累计免税收入
            BigDecimal j;
            // 截止到本月累计应纳税所得额
            BigDecimal k;
            // 本月累计应纳税额
            BigDecimal l;
            // 本月累计应扣缴税额
            BigDecimal m;
            // 本月个税
            BigDecimal n;
            //累计3岁以下婴幼儿照护
            BigDecimal o;

            // 差旅补助为不计入个税收入 计算个税时从应发工资中扣除
            if (detailDTO.getTravelAllowance() != null) {
                detailDTO.setSalary(CalculateUtils.decimalSubtraction(detailDTO.getSalary(), detailDTO.getTravelAllowance()));
            } else {
                if (detailDTO.getHrBillDetailItemsList() != null && !detailDTO.getHrBillDetailItemsList().isEmpty()) {
                    AtomicReference<BigDecimal> travelAllowance = new AtomicReference<>(BigDecimal.ZERO);
                    detailDTO.getHrBillDetailItemsList().forEach(ls -> {
                        if ("差旅补助".equals(ls.getExpenseName())) {
                            travelAllowance.set(ls.getAmount());
                        }
                    });
                    detailDTO.setSalary(CalculateUtils.decimalSubtraction(detailDTO.getSalary(), travelAllowance.get()));
                }
            }
            if (BigDecimalCompare.of(sumCurrentIncome).eq(BigDecimal.ZERO)) {
                a = CalculateUtils.decimalAddition(lastSpecialDeduction.getCumulativeIncome(), detailDTO.getSalary());
                c = CalculateUtils.decimalListAddition(lastSpecialDeduction.getSpecialDeduction(), detailDTO.getPersonalSubtotal(), detailDTO.getPersonalAccumulationFund(), detailDTO.getPersonalAccumulationFundMakeUp());
            } else {
                a = specialDeduction.getCumulativeIncome();
                c = specialDeduction.getSpecialDeduction();
            }
            if (BigDecimalCompare.of(a).le(BigDecimal.valueOf(personalTaxStart))) {
                detailDTO.setPersonalTax(BigDecimal.ZERO);
            } else {
                b = specialDeduction.getSubtract();
                d = specialDeduction.getChildrenEducation();
                e = specialDeduction.getContinuingEducation();
                f = specialDeduction.getHousingLoan();
                g = specialDeduction.getHousingRent();
                h = specialDeduction.getSupportElderly();
                i = specialDeduction.getOther();
                j = specialDeduction.getDutyFree();
                o = specialDeduction.getCumulativeInfantCare();
                k = CalculateUtils.decimalListSubstract(a, CalculateUtils.decimalListAddition(b, c, d, e, f, g, h, i, j),o);
                // 如果k小于等于0
                if (BigDecimalCompare.of(k).le(BigDecimal.ZERO)) {
                    detailDTO.setPersonalTax(BigDecimal.ZERO);
                } else {
                    l = this.calculateAccumulatedTaxPayable(k, hrQuickDeductions);
                    m = CalculateUtils.decimalSubtraction(l, lastSpecialDeduction.getTaxDeduction());
                    n = CalculateUtils.decimalSubtraction(m, specialDeduction.getPrepaidTax());

                    BigDecimal tax = BigDecimal.ZERO;
                    BigDecimal result = n.add(tax);
                    //个税
                    detailDTO.setPersonalTax(BigDecimalCompare.of(result).lt(BigDecimal.ZERO) ? BigDecimal.ZERO : result);
                }
            }

        }
    }

    /**
     * 全年一次性奖金计算个税
     * <p>
     * a. 全年一次性奖金 / 12
     * b. 使用 a 匹配速算扣除数
     * c. 个税 = (全年一次性奖金 * 适用税率 - 速算扣除数) - 补差
     * </p>
     *
     * @param detailDTO
     */
    @Override
    public void annualLumpSumBonusTax(HrBillDetailDTO detailDTO){
        if (detailDTO.getTotal() != null){
            BigDecimal monthlyBonus = detailDTO.getTotal().divide(BigDecimal.valueOf(12), 2, BigDecimal.ROUND_HALF_UP);
            HrQuickDeduction hrQuickDeduction = this.queryQuickCalculationDeduction(monthlyBonus,false,null);
            BigDecimal bigDecimal = CalculateUtils.decimalSubtraction(CalculateUtils.decimalMultiply(detailDTO.getTotal(), hrQuickDeduction.getTaxRate(), 2), BigDecimal.valueOf(hrQuickDeduction.getQuickDeductionNumber()));
            detailDTO.setPersonalTax(bigDecimal);
        }
    }

    /**
     * 计算本月累计应纳税额
     *
     * @param k 本月累计应纳税所得额
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2021/11/7
     **/
    private BigDecimal calculateAccumulatedTaxPayable(BigDecimal k, List<HrQuickDeduction> hrQuickDeductions) {
        HrQuickDeduction hrQuickDeduction = this.queryQuickCalculationDeduction(k,true, hrQuickDeductions);
        // 没有匹配的计算公式
        if(StringUtils.isEmpty(hrQuickDeduction.getId())) {
            return BigDecimal.ZERO;
        }
        // List<HrQuickDeduction> collect = hrQuickDeductions.stream().filter(ls -> BigDecimalCompare.of(k).gt(BigDecimal.valueOf(ls.getMinPayTaxes())) && BigDecimalCompare.of(k).le(BigDecimal.valueOf(ls.getMaxPayTaxes()))).collect(Collectors.toList());
        // HrQuickDeduction hrQuickDeduction = collect.get(0);
        return CalculateUtils.decimalSubtraction(CalculateUtils.decimalMultiply(k, hrQuickDeduction.getTaxRate(), 2), BigDecimal.valueOf(hrQuickDeduction.getQuickDeductionNumber()));
    }

    private HrQuickDeduction queryQuickCalculationDeduction(BigDecimal k,Boolean flag, List<HrQuickDeduction> hrQuickDeductions) {
        if (!flag){
            hrQuickDeductions = new ArrayList<>();
            hrQuickDeductions.add(new HrQuickDeduction().setMinPayTaxes(0).setMaxPayTaxes(3000).setTaxRate(BigDecimal.valueOf(0.03)).setQuickDeductionSeries("1").setQuickDeductionNumber(0));
            hrQuickDeductions.add(new HrQuickDeduction().setMinPayTaxes(3000).setMaxPayTaxes(12000).setTaxRate(BigDecimal.valueOf(0.10)).setQuickDeductionSeries("2").setQuickDeductionNumber(210));
            hrQuickDeductions.add(new HrQuickDeduction().setMinPayTaxes(12000).setMaxPayTaxes(25000).setTaxRate(BigDecimal.valueOf(0.20)).setQuickDeductionSeries("3").setQuickDeductionNumber(1410));
            hrQuickDeductions.add(new HrQuickDeduction().setMinPayTaxes(25000).setMaxPayTaxes(35000).setTaxRate(BigDecimal.valueOf(0.25)).setQuickDeductionSeries("4").setQuickDeductionNumber(2660));
            hrQuickDeductions.add(new HrQuickDeduction().setMinPayTaxes(35000).setMaxPayTaxes(55000).setTaxRate(BigDecimal.valueOf(0.30)).setQuickDeductionSeries("5").setQuickDeductionNumber(4410));
            hrQuickDeductions.add(new HrQuickDeduction().setMinPayTaxes(55000).setMaxPayTaxes(80000).setTaxRate(BigDecimal.valueOf(0.35)).setQuickDeductionSeries("6").setQuickDeductionNumber(7160));
            hrQuickDeductions.add(new HrQuickDeduction().setMinPayTaxes(80000).setMaxPayTaxes(Integer.MAX_VALUE).setTaxRate(BigDecimal.valueOf(0.45)).setQuickDeductionSeries("7").setQuickDeductionNumber(15160));
        }

        if (hrQuickDeductions == null || hrQuickDeductions.isEmpty()) {
            throw new CommonException("未配置速算扣除数！");
        }
        HrQuickDeduction hrQuickDeduction = new HrQuickDeduction();
        if (BigDecimalCompare.of(k).lt(BigDecimal.ZERO)) {
            return hrQuickDeduction;
        }
        for (HrQuickDeduction temp : hrQuickDeductions) {
            // 初始化最大,最小值防止空指针
            if(temp.getMinPayTaxes() == null) {
                temp.setMinPayTaxes(0);
            }
            if(temp.getMaxPayTaxes() == null) {
                temp.setMaxPayTaxes(Integer.MAX_VALUE);
            }

            if (BigDecimalCompare.of(k).eq(BigDecimal.ZERO) && BigDecimalCompare.of(k).eq(BigDecimal.valueOf(temp.getMinPayTaxes()))) {
                hrQuickDeduction = temp;
                break;
            }
            if (BigDecimalCompare.of(k).gt(BigDecimal.valueOf(temp.getMinPayTaxes())) &&
                BigDecimalCompare.of(k).le(BigDecimal.valueOf(temp.getMaxPayTaxes()))) {
                hrQuickDeduction = temp;
                break;
            }
            if ((hrQuickDeductions.indexOf(temp) + 1 == hrQuickDeductions.size()) && BigDecimalCompare.of(k).gt(BigDecimal.valueOf(temp.getMaxPayTaxes()))) {
                hrQuickDeduction = temp;
                break;
            }
        }
        return hrQuickDeduction;
    }


}
