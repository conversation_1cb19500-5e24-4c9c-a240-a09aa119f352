package cn.casair.repository;
import cn.casair.domain.HrFeeReview;
import cn.casair.domain.HrStaffEmolument;
import cn.casair.dto.HrAccumulationFundDTO;
import cn.casair.dto.HrEmployeeWelfareDTO;
import cn.casair.dto.HrSocialSecurityDTO;
import cn.casair.dto.HrStaffEmolumentDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 薪酬参数数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-04
 */
@Repository
public interface HrStaffEmolumentRepository extends BaseMapper<HrStaffEmolument> {

    /**
     * 获取员工福利列表
     *
     * @param staffId
     * @return
     */
    List<HrEmployeeWelfareDTO> selectStaffWelfare(@Param("staffId") String staffId);

    /**
     * 回溯缴费年月
     *
     * @param hrEmployeeWelfareOld
     * <AUTHOR>
     * @return int
     * @date 2021/12/13
     **/
    int updateOldPaymentDate(@Param("params") HrEmployeeWelfareDTO hrEmployeeWelfareOld);

    /**
     * 回溯员工福利配置
     *
     * @param hrEmployeeWelfareOld
     * @return int
     * <AUTHOR>
     * @date 2021/12/10
     **/
    int updateWelfareById(@Param("params") HrEmployeeWelfareDTO hrEmployeeWelfareOld);

    /**
     * 更新员工缴费年月
     *
     * @param staffId
     * @param payYear
     * @param payMonthly
     * @return void
     * <AUTHOR>
     * @date 2021/11/29
     **/
    void updateStaffPaymentDate(@Param("staffId") String staffId, @Param("payYear") int payYear, @Param("payMonthly") int payMonthly);

    /**
     * 根据账单更新员工福利配置缴费年月
     *
     * @param hrFeeReview
     * @return void
     * <AUTHOR>
     * @date 2021/11/11
     **/
    void updatePaymentDateByBillId(HrFeeReview hrFeeReview);

    /**
     * 获取单位下员工福利列表
     *
     * @param clientId
     * @return cn.casair.dto.HrEmployeeWelfareDTO
     * <AUTHOR>
     * @date 2021/11/1
     **/
    List<HrEmployeeWelfareDTO> selectByClientId(String clientId);

    /**
     * 回退员工薪酬参数
     *
     * @param hrStaffEmolument
     * @return int
     * <AUTHOR>
     * @date 2021/11/1
     **/
    int rollBackStaffEmolumentByStaffId(HrStaffEmolument hrStaffEmolument);

    /**
     * 更新员工薪酬参数
     *
     * @param hrStaffEmolument
     * @return int
     * <AUTHOR>
     * @date 2021/10/21
     **/
    int updateStaffEmolumentByStaffId(HrStaffEmolument hrStaffEmolument);

    /**
     * 根据员工id获取薪酬参数
     *
     * @param staffIds
     * @return java.util.List<cn.casair.domain.HrStaffEmolument>
     * <AUTHOR>
     * @date 2021/10/20
     **/
    List<HrStaffEmolument> getHrStaffEmolumentListByIds(@Param("staffIds") List<String> staffIds);

    /**
     * 根据员工id获取薪酬参数
     *
     * @param staffId
     * @return cn.casair.domain.HrStaffEmolument
     * <AUTHOR>
     * @date 2021/10/19
     **/
    HrStaffEmolument getHrStaffEmolumentByStaffId(String staffId);

    /**
     * 根据staffId更新薪酬参数
     *
     * @param hrEmployeeWelfareDTO
     * @return int
     * <AUTHOR>
     * @date 2021/10/18
     **/
    int updateByStaffId(HrEmployeeWelfareDTO hrEmployeeWelfareDTO);

    /**
     * 根据员工id获取薪酬参数
     *
     * @param staffId
     * @return cn.casair.domain.HrStaffEmolument
     * <AUTHOR>
     * @date 2021/9/30
     **/
    HrStaffEmolument getByStaffId(@Param("staffId") String staffId);

    /**
     * 根据员工id获取薪酬参数
     *
     * @param staffId
     * @return
     */
    HrStaffEmolumentDTO getEmolumentByStaffId(@Param("staffId") String staffId);

    /**
     * 更新员工银行卡信息
     *
     * @param staffId
     * @param ownedBank
     * @param bankNo
     * @return int
     * <AUTHOR>
     * @date 2021/9/29
     **/
    int updateStaffBankInfo(@Param("staffId") String staffId, @Param("ownedBank") int ownedBank, @Param("bankNo") String bankNo);

    /**
     * 员工信息--薪酬参数--社保类型
     *
     * @param staffId 员工ID
     * @return
     */
    List<HrSocialSecurityDTO> findSocialSecurity(String staffId);

    /**
     * 员工信息--薪酬参数--公积金类型
     * @param staffId 员工ID
     * @return
     */
    List<HrAccumulationFundDTO> findAccumulationFund(String staffId);

}
