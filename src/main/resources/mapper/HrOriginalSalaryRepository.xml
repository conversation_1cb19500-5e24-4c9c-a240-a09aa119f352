<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrOriginalSalaryRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , client_id, cost_date, title, salary_url,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrOriginalSalary">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
         <result column="title" property="title"/>
        <result column="salary_url" property="salaryUrl"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Find_SQL">
        SELECT
        ho. * ,
        hc.client_name AS clientName
        FROM
        hr_original_salary ho
        LEFT JOIN hr_client hc on hc.id=ho.client_id
        where ho.is_delete=0
        <if test="param1.title!=null and param1.title!=''">
            and ho.title like concat('%',#{param1.title},'%')
        </if>
        <if test="param1.clientIdList!=null and param1.clientIdList.size() > 0">
            and
            <foreach collection="param1.clientIdList" open="(" separator="or" close=")" item="val">
                ho.client_id like concat('%',#{val},'%')
            </foreach>
        </if>
        <if test="param1.ids!=null and param1.ids.size() > 0">
            and ho.id in
            <foreach collection="param1.ids" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.payYear!=null">
            and ho.pay_year=#{param1.payYear}
        </if>
        <if test="param1.payMonthly!=null">
            and ho.pay_monthly=#{param1.payMonthly}
        </if>
        <if test="param1.createdStartDateStart!=null ">
            AND ho.created_date <![CDATA[ >= ]]> #{param1.createdStartDateStart}
        </if>
        <if test="param1.createdStartDateEnd!=null ">
            AND ho.created_date <![CDATA[ <= ]]> date_add(#{param1.createdStartDateEnd}, interval 1 day)
        </if>
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </sql>

    <select id="selectFiPage" resultType="cn.casair.dto.HrOriginalSalaryDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="selectid" resultType="java.lang.String">
        SELECT t3.id as clientId
        FROM (SELECT t1.*,
                     IF(FIND_IN_SET(parent_id, @pids) > 0, @pids := CONCAT(@pids, ',', id), '0') AS ischild
              FROM (SELECT t.id, t.parent_id, t.client_name
                    FROM hr_client AS t
                    ORDER BY t.id ASC) t1,
                   (SELECT @pids :=#{id}) t2) t3
        WHERE ischild != '0'
    </select>

    <select id="findList" resultType="cn.casair.dto.HrOriginalSalaryDTO">
        <include refid="Find_SQL"/>
    </select>
</mapper>
