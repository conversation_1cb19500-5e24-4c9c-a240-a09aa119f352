package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.casair.common.enums.CompanyInfoEnum;
import cn.casair.common.enums.MessageTemplateEnum;
import cn.casair.common.enums.RecruitmentBrochure;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.DataUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.mapper.HrRecruitmentBulletinMapper;
import cn.casair.mapper.HrRecruitmentStationMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.casair.common.utils.HtmlToPdf.htmlToPdf;

/**
 * 发布公告服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrRecruitmentBulletinServiceImpl extends ServiceImpl<HrRecruitmentBulletinRepository, HrRecruitmentBulletin> implements HrRecruitmentBulletinService {

    private final HrRecruitmentBulletinRepository hrRecruitmentBulletinRepository;
    private final HrRecruitmentBulletinMapper hrRecruitmentBulletinMapper;
    private final HrRecruitmentBrochureRepository hrRecruitmentBrochureRepository;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrRecruitmentStationService hrRecruitmentStationService;
    private final HrRecruitmentStationRepository hrRecruitmentStationRepository;
    private final HrRegistrationDetailsRepository hrRegistrationDetailsRepository;
    private final HrRecruitmentStationMapper hrRecruitmentStationMapper;
    private final HrAppendixService hrAppendixService;
    private final HrRecruitmentExamRoomRepository hrRecruitmentExamRoomRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrSmsTemplateService hrSmsTemplateService;
    private final HrMaterialService hrMaterialService;
    private final HrSealsRepository hrSealsRepository;
    private final HrRecruitmentDraftsService hrRecruitmentDraftsService;
    @Value("${file.temp-path}")
    private String fileTempPath;
    @Value("${mini.appletName}")
    private String appletSite;
    @Value("${constant.fontPath}")
    private String fontPath;

    /**
     * 创建发布公告
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    @Override
    public HrRecruitmentBulletinDTO createHrRecruitmentBulletin(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        this.checkBulletinNameRepeat(hrRecruitmentBulletinDTO);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String announcement = "";
        HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(hrRecruitmentBulletinDTO.getRecruitmentBrochureId());
        List<HrRecruitmentStationDTO> recruitmentStation = hrRecruitmentBulletinDTO.getHrRecruitmentStation();
        for (HrRecruitmentStationDTO hrRecruitmentStationDTO : recruitmentStation) {
            List<HrRegistrationDetails> hrRegistrationDetailsList = hrRegistrationDetailsRepository.selectList(new QueryWrapper<HrRegistrationDetails>()
                .eq("station_id", hrRecruitmentStationDTO.getId()).eq("status", RecruitmentBrochure.RegistrationStatus.TO_BE_REVIEWED.getKey()));
            if (CollectionUtils.isNotEmpty(hrRegistrationDetailsList)) {
                throw new CommonException("该岗位下有考生处于待审核简历的状态，暂不可发布公告。");
            }

            HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRecruitmentStationDTO.getId());
            if (hrRecruitmentBulletinDTO.getNoticeType().equals(RecruitmentBrochure.NoticeType.WRITTEN_ANNOUNCEMENT.getKey())) {
                //先面试后笔试发布笔试公告判断是否发布过面试成绩公告
                if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                    List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(new QueryWrapper<HrRecruitmentBulletin>()
                        .eq("recruitment_brochure_id", hrRecruitmentBulletinDTO.getRecruitmentBrochureId())
                        .eq("recruitment_station_name", hrRecruitmentStation.getRecruitmentStationName())
                        .eq("achievement_type", RecruitmentBrochure.AchievementType.INTERVIEW_ACHIEVEMENT.getKey()));
                    if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                        throw new CommonException("该岗位没有发布面试成绩公告，暂不可发布笔试公告。");
                    }
                }
                BigDecimal bigDecimal = DataUtils.conversionScale(1, hrRecruitmentStationDTO.getWrittenScoreWeight());
                hrRecruitmentStationDTO.setWrittenScoreWeight(bigDecimal);
                announcement = "笔试公告";
                List<Integer> integers = new ArrayList<>();
                integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                this.updateNoticeStationInFor(hrRecruitmentStationDTO, hrRecruitmentBulletinDTO.getRecruitmentBrochureId(), hrRecruitmentBrochure.getRecruitBrochureName(), integers, hrRecruitmentBulletinDTO.getNoticeContent());
            } else {
                //先笔试后面试发布成绩公告判断是否发布过笔试成绩公告
                if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                    List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(new QueryWrapper<HrRecruitmentBulletin>()
                        .eq("recruitment_brochure_id", hrRecruitmentBulletinDTO.getRecruitmentBrochureId())
                        .eq("recruitment_station_name", hrRecruitmentStation.getRecruitmentStationName())
                        .eq("achievement_type", RecruitmentBrochure.AchievementType.WRITTEN_ACHIEVEMENT.getKey()));
                    if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                        throw new CommonException("该岗位没有发布笔试成绩公告，暂不可发布面试公告。");
                    }
                }
                announcement = "面试公告";
                BigDecimal bigDecimal = DataUtils.conversionScale(1, hrRecruitmentStationDTO.getInterviewScoreWeight());
                hrRecruitmentStationDTO.setInterviewScoreWeight(bigDecimal);

                List<Integer> integers = new ArrayList<>();
                integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                this.interviewAnnouncement(hrRecruitmentBrochure, hrRecruitmentStation, hrRecruitmentStationDTO, integers, hrRecruitmentBulletinDTO.getNoticeContent());
            }
            //判断考试形式
            if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())
                || hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                //判断是否第一次发布
                if (hrRecruitmentStation.getIsPublishNotice().equals(RecruitmentBrochure.IsPublishNotice.UNPUBLISHED.getKey())) {
                    hrRecruitmentStationDTO.setIsPublishNotice(RecruitmentBrochure.IsPublishNotice.PUBLISHED.getKey());
                } else {
                    hrRecruitmentStationDTO.setIsPublishNotice(RecruitmentBrochure.IsPublishNotice.ALL_PUBLISHED.getKey());
                }
            } else {
                hrRecruitmentStationDTO.setIsPublishNotice(RecruitmentBrochure.IsPublishNotice.ALL_PUBLISHED.getKey());
            }
            hrRecruitmentStationRepository.updateById(hrRecruitmentStationMapper.toEntity(hrRecruitmentStationDTO));
        }
        log.info("Create new HrRecruitmentBulletin:{}", hrRecruitmentBulletinDTO);
        HrRecruitmentBulletin hrRecruitmentBulletin = this.hrRecruitmentBulletinMapper.toEntity(hrRecruitmentBulletinDTO);
        List<String> collect = recruitmentStation.stream().map(HrRecruitmentStationDTO::getRecruitmentStationName).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            hrRecruitmentBulletin.setRecruitmentStationName(String.join(",", collect));
        }
        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletinDTO.getAppendixIdList())) {
            hrRecruitmentBulletin.setAppendixIds(String.join(",", hrRecruitmentBulletinDTO.getAppendixIdList()));
        }
        this.hrRecruitmentBulletinRepository.insert(hrRecruitmentBulletin);
        HrRecruitmentBulletinDTO toDto = this.hrRecruitmentBulletinMapper.toDto(hrRecruitmentBulletin);
        hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(toDto.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "发布了" + announcement, null, ServiceCenterEnum.RECRUITMENT_ANNOUNCEMENT.getKey());
        return toDto;
    }


    /**
     * 创建其他公告
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    @Override
    public HrRecruitmentBulletinDTO saveHrRecruitmentBulletin(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        log.info("创建其他公告:{}", hrRecruitmentBulletinDTO);
        this.checkBulletinNameRepeat(hrRecruitmentBulletinDTO);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        hrRecruitmentBulletinDTO.setNoticeType(RecruitmentBrochure.NoticeType.OTHER_ANNOUNCEMENT.getKey());
        HrRecruitmentBulletin hrRecruitmentBulletin = this.hrRecruitmentBulletinMapper.toEntity(hrRecruitmentBulletinDTO);
        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletinDTO.getAppendixIdList())) {
            hrRecruitmentBulletin.setAppendixIds(String.join(",", hrRecruitmentBulletinDTO.getAppendixIdList()));
        }
        this.hrRecruitmentBulletinRepository.insert(hrRecruitmentBulletin);
        HrRecruitmentBulletinDTO recruitmentBulletinDTO = this.hrRecruitmentBulletinMapper.toDto(hrRecruitmentBulletin);
        if (StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getNoticeContent())) {
            hrRecruitmentDraftsService.generateHrRecruitmentDrafts(9, hrRecruitmentBulletinDTO.getNoticeContent());
        }
        hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(recruitmentBulletinDTO.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "发布了其他公告", null, ServiceCenterEnum.RECRUITMENT_ANNOUNCEMENT.getKey());
        return recruitmentBulletinDTO;
    }

    /**
     * 检查公告名称是否存在重复数据
     *
     * @param hrRecruitmentBulletinDTO
     */
    private void checkBulletinNameRepeat(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        if (hrRecruitmentBulletinDTO.getRecruitmentBulletinName() == null) {
            throw new CommonException("公告名称为必填项！");
        }
        QueryWrapper<HrRecruitmentBulletin> wrapper = new QueryWrapper<>();
        wrapper.eq("recruitment_brochure_id", hrRecruitmentBulletinDTO.getRecruitmentBrochureId());
        wrapper.eq("recruitment_bulletin_name", hrRecruitmentBulletinDTO.getRecruitmentBulletinName());
        wrapper.ne(StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getId()), "id", hrRecruitmentBulletinDTO.getId());
        Integer count = hrRecruitmentBulletinRepository.selectCount(wrapper);
        if (count > 0) {
            throw new CommonException("公告名称已经存在！");
        }
    }

    /**
     * 发布/调整面试公告
     *
     * @param hrRecruitmentBrochure   招聘简章
     * @param hrRecruitmentStation    招聘岗位
     * @param hrRecruitmentStationDTO 招聘岗位
     * @param integers                报名状态
     * @param noticeContent           公告内容
     */
    private void interviewAnnouncement(HrRecruitmentBrochure hrRecruitmentBrochure, HrRecruitmentStation hrRecruitmentStation, HrRecruitmentStationDTO hrRecruitmentStationDTO, List<Integer> integers, String noticeContent) {
        List<HrRegistrationDetails> hrRegistrationDetails = hrRegistrationDetailsRepository.selectList(new QueryWrapper<HrRegistrationDetails>()
            .eq("brochure_id", hrRecruitmentBrochure.getId())
            .eq("station_id", hrRecruitmentStation.getId())
            .in("status", integers));
        if (CollectionUtils.isNotEmpty(hrRegistrationDetails)) {
            for (HrRegistrationDetails hrRegistrationDetail : hrRegistrationDetails) {
                if (hrRegistrationDetail.getStatus().equals(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey())
                    || hrRegistrationDetail.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey())
                    || hrRegistrationDetail.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey())) {
                    throw new CommonException("该岗位下报名人员中已有人录入了面试成绩，不可再进行调整！");
                }
                hrRegistrationDetail.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE_INTERVIEW_EXAM_RESULTS.getKey());
                hrRegistrationDetailsRepository.updateById(hrRegistrationDetail);

                HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(hrRegistrationDetail.getStaffId());
                if (hrTalentStaff.getPhone() != null && StringUtils.isNotBlank(hrTalentStaff.getPhone())) {
                    HashMap<Integer, String> params = new HashMap<>();
                    params.put(1, hrTalentStaff.getName());
                    params.put(2, hrRecruitmentBrochure.getRecruitBrochureName());
                    params.put(3, hrRecruitmentStation.getRecruitmentStationName());
                    // LocalDateTime examTime = hrRecruitmentStationDTO.getInterviewExamStartTime();
                    // String dataExamTime = examTime.getYear() + "年" + examTime.getMonthValue() + "月" + examTime.getDayOfMonth() + "日" + examTime.getHour() + "时";
                    // params.put(4, dataExamTime);
                    // params.put(5, hrRecruitmentStationDTO.getInterviewLocation());
                    params.put(4, CompanyInfoEnum.FIRST_PART_PHONE_FOUR.getValue());
                    this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.INTERVIEW_NOTICE.getTemplateCode(), hrTalentStaff.getPhone());
                }
            }
        }
        //生成面试公告的草稿箱
        if (StringUtils.isNotBlank(noticeContent)) {
            hrRecruitmentDraftsService.generateHrRecruitmentDrafts(RecruitmentBrochure.DraftType.INTERVIEW_ANNOUNCEMENT.getKey(), noticeContent);
        }
    }

    /**
     * 公告发布--更新岗位信息
     *
     * @param hrRecruitmentStationDTO 岗位信息
     * @param recruitmentBrochureId   招聘简章Id
     * @param recruitmentBrochureName 招聘简章名称
     * @param integers                报名状态
     * @param noticeContent           公告内容
     */
    private void updateNoticeStationInFor(HrRecruitmentStationDTO hrRecruitmentStationDTO, String recruitmentBrochureId, String recruitmentBrochureName, List<Integer> integers, String noticeContent) {
        HrSeals hrSeals = hrSealsRepository.getEnterpriseOfficialSeal();
        List<HrRecruitmentExamRoom> hrRecruitmentExamRoomList = hrRecruitmentStationDTO.getHrRecruitmentExamRoomList();
        //需要校验多个考场容纳的总人数是否大于缴费人数
        int sum = hrRecruitmentExamRoomList.stream().mapToInt(HrRecruitmentExamRoom::getContainNum).sum();
        List<HrRegistrationDetails> hrRegistrationDetails = hrRegistrationDetailsRepository.selectList(new QueryWrapper<HrRegistrationDetails>()
            .eq("brochure_id", recruitmentBrochureId).eq("station_id", hrRecruitmentStationDTO.getId())
            .in("status", integers).orderByAsc("created_date"));
        if (hrRegistrationDetails.size() > sum) {
            throw new CommonException("考场容纳人数少于需要笔试的人数，请重新编辑");
        }
        Integer fromIndex = 0;
        for (HrRecruitmentExamRoom hrRecruitmentExamRoom : hrRecruitmentExamRoomList) {
            hrRecruitmentExamRoom.setRecruitmentStationId(hrRecruitmentStationDTO.getId());
            hrRecruitmentExamRoomRepository.insert(hrRecruitmentExamRoom);
            if (CollectionUtils.isNotEmpty(hrRegistrationDetails)) {
                Integer containNum = hrRecruitmentExamRoom.getContainNum();
                int toIndex = fromIndex + containNum;
                //判断是否需要停止循环
                if (fromIndex > hrRegistrationDetails.size()) {
                    log.info("报名人数已经分配完考场---" + toIndex + "---" + hrRegistrationDetails.size());
                    continue;
                }
                List<HrRegistrationDetails> hrRegistrationDetailsList = hrRegistrationDetails.subList(fromIndex, Math.min(toIndex, hrRegistrationDetails.size()));
                fromIndex += containNum;
                Integer num = 0;
                for (HrRegistrationDetails registrationDetails : hrRegistrationDetailsList) {
                    if (registrationDetails.getStatus().equals(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey())
                        || registrationDetails.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey())
                        || registrationDetails.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey())
                        || registrationDetails.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey())) {
                        throw new CommonException("该岗位下报名人员中已有人录入了笔试成绩，不可再进行调整！");
                    }
                    num += 1;
                    //生成准考证信息
                    this.updateHrRecruitmentDetails(registrationDetails, hrRecruitmentExamRoom, hrRecruitmentStationDTO, num, recruitmentBrochureName, hrSeals.getSealUrl());
                    //更新状态为待录入笔试成绩
                    registrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE_WRITTEN_EXAM_RESULTS.getKey());
                    hrRegistrationDetailsRepository.updateById(registrationDetails);
                }
            }
        }
        //生成考试须知的草稿箱
        hrRecruitmentDraftsService.generateHrRecruitmentDrafts(RecruitmentBrochure.DraftType.EXAM_NOTICE.getKey(), hrRecruitmentStationDTO.getExamNotice());
        //生成笔试公告的草稿箱
        if (StringUtils.isNotBlank(noticeContent)) {
            hrRecruitmentDraftsService.generateHrRecruitmentDrafts(RecruitmentBrochure.DraftType.WRITTEN_ANNOUNCEMENT.getKey(), noticeContent);
        }
    }

    /**
     * 准考证信息
     *
     * @param registrationDetails     报名信息
     * @param hrRecruitmentExamRoom   考场信息
     * @param hrRecruitmentStationDTO 岗位信息
     * @param num
     * @param recruitmentBrochureName 招聘简章名称
     */
    private void updateHrRecruitmentDetails(HrRegistrationDetails registrationDetails, HrRecruitmentExamRoom hrRecruitmentExamRoom, HrRecruitmentStationDTO hrRecruitmentStationDTO, Integer num, String recruitmentBrochureName, String sealUrl) {
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(registrationDetails.getStaffId());
        Map<String, Object> templateMap = new HashMap<>();
        templateMap.put("name", hrTalentStaff.getName() == null ? "" : hrTalentStaff.getName());
        templateMap.put("sex", hrTalentStaff.getSex() == 1 ? "男" : "女");
        templateMap.put("idNumber", hrTalentStaff.getCertificateNum() == null ? "" : hrTalentStaff.getCertificateNum());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        templateMap.put("admissionNumber", formatter.format(hrRecruitmentStationDTO.getWrittenExamStartTime()) + hrRecruitmentExamRoom.getExamRoomName() + String.format("%03d", num));
        templateMap.put("examRoom", hrRecruitmentExamRoom.getExamRoomName() == null ? "" : hrRecruitmentExamRoom.getExamRoomName());
        templateMap.put("postRegistration", hrRecruitmentStationDTO.getRecruitmentStationName() == null ? "" : hrRecruitmentStationDTO.getRecruitmentStationName());
        templateMap.put("examRoomPlace", hrRecruitmentExamRoom.getExamRoomPlace() == null ? "" : hrRecruitmentExamRoom.getExamRoomPlace());
        LocalDateTime examTime = hrRecruitmentStationDTO.getWrittenExamStartTime();
        LocalDateTime endTime = hrRecruitmentStationDTO.getWrittenExamEndTime();
        // 准考证考试时间拼接考试结束时间 2021年11月22日9时30分至11时30分
        String dataExamTime = examTime.getYear() + "年" + examTime.getMonthValue() + "月" + examTime.getDayOfMonth() + "日" + examTime.getHour() + "时"
            + examTime.getMinute() + "分" + "至" + endTime.getHour() + "时" + endTime.getMinute() + "分";
        templateMap.put("examTime", dataExamTime);
        templateMap.put("examNotice", hrRecruitmentStationDTO.getExamNotice() == null ? "" : hrRecruitmentStationDTO.getExamNotice());
        //获取准考证模板
        String tempPdfPath = htmlToPdf(fileTempPath, templateMap, fontPath, sealUrl);

        if (StringUtils.isBlank(tempPdfPath)) {
            throw new CommonException("PDF文件制作异常！");
        }
        // 上传到文件服务器
        HrAppendix hrAppendix = this.hrAppendixService.uploadTempPdf(tempPdfPath);
        if (hrAppendix != null) {
            registrationDetails.setAdmissionTicketUrl(hrAppendix.getFileUrl());
        }
        registrationDetails.setAdmissionTicketInfo(JSON.toJSONString(templateMap));
        hrRegistrationDetailsRepository.updateById(registrationDetails);
        // 短信通知
        if (hrTalentStaff.getPhone() != null && StringUtils.isNotBlank(hrTalentStaff.getPhone())) {
            HashMap<Integer, String> params = new HashMap<>();
            params.put(1, hrTalentStaff.getName());
            params.put(2, recruitmentBrochureName);
            params.put(3, hrRecruitmentStationDTO.getRecruitmentStationName());
            // params.put(4, dataExamTime);
            // params.put(5, hrRecruitmentExamRoom.getExamRoomPlace());
            // params.put(6, appletSite);//todo 小程序跳转地址
            params.put(4, CompanyInfoEnum.FIRST_PART_PHONE_FOUR.getValue());
            this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.WRITTEN_EXAM_NOTICE.getTemplateCode(), hrTalentStaff.getPhone());
        }
    }

    /**
     * 修改发布公告
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    @Override
    public Optional<HrRecruitmentBulletinDTO> updateHrRecruitmentBulletin(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        return Optional.ofNullable(this.hrRecruitmentBulletinRepository.selectById(hrRecruitmentBulletinDTO.getId()))
            .map(roleTemp -> {
                this.checkBulletinNameRepeat(hrRecruitmentBulletinDTO);
                String announcement = "";
                HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(hrRecruitmentBulletinDTO.getRecruitmentBrochureId());
                for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentBulletinDTO.getHrRecruitmentStation()) {
                    HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRecruitmentStationDTO.getId());
                    //查询对应的
                    if (roleTemp.getNoticeType().equals(RecruitmentBrochure.NoticeType.WRITTEN_ANNOUNCEMENT.getKey())) {
                        if (hrRecruitmentStation.getWrittenExamStartTime().isAfter(LocalDateTime.now())) {
                            BigDecimal bigDecimal = DataUtils.conversionScale(1, hrRecruitmentStationDTO.getWrittenScoreWeight());
                            hrRecruitmentStationDTO.setWrittenScoreWeight(bigDecimal);
                            announcement = "笔试公告";
                            hrRecruitmentExamRoomRepository.deleteRecruitmentExamRoom(hrRecruitmentStationDTO.getId());
                            List<Integer> integers = new ArrayList<>();
                            integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE_WRITTEN_EXAM_RESULTS.getKey());
                            integers.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                            integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                            if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())
                                || hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                                integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                            } else if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                                integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                            }
                            this.updateNoticeStationInFor(hrRecruitmentStationDTO, hrRecruitmentBrochure.getId(), hrRecruitmentBrochure.getRecruitBrochureName(), integers, hrRecruitmentBulletinDTO.getNoticeContent());
                        } else {
                            throw new CommonException("笔试公告调整需要在笔试开始时间之前！");
                        }
                    } else {
                        if (hrRecruitmentStation.getInterviewExamStartTime().isAfter(LocalDateTime.now())) {
                            announcement = "面试公告";
                            BigDecimal bigDecimal = DataUtils.conversionScale(1, hrRecruitmentStationDTO.getInterviewScoreWeight());
                            hrRecruitmentStationDTO.setInterviewScoreWeight(bigDecimal);
                            List<Integer> integers = new ArrayList<>();
                            integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                            integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INTERVIEW_EXAM_RESULTS.getKey());
                            integers.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                            if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())
                                || hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                                integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                            } else if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                                integers.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                            }
                            this.interviewAnnouncement(hrRecruitmentBrochure, hrRecruitmentStation, hrRecruitmentStationDTO, integers, hrRecruitmentBulletinDTO.getNoticeContent());
                        } else {
                            throw new CommonException("面试公告调整需要在面试开始时间之前！");
                        }
                    }
                    hrRecruitmentStationRepository.updateById(hrRecruitmentStationMapper.toEntity(hrRecruitmentStationDTO));
                    hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(roleTemp.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "调整了" + roleTemp.getRecruitmentStationName() + "的" + announcement, null, ServiceCenterEnum.RECRUITMENT_ANNOUNCEMENT.getKey());
                }
                log.info("Update HrRecruitmentBulletin:{}", hrRecruitmentBulletinDTO);
                return hrRecruitmentBulletinDTO;
            });
    }

    /**
     * 查询发布公告详情
     *
     * @param id
     * @return
     */
    @Override
    public HrRecruitmentBulletinDTO getHrRecruitmentBulletin(String id) {
        log.info("Get HrRecruitmentBulletin :{}", id);

        HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO = this.hrRecruitmentBulletinRepository.findById(id);
        this.setDict(hrRecruitmentBulletinDTO);
        if (!hrRecruitmentBulletinDTO.getNoticeType().equals(RecruitmentBrochure.NoticeType.OTHER_ANNOUNCEMENT.getKey())) {
            //查询该对应的岗位信息
            List<String> stationNameList = Arrays.asList(hrRecruitmentBulletinDTO.getRecruitmentStationName().split(","));
            List<HrRecruitmentStation> hrRecruitmentStations = hrRecruitmentStationRepository.selectList(new QueryWrapper<HrRecruitmentStation>()
                .eq("service_id", hrRecruitmentBulletinDTO.getRecruitmentBrochureId()).in("recruitment_station_name", stationNameList));
            List<HrRecruitmentStationDTO> hrRecruitmentStationDTOList = hrRecruitmentStationMapper.toDto(hrRecruitmentStations);
            for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentStationDTOList) {
                hrRecruitmentStationDTO.setExamFormatName(RecruitmentBrochure.ExamFormat.getValueByKey(hrRecruitmentStationDTO.getExamFormat()));
                if (hrRecruitmentStationDTO.getInterviewScoreWeight() != null) {
                    hrRecruitmentStationDTO.setInterviewScoreWeight(DataUtils.conversionScale(0, hrRecruitmentStationDTO.getInterviewScoreWeight()));
                }
                if (hrRecruitmentStationDTO.getWrittenScoreWeight() != null) {
                    hrRecruitmentStationDTO.setWrittenScoreWeight(DataUtils.conversionScale(0, hrRecruitmentStationDTO.getWrittenScoreWeight()));
                }
                if (hrRecruitmentBulletinDTO.getNoticeType().equals(RecruitmentBrochure.NoticeType.WRITTEN_ANNOUNCEMENT.getKey())) {
                    List<HrRecruitmentExamRoom> hrRecruitmentExamRoomList = hrRecruitmentExamRoomRepository.selectList(new QueryWrapper<HrRecruitmentExamRoom>().eq("recruitment_station_id", hrRecruitmentStationDTO.getId()));
                    hrRecruitmentStationDTO.setHrRecruitmentExamRoomList(hrRecruitmentExamRoomList);
                }
            }
            hrRecruitmentBulletinDTO.setHrRecruitmentStation(hrRecruitmentStationDTOList);
        }
        //返回操作信息
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(id, null);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)) {
            hrRecruitmentBulletinDTO.setApplyOpLogsList(applyOpLogsList);
        }
        return hrRecruitmentBulletinDTO;
    }

    /**
     * 查询发布公告
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    @Override
    public List<HrRecruitmentBulletinDTO> findPage(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        QueryWrapper<HrRecruitmentBulletin> wrapper = new QueryWrapper<>();
        wrapper.eq("recruitment_brochure_id", hrRecruitmentBulletinDTO.getRecruitmentBrochureId());
        wrapper.in(CollectionUtils.isNotEmpty(hrRecruitmentBulletinDTO.getNoticeTypeQuery()), "notice_type", hrRecruitmentBulletinDTO.getNoticeTypeQuery());
        wrapper.in(CollectionUtils.isNotEmpty(hrRecruitmentBulletinDTO.getIds()), "id", hrRecruitmentBulletinDTO.getIds());
        wrapper.like(StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getRecruitmentStationName()), "recruitment_station_name", hrRecruitmentBulletinDTO.getRecruitmentStationName());
        wrapper.like(StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getRecruitmentBulletinName()), "recruitment_bulletin_name", hrRecruitmentBulletinDTO.getRecruitmentBulletinName());
        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletinDTO.getCreatedDateQuery())) {
            wrapper.ge(ObjectUtils.isNotNull(hrRecruitmentBulletinDTO.getCreatedDateStart()), "created_date", hrRecruitmentBulletinDTO.getCreatedDateStart());
            wrapper.lt(ObjectUtils.isNotNull(hrRecruitmentBulletinDTO.getCreatedDateEnd()), "created_date", hrRecruitmentBulletinDTO.getCreatedDateEnd().plusDays(1));
        }
        //排序
        if (StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getOrder())) {
            if (hrRecruitmentBulletinDTO.getOrder().equals("DESC")) {
                wrapper.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getField()), false, hrRecruitmentBulletinDTO.getField());
            } else {
                wrapper.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getField()), true, hrRecruitmentBulletinDTO.getField());
            }
        } else {
            wrapper.orderByDesc("created_date");
        }
        List<HrRecruitmentBulletin> hrRecruitmentBulletins = this.hrRecruitmentBulletinRepository.selectList(wrapper);
        List<HrRecruitmentBulletinDTO> hrRecruitmentBulletinDTOList = hrRecruitmentBulletinMapper.toDto(hrRecruitmentBulletins);
        hrRecruitmentBulletinDTOList.forEach(this::setDict);
        return hrRecruitmentBulletinDTOList;
    }

    private void setDict(HrRecruitmentBulletinDTO recruitmentBulletinDTO) {
        if (recruitmentBulletinDTO.getNoticeType() != null) {
            recruitmentBulletinDTO.setNoticeTypeLabel(RecruitmentBrochure.NoticeType.getValueByKey(recruitmentBulletinDTO.getNoticeType()));
        }
        if (recruitmentBulletinDTO.getAchievementType() != null) {
            recruitmentBulletinDTO.setAchievementTypeLabel(RecruitmentBrochure.AchievementType.getValueByKey(recruitmentBulletinDTO.getAchievementType()));
        }
        if (StringUtils.isNotBlank(recruitmentBulletinDTO.getAppendixIds())) {
            List<String> appendixIds = Arrays.asList(recruitmentBulletinDTO.getAppendixIds().split(","));
            List<HrAppendixDTO> hrAppendixDTOList = hrAppendixService.getHrAppendixListByIds(appendixIds);
            recruitmentBulletinDTO.setHrAppendixDTOList(hrAppendixDTOList);
        }

    }

    /**
     * 公告发布--查询对应的岗位
     *
     * @param recruitmentBrochureId 招聘简章ID
     * @param noticeType            公告类型
     * @return
     */
    @Override
    public HrRecruitmentBrochureDTO matchingRecruitmentStations(String recruitmentBrochureId, Integer noticeType) {
        HrRecruitmentBrochureDTO recruitmentBrochureDTO = hrRecruitmentBrochureRepository.findRecruitmentBrochureById(recruitmentBrochureId);
        //点击“公告发布”时将校验缴费日期是否已结束
        if (recruitmentBrochureDTO.getPaymentEndDate() != null) {
            if (recruitmentBrochureDTO.getPaymentEndDate().isAfter(LocalDate.now())) {
                throw new CommonException("报名缴费日期还未结束，暂不可发布公告。");
            }
        }
        //查询招聘简章中对应的岗位数据
        List<HrRecruitmentStationDTO> hrRecruitmentStationDTOS = hrRecruitmentStationService.listByServiceId(recruitmentBrochureId);
        List<HrRecruitmentStationDTO> hrRecruitmentStationDTOList = new ArrayList<>();
        for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentStationDTOS) {
            if (noticeType.equals(RecruitmentBrochure.NoticeType.WRITTEN_ANNOUNCEMENT.getKey())) {//笔试公告
                switch (hrRecruitmentStationDTO.getExamFormat()) {
                    case 1://笔试
                        if (hrRecruitmentStationDTO.getIsPublishNotice().equals(RecruitmentBrochure.IsPublishNotice.UNPUBLISHED.getKey())) {
                            hrRecruitmentStationDTOList.add(hrRecruitmentStationDTO);
                        }
                        break;
                    default://先笔试后面试、先面试后笔试
                        if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                            if (hrRecruitmentStationDTO.getIsPublishNotice().equals(RecruitmentBrochure.IsPublishNotice.UNPUBLISHED.getKey())) {
                                hrRecruitmentStationDTOList.add(hrRecruitmentStationDTO);
                            }
                        }
                        if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                            if (hrRecruitmentStationDTO.getIsPublishNotice().equals(RecruitmentBrochure.IsPublishNotice.PUBLISHED.getKey())) {
                                hrRecruitmentStationDTOList.add(hrRecruitmentStationDTO);
                            }
                        }
                        break;
                }
            } else {//面试公告
                switch (hrRecruitmentStationDTO.getExamFormat()) {
                    case 1:
                        break;
                    case 2://面试
                        if (hrRecruitmentStationDTO.getIsPublishNotice().equals(RecruitmentBrochure.IsPublishNotice.UNPUBLISHED.getKey())) {
                            hrRecruitmentStationDTOList.add(hrRecruitmentStationDTO);
                        }
                        break;
                    default://先笔试后面试、先面试后笔试
                        if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                            if (hrRecruitmentStationDTO.getIsPublishNotice().equals(RecruitmentBrochure.IsPublishNotice.PUBLISHED.getKey())) {
                                hrRecruitmentStationDTOList.add(hrRecruitmentStationDTO);
                            }
                        }
                        if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                            if (hrRecruitmentStationDTO.getIsPublishNotice().equals(RecruitmentBrochure.IsPublishNotice.UNPUBLISHED.getKey())) {
                                hrRecruitmentStationDTOList.add(hrRecruitmentStationDTO);
                            }
                        }
                        break;
                }
            }
        }
        recruitmentBrochureDTO.setHrRecruitmentStation(hrRecruitmentStationDTOList);
        return recruitmentBrochureDTO;
    }

    /**
     * 考场名称是否重复
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    @Override
    public ResponseEntity examRoomQueryData(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        List<HrRecruitmentExamRoom> hrRecruitmentExamRooms = hrRecruitmentExamRoomRepository.selectList(new QueryWrapper<HrRecruitmentExamRoom>()
            .eq("recruitment_station_id", hrRecruitmentBulletinDTO.getRecruitmentStationId())
            .eq("exam_room_name", hrRecruitmentBulletinDTO.getExamRoomName()));
        if (CollectionUtils.isNotEmpty(hrRecruitmentExamRooms)) {
            return ResponseUtil.buildSuccess("考场名称要求唯一不重复！");
        }
        return ResponseUtil.buildSuccess();
    }

    /**
     * 获取对应的考场信息
     *
     * @param hrRecruitmentStationDTO 招聘岗位
     * @return
     */
    private boolean examRoomInFor(HrRecruitmentStationDTO hrRecruitmentStationDTO) {
        boolean flag = false;
        List<HrRecruitmentExamRoom> hrRecruitmentExamRooms = hrRecruitmentExamRoomRepository.selectList(new QueryWrapper<HrRecruitmentExamRoom>().eq("recruitment_station_id", hrRecruitmentStationDTO.getId()));
        List<HrRecruitmentExamRoom> hrRecruitmentExamRoomList = hrRecruitmentStationDTO.getHrRecruitmentExamRoomList();
        if (hrRecruitmentExamRooms.size() == hrRecruitmentExamRoomList.size()) {
            for (HrRecruitmentExamRoom hrRecruitmentExamRoom : hrRecruitmentExamRoomList) {
                boolean anyMatch = hrRecruitmentExamRooms.stream().anyMatch(ls -> hrRecruitmentExamRoom.getContainNum().equals(ls.getContainNum()));
                if (!anyMatch) {
                    flag = true;
                    break;
                }
            }
        } else {
            flag = true;
        }
        if (flag) {
            //先删除之前的考场信息
            hrRecruitmentExamRoomRepository.deleteRecruitmentExamRoom(hrRecruitmentStationDTO.getId());
        }
        return flag;
    }

    /**
     * 返回相应的成绩单附件
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    @Override
    public List<HrAppendix> returnResultsAppendix(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        List<String> fileList = new ArrayList<>();
        List<String> stationNameList = new ArrayList<>();
        HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(hrRecruitmentBulletinDTO.getRecruitmentBrochureId());
        for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentBulletinDTO.getHrRecruitmentStation()) {
            HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRecruitmentStationDTO.getId());
            //判断之前是否已经发布过此公告类型
            List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(new QueryWrapper<HrRecruitmentBulletin>()
                .eq("recruitment_brochure_id", hrRecruitmentStation.getServiceId())
                .like("recruitment_station_name", hrRecruitmentStation.getRecruitmentStationName())
                .eq("achievement_type", hrRecruitmentBulletinDTO.getAchievementType())
                .eq("is_delete", 0));
            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins)) {
                stationNameList.add(hrRecruitmentStation.getRecruitmentStationName());
                continue;
            }
            //获取这个招聘岗位的招聘人数
            BigDecimal recruitmentPeopleNumber = new BigDecimal(String.valueOf(hrRecruitmentStation.getRecruitmentPeopleNumber()));
            BigDecimal ratio = new BigDecimal(0);
            //获取对应的比例
            List<Integer> statusList = new ArrayList<>();
            if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())) {
                ratio = hrRecruitmentStation.getInvestigationRatio();//考察比例
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
            } else if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())) {
                ratio = hrRecruitmentStation.getInvestigationRatio();//考察比例
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
            }
            switch (hrRecruitmentBulletinDTO.getAchievementType()) {
                case 1://笔试成绩
                    //取对应的报名人数
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                        ratio = hrRecruitmentStation.getInvestigationRatio();//考察比例
                        statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());

                    } else if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                        ratio = hrRecruitmentStation.getPromotedRatio();//晋升比例
                        statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INTERVIEW_SCOPE.getKey());
                    }
                    break;
                case 2://面试成绩
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                        ratio = hrRecruitmentStation.getPromotedRatio();//晋升比例
                        statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.NO_WRITTEN_EXAM_SCOPE.getKey());
                    } else if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                        ratio = hrRecruitmentStation.getInvestigationRatio();//考察比例
                        statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                        statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
                    }
                    break;
                case 3://最终成绩
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
                    break;
                case 4://考察结果
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_PHYSICAL.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INSPECTION_RESULTS.getKey());
                    break;
                case 5://体检结果
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_FORMULA_EMPLOYED.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.FAILED_PHYSICAL_EXAM.getKey());
                    break;
            }
            if (ratio == null) {
                continue;
            }
            Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(ratio));
            List<HrRegistrationDetailsDTO> hrRegistrationDetailsDTOS = hrRegistrationDetailsRepository.findRegistrationDetails(hrRecruitmentBulletinDTO.getRecruitmentBrochureId(), hrRecruitmentStationDTO.getId(), statusList);
            List<HrRegistrationDetailsDTO> mergerList = mergerList(hrRegistrationDetailsDTOS, hrRecruitmentBulletinDTO.getAchievementType());//根据分数倒序排序
            if (CollectionUtils.isNotEmpty(mergerList)) {
                List<ExcelExportEntity> excelHeader = new ArrayList<>();
                // 数据
                List<Map<String, Object>> paramsList = new ArrayList<>();
                mergerList.forEach(ls -> {
                    this.detailsExportHeader(ls, excelHeader, paramsList, number, hrRecruitmentStation, hrRecruitmentBulletinDTO);
                });
                // 去重
                List<ExcelExportEntity> collect = excelHeader.stream().distinct().collect(Collectors.toList());
                String achievementTypeLabel = RecruitmentBrochure.AchievementType.getValueByKey(hrRecruitmentBulletinDTO.getAchievementType());
                fileList.add(Objects.requireNonNull(ExcelUtils.dynamicColumnExportLocal(hrRecruitmentStation.getRecruitmentStationName() + achievementTypeLabel + "_成绩单", hrRecruitmentBrochure.getRecruitBrochureName(), collect, paramsList, fileTempPath)));
            }
        }
        if (CollectionUtils.isNotEmpty(stationNameList)) {
            throw new CommonException(String.join(",", stationNameList) + "已经发布此成绩类型公告，不可重复！");
        }
        if (CollectionUtils.isNotEmpty(fileList)) {
            List<HrAppendix> list = new ArrayList<>();
            for (String file : fileList) {
                list.add(hrAppendixService.uploadImportFile(file));
            }
            return list;
        }
        return null;
    }

    /**
     * 获取报名合格人数
     *
     * @param list            所有人数
     * @param achievementType
     * @return 合适人
     */
    @Override
    public List<HrRegistrationDetailsDTO> mergerList(List<HrRegistrationDetailsDTO> list, Integer achievementType) {
        //list降序排序
        List<HrRegistrationDetailsDTO> collect = new ArrayList<>();
        switch (achievementType) {
            case 1:
                collect = list.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getScore)).collect(Collectors.toList());
                break;
            case 2:
                collect = list.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getInterviewScoreResult)).collect(Collectors.toList());
                break;
            case 3:
                collect = list.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getFinalResult)).collect(Collectors.toList());
                break;
            default:
                collect = list;
                break;
        }
        //翻转list
        Collections.reverse(collect);
        Integer index = 1;
        BigDecimal maxScore = null;
        for (int i = 0; i < list.size(); i++) {
            BigDecimal score = new BigDecimal("0");
            switch (achievementType) {
                case 1:
                    score = collect.get(i).getScore();
                    break;
                case 2:
                    score = collect.get(i).getInterviewScoreResult();
                    break;
                case 3:
                    score = collect.get(i).getFinalResult();
                    break;
            }
            if (i == 0) {
                collect.get(i).setRank(index);
                maxScore = score;
            } else if (Objects.equals(score, maxScore)) {
                collect.get(i).setRank(index);
            } else {
                index++;
                collect.get(i).setRank(index);
                maxScore = score;
            }
        }
        return collect;
    }

    private void detailsExportHeader(HrRegistrationDetailsDTO dto, List<ExcelExportEntity> excelHeader, List<Map<String, Object>> paramsList, Integer number, HrRecruitmentStation hrRecruitmentStationDTO, HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        Map<String, Object> param = new HashMap<>();
        Map<String, Object> map = JSON.parseObject(dto.getAdmissionTicketInfo(), HashMap.class);

        excelHeader.add(new ExcelExportEntity("姓名", "name", 20));
        param.put("name", dto.getName());
        excelHeader.add(new ExcelExportEntity("身份证号", "certificateNum", 20));
        String certificateNum = dto.getCertificateNum();
        StringBuilder sb = new StringBuilder(certificateNum);
        sb.replace(6, 14, "********");
        certificateNum = sb.toString();
        param.put("certificateNum", certificateNum);
        if (hrRecruitmentBulletinDTO.getAchievementType().equals(RecruitmentBrochure.AchievementType.WRITTEN_ACHIEVEMENT.getKey())) {
            if (map != null) {
                excelHeader.add(new ExcelExportEntity("考场", "examRoom", 25));
                param.put("examRoom", map.get("examRoom"));
                excelHeader.add(new ExcelExportEntity("准考证号", "admissionNumber", 25));
                param.put("admissionNumber", map.get("admissionNumber"));
            }
        }
        if (map != null) {
            excelHeader.add(new ExcelExportEntity("岗位", "postRegistration", 20));
            param.put("postRegistration", map.get("postRegistration"));
        }
        excelHeader.add(new ExcelExportEntity("面试顺序号", "number", 20));
        param.put("number", dto.getNumber());
        switch (hrRecruitmentBulletinDTO.getAchievementType()) {
            case 1://笔试成绩
                //取对应的报名人数
                excelHeader.add(new ExcelExportEntity("笔试成绩", "score"));
                if (dto.getScore() != null) {
                    param.put("score", dto.getScore().setScale(2, RoundingMode.HALF_UP));
                }
                if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())
                    || hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                    excelHeader.add(new ExcelExportEntity("是否进入考察范围", "isInvestigation", 20));
                    if (dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey())
                        || dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey())) {
                        param.put("isInvestigation", "是");
                    }
                    excelHeader.add(new ExcelExportEntity("是否等额考察", "isEqualInvestigate", 20));
                    param.put("isEqualInvestigate", dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey()) ? "是" : "");
                } else if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                    excelHeader.add(new ExcelExportEntity("是否进入面试", "isInterview", 20));
                    param.put("isInterview", dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey()) ? "是" : "");
                }
                break;
            case 2://面试成绩
                excelHeader.add(new ExcelExportEntity("面试成绩", "interviewScoreResult"));
                if (dto.getInterviewScoreResult() != null) {
                    param.put("interviewScoreResult", dto.getInterviewScoreResult().setScale(2, RoundingMode.HALF_UP));
                }
                if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())
                    || hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                    excelHeader.add(new ExcelExportEntity("是否进入考察范围", "isInvestigation", 20));
                    if (dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey())
                        || dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey())) {
                        param.put("isInvestigation", "是");
                    }
                    excelHeader.add(new ExcelExportEntity("是否等额考察", "isEqualInvestigate", 20));
                    param.put("isEqualInvestigate", dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey()) ? "是" : "");
                } else if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                    excelHeader.add(new ExcelExportEntity("是否进入笔试", "isWritten", 20));
                    param.put("isWritten", dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey()) ? "是" : "");
                }
                break;
            case 3://最终成绩
                excelHeader.add(new ExcelExportEntity("最终成绩", "finalResult", 20));
                if (dto.getFinalResult() != null) {
                    param.put("finalResult", dto.getFinalResult().setScale(2, RoundingMode.HALF_UP));
                }
                excelHeader.add(new ExcelExportEntity("是否进入考察范围", "isInvestigation", 20));
                if (dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey())
                    || dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey())) {
                    param.put("isInvestigation", "是");
                }
                excelHeader.add(new ExcelExportEntity("是否等额考察", "isEqualInvestigate", 20));
                param.put("isEqualInvestigate", dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey()) ? "是" : "");
                break;
            case 4://考察结果
                excelHeader.add(new ExcelExportEntity("考察是否合格", "examResult", 20));
                excelHeader.add(new ExcelExportEntity("是否进行体检", "isPhysicalExam", 20));
                if (dto.getExamResult() != null) {
                    param.put("examResult", dto.getExamResult().equals(0) ? "" : "是");
                    param.put("isPhysicalExam", dto.getExamResult().equals(0) ? "" : "是");
                }
                break;
            case 5://体检结果
                excelHeader.add(new ExcelExportEntity("体检是否合格", "physicalExaminationResult", 20));
                excelHeader.add(new ExcelExportEntity("是否拟聘用", "isProposedEmployment", 20));
                if (dto.getPhysicalExaminationResult() != null) {
                    param.put("physicalExaminationResult", dto.getPhysicalExaminationResult().equals(0) ? "" : "是");
                    param.put("isProposedEmployment", dto.getPhysicalExaminationResult().equals(0) ? "" : "是");
                }
                break;
        }
        paramsList.add(param);
    }

    /**
     * 发布成绩公告
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    @Override
    public ResponseEntity<?> publishResultsAnnouncement(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        this.checkBulletinNameRepeat(hrRecruitmentBulletinDTO);
        List<String> stationNameList = new ArrayList<>();
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrRecruitmentBulletin hrRecruitmentBulletin = hrRecruitmentBulletinMapper.toEntity(hrRecruitmentBulletinDTO);
        List<String> stationName = hrRecruitmentBulletinDTO.getHrRecruitmentStation().stream().map(HrRecruitmentStationDTO::getRecruitmentStationName).collect(Collectors.toList());
        hrRecruitmentBulletin.setRecruitmentStationName(String.join(",", stationName));
        hrRecruitmentBulletin.setNoticeType(RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
        hrRecruitmentBulletin.setRecruitmentBrochureId(hrRecruitmentBulletinDTO.getHrRecruitmentStation().get(0).getServiceId());
        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletinDTO.getAppendixIdList())) {
            hrRecruitmentBulletin.setAppendixIds(String.join(",", hrRecruitmentBulletinDTO.getAppendixIdList()));
        }
        hrRecruitmentBulletinRepository.insert(hrRecruitmentBulletin);
        HrRecruitmentBulletinDTO toDto = this.hrRecruitmentBulletinMapper.toDto(hrRecruitmentBulletin);
        List<HrRecruitmentStationDTO> hrRecruitmentStation = hrRecruitmentBulletinDTO.getHrRecruitmentStation();
        for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentStation) {
            //每一个结点加限制
            this.nodePlusRestriction(hrRecruitmentBulletinDTO, hrRecruitmentStationDTO);

            if (toDto.getAchievementType().equals(RecruitmentBrochure.AchievementType.WRITTEN_ACHIEVEMENT.getKey())
                || toDto.getAchievementType().equals(RecruitmentBrochure.AchievementType.INTERVIEW_ACHIEVEMENT.getKey())) {
                if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())
                    || hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())) {
                    //通知
                    this.noticeInfo(hrRecruitmentStationDTO, hrRecruitmentBulletinDTO, stationNameList);
                }
            }
            if (toDto.getAchievementType().equals(RecruitmentBrochure.AchievementType.FINAL_ACHIEVEMENT.getKey())) {
                if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())
                    || hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                    //通知
                    this.noticeInfo(hrRecruitmentStationDTO, hrRecruitmentBulletinDTO, stationNameList);
                }
            }
        }
        //生成面试公告的草稿箱
        if (StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getNoticeContent())) {
            hrRecruitmentDraftsService.generateHrRecruitmentDrafts(toDto.getAchievementType(), hrRecruitmentBulletinDTO.getNoticeContent());
        }
        hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(toDto.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "发布了" + RecruitmentBrochure.AchievementType.getValueByKey(hrRecruitmentBulletin.getAchievementType()) + "公告", null, ServiceCenterEnum.RECRUITMENT_ANNOUNCEMENT.getKey());
        if (CollectionUtils.isNotEmpty(stationNameList)) {
            //有的岗位不能系统进行通知报名人数
            return ResponseUtil.buildSuccess(String.join(",", stationNameList) + "需要手动进行通知");
        }
        return ResponseUtil.buildSuccess(toDto);
    }

    /**
     * 结点增加限制
     *
     * @param hrRecruitmentBulletinDTO
     * @param hrRecruitmentStationDTO
     */
    private void nodePlusRestriction(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO, HrRecruitmentStationDTO hrRecruitmentStationDTO) {
        String errorMessage = "";
        QueryWrapper<HrRecruitmentBulletin> wrapper = new QueryWrapper<>();
        wrapper.eq("recruitment_brochure_id", hrRecruitmentStationDTO.getServiceId());
        wrapper.eq("recruitment_station_name", hrRecruitmentStationDTO.getRecruitmentStationName());
        switch (hrRecruitmentBulletinDTO.getAchievementType()) {
            case 1://笔试成绩
                errorMessage = "该岗位还未发布笔试公告，暂不可发布笔试成绩公告！";
                wrapper.eq("notice_type", RecruitmentBrochure.NoticeType.WRITTEN_ANNOUNCEMENT.getKey());
                break;
            case 2: //面试成绩
                errorMessage = "该岗位还未发布面试公告，暂不可发布面试成绩公告！";
                wrapper.eq("notice_type", RecruitmentBrochure.NoticeType.INTERVIEW_ANNOUNCEMENT.getKey());
                break;
            case 3://最终成绩
                if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                    errorMessage = "该岗位还未发布面试成绩公告，暂不可发布最终成绩公告！";
                    wrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.INTERVIEW_ACHIEVEMENT.getKey());
                } else {
                    errorMessage = "该岗位还未发布笔试成绩公告，暂不可发布最终成绩公告！";
                    wrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.WRITTEN_ACHIEVEMENT.getKey());
                }
                break;
            case 4: //考察成绩
                if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())) {
                    errorMessage = "该岗位还未发布笔试成绩公告，暂不可发布考察结果公告！";
                    wrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.WRITTEN_ACHIEVEMENT.getKey());
                } else if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())) {
                    errorMessage = "该岗位还未发布面试成绩公告，暂不可发布考察结果公告！";
                    wrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.INTERVIEW_ACHIEVEMENT.getKey());
                } else if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                    errorMessage = "该岗位还未发布面试成绩或者最终成绩公告，暂不可发布考察结果公告！";
                    wrapper.in("achievement_type", RecruitmentBrochure.AchievementType.INTERVIEW_ACHIEVEMENT.getKey(), RecruitmentBrochure.AchievementType.FINAL_ACHIEVEMENT.getKey());
                } else {
                    errorMessage = "该岗位还未发布笔试成绩或者最终成绩公告，暂不可发布考察结果公告！";
                    wrapper.in("achievement_type", RecruitmentBrochure.AchievementType.WRITTEN_ACHIEVEMENT.getKey(), RecruitmentBrochure.AchievementType.FINAL_ACHIEVEMENT.getKey());
                }
                break;
            case 5: //体检结果
                errorMessage = "该岗位还未发布考察结果公告，暂不可发布体检结果公告！";
                wrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.INVESTIGATION_RESULTS.getKey());
                break;
        }
        List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(wrapper);
        if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
            throw new CommonException(errorMessage);
        }
    }

    private void noticeInfo(HrRecruitmentStationDTO hrRecruitmentStationDTO, HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO, List<String> stationNameList) {
        //查询这个岗位下待通知考察的人进行通知
        List<HrRegistrationDetails> hrRegistrationDetailsList = hrRegistrationDetailsRepository.selectList(new QueryWrapper<HrRegistrationDetails>()
            .eq("brochure_id", hrRecruitmentStationDTO.getServiceId())
            .eq("station_id", hrRecruitmentStationDTO.getId())
            .in("status", RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey()));
        if (CollectionUtils.isNotEmpty(hrRegistrationDetailsList)) {
            if (hrRegistrationDetailsList.size() > hrRecruitmentStationDTO.getRecruitmentPeopleNumber()) {//若系统无法确认通知人选则提示专管员稍后手动进行通知
                stationNameList.add(hrRecruitmentStationDTO.getRecruitmentStationName());
            } else {//短信通知进入考察人员时间和地点
                hrRegistrationDetailsList.forEach(dto -> {
                    //改状态
                    dto.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE_INVESTIGATE_EXAM_RESULTS.getKey());
                    hrRegistrationDetailsRepository.updateById(dto);
                    HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(dto.getStaffId());
                    HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(dto.getBrochureId());
                    HashMap<Integer, String> params = new HashMap<>();
                    params.put(1, hrTalentStaff.getName());
                    params.put(2, hrRecruitmentBrochure.getRecruitBrochureName());
                    params.put(3, hrRecruitmentStationDTO.getRecruitmentStationName());
                    params.put(4, hrRecruitmentBulletinDTO.getInvestigationStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    params.put(5, hrRecruitmentBulletinDTO.getInvestigationPlace());
                    params.put(6, CompanyInfoEnum.FIRST_PART_PHONE_FOUR.getValue());
                    this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.NOTICE_INVESTIGATION.getTemplateCode(), hrTalentStaff.getPhone());
                });
            }
        }

    }

    /**
     * 发布简章到官网
     *
     * @param ids 招聘简章IDS
     * @return
     */
    @Override
    public ResponseEntity releaseOfficialWebsite(List<String> ids) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        for (String id : ids) {
            HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(id);
            hrRecruitmentBrochure.setRegisterState(RecruitmentBrochure.RegisterState.IN_RECRUITMENT.getKey());
            hrRecruitmentBrochure.setReleaseTime(LocalDateTime.now());

            hrRecruitmentBrochureRepository.updateById(hrRecruitmentBrochure);
            hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRecruitmentBrochure.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "发布了招聘简章", null, ServiceCenterEnum.RECRUITMENT_ANNOUNCEMENT.getKey());
        }
        return ResponseUtil.buildSuccess("状态更新成功，但暂不支持发布到官网！");
    }

    /**
     * 是否可以发布成绩公告
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    @Override
    public Boolean publishAchievementAnnouncement(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO) {
        List<Integer> statusList = new ArrayList<>();
        //每一个结点都限制
        HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRecruitmentBulletinDTO.getRecruitmentStationId());
        switch (hrRecruitmentBulletinDTO.getAchievementType()) {
            case 1://笔试成绩
                if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())
                    || hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_PAID_BY_USERS.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_WRITTEN_EXAM_RESULTS.getKey());
                } else {//先面试后笔试
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_WRITTEN_EXAM_RESULTS.getKey());
                }
                break;
            case 2: //面试成绩
                if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())
                    || hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_PAID_BY_USERS.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INTERVIEW_EXAM_RESULTS.getKey());
                } else {//先笔试后面试
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INTERVIEW_EXAM_RESULTS.getKey());
                }
                break;
            case 3://最终成绩
                if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_WRITTEN_EXAM_RESULTS.getKey());
                } else if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                    statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INTERVIEW_EXAM_RESULTS.getKey());
                }
                break;
            case 4: //考察结果
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INVESTIGATE_EXAM_RESULTS.getKey());
                break;
            case 5: //体检结果
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_PHYSICAL.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_PHYSICAL_EXAM_RESULTS.getKey());
                break;
        }
        List<HrRegistrationDetails> hrRegistrationDetailsList = hrRegistrationDetailsRepository.selectList(new QueryWrapper<HrRegistrationDetails>()
            .eq("station_id", hrRecruitmentBulletinDTO.getRecruitmentStationId())
            .in("status", statusList));
        if (CollectionUtils.isNotEmpty(hrRegistrationDetailsList)) {
            return true;
        } else {
            return false;
        }
    }

}
