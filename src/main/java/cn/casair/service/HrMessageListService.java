package cn.casair.service;

import cn.casair.domain.HrMessageList;
import cn.casair.dto.HrMessageListDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

/**
 * 消息列表服务类
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
public interface HrMessageListService extends IService<HrMessageList> {


    /**
     * 创建消息列表
     * @param hrMessageListDTO
     * @return
     */
    HrMessageListDTO createHrMessageList(HrMessageListDTO hrMessageListDTO);

    /**
     * 修改消息列表
     * @param hrMessageListDTO
     * @return
     */
    Optional<HrMessageListDTO> updateHrMessageList(HrMessageListDTO hrMessageListDTO);

    /**
     * 查询消息列表详情
     * @param id
     * @return
     */
    HrMessageListDTO getHrMessageList(String id);

    /**
     * 删除消息列表
     * @param id
     */
    void deleteHrMessageList(String id);

    /**
     * 批量删除消息列表
     * @param ids
     */
    void deleteHrMessageList(List<String> ids);

    /**
     * 分页查询消息列表
     * @param hrMessageListDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrMessageListDTO hrMessageListDTO,Long pageNumber,Long pageSize);

    ResponseEntity<?> getHrMessageListTop(HrMessageListDTO hrMessageListDTO);

    void BatchUpdateHrMessageList(List<String> ids);

    List<String> getHrMessageListType();
}
