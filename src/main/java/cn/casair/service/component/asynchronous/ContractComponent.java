package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.ContractEnum;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.common.utils.StringUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.HrClient;
import cn.casair.domain.HrContract;
import cn.casair.domain.HrRemindConf;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrStaffContractImport;
import cn.casair.repository.HrClientRepository;
import cn.casair.repository.HrContractRepository;
import cn.casair.repository.HrRemindConfRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 劳动合同导入
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/12/24 13:23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContractComponent {

    @Value("${file.temp-path}")
    private String tempPath;

    private final HrRemindConfRepository hrRemindConfRepository;
    private final HrClientRepository hrClientRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrContractRepository hrContractRepository;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final RedisCache redisCache;

    /**
     * 员工合同导入
     *
     * @param inputStream Excel文件
     * @param redisKey    redisKey
     * @param jwtUserDTO  当前用户登录信息
     */
    @Async
    public void dealContractImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrStaffContractImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrStaffContractImport.class);
            ImportResultDTO importResult;

            int scale = 0;
            int listSize = result.getList().size();

            HrRemindConf hrRemindConf = hrRemindConfRepository.selectByRemindKey("staff_contract");
            List<HrContract> hrContractList = new ArrayList<>();
            StringBuilder sbError = new StringBuilder();
            for (HrStaffContractImport importDate : result.getList()) {
                try {
                    sbError.delete(0, sbError.length());
                    HrClient hrClient = this.hrClientRepository.selectClientUnitNumber(importDate.getUnitNumber());
                    if (hrClient == null) {
                        sbError.append("客户编号错误：未查询到相关客户信息！");
                    }
                    HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectBySystemNum(importDate.getSystemNum());
                    if (hrTalentStaff == null) {
                        sbError.append("员工编号错误：未查询到相关员工信息！");
                    }
                    if (importDate.getContractStartDate().isAfter(importDate.getContractEndDate())) {
                        sbError.append("合同期限设置错误：合同开始时间大于合同结束时间！");
                    }
                    if (importDate.getContractStartDate().equals(importDate.getContractEndDate())) {
                        sbError.append("合同期限设置错误：合同开始时间与合同结束时间相同！");
                    }
                    if (StringUtils.isEmpty(sbError)) {
                        // 获取当前员工最新（以劳动合同结束时间为准）的劳动合同
                        HrContract hrContract = this.hrContractRepository.selectNewestRecord(hrTalentStaff.getId(), hrClient.getId());
                        if (hrContract != null) {
                            if (!hrContract.getContractEndDate().plusDays(1).equals(importDate.getContractStartDate())) {
                                // 如果已经存在劳动合同 且上一份劳动合同的结束日期与新合同的开始日期不衔接， 不可生成合同
                                sbError.append("员工新合同开始日期与旧合同结束日期不匹配！");
                            } else {
                                this.addNewStaffContract(hrContractList, hrRemindConf, jwtUserDTO, hrClient, hrTalentStaff, importDate, 1);
                            }
                        } else {
                            // 如果不存在劳动合同
                            this.addNewStaffContract(hrContractList, hrRemindConf, jwtUserDTO, hrClient, hrTalentStaff, importDate, 0);
                        }
                    }
                } catch (Exception e) {
                    log.error("导入员工合同异常:{}", e.getMessage());
                    sbError.append(e.getMessage());
                } finally {
                    scale++;
                    importDate.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 30, TimeUnit.MINUTES);
                }
            }
            if (!hrContractList.isEmpty()) {
                this.hrContractRepository.batchInsert(hrContractList);
            }

            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("员工劳动合同批量导入", HrStaffContractImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CONTRACT.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("员工合同异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "员工合同导入发生异常：Excel文件读取失败，" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }


    /**
     * 创建新合同
     *
     * @param hrClient      hrClient
     * @param hrTalentStaff hrTalentStaff
     * @param template      template
     * @param contractType  contractType
     */
    private void addNewStaffContract(List<HrContract> hrContractList, HrRemindConf hrRemindConf, JWTUserDTO jwtUserDTO, HrClient hrClient, HrTalentStaff hrTalentStaff, HrStaffContractImport template, int contractType) {
        HrContract hrContract = new HrContract();
        hrContract.setId(RandomUtil.generateId());
        hrContract.setClientId(hrClient.getId());
        hrContract.setUnitNumber(hrClient.getUnitNumber());
        hrContract.setClientName(hrClient.getClientName());
        hrContract.setStaffId(hrTalentStaff.getId());
        hrContract.setSystemNum(hrTalentStaff.getSystemNum());
        hrContract.setStaffName(hrTalentStaff.getName());
        hrContract.setIdNo(hrTalentStaff.getCertificateNum());
        hrContract.setPhone(hrTalentStaff.getPhone());

        // 如果合同结束日期在当前日期之前，合同过期
        if (template.getContractEndDate().isBefore(LocalDate.now())) {
            hrContract.setState(ContractEnum.ContractState.EXPIRED.getKey());
        }
        // 若合同的开始日期在当前时间之后 合同未生效
        else if (template.getContractStartDate().isAfter(LocalDate.now())) {
            hrContract.setState(ContractEnum.ContractState.NOT_ACTIVE.getKey());
        }
        // 如果合同结束日期距离当前时间天数小于等于60天， 合同快过期
        else if ((template.getContractEndDate().toEpochDay() - LocalDate.now().toEpochDay()) <= (hrRemindConf == null ? 60 : hrRemindConf.getRuleDay())) {
            hrContract.setState(ContractEnum.ContractState.EXPIRING_SOON.getKey());
        }
        // 合同生效中
        else {
            hrContract.setState(ContractEnum.ContractState.IN_EFFECT.getKey());
        }

        hrContract.setContractType(contractType);
        hrContract.setContractStartDate(template.getContractStartDate());
        hrContract.setContractEndDate(template.getContractEndDate());
        hrContract.setContractInitDate(template.getContractInitDate());
        hrContract.setContractSignDate(template.getContractSignDate());
        hrContract.setCreatedBy(jwtUserDTO.getUserName());
        hrContract.setCreatedDate(LocalDateTime.now());
        hrContractList.add(hrContract);
        // this.hrContractRepository.insert(hrContract);
    }
}
