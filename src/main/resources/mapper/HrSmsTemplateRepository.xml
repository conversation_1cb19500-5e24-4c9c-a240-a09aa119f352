<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrSmsTemplateRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, template_id, template_type, send_content, send_title, `status`,
        is_delete,created_by,last_modified_by,created_date,last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrSmsTemplate">
        <id column="id" property="id"/>
        <result column="template_id" property="templateId"/>
        <result column="template_type" property="templateType"/>
        <result column="send_content" property="sendContent"/>
        <result column="send_title" property="sendTitle"/>
        <result column="status" property="status"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByObject" resultType="cn.casair.domain.HrSmsTemplate">
        SELECT *
        FROM hr_sms_template
        WHERE is_delete = 0
          AND template_id = #{params.templateId}
          AND `status` = #{params.status}
    </select>
</mapper>
