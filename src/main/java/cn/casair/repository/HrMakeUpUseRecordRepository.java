package cn.casair.repository;

import cn.casair.domain.HrMakeUpUseRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

/**
 * 员工福利补差使用记录数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Repository
public interface HrMakeUpUseRecordRepository extends BaseMapper<HrMakeUpUseRecord> {

    /**
     * 根据福利补差id获取补差使用记录信息
     * （最终补差数据）
     * @param makeUpId
     * <AUTHOR>
     * @return cn.casair.domain.HrMakeUpUseRecord
     * @date 2021/11/15
     **/
    HrMakeUpUseRecord selectByMakeUpId(String makeUpId);
}
