package cn.casair.service;

import cn.casair.domain.HrAccumulationFund;
import cn.casair.domain.HrSocialSecurity;
import cn.casair.domain.HrWelfareCompensationRecord;
import cn.casair.dto.HrEmployeeWelfareDTO;
import cn.casair.dto.HrWelfareCompensationRecordDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 福利补差计算操作日志服务类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
public interface HrWelfareCompensationRecordService extends IService<HrWelfareCompensationRecord> {

    /**
     * 根据员工福利补差统计id删除操作日志
     *
     * @param welfareCompensationIds
     * @return int
     * <AUTHOR>
     * @date 2021/12/6
     **/
    int deleteByWelfareCompensationIds(List<String> welfareCompensationIds);

    /**
     * 创建福利补差计算操作日志
     *
     * @param hrWelfareCompensationRecordDTO
     * @return
     */
    HrWelfareCompensationRecordDTO createHrWelfareCompensationLog(HrWelfareCompensationRecordDTO hrWelfareCompensationRecordDTO);

    /**
     * 修改福利补差计算操作日志
     *
     * @param hrWelfareCompensationRecordDTO
     * @return
     */
    Optional<HrWelfareCompensationRecordDTO> updateHrWelfareCompensationLog(HrWelfareCompensationRecordDTO hrWelfareCompensationRecordDTO);

    /**
     * 查询福利补差计算操作日志详情
     *
     * @param id
     * @return
     */
    HrWelfareCompensationRecordDTO getHrWelfareCompensationLog(String id);

    /**
     * 删除福利补差计算操作日志
     *
     * @param id
     */
    void deleteHrWelfareCompensationLog(String id);

    /**
     * 批量删除福利补差计算操作日志
     *
     * @param ids
     */
    void deleteHrWelfareCompensationLog(List<String> ids);

    /**
     * 分页查询福利补差计算操作日志
     *
     * @param hrWelfareCompensationRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrWelfareCompensationRecordDTO hrWelfareCompensationRecordDTO, Long pageNumber, Long pageSize);

    /**
     * 添加员工福利基础补差修改记录
     *
     * @param hrEmployeeWelfareOld
     * @param hrEmployeeWelfareNew
     * @param changeMsg
     * @return cn.casair.domain.HrWelfareCompensationLog
     * <AUTHOR>
     * @date 2021/11/11
     **/
    HrWelfareCompensationRecord addLog(HrEmployeeWelfareDTO hrEmployeeWelfareOld, HrEmployeeWelfareDTO hrEmployeeWelfareNew, String changeMsg);

    /**
     * 添加社保补差修改记录
     *
     * @param hrSocialSecurityOld
     * @param hrSocialSecurityNew
     * @param changeMsg
     * @return cn.casair.domain.HrWelfareCompensationLog
     * <AUTHOR>
     * @date 2021/11/11
     **/
    HrWelfareCompensationRecord addLog(HrSocialSecurity hrSocialSecurityOld, HrSocialSecurity hrSocialSecurityNew, String changeMsg);

    /**
     * 添加公积金补差修改记录
     *
     * @param hrAccumulationFundOld
     * @param hrAccumulationFundNew
     * @param changeMsg
     * @return cn.casair.domain.HrWelfareCompensationLog
     * <AUTHOR>
     * @date 2021/11/11
     **/
    HrWelfareCompensationRecord addLog(HrAccumulationFund hrAccumulationFundOld, HrAccumulationFund hrAccumulationFundNew, String changeMsg);

    /**
     * 获取补差记录信息
     *
     * @param recordId
     * @return cn.casair.domain.HrWelfareCompensationRecord
     * <AUTHOR>
     * @date 2021/11/11
     **/
    HrWelfareCompensationRecord selectById(String recordId);

    /**
     * 根据账单id关联查询补差操作日志
     *
     * @param billId
     * @return java.util.List<cn.casair.dto.HrWelfareCompensationRecordDTO>
     * <AUTHOR>
     * @date 2021/11/15
     **/
    List<HrWelfareCompensationRecordDTO> selectByBillId(String billId);
}
