package cn.casair.service;

import cn.casair.domain.HrRecruitmentDrafts;
import cn.casair.dto.HrRecruitmentDraftsDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Optional;

/**
 * 招聘草稿箱服务类
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
public interface HrRecruitmentDraftsService extends IService<HrRecruitmentDrafts> {


    /**
     * 创建招聘草稿箱
     *
     * @param hrRecruitmentDraftsDTO
     * @return
     */
    HrRecruitmentDraftsDTO createHrRecruitmentDrafts(HrRecruitmentDraftsDTO hrRecruitmentDraftsDTO);

    /**
     * 修改招聘草稿箱
     *
     * @param hrRecruitmentDraftsDTO
     * @return
     */
    Optional<HrRecruitmentDraftsDTO> updateHrRecruitmentDrafts(HrRecruitmentDraftsDTO hrRecruitmentDraftsDTO);

    /**
     * 查询招聘草稿箱详情
     *
     * @param id
     * @return
     */
    HrRecruitmentDraftsDTO getHrRecruitmentDrafts(String id);

    /**
     * 添加草稿箱
     * @param draftType 草稿类型 1考试须知 2笔试公告 3面试公告 4笔试成绩公告 5面试成绩公告 6 最终成绩公告 7考察结果公告 8体检结果公告 9其他公告
     * @param draftContent 草稿箱内容
     */
    HrRecruitmentDraftsDTO generateHrRecruitmentDrafts(Integer draftType, String draftContent);

    /**
     * 查看最新草稿箱
     * @param draftType 草稿类型
     * @return 草稿箱
     */
    HrRecruitmentDraftsDTO latestContentHrRecruitmentDrafts(Integer draftType);

}
