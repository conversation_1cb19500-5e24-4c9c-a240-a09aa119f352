package cn.casair.service;

import cn.casair.domain.HrStaffWorkExperience;
import cn.casair.dto.HrStaffWorkExperienceDTO;
import cn.casair.dto.JWTUserDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 工作经历服务类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
public interface HrStaffWorkExperienceService extends IService<HrStaffWorkExperience> {


    /**
     * 创建工作经历
     * @param hrStaffWorkExperienceDTO
     * @return
     */
    List<HrStaffWorkExperienceDTO> createHrWorkExperience(HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO);

    /**
     * 修改工作经历
     * @param hrStaffWorkExperienceDTO
     * @return
     */
    Optional<List<HrStaffWorkExperienceDTO>> updateHrWorkExperience(HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO);

    /**
     * 查询工作经历详情
     * @param id
     * @return
     */
    HrStaffWorkExperienceDTO getHrWorkExperience(String id);

    /**
     * 删除工作经历
     * @param id
     */
    List<HrStaffWorkExperienceDTO> deleteHrWorkExperience(String id);

    /**
     * 根据员工ID查询工作经历
     * @param staffId 员工ID
     * @param izDefault 标识 0:之前公司 1:入职公司
     * @return
     */
    List<HrStaffWorkExperienceDTO> findWorkExperienceList(String staffId,Boolean izDefault);

    void setDict(HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO);

    void processEmployeeDeletion(JWTUserDTO jwtUserDTO, List<String> ids);

    void deleteBatchIds(List<String> ids, JWTUserDTO jwtUserDTO);

}
