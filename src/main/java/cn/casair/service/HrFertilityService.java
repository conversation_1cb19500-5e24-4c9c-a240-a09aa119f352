package cn.casair.service;
import cn.casair.domain.HrFertility;
import cn.casair.dto.HrFertilityDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;

/**
 * 生育表服务类
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
public interface HrFertilityService extends IService<HrFertility> {


    /**
     * 创建生育表
     * @param hrFertilityDTO
     * @return
     */
    HrFertilityDTO createHrFertility(HrFertilityDTO hrFertilityDTO);

    /**
     * 修改生育表
     * @param hrFertilityDTO
     * @return
     */
    Optional<HrFertilityDTO> updateHrFertility(HrFertilityDTO hrFertilityDTO);

    /**
     * 查询生育表详情
     * @param id
     * @return
     */
    HrFertilityDTO getHrFertility(String id);

    /**
     * 删除生育表
     * @param id
     */
    void deleteHrFertility(String id);

    /**
     * 批量删除生育表
     * @param ids
     */
    void deleteHrFertility(List<String> ids);

    /**
     * 分页查询生育表
     * @param hrFertilityDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrFertilityDTO hrFertilityDTO,Long pageNumber,Long pageSize);

    /**
     * 查看生育信息
     * @param hrFertilityDTO
     * @return
     */
    HrFertilityDTO getDetail(HrFertilityDTO hrFertilityDTO);

    /**
     * 批量进行修改状态
     * @param ids
     * @param status
     * @return
     */
    ResponseEntity<?> updateStates(List<HrFertilityDTO> ids, Integer status);

    /**
     * 导出生育信息
     * @param hrFertilityDTO
     * @param httpServletResponse
     * @return
     */
    String exportFertility(HrFertilityDTO hrFertilityDTO, HttpServletResponse httpServletResponse);

    /**
     *检查员工生育服务结束时间是否在15天之内并且修改员工产假的状态（定时任务）
     */
    void fertilityTask();

    /**
     * 微信 生育的进度详情
     * @return
     */
    Map<String, Object> fertilityPlanned();

    /**
     * 微信 生育单条进度详情
     * @param serviceId
     * @return
     */
    HrFertilityDTO fertilityPlannedOne(String serviceId);
}
