package cn.casair.security.jwt;

import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.dto.JWTMiniDTO;
import cn.casair.dto.JWTUserDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.jhipster.config.JHipsterProperties;
import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class TokenProvider implements InitializingBean {

    @Value("${constant.loginCheck}")
    private boolean loginCheck;

    private static final String AUTHORITIES_KEY = "auth";
    private static final String AUTHORITIES = "auths";
    private static final String JWT_USER = "jwtUser";
    private static final String JWT_MINI = "jwtmini";

    private Key key;

    private long tokenValidityInMilliseconds;

    private long tokenValidityInMillisecondsForRememberMe;

    private final JHipsterProperties jHipsterProperties;
    private final RedisCache redisCache;

    public Map<String, String> checkUserRedisToken(Authentication authentication, String jwt) {
        Map<String, String> result = new HashMap<>();
        if (loginCheck) {
            String redisKey;
            try {
                JWTUserDTO user = (JWTUserDTO) authentication.getPrincipal();
                redisKey = RedisKeyEnum.currencyKey.USER_TOKEN.getValue() + user.getId();
            } catch (Exception ignored) {
                JWTMiniDTO user = (JWTMiniDTO) authentication.getPrincipal();
                redisKey = RedisKeyEnum.currencyKey.USER_TOKEN.getValue() + user.getId();
            }
            String userToken = redisCache.getCacheObject(redisKey);
            if (StringUtils.isBlank(userToken)) {
                result.put("code", "401");
                result.put("msg", "您的登录已过期,请重新登录!");
            } else if (!userToken.equals(jwt)) {
                result.put("code", "401");
                result.put("msg", "此账号在其他设备登录，请确保您的密码未泄露！");
            }
        }
        return result;
    }

    @Override
    public void afterPropertiesSet() {
        byte[] keyBytes;
        String secret = jHipsterProperties.getSecurity().getAuthentication().getJwt().getSecret();
        if (!StringUtils.isEmpty(secret)) {
            log.warn("Warning: the JWT key used is not Base64-encoded. " +
                    "We recommend using the `jhipster.security.authentication.jwt.base64-secret` key for optimum security.");
            keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        } else {
            log.info("Using a Base64-encoded JWT secret key");
            keyBytes = Decoders.BASE64.decode(jHipsterProperties.getSecurity().getAuthentication().getJwt().getBase64Secret());
        }
        this.key = Keys.hmacShaKeyFor(keyBytes);
        this.tokenValidityInMilliseconds =
                1000 * jHipsterProperties.getSecurity().getAuthentication().getJwt().getTokenValidityInSeconds();
        this.tokenValidityInMillisecondsForRememberMe =
                1000 * jHipsterProperties.getSecurity().getAuthentication().getJwt()
                        .getTokenValidityInSecondsForRememberMe();
    }

    public String createToken(Authentication authentication, boolean rememberMe) {
        String authorities = authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.joining(","));

        long now = (new Date()).getTime();
        Date validity;
        if (rememberMe) {
            validity = new Date(now + this.tokenValidityInMillisecondsForRememberMe);
        } else {
            validity = new Date(now + this.tokenValidityInMilliseconds);
        }

        return Jwts.builder()
                .setSubject(authentication.getName())
                .claim(AUTHORITIES_KEY, authorities)
                .claim(JWT_USER, authentication.getPrincipal())
                .signWith(key, SignatureAlgorithm.HS512)
                .setExpiration(validity)
                .compact();
    }

    public String createTokenMini(Authentication authentication, boolean rememberMe) {
        String authorities = authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.joining(","));

        long now = (new Date()).getTime();
        Date validity;
        if (rememberMe) {
            validity = new Date(now + this.tokenValidityInMillisecondsForRememberMe);
        } else {
            validity = new Date(now + this.tokenValidityInMilliseconds);
        }

        return Jwts.builder()
                .setSubject(authentication.getName())
                .claim(AUTHORITIES, authorities)
                .claim(JWT_MINI, authentication.getPrincipal())
                .signWith(key, SignatureAlgorithm.HS512)
                .setExpiration(validity)
                .compact();
    }

    public Authentication getAuthentication(String token) {
        Claims claims = Jwts.parser()
                .setSigningKey(key)
                .parseClaimsJws(token)
                .getBody();

        Collection<? extends GrantedAuthority> authorities =
                null;
        try {
            authorities =
                    Arrays.stream(claims.get(AUTHORITIES_KEY).toString().split(","))
                            .map(SimpleGrantedAuthority::new)
                            .collect(Collectors.toList());
            authorities = Arrays.stream(claims.get(AUTHORITIES).toString().split(","))
                    .map(SimpleGrantedAuthority::new)
                    .collect(Collectors.toList());
        } catch (Exception e) {

        }
        ObjectMapper mapper = new ObjectMapper();
        JWTUserDTO jwtUser = mapper.convertValue(claims.get(JWT_USER), JWTUserDTO.class);
        JWTMiniDTO jwtMiniDTO = mapper.convertValue(claims.get(JWT_MINI), JWTMiniDTO.class);
        if (jwtUser != null) {
            return new UsernamePasswordAuthenticationToken(jwtUser, token, authorities);
        } else {
            return new UsernamePasswordAuthenticationToken(jwtMiniDTO, token, authorities);
        }
    }


    public boolean validateToken(String authToken) {
        try {
            Jwts.parser().setSigningKey(key).parseClaimsJws(authToken);
            return true;
        } catch (io.jsonwebtoken.security.SecurityException | MalformedJwtException e) {
            log.info("Invalid JWT signature.");
            log.trace("Invalid JWT signature trace: {}", e);
        } catch (ExpiredJwtException e) {
            log.info("Expired JWT token.");
            log.trace("Expired JWT token trace: {}", e);
        } catch (UnsupportedJwtException e) {
            log.info("Unsupported JWT token.");
            log.trace("Unsupported JWT token trace: {}", e);
        } catch (IllegalArgumentException e) {
            log.info("JWT token compact of handler are invalid.");
            log.trace("JWT token compact of handler are invalid trace: {}", e);
        }
        return false;
    }
}
