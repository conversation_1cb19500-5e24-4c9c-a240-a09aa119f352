package cn.casair.service;

import cn.casair.domain.HrPaperManagement;
import cn.casair.domain.HrQuestion;
import cn.casair.dto.HrPaperManagementDTO;
import cn.casair.dto.HrQuestionDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * 试卷管理服务类
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
public interface HrPaperManagementService extends IService<HrPaperManagement> {


    /**
     * 创建试卷管理
     *
     * @param hrPaperManagementDTO
     * @return
     */
    HrPaperManagementDTO createHrPaperManagement(HrPaperManagementDTO hrPaperManagementDTO);

    /**
     * 修改试卷管理
     *
     * @param hrPaperManagementDTO
     * @return
     */
    ResponseEntity<String> updateHrPaperManagement(HrPaperManagementDTO hrPaperManagementDTO);

    /**
     * 查询试卷管理详情
     *
     * @param id
     * @return
     */
    HrPaperManagementDTO getHrPaperManagement(String id);

    /**
     * 删除试卷管理
     *
     * @param id
     */
    void deleteHrPaperManagement(String id);

    /**
     * 批量删除试卷管理
     *
     * @param ids
     */
    void deleteHrPaperManagement(List<String> ids);

    /**
     * 分页查询试卷管理
     *
     * @param hrPaperManagementDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrPaperManagementDTO hrPaperManagementDTO, Long pageNumber, Long pageSize);

    List<HrPaperManagementDTO> getHrPaperManagementClient();

    List<HrPaperManagementDTO> getHrPaperManagementStation();


    List<HrPaperManagementDTO> getHrPaperManagementIndexClient();

    List<HrPaperManagementDTO> getHrPaperManagementIndexStation();

    IPage<HrQuestionDTO> findPageHrQuestion(HrQuestionDTO hrQuestionDTO, Long pageNumber, Long pageSize);

    void getCopyHrPaperManagement(HrPaperManagementDTO hrPaperManagementDTO);

    List<HrQuestionDTO> getHrPaperManagementIndexScore(HrQuestionDTO hrQuestionDTO);

    List<HrQuestionDTO> findPageRandom(HrQuestionDTO hrQuestionDTO );


    IPage<HrQuestionDTO> findPageReplace(HrQuestionDTO hrQuestionDTO, Long pageNumber, Long pageSize);


    List<HrPaperManagementDTO> HrPaperManagementDownload(HrPaperManagementDTO hrPaperManagementDTO);

    Object getHrPaperManagementScore(String id);


    Object getHrPaperManagementSingleUpdate(HrPaperManagementDTO hrPaperManagementDTO);

    IPage<HrQuestion> getHrPaperManagementSingle(HrPaperManagementDTO hrPaperManagementDTO, Long pageNumber, Long pageSize);

    int questionssSumRandom(HrQuestionDTO hrQuestionDTO);
}
