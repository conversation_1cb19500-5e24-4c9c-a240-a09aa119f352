package cn.casair.service;

import cn.casair.common.utils.excel.CellItem;
import cn.casair.domain.HrFeeReview;
import cn.casair.dto.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 费用审核服务类
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
public interface HrFeeReviewService extends IService<HrFeeReview> {


    /**
     * 创建费用审核
     * @param hrFeeReviewDTO
     * @return
     */
    HrFeeReviewDTO createHrFeeReview(HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 修改费用审核
     * @param hrFeeReviewDTO
     * @return
     */
    Optional<HrFeeReviewDTO> updateHrFeeReview(HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 查询费用审核详情
     * @param id
     * @return
     */
    HrFeeReviewDTO getHrFeeReview(String id,Integer type,String clientId);

    /**
     * 删除费用审核
     * @param id
     */
    void deleteHrFeeReview(String id);

    /**
     * 批量删除费用审核
     * @param ids
     */
    void deleteHrFeeReview(List<String> ids);

    /**
     * 分页查询费用审核
     * @param hrFeeReviewDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrFeeReviewDTO hrFeeReviewDTO,Long pageNumber,Long pageSize);

    Object getHrFeeReviewStaff(HrFeeReviewDTO hrFeeReviewDTO);

    ResponseEntity getHrFeeReviewStaffEffectiveness(HrFeeReviewDTO hrFeeReviewDTO);

    Object getHrFeeReviewStaffEffReboot(HrFeeReviewDTO hrFeeReviewDTO);

    void getHrFeeReviewLock(HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 作废结算单
     * @param hrFeeReviewDTO
     * @return
     */
    HrFeeReviewDTO launchCancelApply(HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 作废审核
     * @param hrFeeReviewDTO
     * @return
     */
    HrFeeReviewDTO confirmCancelApply(HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 查询所属账单
     * @param hrFeeReviewDTO
     * @return
     */
    List<HrBillDTO> queryBillInfo(HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 查询所属客户
     * @param hrFeeReviewDTO
     * @return
     */
    List<HrClientDTO> queryClientInfo(HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 生成结算单信息
     * @param hrFeeReviewDTO
     * @return
     */
    HrSettleAccountDTO generateSettlementDocumentInfo(HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 保存结算单信息
     * @param hrSettleAccountDTO
     */
    void preservationSettlementDocumentInfo(HrSettleAccountDTO hrSettleAccountDTO);

    /**
     * 查看结算单信息
     * @param id 结算单Id
     * @return
     */
    HrSettleAccountDTO seeSettlementDocumentInfo(String id);

    /**
     * 查看客户选择的结算单表头
     * @param clientId
     * @return
     */
    List<HrReviewChoiceDTO> handleChoiceDynamic(String clientId);

    /**
     * excel导入工资明细,获取表头
     * @param file
     * @param clientId
     * @return
     */
    Map<String, Object> getOriginTableHeader(MultipartFile file, String clientId);

    /**
     * 海尔账单字段映射
     * @param dynamicFieldId 原单ID
     * @param type 0添加 1删除
     * @param param
     * @return
     */
    List<List<CellItem>> handleMappingRelationWithBill(String dynamicFieldId, Integer type, HrExpenseManageDTO param);

    /**
     * 获取未映射的字段列表
     * @param dynamicFieldId 原单ID
     * @return
     */
    List<CellItem> getUnUsedFieldList(String dynamicFieldId);

    /**
     * 返回结算单明细
     *
     * @param hrFeeReviewDTO
     * @return
     */
    Map<String, Object> fillReviewDetail(HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 处理结算单Excel 和 PDF
     * @param hrFeeReviewDTO
     * @return
     */
    HrSettleAccountDTO handleFeeReviewExcelAndPDF(HrFeeReviewDTO hrFeeReviewDTO);

}
