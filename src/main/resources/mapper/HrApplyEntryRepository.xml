<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrApplyEntryRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, client_id, station_id, staff_num, protocol_id, appendix_ids, apply_remark, apply_status,is_default,basic_wage,seniority_wage_base,pay_year,pay_monthly,staff_type,internship_duration
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrApplyEntry">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="protocol_id" property="protocolId"/>
        <result column="station_id" property="stationId"/>
        <result column="staff_num" property="staffNum"/>
        <result column="appendix_ids" property="appendixIds"/>
        <result column="apply_remark" property="applyRemark"/>
        <result column="apply_status" property="applyStatus"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findPage" resultType="cn.casair.dto.HrApplyEntryDTO">
        SELECT
            hc.unit_number,hc.client_name,hs.profession_name,hae.id,hae.staff_num,hae.created_date,hae.apply_status
        FROM
            hr_apply_entry hae
        LEFT JOIN hr_client hc ON hae.client_id = hc.id AND hc.is_delete = 0
        LEFT JOIN hr_station hs ON hae.station_id = hs.id AND hs.is_delete = 0
        WHERE hae.is_delete = 0
        <if test="param.unitNumber != null and param.unitNumber != ''">
            AND hc.unit_number LIKE concat('%', #{param.unitNumber}, '%')
        </if>
        <if test="param.clientId != null">
            AND hc.id = #{param.clientId}
        </if>
        <if test="param.professionName != null and param.professionName != ''">
            AND hs.profession_name LIKE concat('%', #{param.professionName}, '%')
        </if>
        <if test="param.createdDateStart!=null">
            AND hae.created_date &gt;= #{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd!=null">
            AND hae.created_date &lt; date_add(#{param.createdDateEnd}, interval 1 day)
        </if>
        <if test="param.applyStatusList!=null and param.applyStatusList.size() > 0">
            AND hae.apply_status IN
            <foreach collection="param.applyStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hc.id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.stationIdList!=null and param.stationIdList.size() > 0">
            AND hs.id IN
            <foreach collection="param.stationIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            ORDER BY ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY hae.created_date DESC
        </if>
    </select>
    <select id="getHrApplyEntryById" resultType="cn.casair.dto.HrApplyEntryDTO">
        SELECT
            hc.unit_number,
            hc.client_name,
            hp.id AS protocolId,
            hp.agreement_number,
            hp.agreement_title,
            hp.agreement_type,
            hs.profession_name,
            hae.*
        FROM
            hr_apply_entry hae
        LEFT JOIN hr_client hc ON hae.client_id = hc.id AND hc.is_delete = 0
        LEFT JOIN hr_station hs ON hae.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN hr_protocol hp ON hae.protocol_id = hp.id AND hp.is_delete = 0
        WHERE
            hae.is_delete = 0 AND hae.id = #{id}
    </select>
</mapper>
