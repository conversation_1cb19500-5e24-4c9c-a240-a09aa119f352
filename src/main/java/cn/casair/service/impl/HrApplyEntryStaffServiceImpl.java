package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.Constants;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.ValidateUtil;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrApplyEntryStaffFailExport;
import cn.casair.mapper.HrApplyEntryMapper;
import cn.casair.mapper.HrApplyEntryStaffMapper;
import cn.casair.mapper.HrTalentStaffMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 申请入职员工服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HrApplyEntryStaffServiceImpl extends ServiceImpl<HrApplyEntryStaffRepository, HrApplyEntryStaff> implements HrApplyEntryStaffService {

    @Value("${mini.appletName}")
    private String site;

    private final HrAppendixService hrAppendixService;
    private final HrRealNameAuthService hrRealNameAuthService;
    private final HrApplyEntryStaffRepository hrApplyEntryStaffRepository;
    private final HrApplyEntryStaffMapper hrApplyEntryStaffMapper;
    private final CodeTableService codeTableService;
    private final HrApplyOpLogsRepository hrApplyOpLogsRepository;
    private final HrApplyCheckerRepository hrApplyCheckerRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;
    private final HrStaffEmolumentRepository hrStaffEmolumentRepository;
    private final HrContractRepository hrContractRepository;
    private final HrClientRepository hrClientRepository;
    private final HrStaffStationRepository hrStaffStationRepository;
    private final HrSendSmsService hrSendSmsService;
    private final HrClientService hrClientService;
    private final HrTalentStaffMapper hrTalentStaffMapper;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrNotificationUserService hrNotificationUserService;
    private final HrApplyEntryRepository hrApplyEntryRepository;
    private final HrApplyEntryMapper hrApplyEntryMapper;
    private final HrAppletMessageService hrAppletMessageService;
    private final SysOperLogService sysOperLogService;
    private final HrUpcomingService hrUpcomingService;
    private final HrSmsTemplateService hrSmsTemplateService;

    /**
     * 创建申请入职员工
     *
     * @param hrApplyEntryStaffDTO
     * @return
     */
    @Override
    @Transactional
    public HrApplyEntryStaffDTO createHrApplyEntryStaff(HrApplyEntryStaffDTO hrApplyEntryStaffDTO) {
        log.info("Create new HrApplyEntryStaff:{}", hrApplyEntryStaffDTO);
        JWTUserDTO jwtUserDTO = this.loginUserInfo();
        HrApplyEntryStaff hrApplyEntryStaff = this.hrApplyEntryStaffMapper.toEntity(hrApplyEntryStaffDTO);
        //计算工龄工资
        BigDecimal bigDecimal = CalculateUtils.workAge(hrApplyEntryStaff.getContractStartDate(), hrApplyEntryStaff.getSeniorityWageBase());
        hrApplyEntryStaff.setSeniorityPay(bigDecimal).setLastModifiedDate(LocalDateTime.now());
        this.hrApplyEntryStaffRepository.insert(hrApplyEntryStaff);
        HrApplyEntryStaffDTO entryStaffDTO = this.hrApplyEntryStaffMapper.toDto(hrApplyEntryStaff);

        //创建入职申请操作日志
        String message = jwtUserDTO.getRealName()+"发起了员工入职申请。等待审批";
        this.createApplyOpLogs(entryStaffDTO.getId(),jwtUserDTO.getId(),message);
        this.hrNotificationUserService.saveRemindContent(entryStaffDTO.getClientId(),ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(), ServiceCenterEnum.ReminderContentEnum.INITIATE_APPLICATION.getKey(),message,jwtUserDTO.getId());
        return entryStaffDTO;
    }

    /**
     * 查询申请入职员工详情
     *
     * @param id 待入职申请ID
     * @return
     */
    @Override
    public HrApplyEntryStaffDTO getHrApplyEntryStaff(String id) {
        log.info("Get HrApplyEntryStaff :{}", id);

        HrApplyEntryStaffDTO hrApplyEntryStaffDTO = this.hrApplyEntryStaffRepository.getApplyEntryStaffById(id);
        Map<Integer, String> businessType = codeTableService.findCodeTableByInnerName("businessType");//业务类型
        Map<Integer, String> residenceType = codeTableService.findCodeTableByInnerName("residenceType");//户口性质
        this.setDict(hrApplyEntryStaffDTO,businessType,residenceType);
        //返回申请操作日志
        List<HrApplyOpLogsDTO> opLogsList = hrApplyOpLogsRepository.findApplyOpLogsList(hrApplyEntryStaffDTO.getApplyId(),id);
        hrApplyEntryStaffDTO.setOpLogsList(opLogsList);
        return hrApplyEntryStaffDTO;
    }

    /**
     * 批量删除申请入职员工
     *
     * @param ids
     */
    @Override
    @Transactional
    public void deleteHrApplyEntryStaff(List<String> ids) {
        log.info("Delete HrApplyEntryStaffs:{}", ids);
        this.hrApplyEntryStaffRepository.deleteBatchIds(ids);
        //删除审核明细
        this.hrApplyCheckerRepository.delete(new QueryWrapper<HrApplyChecker>().in("apply_id",ids));
        //删除申请操作日志
        this.hrApplyOpLogsRepository.delete(new QueryWrapper<HrApplyOpLogs>().in("apply_id",ids));
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CREATE_APPLICATION.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
    }

    /**
     * 分页查询申请入职员工
     *
     * @param hrApplyEntryStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrApplyEntryStaffDTO hrApplyEntryStaffDTO, Long pageNumber, Long pageSize) {
        Page<HrApplyEntryStaff> page = new Page<>(pageNumber, pageSize);
        if(CollectionUtils.isEmpty(hrApplyEntryStaffDTO.getClientIds())) {
            List<String> clientIds = hrClientService.selectClientIdByUserId();
            if (CollectionUtils.isEmpty(clientIds)) {
                clientIds.add("");
            }
            hrApplyEntryStaffDTO.setClientIds(clientIds);
        }
        IPage<HrApplyEntryStaffDTO> iPage = this.hrApplyEntryStaffRepository.findPage(page, hrApplyEntryStaffDTO);
        Map<Integer, String> businessType = codeTableService.findCodeTableByInnerName("businessType");//业务类型
        Map<Integer, String> residenceType = codeTableService.findCodeTableByInnerName("residenceType");//户口性质
        iPage.getRecords().forEach(ls->{this.setDict(ls,businessType,residenceType);});
        return iPage;
    }

    /**
     * 赋值字典值
     * @param hrApplyEntryStaffDTO
     * @param businessType
     * @param residenceType
     */
    public void setDict(HrApplyEntryStaffDTO hrApplyEntryStaffDTO, Map<Integer, String> businessType, Map<Integer, String> residenceType) {
        if (hrApplyEntryStaffDTO.getEntryStatus()!=null){//入职流程
            hrApplyEntryStaffDTO.setEntryStatusLabel(ApprovalEntryStatusEnum.EntryStatus.getValueByKey(hrApplyEntryStaffDTO.getEntryStatus()));
        }
        if (hrApplyEntryStaffDTO.getBusinessType()!=null){
            hrApplyEntryStaffDTO.setBusinessTypeLabel(businessType.get(hrApplyEntryStaffDTO.getBusinessType()));
        }
        if(hrApplyEntryStaffDTO.getPersonnelType()!=null){
            hrApplyEntryStaffDTO.setPersonnelTypeLabel(StaffEnum.PersonnelTypeEnum.getValueByKey(hrApplyEntryStaffDTO.getPersonnelType()));
        }
        if (hrApplyEntryStaffDTO.getHouseholdRegistration() != null) {
            hrApplyEntryStaffDTO.setHouseholdRegistrationLabel(residenceType.get(hrApplyEntryStaffDTO.getHouseholdRegistration()));
        }
        if (hrApplyEntryStaffDTO.getStaffType() != null) {//员工类型
            hrApplyEntryStaffDTO.setStaffTypeLabel(StaffApplyStatusEnum.StaffTypeEnum.getValueByKey(hrApplyEntryStaffDTO.getStaffType()));
        }
        if (hrApplyEntryStaffDTO.getApplyStatus() != null) {//审批状态
            hrApplyEntryStaffDTO.setApplyStatusLabel(StaffApplyStatusEnum.AuditResultEnum.getValueByKey(hrApplyEntryStaffDTO.getApplyStatus()));
        }
        if (hrApplyEntryStaffDTO.getPayYear()!=null && hrApplyEntryStaffDTO.getPayMonthly()!=null){
            hrApplyEntryStaffDTO.setPaymentDate(hrApplyEntryStaffDTO.getPayYear() + "-" + hrApplyEntryStaffDTO.getPayMonthly());//缴费年月
        }
    }

    /**
     * 待入职员工审核拒绝
     * @param batchOptDTO
     * @return
     */
    @Override
    @Transactional
    public ResponseEntity approveReject(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = this.loginUserInfo();
        HrApplyEntryDTO hrApplyEntryDTO = this.hrApplyEntryRepository.getHrApplyEntryById(batchOptDTO.getApplyId());
        //查询申请审核待入职员工信息 全部
        List<HrApplyEntryStaffDTO> hrApplyEntryStaffDTOS = this.selectHrApplyEntryStaffList(batchOptDTO.getApplyId(),StaffApplyStatusEnum.AuditResultEnum.STAY_AUDIT.getKey(),batchOptDTO.getApplyStaffIdList());
        if (CollectionUtils.isNotEmpty(hrApplyEntryStaffDTOS)){
            for (HrApplyEntryStaffDTO hrApplyEntryStaffDTO : hrApplyEntryStaffDTOS) {
                HrApplyEntryStaff hrApplyEntryStaff = hrApplyEntryStaffMapper.toEntity(hrApplyEntryStaffDTO);
                hrApplyEntryStaff.setApplyStatus(StaffApplyStatusEnum.AuditResultEnum.AUDIT_REFUSE.getKey()).setStaffRemark(batchOptDTO.getStaffRemark());
                hrApplyEntryStaffRepository.updateById(hrApplyEntryStaff);
                //创建入职申请操作日志
                String message = jwtUserDTO.getRealName()+"拒绝了"+hrApplyEntryStaffDTO.getName()+"的入职申请。拒绝理由："+ batchOptDTO.getStaffRemark();
                this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyEntryDTO.getId(),hrApplyEntryStaff.getId(),jwtUserDTO.getId(),message+"。如有问题请联系公司的负责人员",null,0);
            }
       }
        //申请员工审核之后更新申请信息的审核状态、附件以及备注信息
        List<HrApplyEntryStaffDTO> applyEntryStaffList = this.selectHrApplyEntryStaffList(batchOptDTO.getApplyId(),null,null);
        if (CollectionUtils.isNotEmpty(applyEntryStaffList)){
            this.updateApplyEntry(batchOptDTO,jwtUserDTO,hrApplyEntryDTO,applyEntryStaffList,false);
        }
        Map<String, String> map = new HashMap<>();
        return ResponseUtil.buildSuccess(map);
    }

    /**
     * 待入职员工审批通过
     * @param batchOptDTO
     * @return
     */
    @Override
    @Transactional
    public ResponseEntity approvePassed(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = this.loginUserInfo();
        List<CodeTableDTO> monthlyPay = codeTableService.getCodeTableListByInnerName("monthlyPay");
        List<String> successList = new ArrayList<>();
        List<String> nameList = new ArrayList<>();//员工信息存在集合
        String message = "";
        HrApplyEntryDTO hrApplyEntryDTO = this.hrApplyEntryRepository.getHrApplyEntryById(batchOptDTO.getApplyId());
        //查询申请审核待入职员工信息 全部
        List<HrApplyEntryStaffDTO> hrApplyEntryStaffDTOS = this.selectHrApplyEntryStaffList(batchOptDTO.getApplyId(),StaffApplyStatusEnum.AuditResultEnum.STAY_AUDIT.getKey(),batchOptDTO.getApplyStaffIdList());
        if (CollectionUtils.isNotEmpty(hrApplyEntryStaffDTOS)){
            message = this.approvePassStaff(hrApplyEntryStaffDTOS, nameList, message, batchOptDTO, jwtUserDTO, monthlyPay,successList);
        }
        //申请员工审核之后更新申请信息的审核状态、附件以及备注信息
        List<HrApplyEntryStaffDTO> staffList = this.selectHrApplyEntryStaffList(batchOptDTO.getApplyId(), null, null);
        if (CollectionUtils.isNotEmpty(staffList)){
            this.updateApplyEntry(batchOptDTO,jwtUserDTO,hrApplyEntryDTO,staffList,true);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success",successList);
        if (CollectionUtils.isNotEmpty(nameList)){
            jsonObject.put("error_status","审批通过失败员工："+String.join(",", nameList)+"。失败原因："+ message );
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 更新申请信息数据
     * @param batchOptDTO 参数
     * @param jwtUserDTO 登录/审核 信息
     * @param hrApplyEntryDTO 申请信息
     * @param hrApplyEntryStaffDTOS 全部员工信息
     */
    private void updateApplyEntry(BatchOptDTO batchOptDTO, JWTUserDTO jwtUserDTO, HrApplyEntryDTO hrApplyEntryDTO, List<HrApplyEntryStaffDTO> hrApplyEntryStaffDTOS,Boolean opt) {
        //根据员工的审核结果计算申请的审批状态
        Boolean flag = this.calculateApplyStatus(hrApplyEntryDTO, hrApplyEntryStaffDTOS);
        hrApplyEntryRepository.updateById(hrApplyEntryMapper.toEntity(hrApplyEntryDTO));
        //添加操作日志
        String appendixId = null;
        if (batchOptDTO.getAppendixIdList() != null){
            appendixId= String.join(",",batchOptDTO.getAppendixIdList());
        }
        hrApplyOpLogsService.remove(new QueryWrapper<HrApplyOpLogs>().eq("apply_id",hrApplyEntryDTO.getId()).isNull("apply_staff_id"));
        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyEntryDTO.getId(),null,jwtUserDTO.getId(),batchOptDTO.getApplyRemark(),appendixId,0);
        if (flag){
            //创建通知配置
            String suffix = "";
            if (opt){
                suffix = "。等待专管员完善员工信息或者发送员工入职通知";
            }
            this.hrNotificationUserService.saveRemindContent(hrApplyEntryDTO.getClientId(),ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentEnum.MANAGER_REVIEW.getKey(),jwtUserDTO.getRealName()+"审核完成客户为 "+hrApplyEntryDTO.getClientName()+"在"+hrApplyEntryDTO.getCreatedDate()+"时发起的入职申请可通过入职申请页面查看详情"+suffix,jwtUserDTO.getId());
            hrUpcomingService.updateUpcoming(hrApplyEntryDTO.getId());
        }
    }

    /**
     * 赋值申请审核状态
     * @param hrApplyEntryDTO 申请信息
     * @param hrApplyEntryStaffDTOS 申请审核员工信息
     */
    public Boolean  calculateApplyStatus(HrApplyEntryDTO hrApplyEntryDTO, List<HrApplyEntryStaffDTO> hrApplyEntryStaffDTOS) {
        //员工中待审核的信息
        List<HrApplyEntryStaffDTO> stayAuditStaff = hrApplyEntryStaffDTOS.stream().filter(hrApplyEntryStaffDTO -> hrApplyEntryStaffDTO.getApplyStatus() == StaffApplyStatusEnum.AuditResultEnum.STAY_AUDIT.getKey()).collect(Collectors.toList());
        //员工中通过的信息
        List<HrApplyEntryStaffDTO> auditPassStaff = hrApplyEntryStaffDTOS.stream().filter(hrApplyEntryStaffDTO -> hrApplyEntryStaffDTO.getApplyStatus() == StaffApplyStatusEnum.AuditResultEnum.AUDIT_PASS.getKey()).collect(Collectors.toList());
        //员工中拒绝的信息
        List<HrApplyEntryStaffDTO> auditRefuseStaff = hrApplyEntryStaffDTOS.stream().filter(hrApplyEntryStaffDTO -> hrApplyEntryStaffDTO.getApplyStatus() == StaffApplyStatusEnum.AuditResultEnum.AUDIT_REFUSE.getKey()).collect(Collectors.toList());
        if (stayAuditStaff.size() != hrApplyEntryDTO.getStaffNum()){//已经存在审核的员工
            if ((auditPassStaff.size()+auditRefuseStaff.size()) == hrApplyEntryDTO.getStaffNum()){//已审核通过+已审核拒绝的员工数据等于申请员工总数 ，该申请已审核完成
                if (auditPassStaff.size() == 0){//全部审核完成没有通过的员工，申请状态为已拒绝
                    hrApplyEntryDTO.setApplyStatus(StaffEnum.ApplyStatusEnum.REJECTED.getKey());
                }
                if (auditRefuseStaff.size() == 0){//全部审核完成没有拒绝的员工，申请状态为已通过
                    hrApplyEntryDTO.setApplyStatus(StaffEnum.ApplyStatusEnum.ALREADY_PASSED.getKey());
                }
                if (auditPassStaff.size() > 0 && auditRefuseStaff.size() > 0){//全部审核完成 有通过的以及拒绝的，申请状态为部分通过
                    hrApplyEntryDTO.setApplyStatus(StaffEnum.ApplyStatusEnum.PORTION_PASSED.getKey());
                }
                return true;
            }else {//已经审核了一部分还有未审核的，申请状态为部分已审
                hrApplyEntryDTO.setApplyStatus(StaffEnum.ApplyStatusEnum.PORTION__REVIEW.getKey());
                return false;
            }
        }
        return false;
    }

    /**
     * 审核通过生成员工信息
     * @param hrApplyEntryStaffDTOS 待入职员工
     * @param nameList 审核失败员工集合
     * @param message 错误信息
     * @param batchOptDTO 参数
     * @param jwtUserDTO 登录人/审核人信息
     * @param monthlyPay 月薪区间
     */
    public String approvePassStaff(List<HrApplyEntryStaffDTO> hrApplyEntryStaffDTOS, List<String> nameList, String message, BatchOptDTO batchOptDTO, JWTUserDTO jwtUserDTO, List<CodeTableDTO> monthlyPay,List<String> successList) {
        for (HrApplyEntryStaffDTO hrApplyEntryStaffDTO : hrApplyEntryStaffDTOS) {
            HrApplyEntryStaff hrApplyEntryStaff = this.hrApplyEntryStaffMapper.toEntity(hrApplyEntryStaffDTO);
            if (!hrApplyEntryStaff.getApplyStatus().equals(StaffApplyStatusEnum.AuditResultEnum.STAY_AUDIT.getKey())){
                continue;
            }
            //审核通过--录入信息到员工信息 先校验是否在员工中存在
            HrTalentStaff talentStaff = hrTalentStaffRepository.selectOne(
                new QueryWrapper<HrTalentStaff>().eq("certificate_num", hrApplyEntryStaff.getCertificateNum()));
            String msg = this.checkIdNumAndPhone(batchOptDTO.getApplyId(),hrApplyEntryStaffDTO.getId(), jwtUserDTO, hrApplyEntryStaff);//校验身份证以及手机号
            if (msg != null){
                nameList.add(hrApplyEntryStaff.getName());
                message = msg;
                continue;
            }
            //生成系统编号
            String num = "SY"+System.currentTimeMillis();
            //根据身份证号码提取出生日期
            LocalDate birthday = ValidateUtil.splitBirthday(hrApplyEntryStaff.getCertificateNum());
            //不存在新创建一个员工信息
            if (talentStaff != null){
                //这条信息存在
                //判断是人才库还是员工库
                if (talentStaff.getIzDefault()){//人才库
                    this.copyStaffInfo(hrApplyEntryStaff,birthday,talentStaff);
                    this.hrTalentStaffRepository.updateHrTalentStaff(talentStaff);
                }else {//是员工
                    //判断该员工是否离职
                    if (talentStaff.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())
                        || talentStaff.getStaffStatus().equals(StaffEnum.StaffStatusEnum.RETIRE.getKey())){//员工之前已经离职或者已经退休
                        //在这条数据的基础上更新 //重新入职
                        this.copyStaffInfo(hrApplyEntryStaff,birthday,talentStaff);
                        this.hrTalentStaffRepository.updateHrTalentStaff(talentStaff);
                        //更新在职信息为工作经历
                        List<HrStaffWorkExperience> experienceList = hrStaffWorkExperienceRepository.selectList(new QueryWrapper<HrStaffWorkExperience>().eq("staff_id", talentStaff.getId()).eq("iz_default", "1"));
                        if (!experienceList.isEmpty()){
                            HrStaffWorkExperience workExperience = experienceList.get(0).setIzDefault(false);//更新为之前公司
                            hrStaffWorkExperienceRepository.updateById(workExperience);
                        }
                        //删除之前的薪酬参数
//                        hrStaffEmolumentRepository.delete(new QueryWrapper<HrStaffEmolument>().eq("staff_id",talentStaff.getId()));
                    }else {
                        nameList.add(hrApplyEntryStaff.getName());
                        this.createApplyOpLogs(hrApplyEntryStaff.getApplyId(),jwtUserDTO.getId(),jwtUserDTO.getRealName()+"审核通过该申请，审核失败。失败信息："+talentStaff.getName()+"已经在员工库中存在");
                        message = "员工信息已存在";
                        continue;
                    }
                }
            }else {
                talentStaff = new HrTalentStaff();
                this.copyStaffInfo(hrApplyEntryStaff,birthday,talentStaff);
                talentStaff.setSystemNum(num);
                this.hrTalentStaffRepository.insert(talentStaff);
            }
            hrApplyEntryStaff.setEntryStatus(ApprovalEntryStatusEnum.EntryStatus.TO_BE_HIRED.getKey())
                .setApplyStatus(StaffApplyStatusEnum.AuditResultEnum.AUDIT_PASS.getKey())
                .setApplyCheckerId(jwtUserDTO.getId()).setStaffId(talentStaff.getId());
            this.hrApplyEntryStaffRepository.updateById(hrApplyEntryStaff);
            //添加适配岗位
            HrStaffStation hrStaffStation = new HrStaffStation();
            hrStaffStation.setStaffId(talentStaff.getId()).setStationId(hrApplyEntryStaff.getStationId());
            hrStaffStationRepository.insert(hrStaffStation);

            //创建在职公司信息
            HrStaffWorkExperience workExperience = new HrStaffWorkExperience();
            if (hrApplyEntryStaff.getBasicWage() != null){
                Integer salarySection = CalculateUtils.figureBasicWage(hrApplyEntryStaff.getBasicWage(),monthlyPay);
                if (salarySection != null){
                    workExperience.setSalarySection(salarySection);
                }
            }
            workExperience.setClientId(talentStaff.getClientId()).setStaffId(talentStaff.getId()).setPersonnelType(talentStaff.getPersonnelType())
                .setStationId(hrApplyEntryStaff.getStationId()).setBoardDate(hrApplyEntryStaff.getContractStartDate()).setIzDefault(true)
                .setIzInsured(StaffEnum.InsuredStatusEnum.UNINSURED.getKey());
            hrStaffWorkExperienceRepository.insert(workExperience);
            //查询之前是否存在信息，存在更新，不存在新增
            HrStaffEmolument staffEmolument = hrStaffEmolumentRepository.getByStaffId(talentStaff.getId());
            if (staffEmolument == null){
                HrStaffEmolument hrStaffEmolument = new HrStaffEmolument();
                BeanUtils.copyProperties(hrApplyEntryStaff,hrStaffEmolument);
                hrStaffEmolument.setStaffId(talentStaff.getId()).setId(null);
                hrStaffEmolumentRepository.insert(hrStaffEmolument);
            }else {
                String emolumentId = staffEmolument.getId();
                BeanUtils.copyProperties(hrApplyEntryStaff,staffEmolument);
                staffEmolument.setStaffId(talentStaff.getId()).setId(emolumentId);
                hrStaffEmolumentRepository.updateById(staffEmolument);
            }

            //创建未生效的劳动合同
            HrClient hrClient = this.hrClientRepository.selectById(talentStaff.getClientId());
            HrContract hrContract = new HrContract();
            hrContract.setClientId(talentStaff.getClientId()).setUnitNumber(hrClient.getUnitNumber()).setClientName(hrClient.getClientName()).setStaffId(talentStaff.getId()).setSystemNum(talentStaff.getSystemNum())
                .setStaffName(talentStaff.getName()).setIdNo(talentStaff.getCertificateNum()).setPhone(talentStaff.getPhone()).setContractStartDate(hrApplyEntryStaff.getContractStartDate()).setContractEndDate(hrApplyEntryStaff.getContractEndDate());
            this.hrContractRepository.insert(hrContract);
            //创建审批通过的审核明细
            successList.add(hrApplyEntryStaff.getName());
            //创建申请操作明细
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(batchOptDTO.getApplyId(),hrApplyEntryStaff.getId(),jwtUserDTO.getId(),jwtUserDTO.getRealName()+"通过了"+hrApplyEntryStaff.getName()+"的入职申请。",null,0);
            hrUpcomingService.createServiceUpcoming(hrApplyEntryStaff.getId(),hrApplyEntryStaff.getStaffId(), "入职服务-为待入职员工-" + hrApplyEntryStaff.getName() + "完善员工信息或者发送员工入职通知",LocalDate.now(),0);
        }
        return message;
    }

    /**
     * 复制员工信息
     * @param hrApplyEntryStaff
     * @param birthday
     * @param talentStaff
     */
    private void copyStaffInfo(HrApplyEntryStaff hrApplyEntryStaff, LocalDate birthday, HrTalentStaff talentStaff) {
        talentStaff.setClientId(hrApplyEntryStaff.getClientId())
            .setProtocolId(hrApplyEntryStaff.getProtocolId())
            .setName(hrApplyEntryStaff.getName())
            .setCertificateNum(hrApplyEntryStaff.getCertificateNum())
            .setCertificateType(CertificateTypeEnum.CertificateType.RESIDENT_IDENTIFICATION_CARD.getKey())
            .setSex(hrApplyEntryStaff.getSex())
            .setStaffStatus(StaffEnum.StaffStatusEnum.TO_BE_HIRED.getKey())
            .setPhone(hrApplyEntryStaff.getPhone())
            .setPersonnelType(hrApplyEntryStaff.getPersonnelType())
            .setBirthday(birthday)
            .setApplyStaffId(hrApplyEntryStaff.getId())
            .setHouseholdRegistration(hrApplyEntryStaff.getHouseholdRegistration())
            .setIzInsured(StaffEnum.InsuredStatusEnum.UNINSURED.getKey())
            .setIzStartEnd(StaffEnum.IzStartEndEnum.TO_BE_INFO_FILLED.getKey())
            .setIzPreserve(0)
            .setStatus(false)
            .setIzDefault(false)
            .setRenewalProcess(null)
            .setIsRenewalContract(null)
            .setRenewalServiceId(null)
            .setDepartureStaffId(null)
            .setResignationDate(null)
            .setSupplementaryPayment(hrApplyEntryStaff.getSupplementaryPayment())
        ;
    }

    /**
     * 校验身份证以及手机号
     * @param applyId 入职申请ID
     * @param applyStaffId 申请员工ID
     * @param jwtUserDTO 登录人
     * @param hrApplyEntryStaff 申请信息
     */
    private String checkIdNumAndPhone(String applyId, String applyStaffId, JWTUserDTO jwtUserDTO, HrApplyEntryStaff hrApplyEntryStaff) {
        if (hrApplyEntryStaff.getPhone() != null) {
            List<HrTalentStaff> hrTalentStaffs = hrTalentStaffRepository.selectList(new QueryWrapper<HrTalentStaff>().eq("phone", hrApplyEntryStaff.getPhone())
                        .notIn("staff_status",StaffEnum.StaffStatusEnum.SEPARATION.getKey(),StaffEnum.StaffStatusEnum.RETIRE.getKey()));
            if (CollectionUtils.isNotEmpty(hrTalentStaffs)) {
                boolean flag = false;
                for (HrTalentStaff hrTalentStaff : hrTalentStaffs) {
                    if (hrTalentStaff.getIzDefault()){//是人才
                        if (!hrTalentStaff.getCertificateNum().equals(hrApplyEntryStaff.getCertificateNum())){
                            flag = true;
                        }
                    }else {
                        flag = true;
                        break;
                    }
                }
                if (flag){
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(applyId, applyStaffId, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "审核通过" + hrApplyEntryStaff.getName() + "的入职申请，审核失败。失败信息：手机号码已存在", null, 0);
                    return "手机号码已存在";
                }
            }
        }
        // 实名认证
        /*String authMsg = this.hrRealNameAuthService.authNameCertificateNum(hrApplyEntryStaff.getName(), hrApplyEntryStaff.getCertificateNum());
        if (StringUtils.isNotBlank(authMsg)) {
            return authMsg;
        }*/
        return null;
    }

    /**
     * 入职流程
     * @param applyStaffIdS 待入职员工ID
     * @return
     */
    @Override
    @Transactional
    public ResponseEntity entryLevel(List<String> applyStaffIdS) {
        JWTUserDTO jwtUserDTO = this.loginUserInfo();
        JSONObject jsonObject = new JSONObject();
        List<String> errorList = new ArrayList<>();
        for (String applyStaffId : applyStaffIdS) {
            //修改审批状态为入职中  根据员工ID查询对应的申请员工ID，查询待入职的
            HrApplyEntryStaff hrApplyEntryStaff = this.hrApplyEntryStaffRepository.selectOne(new QueryWrapper<HrApplyEntryStaff>().eq("id",applyStaffId));
            if (!hrApplyEntryStaff.getEntryStatus().equals(ApprovalEntryStatusEnum.EntryStatus.TO_BE_HIRED.getKey())){
                errorList.add(hrApplyEntryStaff.getName());
                continue;
            }
            HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrApplyEntryStaff.getStaffId());
            //查询公司名称以及岗位名称
            HrClient hrClient = this.hrClientRepository.selectById(hrApplyEntryStaff.getClientId());
            HashMap<Integer, String> params = new HashMap<>();
            params.put(1, hrTalentStaff.getName());
            params.put(2, hrClient.getClientName());
            /*params.put(3, LocalDate.now().plusDays(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            params.put(4, site);
            params.put(5, CompanyInfoEnum.FIRST_PART_PHONE.getValue()+","+ CompanyInfoEnum.FIRST_PART_PHONE_TWO.getValue());*/
            String sendMessage = this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.ENTRY_SMS_CODE.getTemplateCode(), hrTalentStaff.getPhone());

            if (sendMessage.equalsIgnoreCase(Constants.SEND_EXCEPTION)){//发送成功
                jsonObject.put("message",Constants.SEND_EXCEPTION);
                this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyEntryStaff.getApplyId(),applyStaffId,jwtUserDTO.getId(),jwtUserDTO.getRealName()+"向 "+hrTalentStaff.getName()+" 发送了入职短信通知。发送失败，失败原因："+Constants.SEND_EXCEPTION,null,0);
                continue;
            }
            String content = hrSmsTemplateService.smsTemplateContent(params, MessageTemplateEnum.ENTRY_SMS_CODE.getTemplateCode());
            String message = jwtUserDTO.getRealName()+"向 "+hrTalentStaff.getName()+" 发送了入职短信通知。短信模板："+ content;
            String msg =  "恭喜您已被"+hrClient.getClientName()+"录用，请尽快提交入职信息";
            JSONObject object = new JSONObject();
            object.put("entry",ApprovalEntryStatusEnum.EntryStatus.COMMIT_MESSAGE.getKey());
            object.put("message",msg);
            //申请操作日志
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyEntryStaff.getApplyId(),applyStaffId,jwtUserDTO.getId(),message+"####"+object.toString(),null,0);
            hrApplyEntryStaff.setApplyCheckerId(jwtUserDTO.getId()).setEntryStatus(ApprovalEntryStatusEnum.EntryStatus.COMMIT_MESSAGE.getKey()).setApplyStep(ApprovalEntryStatusEnum.ApplyStep.SEND_ENTRY_NOTICE.getKey());
            this.hrApplyEntryStaffRepository.updateById(hrApplyEntryStaff);
            //查找对应的员工
            hrTalentStaff.setStaffStatus(StaffEnum.StaffStatusEnum.ENROLLING.getKey()).setIzStartEnd(StaffEnum.IzStartEndEnum.TO_BE_INFO_FILLED.getKey());
            this.hrTalentStaffRepository.updateById(hrTalentStaff);
            this.hrNotificationUserService.saveRemindContent(hrApplyEntryStaff.getClientId(),ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentEnum.INITIATE_INDUCTION.getKey(),jwtUserDTO.getRealName()+"向员工"+hrApplyEntryStaff.getName()+"发送了入职通知",jwtUserDTO.getId());
            hrUpcomingService.createServiceUpcoming(hrApplyEntryStaff.getId(),hrApplyEntryStaff.getStaffId(), "入职服务-完善" + hrTalentStaff.getName() + "的入职信息",LocalDate.now(),0);
            hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(),hrApplyEntryStaff.getId(),hrApplyEntryStaff.getStaffId(),msg,false,null);
        }
        if (CollectionUtils.isNotEmpty(errorList)){
            jsonObject.put("errorMessage","选择的数据中"+String.join(",",errorList)+"不是待入职状态");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 生成申请操作日志--小程序端
     * @param applyId 申请ID
     * @param checkerId 操作人ID
     * @param message 操作内容
     * @param checkerType true
     */
    private void createApplyOpLogsMini(String applyId, String checkerId, String message, Boolean checkerType) {
        HrApplyOpLogs hrApplyOpLogs = new HrApplyOpLogs();
        hrApplyOpLogs.setApplyId(applyId);
        hrApplyOpLogs.setCheckerId(checkerId);
        hrApplyOpLogs.setMessage(message);
        hrApplyOpLogs.setCheckerType(checkerType);
        this.hrApplyOpLogsRepository.insert(hrApplyOpLogs);
    }

    /**
     * 生成申请操作日志--PC端
     * @param applyId 申请ID
     * @param checkerId 操作人ID
     * @param message 操作内容
     */
    private void createApplyOpLogs(String applyId, String checkerId, String message) {
        HrApplyOpLogs hrApplyOpLogs = new HrApplyOpLogs();
        hrApplyOpLogs.setApplyId(applyId);
        if (checkerId != null){
            hrApplyOpLogs.setCheckerId(checkerId);
        }
        hrApplyOpLogs.setMessage(message);
        hrApplyOpLogsRepository.insert(hrApplyOpLogs);

    }

    /**
     * 统计每月入职数量
     * @param particularYear 年份
     * @return
     */
    @Override
    public List<Map<String, Object>> entryCountByYear(String particularYear) {
        List<Map<String, Object>> mapList = this.hrApplyEntryStaffRepository.entryCountByYear(particularYear);
        log.info("入职数量 {}",mapList);
        return mapList;
    }

    /**
     * 入职资料-提交入职资料
     * @param id 员工ID
     */
    @Override
    @Transactional
    public ResponseEntity submitStaffBasicInfo(String id) {
        JWTMiniDTO user = SecurityUtils.getCurrentMini().get();
        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(id);
        //对应的申请信息
        HrApplyEntryStaff hrApplyEntryStaff = this.hrApplyEntryStaffRepository.selectById(hrTalentStaff.getApplyStaffId());
        String message = "";
        Boolean flag = false;
        //员工状态是入职中已经提交了入职资料，专管员已经确认信息通过时不可提交
        if (hrTalentStaff.getStaffStatus() == StaffEnum.StaffStatusEnum.ENROLLING.getKey() && hrTalentStaff.getIzStartEnd() == StaffEnum.IzStartEndEnum.INFO_FILLED.getKey()
            && hrApplyEntryStaff.getEntryStatus().equals(ApprovalEntryStatusEnum.EntryStatus.SEND_CONTRACT.getKey())){
            message = hrTalentStaff.getName()+"通过小程序提交入职资料，无法提交。失败原因："+hrTalentStaff.getName()+"已经通过专管员确认信息已通过不可提交";
        }else {
            //提交成功，等待专管员确认信息
            hrTalentStaff.setIzStartEnd(StaffEnum.IzStartEndEnum.CONFIRMATION_MESSAGE.getKey()).setIzPreserve(StaffEnum.IzPreserveEnum.ALREADY_SUBMIT.getKey());//0:未保存 1:已保存 2:已提交
            hrTalentStaffRepository.updateById(hrTalentStaff);
            //更新申请列表入职流程状态
            hrApplyEntryStaff.setApplyCheckerId(id).setEntryStatus(ApprovalEntryStatusEnum.EntryStatus.CONFIRMATION_MESSAGE.getKey()).setApplyStep(ApprovalEntryStatusEnum.ApplyStep.INPUT_INFORMATION.getKey());
            this.hrApplyEntryStaffRepository.updateById(hrApplyEntryStaff);
            message = hrTalentStaff.getName()+"通过小程序提交了入职资料。等待专管员确认信息";
            flag = true;
        }
        //申请操作日志
        this.hrApplyOpLogsService.saveHrApplyOpLogsMiniPhaseTWO(hrApplyEntryStaff.getApplyId(),hrApplyEntryStaff.getId(),user.getId(),message,true,0);
        this.hrNotificationUserService.saveRemindContent(hrApplyEntryStaff.getClientId(),ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentEnum.SUBMIT_INFORMATION.getKey(),user.getName()+"通过小程序提交了入职资料具体详情可通过入职申请页面查看。等待专管员确认信息",user.getId());
        hrUpcomingService.createServiceUpcoming(hrApplyEntryStaff.getId(),hrApplyEntryStaff.getStaffId(), "入职服务-确认" + hrTalentStaff.getName() + "的入职信息",LocalDate.now(),0);
        return ResponseUtil.buildSuccess(flag);
    }

    /**
     * 完善信息流程
     * @param applyStaffId 待入职员工ID
     * @return
     */
    @Override
    public ResponseEntity completeInForApplyStaff(String applyStaffId) {
        JWTUserDTO jwtUserDTO = this.loginUserInfo();
        boolean flag = false;
        //查询对应的申请ID
        HrApplyEntryStaff applyEntryStaff = this.hrApplyEntryStaffRepository.selectOne(
            new QueryWrapper<HrApplyEntryStaff>().eq("id",applyStaffId));
        HrTalentStaffDTO staffInfo = hrTalentStaffRepository.getStaffBasicInfo(applyEntryStaff.getStaffId());
        if (staffInfo.getName()==null || staffInfo.getNationality()==null || staffInfo.getCertificateType()==null
            || staffInfo.getCertificateNum()==null || staffInfo.getBirthday()==null || staffInfo.getPhone()==null){
            flag = true;//还有必填信息没有填写完成
        }else {
            //必填信息全部填写完成，状态更新为待发合同 流程更新为员工录入信息
            applyEntryStaff.setEntryStatus(ApprovalEntryStatusEnum.EntryStatus.SEND_CONTRACT.getKey()).setApplyStep(ApprovalEntryStatusEnum.ApplyStep.INPUT_INFORMATION.getKey());
            hrApplyEntryStaffRepository.updateById(applyEntryStaff);

            staffInfo.setIzStartEnd(StaffEnum.IzStartEndEnum.CONFIRMATION_MESSAGE.getKey()).setIzPreserve(StaffEnum.IzPreserveEnum.ALREADY_SUBMIT.getKey()).setStaffStatus(StaffEnum.StaffStatusEnum.ENROLLING.getKey());
            hrTalentStaffRepository.updateById(hrTalentStaffMapper.toEntity(staffInfo));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("entryStatus",ApprovalEntryStatusEnum.EntryStatus.SEND_CONTRACT.getKey());
            jsonObject.put("message","您的个人信息已通过审核，请等待企业方制作劳动合同");
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(applyEntryStaff.getApplyId(),applyEntryStaff.getId(),jwtUserDTO.getId(),jwtUserDTO.getRealName()+"完善信息完成。####"+jsonObject.toString(),null,0);
            this.hrNotificationUserService.saveRemindContent(applyEntryStaff.getClientId(),ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentEnum.PERFECT_INFORMATION.getKey(),jwtUserDTO.getRealName()+"完善"+applyEntryStaff.getName()+"信息完成具体详情可通过入职申请页面查看。等待专管员制作电子合同或者录入劳动合同",jwtUserDTO.getId());
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(),applyEntryStaff.getId(),applyEntryStaff.getStaffId(),"您的个人信息已通过审核，请等待企业方制作劳动合同",false,null);
            hrUpcomingService.createServiceUpcoming(applyEntryStaff.getId(),applyEntryStaff.getStaffId(), "入职服务-为" + staffInfo.getName() + "制作入职电子合同或者录入劳动合同",LocalDate.now(),0);
        }
        //创建操作日志
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("identification",flag);
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 确认信息流程
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity notarizeInForApplyStaff(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = this.loginUserInfo();
        List<String> successList = new ArrayList<>();
        List<String> errorStatusList = new ArrayList<>();
        List<String> errorInForList = new ArrayList<>();
        String message = "";
        for (String applyStaffId : batchOptDTO.getApplyStaffIdList()) {
            HrApplyEntryStaff applyEntryStaff = this.hrApplyEntryStaffRepository.selectOne(
                new QueryWrapper<HrApplyEntryStaff>().eq("id",applyStaffId));
            if (!applyEntryStaff.getEntryStatus().equals(ApprovalEntryStatusEnum.EntryStatus.CONFIRMATION_MESSAGE.getKey())){
                errorStatusList.add(applyEntryStaff.getName());
                continue;
            }
            HrTalentStaffDTO staffInfo = hrTalentStaffRepository.getStaffBasicInfo(applyEntryStaff.getStaffId());
            JSONObject jsonObject = new JSONObject();
            if (batchOptDTO.getOpt()){//确认信息
                //判断基本信息是否完整
                if (staffInfo.getName()==null || staffInfo.getNationality()==null || staffInfo.getCertificateType()==null
                    || staffInfo.getCertificateNum()==null || staffInfo.getBirthday()==null || staffInfo.getPhone()==null){
                    errorInForList.add(applyEntryStaff.getName());
                    continue;
                }
                //入职流程更新为待发合同 入职步骤更新为发起劳动合同
                jsonObject.put("entryStatus",ApprovalEntryStatusEnum.EntryStatus.SEND_CONTRACT.getKey());
                jsonObject.put("message","您的个人信息已通过审核，请等待企业方制作劳动合同");
                applyEntryStaff.setEntryStatus(ApprovalEntryStatusEnum.EntryStatus.SEND_CONTRACT.getKey()).setApplyStep(ApprovalEntryStatusEnum.ApplyStep.INITIATE_CONTRACT.getKey());
                staffInfo.setIzStartEnd(StaffEnum.IzStartEndEnum.CONFIRMATION_MESSAGE.getKey()).setIzPreserve(StaffEnum.IzPreserveEnum.NOTARIZE_INFO.getKey());
                message = jwtUserDTO.getRealName()+"确认信息是否正确时选择了是，已确认信息通过。";
                hrUpcomingService.createServiceUpcoming(applyEntryStaff.getId(),applyEntryStaff.getStaffId(), "入职服务-为" + staffInfo.getName() + "制作入职电子合同或者录入劳动合同",LocalDate.now(),0);
            }else {
                jsonObject.put("entryStatus",ApprovalEntryStatusEnum.EntryStatus.CONFIRMATION_MESSAGE.getKey());
                jsonObject.put("message","您的个人信息审核失败。"+jwtUserDTO.getRoleName()+"向您提出修改意见：" + batchOptDTO.getErrorMessage());
                applyEntryStaff.setEntryStatus(ApprovalEntryStatusEnum.EntryStatus.COMMIT_MESSAGE.getKey()).setApplyStep(ApprovalEntryStatusEnum.ApplyStep.INPUT_INFORMATION.getKey());
                staffInfo.setIzStartEnd(StaffEnum.IzStartEndEnum.INFO_FILLED.getKey()).setIzPreserve(StaffEnum.IzPreserveEnum.CONFIRMATION_FAILED.getKey());//0:未保存 1:已保存 2:已提交
                message = jwtUserDTO.getRealName()+"确认信息是否正确时选择了否，修改意见：" + batchOptDTO.getErrorMessage();
            }
            successList.add(applyEntryStaff.getName());
            hrApplyEntryStaffRepository.updateById(applyEntryStaff);
            hrTalentStaffRepository.updateById(hrTalentStaffMapper.toEntity(staffInfo));
            hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(applyEntryStaff.getApplyId(),applyEntryStaff.getId(),jwtUserDTO.getId(), message+"####"+jsonObject.toString() ,null,0);
            this.hrNotificationUserService.saveRemindContent(applyEntryStaff.getClientId(),ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentEnum.INFORMATION_CONFIRMATION.getKey(),jwtUserDTO.getRealName()+"进行确认员工为"+applyEntryStaff.getName()+"的信息。具体详情可通过入职申请页面查看",jwtUserDTO.getId());
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(),applyEntryStaff.getId(),applyEntryStaff.getStaffId(),String.valueOf(jsonObject.get("message")),false,null);
        }

        Map<String, String> map = new HashMap<>();
        map.put("success_num",String.valueOf(successList.size()));
        map.put("error_num",String.valueOf(errorInForList.size()+errorStatusList.size()));
        String failureStaff ="";
        String failureMessage ="";
        if (CollectionUtils.isNotEmpty(errorStatusList)){
            failureStaff += String.join(",", errorStatusList);
            failureMessage += "选择的数据中"+failureStaff+"不是需要确认信息的流程。";
        }
        if (CollectionUtils.isNotEmpty(errorInForList)){
            failureStaff += ","+String.join(",", errorInForList);
            failureMessage += failureStaff+"基本信息以及在职信息不完整，无法确认通过。";
        }
        if (failureStaff.startsWith(",")){
            //判断第一个字符是不是 , 是则去除第一个字符、
            failureStaff = failureStaff.substring(1);
        }
        map.put("failure_staff", failureStaff);
        map.put("failure_message",failureMessage);
        return ResponseUtil.buildSuccess(map);
    }

    /**
     * 查询申请信息对应的待入职员工信息
     * @param applyId 申请ID
     * @param pendingReview 待审批
     * @return
     */
    @Override
    public List<HrApplyEntryStaffDTO> selectHrApplyEntryStaffList(String applyId,Integer pendingReview,List<String> applyStaffId) {
        List<HrApplyEntryStaffDTO> hrApplyEntryStaffDTOS = hrApplyEntryStaffRepository.selectHrApplyEntryStaffList(applyId,pendingReview,applyStaffId);
        Map<Integer, String> businessType = codeTableService.findCodeTableByInnerName("businessType");//业务类型
        Map<Integer, String> residenceType = codeTableService.findCodeTableByInnerName("residenceType");//户口性质
        hrApplyEntryStaffDTOS.forEach(ls->{this.setDict(ls,businessType,residenceType);});
        return hrApplyEntryStaffDTOS;
    }

    /**
     * 导出失败员工列表
     *
     * @param applyId
     * @param response
     * @return
     */
    @Override
    public String exportFailureStaff(String applyId, HttpServletResponse response) {
        List<HrApplyEntryStaffFailExport> hrApplyEntryStaffDTOS = hrApplyEntryStaffRepository.getHrApplyEntryStaffList(applyId);
//        hrApplyEntryStaffDTOS.forEach(this::setPayment);
        if (hrApplyEntryStaffDTOS.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        int listSize = hrApplyEntryStaffDTOS.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(hrApplyEntryStaffDTOS, "失败员工信息", HrApplyEntryStaffFailExport.class);
        List<String> ids = new ArrayList<>();
        ids.add(applyId);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.CREATE_APPLICATION.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 导出待入职员工列表
     *
     * @param hrApplyEntryStaffDTO 待入职员工ID
     * @param response
     * @return
     */
    @Override
    public String exportToHiredStaff(HrApplyEntryStaffDTO hrApplyEntryStaffDTO, HttpServletResponse response) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrApplyEntryStaffFailExport> hrApplyEntryStaffDTOS = hrApplyEntryStaffRepository.exportToHiredStaff(hrApplyEntryStaffDTO,clientIds);
        if (hrApplyEntryStaffDTOS.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        int listSize = hrApplyEntryStaffDTOS.size();
        List<String> ids = hrApplyEntryStaffDTOS.stream().map(HrApplyEntryStaffFailExport::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(hrApplyEntryStaffDTOS, "入职员工信息", HrApplyEntryStaffFailExport.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.CREATE_APPLICATION.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    private void setPayment(HrApplyEntryStaffFailExport hrApplyEntryStaffFailExport) {
        if (hrApplyEntryStaffFailExport.getPayYear()!=null && hrApplyEntryStaffFailExport.getPayMonthly()!=null){
            hrApplyEntryStaffFailExport.setPaymentDate(hrApplyEntryStaffFailExport.getPayYear()+"-"+hrApplyEntryStaffFailExport.getPayMonthly());
        }
    }

    /**
     * 当前登录人信息
     * @return 返回结果
     */
    private JWTUserDTO loginUserInfo() {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        if (jwtUserDTO.getRealName() == null){
            jwtUserDTO.setRealName("");
        }
        return jwtUserDTO;
    }
}
