<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrSealsRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sign_id, seal_name, seal_url, seal_type, is_delete, created_by, last_modified_by, created_date, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrSeals">
        <id column="id" property="id"/>
        <result column="seal_name" property="sealName"/>
        <result column="sign_id" property="signId"/>
        <result column="seal_url" property="sealUrl"/>
        <result column="seal_type" property="sealType"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getEffectiveSealses" resultType="cn.casair.dto.HrSealsDTO">
        SELECT * FROM hr_seals WHERE is_delete = 0 AND sign_id IS NOT NULL AND seal_url != ''
    </select>

    <select id="getEnterpriseOfficialSeal" resultType="cn.casair.domain.HrSeals">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_seals
        WHERE is_delete = 0
          AND seal_type = 0
          AND seal_name = '企业公章'
        order by created_date desc
        limit 1
    </select>

    <select id="getSealses" resultType="cn.casair.dto.HrSealsDTO">
        SELECT *
        FROM `hr_seals`
        where is_delete = 0
    </select>

    <update id="delectSeals">
        UPDATE hr_seals
        SET seal_url=''
        WHERE id = #{id}
          AND is_delete = 0
    </update>

    <select id="selectSeals" resultType="int">
        SELECT COUNT(id)
        from hr_seals
        WHERE seal_name = #{sealName}
          AND is_delete = 0
    </select>

    <select id="getOfficialSeal" resultType="cn.casair.dto.formdata.SelectionOption">
        SELECT sign_id `value`,
               seal_name label
        FROM hr_seals
        WHERE is_delete = 0
          AND sign_id IS NOT NULL
        ORDER BY seal_type,
                 created_date
    </select>

    <select id="getOfficialSealCondition" resultType="cn.casair.dto.formdata.SelectionOption">
        SELECT sign_id `value`,
               seal_name label
        FROM hr_seals
        WHERE is_delete = 0
          AND sign_id IS NOT NULL
          AND seal_name IN
        <foreach collection="list" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        ORDER BY seal_type,
                 created_date
    </select>

</mapper>
