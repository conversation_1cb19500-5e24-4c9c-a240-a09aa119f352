<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.SysDictTypeRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, dict_name, dict_type, state, remark,
created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date                    ,is_delete
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.SysDictType">
                    <id column="id" property="id"/>
                    <result column="dict_name" property="dictName"/>
                    <result column="dict_type" property="dictType"/>
                    <result column="state" property="state"/>
                    <result column="remark" property="remark"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
                <result column="is_delete" property="isDelete"/>
        </resultMap>

</mapper>
