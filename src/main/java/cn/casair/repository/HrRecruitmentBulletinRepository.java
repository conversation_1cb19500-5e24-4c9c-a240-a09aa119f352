package cn.casair.repository;

import cn.casair.domain.HrRecruitmentBulletin;
import cn.casair.dto.HrRecruitmentBulletinDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 发布公告数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Repository
public interface HrRecruitmentBulletinRepository extends BaseMapper<HrRecruitmentBulletin> {

    HrRecruitmentBulletinDTO findById(String id);

    /**
     * 查询公告列表
     *
     * @param recruitmentBrochureId
     * @return
     */
    List<HrRecruitmentBulletin> selectByRecruitmentBrochureId(@Param("recruitmentBrochureId") String recruitmentBrochureId);
}
