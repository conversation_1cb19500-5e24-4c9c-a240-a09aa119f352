<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffSignRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_id, sign_id, appendix_id,
        is_delete,created_by,created_date,last_modified_by,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffSign">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="sign_id" property="signId"/>
        <result column="appendix_id" property="appendixId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectListByStaffId" resultType="cn.casair.dto.HrStaffSignDTO">
        SELECT
            ss.*,
            ha.file_url signImg
        FROM hr_staff_sign ss
        LEFT JOIN hr_appendix ha ON ss.appendix_id = ha.id
        WHERE ss.is_delete = 0
          AND ss.staff_id = #{staffId}
        ORDER BY ss.created_date DESC
    </select>
</mapper>
