package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.BillEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.FileUtil;
import cn.casair.common.utils.WordUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.dto.HrAppendixDTO;
import cn.casair.dto.HrDataDetailDTO;
import cn.casair.dto.HrRecruitmentBrochureDTO;
import cn.casair.service.HrAppendixService;
import cn.casair.service.SysOperLogService;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrBillDetailGrant;
import cn.casair.dto.HrBillDetailGrantDTO;
import cn.casair.repository.HrBillDetailGrantRepository;
import cn.casair.mapper.HrBillDetailGrantMapper;
import cn.casair.service.HrBillDetailGrantService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Optional;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 工资发放状态记录服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrBillDetailGrantServiceImpl extends ServiceImpl<HrBillDetailGrantRepository, HrBillDetailGrant> implements HrBillDetailGrantService {

    private final HrBillDetailGrantRepository hrBillDetailGrantRepository;
    private final HrBillDetailGrantMapper hrBillDetailGrantMapper;
    private final SysOperLogService sysOperLogService;
    private final HrAppendixService hrAppendixService;
    @Value("${file.temp-path}")
    private String tempPath;

    /**
     * 创建工资发放状态记录
     *
     * @param hrBillDetailGrantDTO
     * @return
     */
    @Override
    public HrBillDetailGrantDTO createHrBillDetailGrant(HrBillDetailGrantDTO hrBillDetailGrantDTO) {
        log.info("Create new HrBillDetailGrant:{}", hrBillDetailGrantDTO);

        HrBillDetailGrant hrBillDetailGrant = this.hrBillDetailGrantMapper.toEntity(hrBillDetailGrantDTO);
        this.hrBillDetailGrantRepository.insert(hrBillDetailGrant);
        return this.hrBillDetailGrantMapper.toDto(hrBillDetailGrant);
    }

    /**
     * 修改工资发放状态记录
     *
     * @param hrBillDetailGrantDTO
     * @return
     */
    @Override
    public Optional<HrBillDetailGrantDTO> updateHrBillDetailGrant(HrBillDetailGrantDTO hrBillDetailGrantDTO) {
        return Optional.ofNullable(this.hrBillDetailGrantRepository.selectById(hrBillDetailGrantDTO.getId()))
            .map(roleTemp -> {
                HrBillDetailGrant hrBillDetailGrant = this.hrBillDetailGrantMapper.toEntity(hrBillDetailGrantDTO);
                this.hrBillDetailGrantRepository.updateById(hrBillDetailGrant);
                log.info("Update HrBillDetailGrant:{}", hrBillDetailGrantDTO);
                return hrBillDetailGrantDTO;
            });
    }

    /**
     * 查询工资发放状态记录详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillDetailGrantDTO getHrBillDetailGrant(String id) {
        log.info("Get HrBillDetailGrant :{}", id);

        HrBillDetailGrant hrBillDetailGrant = this.hrBillDetailGrantRepository.selectById(id);
        return this.hrBillDetailGrantMapper.toDto(hrBillDetailGrant);
    }

    /**
     * 删除工资发放状态记录
     *
     * @param id
     */
    @Override
    public void deleteHrBillDetailGrant(String id) {
        Optional.ofNullable(this.hrBillDetailGrantRepository.selectById(id))
            .ifPresent(hrBillDetailGrant -> {
                this.hrBillDetailGrantRepository.deleteById(id);
                log.info("Delete HrBillDetailGrant:{}", hrBillDetailGrant);
            });
    }

    /**
     * 批量删除工资发放状态记录
     *
     * @param ids
     */
    @Override
    public void deleteHrBillDetailGrant(List<String> ids) {
        log.info("Delete HrBillDetailGrants:{}", ids);
        this.hrBillDetailGrantRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询工资发放状态记录
     *
     * @param hrBillDetailGrantDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillDetailGrantDTO hrBillDetailGrantDTO, Long pageNumber, Long pageSize) {
        Page<HrBillDetailGrant> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrBillDetailGrant> qw = new QueryWrapper<>(this.hrBillDetailGrantMapper.toEntity(hrBillDetailGrantDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrBillDetailGrantRepository.selectPage(page, qw);
        iPage.setRecords(this.hrBillDetailGrantMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    /**
     * 查询工资发放状态记录
     *
     * @param billDetailId
     * @return
     */
    @Override
    public List<HrBillDetailGrantDTO> findListByBillDetailId(String billDetailId) {
        return this.hrBillDetailGrantRepository.findListByBillDetailId(billDetailId);
    }

    /**
     * 导出工资发放记录
     *
     * @param hrBillDetailGrantDTO
     * @return
     */
    @Override
    public String exportDataList(HrBillDetailGrantDTO hrBillDetailGrantDTO) {
        List<HrBillDetailGrantDTO> list = this.hrBillDetailGrantRepository.findList(hrBillDetailGrantDTO);
        int listSize = list.size();
        List<String> ids = list.stream().map(HrBillDetailGrantDTO::getId).collect(Collectors.toList());
        if(ids == null || ids.isEmpty()){
            throw new CommonException("导出的发放记录为空！");
        }
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "工资发放记录", HrBillDetailGrantDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.BILL_DETAIL_GRANT.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 批量下载附件
     *
     * @param hrBillDetailGrantDTO
     * @return
     */
    @Override
    public String downloadAppendix(HrBillDetailGrantDTO hrBillDetailGrantDTO) {
        List<String> ids = hrBillDetailGrantDTO.getIds();
        if (ids == null || ids.isEmpty()) {
            throw new CommonException("请选择要下载附件的发放记录！");
        }
        List<HrBillDetailGrantDTO> list = this.hrBillDetailGrantRepository.findList(hrBillDetailGrantDTO);
        int listSize = list.size();
        // 临时文件夹
        String zipFileName = "工资发放记录" + "_" + DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
        String tempRootPath = tempPath + zipFileName + File.separator;
        cn.hutool.core.io.FileUtil.mkdir(tempRootPath);
        try {
            int num = 0;
            for (HrBillDetailGrantDTO billDetailGrant : list) {
                List<HrAppendixDTO> appendixList = hrAppendixService.getByUnionId(billDetailGrant.getId());
                if (appendixList != null && !appendixList.isEmpty()) {
                    String recruitmentBrochurePath = tempRootPath + StrUtil.replace(billDetailGrant.getCertificateNum().trim(), " ", "") + File.separator;
                    String generalRulesPath = recruitmentBrochurePath + (billDetailGrant.getGrantState().equals(BillEnum.BillGrantStateEnum.ISSUED.getKey()) ? BillEnum.BillGrantStateEnum.ISSUED.getValue() : BillEnum.BillGrantStateEnum.UNISSUED.getValue()) + File.separator;
                    cn.hutool.core.io.FileUtil.mkdir(generalRulesPath);
                    for (HrAppendixDTO appendix :  appendixList) {
                        FileUtil.saveUrlAs(appendix.getFileUrl(), generalRulesPath + appendix.getOriginName());
                        num++;
                    }
                }
            };
            if (num == 0) {
                throw new CommonException("请选择有附件的发放记录！");
            }
            // 压缩文件夹
            String zipFilePath = tempPath + zipFileName + ".zip";
            ZipUtil.zip(tempRootPath, CharsetUtil.CHARSET_UTF_8);
            String fileUrl = this.hrAppendixService.uploadLocalFile(zipFilePath);
            this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.BILL_DETAIL_GRANT.getValue(), BusinessTypeEnum.DOWNLOAD.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
            return fileUrl;
        } finally {
            // 删除临时文件
            cn.hutool.core.io.FileUtil.del(tempRootPath);
        }
    }
}
