package cn.casair.service;

import cn.casair.domain.HrMessageRole;
import cn.casair.dto.HrMessageRoleDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
public interface HrMessageRoleService extends IService<HrMessageRole> {


    /**
     * 创建
     * @param hrMessageRoleDTO
     * @return
     */
    HrMessageRoleDTO createHrMessageRole(HrMessageRoleDTO hrMessageRoleDTO);

    /**
     * 修改
     * @param hrMessageRoleDTO
     * @return
     */
    Optional<HrMessageRoleDTO> updateHrMessageRole(HrMessageRoleDTO hrMessageRoleDTO);

    /**
     * 查询详情
     * @param id
     * @return
     */
    HrMessageRoleDTO getHrMessageRole(String id);

    /**
     * 删除
     * @param id
     */
    void deleteHrMessageRole(String id);

    /**
     * 批量删除
     * @param ids
     */
    void deleteHrMessageRole(List<String> ids);

    /**
     * 分页查询
     * @param hrMessageRoleDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrMessageRoleDTO hrMessageRoleDTO,Long pageNumber,Long pageSize);
    }
