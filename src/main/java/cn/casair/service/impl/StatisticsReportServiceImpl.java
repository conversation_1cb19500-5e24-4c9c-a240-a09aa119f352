package cn.casair.service.impl;

import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.PageUtils;
import cn.casair.common.utils.excel.ExcelSheetEntity;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.HrClient;
import cn.casair.dto.report.*;
import cn.casair.repository.HrClientRepository;
import cn.casair.repository.HrReportRepository;
import cn.casair.repository.HrWelfareCompensationRepository;
import cn.casair.service.HrClientService;
import cn.casair.service.StatisticsReportService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class StatisticsReportServiceImpl implements StatisticsReportService {

    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final HrClientRepository hrClientRepository;
    private final HrReportRepository hrReportRepository;
    private final HrClientService hrClientService;

    @Override
    public void taxSummaryStaffExport(ReportQueryParam reportQueryParam, HttpServletResponse response) {
        this.checkReportQueryParams(reportQueryParam);
        List<TaxSummaryStaffDTO> list = this.hrWelfareCompensationRepository.selectTaxSummaryStaff(reportQueryParam);
        if (list.isEmpty()) {
            throw new CommonException("未查询到可导出数据!");
        }
        List<HrClient> structure = hrClientService.getStructure();
        list.forEach(record ->{
            if (!record.getParentId().equals("0")) {
                record.setHrClientList(getClientTree(structure, record.getParentId()));
            }
        });
        String sheetName = list.get(0).getDateStr();
        if (list.size() > 1) {
            TaxSummaryStaffDTO sum = this.calculateTaxSummaryStaffSum(list);
            list.add(sum);
            sheetName = sum.getDateStr();
        }
        ExcelUtils.exportExcels(list, "青岛市黄岛区人力资源有限公司个税分类汇总表(个人)", sheetName, TaxSummaryStaffDTO.class, response);
    }

    @Override
    public Map<String, Object> taxSummaryStaff(ReportQueryParam reportQueryParam, Long pageNumber, Long pageSize) {
        this.checkReportQueryParams(reportQueryParam);
        Map<String, Object> report = new HashMap<>();
        List<TaxSummaryStaffDTO> list = this.hrWelfareCompensationRepository.selectTaxSummaryStaff(reportQueryParam);
        if (!list.isEmpty()) {
            // 计算合计
            TaxSummaryStaffDTO sum = this.calculateTaxSummaryStaffSum(list);
            report.put("sum", sum);
            IPage<TaxSummaryStaffDTO> iPage = PageUtils.getPageByList(list, pageNumber, pageSize);
            List<HrClient> structure = hrClientService.getStructure();
            iPage.getRecords().forEach(record ->{
                if (!record.getParentId().equals("0")) {
                    record.setHrClientList(getClientTree(structure, record.getParentId()));
                }
            });
            report.put("list", iPage);
        }
        return report;
    }

    /**
     * 计算员工个税统计合计
     *
     * @param list
     * @return
     */
    private TaxSummaryStaffDTO calculateTaxSummaryStaffSum(List<TaxSummaryStaffDTO> list) {
        TaxSummaryStaffDTO sum = new TaxSummaryStaffDTO();
        String dateStr = "";
        BigDecimal personalTaxSource = BigDecimal.ZERO;
        BigDecimal personalTaxSystem = BigDecimal.ZERO;
        BigDecimal personalTax = BigDecimal.ZERO;
        for (int i = 0; i < list.size(); i++) {
            TaxSummaryStaffDTO ls = list.get(i);
            if (i == 0) {
                dateStr = ls.getDateStr();
            } else if (i == list.size() - 1) {
                dateStr = ls.getDateStr() + "至" + dateStr;
            }
            personalTaxSource = CalculateUtils.objectAddition(personalTaxSource, ls.getPersonalTaxSource());
            personalTaxSystem = CalculateUtils.objectAddition(personalTaxSystem, ls.getPersonalTaxSystem());
            personalTax = CalculateUtils.objectAddition(personalTax, ls.getPersonalTax());
        }
        sum.setDateStr(dateStr);
        sum.setPersonalTaxSource(personalTaxSource);
        sum.setPersonalTaxSystem(personalTaxSystem);
        sum.setPersonalTax(personalTax);
        sum.setClientName("/");
        sum.setStaffName("/");
        sum.setCertificateNum("/");
        sum.setOneClientName("/");
        sum.setTwoClientName("/");
        return sum;
    }

    @Override
    public void taxSummaryClientExport(ReportQueryParam reportQueryParam, HttpServletResponse response) {
        this.checkReportQueryParams(reportQueryParam);
        List<TaxSummaryClientDTO> list = this.hrClientRepository.selectTaxSummaryClient(reportQueryParam);
        if (list.isEmpty()) {
            throw new CommonException("未查询到可导出数据!");
        }
        List<HrClient> structure = hrClientService.getStructure();
        list.forEach(record ->{
            if (!record.getParentId().equals("0")) {
                record.setHrClientList(getClientTree(structure, record.getParentId()));
            }
        });
        String sheetName = list.get(0).getDateStr();
        if (list.size() > 1) {
            TaxSummaryClientDTO sum = this.calculateTaxSummaryClientSummaryData(list);
            list.add(sum);
            sheetName = sum.getDateStr();
        }
        ExcelUtils.exportExcels(list, "青岛市黄岛区人力资源有限公司个税分类汇总表(企业)", sheetName, TaxSummaryClientDTO.class, response);
    }

    @Override
    public Map<String, Object> taxSummaryClient(ReportQueryParam reportQueryParam, Long pageNumber, Long pageSize) {
        this.checkReportQueryParams(reportQueryParam);
        Map<String, Object> report = new HashMap<>();
        List<TaxSummaryClientDTO> list = this.hrClientRepository.selectTaxSummaryClient(reportQueryParam);
        if (!list.isEmpty()) {
            // 计算合计
            TaxSummaryClientDTO sum = this.calculateTaxSummaryClientSummaryData(list);
            report.put("sum", sum);
            // 列表数据
            IPage<TaxSummaryClientDTO> iPage = PageUtils.getPageByList(list, pageNumber, pageSize);
            List<HrClient> structure = hrClientService.getStructure();
            iPage.getRecords().forEach(record ->{
                if (!record.getParentId().equals("0")) {
                    record.setHrClientList(getClientTree(structure, record.getParentId()));
                }
            });
            report.put("list", iPage);
        }

        return report;
    }

    /**
     * 计算企业个税汇总合计
     *
     * @param list
     * @return
     */
    private TaxSummaryClientDTO calculateTaxSummaryClientSummaryData(List<TaxSummaryClientDTO> list) {
        TaxSummaryClientDTO sum = new TaxSummaryClientDTO();
        String dateStr = "";
        BigDecimal staffTaxTotal = BigDecimal.ZERO;
        for (int i = 0; i < list.size(); i++) {
            TaxSummaryClientDTO temp = list.get(i);
            if (i == 0) {
                dateStr = temp.getDateStr();
            } else if (i == list.size() - 1) {
                dateStr = temp.getDateStr() + "至" + dateStr;
            }
            staffTaxTotal = CalculateUtils.objectAddition(staffTaxTotal, temp.getStaffTaxTotal());
        }
        sum.setDateStr(dateStr);
        sum.setStaffTaxTotal(staffTaxTotal);
        sum.setClientName("/");
        sum.setOneClientName("/");
        sum.setTwoClientName("/");
        return sum;
    }

    /**
     * 检查报表请求参数
     *
     * @param reportQueryParam
     */
    private void checkReportQueryParams(ReportQueryParam reportQueryParam) {
        if (reportQueryParam.getExhibitionType() == null) {
            throw new CommonException("筛选类型不能为空!");
        }
        if (reportQueryParam.getExhibitionDateStart() == null) {
            throw new CommonException("筛选开始时间不能为空!");
        }
        if (reportQueryParam.getExhibitionDateEnd() == null) {
            throw new CommonException("筛选结束时间不能为空!");
        }
    }


    @Override
    public void clientInfoChangeDetailExport(ReportQueryParam reportQueryParam, HttpServletResponse response) {
        List<ExcelSheetEntity> excelSheetEntityList = new ArrayList<>();
        // 新增公司
        List<ClientInfoChangeDetailDTO> increasedList = this.hrClientRepository.selectIncreasedClient(reportQueryParam);
        excelSheetEntityList.add(new ExcelSheetEntity().setSheetName("新增公司").setPojoClass(ClientInfoChangeDetailDTO.class).setDataList(increasedList));
        // 减少公司
        List<ClientInfoChangeDetailDTO> reducedList = this.hrClientRepository.selectReducedClient(reportQueryParam);
        excelSheetEntityList.add(new ExcelSheetEntity().setSheetName("减少公司").setPojoClass(ClientInfoChangeDetailDTO.class).setDataList(reducedList));

        String fileName = "青岛市黄岛区人力资源有限公司客户信息变动详情";
        ExcelUtils.exportMultipleSheetsExcel(excelSheetEntityList, fileName, null, response);
    }

    @Override
    public Map<String, Object> clientInfoChangeDetail(ReportQueryParam reportQueryParam, Long pageNumber, Long pageSize) {
        if (reportQueryParam.getExhibitionType() == null) {
            throw new CommonException("筛选类型不能为空!");
        }
        if (StringUtils.isBlank(reportQueryParam.getExhibitionDateStart()) || StringUtils.isBlank(reportQueryParam.getExhibitionDateEnd())) {
            throw new CommonException("筛选时间不能为空!");
        }
        Map<String, Object> report = new HashMap<>();

        // 此时间段内新增公司
        List<ClientInfoChangeDetailDTO> increasedList = this.hrClientRepository.selectIncreasedClient(reportQueryParam);
        if (!increasedList.isEmpty()) {
            IPage<ClientInfoChangeDetailDTO> iPage = PageUtils.getPageByList(increasedList, pageNumber, pageSize);
            report.put("increased", iPage);
        }

        // 此时间段内减少的公司
        List<ClientInfoChangeDetailDTO> reducedList = this.hrClientRepository.selectReducedClient(reportQueryParam);
        if (!reducedList.isEmpty()) {
            IPage<ClientInfoChangeDetailDTO> iPage = PageUtils.getPageByList(reducedList, pageNumber, pageSize);
            report.put("reduced", iPage);
        }
        return report;
    }

    @Override
    public void clientInfoChangeExport(ReportQueryParam reportQueryParam, HttpServletResponse response) {
        this.checkReportQueryParams(reportQueryParam);
        List<ClientInfoChangeDTO> list = this.hrReportRepository.getClientInfoChange(reportQueryParam);
        if (list.isEmpty()) {
            throw new CommonException("未查询到可导出数据!");
        }
        List<ClientInfoChangeDTO> resultList = this.dealClientInfoChangeList(reportQueryParam, list);
        // 计算合计
        ClientInfoChangeDTO sum = this.calculateClientInfoChangeSummaryData(reportQueryParam, resultList);
        if (resultList.size() > 1) {
            resultList.add(sum);
        }
        String sheetName = sum.getDateStr();
        ExcelUtils.exportExcels(resultList, "青岛市黄岛区人力资源有限公司客户信息变动", sheetName, ClientInfoChangeDTO.class, response);
    }

    @Override
    public Map<String, Object> clientInfoChange(ReportQueryParam queryParam, Long pageNumber, Long pageSize) {
        this.checkReportQueryParams(queryParam);
        Map<String, Object> report = new HashMap<>();
        List<ClientInfoChangeDTO> list = this.hrReportRepository.getClientInfoChange(queryParam);
        if (!list.isEmpty()) {
            // 根据筛选条件处理数据
            List<ClientInfoChangeDTO> resultList = this.dealClientInfoChangeList(queryParam, list);
            IPage<ClientInfoChangeDTO> iPage = PageUtils.getPageByList(resultList, pageNumber, pageSize);
            report.put("list", iPage);
            // 计算汇总数据
            ClientInfoChangeDTO sum = this.calculateClientInfoChangeSummaryData(queryParam, resultList);
            report.put("sum", sum);
        }
        return report;
    }

    /**
     * 根据筛选条件处理数据
     *
     * @param queryParam
     * @param list
     * @return
     */
    private List<ClientInfoChangeDTO> dealClientInfoChangeList(ReportQueryParam queryParam, List<ClientInfoChangeDTO> list) {
        List<ClientInfoChangeDTO> resultList = new ArrayList<>();
        if (queryParam.getExhibitionType() == 1) {
            return list;
        } else {
            Map<String, List<ClientInfoChangeDTO>> collect = list.stream().collect(Collectors.groupingBy(ClientInfoChangeDTO::getDateStr));
            collect.forEach((k, v) -> {
                ClientInfoChangeDTO clientInfoChangeDTO = this.calculateClientInfoChange(v, k);
                resultList.add(clientInfoChangeDTO);
            });
        }
        return resultList;
    }

    /**
     * 计算客户信息变动汇总数据
     *
     * @param queryParam
     * @param list
     */
    private ClientInfoChangeDTO calculateClientInfoChangeSummaryData(ReportQueryParam queryParam, List<ClientInfoChangeDTO> list) {
        String dateStr;
        if (queryParam.getExhibitionType() == 1) {
            if (queryParam.getExhibitionDateStart().equals(queryParam.getExhibitionDateEnd())) {
                dateStr = queryParam.getExhibitionDateStart();
            } else {
                dateStr = queryParam.getExhibitionDateStart() + "至" + queryParam.getExhibitionDateEnd();
            }
        } else if (queryParam.getExhibitionType() == 2) {
            String[] startDateStr = queryParam.getExhibitionDateStart().split("-");
            String[] endDateStr = queryParam.getExhibitionDateEnd().split("-");
            String startDate = startDateStr[0] + "年第" + (Integer.parseInt(startDateStr[1]) + 2) / 3 + "季度";
            String endDate = endDateStr[0] + "年第" + (Integer.parseInt(endDateStr[1]) + 2) / 3 + "季度";
            if (startDate.equals(endDate)) {
                dateStr = startDate;
            } else {
                dateStr = startDate + "至" + endDate;
            }
        } else if (queryParam.getExhibitionType() == 3) {
            String[] startDateStr = queryParam.getExhibitionDateStart().split("-");
            String[] endDateStr = queryParam.getExhibitionDateEnd().split("-");
            String startDate = startDateStr[0] + "年" + (Integer.parseInt(startDateStr[1]) < 7 ? '上' : '下') + "半年度";
            String endDate = endDateStr[0] + "年" + (Integer.parseInt(endDateStr[1]) < 7 ? '上' : '下') + "半年度";
            if (startDate.equals(endDate)) {
                dateStr = startDate;
            } else {
                dateStr = startDate + "至" + endDate;
            }
        } else {
            String[] startDateStr = queryParam.getExhibitionDateStart().split("-");
            String[] endDateStr = queryParam.getExhibitionDateEnd().split("-");
            String startDate = startDateStr[0] + "年";
            String endDate = endDateStr[0] + "年";
            if (startDate.equals(endDate)) {
                dateStr = startDate;
            } else {
                dateStr = startDate + "至" + endDate;
            }
        }
        return this.calculateClientInfoChange(list, dateStr);
    }

    /**
     * 计算合计
     *
     * @param list
     * @param dateStr
     * @return
     */
    private ClientInfoChangeDTO calculateClientInfoChange(List<ClientInfoChangeDTO> list, String dateStr) {
        int clientCount = 0;
        int increasedCount = 0;
        int reducedCount = 0;
        for (int i = 0; i < list.size(); i++) {
            ClientInfoChangeDTO temp = list.get(i);
            if (i == 0) {
                clientCount = temp.getClientCount();
            }
            increasedCount += temp.getIncreasedCount();
            reducedCount += temp.getReducedCount();
        }
        ClientInfoChangeDTO clientInfoChangeDTO = new ClientInfoChangeDTO();
        clientInfoChangeDTO.setDateStr(dateStr);
        clientInfoChangeDTO.setClientCount(clientCount);
        clientInfoChangeDTO.setIncreasedCount(increasedCount);
        clientInfoChangeDTO.setReducedCount(reducedCount);
        return clientInfoChangeDTO;
    }

    /**
     * 营业数据汇总
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public Map getBusinessData(ReportQueryParam queryParam, Long pageNumber, Long pageSize) {
        Page<BusinessDataDTO> page = new Page<>(pageNumber, pageSize);
        IPage<BusinessDataDTO> data = hrReportRepository.getBusinessData(page, queryParam);
        List<HrClient> structure = hrClientService.getStructure();
        for (BusinessDataDTO record : data.getRecords()) {
            if (!record.getParentId().equals("0")) {
                record.setHrClientList(getClientTree(structure,record.getParentId()));
            }
        }
        // 处理汇总数据
        List<BusinessDataDTO> allList = hrReportRepository.getBusinessData(queryParam);
        BusinessDataDTO sum = getBusinessDataSum(allList);
        Map report = new HashMap<>();
        report.put("list", data);
        report.put("sum", sum);

        return report;
    }

    /**
     * 返回客户上级客户信息
     * @param structure
     * @param parentId
     * @return
     */
    private List<HrClient> getClientTree(List<HrClient> structure, String parentId) {
        List<HrClient> hrClientList = new ArrayList<>();
        HrClient hrClientOne = structure.stream().filter(lst -> lst.getId().equals(parentId)).findAny().orElse(null);
        hrClientList.add(hrClientOne);
        if (!hrClientOne.getParentId().equals("0")){
            HrClient hrClientTwo = structure.stream().filter(lst -> lst.getId().equals(hrClientOne.getParentId())).findAny().orElse(null);
            hrClientList.add(hrClientTwo);
        }
        return hrClientList;
    }

    /**
     * 导出营业数据汇总表
     *
     * @param queryParam
     * @param response
     * @param request
     */
    @Override
    public void exportBusinessData(ReportQueryParam queryParam, HttpServletResponse response, HttpServletRequest request) {
        try {
            List<BusinessDataDTO> list = hrReportRepository.getBusinessData(queryParam);
            List<HrClient> structure = hrClientService.getStructure();
            for (BusinessDataDTO record : list) {
                if (!record.getParentId().equals("0")) {
                    record.setHrClientList(getClientTree(structure,record.getParentId()));
                }
            }
            BusinessDataDTO sum = getBusinessDataSum(list);
            sum.setDateStr("合计");
            list.add(sum);
            ExcelUtils.exportExcels(list, null, "营业数据汇总表", BusinessDataDTO.class, response);
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            throw new CommonException("导出excel失败");
        }
    }

    private BusinessDataDTO getBusinessDataSum(List<BusinessDataDTO> allList) {
        BusinessDataDTO sum = new BusinessDataDTO();
        sum.setDateStr("-");
        sum.setClientName("-");
        sum.setOneClientName("-").setTwoClientName("-");
        int totalOnjobNum = allList.stream().mapToInt(BusinessDataDTO::getOnJobNum).sum();
        BigDecimal serviceAmountPrice = allList.stream().map(BusinessDataDTO::getServiceAmountPrice).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2,BigDecimal.ROUND_HALF_UP);
        BigDecimal totalNoTaxIncomeServiceAmount = allList.stream().map(BusinessDataDTO::getNoTaxIncomeServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2,BigDecimal.ROUND_HALF_UP);
        BigDecimal totalTaxAmount = allList.stream().map(BusinessDataDTO::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2,BigDecimal.ROUND_HALF_UP);
        BigDecimal totalIncomeServiceAmount = allList.stream().map(BusinessDataDTO::getIncomeServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2,BigDecimal.ROUND_HALF_UP);
        BigDecimal totalIncomeAmount = allList.stream().map(BusinessDataDTO::getIncomeAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2,BigDecimal.ROUND_HALF_UP);
        String serviceFeeType = allList.stream().map(BusinessDataDTO::getServiceFeeType).distinct().filter(Objects::nonNull).collect(Collectors.joining(";"));

        sum.setOnJobNum(totalOnjobNum);
        sum.setTaxAmount(totalTaxAmount);
        sum.setServiceAmountPrice(serviceAmountPrice);
        sum.setNoTaxIncomeServiceAmount(totalNoTaxIncomeServiceAmount);
        sum.setIncomeServiceAmount(totalIncomeServiceAmount);
        sum.setIncomeAmount(totalIncomeAmount);
        sum.setServiceFeeType(serviceFeeType);

        return sum;
    }

    /**
     * 工作数据汇总
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public Map getWorkData(ReportQueryParam queryParam, Long pageNumber, Long pageSize) {
        Page<WorkDataDTO> page = new Page<>(pageNumber, pageSize);
        // 入职数据统计
        IPage<WorkDataDTO> data = hrReportRepository.getWorkData(page, queryParam);
        List<HrClient> structure = hrClientService.getStructure();
        data.getRecords().forEach(record ->{
            if (!record.getParentId().equals("0")){
                record.setHrClientList(getClientTree(structure, record.getParentId()));
            }
        });
        // 处理汇总数据
        List<WorkDataDTO> allList = hrReportRepository.getWorkData(queryParam);
        WorkDataDTO sum = getWorkDataSum(allList);

        Map report = new HashMap<>();
        report.put("list", data);
        report.put("sum", sum);

        return report;
    }

    private WorkDataDTO getWorkDataSum(List<WorkDataDTO> allList) {
        int boardSum = allList.stream().mapToInt(WorkDataDTO::getBoardNum).sum();
        double boardHoursSum = allList.stream().mapToDouble(WorkDataDTO::getBoardHours).sum();
        int lzSum = allList.stream().mapToInt(WorkDataDTO::getDepartureNum).sum();
        double lzHoursSum = allList.stream().mapToDouble(WorkDataDTO::getDepartureHours).sum();
        int gsSum = allList.stream().mapToInt(WorkDataDTO::getGsNum).sum();
        double gsHoursSum = allList.stream().mapToDouble(WorkDataDTO::getGsHours).sum();
        int txSum = allList.stream().mapToInt(WorkDataDTO::getTxNum).sum();
        double txHoursSum = allList.stream().mapToDouble(WorkDataDTO::getTxHours).sum();
        int contractRenewalSum = allList.stream().mapToInt(WorkDataDTO::getContractRenewalNum).sum();
        double contractRenewalHoursSum = allList.stream().mapToDouble(WorkDataDTO::getBoardHours).sum();
        int zmSum = allList.stream().mapToInt(WorkDataDTO::getCertificateNum).sum();
        double zmHoursSum = allList.stream().mapToDouble(WorkDataDTO::getCertificateHours).sum();
        int sySum = allList.stream().mapToInt(WorkDataDTO::getSyNum).sum();
        int protocolNum = allList.stream().mapToInt(WorkDataDTO::getProtocolNum).sum();


        WorkDataDTO sum = new WorkDataDTO();
        sum.setDateStr("-");
        sum.setClientName("-");
        sum.setOneClientName("-");
        sum.setTwoClientName("-");
        sum.setBoardNum(boardSum);
        sum.setBoardHours(boardHoursSum);
        sum.setDepartureNum(lzSum);
        sum.setDepartureHours(lzHoursSum);
        sum.setGsNum(gsSum);
        sum.setGsHours(gsHoursSum);
        sum.setTxNum(txSum);
        sum.setTxHours(txHoursSum);
        sum.setContractRenewalNum(contractRenewalSum);
        sum.setContractRenewalHours(contractRenewalHoursSum);
        sum.setCertificateNum(zmSum);
        sum.setCertificateHours(zmHoursSum);
        sum.setSyNum(sySum);
        sum.setProtocolNum(protocolNum);

        return sum;
    }

    /**
     * 导出工作汇总表
     *
     * @param queryParam
     * @param response
     * @param request
     */
    @Override
    public void exportWorkData(ReportQueryParam queryParam, HttpServletResponse response, HttpServletRequest request) {
        try {
            List<WorkDataDTO> list = hrReportRepository.getWorkData(queryParam);
            List<HrClient> structure = hrClientService.getStructure();
            list.forEach(record ->{
                if (!record.getParentId().equals("0")){
                    record.setHrClientList(getClientTree(structure, record.getParentId()));
                }
            });
            WorkDataDTO sum = getWorkDataSum(list);
            sum.setDateStr("合计");
            list.add(sum);
            ExcelUtils.exportExcels(list, null, "工作数据汇总表", WorkDataDTO.class, response);
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            throw new CommonException("导出excel失败："+e.getMessage());
        }
    }


    /**
     * 人员增减变化
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public Map getEmployeeChangeData(ReportQueryParam queryParam, Long pageNumber, Long pageSize) {
        Page<EmployeeChangeDataDTO> page = new Page<>(pageNumber, pageSize);
        IPage<EmployeeChangeDataDTO> data = hrReportRepository.getEmployeeChangeData(page, queryParam);
        // 组装分页数据
        if (!data.getRecords().isEmpty()) {
            List<HrClient> structure = hrClientService.getStructure();
            data.getRecords().forEach(item -> {
                if (!item.getParentId().equals("0")){
                    item.setHrClientList(getClientTree(structure, item.getParentId()));
                }
                this.handleEmployeeChangeDataRate(item, queryParam);
            });
        }
        // 处理汇总数据
        List<EmployeeChangeDataDTO> allList = hrReportRepository.getEmployeeChangeData(queryParam);
        EmployeeChangeDataDTO sum = this.getEmployeeChangeDataSum(allList, queryParam);
        Map report = new HashMap<>();
        report.put("list", data);
        report.put("sum", sum);
        return report;
    }

    /**
     * 人员增减变化 图表
     *
     * @param queryParam
     * @return
     */
    @Override
    public List<EmployeeChangeDataDTO> getEmployeeChangeList(ReportQueryParam queryParam) {
        // 处理汇总数据
        List<EmployeeChangeDataDTO> list = hrReportRepository.getEmployeeChangeData(queryParam);
        // 组装分页数据
        if (!list.isEmpty()) {
            list.forEach(item -> {
                this.handleEmployeeChangeDataRate(item, queryParam);
            });
        }
        return list;
    }

    /**
     * @param allList
     * @return
     */
    private EmployeeChangeDataDTO getEmployeeChangeDataSum(List<EmployeeChangeDataDTO> allList, ReportQueryParam queryParam) {
        // 处理汇总数据
        EmployeeChangeDataDTO sum = new EmployeeChangeDataDTO();
        sum.setClientName("-");
        int totalNum1 = allList.stream().filter(lst -> lst.getNum1() != null).mapToInt(EmployeeChangeDataDTO::getNum1).sum();
        int totalNum2 = allList.stream().filter(lst -> lst.getNum2() != null).mapToInt(EmployeeChangeDataDTO::getNum2).sum();
        sum.setNum1(totalNum1);
        sum.setNum2(totalNum2);
        sum.setRate(new BigDecimal((double) (totalNum2 - totalNum1) / totalNum1).setScale(4, RoundingMode.HALF_UP));
        this.handleEmployeeChangeDataRate(sum, queryParam);
        return sum;
    }

    /**
     * 处理人员增减变化 增长率和流失率
     */
    private void handleEmployeeChangeDataRate(EmployeeChangeDataDTO employeeChangeData, ReportQueryParam queryParam) {
        String growthRate = "/";
        String lossRate = "/";
        employeeChangeData.setTime1(queryParam.getTime1());
        employeeChangeData.setTime2(queryParam.getTime2());
        BigDecimal rate = employeeChangeData.getRate() == null ? BigDecimal.ZERO : employeeChangeData.getRate().setScale(4, BigDecimal.ROUND_HALF_UP);
        if (rate.doubleValue() != 0) {
            DecimalFormat df = new DecimalFormat("0.00%");
            if (rate.doubleValue() > 0d) {
                growthRate = df.format(rate);
            } else {
                lossRate = df.format(rate);
            }
        }
        employeeChangeData.setRate(rate);
        employeeChangeData.setGrowthRate(growthRate);
        employeeChangeData.setLossRate(lossRate);
    }

    /**
     * 导出人员增减变化表
     *
     * @param queryParam
     * @param response
     */
    @Override
    public void exportEmployeeChangeData(ReportQueryParam queryParam, HttpServletResponse response) {
        List<EmployeeChangeDataDTO> list = hrReportRepository.getEmployeeChangeData(queryParam);
        if (list.isEmpty()) {
            throw new CommonException("未查询到可导出数据!");
        }
        if (!list.isEmpty()) {
            List<HrClient> structure = hrClientService.getStructure();
            list.forEach(item -> {
                if (!item.getParentId().equals("0")){
                    item.setHrClientList(getClientTree(structure, item.getParentId()));
                }
                this.handleEmployeeChangeDataRate(item, queryParam);
            });
        }
        // 处理汇总数据
        List<EmployeeChangeDataDTO> allList = hrReportRepository.getEmployeeChangeData(queryParam);
        EmployeeChangeDataDTO sum = this.getEmployeeChangeDataSum(allList, queryParam);
        sum.setOneClientName("合计");
        sum.setTwoClientName("-");
        sum.setClientName("-");
        list.add(sum);
        ExcelUtils.exportExcels(list, "人员增减变化表", null, EmployeeChangeDataDTO.class, response);
    }

    /**
     * 收入同期对比数据
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public Map getIncomeComparisonData(ReportQueryParam queryParam, Long pageNumber, Long pageSize) {
        Page<IncomeComparisonDataDTO> page = new Page<>(pageNumber, pageSize);
        IPage<IncomeComparisonDataDTO> data = hrReportRepository.getIncomeComparisonData(page, queryParam);
        // 组装分页数据
        if (!data.getRecords().isEmpty()) {
            List<HrClient> structure = hrClientService.getStructure();
            data.getRecords().forEach(item -> {
                if (!item.getParentId().equals("0")) {
                    item.setHrClientList(getClientTree(structure, item.getParentId()));
                }
                this.handleIncomeComparisonDataRate(item, queryParam);
            });
        }
        // 处理汇总数据
        List<IncomeComparisonDataDTO> allList = hrReportRepository.getIncomeComparisonData(queryParam);
        IncomeComparisonDataDTO sum = this.getIncomeComparisonSum(allList, queryParam);
        Map report = new HashMap<>();
        report.put("list", data);
        report.put("sum", sum);
        return report;
    }

    /**
     * 收入同期对比数据 图表
     *
     * @param queryParam
     * @return
     */
    @Override
    public List<IncomeComparisonDataDTO> getIncomeComparisonList(ReportQueryParam queryParam) {
        List<IncomeComparisonDataDTO> list = hrReportRepository.getIncomeComparisonData(queryParam);
        if (!list.isEmpty()) {
            list.forEach(item -> {
                this.handleIncomeComparisonDataRate(item, queryParam);
            });
        }
        return list;
    }


    /**
     * 收入同期对比 汇总
     *
     * @param allList
     * @return
     */
    private IncomeComparisonDataDTO getIncomeComparisonSum(List<IncomeComparisonDataDTO> allList, ReportQueryParam queryParam) {
        // 处理汇总数据
        IncomeComparisonDataDTO sum = new IncomeComparisonDataDTO();
        sum.setClientName("-");
        BigDecimal totalTqServiceFee = BigDecimal.ZERO, totalBqServiceFee = BigDecimal.ZERO, totalTqTotalAmount = BigDecimal.ZERO, totalBqTotalAmount = BigDecimal.ZERO;
        for (IncomeComparisonDataDTO incomeComparisonData : allList) {
            totalTqServiceFee = totalTqServiceFee.add(incomeComparisonData.getTqServiceFee());
            totalBqServiceFee = totalBqServiceFee.add(incomeComparisonData.getBqServiceFee());
            totalTqTotalAmount = totalTqTotalAmount.add(incomeComparisonData.getTqTotalAmount());
            totalBqTotalAmount = totalBqTotalAmount.add(incomeComparisonData.getBqTotalAmount());

        }
        sum.setTqServiceFee(totalTqServiceFee.setScale(2, BigDecimal.ROUND_HALF_UP));
        sum.setBqServiceFee(totalBqServiceFee.setScale(2, BigDecimal.ROUND_HALF_UP));
        sum.setTqTotalAmount(totalTqTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        sum.setBqTotalAmount(totalBqTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        if (sum.getTqServiceFee().doubleValue() != 0d) {
            sum.setServiceFeeRate(new BigDecimal((sum.getBqServiceFee().doubleValue() - sum.getTqServiceFee().doubleValue()) / sum.getTqServiceFee().doubleValue()));
        }
        if (sum.getTqTotalAmount().doubleValue() != 0d) {
            sum.setIncomeRate(new BigDecimal((sum.getBqTotalAmount().doubleValue() - sum.getTqTotalAmount().doubleValue()) / sum.getTqTotalAmount().doubleValue()));
        }
        this.handleIncomeComparisonDataRate(sum, queryParam);
        return sum;
    }

    /**
     * 处理收入同期对比 增长/下降率
     */
    private void handleIncomeComparisonDataRate(IncomeComparisonDataDTO incomeComparisonDataDTO, ReportQueryParam queryParam) {
        DecimalFormat df = new DecimalFormat("0.00%");
        incomeComparisonDataDTO.setTqTime(queryParam.getTime1());
        incomeComparisonDataDTO.setBqTime(queryParam.getTime2());
        incomeComparisonDataDTO.setServiceFeeRate(incomeComparisonDataDTO.getServiceFeeRate() == null ? BigDecimal.ZERO : incomeComparisonDataDTO.getServiceFeeRate());
        incomeComparisonDataDTO.setIncomeRate(incomeComparisonDataDTO.getIncomeRate() == null ? BigDecimal.ZERO : incomeComparisonDataDTO.getIncomeRate());
        incomeComparisonDataDTO.setServiceFeeRateStr(incomeComparisonDataDTO.getServiceFeeRate() == null ? "/" : df.format(incomeComparisonDataDTO.getServiceFeeRate().doubleValue()));
        incomeComparisonDataDTO.setIncomeRateStr(incomeComparisonDataDTO.getIncomeRate() == null ? "/" : df.format(incomeComparisonDataDTO.getIncomeRate().doubleValue()));
    }


    /**
     * 导出收入同期对比数据
     *
     * @param queryParam
     * @param response
     */
    @Override
    public void exportIncomeComparisonData(ReportQueryParam queryParam, HttpServletResponse response) {
        List<IncomeComparisonDataDTO> list = hrReportRepository.getIncomeComparisonData(queryParam);
        if (list.isEmpty()) {
            throw new CommonException("未查询到可导出数据!");
        }
        if (!list.isEmpty()) {
            List<HrClient> structure = hrClientService.getStructure();
            list.forEach(item -> {
                if (!item.getParentId().equals("0")) {
                    item.setHrClientList(getClientTree(structure, item.getParentId()));
                }
                this.handleIncomeComparisonDataRate(item, queryParam);
            });
        }
        // 处理汇总数据
        List<IncomeComparisonDataDTO> allList = hrReportRepository.getIncomeComparisonData(queryParam);
        IncomeComparisonDataDTO sum = this.getIncomeComparisonSum(allList, queryParam);
        sum.setOneClientName("合计");
        sum.setTwoClientName("-");
        sum.setClientName("-");
        list.add(sum);
        ExcelUtils.exportExcels(list, "收入同期对比表", null, IncomeComparisonDataDTO.class, response);
    }

    /**
     * 预算执行控制数据
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public Map getBudgetControlData(ReportQueryParam queryParam, Long pageNumber, Long pageSize) {
        Page<BudgetControlDataDTO> page = new Page<>(pageNumber, pageSize);
        IPage<BudgetControlDataDTO> data = hrReportRepository.getBudgetControlData(page, queryParam);
        List<HrClient> structure = hrClientService.getStructure();
        data.getRecords().forEach(record ->{
            if (!record.getParentId().equals("0")){
                record.setHrClientList(getClientTree(structure, record.getParentId()));
            }
        });
        // 处理汇总数据
        List<BudgetControlDataDTO> allList = hrReportRepository.getBudgetControlData(queryParam);
        BudgetControlDataDTO sum = this.getBudgetControlSum(allList, queryParam);
        Map report = new HashMap<>();
        report.put("list", data);
        report.put("sum", sum);
        return report;
    }

    /**
     * 预算执行控制数据 汇总
     *
     * @param allList
     * @return
     */
    private BudgetControlDataDTO getBudgetControlSum(List<BudgetControlDataDTO> allList, ReportQueryParam queryParam) {
        // 处理汇总数据
        BudgetControlDataDTO sum = new BudgetControlDataDTO();
        sum.setPaymentDate(queryParam.getTime1Start() + "至" + queryParam.getTime1End());
        sum.setClientName("-");
        BigDecimal realSalaryTotal = BigDecimal.ZERO, personalTaxTotal = BigDecimal.ZERO, socialSecurityTotal = BigDecimal.ZERO, accumulationFundTotal = BigDecimal.ZERO, serviceFeeTotal = BigDecimal.ZERO;
        for (BudgetControlDataDTO budgetControlData : allList) {
            realSalaryTotal = realSalaryTotal.add(budgetControlData.getRealSalaryTotal());
            personalTaxTotal = personalTaxTotal.add(budgetControlData.getPersonalTaxTotal());
            socialSecurityTotal = socialSecurityTotal.add(budgetControlData.getSocialSecurityTotal());
            accumulationFundTotal = accumulationFundTotal.add(budgetControlData.getAccumulationFundTotal());
            serviceFeeTotal = serviceFeeTotal.add(budgetControlData.getServiceFeeTotal());
        }
        sum.setRealSalaryTotal(realSalaryTotal).setPersonalTaxTotal(personalTaxTotal).setSocialSecurityTotal(socialSecurityTotal).setAccumulationFundTotal(accumulationFundTotal).setServiceFeeTotal(serviceFeeTotal);
        return sum;
    }

    /**
     * 导出预算执行控制数据
     *
     * @param queryParam
     * @param response
     */
    @Override
    public void exportBudgetControlData(ReportQueryParam queryParam, HttpServletResponse response) {
        List<BudgetControlDataDTO> list = hrReportRepository.getBudgetControlData(queryParam);
        if (list.isEmpty()) {
            throw new CommonException("未查询到可导出数据!");
        }
        List<HrClient> structure = hrClientService.getStructure();
        list.forEach(record ->{
            if (!record.getParentId().equals("0")){
                record.setHrClientList(getClientTree(structure, record.getParentId()));
            }
        });
        // 处理汇总数据
        List<BudgetControlDataDTO> allList = hrReportRepository.getBudgetControlData(queryParam);
        BudgetControlDataDTO sum = this.getBudgetControlSum(allList, queryParam);
        sum.setClientName("-");
        sum.setOneClientName("-");
        sum.setTwoClientName("-");
        list.add(sum);
        ExcelUtils.exportExcels(list, "预算执行控制数据表", null, BudgetControlDataDTO.class, response);
    }

    /**
     * 招聘数据统计
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public Map getRecruitmentData(ReportQueryParam queryParam, Long pageNumber, Long pageSize) {
        Page<RecruitmentDataDTO> page = new Page<>(pageNumber, pageSize);
        IPage<RecruitmentDataDTO> data = hrReportRepository.getRecruitmentData(page, queryParam);
        // 处理汇总数据
        List<RecruitmentDataDTO> allList = hrReportRepository.getRecruitmentData(queryParam);
        RecruitmentDataDTO sum = this.getRecruitmentDataSum(allList);
        Map report = new HashMap<>();
        report.put("list", data);
        report.put("sum", sum);
        return report;
    }

    /**
     * 导出招聘数据统计
     *
     * @param queryParam
     * @param response
     */
    @Override
    public void exportRecruitmentData(ReportQueryParam queryParam, HttpServletResponse response) {
        List<RecruitmentDataDTO> list = hrReportRepository.getRecruitmentData(queryParam);
        if (list.isEmpty()) {
            throw new CommonException("未查询到可导出数据!");
        }
        RecruitmentDataDTO totalDataDTO = this.getRecruitmentDataSum(list);
        list.add(totalDataDTO);
        ExcelUtils.exportExcels(list, "青岛市黄岛区人力资源有限公司" + LocalDate.now() + "招聘数据统计表", "招聘数据", RecruitmentDataDTO.class, response);
    }

    /**
     * 处理汇总数据
     *
     * @param allList
     * @return
     */
    private RecruitmentDataDTO getRecruitmentDataSum(List<RecruitmentDataDTO> allList) {
        int proSessions = allList.stream().mapToInt(RecruitmentDataDTO::getProSessions).sum();
        int signUpNum = allList.stream().mapToInt(RecruitmentDataDTO::getSignUpNum).sum();
        int approvedNum = allList.stream().mapToInt(RecruitmentDataDTO::getApprovedNum).sum();
        int payNum = allList.stream().mapToInt(RecruitmentDataDTO::getPayNum).sum();
        int onDutyNum = allList.stream().mapToInt(RecruitmentDataDTO::getOnDutyNum).sum();
        BigDecimal payAmount = allList.stream().map(RecruitmentDataDTO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal serviceFee = allList.stream().map(RecruitmentDataDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal backArrivalAmount = allList.stream().map(RecruitmentDataDTO::getBackArrivalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        RecruitmentDataDTO recruitmentDataDTO = new RecruitmentDataDTO();
        recruitmentDataDTO.setDateStr("-")
            .setProSessions(proSessions)
            .setSignUpNum(signUpNum)
            .setApprovedNum(approvedNum)
            .setPayNum(payNum)
            .setOnDutyNum(onDutyNum)
            .setPayAmount(payAmount)
            .setServiceFee(serviceFee)
            .setBackArrivalAmount(backArrivalAmount);
        return recruitmentDataDTO;
    }

    /**
     * 招聘信息汇总表
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public Map getRecruitmentInfoData(ReportQueryParam queryParam, Long pageNumber, Long pageSize) {
        Page<RecruitmentInfoDataDTO> page = new Page<>(pageNumber, pageSize);
        IPage<RecruitmentInfoDataDTO> data = hrReportRepository.getRecruitmentInfoData(page, queryParam);
        List<HrClient> structure = hrClientService.getStructure();
        if (data.getRecords() != null && !data.getRecords().isEmpty()){
            data.getRecords().forEach(record ->{
                if (!record.getParentId().equals("0")){
                    record.setHrClientList(getClientTree(structure,record.getParentId()));
                }
            });
        }
        // 处理汇总数据
        List<RecruitmentInfoDataDTO> allList = hrReportRepository.getRecruitmentInfoData(queryParam);
        RecruitmentInfoDataDTO sum = this.getRecruitmentInfoDataSum(allList);
        Map report = new HashMap<>();
        report.put("list", data);
        report.put("sum", sum);
        return report;
    }

    /**
     * 导出招聘信息汇总表
     *
     * @param queryParam
     * @param response
     */
    @Override
    public void exportRecruitmentInfoData(ReportQueryParam queryParam, HttpServletResponse response) {
        List<RecruitmentInfoDataDTO> list = hrReportRepository.getRecruitmentInfoData(queryParam);
        if (list.isEmpty()) {
            throw new CommonException("未查询到可导出数据!");
        }
        List<HrClient> structure = hrClientService.getStructure();
        list.forEach(record ->{
            if (!record.getParentId().equals("0")) {
                record.setHrClientList(getClientTree(structure, record.getParentId()));
            }
        });
        RecruitmentInfoDataDTO totalDataDTO = this.getRecruitmentInfoDataSum(list);
        list.add(totalDataDTO);
        ExcelUtils.exportExcels(list, list.get(0).getClientName() + LocalDate.now() + "招聘信息汇总表", "招聘信息", RecruitmentInfoDataDTO.class, response);
    }

    private RecruitmentInfoDataDTO getRecruitmentInfoDataSum(List<RecruitmentInfoDataDTO> allList) {
        int signUpNum = allList.stream().mapToInt(RecruitmentInfoDataDTO::getSignUpNum).sum();
        int approvedNum = allList.stream().mapToInt(RecruitmentInfoDataDTO::getApprovedNum).sum();
        int payNum = allList.stream().mapToInt(RecruitmentInfoDataDTO::getPayNum).sum();
        int onDutyNum = allList.stream().mapToInt(RecruitmentInfoDataDTO::getOnDutyNum).sum();
        int specialtyNum = allList.stream().mapToInt(RecruitmentInfoDataDTO::getSpecialtyNum).sum();
        int undergraduateNum = allList.stream().mapToInt(RecruitmentInfoDataDTO::getUndergraduateNum).sum();
        int postgraduateNum = allList.stream().mapToInt(RecruitmentInfoDataDTO::getPostgraduateNum).sum();
        BigDecimal payAmount = allList.stream().map(RecruitmentInfoDataDTO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal serviceFee = allList.stream().map(RecruitmentInfoDataDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal backArrivalAmount = allList.stream().map(RecruitmentInfoDataDTO::getBackArrivalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        RecruitmentInfoDataDTO recruitmentInfoDataDTO = new RecruitmentInfoDataDTO();
        recruitmentInfoDataDTO.setClientNum(allList.size())
            .setSignUpNum(signUpNum)
            .setApprovedNum(approvedNum)
            .setPayNum(payNum)
            .setOnDutyNum(onDutyNum)
            .setSpecialtyNum(specialtyNum)
            .setUndergraduateNum(undergraduateNum)
            .setPostgraduateNum(postgraduateNum)
            .setPayAmount(payAmount)
            .setServiceFee(serviceFee)
            .setBackArrivalAmount(backArrivalAmount)
            .setClientName("-")
            .setInvoiceStatus("-")
            .setOneClientName("-")
            .setTwoClientName("-");
        return recruitmentInfoDataDTO;
    }


    /**
     * 分页PRO业务样表
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public Map getRecruitmentPROData(ReportQueryParam queryParam, Long pageNumber, Long pageSize) {
        Page<RecruitmentPRODataDTO> page = new Page<>(pageNumber, pageSize);
        IPage<RecruitmentPRODataDTO> data = hrReportRepository.getRecruitmentPROData(page, queryParam);
        // 处理汇总数据
        List<RecruitmentPRODataDTO> allList = hrReportRepository.getRecruitmentPROData(queryParam);
        RecruitmentPRODataDTO sum = this.getRecruitmentPRODataSum(allList);
        Map report = new HashMap<>();
        report.put("list", data);
        report.put("sum", sum);
        return report;
    }

    /**
     * 导出PRO业务样表
     *
     * @param queryParam
     * @param response
     */
    @Override
    public void exportRecruitmentPROData(ReportQueryParam queryParam, HttpServletResponse response) {
        List<RecruitmentPRODataDTO> list = hrReportRepository.getRecruitmentPROData(queryParam);
        if (list.isEmpty()) {
            throw new CommonException("未查询到可导出数据!");
        }
        RecruitmentPRODataDTO totalDataDTO = this.getRecruitmentPRODataSum(list);
        list.add(totalDataDTO);
        ExcelUtils.exportExcels(list, "青岛市黄岛区人力资源有限公司" + LocalDate.now() + "PRO业务样表", "PRO业务样表", RecruitmentPRODataDTO.class, response);
    }

    private RecruitmentPRODataDTO getRecruitmentPRODataSum(List<RecruitmentPRODataDTO> allList) {
        int signUpNum = allList.stream().mapToInt(RecruitmentPRODataDTO::getSignUpNum).sum();
        int approvedNum = allList.stream().mapToInt(RecruitmentPRODataDTO::getApprovedNum).sum();
        int payNum = allList.stream().mapToInt(RecruitmentPRODataDTO::getPayNum).sum();
        int onDutyNum = allList.stream().mapToInt(RecruitmentPRODataDTO::getOnDutyNum).sum();
        int specialtyNum = allList.stream().mapToInt(RecruitmentPRODataDTO::getSpecialtyNum).sum();
        int undergraduateNum = allList.stream().mapToInt(RecruitmentPRODataDTO::getUndergraduateNum).sum();
        int postgraduateNum = allList.stream().mapToInt(RecruitmentPRODataDTO::getPostgraduateNum).sum();
        BigDecimal payAmount = allList.stream().map(RecruitmentPRODataDTO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal serviceFee = allList.stream().map(RecruitmentPRODataDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal backArrivalAmount = allList.stream().map(RecruitmentPRODataDTO::getBackArrivalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        RecruitmentPRODataDTO recruitmentPRODataDTO = new RecruitmentPRODataDTO();
        recruitmentPRODataDTO
            .setSignUpNum(signUpNum)
            .setApprovedNum(approvedNum)
            .setPayNum(payNum)
            .setOnDutyNum(onDutyNum)
            .setSpecialtyNum(specialtyNum)
            .setUndergraduateNum(undergraduateNum)
            .setPostgraduateNum(postgraduateNum)
            .setPayAmount(payAmount)
            .setServiceFee(serviceFee)
            .setBackArrivalAmount(backArrivalAmount)
            .setInvoiceStatus("-");
        return recruitmentPRODataDTO;
    }
}
