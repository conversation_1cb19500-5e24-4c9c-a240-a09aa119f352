package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.CompanyInfoEnum;
import cn.casair.common.enums.MessageTemplateEnum;
import cn.casair.common.enums.RecruitmentBrochure;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrDrawLotsNumberImport;
import cn.casair.dto.excel.HrDrawLotsNumberTemplate;
import cn.casair.dto.excel.HrRegistrationDetailsExport;
import cn.casair.mapper.*;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.casair.common.utils.DataUtils.renderString;
import static cn.casair.common.utils.FileUtil.zipFile;

/**
 * 报名情况服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrRegistrationDetailsServiceImpl extends ServiceImpl<HrRegistrationDetailsRepository, HrRegistrationDetails> implements HrRegistrationDetailsService {

    private final HrRecruitmentBulletinRepository hrRecruitmentBulletinRepository;
    private final HrRegistrationDetailsRepository hrRegistrationDetailsRepository;
    private final HrRegistrationDetailsMapper hrRegistrationDetailsMapper;
    private final HrRegistrationInfoRepository hrRegistrationInfoRepository;
    private final HrRegistrationDetailsEvaluationRepository hrRegistrationDetailsEvaluationRepository;
    private final HrAppendixService hrAppendixService;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrMessageRoleRepository hrMessageRoleRepository;
    private final HrMessageRoleMapper hrMessageRoleMapper;
    private final HrClientRepository hrClientRepository;
    private final HrRecruitmentBrochureRepository hrRecruitmentBrochureRepository;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrApplyOpLogsRepository hrApplyOpLogsRepository;
    private final RedisCache redisCache;
    private final HrMessageListMapper hrMessageListMapper;
    private final HrExamResultRepository hrExamResultRepository;
    private final HrSmsTemplateService hrSmsTemplateService;
    private final UserRepository userRepository;
    private final HrExamResultMapper hrExamResultMapper;
    private final HrRecruitmentStationRepository hrRecruitmentStationRepository;
    private final HrMessageListRepository hrMessageListRepository;
    private final HrApplyOpLogsMapper hrApplyOpLogsMapper;
    private final SysOperLogService sysOperLogService;
    private final HrCertificateRepository hrCertificateRepository;
    private final HrContentTemplateRepository hrContentTemplateRepository;
    private final CodeTableService codeTableService;
    private final HrAppletMessageService hrAppletMessageService;
    private final HrAppletMessageRepository hrAppletMessageRepository;
    @Autowired
    private HrTalentStaffService hrTalentStaffService;
    @Resource
    private HrExamResultService hrExamResultService;
    @Value("${file.temp-path}")
    private String fileTempPath;

    @Value("${file.temp-path}")
    private String tempPath;

    @Value("${mini.appletName}")
    private String appletSite;


    public HrRegistrationDetailsServiceImpl(HrRecruitmentBulletinRepository hrRecruitmentBulletinRepository, HrRegistrationDetailsRepository hrRegistrationDetailsRepository, HrClientRepository hrClientRepository, HrRegistrationDetailsMapper hrRegistrationDetailsMapper, HrRegistrationInfoRepository hrRegistrationInfoRepository, HrRegistrationDetailsEvaluationRepository hrRegistrationDetailsEvaluationRepository, HrAppendixService hrAppendixService, HrTalentStaffRepository hrTalentStaffRepository, HrMessageRoleRepository hrMessageRoleRepository, HrMessageRoleMapper hrMessageRoleMapper, HrRecruitmentBrochureRepository hrRecruitmentBrochureRepository, HrApplyOpLogsService hrApplyOpLogsService, HrApplyOpLogsRepository hrApplyOpLogsRepository, RedisCache redisCache, HrMessageListMapper hrMessageListMapper, HrExamResultRepository hrExamResultRepository, HrSmsTemplateService hrSmsTemplateService, UserRepository userRepository, HrExamResultMapper hrExamResultMapper, HrRecruitmentStationRepository hrRecruitmentStationRepository, HrMessageListRepository hrM, HrMessageListRepository hrMessageListRepository, HrApplyOpLogsMapper hrApplyOpLogsMapper, UserRoleService userRoleService, SysOperLogService sysOperLogService, HrCertificateRepository hrCertificateRepository, HrContentTemplateRepository hrContentTemplateRepository, CodeTableService codeTableService, HrAppletMessageService hrAppletMessageService, HrAppletMessageRepository hrAppletMessageRepository) {
        this.hrRecruitmentBulletinRepository = hrRecruitmentBulletinRepository;
        this.hrRegistrationDetailsRepository = hrRegistrationDetailsRepository;
        this.hrClientRepository = hrClientRepository;
        this.hrRegistrationDetailsMapper = hrRegistrationDetailsMapper;
        this.hrRegistrationInfoRepository = hrRegistrationInfoRepository;
        this.hrRegistrationDetailsEvaluationRepository = hrRegistrationDetailsEvaluationRepository;
        this.hrAppendixService = hrAppendixService;
        this.hrTalentStaffRepository = hrTalentStaffRepository;
        this.hrMessageRoleRepository = hrMessageRoleRepository;
        this.hrMessageRoleMapper = hrMessageRoleMapper;
        this.hrRecruitmentBrochureRepository = hrRecruitmentBrochureRepository;
        this.hrApplyOpLogsService = hrApplyOpLogsService;
        this.hrApplyOpLogsRepository = hrApplyOpLogsRepository;
        this.redisCache = redisCache;
        this.hrMessageListMapper = hrMessageListMapper;
        this.hrExamResultRepository = hrExamResultRepository;
        this.hrSmsTemplateService = hrSmsTemplateService;
        this.userRepository = userRepository;
        this.hrExamResultMapper = hrExamResultMapper;
        this.hrRecruitmentStationRepository = hrRecruitmentStationRepository;
        this.hrMessageListRepository = hrMessageListRepository;

        this.hrApplyOpLogsMapper = hrApplyOpLogsMapper;
        this.sysOperLogService = sysOperLogService;
        this.hrCertificateRepository = hrCertificateRepository;
        this.hrContentTemplateRepository = hrContentTemplateRepository;
        this.codeTableService = codeTableService;
        this.hrAppletMessageService = hrAppletMessageService;
        this.hrAppletMessageRepository = hrAppletMessageRepository;
    }

    /**
     * 创建报名情况
     *
     * @param hrRegistrationDetailsDTO
     * @return
     */
    @Override
    public HrRegistrationDetailsDTO createHrRegistrationDetails(HrRegistrationDetailsDTO hrRegistrationDetailsDTO) {
        log.info("Create new HrRegistrationDetails:{}", hrRegistrationDetailsDTO);

        HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
        this.hrRegistrationDetailsRepository.insert(hrRegistrationDetails);
        return this.hrRegistrationDetailsMapper.toDto(hrRegistrationDetails);
    }

    //生成拟聘用公式的exeal
    @Override
    public ResponseEntity<?> updateHrRegistrationEvaluationExel(List<String> ids) {
        List<String> mationName = new ArrayList<>();
        List<String> fileList = new ArrayList<>();
        HrAppendixDTO hrAppendixDTO = new HrAppendixDTO();
        List<ExcelExportEntity> collect = new ArrayList<>();
        int numder = 0;
        List<Map<String, Object>> paramsList = new ArrayList<>();
        HrRecruitmentStation hrRecruitmentStation = new HrRecruitmentStation();
        HrRecruitmentBrochure hrRecruitmentBrochure = new HrRecruitmentBrochure();
        for (String id : ids) {
            List<ExcelExportEntity> excelHeader = new ArrayList<>();
            // 数据
            HrRegistrationDetails hrRegistrationDetailss = this.hrRegistrationDetailsRepository.selectById(id);
            QueryWrapper<HrRecruitmentBulletin> qw = new QueryWrapper<>();
            hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(hrRegistrationDetailss.getBrochureId());
            hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRegistrationDetailss.getStationId());
            if (!hrRegistrationDetailss.getStatus().equals(15)) {
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrRegistrationDetailss.getStaffId());
                mationName.add(hrTalentStaff.getName());
            } else {
                numder = numder + 1;
                paramsList = this.detailsExportHeaders(numder, hrRegistrationDetailss, excelHeader, paramsList, hrRecruitmentStation, hrRecruitmentBrochure);
                // 去重
                collect = excelHeader.stream().distinct().collect(Collectors.toList());
            }
        }
        fileList.add(Objects.requireNonNull(ExcelUtils.dynamicColumnExportLocal(hrRecruitmentStation.getRecruitmentStationName() + "_拟聘用公式", "青岛市黄岛区人力资源有限公司公开补录" + hrRecruitmentBrochure.getRecruitBrochureName() + "体检结果即拟聘用名单", collect, paramsList, fileTempPath)));
        List<HrAppendix> list = new ArrayList<>();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fileList)) {
            for (String file : fileList) {
                list.add(hrAppendixService.uploadImportFile(file));
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", mationName);
        jsonObject.put("sc", list);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mationName)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", mationName) + "的状态不是待拟聘用公示    ");
        }

        return ResponseUtil.buildSuccess(jsonObject);
    }

    //动态生成exeal
    private List<Map<String, Object>> detailsExportHeaders(int sum, HrRegistrationDetails dto, List<ExcelExportEntity> excelHeader, List<Map<String, Object>> paramsList, HrRecruitmentStation hrRecruitmentStationDTO, HrRecruitmentBrochure hrRecruitmentBrochure) {
        Map<String, Object> param = new HashMap<>();
        Map<String, Object> map = JSON.parseObject(dto.getAdmissionTicketInfo(), HashMap.class);
        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(dto.getStaffId());
        QueryWrapper<HrExamResult> wq = new QueryWrapper<>();
        wq.eq("staff_id", dto.getStaffId());
        wq.eq("profession_name", dto.getStationName());
        wq.eq("exam_name", hrRecruitmentBrochure.getRecruitBrochureName());
        HrExamResult hrExamResult = this.hrExamResultRepository.selectOne(wq);
        excelHeader.add(new ExcelExportEntity("序号", "serial", 20));
        param.put("serial", sum);
        excelHeader.add(new ExcelExportEntity("姓名", "name", 20));
        param.put("name", hrTalentStaff.getName());
        if (map != null) {
            excelHeader.add(new ExcelExportEntity("岗位名称", "postRegistration", 20));
            param.put("postRegistration", map.get("postRegistration"));
        }
        if (map != null) {
            excelHeader.add(new ExcelExportEntity("准考证号", "admissionNumber", 25));
            param.put("admissionNumber", map.get("admissionNumber"));
        }

        excelHeader.add(new ExcelExportEntity("面试顺序号", "number", 20));
        param.put("number", dto.getNumber());


        excelHeader.add(new ExcelExportEntity("体检是否合格", "physicalExaminationResult", 20));
        excelHeader.add(new ExcelExportEntity("是否拟聘用", "isProposedEmployment", 20));
        if (hrExamResult != null) {
            if (hrExamResult.getPhysicalExaminationResult() != null) {
                param.put("physicalExaminationResult", hrExamResult.getPhysicalExaminationResult().equals(0) ? "" : "是");
                param.put("isProposedEmployment", hrExamResult.getPhysicalExaminationResult().equals(0) ? "" : "是");
            }
        }


       /*     case 1:
                if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())
                    || hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                    excelHeader.add(new ExcelExportEntity("体检是否合格", "isInvestigation", 20));
                    if (dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE_PHYSICAL_EXAM_RESULTS.getKey())
                        || dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey())) {
                        param.put("isInvestigation", "是");
                    }
                    excelHeader.add(new ExcelExportEntity("是否等额考察", "isEqualInvestigate", 20));
                    param.put("isEqualInvestigate", dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey()) ? "是" : "");
                } else if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                    excelHeader.add(new ExcelExportEntity("是否进入面试", "isInterview", 20));
                    param.put("isInterview", dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey()) ? "是" : "");
                }
                break;
            case 2:
                if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())
                    || hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                    excelHeader.add(new ExcelExportEntity("是否进入考察范围", "isInvestigation", 20));
                    if (dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey())
                        || dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey())) {
                        param.put("isInvestigation", "是");
                    }

                } else if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                    excelHeader.add(new ExcelExportEntity("是否进入笔试", "isWritten", 20));
                    param.put("isWritten", dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey()) ? "是" : "");
                }
                break;
            case 3://最终成绩

                excelHeader.add(new ExcelExportEntity("是否进入考察范围", "isInvestigation", 20));
                if (dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey())
                    || dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey())) {
                    param.put("isInvestigation", "是");
                }
                excelHeader.add(new ExcelExportEntity("是否等额考察", "isEqualInvestigate", 20));
                param.put("isEqualInvestigate", dto.getStatus().equals(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey()) ? "是" : "");
                break;
            case 4://考察结果
                excelHeader.add(new ExcelExportEntity("考察是否合格", "examResult", 20));
                excelHeader.add(new ExcelExportEntity("是否进行体检", "isPhysicalExam", 20));
                if (hrExamResult.getExamResult() != null) {
                    param.put("examResult", hrExamResult.getExamResult().equals(0) ? "" : "是");
                    param.put("isPhysicalExam", hrExamResult.getExamResult().equals(0) ? "" : "是");
              break;
               }*/


        paramsList.add(param);
        return paramsList;
    }

    /**
     * 拟聘用公示
     *
     * @param hrRegistrationDetailsDTO
     * @return
     */
    @Override
    public ResponseEntity<?> updateHrRegistrationDetails(HrRegistrationDetailsDTO hrRegistrationDetailsDTO) {
        List<String> mationName = new ArrayList<>();
        for (String s : hrRegistrationDetailsDTO.getIdList()) {
            HrRegistrationDetails hrRegistrationDetailss = this.hrRegistrationDetailsRepository.selectById(s);
            if (!hrRegistrationDetailss.getStatus().equals(15)) {
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrRegistrationDetailss.getStaffId());
                mationName.add(hrTalentStaff.getName());
            } else {

                HrRegistrationDetails hrRegistrationDetailsr = this.hrRegistrationDetailsRepository.selectById(s);
                if (hrRegistrationDetailsr.getStatus().equals(15)) {
                    HrRegistrationDetailsDTO hrRegistrationDetailsDTOs = new HrRegistrationDetailsDTO();
                    hrRegistrationDetailsDTOs.setId(s);
                    hrRegistrationDetailsDTOs.setPublicityDate(hrRegistrationDetailsDTO.getPublicityDate());
                    hrRegistrationDetailsDTOs.setPublicityDateEnd(hrRegistrationDetailsDTO.getPublicityDateEnd().plusDays(1));
                    hrRegistrationDetailsDTOs.setHiringPublicity(hrRegistrationDetailsDTO.getHiringPublicity());
                    hrRegistrationDetailsDTOs.setAppendixId(hrRegistrationDetailsDTO.getAppendixId());
                    HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTOs);
                    this.hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                    log.info("Update HrRegistrationDetails:{}", hrRegistrationDetailsDTO);
                    this.hrRegistrationDetailsRepository.updatestas(s);
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", mationName);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mationName)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", mationName) + "的状态不是待拟聘用公示    ");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 查询报名情况详情 todo
     *
     * @param id
     * @return
     */
    @Override
    public HrRegistrationDetailsDTO getHrRegistrationDetails(String id) {
        log.info("Get HrRegistrationDetails :{}", id);
        QueryWrapper<HrRecruitmentBrochure> ew = new QueryWrapper<>();

        HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectById(id);
        HrRegistrationInfo hrRegistrationInfo = this.hrRegistrationInfoRepository.selectById(hrRegistrationDetails.getInfoId());
        HrRegistrationDetailsDTO hrRegistrationDetailsDTO = this.hrRegistrationDetailsMapper.toDto(hrRegistrationDetails);
        ew.eq("id", hrRegistrationDetailsDTO.getBrochureId());
        HrRecruitmentBrochure hrRecruitmentBrochure = this.hrRecruitmentBrochureRepository.selectOne(ew);
        HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRegistrationDetails.getStationId());
        HrClient hrClient = this.hrClientRepository.selectById(hrRecruitmentBrochure.getClientId());
        hrRegistrationDetailsDTO.setClientName(hrClient.getClientName());
        hrRegistrationDetailsDTO.setRegisterTemplateId(hrRecruitmentStation.getRegisterTemplateId());
        hrRegistrationDetailsDTO.setRegistrationJson(hrRegistrationInfo.getRegistrationJson());
        hrRegistrationDetailsDTO.setAppendixJson(hrRegistrationInfo.getAppendixJson());
        hrRegistrationDetailsDTO.setNumbers(hrRegistrationInfo.getNumber());
        return hrRegistrationDetailsDTO;
    }

    /**
     * 删除报名情况
     *
     * @param id
     */
    @Override
    public void deleteHrRegistrationDetails(String id) {
        Optional.ofNullable(this.hrRegistrationDetailsRepository.selectById(id))
            .ifPresent(hrRegistrationDetails -> {
                this.hrRegistrationDetailsRepository.deleteById(id);
                log.info("Delete HrRegistrationDetails:{}", hrRegistrationDetails);
            });
    }

    /**
     * 批量删除报名情况
     *
     * @param ids
     */
    @Override
    public void deleteHrRegistrationDetails(List<String> ids) {
        log.info("Delete HrRegistrationDetailss:{}", ids);
        this.hrRegistrationDetailsRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询报名情况
     *
     * @param hrRegistrationDetailsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrRegistrationDetailsDTO hrRegistrationDetailsDTO, Long pageNumber, Long pageSize) {
        Page<HrRegistrationDetails> page = new Page<>(pageNumber, pageSize);

        IPage<HrRegistrationDetailsDTO> iPage = this.hrRegistrationDetailsRepository.selectPages(page, hrRegistrationDetailsDTO);

        for (HrRegistrationDetailsDTO record : iPage.getRecords()) {


            QueryWrapper<HrApplyOpLogs> qw = new QueryWrapper<>();
            QueryWrapper<HrRegistrationDetailsEvaluation> qws = new QueryWrapper<>();
            qws.eq("details_id", record.getId());
            record.setHrRegistrationDetailsEvaluation(this.hrRegistrationDetailsEvaluationRepository.selectList(qws));
            qw.eq("apply_id", record.getId());
            qw.eq("apply_staff_id", record.getStaffId());
            qw.orderByAsc("created_date");
            List<HrApplyOpLogs> applyOpLogsList = hrApplyOpLogsRepository.selectList(qw);
            List<HrApplyOpLogsDTO> applyOpLogsDtoList = this.hrApplyOpLogsMapper.toDto(applyOpLogsList);
            for (HrApplyOpLogsDTO hrApplyOpLogs : applyOpLogsDtoList) {
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrApplyOpLogs.getCheckerId());
                if (hrTalentStaff != null) {
                    if (hrTalentStaff.getIzDefault() == false) {
                        String staffName = hrTalentStaff.getName() + "(员工)";
                        hrApplyOpLogs.setRealName(staffName);
                    } else {
                        String staffName = hrTalentStaff.getName() + "(人才)";
                        hrApplyOpLogs.setRealName(staffName);
                    }
                } else {
                    User user = this.userRepository.selectById(hrApplyOpLogs.getCheckerId());
                    if (user != null) {
                        String roleName = this.hrRegistrationDetailsRepository.selectRoleName(user.getId());
                        String staffName = user.getRealName() + "(" + roleName + ")";
                        hrApplyOpLogs.setRealName(staffName);
                    }
                }
            }
            record.setApplyOpLogsList(applyOpLogsDtoList);
            //获取这个简章每个岗位的的公告
            QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
            hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", record.getBrochureId());
            hrRecruitmentBulletinQueryWrapper.eq("recruitment_station_name", record.getStationName());
            List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
            record.setHrRecruitmentBulletinList(hrRecruitmentBulletins);
        }

        return iPage;
    }

    /**
     * PUT /hr-registration-detailses
     * <p>
     * 录入面试成绩
     *
     * @param hrRegistrationDetailsDTO
     * @return 传员工id
     * 传岗位名称
     * 面试成绩
     * 面试平均分
     */
    @Override
    public void updateHrRegistration(HrRegistrationDetailsDTO hrRegistrationDetailsDTO) {
        //获取笔试成绩
        QueryWrapper<HrExamResult> ew = new QueryWrapper<>();
        //面试权重
        BigDecimal interviewScoreWeight = hrRegistrationDetailsDTO.getInterviewScoreWeight();
        //笔试权重
        BigDecimal writtenScoreWeight = hrRegistrationDetailsDTO.getWrittenScoreWeight();
        //获取面试及格线
        BigDecimal interviewPassLine = hrRegistrationDetailsDTO.getInterviewPassLine();
        // 现根据考试形式来判断如何计算最终成绩
        if (hrRegistrationDetailsDTO.getExamFormat() == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
            BigDecimal score = hrRegistrationDetailsDTO.getScore();
            if (score != null) {
                BigDecimal multiply = score.multiply(writtenScoreWeight);
                hrRegistrationDetailsDTO.setFinalResult(multiply);
            }
        } else if (hrRegistrationDetailsDTO.getExamFormat() == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
            BigDecimal interviewScoreResult = hrRegistrationDetailsDTO.getInterviewScoreResult();
            if (interviewScoreResult != null) {
                BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
                hrRegistrationDetailsDTO.setFinalResult(multiply);
            }
        } else {
            BigDecimal score = hrRegistrationDetailsDTO.getScore();
            BigDecimal interviewScoreResult = hrRegistrationDetailsDTO.getInterviewScoreResult();
            if (interviewScoreResult != null && score != null) {
                BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
                BigDecimal bigDecimal = score.multiply(writtenScoreWeight);
                BigDecimal add = multiply.add(bigDecimal);
                hrRegistrationDetailsDTO.setFinalResult(add);
            }
        }

        ew.eq("staff_id", hrRegistrationDetailsDTO.getStaffId());
        ew.eq("exam_name", hrRegistrationDetailsDTO.getRecruitBrochureName());
        ew.eq("profession_name", hrRegistrationDetailsDTO.getStationName());
        HrExamResult hrExamResult = this.hrExamResultRepository.selectOne(ew);

        if (hrExamResult == null) {
            if (StringUtils.isNotEmpty(hrRegistrationDetailsDTO.getStaffId())) {
                QueryWrapper<HrExamResult> ews = new QueryWrapper<>();
                ews.eq("exam_name", hrRegistrationDetailsDTO.getRecruitBrochureName());
                ews.eq("profession_name", hrRegistrationDetailsDTO.getStationName());
                ews.eq("staff_id", hrRegistrationDetailsDTO.getStaffId());
                HrExamResult hrExamResultss = this.hrExamResultRepository.selectOne(ews);
                if (hrExamResultss != null) {
                    this.hrExamResultRepository.deleteById(hrExamResultss.getId());
                }
                HrExamResultDTO hrExamResultDTO = new HrExamResultDTO();
                hrExamResultDTO.setStaffId(hrRegistrationDetailsDTO.getStaffId());
                hrExamResultDTO.setInterviewScore(hrRegistrationDetailsDTO.getInterviewScore());
                hrExamResultDTO.setInterviewScoreResult(hrRegistrationDetailsDTO.getInterviewScoreResult());
                hrExamResultDTO.setStaffId(hrRegistrationDetailsDTO.getStaffId());
                hrExamResultDTO.setProfessionName(hrRegistrationDetailsDTO.getStationName());
                hrExamResultDTO.setScore(hrRegistrationDetailsDTO.getScore());
                hrExamResultDTO.setExamName(hrRegistrationDetailsDTO.getRecruitBrochureName());
                HrExamResult hrExamResults = this.hrExamResultMapper.toEntity(hrExamResultDTO);
                this.hrExamResultRepository.insert(hrExamResults);
            }
        } else {
            this.hrRegistrationDetailsRepository.updateExam(hrRegistrationDetailsDTO);
        }
        //获取招聘简章以及对应的岗位
        HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(hrRegistrationDetailsDTO.getBrochureId());
        this.hrExamResultService.updateApplicantStatus(hrRecruitmentBrochure, hrRegistrationDetailsDTO.getStationName(), 2);
    }

    /**
     * PUT /hr-registration-detailses
     * <p>
     * 应是评价
     *
     * @param hrRegistrationDetailsDTO
     * @return 传员工id
     * 传岗位名称
     */
    @Override
    public ResponseEntity<?> updateHrRegistrationEvaluation(HrRegistrationDetailsDTO hrRegistrationDetailsDTO) {
        QueryWrapper<HrRegistrationDetails> qw = new QueryWrapper<>();
        QueryWrapper<HrRegistrationDetailsEvaluation> qws = new QueryWrapper<>();
        qw.eq("staff_id", hrRegistrationDetailsDTO.getStaffId());
        qw.eq("station_name", hrRegistrationDetailsDTO.getStationName());
        qw.eq("brochure_id", hrRegistrationDetailsDTO.getBrochureId());
        HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectOne(qw);
        HrRegistrationDetailsEvaluation hrRegistrationDetailsEvaluation = new HrRegistrationDetailsEvaluation();
        if (hrRegistrationDetails != null) {
            qws.eq("details_id", hrRegistrationDetails.getId());
            qws.eq("evaluation_status", hrRegistrationDetailsDTO.getEvaluationStatus());
            HrRegistrationDetailsEvaluation hrRegistrationDetailsEvaluations = this.hrRegistrationDetailsEvaluationRepository.selectOne(qws);

            if (hrRegistrationDetailsEvaluations == null) {
                hrRegistrationDetailsEvaluation.setDetailsId(hrRegistrationDetails.getId());
                hrRegistrationDetailsEvaluation.setEvaluationStatus(hrRegistrationDetailsDTO.getEvaluationStatus());
                hrRegistrationDetailsEvaluation.setEvaluation(hrRegistrationDetailsDTO.getEvaluation());
                this.hrRegistrationDetailsEvaluationRepository.insert(hrRegistrationDetailsEvaluation);
            } else {
                hrRegistrationDetailsEvaluation.setId(hrRegistrationDetailsEvaluations.getId());
                hrRegistrationDetailsEvaluation.setDetailsId(hrRegistrationDetails.getId());
                hrRegistrationDetailsEvaluation.setEvaluationStatus(hrRegistrationDetailsDTO.getEvaluationStatus());
                hrRegistrationDetailsEvaluation.setEvaluation(hrRegistrationDetailsDTO.getEvaluation());
                this.hrRegistrationDetailsEvaluationRepository.updateById(hrRegistrationDetailsEvaluation);
            }

        }
        return null;
    }

    /**
     * 导入抽签号模板
     *
     * @param response
     * @param brochureId
     */
    @Override
    public void importDrawLotsNumberTemplate(HttpServletResponse response, String brochureId) {
        //获取所有报名中所有数据
        List<HrDrawLotsNumberTemplate> list = hrRegistrationDetailsRepository.importDrawLotsNumberTemplate(brochureId);
        ExcelUtils.exportExcelWithNoHeader(list, "抽签号模板", "sheet1", HrDrawLotsNumberTemplate.class, response);
    }

    /**
     * @param file
     * @param redisKey
     * @return
     */
    @Override
    public ResponseEntity importDrawLotsNumber(MultipartFile file, String redisKey, String brochureId) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();

        ExcelImportResult<HrDrawLotsNumberImport> result =
            ExcelUtils.importExcel(file, 0, 1, true, HrDrawLotsNumberImport.class);
//        int size = result.getList().size();
//        if (size == 0 || size > 1000) {
//            throw new CommonException("最少导入一条数据，最多导入1000条数据");
//        }
        //进度条
        String key = jwtUserDTO.getId() + "Talent" + redisKey;
        redisCache.setCacheObject(key, 0, 10, TimeUnit.MINUTES);
        try {
            //导入数据
            this.saveData(result, key, brochureId);
        } catch (Exception e) {
            redisCache.deleteObject(key);
            throw new CommonException(e.getMessage());
        }
        ImportResultDTO resultDTO;
        try {
            resultDTO = ImportResultUtils.writeErrorFile("导入抽签号" + System.currentTimeMillis(), HrDrawLotsNumberImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            if (resultDTO.getFailureFileUrl() != null) {
                String fileUrl = this.hrAppendixService.uploadErrorImportFile(resultDTO.getFailureFileUrl());
                resultDTO.setFailureFileUrl(fileUrl);
            }
        } catch (IOException e) {
            log.error("抽签号导入异常:{}", e.getMessage());
            return ResponseUtil.buildError(e.getMessage());
        } finally {
            redisCache.deleteObject(key);
        }
        return ResponseUtil.buildSuccess(resultDTO);
    }

    //审核简历模板
    @Override
    public ResponseEntity<?> selectHrRegistrationDetails(HrRegistrationDetailsDTO hrRegistrationDetailsDTO) {
        HrRegistrationDetails hrRegistrationDetails = new HrRegistrationDetails();
        hrRegistrationDetails.setId(hrRegistrationDetailsDTO.getId());
        hrRegistrationDetails.setStatus(hrRegistrationDetailsDTO.getStatus());
        if (StringUtils.isNotBlank(hrRegistrationDetails.getDenialReason())) {
            hrRegistrationDetails.setDenialReason(hrRegistrationDetails.getDenialReason());
        }
        //查询招聘简章
        HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(hrRegistrationDetailsDTO.getBrochureId());
        //查询招聘岗位
        HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRegistrationDetailsDTO.getStationId());
        HrTalentStaff hrTalentSta = this.hrTalentStaffRepository.selectById(hrRegistrationDetailsDTO.getStaffId());
        Integer statuser = null;

        LambdaQueryWrapper<HrRegistrationDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(HrRegistrationDetails::getStatus).eq(HrRegistrationDetails::getId, hrRegistrationDetailsDTO.getId());
        HrRegistrationDetails dbDetail = hrRegistrationDetailsRepository.selectOne(queryWrapper);
        if (dbDetail.getStatus() != 0) {
            throw new CommonException("简历已审核，请勿重复审核");
        }

        if (hrRegistrationDetailsDTO.getStatus().equals(1)) {
            int i = hrRecruitmentStation.getPassNum() + 1;
            this.hrRegistrationDetailsRepository.updatePassNum(i, hrRecruitmentStation.getId());
            if (hrRegistrationDetailsDTO.getExamFormat().equals(1)) {
                if (hrRegistrationDetailsDTO.getIsNeedPay().equals(false)) {
                    statuser = 17;
                } else {
                    statuser = 2;
                }

            } else if (hrRegistrationDetailsDTO.getExamFormat().equals(2)) {
                if (hrRegistrationDetailsDTO.getIsNeedPay().equals(false)) {
                    statuser = 6;
                } else {
                    statuser = 2;
                }
            } else if (hrRegistrationDetailsDTO.getExamFormat().equals(3)) {
                if (hrRegistrationDetailsDTO.getIsNeedPay().equals(false)) {
                    statuser = 6;
                } else {
                    statuser = 2;
                }
            } else if (hrRegistrationDetailsDTO.getExamFormat().equals(4)) {
                if (hrRegistrationDetailsDTO.getIsNeedPay().equals(false)) {
                    statuser = 17;
                } else {
                    statuser = 2;
                }
            }
            //添加日志
            JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
            User user = this.userRepository.selectById(jwtUserDTO.getId());
            String message = user.getRealName() + "审核了简历信息";
            String msg = user.getRealName() + "通过了" + hrTalentSta.getName() + "的报名";
            JSONObject object = new JSONObject();
            object.put("entry", RecruitmentBrochure.RegistrationStatus.TO_BE_REVIEWED.getKey());
            object.put("message", msg);
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrRegistrationDetails.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null, true, null, ServiceCenterEnum.SIGN_UP.getKey());

            QueryWrapper<HrAppletMessage> wq = new QueryWrapper<>();
            wq.eq("staff_id", hrRegistrationDetailsDTO.getStaffId());
            wq.eq("service_id", hrRegistrationDetailsDTO.getId());
            List<HrAppletMessage> hrAppletMessagae = this.hrAppletMessageRepository.selectList(wq);
            if (CollectionUtils.isNotEmpty(hrAppletMessagae)) {
                //小程序消息中心修改
                hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrRegistrationDetails.getId(), hrRegistrationDetailsDTO.getStaffId(), user.getRealName() + "通过了" + hrTalentSta.getName() + "的报名", false, ServiceCenterEnum.SIGN_UP.getValue());

            } else {
                //小程序消息中心
                hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrRegistrationDetails.getId(), hrRegistrationDetailsDTO.getStaffId(), user.getRealName() + "通过了" + hrTalentSta.getName() + "的报名", false, ServiceCenterEnum.SIGN_UP.getValue());

            }

/*
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null,  ServiceCenterEnum.SIGN_UP.getKey());
*/

            if (StringUtils.isNotEmpty(hrTalentSta.getPhone()) && hrTalentSta.getPhone() != null) {
                HashMap<Integer, String> params = new HashMap<>();
                params.put(1, hrTalentSta.getName());
                params.put(2, hrRecruitmentBrochure.getRecruitBrochureName());
                params.put(3, hrRecruitmentStation.getRecruitmentStationName());
                params.put(4, CompanyInfoEnum.FIRST_PART_PHONE_FOUR.getValue());
                this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.RESUME_REVIEW_ADOPT_NOTICE.getTemplateCode(), hrTalentSta.getPhone());
            }
        } else {
            statuser = 1;
            //添加日志
            JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
            User user = this.userRepository.selectById(jwtUserDTO.getId());
            String message = user.getRealName() + "审核拒绝了简历信息，拒绝原因：" + hrRegistrationDetailsDTO.getDenialReason();
            String msg = user.getRealName() + "拒绝了" + hrTalentSta.getName() + "的报名";
            JSONObject object = new JSONObject();
            object.put("entry", RecruitmentBrochure.RegistrationStatus.AUDIT_FAILED.getKey());
            object.put("message", msg);
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrRegistrationDetails.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null, true, null, ServiceCenterEnum.SIGN_UP.getKey());

            if (StringUtils.isNotEmpty(hrTalentSta.getPhone()) && hrTalentSta.getPhone() != null) {
                HashMap<Integer, String> params = new HashMap<>();
                params.put(1, hrTalentSta.getName());
                params.put(2, hrRecruitmentBrochure.getRecruitBrochureName());
                params.put(3, hrRecruitmentStation.getRecruitmentStationName());
                params.put(4, hrRegistrationDetailsDTO.getDenialReason());
                params.put(5, CompanyInfoEnum.FIRST_PART_PHONE_FOUR.getValue());
                this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.RESUME_REVIEW_FAIL_NOTICE.getTemplateCode(), hrTalentSta.getPhone());
            }
        }
        hrRegistrationDetails.setStatus(statuser);
        hrRegistrationDetails.setDenialReason(hrRegistrationDetailsDTO.getDenialReason());
        this.hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
        return null;
    }


    /**
     * 查询该员工所有的报名信息
     *
     * @param staffId 员工Id
     * @return
     */
    @Override
    public List<HrRegistrationDetails> findRegistrationDetailsByStaffId(String staffId) {
        return hrRegistrationDetailsRepository.selectList(new QueryWrapper<HrRegistrationDetails>().eq("staff_id", staffId));
    }

    /**
     * 查询岗位下拉
     *
     * @param
     * @return
     */
    @Override
    public List<HrRegistrationDetails> getHrRegistrationDetailsStation(String id) {
        QueryWrapper<HrRegistrationDetails> wq = new QueryWrapper<>();
        wq.select("station_name");
        wq.eq("brochure_id", id);
        wq.groupBy("station_name");
        List<HrRegistrationDetails> StationList = this.hrRegistrationDetailsRepository.selectList(wq);
        return StationList;
    }


    /**
     * 导出报名情况
     *
     * @param hrRegistrationDetailsDTO
     * @param response
     */
    @Override
    public String detailsExport(HrRegistrationDetailsDTO hrRegistrationDetailsDTO, HttpServletResponse response) {
        List<HrRegistrationDetails> hrRegistrationDetailsList = hrRegistrationDetailsRepository.findRegistrationDetailsList(hrRegistrationDetailsDTO);
        List<String> ids = hrRegistrationDetailsList.stream().map(HrRegistrationDetails::getId).collect(Collectors.toList());
        List<HrContentTemplate> contentTemplateList = hrContentTemplateRepository.selectList(new QueryWrapper<HrContentTemplate>().eq("is_delete", 0));
        List<String> typeList = hrRegistrationDetailsDTO.getTypeList();
        ArrayList<File> fileList = new ArrayList<>();
        //导出报名记录
        if (typeList.contains("1")) {
            Map<Integer, String> examFormat = codeTableService.findCodeTableByInnerName("examFormat");
            Map<Integer, String> applicationSituationState = codeTableService.findCodeTableByInnerName("applicationSituationState");
            //动态导出
            //所有excel字段
            List<ExcelExportEntity> excelHeader = new ArrayList<>();
            //所有参数数据
            List<Map<String, Object>> paramsList = new ArrayList<>();
            //定义excel字段在数据中的idList
            ArrayList<String> paramIdList = new ArrayList<>();
            List<String> detailIds = hrRegistrationDetailsList.stream().map(HrRegistrationDetails::getId).collect(Collectors.toList());
            List<HrTemplateDTO> hrTemplateDTOList = hrRegistrationDetailsRepository.selectBybrochureId(null, detailIds);
            for (HrRegistrationDetails hrRegistrationDetails : hrRegistrationDetailsList) {
                HrTemplateDTO hrTemplateDTO = hrTemplateDTOList.stream().filter(lst -> lst.getId().equals(hrRegistrationDetails.getId())).findAny().orElse(null);
                //查询模板数据
                if (hrTemplateDTO == null) {
                    continue;
                }
                String contentId = hrTemplateDTO.getContentId();
                String regex = ",|，|\\s+";
                if (StringUtils.isNotBlank(contentId)) {
                    List<String> stationIDList = Arrays.asList((contentId.split(regex)));
                    for (String paramId : stationIDList) {
                        if (!paramIdList.contains(paramId)) {
                            paramIdList.add(paramId);
                        }
                    }
                }

            }
            if (CollectionUtils.isEmpty(paramIdList)) {
                throw new CommonException("请先正确配置报名模板");
            }
            //查询excel字段详情信息
            QueryWrapper<HrContentTemplate> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", paramIdList);
            queryWrapper.notIn("type", "img", "appendix", "table");
            queryWrapper.orderByAsc("orders");
            List<HrContentTemplate> hrContractTemplatesList = hrContentTemplateRepository.selectList(queryWrapper);
            //赋值excel字段
            for (HrContentTemplate hrContractTemplate : hrContractTemplatesList) {
                if ("change".equals(hrContractTemplate.getType())) {
                    excelHeader.add(new ExcelExportEntity(hrContractTemplate.getLabel(), hrContractTemplate.getName() + "Label", 20));
                } else {
                    excelHeader.add(new ExcelExportEntity(hrContractTemplate.getLabel(), hrContractTemplate.getName(), 20));
                }
            }
            //遍历所有要导出的数据
            for (HrRegistrationDetails hrRegistrationDetails : hrRegistrationDetailsList) {
                String infoId = hrRegistrationDetails.getInfoId();
                //根据id或取详情
                HrRegistrationDetailsExport hrRegistrationDetailsExport = this.hrRegistrationDetailsRepository.detailsExportById(hrRegistrationDetails.getId());
                //简章id
                //String brochureId = hrRegistrationDetails.getBrochureId();
                //获取报名信息
                HrRegistrationInfo hrRegistrationInfo = hrRegistrationInfoRepository.selectById(infoId);
                if (hrRegistrationInfo == null) {
                    continue;
                }

                //获取模板填入的数据
                String registrationJson = hrRegistrationInfo.getRegistrationJson();
                //赋值excel数据
                HashMap<String, Object> hashMap = new HashMap<>();
                if (StringUtils.isNotBlank(registrationJson)) {
                    JSONObject jsonObject = JSON.parseObject(registrationJson);
                    HrTalentStaffDTO hrTalentStaffsDTO = JSON.parseObject(registrationJson, HrTalentStaffDTO.class);
                    for (Map.Entry<String, Object> stringObjectEntry : jsonObject.entrySet()) {
                        HrContentTemplate hrContentTemplate = contentTemplateList.stream().filter(lst -> lst.getName().equals(stringObjectEntry.getKey())).findFirst().orElse(new HrContentTemplate());
                        if (stringObjectEntry.getValue() != null) {
                            if (stringObjectEntry.getValue().toString().equals("false")) {
                                stringObjectEntry.setValue("否");
                            }
                            if (stringObjectEntry.getValue().toString().equals("true")) {
                                stringObjectEntry.setValue("是");
                            }
                        }
                        //最高学历 第一学历
                        if (stringObjectEntry.getKey().equals("highestEducationLists")) {
                            List<HrStaffEducationDTO> highestEducationLists = hrTalentStaffsDTO.getHighestEducationLists();
                            extracted(excelHeader, hashMap, stringObjectEntry.getKey(), hrContentTemplate.getLabel(), highestEducationLists);
                        }
                        //第一学历
                        else if (stringObjectEntry.getKey().equals("firstEducation")) {
                            List<HrStaffEducationDTO> firstEducation = hrTalentStaffsDTO.getFirstEducation();
                            extracted(excelHeader, hashMap, stringObjectEntry.getKey(), hrContentTemplate.getLabel(), firstEducation);
                        }
                        //教育经历
                        else if (stringObjectEntry.getKey().equals("staffEducation")) {
                            List<HrStaffEducationDTO> staffEducation = hrTalentStaffsDTO.getStaffEducation();
                            extracted(excelHeader, hashMap, stringObjectEntry.getKey(), hrContentTemplate.getLabel(), staffEducation);
                        }
                        //是否服兵役
                        else if (stringObjectEntry.getKey().equals("militaryDate")) {
                            Object value = stringObjectEntry.getValue();
                            if (ObjectUtil.isEmpty(value)) {
                                hashMap.put(stringObjectEntry.getKey(), "");
                            } else {
                                String listDate = value.toString();
                                ArrayList arrayList = JSONObject.parseObject(listDate, ArrayList.class);
                                hashMap.put(stringObjectEntry.getKey(), "服兵役期限：" + arrayList.get(0) + " 至 " + arrayList.get(1));
                            }
                        }
                        //工作经历
                        else if (stringObjectEntry.getKey().equals("workExperience")) {
                            ExcelExportEntity excelExportEntity = new ExcelExportEntity(hrContentTemplate.getLabel(), stringObjectEntry.getKey(), 20);
                            //多层级表头
                            List<ExcelExportEntity> entityList = new ArrayList<>();
                            entityList.add(new ExcelExportEntity("用工单位", "employerUnit", 20));
                            entityList.add(new ExcelExportEntity("职业", "duties", 20));
                            entityList.add(new ExcelExportEntity("起止时间", "boardDateLabel", 20));
                            excelExportEntity.setList(entityList);
                            excelHeader.add(excelExportEntity);

                            StringBuilder employerUnit = new StringBuilder();  //用工单位
                            StringBuilder duties = new StringBuilder(); //职业
                            StringBuilder boardDateLabel = new StringBuilder(); //起止时间
                            List<Map<String, Object>> maps = new ArrayList<>();
                            List<HrStaffWorkExperienceDTO> workExperience = hrTalentStaffsDTO.getWorkExperience();
                            if (CollectionUtils.isNotEmpty(workExperience)) {
                                for (int i = 0; i < workExperience.size(); i++) {
                                    HrStaffWorkExperienceDTO experienceDTO = workExperience.get(i);
                                    employerUnit.append(experienceDTO.getEmployerUnit()).append("\n");
                                    duties.append(experienceDTO.getDuties()).append("\n");
                                    boardDateLabel.append(experienceDTO.getBoardDateLabel()).append("\n");
                                }
                            }
                            Map<String, Object> map = new HashMap<>();
                            map.put("employerUnit", employerUnit);
                            map.put("duties", duties);
                            map.put("boardDateLabel", boardDateLabel);
                            maps.add(map);
                            hashMap.put(stringObjectEntry.getKey(), maps);
                        }
                        //家庭成员情况
                        else if (stringObjectEntry.getKey().equals("staffFamily")) {
                            ExcelExportEntity excelExportEntity = new ExcelExportEntity(hrContentTemplate.getLabel(), stringObjectEntry.getKey(), 20);
                            //多层级表头
                            List<ExcelExportEntity> entityList = new ArrayList<>();
                            entityList.add(new ExcelExportEntity("姓名", "familyName", 20));
                            entityList.add(new ExcelExportEntity("联系方式", "contactWay", 20));
                            entityList.add(new ExcelExportEntity("关系", "relation", 20));
                            entityList.add(new ExcelExportEntity("用工单位", "workUnit", 20));
                            excelExportEntity.setList(entityList);
                            excelHeader.add(excelExportEntity);
                            List<Map<String, Object>> maps = new ArrayList<>();

                            StringBuilder familyName = new StringBuilder();
                            StringBuilder contactWay = new StringBuilder();
                            StringBuilder relation = new StringBuilder();
                            StringBuilder workUnit = new StringBuilder();
                            List<HrStaffFamilyDTO> staffFamilyList = hrTalentStaffsDTO.getStaffFamily();
                            for (HrStaffFamilyDTO familyDTO : staffFamilyList) {
                                familyName.append(familyDTO.getFamilyName()).append("\r\n");
                                contactWay.append(familyDTO.getContactWay()).append("\r\n");
                                relation.append(familyDTO.getRelation()).append("\r\n");
                                workUnit.append(familyDTO.getWorkUnit()).append("\r\n");
                            }
                            Map<String, Object> map = new HashMap<>();
                            map.put("familyName", familyName);
                            map.put("contactWay", contactWay);
                            map.put("relation", relation);
                            map.put("workUnit", workUnit);
                            maps.add(map);
                            hashMap.put(stringObjectEntry.getKey(), maps);
                        }
                        //职业工中
                        else if (stringObjectEntry.getKey().equals("staffQualification")) {
                            ExcelExportEntity excelExportEntity = new ExcelExportEntity(hrContentTemplate.getLabel(), stringObjectEntry.getKey(), 20);
                            //多层级表头
                            List<ExcelExportEntity> entityList = new ArrayList<>();
                            entityList.add(new ExcelExportEntity("职业(工种)资格", "qualificationName", 20));
                            entityList.add(new ExcelExportEntity("等级", "qualificationGradeLabel", 20));
                            entityList.add(new ExcelExportEntity("取得时间", "acquisitionDate", 20));
                            excelExportEntity.setList(entityList);
                            excelHeader.add(excelExportEntity);

                            StringBuilder qualificationName = new StringBuilder();
                            StringBuilder qualificationGradeLabel = new StringBuilder();
                            StringBuilder boardDateLabel = new StringBuilder();
                            List<Map<String, Object>> maps = new ArrayList<>();
                            List<HrStaffQualificationDTO> staffQualification = hrTalentStaffsDTO.getStaffQualification();
                            for (HrStaffQualificationDTO qualificationDTO : staffQualification) {
                                qualificationName.append(qualificationDTO.getQualificationName()).append("\r\n");
                                qualificationGradeLabel.append(qualificationDTO.getQualificationGradeLabel()).append("\r\n");
                                boardDateLabel.append(qualificationDTO.getAcquisitionDate()).append("\r\n");
                            }
                            Map<String, Object> map = new HashMap<>();
                            map.put("qualificationName", qualificationName);
                            map.put("qualificationGradeLabel", qualificationGradeLabel);
                            map.put("boardDateLabel", boardDateLabel);
                            maps.add(map);
                            hashMap.put(stringObjectEntry.getKey(), maps);
                        } else if (stringObjectEntry.getKey().equals("qualification")) {
                            ExcelExportEntity excelExportEntity = new ExcelExportEntity(hrContentTemplate.getLabel(), stringObjectEntry.getKey(), 20);
                            //多层级表头
                            List<ExcelExportEntity> entityList = new ArrayList<>();
                            entityList.add(new ExcelExportEntity("证书名称", "certificateName", 20));
                            entityList.add(new ExcelExportEntity("证书编号", "certificateNo", 20));
                            entityList.add(new ExcelExportEntity("发证单位", "issuingAgency", 20));
                            entityList.add(new ExcelExportEntity("发证日期", "acquisitionDate", 20));
                            excelExportEntity.setList(entityList);
                            excelHeader.add(excelExportEntity);

                            StringBuilder certificateName = new StringBuilder();
                            StringBuilder certificateNo = new StringBuilder();
                            StringBuilder issuingAgency = new StringBuilder();
                            StringBuilder acquisitionDate = new StringBuilder();
                            List<Map<String, Object>> maps = new ArrayList<>();

                            List<HrStaffCertificateDTO> staffCertificateList = hrTalentStaffsDTO.getQualification();
                            for (HrStaffCertificateDTO certificateDTO : staffCertificateList) {
                                certificateName.append(certificateDTO.getCertificateName()).append("\r\n");
                                certificateNo.append(certificateDTO.getCertificateNo()).append("\r\n");
                                issuingAgency.append(certificateDTO.getIssuingAgency()).append("\r\n");
                                acquisitionDate.append(certificateDTO.getAcquisitionDate()).append("\r\n");
                            }
                            Map<String, Object> map = new HashMap<>();
                            map.put("certificateName", certificateName);
                            map.put("certificateNo", certificateNo);
                            map.put("issuingAgency", issuingAgency);
                            map.put("acquisitionDate", acquisitionDate);
                            maps.add(map);
                            hashMap.put(stringObjectEntry.getKey(), maps);
                        } else {
                            hashMap.put(stringObjectEntry.getKey(), stringObjectEntry.getValue());
                        }

                    }
                }
                //数据库中数据，赋值到excel
                JSONObject dateJson = JSON.parseObject(JSONObject.toJSONString(hrRegistrationDetailsExport));
                for (Map.Entry<String, Object> stringObjectEntry : dateJson.entrySet()) {
                    if (stringObjectEntry.getKey().equals("examFormat")) {
                        if (ObjectUtil.isNotEmpty(stringObjectEntry.getValue())) {
                            hashMap.put(stringObjectEntry.getKey(), examFormat.get(Integer.parseInt(stringObjectEntry.getValue().toString())));
                        }

                    } else if (stringObjectEntry.getKey().equals("examResult")) {
                        if (ObjectUtil.isNotEmpty(stringObjectEntry.getValue()) && stringObjectEntry.getValue().toString().equals("1")) {
                            hashMap.put(stringObjectEntry.getKey(), "合格");
                        }
                        if (ObjectUtil.isNotEmpty(stringObjectEntry.getValue()) && stringObjectEntry.getValue().toString().equals("0")) {
                            hashMap.put(stringObjectEntry.getKey(), "不合格");
                        }
                    } else if (stringObjectEntry.getKey().equals("physicalExaminationResult")) {
                        if (ObjectUtil.isNotEmpty(stringObjectEntry.getValue()) && stringObjectEntry.getValue().toString().equals("1")) {
                            hashMap.put(stringObjectEntry.getKey(), "合格");
                        }
                        if (ObjectUtil.isNotEmpty(stringObjectEntry.getValue()) && stringObjectEntry.getValue().toString().equals("0")) {
                            hashMap.put(stringObjectEntry.getKey(), "不合格");
                        }

                    } else if (stringObjectEntry.getKey().equals("status")) {
                        if (ObjectUtil.isNotEmpty(stringObjectEntry.getValue())) {
                            hashMap.put(stringObjectEntry.getKey(), applicationSituationState.get(Integer.parseInt(stringObjectEntry.getValue().toString())));
                        }
                    } else {
                        hashMap.put(stringObjectEntry.getKey(), stringObjectEntry.getValue());
                    }

                }
                // 添加考场和准考证号
                String json = hrRegistrationDetails.getAdmissionTicketInfo();
                if (StringUtils.isNotBlank(json)) {
                    JSONObject info = JSON.parseObject(json);
                    if (info != null) {
                        if (info.containsKey("examRoom")) {
                            hashMap.put("examRoom", info.get("examRoom"));
                        }
                        if (info.containsKey("admissionNumber")) {
                            hashMap.put("admissionNumber", info.get("admissionNumber"));
                        }
                    }
                }

                paramsList.add(hashMap);
            }
            excelHeader.add(new ExcelExportEntity("状态", "status", 20));
            excelHeader.add(new ExcelExportEntity("体检结果", "physicalExaminationResult", 20));
            excelHeader.add(new ExcelExportEntity("考查结果", "examResult", 20));
            excelHeader.add(new ExcelExportEntity("最终成绩", "finalResult", 20));
            excelHeader.add(new ExcelExportEntity("加试成绩", "addResult", 20));
            excelHeader.add(new ExcelExportEntity("面试平均成绩", "interviewScoreResult", 20));
            excelHeader.add(new ExcelExportEntity("面试考官打分", "interviewScore", 20));
            excelHeader.add(new ExcelExportEntity("笔试成绩", "score", 20));
            excelHeader.add(new ExcelExportEntity("考试形式", "examFormat", 20));
            excelHeader.add(new ExcelExportEntity("应聘岗位", "stationName", 20));
            // 添加考场和准考证号
            excelHeader.add(new ExcelExportEntity("考场", "examRoom", 20));
            excelHeader.add(new ExcelExportEntity("准考证号", "admissionNumber", 20));
            //去重
            List<ExcelExportEntity> excelHeaderList = excelHeader.stream().distinct().collect(Collectors.toList());
            fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicColumnExportLocal("报名记录模板", "报名记录", excelHeaderList, paramsList, fileTempPath))));
        }
        //桌贴
        if (typeList.contains("2")) {
            //获取前端传入的html
            String deskStickerHtml = hrRegistrationDetailsDTO.getDeskStickerHtml();
            String html = "";
            for (HrRegistrationDetails hrRegistrationDetails : hrRegistrationDetailsList) {
                String admissionTicketInfo = hrRegistrationDetails.getAdmissionTicketInfo();
                if (StringUtils.isNotBlank(admissionTicketInfo)) {
                    JSONObject info = JSON.parseObject(admissionTicketInfo);
                    //HashMap<String,Object> hashMap = JSON.toJavaObject(info, HashMap.class);
                    Map<String, Object> hashMap = JSON.parseObject(info.toJSONString(), HashMap.class);
                    html += renderString(deskStickerHtml, hashMap);
                }
            }
            if (StringUtils.isNotBlank(html)) {
                String wordName = "报名桌贴";
                String wordFile = hrAppendixService.generateWordFile(html, wordName, ".doc");
                fileList.add(FileUtil.getFile(wordFile, wordName));

            }

        }
        //导出报名表
        List<String> stringArrayList = new ArrayList<>();
        if (typeList.contains("3")) {
            stringArrayList = exportSignUp(fileList, hrRegistrationDetailsList);
        }
        try {
            //日志
            String fileUrsl = this.hrAppendixService.zipAndUploadFile(fileList, "报名");
            this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.RECRUITMENT.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), ids.size(), fileUrsl);
            //删除临时文件
            /*if (CollectionUtils.isNotEmpty(stringArrayList)) {
                for (String name : stringArrayList) {
                    FileUtil.deleteTempFile(name);
                }
            }*/
            return fileUrsl;

        } catch (Exception e) {
            if (CollectionUtils.isNotEmpty(stringArrayList)) {
                for (String name : stringArrayList) {
                    FileUtil.deleteTempFile(name);
                }
            }
            throw new CommonException("导出出现错误：" + e.getMessage());
        }
    }

    /**
     * 最高学历 第一学历   教育经历  赋值excel公共方法
     *
     * @param excelHeader
     * @param hashMap
     * @param highestEducationLists
     */
    private void extracted(List<ExcelExportEntity> excelHeader, HashMap<String, Object> hashMap, String key, String label, List<HrStaffEducationDTO> highestEducationLists) {
        ExcelExportEntity excelExportEntity = new ExcelExportEntity(label, key, 20);
        //多层级表头
        List<ExcelExportEntity> entityList = new ArrayList<>();
        entityList.add(new ExcelExportEntity("学校", "education", 20));
        entityList.add(new ExcelExportEntity("学历", "highestDegreeLabel", 20));
        entityList.add(new ExcelExportEntity("专业", "specialty", 20));
        entityList.add(new ExcelExportEntity("毕业时间", "毕业时间", 20));
        excelExportEntity.setList(entityList);
        excelHeader.add(excelExportEntity);

        StringBuilder highestDegreeLabel = new StringBuilder();  //学历
        StringBuilder education = new StringBuilder(); //学校
        StringBuilder specialty = new StringBuilder(); //专业
        StringBuilder educationEndDate = new StringBuilder(); //毕业时间
        List<Map<String, Object>> maps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(highestEducationLists)) {
            for (int i = 0; i < highestEducationLists.size(); i++) {
                HrStaffEducationDTO hrStaffEducationDTO = highestEducationLists.get(i);
                highestDegreeLabel.append(hrStaffEducationDTO.getHighestDegreeLabel()).append("\r\n");
                education.append(hrStaffEducationDTO.getEducation()).append("\r\n");
                specialty.append(hrStaffEducationDTO.getSpecialty()).append("\r\n");
                educationEndDate.append(hrStaffEducationDTO.getEducationEndDate()).append("\r\n");
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("education", education);
        map.put("highestDegreeLabel", highestDegreeLabel);
        map.put("specialty", specialty);
        map.put("educationEndDate", educationEndDate);
        maps.add(map);
        hashMap.put(key, maps);
    }

    /**
     * 导出时导出人员报名信息
     *
     * @param fileList
     * @param hrRegistrationDetailsList
     * @return
     */
    public List<String> exportSignUp(List<File> fileList, List<HrRegistrationDetails> hrRegistrationDetailsList) {
        //所有文件路径
        List<String> pathList = new ArrayList<>();
        List<String> infoIds = hrRegistrationDetailsList.stream().map(HrRegistrationDetails::getInfoId).collect(Collectors.toList());
        List<HrRegistrationInfo> hrRegistrationInfoList = hrRegistrationInfoRepository.selectBatchIds(infoIds);
        List<String> ids = hrRegistrationDetailsList.stream().map(HrRegistrationDetails::getId).collect(Collectors.toList());
        List<HrTemplateDTO> hrTemplateDTOList = hrRegistrationDetailsRepository.selectBybrochureId(null, ids);
        List<HrContentTemplate> contentTemplateList = hrContentTemplateRepository.selectList(new QueryWrapper<HrContentTemplate>().eq("is_delete", 0));
        for (HrRegistrationDetails hrRegistrationDetails : hrRegistrationDetailsList) {
            long startTime = System.currentTimeMillis();
            List<File> files = new ArrayList<>();
            String infoId = hrRegistrationDetails.getInfoId();
            //获取报名信息
            HrRegistrationInfo hrRegistrationInfo = hrRegistrationInfoList.stream().filter(lst -> lst.getId().equals(infoId)).findFirst().orElse(null);
            if (hrRegistrationInfo == null) {
                continue;
            }

            //获取模板填入的数据
            String registrationJson = hrRegistrationInfo.getRegistrationJson();
            if (StringUtils.isBlank(registrationJson)) {
                continue;
            }
            //查询模板数据
            HrTemplateDTO hrTemplateDTO = hrTemplateDTOList.stream().filter(lst -> lst.getId().equals(hrRegistrationDetails.getId())).findFirst().orElse(null);
            if (hrTemplateDTO == null) {
                continue;
            }
            //json模板
            String preview = hrTemplateDTO.getPreview();
            if (StringUtils.isBlank(preview)) {
                continue;
            }
            JSONObject hrTalentStaff = JSON.parseObject(registrationJson);
            if (hrTalentStaff == null) {
                continue;
            }
            for (Map.Entry<String, Object> stringObjectEntry : hrTalentStaff.entrySet()) {
                String start = "pname=\"";
                String end = "\">";
                String key = stringObjectEntry.getKey();
                if (key.equals("avatar")) {

                    try {
                        String path = stringObjectEntry.getValue().toString();
                        File file = FileUtil.getFile(path, "picture");
                        String suffix = path.substring(path.lastIndexOf(".") + 1);
                        String name = fileTempPath + "picture." + suffix;
                        //修改图片大小
                        ImgUtils.zoomImageScale(file, name, 140);
                        //将图片转化成base64
                        String imageStr = ImgUtils.getImageStr(name);
                        //删除临时文件
                        FileUtil.deleteTempFile(name);
                        imageStr = "data:image/jpeg;base64," + imageStr;
                        String a = "><img width=\"7%\" height=\"7%;\" src=\"";
                        preview = preview.replace("pname=\"avatar\">照片", a + imageStr + "\">");
                    } catch (Exception e) {
                        log.error("图像图片出现错误", e);
                    }
                }
                if (stringObjectEntry.getValue() != null) {
                    if (stringObjectEntry.getValue().toString().equals("false")) {
                        stringObjectEntry.setValue("否");
                    }
                    if (stringObjectEntry.getValue().toString().equals("true")) {
                        stringObjectEntry.setValue("是");
                    }
                }
                preview = preview.replaceAll(start + key + end, ">" + stringObjectEntry.getValue());

            }
            //将json转化成对象
            HrTalentStaffDTO hrTalentStaffsDTO = JSON.parseObject(registrationJson, HrTalentStaffDTO.class);
            //获取最高x学历list
            List<HrStaffEducationDTO> highestEducationLists = hrTalentStaffsDTO.getHighestEducationLists();
            String highestList = "highestEducationLists";
            preview = StringUtil.replaceHtml(preview, highestEducationLists, highestList);
            //第一学历
            List<HrStaffEducationDTO> firstEducation = hrTalentStaffsDTO.getFirstEducation();
            String firstList = "firstEducation";
            preview = StringUtil.replaceHtml(preview, firstEducation, firstList);
            //工作经历
            List<HrStaffWorkExperienceDTO> workExperience = hrTalentStaffsDTO.getWorkExperience();
            String workList = "workExperience";
            preview = StringUtil.replaceHtml(preview, workExperience, workList);
            //教育经历
            List<HrStaffEducationDTO> staffEducation = hrTalentStaffsDTO.getStaffEducation();
            String staffList = "staffEducation";
            preview = StringUtil.replaceHtml(preview, staffEducation, staffList);
            //家庭成员情况
            List<HrStaffFamilyDTO> staffFamilyList = hrTalentStaffsDTO.getStaffFamily();
            preview = StringUtil.replaceHtml(preview, staffFamilyList, "staffFamily");
            //职业工中
            List<HrStaffQualificationDTO> staffQualification = hrTalentStaffsDTO.getStaffQualification();
            preview = StringUtil.replaceHtml(preview, staffQualification, "staffQualification");
            if (StringUtils.isNotBlank(preview)) {
                String name = hrTalentStaff.getString("name");
                String wordName = name + "报名表";
                String wordFile = hrAppendixService.generateWordFile(preview, wordName, ".doc");
                files.add(FileUtil.getFile(wordFile, wordName));
            }
            //获取所有附件
            //查询info 表
            String appendixJson = hrRegistrationInfo.getAppendixJson();
            if (StringUtils.isNotBlank(appendixJson)) {
                log.info("{}进行生成附件。。。。", hrTalentStaff.getString("name"));
                JSONObject json = JSON.parseObject(appendixJson);
                for (Map.Entry<String, Object> stringObjectEntry : json.entrySet()) {
                    String key = stringObjectEntry.getKey();
                    HrContentTemplate hrContentTemplate = contentTemplateList.stream().filter(lst -> lst.getName().equals(key)).findFirst().orElse(null);
                    String hrContentTemplateName = hrContentTemplate == null ? "" : hrContentTemplate.getLabel();
                    JSONObject jsonObject = json.getJSONObject(key);
                    String appendixIds = jsonObject.getString("appendixIds");
                    List<HrAppendix> hrAppendices = JSONArray.parseArray(appendixIds, HrAppendix.class);
                    //获取这个人的所有附件
                    if (CollectionUtils.isNotEmpty(hrAppendices)) {
                        for (HrAppendix hrAppendix : hrAppendices) {
                            File file = FileUtil.getFile(hrAppendix.getFileUrl(), "附件：" + hrContentTemplateName);
                            files.add(file);
                        }
                    }
                }

            }
            String staffName = hrTalentStaff.getString("name");
            String zipMame = tempPath + staffName + System.currentTimeMillis() + ".zip";
            try {
                zipFile(files, zipMame);
                fileList.add(new File(zipMame));
                pathList.add(zipMame);
            } catch (FileNotFoundException e) {
                FileUtil.deleteTempFile(zipMame);
                log.error("压缩出现错误", e);
            }
            long end = System.currentTimeMillis();
            System.out.println(staffName + "压缩处理总耗时：" + (end - startTime) + " ms");
        }
        return pathList;
    }


    /**
     * 考察通知
     *
     * @param hrRegistrationDetailsDTO
     * @return
     */
    @Override
    public ResponseEntity<?> noticeInvestigation(List<HrRegistrationDetailsDTO> hrRegistrationDetailsDTO) {
        List<String> mationName = new ArrayList<>();
        for (HrRegistrationDetailsDTO registrationDetailsDTO : hrRegistrationDetailsDTO) {
            HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectById(registrationDetailsDTO.getId());

            //查询招聘简章
            HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(registrationDetailsDTO.getBrochureId());
            HrRegistrationDetails hrRegistrationDetailsa = this.hrRegistrationDetailsRepository.selectById(registrationDetailsDTO.getId());

            if (hrRegistrationDetailsa.getStatus() != 9 && hrRegistrationDetailsa.getStatus() != 20) {
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrRegistrationDetailsa.getStaffId());
                mationName.add(hrTalentStaff.getName());
            } else {
                //查询招聘岗位
                HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(registrationDetailsDTO.getStationId());
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(registrationDetailsDTO.getStaffId());
                registrationDetailsDTO.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE_INVESTIGATE_EXAM_RESULTS.getKey());
                hrRegistrationDetailsRepository.updateById(hrRegistrationDetailsMapper.toEntity(registrationDetailsDTO));
                HrRecruitmentBulletin hrRecruitmentBulletin = new HrRecruitmentBulletin();
                if (registrationDetailsDTO.getExamFormat().equals(1)) {
                    QueryWrapper<HrRecruitmentBulletin> qws = new QueryWrapper<>();
                    qws.eq("recruitment_brochure_id", registrationDetailsDTO.getBrochureId());
                    qws.eq("recruitment_station_name", hrRegistrationDetails.getStationName());
                    qws.eq("achievement_type", 1);
                    hrRecruitmentBulletin = this.hrRecruitmentBulletinRepository.selectOne(qws);
                }
                if (registrationDetailsDTO.getExamFormat().equals(2)) {
                    QueryWrapper<HrRecruitmentBulletin> qws = new QueryWrapper<>();
                    qws.eq("recruitment_brochure_id", registrationDetailsDTO.getBrochureId());
                    qws.eq("recruitment_station_name", hrRegistrationDetails.getStationName());
                    qws.eq("achievement_type", 2);
                    hrRecruitmentBulletin = this.hrRecruitmentBulletinRepository.selectOne(qws);
                }
                if (registrationDetailsDTO.getExamFormat().equals(3)) {
                    QueryWrapper<HrRecruitmentBulletin> qws = new QueryWrapper<>();
                    qws.eq("recruitment_brochure_id", registrationDetailsDTO.getBrochureId());
                    qws.eq("recruitment_station_name", hrRegistrationDetails.getStationName());
                    qws.eq("achievement_type", 3);
                    hrRecruitmentBulletin = this.hrRecruitmentBulletinRepository.selectOne(qws);
                }
                if (registrationDetailsDTO.getExamFormat().equals(4)) {
                    QueryWrapper<HrRecruitmentBulletin> qws = new QueryWrapper<>();
                    qws.eq("recruitment_brochure_id", registrationDetailsDTO.getBrochureId());
                    qws.eq("recruitment_station_name", hrRegistrationDetails.getStationName());
                    qws.eq("achievement_type", 3);
                    hrRecruitmentBulletin = this.hrRecruitmentBulletinRepository.selectOne(qws);
                }
                if (hrRecruitmentBulletin == null) {
                    throw new CommonException("成绩未发布不能进行考察通知");
                }
                if (hrTalentStaff != null) {
                    if (StringUtils.isNotEmpty(hrTalentStaff.getPhone()) && hrTalentStaff.getPhone() != null) {
                        HashMap<Integer, String> params = new HashMap<>();
                        params.put(1, hrTalentStaff.getName());
                        params.put(2, hrRecruitmentBrochure.getRecruitBrochureName());
                        params.put(3, hrRecruitmentStation.getRecruitmentStationName());
                        LocalDateTime examTime = hrRecruitmentBulletin.getInvestigationStartTime();
                        String dataExamTime = examTime.getYear() + "年" + examTime.getMonthValue() + "月" + examTime.getDayOfMonth() + "日" + examTime.getHour() + "时" + examTime.getMinute() + "分";
                        params.put(4, dataExamTime);//todo 考察时间
                        params.put(5, hrRecruitmentBulletin.getInvestigationPlace());//todo 考察地点
                        params.put(6, CompanyInfoEnum.FIRST_PART_PHONE_FOUR.getValue());
                        this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.NOTICE_INVESTIGATION.getTemplateCode(), hrTalentStaff.getPhone());
                    }
                }

            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", mationName);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mationName)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", mationName) + "的状态不是待通知考察");
        }
        return ResponseUtil.buildSuccess(jsonObject);

    }

    /*体检*/
    @Override
    public ResponseEntity<?> getHrRegistrationDetailsCheckup(List<HrRegistrationDetailsDTO> hrRegistrationDetailsDTO) {
        List<String> mationName = new ArrayList<>();
        for (HrRegistrationDetailsDTO registrationDetailsDTO : hrRegistrationDetailsDTO) {

            HrRegistrationDetails hrRegistrationDetailsa = this.hrRegistrationDetailsRepository.selectById(registrationDetailsDTO.getId());
            if (!hrRegistrationDetailsa.getStatus().equals(12)) {
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrRegistrationDetailsa.getStaffId());
                mationName.add(hrTalentStaff.getName());
            } else {
                HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsMapper.toEntity(registrationDetailsDTO);
                this.hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                //查询招聘简章
                HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(registrationDetailsDTO.getBrochureId());
                //查询招聘岗位
                HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(registrationDetailsDTO.getStationId());
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(registrationDetailsDTO.getStaffId());
                registrationDetailsDTO.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE_PHYSICAL_EXAM_RESULTS.getKey());
                hrRegistrationDetailsRepository.updateById(hrRegistrationDetailsMapper.toEntity(registrationDetailsDTO));
                if (StringUtils.isNotEmpty(registrationDetailsDTO.getPhone()) && registrationDetailsDTO.getPhone() != null) {
                    String checkupDat = String.valueOf(registrationDetailsDTO.getCheckupDate());
                    HashMap<Integer, String> params = new HashMap<>();
                    params.put(1, hrTalentStaff.getName());
                    params.put(2, hrRecruitmentBrochure.getRecruitBrochureName());
                    params.put(3, hrRecruitmentStation.getRecruitmentStationName());
                    params.put(4, checkupDat);//todo 考察时间
                    params.put(5, registrationDetailsDTO.getCheckupPlace());//todo 考察地点
                    params.put(6, CompanyInfoEnum.FIRST_PART_PHONE_FOUR.getValue());
                    this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.NOTICE_PHYSICAL_EXAM.getTemplateCode(), hrTalentStaff.getPhone());
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", mationName);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mationName)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", mationName) + "的状态不是待通知体检");
        }
        return ResponseUtil.buildSuccess(jsonObject);


    }

    //批量审核
    @Override
    public ResponseEntity<?> getHrRegistrationDetailsReview(HrRegistrationDetailsDTO hrRegistrationDetailsDTO) {
        List<String> mationName = new ArrayList<>();
        for (String id : hrRegistrationDetailsDTO.getIds()) {
            HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectById(id);
            HrTalentStaff hrTalentSta = this.hrTalentStaffRepository.selectById(hrRegistrationDetails.getStaffId());
            //查询招聘简章
            HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(hrRegistrationDetails.getBrochureId());
            //查询招聘岗位
            HrRecruitmentStation hrRecruitmentStations = hrRecruitmentStationRepository.selectById(hrRegistrationDetails.getStationId());
            if (!hrRegistrationDetails.getStatus().equals(0)) {
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrRegistrationDetails.getStaffId());
                mationName.add(hrTalentStaff.getName());
            } else {

                String status = "";
                if (hrRegistrationDetailsDTO.getTypes().equals("1")) {
                    status = "1";
                    this.hrRegistrationDetailsRepository.updateStatusReviews(id, status, hrRegistrationDetailsDTO.getDenialReason());
                    //添加日志
                    JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
                    User user = this.userRepository.selectById(jwtUserDTO.getId());
                    String message = user.getRealName() + "审核拒绝了简历信息，拒绝原因：" + hrRegistrationDetailsDTO.getDenialReason();
                    String msg = user.getRealName() + "拒绝了" + hrTalentSta.getName() + "的报名";
                    JSONObject object = new JSONObject();
                    object.put("entry", RecruitmentBrochure.RegistrationStatus.AUDIT_FAILED.getKey());
                    object.put("message", msg);

                    hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrRegistrationDetails.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null, true, null, ServiceCenterEnum.SIGN_UP.getKey());
                    QueryWrapper<HrAppletMessage> wq = new QueryWrapper<>();
                    wq.eq("staff_id", hrRegistrationDetailsDTO.getStaffId());
                    wq.eq("service_id", hrRegistrationDetailsDTO.getId());
                    List<HrAppletMessage> hrAppletMessagae = this.hrAppletMessageRepository.selectList(wq);
                    if (CollectionUtils.isNotEmpty(hrAppletMessagae)) {
                        //小程序消息中心修改
                        hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrRegistrationDetails.getId(), hrRegistrationDetails.getStaffId(), user.getRealName() + "拒绝了" + hrTalentSta.getName() + "的报名", false, ServiceCenterEnum.SIGN_UP.getValue());

                    } else {
                        //小程序消息中心
                        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrRegistrationDetails.getId(), hrRegistrationDetails.getStaffId(), user.getRealName() + "拒绝了" + hrTalentSta.getName() + "的报名", false, ServiceCenterEnum.SIGN_UP.getValue());

                    }

/*
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null,  ServiceCenterEnum.SIGN_UP.getKey());
*/
                    //添加操作信息--小程序

                } else {
                    HrRecruitmentStation hrRecruitmentStation = this.hrRecruitmentStationRepository.selectById(hrRegistrationDetails.getStationId());
                    if (hrRecruitmentStation.getExamFormat().equals(1)) {
                        if (hrRecruitmentStation.getIsNeedPay().equals(false)) {
                            status = "17";
                        } else {
                            status = "2";
                        }

                    } else if (hrRecruitmentStation.getExamFormat().equals(2)) {
                        if (hrRecruitmentStation.getIsNeedPay().equals(false)) {
                            status = "6";
                        } else {
                            status = "2";
                        }
                    } else if (hrRecruitmentStation.getExamFormat().equals(3)) {
                        if (hrRecruitmentStation.getIsNeedPay().equals(false)) {
                            status = "6";
                        } else {
                            status = "2";
                        }
                    } else if (hrRecruitmentStation.getExamFormat().equals(4)) {
                        if (hrRecruitmentStation.getIsNeedPay().equals(false)) {
                            status = "17";
                        } else {
                            status = "2";
                        }
                    }
                    this.hrRegistrationDetailsRepository.updateStatusReview(id, status);
                    //添加日志
                    JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
                    User user = this.userRepository.selectById(jwtUserDTO.getId());
                    String message = user.getRealName() + "审核了简历信息";
                    String msg = user.getRealName() + "通过了" + hrTalentSta.getName() + "的报名";
                    JSONObject object = new JSONObject();
                    object.put("entry", RecruitmentBrochure.RegistrationStatus.TO_BE_REVIEWED.getKey());
                    object.put("message", msg);
                    hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrRegistrationDetails.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null, true, null, ServiceCenterEnum.SIGN_UP.getKey());
                    QueryWrapper<HrAppletMessage> wq = new QueryWrapper<>();
                    wq.eq("staff_id", hrRegistrationDetailsDTO.getStaffId());
                    wq.eq("service_id", hrRegistrationDetailsDTO.getId());
                    List<HrAppletMessage> hrAppletMessagae = this.hrAppletMessageRepository.selectList(wq);
                    if (CollectionUtils.isNotEmpty(hrAppletMessagae)) {
                        //小程序消息中心修改
                        hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrRegistrationDetails.getId(), hrRegistrationDetails.getStaffId(), user.getRealName() + "拒绝了" + hrTalentSta.getName() + "的报名", false, ServiceCenterEnum.SIGN_UP.getValue());

                    } else {
                        //小程序消息中心
                        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrRegistrationDetails.getId(), hrRegistrationDetails.getStaffId(), user.getRealName() + "通过了" + hrTalentSta.getName() + "的报名", false, ServiceCenterEnum.SIGN_UP.getValue());

                    }

                }


/*
                this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null,  ServiceCenterEnum.SIGN_UP.getKey());
*/

                if (StringUtils.isNotEmpty(hrTalentSta.getPhone()) && hrTalentSta.getPhone() != null) {
                    HashMap<Integer, String> params = new HashMap<>();
                    params.put(1, hrTalentSta.getName());
                    params.put(2, hrRecruitmentBrochure.getRecruitBrochureName());
                    params.put(3, hrRecruitmentStations.getRecruitmentStationName());
                    params.put(4, CompanyInfoEnum.FIRST_PART_PHONE_FOUR.getValue());
                    this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.RESUME_REVIEW_ADOPT_NOTICE.getTemplateCode(), hrTalentSta.getPhone());
                }
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", mationName);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mationName)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", mationName) + "的状态不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 考察体检成绩
     *
     * @param
     * @return
     */
    @Override
    public ResponseEntity<?> getHrRegistrationDetailsReviewCheckup(HrExamResultDTO hrExamResultDTO) {
        if (StringUtils.isNotEmpty(hrExamResultDTO.getTypes())) {
            //考察
            if (hrExamResultDTO.getTypes().equals("0")) {
                if (hrExamResultDTO.getExamResult() != null) {
                    String type = "";
                    this.hrRegistrationDetailsRepository.updateAchievement(hrExamResultDTO);
                    if (hrExamResultDTO.getExamResult().equals(0)) {
                        String status = "11";
                        type = "0";
                        this.hrRegistrationDetailsRepository.updatestatuss(status, hrExamResultDTO.getId());
                    } else {
                        type = "1";
                        String status = "12";
                        this.hrRegistrationDetailsRepository.updatestatuss(status, hrExamResultDTO.getId());
                    }
                    //添加日志
                    HrTalentStaff hrTalentSta = this.hrTalentStaffRepository.selectById(hrExamResultDTO.getStaffId());
                    JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
                    User user = this.userRepository.selectById(jwtUserDTO.getId());
                    String status = "";
                    if (type.equals("0")) {
                        status = "不合格";
                    } else {
                        status = "合格";
                    }
                    String message = user.getRealName() + "录入了" + hrTalentSta.getName() + "的考察成绩,考查结果" + status + ".";
                    String msg = user.getRealName() + "录入了" + hrTalentSta.getName() + "的考察成绩,考查结果" + status + ".";
                    JSONObject object = new JSONObject();
                    object.put("entry", RecruitmentBrochure.RegistrationStatus.TO_BE_REVIEWED.getKey());
                    object.put("message", msg);
                    hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrExamResultDTO.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null, true, null, ServiceCenterEnum.SIGN_UP.getKey());
                    QueryWrapper<HrAppletMessage> wq = new QueryWrapper<>();
                    wq.eq("staff_id", hrExamResultDTO.getStaffId());
                    wq.eq("service_id", hrExamResultDTO.getId());
                    List<HrAppletMessage> hrAppletMessagae = this.hrAppletMessageRepository.selectList(wq);
                    if (CollectionUtils.isNotEmpty(hrAppletMessagae)) {
                        //小程序消息中心
                        hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrExamResultDTO.getId(), hrExamResultDTO.getStaffId(), "录入了" + hrTalentSta.getName() + "的考察成绩,考查结果" + status + ".", false, ServiceCenterEnum.SIGN_UP.getValue());

                    } else {
                        //小程序消息中心
                        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrExamResultDTO.getId(), hrExamResultDTO.getStaffId(), "录入了" + hrTalentSta.getName() + "的考察成绩,考查结果" + status + ".", false, ServiceCenterEnum.SIGN_UP.getValue());

                    }

/*
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrExamResultDTO.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null,  ServiceCenterEnum.SIGN_UP.getKey());
*/

                }
            }
            //体检
            else {
                if (hrExamResultDTO.getPhysicalExaminationResult() != null) {
                    String type = "";
                    this.hrRegistrationDetailsRepository.updateAchievements(hrExamResultDTO);
                    if (hrExamResultDTO.getPhysicalExaminationResult().equals(0)) {
                        String status = "14";
                        type = "0";
                        this.hrRegistrationDetailsRepository.updatestatuss(status, hrExamResultDTO.getId());
                    } else {
                        String status = "15";
                        type = "1";
                        this.hrRegistrationDetailsRepository.updatestatuss(status, hrExamResultDTO.getId());
                    }
                    //添加日志
                    HrTalentStaff hrTalentSta = this.hrTalentStaffRepository.selectById(hrExamResultDTO.getStaffId());
                    JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
                    User user = this.userRepository.selectById(jwtUserDTO.getId());
                    String status = "";
                    if (type.equals("0")) {
                        status = "不合格";
                    } else {
                        status = "合格";
                    }
                    String message = user.getRealName() + "录入了" + hrTalentSta.getName() + "的体检成绩,体检结果" + status + ".";
                    String msg = user.getRealName() + "录入了" + hrTalentSta.getName() + "的体检成绩,体检结果" + status + ".";
                    JSONObject object = new JSONObject();
                    object.put("entry", RecruitmentBrochure.RegistrationStatus.TO_BE_REVIEWED.getKey());
                    object.put("message", msg);
                    hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrExamResultDTO.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null, true, null, ServiceCenterEnum.SIGN_UP.getKey());
                    QueryWrapper<HrAppletMessage> wq = new QueryWrapper<>();
                    wq.eq("staff_id", hrExamResultDTO.getStaffId());
                    wq.eq("service_id", hrExamResultDTO.getId());
                    List<HrAppletMessage> hrAppletMessagae = this.hrAppletMessageRepository.selectList(wq);
                    if (CollectionUtils.isNotEmpty(hrAppletMessagae)) {
                        //小程序消息中心
                        hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrExamResultDTO.getId(), hrExamResultDTO.getStaffId(), "录入了" + hrTalentSta.getName() + "的体检成绩,体检结果" + status + ".", false, ServiceCenterEnum.SIGN_UP.getValue());

                    } else {
                        //小程序消息中心
                        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrExamResultDTO.getId(), hrExamResultDTO.getStaffId(), "录入了" + hrTalentSta.getName() + "的体检成绩,体检结果" + status + ".", false, ServiceCenterEnum.SIGN_UP.getValue());

                    }

/*
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrExamResultDTO.getId(), hrTalentSta.getId(), jwtUserDTO.getId(), message + "####" + object.toString(), null,  ServiceCenterEnum.SIGN_UP.getKey());
*/

                }
            }
        }
        return null;
    }


    //入职消息提醒
    @Override
    public ResponseEntity<?> getHrRegistrationInformation(List<String> id) {
        List<String> mationName = new ArrayList<>();

        for (String i : id) {

            HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectById(i);
            if (!hrRegistrationDetails.getStatus().equals(16)) {
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrRegistrationDetails.getStaffId());
                mationName.add(hrTalentStaff.getName());
            } else {

                this.hrRegistrationDetailsRepository.updateState(i);
                //招聘结束状态
                this.hrRegistrationDetailsRepository.updateBrochureStates(hrRegistrationDetails.getBrochureId());
                HrRecruitmentBrochure hrRecruitmentBrochure = this.hrRecruitmentBrochureRepository.selectById(hrRegistrationDetails.getBrochureId());
                HrMessageListDTO hrMessageListDTO = new HrMessageListDTO();
                hrMessageListDTO.setTitle("招聘入职人员消息");
                hrMessageListDTO.setType("通知");
                JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
                hrMessageListDTO.setCreatedById(jwtUserDTO.getId());
                hrMessageListDTO.setContent(hrRecruitmentBrochure.getRecruitBrochureName() + "简章的入职人员待进行处理,请尽快到招聘详情进行处理！");
                hrMessageListDTO.setContentType(1);
                HrMessageList hrMessageList = this.hrMessageListMapper.toEntity(hrMessageListDTO);
                this.hrMessageListRepository.insert(hrMessageList);
                List<String> list = new ArrayList<>();

                HrClient hrClient = this.hrClientRepository.selectById(hrRegistrationDetails.getClientId());
                list.add(hrClient.getSpecializedId());
                list.add(hrClient.getUserId());

                for (String s : list) {
                    HrMessageRoleDTO hrMessageRoleDTO = new HrMessageRoleDTO();
                    hrMessageRoleDTO.setMessageId(hrMessageList.getId());
                    hrMessageRoleDTO.setUserId(s);
                    hrMessageRoleDTO.setTop(1);
           /*     SimpleDateFormat sdf = new SimpleDateFormat();
                sdf.applyPattern("yyyy-MM-dd HH:mm:ss a");
                Date date = new Date();
                hrMessageRoleDTO.setTopDate(LocalDateTime.parse(sdf.format(date)));*/
                    HrMessageRole hrMessageRole = this.hrMessageRoleMapper.toEntity(hrMessageRoleDTO);
                    this.hrMessageRoleRepository.insert(hrMessageRole);
                }
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", mationName);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(mationName)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", mationName) + "的状态不是转入职");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    private void saveData(ExcelImportResult<HrDrawLotsNumberImport> result, String key, String brochureId) {
        int listSize = result.getList().size();
        int scale = 0;
        Map<String, List<HrDrawLotsNumberImport>> hashMap = result.getList().stream().collect(Collectors.groupingBy(o -> o.getStationName() + "_" + o.getDrawLotsNumber()));

        for (HrDrawLotsNumberImport hrDrawLotsNumberImport : result.getList()) {
            try {
                String drawLotsNumber = hrDrawLotsNumberImport.getDrawLotsNumber();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(drawLotsNumber)) {
                    if (drawLotsNumber.matches("^[\\u4e00-\\u9fa5_0-9]+$")) {
                        if (drawLotsNumber.matches("[0-9]+")) {
                            String keys = hrDrawLotsNumberImport.getStationName() + "_" + drawLotsNumber;
                            List<HrDrawLotsNumberImport> hrDrawLotsNumberImports = hashMap.get(keys);
                            if (CollectionUtils.isNotEmpty(hrDrawLotsNumberImports) && hrDrawLotsNumberImports.size() > 1) {
                                throw new CommonException("同岗位的抽签号不能重复");
                            }
                        }
                    } else {
                        throw new CommonException("请正确输入抽签号，抽签号是数字或者汉字");
                    }
                }
                //保存标签号
                QueryWrapper<HrTalentStaff> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("certificate_num", hrDrawLotsNumberImport.getCertificateNum());
                List<HrTalentStaff> talentStaffList = hrTalentStaffService.list(queryWrapper);
                if (CollectionUtils.isEmpty(talentStaffList)) {
                    throw new CommonException("系统中不存在该员工");
                }
                //查询报名表的数据
                QueryWrapper<HrRegistrationDetails> hrRegistrationDetailsQueryWrapper = new QueryWrapper<>();
                hrRegistrationDetailsQueryWrapper.eq("staff_id", talentStaffList.get(0).getId());
                hrRegistrationDetailsQueryWrapper.eq("station_name", hrDrawLotsNumberImport.getStationName());
                hrRegistrationDetailsQueryWrapper.eq("brochure_id", brochureId);
                List<HrRegistrationDetails> list = list(hrRegistrationDetailsQueryWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    throw new CommonException("该员工没有报名");
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(hrDrawLotsNumberImport.getDrawLotsNumber())) {
                    HrRegistrationDetails hrRegistrationDetails = list.get(0);
                    hrRegistrationDetails.setNumber(hrDrawLotsNumberImport.getDrawLotsNumber());
                    updateById(hrRegistrationDetails);
                }
            } catch (Exception e) {
                log.error("保存抽签号异常:{}", e.getMessage());
                hrDrawLotsNumberImport.setErrorMsg(e.getMessage());
            } finally {
                scale++;
                int i = CalculateUtils.calculationProgress(scale, listSize);
                redisCache.setCacheObject(key, i, 10, TimeUnit.MINUTES);
            }

        }
    }


}
