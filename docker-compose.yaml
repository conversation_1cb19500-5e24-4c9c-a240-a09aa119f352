#docker-compose up -d
version: "3.7"
services:
  base-minio:
    image: minio/minio
    container_name: base-minio
    volumes:
      - /data/miniodata:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: asdf.1234
    command: server /data --console-address ":9001"
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
