package cn.casair.repository;

import cn.casair.domain.HrSpecialDeduction;
import cn.casair.dto.HrSpecialDeductionDTO;
import cn.casair.dto.excel.HrSpecialDeductionExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * 数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-28
 */
@Repository
public interface HrSpecialDeductionRepository extends BaseMapper<HrSpecialDeduction> {

    IPage<HrSpecialDeductionDTO> Page(Page<HrSpecialDeduction> page, @Param("param1") HrSpecialDeductionDTO hrSpecialDeductionDTO);

    HrSpecialDeductionExport selectClientId(String id);

    /**
     * @param clientId
     * @param startDate
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2022/2/24
     **/
    BigDecimal getSumCurrentIncome(@Param("clientId") String clientId, @Param("startDate") LocalDate startDate);

    /**
     * 获取员工某月专项扣除
     *
     * @param certificateNum
     * @param startDate
     * @return cn.casair.domain.HrSpecialDeduction
     * <AUTHOR>
     * @date 2021/11/7
     **/
    HrSpecialDeduction getByStaffIdAndStartDate(@Param("certificateNum") String certificateNum, @Param("startDate") LocalDate startDate);

    /**
     *  查询某一期账单,所有员工对应的上月专项扣除
     * @param billId
     * @param startDate
     * @return
     */
    List<HrSpecialDeductionDTO> getByBillIdAndStartDate(@Param("billId") String billId, @Param("startDate") LocalDate startDate);

    List<HrSpecialDeductionExport> findList(@Param("param1") HrSpecialDeductionDTO hrSpecialDeductionDTO);

    List<HrSpecialDeduction> getAllByStartDate(@Param("endDate") LocalDate endDate, @Param("startDate") LocalDate startDate);

    /**
     * 获取每个客户的本期收入
     * @param startDate
     * @return
     */
    List<Map<String, BigDecimal>> getSumCurrentIncomeGroupClient(@Param("startDate") LocalDate startDate);

}
