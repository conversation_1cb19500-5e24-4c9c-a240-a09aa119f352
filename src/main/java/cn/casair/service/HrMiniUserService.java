package cn.casair.service;

import cn.casair.domain.HrMiniUser;
import cn.casair.dto.HrMiniUserDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 微信小程序用户服务类
 *
 * <AUTHOR>
 * @since 2021-11-20
 */
public interface HrMiniUserService extends IService<HrMiniUser> {


    /**
     * 创建微信小程序用户
     *
     * @param hrMiniUserDTO
     * @return
     */
    HrMiniUserDTO createHrMiniUser(HrMiniUserDTO hrMiniUserDTO);

    /**
     * 修改微信小程序用户
     *
     * @param hrMiniUserDTO
     * @return
     */
    Optional<HrMiniUserDTO> updateHrMiniUser(HrMiniUserDTO hrMiniUserDTO);

    /**
     * 查询微信小程序用户详情
     *
     * @param id
     * @return
     */
    HrMiniUserDTO getHrMiniUser(String id);

    /**
     * 删除微信小程序用户
     *
     * @param id
     */
    void deleteHrMiniUser(String id);

    /**
     * 批量删除微信小程序用户
     *
     * @param ids
     */
    void deleteHrMiniUser(List<String> ids);

    /**
     * 分页查询微信小程序用户
     *
     * @param hrMiniUserDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrMiniUserDTO hrMiniUserDTO, Long pageNumber, Long pageSize);

}
