package cn.casair.repository;

import cn.casair.domain.HrBillInvoiceRecord;
import cn.casair.dto.HrBillInvoiceRecordDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 开票明细数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Repository
public interface HrBillInvoiceRecordRepository extends BaseMapper<HrBillInvoiceRecord> {

    /**
     * 根据申请发票id删除发票明细数据
     * @param id
     */
    @Update("UPDATE hr_bill_invoice_record SET is_delete = 1,last_modified_date = NOW() WHERE invoice_id = #{invoiceId}")
    void deleteByInvoiceId(@Param("invoiceId") String id);

    /**
     * 修改开票明细状态
     * @param ids
     * @param state
     */
    void updateState(@Param("ids") List<String> ids, @Param("state") Integer state);

    @Update("UPDATE hr_bill_invoice_record SET level_id = #{levelId} WHERE id = #{id}")
    void updateLevelId(@Param("id") String id, @Param("levelId") String levelId);

    /**
     * 根据开票ID修改开票明细状态
     * @param invoiceIds 开票IDS
     * @param invoiceTypes 开票类型
     * @param state 锁定状态
     */
    int updateStateByInvoiceId(@Param("invoiceIds") List<String> invoiceIds, @Param("invoiceTypes") List<HrBillInvoiceRecordDTO> invoiceTypes, @Param("state") Integer state);

    /**
     * 根据发票id获取发票明细
     * @param invoiceId 发票ID
     * @return 发票明细
     */
    List<HrBillInvoiceRecordDTO> getByInvoiceId(@Param("invoiceId") String invoiceId);

    /**
     * 根据明细ID查询
     * @param invoiceRecordIds
     * @return
     */
    List<HrBillInvoiceRecordDTO> getByIdBatch(@Param("invoiceRecordIds") List<String> invoiceRecordIds);

}
