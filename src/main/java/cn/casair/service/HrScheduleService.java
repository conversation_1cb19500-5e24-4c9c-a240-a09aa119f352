package cn.casair.service;

import cn.casair.domain.HrSchedule;
import cn.casair.dto.HrScheduleDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 日程表服务类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
public interface HrScheduleService extends IService<HrSchedule> {


    /**
     * 创建日程表
     *
     * @param hrScheduleDTO
     * @return
     */
    HrScheduleDTO createHrSchedule(HrScheduleDTO hrScheduleDTO);

    /**
     * 修改日程表
     *
     * @param hrScheduleDTO
     * @return
     */
    Optional<HrScheduleDTO> updateHrSchedule(HrScheduleDTO hrScheduleDTO);

    /**
     * 查询日程表详情
     *
     * @param id
     * @return
     */
    HrScheduleDTO getHrSchedule(String id);

    /**
     * 删除日程表
     *
     * @param id
     */
    void deleteHrSchedule(String id);

    /**
     * 批量删除日程表
     *
     * @param ids
     */
    void deleteHrSchedule(List<String> ids);

    /**
     * 分页查询日程表
     *
     * @param hrScheduleDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrScheduleDTO hrScheduleDTO, Long pageNumber, Long pageSize);
}
