package cn.casair.repository;

import cn.casair.domain.HrStaffWorkExperience;
import cn.casair.dto.HrStaffWorkExperienceDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 工作经历数据库操作类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Repository
public interface HrStaffWorkExperienceRepository extends BaseMapper<HrStaffWorkExperience> {

    /**
     * 获取员工最近的入职经历
     *
     * @param staffId 员工id
     * @return
     */
    HrStaffWorkExperience getStaffCurrentWorkExperience(@Param("staffId") String staffId);

    /**
     * 获取员工最新的工作信息
     *
     * @param staffId
     * @param clientId
     * @return cn.casair.domain.HrStaffWorkExperience
     * <AUTHOR>
     * @date 2021/12/6
     **/
    HrStaffWorkExperience selectStaffNewstWorkExperience(@Param("staffId") String staffId, @Param("clientId") String clientId);

    /**
     * 根据员工ID查询工作经历
     *
     * @param staffId   员工ID
     * @param izDefault
     * @return
     */
    List<HrStaffWorkExperienceDTO> findWorkExperienceList(@Param("staffId") String staffId, @Param("izDefault") Boolean izDefault);

    /**
     * 查询工作经历详情
     *
     * @param id
     * @return
     */
    HrStaffWorkExperienceDTO selectWorkExperienceById(String id);

    /**
     * 查询被删除的在职信息
     *
     * @param staffId
     * @param clientId
     * @return
     */
    HrStaffWorkExperience findDelExperience(@Param("staffId") String staffId, @Param("clientId") String clientId);

    void updateDelExperience(String id);
}
