package cn.casair.service.impl;

import cn.casair.cache.RedisCache;
import cn.casair.common.Constants;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.ValidateUtil;
import cn.casair.common.utils.VerifyCodeUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.mapper.*;
import cn.casair.repository.*;
import cn.casair.security.jwt.TokenProvider;
import cn.casair.service.*;
import cn.casair.service.component.ecloud.ECloudComponent;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.agile.ecloud.sdk.bean.ECloudDomain;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 报名信息表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrRegistrationInfoServiceImpl extends ServiceImpl<HrRegistrationInfoRepository, HrRegistrationInfo> implements HrRegistrationInfoService {
    private final HrStaffEducationMapper hrStaffEducationMapper;
    private final HrStaffEducationRepository hrStaffEducationRepository;
    private final HrRegistrationInfoRepository hrRegistrationInfoRepository;
    private final HrRegistrationInfoMapper hrRegistrationInfoMapper;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrStaffSignCertService hrStaffSignCertService;
    private final HrAppletMessageService hrAppletMessageService;
    private final ECloudComponent eCloudComponent;
    private final HrStaffFamilyMapper hrStaffFamilyMapper;
    private final HrStaffQualificationRepository hrStaffQualificationRepository;
    private final HrStaffQualificationMapper hrStaffQualificationMapper;
    private final HrTalentStaffMapper hrTalentStaffMapper;
    private final HrRegistrationDetailsRepository hrRegistrationDetailsRepository;
    private final HrRegistrationDetailsMapper hrRegistrationDetailsMapper;
    private final RedisCache redisCache;
    private final HrRecruitmentBrochureRepository hrRecruitmentBrochureRepository;
    private final HrApplyOpLogsRepository hrApplyOpLogsRepository;
    private final HrApplyOpLogsMapper hrApplyOpLogsMapper;
    private final HrStaffFieldRepository hrStaffFieldRepository;
    private final HrStaffFieldMapper hrStaffFieldMapper;
    private final HrSmsTemplateService hrSmsTemplateService;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;
    private final HrStaffFamilyRepository hrStaffFamilyRepository;
    private final HrStaffWorkExperienceMapper hrStaffWorkExperienceMapper;
    private final TokenProvider tokenProvider;
    private final HrStaffCertificateRepository hrStaffCertificateRepository;
    private final HrStaffCertificateMapper hrStaffCertificateMapper;


    @Data
    static class JWTToken {
        private HrTalentStaff hrTalentStaff;
        private String token;

        JWTToken(String token) {
            this.token = token;
        }
    }

    /**
     * 创建报名信息表
     *
     * @param hrRegistrationInfoDTO
     * @return
     */
    @Override
    public HrRegistrationInfoDTO createHrRegistrationInfo(HrRegistrationInfoDTO hrRegistrationInfoDTO) {
        log.info("Create new HrRegistrationInfo:{}", hrRegistrationInfoDTO);
        //解决因网络重复提交问题
        //json 转人才表
        String regJson = hrRegistrationInfoDTO.getRegistrationJson();
        HrTalentStaffDTO hrTalentStaffsDTO = JSON.parseObject(regJson, HrTalentStaffDTO.class);
        Integer count = hrRegistrationInfoRepository.queryDuplicateData(hrRegistrationInfoDTO.getBrochureId(), hrRegistrationInfoDTO.getStationName(), hrTalentStaffsDTO);
        if (count > 1) {
            throw new CommonException("您已经保存成功！请不要重复操作");
        }
        QueryWrapper<HrTalentStaff> ew = new QueryWrapper<>();
        QueryWrapper<HrRegistrationDetails> ews = new QueryWrapper<>();
        QueryWrapper<HrRegistrationInfo> ess = new QueryWrapper<>();
        String num = String.valueOf(System.currentTimeMillis());
        Integer sums = this.hrRegistrationInfoRepository.selectSum();
        String zum = num + sums + 1;
        hrRegistrationInfoDTO.setNumber(zum);
        HrRegistrationInfo hrRegistrationInfo = this.hrRegistrationInfoMapper.toEntity(hrRegistrationInfoDTO);
        HrRegistrationInfo hrRegistrationInfos = new HrRegistrationInfo();
        if (StringUtils.isNotBlank(hrRegistrationInfoDTO.getStaffId())) {
            ess.eq("open_id", hrRegistrationInfoDTO.getStaffId());
            ess.eq("station_name", hrRegistrationInfoDTO.getStationName());
            ess.eq("brochure_id", hrRegistrationInfoDTO.getBrochureId());
            hrRegistrationInfos = this.hrRegistrationInfoRepository.selectOne(ess);
        }
        ew.eq("certificate_num", hrTalentStaffsDTO.getCertificateNum());
        HrTalentStaff hrTalentStaffer = this.hrTalentStaffRepository.selectOne(ew);

        //清除所有相同的手机号
        if (StringUtils.isNotBlank(hrTalentStaffsDTO.getPhone())) {
            QueryWrapper<HrTalentStaff> aew = new QueryWrapper<>();
            aew.eq("phone", hrTalentStaffsDTO.getPhone());
            List<HrTalentStaff> hrTalentStafferList = this.hrTalentStaffRepository.selectList(aew);
            if (CollectionUtil.isNotEmpty(hrTalentStafferList)) {
                ArrayList<HrTalentStaff> hrTalentStaffers = new ArrayList<>();

                for (HrTalentStaff talentStaffer : hrTalentStafferList) {
                    if (talentStaffer != null) {
                        if (!talentStaffer.getCertificateNum().equals(hrTalentStaffsDTO.getCertificateNum())) {
                            hrTalentStaffers.add(talentStaffer);
                        }
                    }
                }

                if (CollectionUtil.isNotEmpty(hrTalentStaffers)) {
                    for (HrTalentStaff talentStaffer : hrTalentStaffers) {
                        this.hrRegistrationInfoRepository.updatePhone(talentStaffer.getId());
                        // 检查易云章是否存在用户已注册信息
                        ECloudDomain returnData = eCloudComponent.getCertInfo("1", "0", talentStaffer.getCertificateNum());
                        log.info("易云章用户证书信息：{}", JSON.toJSONString(returnData));

                        if (Constants.SUCCESS_CODE.equals(returnData.getCode())) {
                            this.eCloudComponent.updateCertUserPhone(talentStaffer.getCertificateNum(), talentStaffer.getName(), talentStaffer.getCertificateNum());
                            HrStaffSignCert hrStaffSignCert = new HrStaffSignCert();
                            hrStaffSignCert.setMobilePhone(talentStaffer.getCertificateNum());
                            hrStaffSignCert.setUserNum(talentStaffer.getCertificateNum());
                            hrStaffSignCert.setStaffId(talentStaffer.getId());
                            this.hrRegistrationInfoRepository.updateHrStaffSignCert(hrStaffSignCert);
                        }
                    }
                }
            }
        }


        //查询openID是否重复要是重复直接清空
        QueryWrapper<HrTalentStaff> ewq = new QueryWrapper<>();
        if (StringUtils.isNotBlank(hrRegistrationInfoDTO.getOpenId())) {
            ewq.eq("open_id", hrRegistrationInfoDTO.getOpenId());
            List<HrTalentStaff> hrTalentStaffssf = this.hrTalentStaffRepository.selectList(ewq);
            if (CollectionUtil.isNotEmpty(hrTalentStaffssf)) {
                this.hrRegistrationInfoRepository.uodateOpenId(hrRegistrationInfoDTO.getOpenId());
            }
        }

        //效验简章岗位数
        if (hrRegistrationInfoDTO.getIsOverstate() == false) {
            ew.eq("certificate_num", hrTalentStaffsDTO.getCertificateNum());
            ew.eq("is_delete", 0);
            HrTalentStaff hrTalentStaffDTOs = this.hrTalentStaffRepository.selectOne(ew);
            if (hrTalentStaffDTOs != null) {
                List<HrRegistrationDetails> hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectOnea(hrTalentStaffDTOs.getId(), hrRegistrationInfoDTO.getBrochureId());
                if (CollectionUtil.isNotEmpty(hrRegistrationDetails)) {
                    throw new CommonException("本简章只能报一个岗位");
                }
            }

        }


        String staffId = "";
        if (hrTalentStaffsDTO != null) {
            ew.eq("certificate_num", hrTalentStaffsDTO.getCertificateNum());
            HrTalentStaff hrTalentStaffss = this.hrTalentStaffRepository.selectOne(ew);
            if (hrTalentStaffss != null) {
                staffId = hrTalentStaffss.getId();
                hrTalentStaffsDTO.setId(hrTalentStaffss.getId());
                //处理报名人员的附加信息
                this.handleStaffAdditionalInfo(hrTalentStaffsDTO, hrRegistrationInfoDTO);
                //修改人员
                if (hrTalentStaffsDTO.getHighestEducation() != null) {
                    this.judgeStaffSignCert(hrTalentStaffer, hrTalentStaffsDTO);
                    HrTalentStaff hrTalentStaffu = this.hrTalentStaffMapper.toEntity(hrTalentStaffsDTO);
                    hrTalentStaffu.setClientId(hrTalentStaffer.getClientId());
                    hrTalentStaffu.setProtocolId(hrTalentStaffer.getProtocolId());
                    hrTalentStaffu.setApplyStaffId(hrTalentStaffer.getApplyStaffId());
                    if (StringUtils.isNotBlank(hrRegistrationInfoDTO.getOpenId())) {
                        hrTalentStaffu.setOpenId(hrRegistrationInfoDTO.getOpenId());
                    } else {
                        hrTalentStaffu.setOpenId(null);
                    }
                    this.hrTalentStaffRepository.updateById(hrTalentStaffu);
                } else {
                    if (CollectionUtil.isNotEmpty(hrRegistrationInfoDTO.getHighestEducationLists())) {
                        for (HrStaffEducationDTO highestEducationList : hrRegistrationInfoDTO.getHighestEducationLists()) {
                            if (highestEducationList.getHighestDegree() != null) {
                                hrTalentStaffsDTO.setHighestEducation(highestEducationList.getHighestDegree());
                            }

                        }
                    }
                    HrTalentStaff hrTalentStaffu = this.hrTalentStaffMapper.toEntity(hrTalentStaffsDTO);
                    hrTalentStaffu.setClientId(hrTalentStaffer.getClientId());
                    hrTalentStaffu.setProtocolId(hrTalentStaffer.getProtocolId());
                    if (StringUtils.isNotBlank(hrRegistrationInfoDTO.getOpenId())) {
                        hrTalentStaffu.setOpenId(hrRegistrationInfoDTO.getOpenId());
                    } else {
                        hrTalentStaffu.setOpenId(null);
                    }
                    hrTalentStaffu.setApplyStaffId(hrTalentStaffer.getApplyStaffId());
                    this.hrTalentStaffRepository.updateById(hrTalentStaffu);
                }
            } else {
                String unitSum = "";
                unitSum = "SY" + System.currentTimeMillis();
                //新增人员
                if (hrTalentStaffsDTO.getHighestEducation() != null) {
                    hrTalentStaffsDTO.setIzDefault(true);
                    HrTalentStaff hrTalentStaff = this.hrTalentStaffMapper.toEntity(hrTalentStaffsDTO);
                    if (StringUtils.isNotBlank(hrRegistrationInfoDTO.getOpenId())) {
                        hrTalentStaff.setOpenId(hrRegistrationInfoDTO.getOpenId());
                    } else {
                        hrTalentStaff.setOpenId(null);
                    }
                    hrTalentStaff.setSystemNum(unitSum);
                    try {
                        this.hrTalentStaffRepository.insert(hrTalentStaff);
                    } catch (Exception e) {
                        throw new CommonException("不可重复提交");
                    }
                    staffId = hrTalentStaff.getId();
                } else {
                    if (CollectionUtil.isNotEmpty(hrRegistrationInfoDTO.getHighestEducationLists())) {
                        for (HrStaffEducationDTO highestEducationList : hrRegistrationInfoDTO.getHighestEducationLists()) {
                            if (highestEducationList.getHighestDegree() != null) {
                                hrTalentStaffsDTO.setHighestEducation(highestEducationList.getHighestDegree());
                            }
                            hrTalentStaffsDTO.setIzDefault(true);
                            HrTalentStaff hrTalentStaff = this.hrTalentStaffMapper.toEntity(hrTalentStaffsDTO);
                            hrTalentStaff.setSystemNum(unitSum);
                            if (StringUtils.isNotBlank(hrRegistrationInfoDTO.getOpenId())) {
                                hrTalentStaff.setOpenId(hrRegistrationInfoDTO.getOpenId());
                            } else {
                                hrTalentStaff.setOpenId(null);
                            }
                            try {
                                this.hrTalentStaffRepository.insert(hrTalentStaff);
                            } catch (Exception e) {
                                throw new CommonException("不可重复提交");
                            }
                            staffId = hrTalentStaff.getId();
                        }
                    } else {
                        hrTalentStaffsDTO.setIzDefault(true);
                        HrTalentStaff hrTalentStaff = this.hrTalentStaffMapper.toEntity(hrTalentStaffsDTO);
                        hrTalentStaff.setSystemNum(unitSum);
                        if (StringUtils.isNotBlank(hrRegistrationInfoDTO.getOpenId())) {
                            hrTalentStaff.setOpenId(hrRegistrationInfoDTO.getOpenId());
                        } else {
                            hrTalentStaff.setOpenId(null);
                        }
                        try {
                            this.hrTalentStaffRepository.insert(hrTalentStaff);
                        } catch (Exception e) {
                            throw new CommonException("不可重复提交");
                        }
                        staffId = hrTalentStaff.getId();

                    }
                }
                //处理报名人员的附加信息
                hrTalentStaffsDTO.setId(staffId);
                this.handleStaffAdditionalInfo(hrTalentStaffsDTO, hrRegistrationInfoDTO);
            }
            //报名人数
            if (hrTalentStaffer != null) {
                if (StringUtils.isNotBlank(hrTalentStaffer.getId())) {
                    QueryWrapper<HrRegistrationDetails> ww = new QueryWrapper<>();
                    ww.eq("staff_id", hrTalentStaffer.getId());
                    ww.eq("station_name", hrRegistrationInfoDTO.getStationName());
                    ww.eq("brochure_id", hrRegistrationInfoDTO.getBrochureId());
                    List<HrRegistrationDetails> hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectList(ww);
                    if (CollectionUtil.isEmpty(hrRegistrationDetails)) {
                        //修改报名人数
                        Integer sum = this.hrRegistrationInfoRepository.selectRecruitmentNum(hrRegistrationInfo);
                        if (sum != null) {
                            Integer recruitmentNum = sum + 1;
                            this.hrRegistrationInfoRepository.uodateRecruitmentNum(hrRegistrationInfo.getBrochureId(), hrRegistrationInfo.getStationName(), recruitmentNum);
                        }
                    }
                }
            } else {
                //修改报名人数
                Integer sum = this.hrRegistrationInfoRepository.selectRecruitmentNum(hrRegistrationInfo);
                if (sum != null) {
                    Integer recruitmentNum = sum + 1;
                    this.hrRegistrationInfoRepository.uodateRecruitmentNum(hrRegistrationInfo.getBrochureId(), hrRegistrationInfo.getStationName(), recruitmentNum);
                }

            }
        }
        //效验重复提交
        if (hrRegistrationInfos != null) {
            if (StringUtils.isNotBlank(hrRegistrationInfos.getId())) {
                if (hrTalentStaffer != null) {
                    ews.eq("staff_id", hrTalentStaffer.getId());
                    ews.eq("brochure_id", hrRegistrationInfoDTO.getBrochureId());
                    ews.eq("station_name", hrRegistrationInfoDTO.getStationName());
                    HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectOne(ews);
                    try {
                        if (hrRegistrationInfos != null) {
                            if (hrRegistrationDetails != null) {
                                if (hrRegistrationDetails.getStatus().equals(1)) {
                                    this.hrRegistrationInfoRepository.deleteById(hrRegistrationInfos.getId());
                                    hrRegistrationInfo.setOpenId(hrTalentStaffer.getId());
                                    this.hrRegistrationInfoRepository.insert(hrRegistrationInfo);
                                } else {
                                    throw new CommonException("不可重复提交");
                                }
                            }

                        } else {
                            if (hrRegistrationDetails != null) {
                                if (hrRegistrationDetails.getStatus() == 0) {
                                    this.hrRegistrationInfoRepository.insert(hrRegistrationInfo);
                                } else {
                                    throw new CommonException("不可重复提交");
                                }
                            }
                        }
                    } catch (CommonException e) {
                        throw new CommonException("不可重复提交");
                    }
                }
            } else {
                hrRegistrationInfo.setOpenId(staffId);
                try {
                    this.hrRegistrationInfoRepository.insert(hrRegistrationInfo);
                } catch (Exception e) {
                    throw new CommonException("不可重复提交");
                }
            }
        } else {
            try {
                hrRegistrationInfo.setOpenId(staffId);
                this.hrRegistrationInfoRepository.insert(hrRegistrationInfo);
            } catch (Exception e) {
                throw new CommonException("不可重复提交");
            }
        }
        //存报名详情表
        HrRegistrationDetails hrRegistrationDetails = new HrRegistrationDetails();

        hrRegistrationDetails.setBrochureId(hrRegistrationInfoDTO.getBrochureId());
        hrRegistrationDetails.setStationId(hrRegistrationInfoDTO.getStationId());
        hrRegistrationDetails.setClientId(hrRegistrationInfoDTO.getClientId());
        hrRegistrationDetails.setStationName(hrRegistrationInfoDTO.getStationName());
        ews.eq("staff_id", staffId);
        ews.eq("station_name", hrRegistrationInfoDTO.getStationName());
        ews.eq("brochure_id", hrRegistrationInfoDTO.getBrochureId());
        HrRegistrationDetails hrRegistrationDetailsss = this.hrRegistrationDetailsRepository.selectOne(ews);

        //添加日志
        HrTalentStaff hrTalentSta = this.hrTalentStaffRepository.selectById(staffId);
        String message = hrTalentSta.getName() + "填写了" + hrRegistrationInfoDTO.getStationName() + "报名信息";
        String msg = hrTalentSta.getName() + "填写了" + hrRegistrationInfoDTO.getStationName() + "报名信息";
        JSONObject object = new JSONObject();
        object.put("entry", RecruitmentBrochure.RegistrationStatus.TO_BE_REVIEWED.getKey());
        object.put("message", msg);

        if (hrRegistrationDetailsss != null) {
            try {
                this.hrRegistrationDetailsRepository.deleteRegistrationDetailsById(hrRegistrationDetailsss.getId());
                hrRegistrationDetails.setId(hrRegistrationDetailsss.getId());
                hrRegistrationDetails.setStaffId(staffId);
                hrRegistrationDetails.setInfoId(hrRegistrationInfo.getId());
                this.hrRegistrationDetailsRepository.insert(hrRegistrationDetails);
                hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrRegistrationDetails.getId(), staffId, hrTalentSta.getName() + "填写了" + hrRegistrationInfoDTO.getStationName() + "报名信息", false, ServiceCenterEnum.SIGN_UP.getValue());
            } catch (Exception e) {
                throw new CommonException("不可重复提交");
            }
        } else {
            try {
                hrRegistrationDetails.setStaffId(staffId);
                hrRegistrationDetails.setInfoId(hrRegistrationInfo.getId());
                this.hrRegistrationDetailsRepository.insert(hrRegistrationDetails);
                //小程序消息中心
                hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), hrRegistrationDetails.getId(), staffId, hrTalentSta.getName() + "填写了" + hrRegistrationInfoDTO.getStationName() + "报名信息", false, ServiceCenterEnum.SIGN_UP.getValue());
            } catch (Exception e) {
                throw new CommonException("不可重复提交");
            }
        }
        //添加操作信息--小程序
        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrRegistrationDetails.getId(), hrTalentSta.getId(), hrTalentSta.getId(), message + "####" + object.toString(), null, true, null, ServiceCenterEnum.SIGN_UP.getKey());
        return this.hrRegistrationInfoMapper.toDto(hrRegistrationInfo);
    }

    /**
     * 处理报名人员的附加信息
     *
     * @param hrTalentStaffsDTO
     * @param hrRegistrationInfoDTO
     */
    private void handleStaffAdditionalInfo(HrTalentStaffDTO hrTalentStaffsDTO, HrRegistrationInfoDTO hrRegistrationInfoDTO) {
        String staffId = hrTalentStaffsDTO.getId();
        //工作经历
        if (CollectionUtil.isNotEmpty(hrTalentStaffsDTO.getWorkExperience())) {
            for (HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO : hrTalentStaffsDTO.getWorkExperience()) {
                hrStaffWorkExperienceDTO.setStaffId(staffId);
                HrStaffWorkExperience hrStaffWorkExperience = this.hrStaffWorkExperienceMapper.toEntity(hrStaffWorkExperienceDTO);
                if (StringUtils.isNotBlank(hrStaffWorkExperienceDTO.getId())) {
                    //存在id说明数据已经存在，修改数据
                    this.hrStaffWorkExperienceRepository.updateById(hrStaffWorkExperience);
                } else {
                    //没有id直接添加数据
                    hrStaffWorkExperience.setId(null);
                    this.hrStaffWorkExperienceRepository.insert(hrStaffWorkExperience);
                }
            }
        }
        //家庭成员
        if (CollectionUtil.isNotEmpty(hrTalentStaffsDTO.getStaffFamily())) {
            for (HrStaffFamilyDTO hrStaffFamilyDTO : hrTalentStaffsDTO.getStaffFamily()) {
                hrStaffFamilyDTO.setStaffId(staffId);
                HrStaffFamily entity = this.hrStaffFamilyMapper.toEntity(hrStaffFamilyDTO);
                if (StringUtils.isNotBlank(hrStaffFamilyDTO.getId())) {
                    this.hrStaffFamilyRepository.updateById(entity);
                } else {
                    entity.setId(null);
                    this.hrStaffFamilyRepository.insert(entity);
                }
            }
        }
        //其他信息
        if (CollectionUtil.isNotEmpty(hrRegistrationInfoDTO.getHrStaffFieldDTOList())) {
            for (HrStaffFieldDTO hrStaffFieldDTO : hrRegistrationInfoDTO.getHrStaffFieldDTOList()) {
                QueryWrapper<HrStaffField> qw = new QueryWrapper<>();
                qw.eq("staff_id", staffId)
                    .eq("field_name", hrStaffFieldDTO.getFieldName())
                    .last("limit 1");
                HrStaffField selectOne = hrStaffFieldRepository.selectOne(qw);
                hrStaffFieldDTO.setStaffId(staffId);
                HrStaffField hrStaffField = this.hrStaffFieldMapper.toEntity(hrStaffFieldDTO);
                if (selectOne != null) {
                    hrStaffField.setId(selectOne.getId());
                    this.hrStaffFieldRepository.updateById(hrStaffField);
                } else {
                    hrStaffField.setId(null);
                    this.hrStaffFieldRepository.insert(hrStaffField);
                }
            }
        }
        //学习经历
        if (CollectionUtil.isNotEmpty(hrTalentStaffsDTO.getStaffEducation())) {
            for (HrStaffEducationDTO hrStaffEducationDTO : hrTalentStaffsDTO.getStaffEducation()) {
                hrStaffEducationDTO.setStaffId(staffId);
                HrStaffEducation hrStaffEducation = this.hrStaffEducationMapper.toEntity(hrStaffEducationDTO);
                if (StringUtils.isNotBlank(hrStaffEducationDTO.getId())) {
                    this.hrStaffEducationRepository.updateById(hrStaffEducation);
                } else {
                    hrStaffEducation.setId(null);
                    this.hrStaffEducationRepository.insert(hrStaffEducation);
                }
            }
        }
        //职业(工种)资格
        if (CollectionUtil.isNotEmpty(hrTalentStaffsDTO.getStaffQualification())) {
            for (HrStaffQualificationDTO hrStaffQualificationDTO : hrTalentStaffsDTO.getStaffQualification()) {
                hrStaffQualificationDTO.setStaffId(staffId);
                HrStaffQualification hrStaffQualifications = this.hrStaffQualificationMapper.toEntity(hrStaffQualificationDTO);
                if (StringUtils.isNotBlank(hrStaffQualificationDTO.getId())) {
                    this.hrStaffQualificationRepository.updateById(hrStaffQualifications);
                } else {
                    hrStaffQualifications.setId(null);
                    this.hrStaffQualificationRepository.insert(hrStaffQualifications);
                }
            }
        }
        // 资格证书
        if (CollectionUtil.isNotEmpty(hrTalentStaffsDTO.getQualification())) {
            for (HrStaffCertificateDTO certificateDTO : hrTalentStaffsDTO.getQualification()) {
                certificateDTO.setStaffId(staffId);
                HrStaffCertificate entity = hrStaffCertificateMapper.toEntity(certificateDTO);
                if (StringUtils.isNotBlank(certificateDTO.getId())) {
                    hrStaffCertificateRepository.updateById(entity);
                } else {
                    entity.setId(null);
                    hrStaffCertificateRepository.insert(entity);
                }
            }
        }
    }

    /**
     * 判断用户易云章证书是否需要更新
     *
     * @param talentStaff
     * @param hrTalentStaffDTO
     * @return void
     * <AUTHOR>
     * @date 2021/9/28
     **/
    private void judgeStaffSignCert(HrTalentStaff talentStaff, HrTalentStaffDTO hrTalentStaffDTO) {
        HrStaffSignCert hrStaffSignCert = this.hrStaffSignCertService.getByStaffId(talentStaff.getId());
        if (hrStaffSignCert != null) {
            // 若身份证没变,判断姓名手机号是否更改
            if (talentStaff.getCertificateNum().equals(hrTalentStaffDTO.getCertificateNum())) {
                // 姓名修改则更新姓名
                if (!talentStaff.getName().equals(hrTalentStaffDTO.getName())) {
                    // 更新证书主体名称
                    this.hrStaffSignCertService.updateCertUserName(talentStaff.getCertificateNum(), hrTalentStaffDTO.getName(), talentStaff.getPhone(), hrStaffSignCert);
                }
                // 手机号修改则更新手机号
                if (!talentStaff.getPhone().equals(hrTalentStaffDTO.getPhone())) {
                    // 更新证书主体手机号
                    this.hrStaffSignCertService.updateCertUserPhone(talentStaff.getCertificateNum(), talentStaff.getName(), hrTalentStaffDTO.getPhone(), hrStaffSignCert);
                }
            } else {
                // 身份证信息改变 则删除证书数据
                this.hrStaffSignCertService.removeById(hrStaffSignCert.getId());
            }
        }
    }


    /**
     * 手机号有效性/重复性
     *
     * @param phone 手机号码
     */
    private void checkoutPhone(String phone) {
        //验证手机号码有效性
        if (!ValidateUtil.isCellPhoneNo(phone)) {
            throw new CommonException("手机号码格式填写不正确！");
        }
        HrTalentStaff staff = hrTalentStaffRepository.selectOne(
            new QueryWrapper<HrTalentStaff>().eq("phone", phone).eq("iz_default", 0));
        if (staff != null) {
            throw new CommonException("手机号码已存在！");
        }
    }

    /**
     * 校验证件号码
     *
     * @param certificateNum
     * @return
     */
    private void checkoutIDNumber(Integer certificateType, String certificateNum) {
        //校验格式
        boolean idNumber = ValidateUtil.isIdNumber(certificateNum);
        if (certificateType.equals(CertificateTypeEnum.CertificateType.RESIDENT_IDENTIFICATION_CARD.getKey()) && !idNumber) {
            throw new CommonException("身份证号格式不正确！");
        }
        HrTalentStaff staff = hrTalentStaffRepository.selectOne(
            new QueryWrapper<HrTalentStaff>().eq("certificate_num", certificateNum).eq("is_delete", 0));
        if (staff != null) {
            throw new CommonException("证件号码已存在！");
            //证件号码已存在，判断该条数据是人才库数据还是员工信息数据
        }
    }


    /**
     * 修改报名信息表
     *
     * @param hrRegistrationInfoDTO
     * @return
     */
    @Override
    public Optional<HrRegistrationInfoDTO> updateHrRegistrationInfo(HrRegistrationInfoDTO hrRegistrationInfoDTO) {
        return Optional.ofNullable(this.hrRegistrationInfoRepository.selectById(hrRegistrationInfoDTO.getId()))
            .map(roleTemp -> {
                HrRegistrationInfo hrRegistrationInfo = this.hrRegistrationInfoMapper.toEntity(hrRegistrationInfoDTO);
                this.hrRegistrationInfoRepository.updateById(hrRegistrationInfo);
                log.info("Update HrRegistrationInfo:{}", hrRegistrationInfoDTO);
                return hrRegistrationInfoDTO;
            });
    }

    /**
     * 查询报名信息表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrRegistrationInfoDTO getHrRegistrationInfo(String id) {
        log.info("Get HrRegistrationInfo :{}", id);

        HrRegistrationInfo hrRegistrationInfo = this.hrRegistrationInfoRepository.selectById(id);
        return this.hrRegistrationInfoMapper.toDto(hrRegistrationInfo);
    }

    /**
     * 删除报名信息表
     *
     * @param id
     */
    @Override
    public void deleteHrRegistrationInfo(String id) {
        Optional.ofNullable(this.hrRegistrationInfoRepository.selectById(id))
            .ifPresent(hrRegistrationInfo -> {
                this.hrRegistrationInfoRepository.deleteById(id);
                log.info("Delete HrRegistrationInfo:{}", hrRegistrationInfo);
            });
    }

    /**
     * 批量删除报名信息表
     *
     * @param ids
     */
    @Override
    public void deleteHrRegistrationInfo(List<String> ids) {
        log.info("Delete HrRegistrationInfos:{}", ids);
        this.hrRegistrationInfoRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询报名信息表
     *
     * @param hrRegistrationInfoDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrRegistrationInfoDTO hrRegistrationInfoDTO, Long pageNumber, Long pageSize) {
        Page<HrRegistrationInfo> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrRegistrationInfo> qw = new QueryWrapper<>(this.hrRegistrationInfoMapper.toEntity(hrRegistrationInfoDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrRegistrationInfoRepository.selectPage(page, qw);
        iPage.setRecords(this.hrRegistrationInfoMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    @Override
    public Object getRegistrationopenId(String id) {
        QueryWrapper<HrTalentStaff> qe = new QueryWrapper<>();
        String i = "员工信息不存在";
        qe.eq("open_id", id);
        List<HrTalentStaff> hrTalentStaffList = this.hrTalentStaffRepository.selectList(qe);
        List<HrTalentStaff> hrTalentStaffs = new ArrayList<>();
        HrTalentStaff hrTalentStaffss = new HrTalentStaff();
        if (hrTalentStaffList.size() > 1) {
            this.hrRegistrationInfoRepository.uodateOpenId(id);
            return i;
        } else {
            hrTalentStaffs.addAll(hrTalentStaffList);
            for (HrTalentStaff hrTalentStaff : hrTalentStaffs) {
                //构造jwt生成token的参数
                JWTMiniDTO jwtMiniDTO = new JWTMiniDTO();
                jwtMiniDTO.setId(hrTalentStaff.getId());
                jwtMiniDTO.setName(hrTalentStaff.getName());
                jwtMiniDTO.setCertificateNum(hrTalentStaff.getCertificateNum());
                jwtMiniDTO.setPhone(hrTalentStaff.getPhone());
                jwtMiniDTO.setClientId(hrTalentStaff.getClientId());
                jwtMiniDTO.setProtocolId(hrTalentStaff.getProtocolId());
                jwtMiniDTO.setOpenId(hrTalentStaff.getOpenId());
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(jwtMiniDTO, "", null);
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                String jwt = tokenProvider.createTokenMini(authenticationToken, false);

                // 将用户token放入redis
                String redisKey = RedisKeyEnum.currencyKey.USER_TOKEN.getValue() + jwtMiniDTO.getId();
                redisCache.setCacheObject(redisKey, jwt, 24, TimeUnit.HOURS);

                //返回生成的token
                HrRegistrationInfoServiceImpl.JWTToken jwtToken = new HrRegistrationInfoServiceImpl.JWTToken(jwt);
                jwtToken.setHrTalentStaff(hrTalentStaff);
                return jwtToken;
            }
        }


        return i;
    }


    @Override
    public String getRegistratiupdateopenId(String id) {
        this.hrRegistrationInfoRepository.uodateOpenId(id);
        HrTalentStaff hrTalentStaff = new HrTalentStaff();
        String re = "退出成功";
        return re;

    }

    @Override
    public HrRegistrationInfoDTO getRegistratiselectOpendId(HrRegistrationInfoDTO hrRegistrationInfoDTO) {
        HrRegistrationInfoDTO hrRegistrationInfoDTOs = this.hrRegistrationInfoRepository.selectInfo(hrRegistrationInfoDTO);
        return hrRegistrationInfoDTOs;
    }


    //查询手机号时候重复
    @Override
    public ResponseEntity<?> getHrRegistrationInfoPhone(HrTalentStaffDTO hrTalentStaff) {
        List<String> mationName = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        QueryWrapper<HrTalentStaff> qw = new QueryWrapper<>();
        if (hrTalentStaff != null) {
            if (StringUtils.isBlank(hrTalentStaff.getCertificateNum())) {
                throw new CommonException("缺失证件号码");
            }
            if (StringUtils.isBlank(hrTalentStaff.getPhone())) {
                throw new CommonException("缺失手机号码");
            }
            if (StringUtils.isNotBlank(hrTalentStaff.getPhone()) && StringUtils.isNotBlank(hrTalentStaff.getCertificateNum())) {
                //手机号身份证号相同的
                qw.eq("certificate_num", hrTalentStaff.getCertificateNum());
                qw.eq("phone", hrTalentStaff.getPhone());
                List<HrTalentStaff> hrTalentStaffs = this.hrTalentStaffRepository.selectList(qw);
                if (hrTalentStaffs.size() == 1) {
                    jsonObject.put("status", 1);
                    jsonObject.put("error_status", "手机号身份证号相匹配");
                } else {

                    QueryWrapper<HrTalentStaff> qws = new QueryWrapper<>();
                    qws.eq("phone", hrTalentStaff.getPhone());
                    List<HrTalentStaff> hrTalentStaffss = this.hrTalentStaffRepository.selectList(qws);
                    if (CollectionUtil.isNotEmpty(hrTalentStaffss)) {
                        if (hrTalentStaffss.size() > 1) {
                            jsonObject.put("status", 2);
                            jsonObject.put("error_status", "数据库存在多条");
                        } else {
                            for (HrTalentStaff talentStaffss : hrTalentStaffss) {
                                if (!talentStaffss.getCertificateNum().equals(hrTalentStaff.getCertificateNum())) {
                                    jsonObject.put("status", 3);
                                    jsonObject.put("error_status", "手机号重复");
                                    jsonObject.put("phone", hrTalentStaffss.get(0).getPhone());
                                }
                            }

                        }
                    } else {
                        QueryWrapper<HrTalentStaff> qwsa = new QueryWrapper<>();
                        qwsa.eq("certificate_num", hrTalentStaff.getCertificateNum());
                        List<HrTalentStaff> hrTalentStaffsss = this.hrTalentStaffRepository.selectList(qwsa);
                        if (CollectionUtil.isEmpty(hrTalentStaffsss)) {
                            jsonObject.put("status", 4);
                            jsonObject.put("error_status", "新用户");
                        } else {
                            for (HrTalentStaff talentStaffss : hrTalentStaffsss) {
                                if (StringUtils.isNotBlank(talentStaffss.getPhone())) {
                                    if (!talentStaffss.getPhone().equals(hrTalentStaff.getPhone())) {
                                        jsonObject.put("status", 5);
                                        jsonObject.put("error_status", "身份证与手机号不匹配");
                                        jsonObject.put("phone", talentStaffss.getPhone());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    @Override
    public JSONObject getHrRegistrationInfoPhoneEffectiveness(WebLoginDTO webLoginDTO) {
        JSONObject json = new JSONObject();
        //验证验证码是否正确
        Object cacheObject = redisCache.getCacheObject(RedisKeyEnum.smsMessage.VERIFY_CODE.getValue() + webLoginDTO.getPhone());
        if (cacheObject != null) {
            if (cacheObject == null) {
                json.put("message", "请先获取验证码");
                json.put("code", 500);
            }
            if (!webLoginDTO.getCode().equals(cacheObject.toString())) {
                json.put("message", "验证码不正确");
                json.put("code", 500);

            } else {
                json.put("message", "验证码正确");
                json.put("code", 200);
            }
        } else {
            json.put("message", "验证码发送失败");
            json.put("code", 400);
        }

        return json;
    }


    @Override
    public String sendAppCodes(WebLoginDTO appLoginDTO) {
        if (cn.casair.common.utils.StringUtils.isBlank(appLoginDTO.getPhone())) {
            throw new CommonException("请输入手机号");
        }

        // 生成验证码
        Integer code = VerifyCodeUtils.generateValidateCode(4);
        //将验证码放入redis
        redisCache.setCacheObject(RedisKeyEnum.smsMessage.VERIFY_CODE.getValue() + appLoginDTO.getPhone(), code, 5, TimeUnit.MINUTES);
        // 组装短信模板参数
        //根据手机号查询员工信息
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectOne(new QueryWrapper<HrTalentStaff>().eq("is_delete", 0).eq("phone", appLoginDTO.getPhone()).last("LIMIT 1"));

        HashMap<Integer, String> params = new HashMap<>();
        params.put(1, code.toString());
        params.put(2, String.valueOf(5));
        String sendMessage = this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.VERIFICATION_CODE.getTemplateCode(), appLoginDTO.getPhone(), hrTalentStaff != null ? hrTalentStaff.getName() : "");
        return sendMessage;
    }


    //查询我的消息
    @Override
    public HrRegistrationDetailsDTO getRegistratiinformation(String serviceId) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrRegistrationDetailsDTO hrRegistrationDetailsDTO = new HrRegistrationDetailsDTO();
        if (StringUtils.isNotBlank(serviceId)) {
            HrRegistrationDetails hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectById(serviceId);
            hrRegistrationDetailsDTO = this.hrRegistrationDetailsMapper.toDto(hrRegistrationDetails);
            QueryWrapper<HrApplyOpLogs> wq = new QueryWrapper<>();
            wq.eq(StringUtils.isNotBlank(jwtMiniDTO.getId()), "apply_staff_id", jwtMiniDTO.getId());
            wq.eq(StringUtils.isNotBlank(serviceId), "apply_id", serviceId);
            wq.orderByAsc("created_date");
            List<HrApplyOpLogs> hrApplyOpLogsList = this.hrApplyOpLogsRepository.selectList(wq);
            List<HrApplyOpLogsDTO> hrApplyOpLogsDTOS = this.hrApplyOpLogsMapper.toDto(hrApplyOpLogsList);
            if (CollectionUtil.isNotEmpty(hrApplyOpLogsDTOS)) {
                hrRegistrationDetailsDTO.setApplyOpLogsList(hrApplyOpLogsDTOS);
            }
            HrRecruitmentBrochure hrRecruitmentBrochure = this.hrRecruitmentBrochureRepository.selectById(hrRegistrationDetailsDTO.getBrochureId());
            if (hrRecruitmentBrochure != null) {
                hrRegistrationDetailsDTO.setRecruitBrochureName(hrRecruitmentBrochure.getRecruitBrochureName());
            }
        }
        return hrRegistrationDetailsDTO;
    }

}
