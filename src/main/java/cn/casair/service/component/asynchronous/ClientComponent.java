package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.HrClient;
import cn.casair.domain.HrDocking;
import cn.casair.domain.HrPaperClient;
import cn.casair.domain.HrPaperManagement;
import cn.casair.domain.HrProtocol;
import cn.casair.domain.HrSpecialDeduction;
import cn.casair.domain.HrTalentStaff;
import cn.casair.domain.HrUserClient;
import cn.casair.domain.User;
import cn.casair.domain.UserRole;
import cn.casair.dto.HrAccumulationFundDTO;
import cn.casair.dto.HrClientDTO;
import cn.casair.dto.HrPlatformAccountDTO;
import cn.casair.dto.HrSocialSecurityDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrClientImport;
import cn.casair.dto.excel.HrProtocolImport;
import cn.casair.dto.excel.HrSpecialDeductionImport;
import cn.casair.mapper.HrClientMapper;
import cn.casair.mapper.HrProtocolMapper;
import cn.casair.mapper.UserMapper;
import cn.casair.repository.HrClientRepository;
import cn.casair.repository.HrDockingRepository;
import cn.casair.repository.HrExamRepository;
import cn.casair.repository.HrFeeReviewRepository;
import cn.casair.repository.HrPaperClientRepository;
import cn.casair.repository.HrPaperManagementRepository;
import cn.casair.repository.HrProtocolRepository;
import cn.casair.repository.HrSpecialDeductionRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.repository.HrUserClientRepository;
import cn.casair.repository.UserRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrAppendixService;
import cn.casair.service.RoleService;
import cn.casair.service.SysDictDataService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.UserRoleService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 客户导入
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/12/24 13:23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ClientComponent {

    @Value("${file.temp-path}")
    private String tempPath;
    @Value("${file.temp-path}")
    private String fileTempPath;
    private final CodeTableService codeTableService;
    private final SysDictDataService sysDictDataService;
    private final HrClientRepository hrClientRepository;
    private final HrProtocolRepository protocolRepository;
    private final UserMapper userMapper;
    private final HrClientMapper hrClientMapper;
    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;
    private final UserRoleService userRoleService;
    private final HrProtocolMapper hrProtocolMapper;
    private final HrProtocolRepository hrProtocolRepository;
    private final HrUserClientRepository hrUserClientRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;

    private final RedisCache redisCache;
    private final HrSpecialDeductionRepository hrSpecialDeductionRepository;

    private final RoleService roleService;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final HrFeeReviewRepository hrFeeReviewRepository;
    private final HrDockingRepository hrDockingRepository;
    private final HrExamRepository hrExamRepository;
    private final HrPaperManagementRepository hrPaperManagementRepository;
    private final HrPaperClientRepository hrPaperClientRepository;

    //客户
    @Async
    public void saveClientData(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrClientImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrClientImport.class);

            ImportResultDTO importResult;

            int scale = 0;
            int listSize = result.getList().size();
            List<HrClientImport> hrClientImport = new ArrayList<>();
            Map<String, List<HrClientImport>> map = result.getList().stream().collect(Collectors.groupingBy(HrClientImport::getClientName));

            StringBuilder sbError = new StringBuilder();
            for (HrClientImport hrClientImports : result.getList()) {
                sbError.delete(0, sbError.length());
                try {
                    //判断excel中是否存在重复数据
                    String clientName = hrClientImports.getClientName();
                    List<HrClientImport> hrClientImportssa = map.get(clientName);
                    if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(hrClientImportssa) && hrClientImportssa.size() > 1) {
                        throw new CommonException("导入Excel中该数据重复!");
                    }
                    //效验客户名是否为空
                    if (org.apache.commons.lang3.StringUtils.isBlank(hrClientImports.getClientName())) {
                        sbError.append("客户名称不能为空");
                    }
                    log.info("效验客户名是否为空");
                    //客户名称效验
                    int getclientnames = this.hrClientRepository.getclientName(hrClientImports.getClientName());
                    if (getclientnames > 0) {
                        sbError.append("客户名称重复，请重新进行填写!");
                    }
                    log.info("客户名称效验");
                    //生成客户编号
                    String unitSum = "";
                    HrClient hrClient = new HrClient();
                    if (org.apache.commons.lang3.StringUtils.isBlank(hrClientImports.getUnitNumber())) {
                        long timeNew = System.currentTimeMillis();
                        int radom = (int) (Math.random() * 10 + 1);
                        unitSum = "KH" + System.currentTimeMillis();
                    }
                    log.info("生成客户编号");
                    //效验用户名是否重复
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(hrClientImports.getUserName())) {
                        QueryWrapper<User> ew = new QueryWrapper<>();
                        ew.eq("user_name", hrClientImports.getUserName());
                        List<User> userList = this.userRepository.selectList(ew);
                        if (CollectionUtils.isNotEmpty(userList)) {
                            sbError.append("用户名已重复，请修改");
                        }
                    }
                    log.info("效验用户名是否重复");
                    //根据证件号码查询是否已经存在
                    HrClient hrClients = this.hrClientRepository.selectOne(new QueryWrapper<HrClient>().eq("unit_number", hrClient.getUnitNumber()).eq("status", 0));
                    if (hrClients != null) {
                        throw new CommonException("已存在!");
                    }
                    hrClientImport.add(hrClientImports);
                    if (CollectionUtils.isNotEmpty(hrClientImport)) {
                        log.info("是否为空");
                        BeanUtils.copyProperties(hrClientImports, hrClient);

                        //效验客户层级是否存在上级客户编号
                        if (hrClientImports.getCustomerLevel() != null) {

                            if (hrClientImports.getCustomerLevel().equals("2")) {
                                String parid = hrClientImports.getParentcustomerLevel();
                                HrClientDTO hrClientDTO = new HrClientDTO();
                                if (parid != null) {
                                    hrClientDTO = this.hrClientRepository.selectCountparid(parid);
                                    if (hrClientDTO == null) {
                                        sbError.append("上级客户编号不存在,请重新填写!");
//                                        throw new CommonException("上级客户编号不存在,请重新填写!");
                                    } else {
                                        hrClient.setParentId(hrClientDTO.getId());
                                    }
                                }
                            }
                        }
                        if (hrClientImports.getEstablishedDate() != null) {
                            hrClient.setEstablishedDate(hrClientImports.getEstablishedDate());
                        }
                        log.info("效验客户成立时间");

                        //效验社保账号
                        if (hrClientImports.getSocialSecurityAccount() != null) {
                            HrPlatformAccountDTO hrPlatformAccountDTO = new HrPlatformAccountDTO();
                            hrPlatformAccountDTO = this.hrClientRepository.getSocialSecuritysum(hrClientImports.getSocialSecurityAccount());
                            if (hrPlatformAccountDTO == null) {
                                sbError.append("社保账号不存在,请重新填写!");
                            } else {
                                hrClient.setSocialSecurityAccountId(hrPlatformAccountDTO.getId());
                            }
                        }
                        log.info("效验社保账号");
                        //效验医保账号
                        if (hrClientImports.getMedicalInsuranceAccount() != null) {
                            HrPlatformAccountDTO hrPlatformAccountDTO = new HrPlatformAccountDTO();
                            hrPlatformAccountDTO = this.hrClientRepository.getMedicalInsurance(hrClientImports.getMedicalInsuranceAccount());
                            if (hrPlatformAccountDTO == null) {
                                sbError.append("医保账号不存在,请重新填写!");
                            } else {
                                hrClient.setMedicalInsuranceAccountId(hrPlatformAccountDTO.getId());
                            }
                        }
                        log.info("效验医保账号");
                        //效验公积金账号
                        if (hrClientImports.getProvidentFundAccount() != null) {
                            HrPlatformAccountDTO hrPlatformAccountDTO = new HrPlatformAccountDTO();
                            hrPlatformAccountDTO = this.hrClientRepository.getProvidentFund(hrClientImports.getProvidentFundAccount());
                            if (hrPlatformAccountDTO == null) {
                                sbError.append("公积金账号不存在,请重新填写!");
                            } else {
                                hrClient.setProvidentFundAccountId(hrPlatformAccountDTO.getId());
                            }
                        }
                        log.info("效验公积金账号");
                        //效验工资账号
                        if (hrClientImports.getPayrollAccount() != null) {
                            HrPlatformAccountDTO hrPlatformAccountDTO = new HrPlatformAccountDTO();
                            hrPlatformAccountDTO = this.hrClientRepository.getPayroll(hrClientImports.getPayrollAccount());
                            if (hrPlatformAccountDTO == null) {
                                sbError.append("工资账号不存在,请重新填写!");
                            } else {
                                hrClient.setPayrollAccountId(hrPlatformAccountDTO.getId());
                            }
                        }
                        log.info("效验工资账号");
                        //效验社保类型
                        if (hrClientImports.getSocialSecurityType() != null) {
                            HrSocialSecurityDTO hrSocialSecurityDTO = new HrSocialSecurityDTO();
                            hrSocialSecurityDTO = this.hrClientRepository.getSocialSecurity(hrClientImports.getSocialSecurityType());
                            if (hrSocialSecurityDTO == null) {
                                sbError.append("社保类型不存在,请重新填写!");
                            } else {
                                hrClient.setSocialSecurityTypeId(hrSocialSecurityDTO.getId());
                            }
                        }
                        log.info("效验社保类型");
                        //效验公积金类型
                        if (hrClientImports.getProvidentFundType() != null) {
                            HrAccumulationFundDTO hrAccumulationFundDTO = new HrAccumulationFundDTO();
                            hrAccumulationFundDTO = this.hrClientRepository.getProvidentFundType(hrClientImports.getProvidentFundType());
                            if (hrAccumulationFundDTO == null) {
                                sbError.append("公积金类型不存在,请重新填写!");
                            } else {
                                hrClient.setProvidentFundTypeId(hrAccumulationFundDTO.getId());
                            }
                        }
                        log.info("效验公积金类型");
                        //效验专管员
                        if (hrClientImports.getSpecialized() != null) {

                            String specializedId = this.hrClientRepository.getSpecialized(hrClientImports.getSpecialized());
                            if (specializedId == null || specializedId.equals("")) {
                                sbError.append("专管员不存在,请重新填写!");
                            } else {
                                hrClient.setSpecializedId(specializedId);
                            }
                        }
                        log.info("效验专管员");
                        //效验是否发放工资
                        if (hrClientImports.getClientPay() != null) {
                            hrClient.setClientPay(Boolean.valueOf(hrClientImports.getClientPay()));
                            if (hrClientImports.getClientPay().equals("1")) {
                                if (hrClientImports.getPayDate() != null) {
                                    hrClient.setPayDate(hrClientImports.getPayDate());
                                } else {
                                    sbError.append("是否发放工资日期为必填项，请重新填写");
                                }
                            }
                        } else {
                            hrClient.setClientPay(false);
                        }
                        log.info("效验是否发放工资");
                        //查询数据字典中的key
                        //企业性质
                        if (hrClientImports.getEnterpriseNature() != null) {
                            hrClient.setEnterpriseNature(hrClientImports.getEnterpriseNature());
                        }
                        log.info("企业性质");
                        //行业类别
                        if (hrClientImports.getIndustry() != null) {
                            hrClient.setIndustry(hrClientImports.getIndustry());
                        }
                        log.info("行业类别");
                        //业务类别
                        if (StringUtils.isNotEmpty(hrClientImports.getBusinessType()) && !hrClientImports.getBusinessType().equals("null")) {
                            hrClient.setBusinessType(Integer.valueOf(hrClientImports.getBusinessType()));
                        }
                        log.info("业务类别");

                        // 创建客户登录角色信息
                        if (hrClientImports.getUserName() != null && StringUtils.isEmpty(sbError)) {
                            //客户用户名效验
                            log.info("客户用户名效验");
                            int getuser = this.hrClientRepository.getclientUser(hrClientImports.getUserName());
                            if (getuser > 0) {
                                throw new CommonException("用户名重复，请重新进行填写!");
                            }
                            log.info("客户user");
                            User user = new User();
                            user.setUserName(hrClientImports.getUserName());
                            user.setRealName(hrClientImports.getClientName());
                            user.setPassword(passwordEncoder.encode("c123456"));
                            log.info("insert user: {}", user);
                            this.userRepository.insert(user);
                            // 创建角色关联

                            Integer roleid = this.hrClientRepository.getroleId("client");
                            if (roleid != null) {
                                UserRole userRole = new UserRole();
                                userRole.setUserId(user.getId());
                                userRole.setRoleId(roleid);

                                this.hrClientRepository.insertuserrole(userRole);
                            }
                            //录入客户基本信息
                            if (hrClientImports.getClientPay() != null) {
                                if (hrClientImports.getClientPay().equals("1")) {
                                    hrClient.setClientPay(true);
                                } else {
                                    hrClient.setClientPay(false);
                                }
                            }

                            hrClient.setUserId(user.getId());
                            if (org.apache.commons.lang3.StringUtils.isNotBlank(unitSum)) {
                                hrClient.setUnitNumber(unitSum);
                            }

                            this.hrClientRepository.insert(hrClient);
                            //创建试卷
                            QueryWrapper<HrPaperManagement> ew = new QueryWrapper<>();
                            ew.eq("is_preset", 1);
                            HrPaperManagement hrPaperManagement = this.hrPaperManagementRepository.selectOne(ew);
                            HrPaperClient hrPaperClient = new HrPaperClient();
                            hrPaperClient.setClientId(hrClient.getId());
                            hrPaperClient.setPaperId(hrPaperManagement.getId());
                            this.hrPaperClientRepository.insert(hrPaperClient);

                            //专管员进行关联
                            HrUserClient hrUserClient = new HrUserClient();
                            if (hrClient.getSpecializedId() != null) {
                                hrUserClient.setUserId(hrClient.getSpecializedId());
                                hrUserClient.setClientId(hrClient.getId());
                                hrUserClient.setIsSpecialized(true);
                                this.hrUserClientRepository.insert(hrUserClient);
                            }

                        }


                    }

                } catch (Exception e) {
                    log.error("保存客户信息异常:{}", e.getMessage());
                    if (e instanceof MyBatisSystemException) {
                        sbError.append("该信息查询出多条记录!");
                    } else if (e.getMessage().contains("已存在")) {
                        hrClientImports.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    scale++;
                    hrClientImports.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }
            }
            long timeMillis = System.currentTimeMillis();

            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("客户批量导入", HrClientImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CLIENT.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("客户异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "客户导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    //协议
    @Async
    public void saveProtocolData(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        ExcelImportResult<HrProtocolImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrProtocolImport.class);
        //排序
        result.getList().sort((x, y) -> Integer.compare(y.getType(), x.getType()));
        ImportResultDTO importResult;
        Map<String, List<HrProtocolImport>> map = result.getList().stream().collect(Collectors.groupingBy(HrProtocolImport::getAgreementNumber));

        try {
            int scale = 0;
            int listSize = result.getList().size();
            StringBuilder sbError = new StringBuilder();
            for (HrProtocolImport hrProtocolImpors : result.getList()) {
                sbError.delete(0, sbError.length());
                List<HrProtocolImport> HrProtocolImport = new ArrayList<>();
                try {
                    //判断excel中是否存在重复数据
                    String agNumber = hrProtocolImpors.getAgreementNumber();
                    List<HrProtocolImport> hrProtocolImportsa = map.get(agNumber);
                    if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(hrProtocolImportsa) && hrProtocolImportsa.size() > 1) {
                        throw new CommonException("导入Excel中该数据重复!");
                    }

                    if (hrProtocolImpors.getType().equals(1)) {
                        //判断excel中是否存在重复数据
                        String unitNumber = hrProtocolImpors.getAgreementNumber();
                        QueryWrapper<HrProtocol> hrProtocolss = new QueryWrapper<>();
                        hrProtocolss.eq("agreement_number", unitNumber);
                        List<HrProtocol> lists = this.hrProtocolRepository.selectList(hrProtocolss);
                        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(lists)) {
                            sbError.append("协议编号重复，请重新修改!");
                        }
                    }
                    //根据证件号码查询是否已经存在
                    HrProtocol hrProtocols = this.hrProtocolRepository.selectnumber(hrProtocolImpors.getUnitNumber());
                    if (hrProtocols != null) {
                        sbError.append("已存在!");
                    }
                    HrProtocolImport.add(hrProtocolImpors);
                    //导入数据
                    if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(HrProtocolImport)) {
                        //排序
                        HrProtocolImport.sort((x, y) -> Integer.compare(y.getType(), x.getType()));
                        for (cn.casair.dto.excel.HrProtocolImport hrProtocolImport : HrProtocolImport) {
                            // 校验协议标题和服务费
                            if (StringUtils.isBlank(hrProtocolImport.getAgreementTitle())) {
                                sbError.append("协议标题不能为空，请重新输入！");
                            }
                            if (ObjectUtils.isEmpty(hrProtocolImport.getServiceCharge())) {
                                sbError.append("服务费不能为空，请重新输入！");
                            }
                            if (hrProtocolImport.getType().equals(0)) {
                                if (StringUtils.isNotBlank(hrProtocolImport.getAgreementNumber())) {
                                    QueryWrapper<HrProtocol> op = new QueryWrapper<>();
                                    HrClient hrClients = new HrClient();
                                    op.eq("agreement_number", hrProtocolImport.getAgreementNumber());
                                    List<String> lis = new ArrayList<>();
                                    lis.add("0");
                                    lis.add("1");
                                    op.in("use_status", lis);
                                    op.eq("type", 1);
                                    op.last("limit 1");
                                    HrProtocol hrProtocolsa = this.hrProtocolRepository.selectOne(op);
                                    if (hrProtocolsa == null) {
                                        sbError.append("请检查协议编号是否存在!");
                                    }

                                    //判断协议是否所属客户
                                    if (hrProtocolImpors.getType() != null) {
                                        hrProtocolsa.setType(0);
                                    } else {
                                        sbError.append("是否所属客户为必选，请进行选择!");
                                    }

                                    if (hrProtocolImport.getUnitNumber() != null && !hrProtocolImport.getUnitNumber().equals("")) {
                                        List<String> list = this.hrClientRepository.selectunitnumberID(hrProtocolImport.getUnitNumber());
                                        if (list.size() > 1) {
                                            sbError.append("客户编号重复,请重新输入!");

                                        } else {
                                            hrClients = this.hrClientRepository.selectunitnumber(hrProtocolImport.getUnitNumber());
                                        }
                                        if (hrClients != null) {
                                            hrProtocolsa.setClientId(hrClients.getId());
                                        } else {
                                            sbError.append("客户编号不正确,请重新输入!");
                                        }
                                    }
                                    hrProtocolsa.setRenewType(0);
                                    QueryWrapper<HrProtocol> ew = new QueryWrapper<>();
                                    ew.eq("client_id", hrClients.getId());
                                    ew.eq("use_status", 1);
                                    HrProtocol hrProtocolsss = this.hrProtocolRepository.selectOne(ew);
                                    if (hrProtocolsss != null) {
                                        hrProtocolsa.setUseStatus(0);
                                    } else {
                                        hrProtocolsa.setUseStatus(1);
                                    }
                                    hrProtocolsa.setId(null);
                                    if (StringUtils.isEmpty(sbError)) {
                                        this.hrProtocolRepository.insert(hrProtocolsa);
                                        //恢复用户账号
                                        HrClient hrClientas = this.hrClientRepository.selectById(hrProtocolsa.getClientId());
                                        if (hrClientas != null) {
                                            this.userRepository.updateStatus(hrClientas.getUserId());
                                        }
                                    }

                                } else {
                                    sbError.append("协议编号不能为空!");
                                }
                            } else {
                                HrProtocol hrProtocol = new HrProtocol();
                                HrClient hrClient = new HrClient();

                                if (hrProtocolImport.getAgreementStartDate() == null) {
                                    sbError.append("协议开始时间不能为空!");
                                } else {
                                    if (LocalDate.now().isBefore(hrProtocolImport.getAgreementStartDate())) {
                                        hrProtocol.setStates(4);
                                    } else if (hrProtocolImport.getAgreementStartDate().isBefore(LocalDate.now())) {
                                        hrProtocol.setStates(0);
                                    }
                                }
                                if (hrProtocolImport.getAgreementEndDate() == null) {
                                    sbError.append("协议结束时间不能为空!");
                                }
                                if (hrProtocolImport.getUnitNumber() != null && !hrProtocolImport.getUnitNumber().equals("")) {
                                    List<String> list = this.hrClientRepository.selectunitnumberID(hrProtocolImport.getUnitNumber());
                                    List<String> prolist = this.hrProtocolRepository.selectAgreementNumber(hrProtocolImport.getAgreementNumber());
                                    if (prolist.size() > 0 && prolist == null) {
                                        sbError.append("协议编号不正确,请重新输入!");
                                    }

                                    if (list.size() > 1) {
                                        throw new CommonException("客户编号重复,请重新输入!");
                                    } else {
                                        hrClient = this.hrClientRepository.selectunitnumber(hrProtocolImport.getUnitNumber());
                                    }

                                    if (hrClient != null) {
                                        hrProtocol.setClientId(hrClient.getId());
                                    } else {
                                        throw new CommonException("客户编号不正确,请重新输入!");
                                    }
                                }

                                if (hrProtocolImport.getServiceChargeType() != null) {
                                    hrProtocol.setServiceChargeType(hrProtocolImport.getServiceChargeType().toString());
                                } else {
                                    sbError.append("服务费类型不能为空,请重新输入!");
                                }

                                //结算方式
                                if (hrProtocolImport.getSettlementMethod() != null && !hrProtocolImport.getSettlementMethod().equals("")) {
                                    hrProtocol.setSettlementMethod(hrProtocolImport.getSettlementMethod());
                                }
                                BeanUtils.copyProperties(hrProtocolImport, hrProtocol);
                                QueryWrapper<HrProtocol> ew = new QueryWrapper<>();
                                ew.eq("client_id", hrClient.getId());
                                ew.eq("use_status", 1);
                                HrProtocol hrProtocolsss = this.hrProtocolRepository.selectOne(ew);
                                if (hrProtocolsss != null) {
                                    hrProtocol.setUseStatus(0);
                                } else {
                                    hrProtocol.setUseStatus(1);
                                }
                                //获取当前日期
                                DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd");
                                LocalDate newdate = LocalDate.now();
                                if (hrProtocol.getAgreementStartDate() != null) {
                                    if (newdate.now().isBefore(hrProtocol.getAgreementStartDate())) {
                                        hrProtocol.setStates(4);
                                    }
                                }

                                //判断协议编号是不是空
                                if (cn.casair.common.utils.StringUtils.isEmpty(hrProtocolImpors.getAgreementNumber())) {

                                    hrProtocol.setAgreementNumber("XY" + System.currentTimeMillis());
                                }
                                //判断协议是否所属客户
                                if (hrProtocolImpors.getType() != null) {
                                    hrProtocol.setType(hrProtocolImpors.getType());
                                } else {
                                    sbError.append("是否所属客户为必选，请进行选择!");
                                }
                                hrProtocol.setRenewType(0);
                                if (StringUtils.isEmpty(sbError)) {
                                    this.hrProtocolRepository.insert(hrProtocol);
                                    //恢复用户账号
                                    HrClient hrClients = this.hrClientRepository.selectById(hrProtocol.getClientId());
                                    if (hrClients != null) {
                                        this.userRepository.updateStatus(hrClient.getUserId());
                                    }
                                    //对接部门
                                    if (hrProtocolImport.getDocking() != null && !hrProtocolImport.getDocking().equals("")) {
                                        if (hrProtocolImport.getDockingPhone() != null && !hrProtocolImport.getDockingPhone().equals("")) {
                                            if (hrProtocolImport.getDepartment() != null && !hrProtocolImport.getDepartment().equals("")) {
                                                HrDocking hrDocking = new HrDocking();
                                                hrDocking.setProtocolId(hrProtocol.getId());
                                                hrDocking.setDocking(hrProtocolImport.getDocking());
                                                hrDocking.setDockingPhone(hrProtocolImport.getDockingPhone());
                                                hrDocking.setDepartment(hrProtocolImport.getDepartment());
                                                this.hrDockingRepository.insert(hrDocking);
                                            } else {
                                                sbError.append("对接部门不能为空,请重新输入!");
                                            }
                                        } else {
                                            sbError.append("对接人电话不能为空,请重新输入!");
                                        }
                                    }
                                    if (hrProtocolImport.getDockingg() != null && !hrProtocolImport.getDockingg().equals("")) {
                                        if (hrProtocolImport.getDockingPhonee() != null && !hrProtocolImport.getDockingPhonee().equals("")) {
                                            if (hrProtocolImport.getDepartmentt() != null && !hrProtocolImport.getDepartmentt().equals("")) {
                                                HrDocking hrDocking = new HrDocking();
                                                hrDocking.setProtocolId(hrProtocol.getId());
                                                hrDocking.setDocking(hrProtocolImport.getDockingg());
                                                hrDocking.setDockingPhone(hrProtocolImport.getDockingPhonee());
                                                hrDocking.setDepartment(hrProtocolImport.getDepartmentt());
                                                this.hrDockingRepository.insert(hrDocking);
                                            } else {
                                                sbError.append("对接部门不能为空,请重新输入!");
                                            }
                                        } else {
                                            sbError.append("对接人电话不能为空,请重新输入!");
                                        }
                                    }

                                    if (hrProtocolImport.getDockinggs() != null && !hrProtocolImport.getDockinggs().equals("")) {
                                        if (hrProtocolImport.getDockingPhonees() != null && !hrProtocolImport.getDockingPhonees().equals("")) {
                                            if (hrProtocolImport.getDepartmentts() != null && !hrProtocolImport.getDepartmentts().equals("")) {
                                                HrDocking hrDocking = new HrDocking();
                                                hrDocking.setProtocolId(hrProtocol.getId());
                                                hrDocking.setDocking(hrProtocolImport.getDockinggs());
                                                hrDocking.setDockingPhone(hrProtocolImport.getDockingPhonees());
                                                hrDocking.setDepartment(hrProtocolImport.getDepartmentts());
                                                this.hrDockingRepository.insert(hrDocking);

                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }


                } catch (Exception e) {
                    log.error("保存客户协议信息异常:{}", e.getMessage());
                    if (e instanceof MyBatisSystemException) {
                        sbError.append("该信息查询出多条记录!");
                    } else if (e.getMessage().contains("已存在")) {
                        hrProtocolImpors.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    hrProtocolImpors.setErrorMsg(sbError.toString());
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);

                }
            }

            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("客户协议批量导入", HrProtocolImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CLIENT_PROTOCOL.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("客户协议异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "客户协议导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    //专项扣除
    @Async
    public void saveSpecialDeduction(MultipartFile file, String redisKey, JWTUserDTO jwtUserDTO) {
        ExcelImportResult<HrSpecialDeductionImport> result = ExcelUtils.importExcelKeyIndex(file, 0, 1, true, HrSpecialDeductionImport.class);
        List<HrTalentStaff> hrTalentStaffList = hrTalentStaffRepository.selectList(new QueryWrapper<HrTalentStaff>().eq("is_delete", 0));
        List<HrSpecialDeduction> hrSpecialDeductionList = hrSpecialDeductionRepository.selectList(new QueryWrapper<HrSpecialDeduction>().eq("is_delete", 0));

        ImportResultDTO importResult;
        try {
            int scale = 0;
            int listSize = result.getList().size();
            List<HrSpecialDeductionImport> list = result.getList();
            Map<String, List<HrSpecialDeductionImport>> listMap = list.stream().collect(Collectors.groupingBy(o -> o.getTalentName() + "_" + o.getCertificateNum()));
            StringBuilder sbError = new StringBuilder();
            for (HrSpecialDeductionImport hrSpecialDeductionImport : list) {
                try {
                    sbError.delete(0, sbError.length());
                    //身份证号
                    String unitNumber = hrSpecialDeductionImport.getCertificateNum();
                    //员工姓名
                    String talentName = hrSpecialDeductionImport.getTalentName();
                    List<HrSpecialDeductionImport> mapList = listMap.get(talentName + "_" + unitNumber);
                    if (CollectionUtils.isNotEmpty(mapList) && mapList.size() > 1) {
                        throw new CommonException("导入Excel中该数据存在重复!");
                    }
                    //税款所属期起
                    LocalDate startDate = null;
                    if (hrSpecialDeductionImport.getStartDate() != null) {
                        startDate = hrSpecialDeductionImport.getStartDate();
                    } else {
                        sbError.append("税款所属期起不能为空");
                    }

                    // 税款所属期止
                    LocalDate endDate = null;
                    if (hrSpecialDeductionImport.getEndDate() != null) {
                        endDate = hrSpecialDeductionImport.getEndDate();
                    } else {
                        sbError.append("税款所属期止不能为空");
                    }

                    //判断税款所属期开始和税款所属期结束是否正确
                    ZonedDateTime zonedDateTime = startDate.atStartOfDay(ZoneId.systemDefault());
                    ZonedDateTime zonedDateTimeend = endDate.atStartOfDay(ZoneId.systemDefault());
                    Date start = Date.from(zonedDateTime.toInstant());
                    Date end = Date.from(zonedDateTimeend.toInstant());
                    Calendar calendar = Calendar.getInstance();
                    Calendar calendarend = Calendar.getInstance();
                    calendar.setTime(start);
                    calendarend.setTime(end);
                    if (calendar.get(Calendar.DAY_OF_MONTH) != 1) {
                        sbError.append("税款所属期开始日期不正确，请重新输入!");
                    }
                    if (calendarend.get(Calendar.DAY_OF_MONTH) != calendarend
                        .getActualMaximum(Calendar.DAY_OF_MONTH)) {
                        sbError.append("税款所属期结束日期不正确，请重新输入!");
                    }
                    //查询用户id
                    HrTalentStaff hrTalentStaff = hrTalentStaffList.stream().filter(lst -> lst.getCertificateNum() != null && lst.getCertificateNum().equalsIgnoreCase(unitNumber)).findFirst().orElse(null);
                    if (hrTalentStaff == null) {
                        throw new CommonException("员工信息不存在");
                    }
                    if (StringUtils.isEmpty(sbError)) {
                        //根据证件号码 时间  员工id查询 要是有重复的进行逻辑删除
                        LocalDate finalStartDate = startDate;
                        LocalDate finalEndDate = endDate;
                        List<HrSpecialDeduction> collect = hrSpecialDeductionList.stream().filter(lst -> lst.getTalentStaffId().equalsIgnoreCase(hrTalentStaff.getId())
                            && lst.getStartDate().isEqual(finalStartDate) && lst.getEndDate().isEqual(finalEndDate)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect)) {
                            List<String> ids = collect.stream().map(HrSpecialDeduction::getId).collect(Collectors.toList());
                            hrSpecialDeductionRepository.deleteBatchIds(ids);
                        }
                        HrSpecialDeduction hrSpecialDeductions = new HrSpecialDeduction();
                        hrSpecialDeductions.setClientId(hrTalentStaff.getClientId());
                        hrSpecialDeductions.setTalentStaffId(hrTalentStaff.getId());
                        BeanUtils.copyProperties(hrSpecialDeductionImport, hrSpecialDeductions);
                        this.hrSpecialDeductionRepository.insert(hrSpecialDeductions);
                    }
                } catch (Exception e) {
                    log.error("保存客户信息异常:{}", e.getMessage());
                    if (e instanceof MyBatisSystemException) {
                        sbError.append("该信息查询出多条记录!");
                    } else if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("已存在")) {
                        hrSpecialDeductionImport.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    scale++;
                    hrSpecialDeductionImport.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }

            }
            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("专项扣除批量导入", HrSpecialDeductionImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.SPECIAL_DEDUCTION.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("专项扣除异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "专项扣除导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }
}
