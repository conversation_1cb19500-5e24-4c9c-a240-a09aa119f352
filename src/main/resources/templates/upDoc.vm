**简要描述：**

- $!{table.comment} 数据更新接口文档

**请求URL：**
- ` ${table.entityPath}/update${entity} `

**请求方式：**
- PUT

**请求示例**

```
{
#foreach($field in ${table.fields})
"${field.propertyName}":"", //${field.comment}
#end
}
```


**参数：**

|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
#foreach($field in ${table.fields})
|${field.propertyName} |是  |${field.propertyType} |${field.comment}   |
#end

**返回示例**

```
{ "data": "数据更改成功！", "errorCode": 0, "errorMessage": "SUCCESS" }
```

**返回参数说明**

|参数名|类型|说明|
|:-----  |:-----|-----                           |


**备注**

- 更多返回错误代码请看首页的错误代码描述

