package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.FileUtil;
import cn.casair.common.utils.StringUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.mapper.HrPaperManagementMapper;
import cn.casair.mapper.HrPaperQuestionMapper;
import cn.casair.repository.*;
import cn.casair.service.HrPaperManagementService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.minio.MinioClient;
import io.minio.ObjectWriteResponse;
import io.minio.PutObjectArgs;
import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.DocumentEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 试卷管理服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrPaperManagementServiceImpl extends ServiceImpl<HrPaperManagementRepository, HrPaperManagement> implements HrPaperManagementService {

    private static final Logger log = LoggerFactory.getLogger(HrPaperManagementServiceImpl.class);
    private final HrPaperClientRepository hrPaperClientRepository;
    private final HrPaperStationRepository hrPaperStationRepository;
    private final HrPaperQuestionRepository hrPaperQuestionRepository;
    private final MinioClient minioClient;
    private final SysOperLogService sysOperLogService;
    private final HrPaperQuestionMapper hrPaperQuestionMapper;

    private final HrPaperManagementRepository hrPaperManagementRepository;
    private final HrPaperManagementMapper hrPaperManagementMapper;
    private final HrQuestionRepository hrQuestionRepository;
    @Value("${minio.serverUrl}")
    private String serverUrl;


    /**
     * 临时桶
     */
    @Value("${minio.defaultBucket}")
    private String defaultBucket;


    @Value("${file.temp-path}")
    private String tempPath;

    public HrPaperManagementServiceImpl(HrPaperClientRepository hrPaperClientRepository, HrPaperStationRepository hrPaperStationRepository, HrPaperQuestionRepository hrPaperQuestionRepository, MinioClient minioClient, SysOperLogService sysOperLogService, HrPaperQuestionMapper hrPaperQuestionMapper, HrPaperManagementRepository hrPaperManagementRepository, HrPaperManagementMapper hrPaperManagementMapper, HrQuestionRepository hrQuestionRepository) {
        this.hrPaperClientRepository = hrPaperClientRepository;
        this.hrPaperStationRepository = hrPaperStationRepository;
        this.hrPaperQuestionRepository = hrPaperQuestionRepository;
        this.minioClient = minioClient;
        this.sysOperLogService = sysOperLogService;
        this.hrPaperQuestionMapper = hrPaperQuestionMapper;
        this.hrPaperManagementRepository = hrPaperManagementRepository;
        this.hrPaperManagementMapper = hrPaperManagementMapper;
        this.hrQuestionRepository = hrQuestionRepository;
    }


    /**
     * 创建试卷管理
     *
     * @param hrPaperManagementDTO
     * @return
     */
    @Override
    public HrPaperManagementDTO createHrPaperManagement(HrPaperManagementDTO hrPaperManagementDTO) {
        log.info("Create new HrPaperManagement:{}", hrPaperManagementDTO);
        hrPaperManagementDTO.setUsageSum(null);
        hrPaperManagementDTO.setIsPreset(null);
        HrPaperManagement hrPaperManagement = this.hrPaperManagementMapper.toEntity(hrPaperManagementDTO);
        hrPaperManagement.setPaperStatus(hrPaperManagementDTO.getPaperStatus());
        this.hrPaperManagementRepository.insert(hrPaperManagement);
        if (hrPaperManagementDTO.getClientIdList() != null) {
            for (String s : hrPaperManagementDTO.getClientIdList()) {
                HrPaperClient hrPaperClient = new HrPaperClient();
                hrPaperClient.setPaperId(hrPaperManagement.getId());
                hrPaperClient.setClientId(s);
                this.hrPaperClientRepository.insert(hrPaperClient);
            }
        }
        if (hrPaperManagementDTO.getStationIdList() != null) {
            for (String s : hrPaperManagementDTO.getStationIdList()) {
                HrPaperStation hrPaperStation = new HrPaperStation();
                hrPaperStation.setPaperId(hrPaperManagement.getId());
                hrPaperStation.setStationId(s);
                this.hrPaperStationRepository.insert(hrPaperStation);
            }
        }
        if (hrPaperManagementDTO.getQuestionIdDTO() != null) {
            for (HrPaperQuestionDTO hrPaperQuestionDTO : hrPaperManagementDTO.getQuestionIdDTO()) {
                HrPaperQuestion hrPaperQuestion = new HrPaperQuestion();
                hrPaperQuestion.setPaperId(hrPaperManagement.getId());
                hrPaperQuestion.setQuestionId(hrPaperQuestionDTO.getId());
                hrPaperQuestion.setOrdersum(hrPaperQuestionDTO.getOrdersum());
                this.hrPaperQuestionRepository.insert(hrPaperQuestion);
            }

        }
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.TEST_PAPER.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrPaperManagementDTO),
            HrPaperManagementDTO.class,
            null,
            JSON.toJSONString(hrPaperManagement)
        );
        return this.hrPaperManagementMapper.toDto(hrPaperManagement);
    }

    /**
     * 修改试卷管理
     *
     * @param hrPaperManagementDTO
     * @return
     */
    @Override
    public ResponseEntity<String> updateHrPaperManagement(HrPaperManagementDTO hrPaperManagementDTO) {
        if (hrPaperManagementDTO.getId() != null) {
            List<String> Pape = new ArrayList<>();
            Pape.add(hrPaperManagementDTO.getId());
            this.deleteHrPaperManagement(Pape);
            hrPaperManagementDTO.setId(null);
            this.createHrPaperManagement(hrPaperManagementDTO);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(
                ModuleTypeEnum.TEST_PAPER.getValue(),
                BusinessTypeEnum.UPDATE.getKey(),
                JSON.toJSONString(hrPaperManagementDTO),
                HrPaperManagementDTO.class,
                null,
                JSON.toJSONString(hrPaperManagementDTO)
            );
            return ResponseUtil.buildSuccess("修改成功");
        }else{
            return ResponseUtil.buildSuccess("修改失败");
        }



    }

    /**
     * 查询试卷管理详情
     *
     * @param id
     * @return
     */
    @Override
    public HrPaperManagementDTO getHrPaperManagement(String id) {
        log.info("Get HrPaperManagement :{}", id);
        HrPaperManagementDTO hrPaperManagement = new HrPaperManagementDTO();
        hrPaperManagement = this.hrPaperManagementRepository.selectmanId(id);
        List<HrQuestionDTO> list = this.hrPaperManagementRepository.selectHrQuestion(id);
        if (list != null) {
            hrPaperManagement.setQuestionDTO(list);
        }
        if (hrPaperManagement.getClientId() != null) {
            hrPaperManagement.setClientIdList(this.hrPaperManagementRepository.selectClientid(id));
        }
        if (hrPaperManagement.getStationId() != null) {
            hrPaperManagement.setStationIdList(Arrays.asList(hrPaperManagement.getStationId().split(",")));
        }
        // 将逗号分隔的字符串转换为List
        String str = hrPaperManagement.getClientId();
        // 1.使用JDK,逗号分隔的字符串-->数组-->list
        List<String> result = Arrays.asList(str.split(","));
        // 2.使用Apache Commons的StringUtils
        List<String> result1 = Arrays.asList(str.split(","));
        // 3.通过遍历
        String[] strings = str.split(",");
        String clientName = "";
        StringBuffer permission = new StringBuffer();
        for (String string : strings) {
            String clientNames = this.hrPaperManagementRepository.getClientName(string);
            if (clientNames != null) {
                clientName += clientNames + ",";
            }
        }
        if (StringUtils.isNotEmpty(clientName)){
            hrPaperManagement.setClientName(clientName.substring(0, clientName.length() - 1));
        }

        // 将逗号分隔的字符串转换为List
        String strs = hrPaperManagement.getStationId();
        // 1.使用JDK,逗号分隔的字符串-->数组-->list
        List<String> results = Arrays.asList(strs.split(","));
        // 2.使用Apache Commons的StringUtils
        List<String> result1s = Arrays.asList(strs.split(","));
        // 3.通过遍历
        String[] stringss = strs.split(",");
        String stationName = "";
        for (String stringa : stringss) {
            String stationNames = this.hrPaperManagementRepository.getStationName(stringa);
            if (stationNames != null) {
                stationName += stationNames + ",";
            }

        }

        hrPaperManagement.setStationName(stationName.substring(0, stationName.length() - 1));


        return hrPaperManagement;

    }

    /**
     * 删除试卷管理
     *
     * @param id
     */
    @Override
    public void deleteHrPaperManagement(String id) {
        Optional.ofNullable(this.hrPaperManagementRepository.selectById(id))
            .ifPresent(hrPaperManagement -> {
                this.hrPaperManagementRepository.deleteById(id);
                log.info("Delete HrPaperManagement:{}", hrPaperManagement);
            });
    }

    /**
     * 批量删除试卷管理
     *
     * @param ids
     */
    @Override
    public void deleteHrPaperManagement(List<String> ids) {
        log.info("Delete HrPaperManagements:{}", ids);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrPaperManagement>hrPaperManagements=this.hrPaperManagementRepository.selectBatchIds(ids);
        for (String id : ids) {
            QueryWrapper<HrPaperManagement> qw = new QueryWrapper<>();
            QueryWrapper<HrPaperClient> qwc = new QueryWrapper<>();
            QueryWrapper<HrPaperQuestion> qwq = new QueryWrapper<>();
            QueryWrapper<HrPaperStation> qws = new QueryWrapper<>();
            qw.eq("id", id);
            qwc.eq("paper_id", id);
            qwq.eq("paper_id", id);
            qws.eq("paper_id", id);
            this.hrPaperManagementRepository.delete(qw);
            this.hrPaperQuestionRepository.delete(qwq);
            this.hrPaperClientRepository.delete(qwc);
            this.hrPaperStationRepository.delete(qws);

        }
        List<String> collect = hrPaperManagements.stream().map(HrPaperManagement::getPaperName).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.TEST_PAPER.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除试卷: "+JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(hrPaperManagements),
            jwtUserDTO
        );
    }

    /**
     * 分页查询试卷管理
     *
     * @param hrPaperManagementDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrPaperManagementDTO hrPaperManagementDTO, Long pageNumber, Long pageSize) {
        Page<HrPaperManagement> page = new Page<>(pageNumber, pageSize);
        this.setAssignment(hrPaperManagementDTO);
        IPage<HrPaperManagementDTO> iPage = this.hrPaperManagementRepository.pageselect(page, hrPaperManagementDTO);
        for (HrPaperManagementDTO record : iPage.getRecords()) {
            // 将逗号分隔的字符串转换为List
            String str = record.getClientId();
            // 1.使用JDK,逗号分隔的字符串-->数组-->list
            List<String> result = Arrays.asList(str.split(","));
            // 2.使用Apache Commons的StringUtils
            List<String> result1 = Arrays.asList(str.split(","));
            // 3.通过遍历
            String[] strings = str.split(",");
            String clientName = "";
            StringBuffer permission = new StringBuffer();
            for (String string : strings) {
                String clientNames = this.hrPaperManagementRepository.getClientName(string);
                if (clientNames != null) {
                    clientName += clientNames + ",";
                }
            }
            if ( clientName.length()!=0){
                record.setClientName(clientName.substring(0, clientName.length() - 1));
            }


            // 将逗号分隔的字符串转换为List
            String strs = record.getStationId();
            // 1.使用JDK,逗号分隔的字符串-->数组-->list
            if(StringUtils.isNotEmpty(strs)){
                List<String> results = Arrays.asList(strs.split(","));
                // 2.使用Apache Commons的StringUtils
                List<String> result1s = Arrays.asList(strs.split(","));
                // 3.通过遍历
                String[] stringss = strs.split(",");
                String stationName = "";
                for (String stringa : stringss) {
                    String stationNames = this.hrPaperManagementRepository.getStationName(stringa);
                    if (stationNames != null) {
                        stationName += stationNames + ",";
                    }
                }
                record.setStationName(stationName.substring(0, stationName.length() - 1));
            }


        }


        return iPage;
    }

    private void setAssignment(HrPaperManagementDTO hrPaperManagementDTO) {
        if (hrPaperManagementDTO.getField()!=null){
            if (hrPaperManagementDTO.getField().equals("paper_name")){
                hrPaperManagementDTO.setField("paperName");
            }
            if (hrPaperManagementDTO.getField().equals("client_name")){
                hrPaperManagementDTO.setField("clientId");
            }
            if (hrPaperManagementDTO.getField().equals("station_name")){
                hrPaperManagementDTO.setField("stationId");
            }
            if (hrPaperManagementDTO.getField().equals("created_date")){
                hrPaperManagementDTO.setField("createdDate");
            }

            if (hrPaperManagementDTO.getField().equals("usage_sum")){
                hrPaperManagementDTO.setField("usageSum");
            }
            if (hrPaperManagementDTO.getField().equals("paper_status")){
                hrPaperManagementDTO.setField("paperStatus");
            }
        }
        if (hrPaperManagementDTO.getClientIdList() != null) {
            hrPaperManagementDTO.setClientId(String.join(",", hrPaperManagementDTO.getClientIdList()));
        }
        if (hrPaperManagementDTO.getStationIdList() != null) {
            hrPaperManagementDTO.setStationId(String.join(",", hrPaperManagementDTO.getStationIdList()));
        }
    }

    /**
     * GET /hr-paper-managements/:id
     * <p>
     * 查询试卷适用单位
     *
     * @param
     * @return
     */
    @Override
    public List<HrPaperManagementDTO> getHrPaperManagementClient() {
        List<HrPaperManagementDTO> list = this.hrPaperManagementRepository.getHrPaperManagementClient();
        return list;
    }

    /**
     * GET /hr-paper-managements/:id
     * <p>
     * 查询试卷适用岗位
     *
     * @param
     * @return
     */

    @Override
    public List<HrPaperManagementDTO> getHrPaperManagementStation() {
        List<HrPaperManagementDTO> list = this.hrPaperManagementRepository.getHrPaperManagementStation();
        return list;
    }

    /**
     * GET /hr-paper-managements/:id
     * <p>
     * 批量下载试卷管理
     *
     * @param
     * @param paperManagementDTO
     * @return
     */
    @Override
    public List<HrPaperManagementDTO> HrPaperManagementDownload(HrPaperManagementDTO paperManagementDTO) {
        this.setAssignment(paperManagementDTO);
        List<HrPaperManagementDTO> hrPaperManagementDTOS = hrPaperManagementRepository.findList(paperManagementDTO);
        List<String> ids = hrPaperManagementDTOS.stream().map(HrPaperManagementDTO::getId).collect(Collectors.toList());

        for (HrPaperManagementDTO hrPaperManagementDTO : hrPaperManagementDTOS) {
            // 数据库查富文本数据
            String richText = "<html><body>" + hrPaperManagementDTO.getPreview() + "</body></html>";
            try {
                //设置编码
                byte b[] = richText.getBytes("GBK");
                ByteArrayInputStream bais = new ByteArrayInputStream(b);
                POIFSFileSystem poifs = new POIFSFileSystem();
                // ##############下面这两个不能删掉
                DirectoryEntry directory = poifs.getRoot();
                DocumentEntry documentEntry = directory.createDocument("WordDocument", bais);
                //################这两个不能删掉
                //输出文件
                String name = hrPaperManagementDTO.getPaperName();
                SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");//设置日期格式
                String date = df.format(new Date());// new Date()为获取当前系统时间，也可使用当前时间戳

                String namedate = name + date;

                //输出到本地文件的话，new一个文件流
                String targetPath = tempPath + namedate + ".doc";
                File file = new File(targetPath);

                FileOutputStream ostream = new FileOutputStream(targetPath);
                poifs.writeFilesystem(ostream);
                bais.close();
                ostream.close();
                String originalFileName = file.getName();
                if (org.apache.commons.lang3.StringUtils.isBlank(originalFileName)) {
                    throw new CommonException("获取文件名称异常！");
                }
                String fileType = FileUtil.getFileExt(originalFileName);
                String contentType = FileUtil.checkFileType(fileType);
                String idas = UUID.randomUUID().toString().replace("-", "");
                String fileName = idas + FileUtil.getFileExt(originalFileName);
                FileInputStream fileInputStream = new FileInputStream(file);
                PutObjectArgs objectArgs = PutObjectArgs.builder().object(fileName)
                    .bucket(defaultBucket)
                    .contentType(contentType == null ? "application/octet-stream" : contentType)
                    .stream(fileInputStream, fileInputStream.available(), -1).build();
                ObjectWriteResponse writeResponse = minioClient.putObject(objectArgs);
                fileInputStream.close();

                // 删除临时文件
                FileUtil.deleteTempFile(targetPath);

                // 文件服务器路径
                String fileUrlss = serverUrl + "/" + defaultBucket + "/" + fileName;

                // 并返回

                hrPaperManagementDTO.setFileUrL(fileUrlss);
                hrPaperManagementDTO.setFileUrLName(name);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //下载日志
        String operDetail = "试卷模板下载:" + hrPaperManagementDTOS.stream().map(HrPaperManagementDTO::getFileUrLName).collect(Collectors.joining(","));
        String fileUrl = hrPaperManagementDTOS.stream().map(HrPaperManagementDTO::getFileUrL).collect(Collectors.joining(","));
        this.sysOperLogService.insertDownloadSysOper(ModuleTypeEnum.EXAM.getValue(), BusinessTypeEnum.DOWNLOAD.getKey(), operDetail, JSON.toJSONString(ids), fileUrl);
        return hrPaperManagementDTOS;
    }

    @Override
    public Object getHrPaperManagementScore(String id) {
        QueryWrapper<HrQuestion> re=new QueryWrapper<>();
        re.eq("question_type",id);
        re.groupBy("score");
        List<HrQuestion> list=this.hrQuestionRepository.selectList(re);
        List< BigDecimal>scoresum=new ArrayList<>();
        for (HrQuestion hrQuestion : list) {
            scoresum.add(hrQuestion.getScore());
        }
        return scoresum;
    }


    /**
     * 入职考试修改
     *
     * @param
     * @return
     */
    @Override
    public Object getHrPaperManagementSingleUpdate(HrPaperManagementDTO hrPaperManagementDTO) {
        HrPaperManagement hrPaperManagement=this.hrPaperManagementMapper.toEntity(hrPaperManagementDTO);
        this.hrPaperManagementRepository.updateById(hrPaperManagement);
        QueryWrapper<HrPaperQuestion>wq=new QueryWrapper<>();
        wq.eq("paper_id",hrPaperManagement.getId());
        this.hrPaperQuestionRepository.delete(wq);
        for (HrPaperQuestionDTO hrPaperQuestionDTO : hrPaperManagementDTO.getQuestionIdDTO()) {
            HrPaperQuestion hrPaperQuestion=this.hrPaperQuestionMapper.toEntity(hrPaperQuestionDTO);
            hrPaperQuestion.setPaperId(hrPaperManagementDTO.getId());
            hrPaperQuestion.setQuestionId(hrPaperQuestionDTO.getId());
            hrPaperQuestion.setOrdersum(hrPaperQuestionDTO.getOrdersum());
            hrPaperQuestion.setId(null);
            this.hrPaperQuestionRepository.insert(hrPaperQuestion);
         }
        return this.hrPaperManagementMapper.toDto(hrPaperManagement);
    }
    /**
     * 查询题目类型是单选和多选的
     *
     * @param
     * @return
     */
    @Override
    public IPage<HrQuestion> getHrPaperManagementSingle(HrPaperManagementDTO hrPaperManagementDTO, Long pageNumber, Long pageSize) {
        Page<HrQuestion> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrQuestion> wq=new QueryWrapper<>();
        List<String>list=new ArrayList<>();
        list.add("0");
        list.add("1");
        wq.in("question_type",list);
        //排序
        if (cn.casair.common.utils.StringUtils.isNotBlank(hrPaperManagementDTO.getOrder())) {
            if (hrPaperManagementDTO.getOrder().equals("DESC")) {
                wq.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrPaperManagementDTO.getField()), false, hrPaperManagementDTO.getField());
            } else {
                wq.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrPaperManagementDTO.getField()), true, hrPaperManagementDTO.getField());
            }
        }
         IPage<HrQuestion> iPage=this.hrQuestionRepository.selectPage(page,wq);
         return iPage;
    }

    /**
     * //查询题目类型数量
     *
     * @param
     * @return
     */

    @Override
    public int questionssSumRandom(HrQuestionDTO hrQuestionDTO) {
        List<HrQuestionDTO> questionssSum = this.hrPaperManagementRepository.selectQuestionssSum(hrQuestionDTO);
        return questionssSum.size();
    }

    /**
     * 查询新建试卷适用单位
     *
     * @param
     * @return
     */
    @Override
    public List<HrPaperManagementDTO> getHrPaperManagementIndexClient() {
        List<HrPaperManagementDTO> list = this.hrPaperManagementRepository.getHrPaperManagementIndexClient();
        return list;
    }

    /**
     * 查询新建试卷适用单位
     *
     * @param
     * @return
     */
    @Override
    public List<HrPaperManagementDTO> getHrPaperManagementIndexStation() {
        List<HrPaperManagementDTO> list = this.hrPaperManagementRepository.getHrPaperManagementIndexStation();
        return list;
    }

    /**
     * POST /hr-paper-managements/page
     * <p>
     * 分页查询新建试卷题库
     *
     * @param hrQuestionDTO
     * @param pageNumber
     * @param pageSize
     * @returnh
     */
    @Override
    public IPage<HrQuestionDTO> findPageHrQuestion(HrQuestionDTO hrQuestionDTO, Long pageNumber, Long pageSize) {
        Page<HrPaperQuestion> page = new Page<>(pageNumber, pageSize);
        IPage<HrQuestionDTO> iPage = new Page<>();
        List<HrQuestionDTO> hrQuestionList = new ArrayList<>();
        if (hrQuestionDTO.getStationIdList() != null) {
            iPage=this.hrPaperManagementRepository.selectQuestionPage(page, hrQuestionDTO);
        } else {
            throw new CommonException("适用岗位不能为空!");
        }

        return iPage;
    }

    /**
     * GET /hr-paper-managements/:id
     * <p>
     * 复制试卷管理
     *
     * @param
     * @return
     */
    @Override
    public void getCopyHrPaperManagement(HrPaperManagementDTO hrPaperManagementDTO) {
         hrPaperManagementDTO.setId(null);
        hrPaperManagementDTO.setUsageSum(null);
        hrPaperManagementDTO.setIsPreset(null);
        hrPaperManagementDTO.setCreatedDate(null);
        this.createHrPaperManagement(hrPaperManagementDTO);

    }

    /**
     * 查询新建试卷分数
     *
     * @param
     * @return
     */
    @Override
    public List<HrQuestionDTO> getHrPaperManagementIndexScore(HrQuestionDTO hrQuestionDTO) {
        List<String> questionId = this.hrPaperManagementRepository.getHrPaperManagementIndexScore(hrQuestionDTO);
        List<HrQuestionDTO> list = this.hrPaperManagementRepository.getHrPaperManagementScore(questionId);
        return list;
    }


    /**
     * POST /hr-paper-managements/page
     * <p>
     * 系统随机分页查询新建试卷题库
     *
     * @param hrQuestionDTO
     * @param
     * @param
     * @returnh
     */
    @Override
    public List<HrQuestionDTO> findPageRandom(HrQuestionDTO hrQuestionDTO) {
        IPage<HrQuestionDTO> iPage = new Page<>();
        List<String> questionId = new ArrayList<>();
        //循环获取题目类型的id
        for (HrPagerSetUpDTO hrPagerSetUpDTO : hrQuestionDTO.getHrPagerSetUpDTO()) {
            HrQuestionDTO hrQuestionDTOs = new HrQuestionDTO();

            hrQuestionDTOs.setScore(hrPagerSetUpDTO.getScore());
            hrQuestionDTOs.setQuestionType(hrPagerSetUpDTO.getQuestionType());
            hrQuestionDTOs.setApplicablePostList(hrQuestionDTO.getStationIdList());
            if (CollectionUtils.isNotEmpty(hrQuestionDTO.getHrPagerQuestionProDTO())) {
                for (HrPagerQuestionProDTO hrPagerQuestionProDTO : hrQuestionDTO.getHrPagerQuestionProDTO()) {
                    for (HrPagerSetUpDTO pagerSetUpDTO : hrQuestionDTO.getHrPagerSetUpDTO()) {
                        //获取属性的单个数量
                        BigDecimal ScoreSums = null;
                        Integer sunint = pagerSetUpDTO.getScoreSum();
                        Integer sum = 9;
                        if (sunint > sum) {
                            ScoreSums = BigDecimal.valueOf(pagerSetUpDTO.getScoreSum());
                        } else {
                            BigDecimal Score = BigDecimal.valueOf(pagerSetUpDTO.getScoreSum());
                            ScoreSums = Score.multiply(BigDecimal.valueOf(10));
                        }
                        //获取属性的占比
                        BigDecimal percentage = hrPagerQuestionProDTO.getPercentage();
                        //获取单个题目获取的题目数
                        int scoreSum = ScoreSums.multiply(percentage).intValue();

                        hrQuestionDTOs.setQuestionPro(hrPagerQuestionProDTO.getQuestionPro());
                        hrQuestionDTOs.setScoreSum(scoreSum);
                        hrQuestionDTOs.setQuestionType(pagerSetUpDTO.getQuestionType());
                        List<String> questionIds = this.hrPaperManagementRepository.getQuestionId(hrQuestionDTOs);
                        questionId.addAll(questionIds);
                    }

                }

            } else {
                hrQuestionDTOs.setScoreSum(hrPagerSetUpDTO.getScoreSum());
                hrQuestionDTOs.setScore(hrPagerSetUpDTO.getScore());
                hrQuestionDTOs.setQuestionType(hrPagerSetUpDTO.getQuestionType());
                List<String> questionIds = this.hrPaperManagementRepository.getQuestionId(hrQuestionDTOs);
                questionId.addAll(questionIds);

            }
        }
        if (CollectionUtils.isEmpty(questionId)) {
            return null;
        }

    //集合去除重复的id
        List<String> questionIdNew = new ArrayList<String>(new TreeSet<String>(questionId));

    /*        if (questionIdNew.size() != hrQuestionDTO.getTotalscoreSum()) {
            for (HrPagerSetUpDTO pagerSetUpDTO : hrQuestionDTO.getHrPagerSetUpDTO()) {
                HrQuestionDTO hrQuestionDTOS = new HrQuestionDTO();
                int i = hrQuestionDTO.getTotalscoreSum();
                int o = questionIdNew.size();
                int sum = i - o;
                if (sum < 0) {
                    sum = i;
                }
                hrQuestionDTOS.setScoreSum(sum);
                hrQuestionDTOS.setQuestionType(pagerSetUpDTO.getQuestionType());
                hrQuestionDTOS.setApplicablePostList(hrQuestionDTO.getStationIdList());
                List<BigDecimal>scoreList=new ArrayList<>();
                for (HrPagerSetUpDTO hrPagerSetUpDTO : hrQuestionDTO.getHrPagerSetUpDTO()) {
                    List<BigDecimal>list=new ArrayList<>();
                    list.add(hrPagerSetUpDTO.getScore());
                    scoreList.addAll(list);
                }
                hrQuestionDTOS.setScoredList(scoreList);
                List<String> lists = this.hrQuestionRepository.getlistsQuestion(hrQuestionDTOS);
                questionIdNew.addAll(lists);
            }
        }*/

        List<HrQuestionDTO> list = this.hrQuestionRepository.selectQuesionid(questionId, hrQuestionDTO.getTotalscoreSum());
        return list;
    }


    /**
     * POST /hr-paper-managements/page
     * <p>
     * 系统随机分页查询新建试卷更换
     * 百分比传我小数
     *
     * @param hrQuestionDTO
     * @param pageNumber
     * @param pageSize
     * @returnh
     */
    @Override
    public IPage<HrQuestionDTO> findPageReplace(HrQuestionDTO hrQuestionDTO, Long pageNumber, Long pageSize) {
        QueryWrapper<HrQuestion> qw = new QueryWrapper<>();
        Page<HrQuestion> page = new Page<>(pageNumber, pageSize);

        IPage iPage = this.hrQuestionRepository.selectPages(page, hrQuestionDTO);
        return iPage;
    }

}
