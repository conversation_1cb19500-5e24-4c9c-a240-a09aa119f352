<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrUpcomingRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, schedule_id, title, type, is_warn, warn_time, priority, is_top, upcoming_date, is_edit, upcoming_status, remark,
is_delete                    ,user_id,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrUpcoming">
        <id column="id" property="id"/>
        <result column="schedule_id" property="scheduleId"/>
        <result column="user_id" property="userId"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="is_warn" property="isWarn"/>
        <result column="warn_time" property="warnTime"/>
        <result column="priority" property="priority"/>
        <result column="is_top" property="isTop"/>
        <result column="upcoming_date" property="upcomingDate"/>
        <result column="is_edit" property="isEdit"/>
        <result column="upcoming_status" property="upcomingStatus"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectWarn" resultType="cn.casair.dto.HrUpcomingDTO">
            SELECT * FROM hr_upcoming WHERE date(upcoming_date) = curdate() and is_warn = 1 and user_id = #{userId} and upcoming_status not in (3)  AND is_delete = 0
            and  warn_time > #{nowTime}  and warn_time &lt;= #{localTime}
    </select>

    <select id="selectUserId" resultType="cn.casair.dto.HrUpcomingDTO">
            SELECT
                huc.user_id,
                hc.user_id as client_user_id
            FROM
                hr_talent_staff hts
                    LEFT JOIN hr_client hc ON hts.client_id = hc.id
                    LEFT JOIN hr_user_client huc ON huc.client_id = hts.client_id
                    AND huc.is_delete = 0
                    AND huc.is_specialized = 1
            where hts.id = #{staffId}
            LIMIT 1
    </select>
    <select id="selectUserIdByClientId" resultType="cn.casair.dto.HrUpcomingDTO">
        SELECT
            huc.user_id,
            hc.user_id as client_user_id
        FROM hr_client hc
        LEFT JOIN hr_user_client huc ON huc.client_id = hc.id AND huc.is_delete = 0 AND huc.is_specialized = 1
        where hc.id = #{clientId}
        LIMIT 1
    </select>
</mapper>
