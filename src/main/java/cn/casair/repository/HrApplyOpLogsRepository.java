package cn.casair.repository;
import cn.casair.domain.HrApplyOpLogs;
import cn.casair.dto.HrApplyOpLogsDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 申请操作日志数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Repository
public interface HrApplyOpLogsRepository extends BaseMapper<HrApplyOpLogs> {

    /**
     * 申请员工的操作日志
     * @param applyId 申请ID
     * @param applyStaffId 待入职员工ID
     * @return
     */
    List<HrApplyOpLogsDTO> findApplyOpLogsList(@Param("applyId") String applyId, @Param("applyStaffId") String applyStaffId);

    /**
     * 根据业务id查询审批记录
     * @param id
     * @return
     */
    List<HrApplyOpLogsDTO> getByApplyId(@Param("applyId") String id);

    /**
     * 查看审核人信息
     * @param ids 申请ID
     * @param roleKey 角色
     * @param message 审核信息
     * @return
     */
    List<HrApplyOpLogsDTO> reviewerInfo(@Param("ids") List<String> ids, @Param("roleKey") String roleKey, @Param("message") String message);

}
