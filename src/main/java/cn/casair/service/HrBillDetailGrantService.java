package cn.casair.service;

import cn.casair.domain.HrBillDetailGrant;
import cn.casair.dto.HrBillDetailGrantDTO;
import cn.casair.dto.HrDataDetailDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * 工资发放状态记录服务类
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
public interface HrBillDetailGrantService extends IService<HrBillDetailGrant> {


    /**
     * 创建工资发放状态记录
     *
     * @param hrBillDetailGrantDTO
     * @return
     */
    HrBillDetailGrantDTO createHrBillDetailGrant(HrBillDetailGrantDTO hrBillDetailGrantDTO);

    /**
     * 修改工资发放状态记录
     *
     * @param hrBillDetailGrantDTO
     * @return
     */
    Optional<HrBillDetailGrantDTO> updateHrBillDetailGrant(HrBillDetailGrantDTO hrBillDetailGrantDTO);

    /**
     * 查询工资发放状态记录详情
     *
     * @param id
     * @return
     */
    HrBillDetailGrantDTO getHrBillDetailGrant(String id);

    /**
     * 删除工资发放状态记录
     *
     * @param id
     */
    void deleteHrBillDetailGrant(String id);

    /**
     * 批量删除工资发放状态记录
     *
     * @param ids
     */
    void deleteHrBillDetailGrant(List<String> ids);

    /**
     * 分页查询工资发放状态记录
     *
     * @param hrBillDetailGrantDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillDetailGrantDTO hrBillDetailGrantDTO, Long pageNumber, Long pageSize);


    /**
     * 查询工资发放状态记录
     *
     * @param billDetailId
     * @return
     */
    List<HrBillDetailGrantDTO> findListByBillDetailId(String billDetailId);

    /**
     * 导出工资发放记录
     *
     *  @param hrBillDetailGrantDTO
     * @return
     */
    String exportDataList(HrBillDetailGrantDTO hrBillDetailGrantDTO);

    /**
     * 批量下载附件
     *
     *  @param hrBillDetailGrantDTO
     * @return
     */
    String downloadAppendix(HrBillDetailGrantDTO hrBillDetailGrantDTO);
}
