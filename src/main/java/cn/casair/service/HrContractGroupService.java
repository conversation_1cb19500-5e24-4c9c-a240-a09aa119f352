package cn.casair.service;
import cn.casair.domain.HrCertificate;
import cn.casair.domain.HrContractGroup;
import cn.casair.dto.HrContractGroupDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 合同组服务类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface HrContractGroupService extends IService<HrContractGroup> {


    /**
     * 创建合同组
     * @param hrContractGroupDTO
     * @return
     */
    HrContractGroupDTO createHrContractGroup(HrContractGroupDTO hrContractGroupDTO);

    /**
     * 修改合同组
     * @param hrContractGroupDTO
     * @return
     */
    Optional<HrContractGroupDTO> updateHrContractGroup(HrContractGroupDTO hrContractGroupDTO);

    /**
     * 查询合同组详情
     * @param id
     * @return
     */
    HrContractGroupDTO getHrContractGroup(String id);

    /**
     * 删除合同组
     * @param id
     */
    void deleteHrContractGroup(String id);

    /**
     * 批量删除合同组
     * @param ids
     */
    void deleteHrContractGroup(List<String> ids);

    /**
     * 分页查询合同组
     * @param hrContractGroupDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrContractGroupDTO hrContractGroupDTO,Long pageNumber,Long pageSize);

    List<HrCertificate> getHrCertificate();

    List<HrContractGroupDTO> getHrCertificateSelectSum();
}
