package cn.casair.service;
import cn.casair.domain.HrExamResult;
import cn.casair.domain.HrRecruitmentBrochure;
import cn.casair.dto.HrExamResultDTO;
import cn.casair.dto.HrPaperManagementDTO;
import cn.casair.dto.HrQuestionDTO;
import cn.casair.dto.WxExamResultDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 考试结果表服务类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface HrExamResultService extends IService<HrExamResult> {


    /**
     * 创建考试结果表
     * @param hrExamResultDTO
     * @return
     */
    HrExamResultDTO createHrExamResult(HrExamResultDTO hrExamResultDTO);

    /**
     * 修改考试结果表
     * @param hrExamResultDTO
     * @return
     */
    Optional<HrExamResultDTO> updateHrExamResult(HrExamResultDTO hrExamResultDTO);

    /**
     * 查询考试结果表详情
     * @param id
     * @return
     */
    HrExamResultDTO getHrExamResult(String id);

    /**
     * 删除考试结果表
     * @param id
     */
    void deleteHrExamResult(String id);

    /**
     * 批量删除考试结果表
     * @param ids
     */
    void deleteHrExamResult(List<String> ids);

    /**
     * 分页查询考试结果表
     * @param hrExamResultDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrExamResultDTO hrExamResultDTO, Long pageNumber, Long pageSize);

    IPage<HrPaperManagementDTO> findExamPage(HrPaperManagementDTO hrPaperManagementDTO, Long pageNumber, Long pageSize);

    /**
     * 获取所有试卷名称
     * @return
     */
    HashMap<String, String> getPager();

    /**
     * 考试结果导入
     * @param file

     * @param paperId
     * @param professionName
     * @param examName
     * @return
     */
    String importExamResult(MultipartFile file, String paperId,String professionName,String examName);

    String importExamResultTemplate(HttpServletResponse response);

    /**
     * 考试结果详情
     * @param hrExamResultDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrQuestionDTO> examResultDetail(HrExamResultDTO hrExamResultDTO, Long pageNumber, Long pageSize);

    /**
     *  考试结果导出
     * @param ids
     * @param httpServletResponse
     * @return
     */
    String exportExamResults(List<String> ids, HttpServletResponse httpServletResponse);

    /**
     * 微信员工入职考试提交结果
     * @param wxExamResultDTOList
     */
    HrExamResult wxExamResult(List<WxExamResultDTO> wxExamResultDTOList);

    /**
     * 微信员工入职考试提交结果
     */
    HrExamResult hrExamGetResult();

    /**
     * 获取应试经历
     * @return
     */
    List<HrExamResultDTO> examResultExperience(String staffId);

    /**
     * 简章成绩导入模板
     * @param response
     */
    String importProfileGradesTemplate(HttpServletResponse response);

    /**
     * 简章成绩导入
     * @param file
     * @param examName
     * @return
     */
    String importProfileGrades(MultipartFile file, String examName);

    /**
     * 更新考试结果表
     * @param hrExamResultDTO
     */
    void updateBatch(List<HrExamResult> hrExamResultDTO);

    /**
     * 修改报名人员状态
     * @param hrRecruitmentBrochure 简章
     * @param professionName 岗位
     * @param achievementType 成绩类型
     */
    void updateApplicantStatus(HrRecruitmentBrochure hrRecruitmentBrochure, String professionName, Integer achievementType);

}
