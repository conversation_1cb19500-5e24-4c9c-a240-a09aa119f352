<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrProofTemplateRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, title, appendix_id, use_num,template_type,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrProofTemplate">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="template_type" property="templateType"/>
        <result column="appendix_id" property="appendixId"/>
        <result column="use_num" property="useNum"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Query_SQL">
        SELECT
            hpt.*,
            ha.origin_name fileName,
            ha.file_url fileUrl
        FROM
            hr_proof_template hpt
        LEFT JOIN hr_appendix ha ON hpt.appendix_id = ha.id
        WHERE hpt.is_delete = 0
        <if test="param.title != null and param.title != ''">
            AND hpt.title LIKE CONCAT('%',#{param.title},'%')
        </if>
        <if test="param.templateType != null">
            AND hpt.template_type = #{param.templateType}
        </if>
        <if test="param.templateTypeList!=null and param.templateTypeList.size() > 0">
            AND hpt.template_type IN
            <foreach collection="param.templateTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.ids!=null and param.ids.size() > 0">
            AND hpt.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <choose>
            <when test="param.field != null and param.field != ''">
                ORDER BY #{param.field} #{param.order}
            </when>
            <otherwise>
                ORDER BY hpt.last_modified_date DESC
            </otherwise>
        </choose>
    </sql>

    <select id="findPage" resultType="cn.casair.dto.HrProofTemplateDTO">
        <include refid="Query_SQL"/>
    </select>

    <select id="findList" resultType="cn.casair.dto.HrProofTemplateDTO">
        <include refid="Query_SQL"/>
    </select>

    <select id="findById" resultType="cn.casair.dto.HrProofTemplateDTO">
        SELECT
            hpt.*,
            ha.origin_name fileName,
            ha.file_url fileUrl
        FROM
            hr_proof_template hpt
        LEFT JOIN hr_appendix ha ON hpt.appendix_id = ha.id
        WHERE hpt.is_delete = 0 AND hpt.id = #{id}
    </select>

    <update id="updateUseNum">
        UPDATE hr_proof_template SET use_num = #{useNum} WHERE id = #{id}
    </update>
</mapper>
