package cn.casair.repository;

import cn.casair.domain.HrProofTemplate;
import cn.casair.dto.HrProofTemplateDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 证明模板数据库操作类
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Repository
public interface HrProofTemplateRepository extends BaseMapper<HrProofTemplate> {

    IPage<HrProofTemplateDTO> findPage(Page<HrProofTemplate> page, @Param("param") HrProofTemplateDTO hrProofTemplateDTO);

    List<HrProofTemplateDTO> findList(@Param("param") HrProofTemplateDTO hrProofTemplateDTO);

    HrProofTemplateDTO findById(@Param("id") String id);

    void updateUseNum(@Param("useNum") Integer useNum, @Param("id") String id);

}
