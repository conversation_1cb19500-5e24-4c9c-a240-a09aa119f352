package cn.casair.repository;
import cn.casair.domain.HrSmsTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Repository
public interface HrSmsTemplateRepository extends BaseMapper<HrSmsTemplate> {

    /**
     * 查询短信模板
     *
     * @param hrSmsTemplate
     * @return
     */
    HrSmsTemplate selectByObject(@Param("params") HrSmsTemplate hrSmsTemplate);
}
