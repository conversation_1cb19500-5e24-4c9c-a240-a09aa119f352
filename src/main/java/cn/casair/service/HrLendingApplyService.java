package cn.casair.service;

import cn.casair.domain.HrArchivesDetail;
import cn.casair.domain.HrLendingApply;
import cn.casair.domain.User;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrArchivesDetailDTO;
import cn.casair.dto.HrLendingApplyDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

/**
 * 借阅申请表服务类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
public interface HrLendingApplyService extends IService<HrLendingApply> {


    /**
     * 创建借阅申请表
     *
     * @param hrLendingApplyDTO
     * @param requestFromWx 是否是微信小程序的请求
     * @return
     */
    HrLendingApplyDTO createHrLendingApply(HrLendingApplyDTO hrLendingApplyDTO, Boolean requestFromWx);

    /**
     * 修改借阅申请表
     *
     * @param hrLendingApplyDTO
     * @return
     */
    Optional<HrLendingApplyDTO> updateHrLendingApply(HrLendingApplyDTO hrLendingApplyDTO);

    /**
     * 查询借阅申请表详情
     *
     * @param id
     * @return
     */
    HrLendingApplyDTO getHrLendingApply(String id);

    /**
     * 删除借阅申请表
     *
     * @param id
     */
    void deleteHrLendingApply(String id);

    /**
     * 批量删除借阅申请表
     *
     * @param ids
     */
    void deleteHrLendingApply(List<String> ids);

    /**
     * 分页查询借阅申请表
     *
     * @param hrLendingApplyDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrLendingApplyDTO hrLendingApplyDTO, Long pageNumber, Long pageSize);

    /**
     * 导出
     *
     * @param hrLendingApplyDTO
     * @return
     */
    String export(HrLendingApplyDTO hrLendingApplyDTO);

    IPage<HrLendingApplyDTO> findWxPage(Long pageNumber, Long pageSize);

    ResponseEntity auditorReviewLendingApply(BatchOptDTO batchOptDTO);

    ResponseEntity customerReviewLendingApply(BatchOptDTO batchOptDTO);

    ResponseEntity managerReviewLendingApply(BatchOptDTO batchOptDTO);

    ResponseEntity archivesManagerReviewLendingApply(BatchOptDTO batchOptDTO);

    ResponseEntity pickUpLendingApply(BatchOptDTO batchOptDTO);

    List<HrLendingApplyDTO> getLendingApply();

    ResponseEntity batchPassLendingApply(BatchOptDTO batchOptDTO);

    List<HrLendingApplyDTO> findWxNoPage();

    User getAuditorInfo(String id);

    List<HrArchivesDetailDTO> getBorrowingMaterials(String id);

    /**
     * 查询员工对应的档案明细
     * @param staffId 员工ID
     * @return
     */
    List<HrArchivesDetail> findDetailStaff(String staffId);

}
