package cn.casair.service;

import cn.casair.domain.HrStaffSignCert;
import cn.casair.dto.HrStaffSignCertDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 入职员工签名证书（易云章）服务类
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
public interface HrStaffSignCertService extends IService<HrStaffSignCert> {

    /**
     * 检查并创建员工易云章证书
     *
     * @param staffId   员工id
     * @param clientId  客户id
     * @param staffName 姓名
     * @param idNo      身份证号
     * @param phone     手机号
     * @return void
     * <AUTHOR>
     * @date 2021/9/22
     **/
    void checkAndGetStaffSignCert(String staffId, String clientId, String staffName, String idNo, String phone);

    /**
     * 创建入职员工签名证书（易云章）
     *
     * @param hrStaffSignCertDTO
     * @return
     */
    HrStaffSignCertDTO createHrStaffSignCert(HrStaffSignCertDTO hrStaffSignCertDTO);

    /**
     * 修改入职员工签名证书（易云章）
     *
     * @param hrStaffSignCertDTO
     * @return
     */
    Optional<HrStaffSignCertDTO> updateHrStaffSignCert(HrStaffSignCertDTO hrStaffSignCertDTO);

    /**
     * 查询入职员工签名证书（易云章）详情
     *
     * @param id
     * @return
     */
    HrStaffSignCertDTO getHrStaffSignCert(String id);

    /**
     * 删除入职员工签名证书（易云章）
     *
     * @param id
     */
    void deleteHrStaffSignCert(String id);

    /**
     * 批量删除入职员工签名证书（易云章）
     *
     * @param ids
     */
    void deleteHrStaffSignCert(List<String> ids);

    /**
     * 分页查询入职员工签名证书（易云章）
     *
     * @param hrStaffSignCertDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrStaffSignCertDTO hrStaffSignCertDTO, Long pageNumber, Long pageSize);

    /**
     * 更新员工证书主体名称
     *
     * @param idNo            旧身份证号码
     * @param name            新姓名
     * @param phone           旧手机号码
     * @param hrStaffSignCert 旧证书数据
     * @return void
     * <AUTHOR>
     * @date 2021/9/28
     **/
    void updateCertUserName(String idNo, String name, String phone, HrStaffSignCert hrStaffSignCert);

    /**
     * 更新证书主体手机号
     *
     * @param idNo            旧身份证号码
     * @param name            旧姓名
     * @param phone           新手机号
     * @param hrStaffSignCert 旧证书数据
     * @return void
     * <AUTHOR>
     * @date 2021/9/28
     **/
    void updateCertUserPhone(String idNo, String name, String phone, HrStaffSignCert hrStaffSignCert);

    /**
     * 根据员工id获取易云章证书信息
     *
     * @param staffId
     * @return
     */
    HrStaffSignCert getByStaffId(String staffId);
}
