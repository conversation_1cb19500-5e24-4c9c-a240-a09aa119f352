package cn.casair.repository;

import cn.casair.domain.HrBill;
import cn.casair.dto.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 账单数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Repository
public interface HrBillRepository extends BaseMapper<HrBill> {

    /**
     * 获取账单
     *
     * @param hrBillDTO
     * @return
     */
    List<HrBillDTO> getBillByObject(@Param("params") HrBillDTO hrBillDTO);

    /**
     * 更新账单审核状态
     *
     * @param billIdList
     * @param reviewState
     * @return int
     * <AUTHOR>
     * @date 2021/12/16
     **/
    int updateBillReviewState(@Param("billIdList") List<String> billIdList, @Param("reviewState") Integer reviewState);

    /**
     * 更新账单为已导出状态
     *
     * @param ids
     * @param exportState
     * @return int
     * <AUTHOR>
     * @date 2021/11/9
     **/
    int updateExportState(@Param("ids") List<String> ids, @Param("exportState") Integer exportState);

    /**
     * 获取正常薪金最近有效的缴费年月
     *
     * @return cn.casair.dto.HrBilNormalDTO
     * <AUTHOR>
     * @date 2021/11/9
     **/
    HrBilNormalDTO getNormalSalaryEffectivePaymentDate();

    /**
     * 分页查询账单
     *
     * @param page
     * @param hrBillDTO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrBillDTO>
     * <AUTHOR>
     * @date 2021/11/6
     **/
    IPage<HrBillDTO> findPage(Page<HrBill> page, @Param("params") HrBillDTO hrBillDTO);

    /**
     * 分页查询正常薪金账单
     *
     * @param page
     * @param hrBillDTO
     * @param clientIds
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2021/11/3
     **/
    IPage<HrBilNormalDTO> findNormalSalaryPage(Page<HrBilNormalDTO> page, @Param("params") HrBillDTO hrBillDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 根据缴费年月查询账单
     *
     * @param payYear    缴费年
     * @param payMonthly 缴费月
     * @return cn.casair.domain.HrBill
     * <AUTHOR>
     * @date 2021/10/29
     **/
    HrBill getBillByPaymentDate(@Param("clientId") String clientId, @Param("payYear") Integer payYear, @Param("payMonthly") Integer payMonthly);

    /**
     * 获取年月账单
     *
     * @param hrBillDTO
     * @return int
     * <AUTHOR>
     * @date 2021/10/26
     **/
    int getBillCountByPaymentDate(HrBillDTO hrBillDTO);

    /**
     * 获取客户的计算相关的参数
     *
     * @param clientId
     * @return
     */
    HrBillDTO getBillParamsByClientId(@Param("clientId") String clientId);

    /**
     * 获取账单信息
     *
     * @param id
     * @return
     */
    HrBillDTO getBillInfoById(@Param("id") String id);

//    IPage<HrBillDTO> getBillPageByClient(Page<HrBill> page, @Param("clientId") String clientId);

    /**
     * 不分页查询账单
     * @param hrBillDTO
     * @return
     */
    List<HrBillDTO> findList(@Param("params") HrBillDTO hrBillDTO);

    /**
     * 根据缴费年月查询所有的保障账单
     *
     * @param payYear
     * @param payMonthly
     * @param billType
     * @param clientIds
     * @return
     */
    List<String> getSecurityBill(@Param("payYear") Integer payYear, @Param("payMonthly") Integer payMonthly,
                                 @Param("billType") Integer billType, @Param("clientIds") List<String> clientIds);

    /**
     * 删除批量对账生成的账单
     * @param hrBill
     */
    void deleteBatchBill(@Param("param") HrBill hrBill);

    /**
     * 更新账单锁定状态
     * @param ids 账单ID
     * @param billState 锁定状态
     */
    void updateBillState(@Param("billIdList") List<String> ids, @Param("billState") Integer billState);

    /**
     * 工资发放分页列表
     * @param page
     * @param hrBillDTO
     * @param clientIds
     * @return
     */
    IPage<HrSalaryPaymentDTO> findSalaryPaymentPage(Page<HrSalaryPaymentDTO> page, @Param("params") HrBillDTO hrBillDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 更新账单为已导出状态
     * @param ids
     * @param salaryExportState
     */
    void updateSalaryExportState(@Param("ids") List<String> ids, @Param("salaryExportState") Integer salaryExportState);

    /**
     * 工资发放不分页列表
     * @param hrBillDTO
     * @param clientIds
     * @return
     */
    List<HrSalaryPaymentDTO> findSalaryPaymentList(@Param("params") HrBillDTO hrBillDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 全年一次性奖金分页列表
     * @param page
     * @param hrBillDTO
     * @param clientIds
     * @return
     */
    IPage<HrAnnualBonusDTO> findAnnualLumpSumBonusPage(Page<HrAnnualBonusDTO> page, @Param("params") HrBillDTO hrBillDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 全年一次性奖金不分页查询
     * @param hrBillDTO
     * @param clientIds
     * @return
     */
    List<HrAnnualBonusDTO> findAnnualLumpSumBonusList(@Param("params") HrBillDTO hrBillDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 查询账单选择是创建的账单
     * @param billIdList
     * @return
     */
    HrBillDTO getBillInfoByIdList(@Param("billIdList") List<String> billIdList, @Param("isChoice") Integer isChoice);

    /**
     * 删除草稿账单
     * @param billIds
     */
    void deleteByBillIds(@Param("billIds")List<String> billIds);

    /**
     * 更新全年一次性奖金为导出
     * @param ids
     * @param bonusExportState
     */
    void updateBonusState(@Param("ids") List<String> ids, @Param("bonusExportState") Integer bonusExportState);

    /**
     * 费用审核查询账单
     * @param billDTO
     * @return
     */
    List<HrBillDTO> findBillList(@Param("params")HrBillDTO billDTO);

    /**
     * 修改状态
     * @param billIdList
     * @param salaryPaymentState
     * @return
     */
    int updateSalaryPaymentState(@Param("billIdList") List<String> billIdList, @Param("salaryPaymentState") Integer salaryPaymentState);

    /**
     * 修改锁定以及审核状态
     * @param billIdList 账单ID
     * @param reviewState 审核状态
     * @param billState 锁定状态
     * @return
     */
    int updateReviewStateAndBillState(@Param("billIdList") List<String> billIdList, @Param("reviewState") Integer reviewState, @Param("billState") Integer billState);

    /**
     * 查询账单
     * @param hrFeeReviewDTO
     * @return
     */
    List<HrBillDTO> queryBillInfo(@Param("params") HrFeeReviewDTO hrFeeReviewDTO);

    /**
     * 批量获取账单信息
     * @param billIdList
     * @return
     */
    List<HrBillDTO> getBillInfoBatch(@Param("ids") List<String> billIdList);

    /**
     * 修改其他账单标识
     * @param otherBillFlag 其他账单标识 0员工 1客户
     * @param billIdList 账单ID
     */
    void updateOtherBillFlag(@Param("otherBillFlag") Integer otherBillFlag, @Param("billIdList") List<String> billIdList);

    /**
     * 工资未发放的账单收入
     * @param clientIds
     * @return
     */
    List<HrBilNormalDTO> findUnissuedSalaryList(@Param("permissionClient")List<String> clientIds);

    /**
     * 维护工资发放状态
     * @param billId
     * @param grantState
     */
    void updateGrantState(@Param("billId") String billId, @Param("grantState") Integer grantState);

    /**
     * 删除对账结果删除批量对账账单
     * @param billIds
     * @param title
     * @param lastModifiedBy
     */
    void delByCompareResult(@Param("billIds") List<String> billIds, @Param("title") String title, @Param("lastModifiedBy") String lastModifiedBy);


    /**
     * 查询重复薪酬账单IDS
     * @return
     */
    List<String> findRepeatBill();

    /**
     * 根据条件分页查询制作账单的记录
     * @param page
     * @param hrBillDTO
     * @return
     */
    IPage<HrBillDTO> findRecordPage(Page<HrBill> page, @Param("params") HrBillDTO hrBillDTO);

    List<HrBillDTO> findByBillNo(@Param("billNo") String billNo, @Param("clientId")String clientId);

    /**
     * 根据账单编号查询账单列表
     * @param id
     * @return
     */
    @Select("select *from hr_bill b where b.bill_no = #{billNo} and b.is_delete = 0")
    List<HrBillDTO> findListByBillNo(@Param("billNo")String billNo);

    List<HrBillDTO> findSocialConfig(@Param("billIds") List<String> billIds, @Param("billType") Integer billType);

    List<HrBill> selectByIdOrBillNo(@Param("value") String value);

    /**
     * 修改中石化账单合同编号标识
     * @param billId 账单ID
     * @param contractNoFlag 标识
     */
    void updateSinopecContractNo(@Param("billId") String billId, @Param("contractNoFlag") Integer contractNoFlag);

}
