package cn.casair.service.util;

import cn.casair.common.AuthoritiesConstants;
import cn.casair.common.errors.CommonException;
import cn.casair.dto.JWTMiniDTO;
import cn.casair.dto.JWTUserDTO;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

import static cn.casair.common.Constants.ANONYMOUS_USER;

/**
 * Utility class for Spring Security.
 *
 * <AUTHOR>
 */
public final class SecurityUtils {

    private SecurityUtils() {
    }

    /**
     * Get the login of the current user.
     *
     * @return the login of the current user.
     */
    public static Optional<String> getCurrentUserLogin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
                .map(authentication -> {
                    if (authentication.getPrincipal() instanceof JWTUserDTO) {
                        JWTUserDTO springSecurityUser = (JWTUserDTO) authentication.getPrincipal();
                        return springSecurityUser.getMobile();
                    } else if (authentication.getPrincipal() instanceof String) {
                        return (String) authentication.getPrincipal();
                    }
                    return null;
                });
    }

    public static Optional<JWTUserDTO> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return Optional.ofNullable((JWTUserDTO) authentication.getPrincipal());
    }

    public static Optional<JWTMiniDTO> getCurrentMini() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return Optional.ofNullable((JWTMiniDTO) authentication.getPrincipal());
    }


    /**
     * Get the JWT of the current user.
     *
     * @return the JWT of the current user.
     */
    public static Optional<String> getCurrentUserJWT() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
                .filter(authentication -> authentication.getCredentials() instanceof String)
                .map(authentication -> (String) authentication.getCredentials());
    }

    /**
     * Check if a user is authenticated.
     *
     * @return true if the user is authenticated, false otherwise.
     */
    public static boolean isAuthenticated() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
                .map(authentication -> authentication.getAuthorities().stream()
                        .noneMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(AuthoritiesConstants.ANONYMOUS)))
                .orElse(false);
    }

    /**
     * If the current user has a specific authority (security role).
     * <p>
     * The name of this method comes from the {@code isUserInRole()} method in the Servlet API.
     *
     * @param authority the authority to check.
     * @return true if the current user has the authority, false otherwise.
     */
    public static boolean isCurrentUserInRole(String authority) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
                .map(authentication -> authentication.getAuthorities().stream()
                        .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(authority)))
                .orElse(false);
    }

    /**
     * 获取当前用户真实姓名
     *
     * @return java.util.Optional<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/27
     **/
    public static Optional<String> getCurrentRealName() {
        String name;
        try {
            name = SecurityUtils.getCurrentUser().get().getRealName();
        } catch (Exception e) {
            try {
                name = SecurityUtils.getCurrentMini().get().getName();
            } catch (Exception e1) {
                name = ANONYMOUS_USER;
            }
        }
        return Optional.ofNullable(name);
    }

    /**
     * 获取当前用户名
     *
     * @return java.util.Optional<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/27
     **/
    public static Optional<String> getCurrentUserName() {
        String username;
        try {
            username = SecurityUtils.getCurrentUser().get().getUserName();
        } catch (Exception e) {
            try {
                username = SecurityUtils.getCurrentMini().get().getName();
            } catch (Exception e1) {
                username = ANONYMOUS_USER;
            }
        }
        return Optional.ofNullable(username);
    }

    /**
     * 获取用户信息,未获取到时抛出异常
     *
     * @return
     */
    public static JWTUserDTO getJwtUser() {
        return getCurrentUser().orElseThrow(() -> new CommonException("获取用户信息异常!"));
    }
}
