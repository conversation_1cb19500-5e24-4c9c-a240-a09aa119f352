package cn.casair.repository;

import cn.casair.domain.HrApplyDepartureStaff;
import cn.casair.dto.HrApplyDepartureStaffDTO;
import cn.casair.dto.excel.HrApplyDepartureStaffExport;
import cn.casair.dto.excel.HrApplyDepartureStaffTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 待离职员工数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
@Repository
public interface HrApplyDepartureStaffRepository extends BaseMapper<HrApplyDepartureStaff> {

    /**
     * 获取员工离职信息
     *
     * @param clientId
     * @param staffId
     * @return cn.casair.dto.HrApplyDepartureStaffDTO
     * <AUTHOR>
     * @date 2021/10/26
     **/
    HrApplyDepartureStaffDTO selectByClientIdAndStaffId(@Param("clientId") String clientId, @Param("staffId") String staffId);

    /**
     * 待离职员工列表
     * @param page 分页
     * @param hrApplyDepartureStaffDTO 查询参数
     * @return 返回结果
     */
    IPage<HrApplyDepartureStaffDTO> findPage(Page<HrApplyDepartureStaff> page, @Param("param") HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO);

    /**
     * 查看待离职员工
     * @param id 待离职员工ID
     * @return 返回结果
     */
    HrApplyDepartureStaffDTO findById(@Param("id") String id);

    /**
     * 查询待离职员工信息
     * @param departureId 离职服务ID
     * @param departureApplyStatus 待离职员工审核结果
     * @param departureStaffIds 待离职员工ID
     * @return 待离职员工信息
     */
    List<HrApplyDepartureStaffDTO> viewResignationInFor(@Param("departureId") String departureId, @Param("departureApplyStatus") Integer departureApplyStatus, @Param("departureStaffIds") List<String> departureStaffIds);

    /**
     * 首页：统计每月离职职数量
     *
     * @param particularYear
     * @return
     */
    List<Map<String, Object>> resignedCountByYear(String particularYear);

    /**
     * 查询待离职员工信息
     * @param departureId 离职服务ID
     * @param departureStaffIds 待离职员工ID集合
     * @param status
     * @return
     */
    List<HrApplyDepartureStaffExport> getHrApplyDepartureStaffStaffList(@Param("departureId") String departureId, @Param("departureStaffIds") List<String> departureStaffIds, @Param("status") Integer status);

    /**
     * 不分页查询待离职员工信息
     * @param hrApplyDepartureStaffDTO
     * @return
     */
    List<HrApplyDepartureStaffExport> findList(@Param("param") HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO);

}
