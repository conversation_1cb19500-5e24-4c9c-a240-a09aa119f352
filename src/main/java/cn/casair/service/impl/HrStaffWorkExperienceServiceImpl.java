package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.ApprovalEntryStatusEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.DateUtils;
import cn.casair.domain.*;
import cn.casair.dto.HrStaffWorkExperienceDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.mapper.HrStaffWorkExperienceMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 工作经历服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class HrStaffWorkExperienceServiceImpl extends ServiceImpl<HrStaffWorkExperienceRepository, HrStaffWorkExperience>implements HrStaffWorkExperienceService {

    private static final Logger log=LoggerFactory.getLogger(HrStaffWorkExperienceServiceImpl.class);
    private final HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;
    private final HrStaffWorkExperienceMapper hrStaffWorkExperienceMapper;
    private final CodeTableService codeTableService;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrStationService hrStationService;
    private final HrApplyEntryStaffRepository hrApplyEntryStaffRepository;
    private final SysOperLogService sysOperLogService;
    private final HrArchivesManageRepository hrArchivesManageRepository;
    private final HrArchivesDetailRepository hrArchivesDetailRepository;
    private final HrArchivesBringRepository hrArchivesBringRepository;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrStaffEmolumentService hrStaffEmolumentService;
    private final HrStaffFieldService hrStaffFieldService;
    private final HrStaffNoteService hrStaffNoteService;
    private final HrContractRepository hrContractRepository;
    private final HrStaffEducationService hrStaffEducationService;
    private final HrStaffInterviewService hrStaffInterviewService;
    private final HrStaffFamilyService hrStaffFamilyService;
    private final HrStaffContactsService hrStaffContactsService;
    private final HrStaffLanguageService hrStaffLanguageService;
    private final HrStaffProfessionService hrStaffProfessionService;
    private final HrStaffQualificationService hrStaffQualificationService;
    private final HrStaffTechniqueService hrStaffTechniqueService;
    private final HrStaffCertificateService hrStaffCertificateService;
    private final HrStaffStationService hrStaffStationService;

    /**
     * 创建工作经历
     * @param hrStaffWorkExperienceDTO
     * @return
     */
    @Override
    public List<HrStaffWorkExperienceDTO> createHrWorkExperience(HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO){
        log.info("Create new HrWorkExperience:{}", hrStaffWorkExperienceDTO);
        //同一时间段内应该只有一条工作经历
        this.dateRangeVerification(hrStaffWorkExperienceDTO);
        HrStaffWorkExperience hrWorkExperience =this.hrStaffWorkExperienceMapper.toEntity(hrStaffWorkExperienceDTO);
        this.hrStaffWorkExperienceRepository.insert(hrWorkExperience);
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(hrWorkExperience.getStaffId());
        if (hrTalentStaff.getIzDefault()){
            //人才列表的工作状态：在职还是待业 根据离职日期判断
            this.updateTalentInfo(hrStaffWorkExperienceDTO,hrTalentStaff);
        }
        // 操作日志
        setDict(hrStaffWorkExperienceDTO);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.WORK_EXPERIENCE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffWorkExperienceDTO),
            HrStaffWorkExperienceDTO.class,
            null,
            JSON.toJSONString(hrWorkExperience)
        );
        //查询全部数据返回
        List<HrStaffWorkExperienceDTO> workExperienceDTOList = this.findWorkExperienceList(hrWorkExperience.getStaffId(),hrWorkExperience.getIzDefault());
        return workExperienceDTOList;
    }

    /**
     * 同一时间段内应该只有一条工作经历
     * @param hrStaffWorkExperienceDTO
     */
    private void dateRangeVerification(HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO) {
        //获取员工最新的一条工作经历
        List<HrStaffWorkExperience> workExperienceList = hrStaffWorkExperienceRepository.selectList(new QueryWrapper<HrStaffWorkExperience>()
            .eq("staff_id", hrStaffWorkExperienceDTO.getStaffId()).eq("iz_default",0).ne(StringUtils.isNotBlank(hrStaffWorkExperienceDTO.getId()),"id",hrStaffWorkExperienceDTO.getId()));
        if (CollectionUtils.isNotEmpty(workExperienceList)){
            for (HrStaffWorkExperience hrStaffWorkExperience : workExperienceList) {
                if (hrStaffWorkExperienceDTO.getDepartureDate()!=null){
                    if (DateUtils.isWithin(hrStaffWorkExperience.getBoardDate(),hrStaffWorkExperience.getDepartureDate(),hrStaffWorkExperienceDTO.getBoardDate(),hrStaffWorkExperienceDTO.getDepartureDate())){
                        throw new CommonException("同一时间段内应该只有一条工作经历");
                    }
                }
            }
        }
    }

    /**
     * 更新人才工作状态
     * @param hrStaffWorkExperienceDTO
     */
    private void updateTalentInfo(HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO,HrTalentStaff hrTalentStaff) {
        //判断离职日期是否填入，没有填入就是在职
        if (hrStaffWorkExperienceDTO.getDepartureDate()==null){
            hrTalentStaff.setWorkStatus(1);
            hrTalentStaffRepository.updateById(hrTalentStaff);
        }else {
            if (hrStaffWorkExperienceDTO.getDepartureDate().isAfter(LocalDate.now())){
                hrTalentStaff.setWorkStatus(1);
                hrTalentStaffRepository.updateById(hrTalentStaff);
            }
        }
    }

    /**
     * 修改工作经历
     * @param hrStaffWorkExperienceDTO
     * @return
     */
    @Override
    public Optional<List<HrStaffWorkExperienceDTO>> updateHrWorkExperience(HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO){
        return Optional.ofNullable(this.hrStaffWorkExperienceRepository.selectById(hrStaffWorkExperienceDTO.getId()))
        .map(roleTemp->{
            //判断是否是入职公司
            HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(roleTemp.getStaffId());
            //同一时间段内应该只有一条工作经历
            this.dateRangeVerification(hrStaffWorkExperienceDTO);
            if (hrStaffWorkExperienceDTO.getIzDefault()){
                //入职公司某些信息不可更改
                hrStaffWorkExperienceDTO.setEmployerUnit(null).setBoardDate(null).setDepartureDate(null).setContractStartDate(null).setContractEndDate(null);
                //判断是否需要同步更新员工的人员类型
                //h获取员工人员信息
                if (hrTalentStaff != null){
                    if (hrTalentStaff.getPersonnelType() != null ){
                        if (!hrTalentStaff.getPersonnelType().equals(hrStaffWorkExperienceDTO.getPersonnelType())){
                            hrTalentStaff.setPersonnelType(hrStaffWorkExperienceDTO.getPersonnelType());
                        }
                    }else {
                        hrTalentStaff.setPersonnelType(hrStaffWorkExperienceDTO.getPersonnelType());
                    }
                    hrTalentStaffRepository.updateById(hrTalentStaff);
                    HrApplyEntryStaff hrApplyEntryStaff = hrApplyEntryStaffRepository.selectOne(new QueryWrapper<HrApplyEntryStaff>()
                        .eq("id",hrTalentStaff.getApplyStaffId()).le("entry_status", ApprovalEntryStatusEnum.EntryStatus.SEND_CONTRACT.getKey()));
                    if (hrApplyEntryStaff != null){
                        hrApplyEntryStaff.setPersonnelType(hrStaffWorkExperienceDTO.getPersonnelType());
                        hrApplyEntryStaff.setStationId(hrStaffWorkExperienceDTO.getStationId());
                        hrApplyEntryStaffRepository.updateById(hrApplyEntryStaff);
                    }
                }
            }else {
                if (hrTalentStaff.getIzDefault()){
                    //人才列表的工作状态：在职还是待业 根据离职日期判断
                    this.updateTalentInfo(hrStaffWorkExperienceDTO,hrTalentStaff);
                }
            }

            HrStaffWorkExperience hrWorkExperience =this.hrStaffWorkExperienceMapper.toEntity(hrStaffWorkExperienceDTO);
            this.hrStaffWorkExperienceRepository.updateById(hrWorkExperience);
            log.info("Update HrWorkExperience:{}", hrStaffWorkExperienceDTO);
            // 操作日志
            setDict(hrStaffWorkExperienceDTO);
            HrStaffWorkExperienceDTO dto = hrStaffWorkExperienceMapper.toDto(roleTemp);
            setDict(dto);
            this.sysOperLogService.insertSysOperLog(
                hrStaffWorkExperienceDTO.getIzDefault() ? ModuleTypeEnum.ON_THE_JOB_INFO.getValue() : ModuleTypeEnum.WORK_EXPERIENCE.getValue(),
                BusinessTypeEnum.UPDATE.getKey(),
                JSON.toJSONString(hrStaffWorkExperienceDTO),
                HrStaffWorkExperienceDTO.class,
                null,
                JSON.toJSONString(dto),
                JSON.toJSONString(hrStaffWorkExperienceDTO),
                null,
                HrStaffWorkExperienceDTO.class
            );
            return this.findWorkExperienceList(roleTemp.getStaffId(),roleTemp.getIzDefault());
        });
    }

    /**
     * 查询工作经历详情
     * @param id
     * @return
     */
    @Override
    public HrStaffWorkExperienceDTO getHrWorkExperience(String id){
        log.info("Get HrWorkExperience :{}",id);

        HrStaffWorkExperienceDTO experienceDTO = this.hrStaffWorkExperienceRepository.selectWorkExperienceById(id);
        this.setDict(experienceDTO);//赋值字典值
        return experienceDTO;
    }

    /**
     * 删除工作经历
     * @param id
     */
    @Override
    public List<HrStaffWorkExperienceDTO> deleteHrWorkExperience(String id){
        HrStaffWorkExperienceDTO experienceDTO = hrStaffWorkExperienceRepository.selectWorkExperienceById(id);
        this.hrStaffWorkExperienceRepository.deleteById(id);
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.WORK_EXPERIENCE.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除"+ ModuleTypeEnum.WORK_EXPERIENCE.getValue() +": "+ experienceDTO.getEmployerUnit(),
            null,
            null,
            null,
            JSON.toJSONString(experienceDTO),
            null
        );
        return this.findWorkExperienceList(experienceDTO.getStaffId(),experienceDTO.getIzDefault());
    }

    /**
     * 根据员工ID查询工作经历
     * @param staffId 员工ID
     * @param izDefault 标识 0:之前公司 1:入职公司
     *
     * @return
     */
    @Override
    public List<HrStaffWorkExperienceDTO> findWorkExperienceList(String staffId, Boolean izDefault) {
        List<HrStaffWorkExperienceDTO> workExperienceDTOList = this.hrStaffWorkExperienceRepository.findWorkExperienceList(staffId,izDefault);
        if (CollectionUtils.isNotEmpty(workExperienceDTOList)) {
            workExperienceDTOList.forEach(this::setDict);//赋值字典值
        }
        return workExperienceDTOList;
    }

    public void setDict(HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO) {
        if (hrStaffWorkExperienceDTO.getPersonnelType()!=null){
            Map<Integer, String> staffType = codeTableService.findCodeTableByInnerName("staffType");//人员类型
            hrStaffWorkExperienceDTO.setPersonnelTypeLabel(staffType.get(hrStaffWorkExperienceDTO.getPersonnelType()));
        }
        if (hrStaffWorkExperienceDTO.getSalarySection()!=null){
            Map<Integer, String> staffType = codeTableService.findCodeTableByInnerName("monthlyPay");//月薪区间
            hrStaffWorkExperienceDTO.setSalarySectionLabel(staffType.get(hrStaffWorkExperienceDTO.getSalarySection()));
        }
        if (hrStaffWorkExperienceDTO.getWorkNature()!=null){
            Map<Integer, String> workNature = codeTableService.findCodeTableByInnerName("workNature");//工作地性质
            hrStaffWorkExperienceDTO.setWorkNatureLabel(workNature.get(hrStaffWorkExperienceDTO.getWorkNature()));
        }
        if (hrStaffWorkExperienceDTO.getStationId() != null){
            HrStation hrStation = hrStationService.getById(hrStaffWorkExperienceDTO.getStationId());
            if(hrStation != null){
                hrStaffWorkExperienceDTO.setProfessionName(hrStation.getProfessionName());
            }
        }
    }

    @Async
    public void processEmployeeDeletion(JWTUserDTO jwtUserDTO, List<String> ids) {
        long l = System.currentTimeMillis();
        log.info("异步处理员工删除开始：{}",l);
        List<HrTalentStaff> hrTalentStaffs = hrTalentStaffRepository.selectBatchIds(ids);
        //删除薪酬参数
        hrStaffEmolumentService.remove(new QueryWrapper<HrStaffEmolument>().in("staff_id", ids));
        //删除合同信息
        hrContractRepository.delete(new QueryWrapper<HrContract>().in("staff_id", ids));
        for (HrTalentStaff hrTalentStaff : hrTalentStaffs) {
            HrStaffWorkExperience serviceOne = hrStaffWorkExperienceRepository.selectStaffNewstWorkExperience(hrTalentStaff.getId(), hrTalentStaff.getClientId());
            if (serviceOne!=null){
                //查询之前入职的公司名称
                serviceOne.setIzDefault(false).setWorkRemark(jwtUserDTO.getRealName()+"删除了该信息，由此转为人才").setDepartureDate(LocalDate.now())
                    .setStaffStatus(hrTalentStaff.getStaffStatus()).setIzInsured(hrTalentStaff.getIzInsured());
                hrStaffWorkExperienceRepository.updateById(serviceOne);
            }
            //对应的流程直接结束 入职流程
            if (StringUtils.isNotBlank(hrTalentStaff.getApplyStaffId())){
                HrApplyEntryStaff hrApplyEntryStaff = hrApplyEntryStaffRepository.selectOne(new QueryWrapper<HrApplyEntryStaff>().eq("id",hrTalentStaff.getApplyStaffId()).lt("entry_status", ApprovalEntryStatusEnum.EntryStatus.DELETED.getKey()));
                if (hrApplyEntryStaff != null){
                    //流程结束
                    hrApplyEntryStaff.setApplyStep(ApprovalEntryStatusEnum.ApplyStep.APPLY_OVER.getKey()).setEntryStatus(ApprovalEntryStatusEnum.EntryStatus.DELETED.getKey());
                    hrApplyEntryStaffRepository.updateById(hrApplyEntryStaff);
                    //操作明细
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyEntryStaff.getApplyId(),hrApplyEntryStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "删除了[" + hrTalentStaff.getName() + "]的员工信息，入职流程结束",null, ServiceCenterEnum.ENTRY_APPLICATIONS.getKey());
                }
            }
            //查询工作经历 判断是在职还是待业 只要有一个离职日期为空就是在职
            List<HrStaffWorkExperience> hrStaffWorkExperience = hrStaffWorkExperienceRepository.selectList(new QueryWrapper<HrStaffWorkExperience>().eq("staff_id", hrTalentStaff.getId()).eq("iz_default",0).orderByDesc("departure_date"));
            if (CollectionUtils.isNotEmpty(hrStaffWorkExperience)){
                boolean anyMatch = hrStaffWorkExperience.stream().anyMatch(experience -> experience.getDepartureDate()==null );
                if (anyMatch) {
                    hrTalentStaff.setWorkStatus(1);
                }
                LocalDate departureDate = hrStaffWorkExperience.get(0).getDepartureDate();
                if (departureDate.isAfter(LocalDate.now())){
                    hrTalentStaff.setWorkStatus(1);
                }
                hrTalentStaffRepository.updateWorkStatus(hrTalentStaff.getId(),hrTalentStaff.getWorkStatus());
            }
            //删除档案
            List<HrArchivesManage> list = hrArchivesManageRepository.selectList(new QueryWrapper<HrArchivesManage>().eq("staff_id", hrTalentStaff.getId()).eq("client_id", hrTalentStaff.getClientId()));
            if (list != null && !list.isEmpty()){
                List<String> manageIds = list.stream().map(HrArchivesManage::getId).distinct().collect(Collectors.toList());
                //获取对应的明细
                List<HrArchivesDetail> detailList = hrArchivesDetailRepository.selectList(new QueryWrapper<HrArchivesDetail>().in("archives_id", manageIds));
                if (CollectionUtils.isNotEmpty(detailList)){
                    List<String> detailIds = detailList.stream().map(HrArchivesDetail::getId).distinct().collect(Collectors.toList());
                    List<String> bringDetailIds = hrTalentStaffRepository.findArchivesBringDetail(detailIds);
                    if(CollectionUtils.isNotEmpty(bringDetailIds)){
                        hrArchivesBringRepository.deleteBatchIds(bringDetailIds);//删除档案调入调出记录
                    }
                    hrTalentStaffRepository.deleteArchivesBringDetail(detailIds);//删除档案明细变更记录关联
                    hrArchivesDetailRepository.deleteBatchIds(detailIds);//删除档案明细
                }
                hrArchivesManageRepository.deleteBatchIds(manageIds);
            }
        }
        //批量修改员工信息
        this.hrTalentStaffRepository.updateBatchIds(ids,jwtUserDTO.getUserName(), LocalDateTime.now());
        List<String> collect = hrTalentStaffs.stream().map(HrTalentStaff::getSystemNum).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.STAFF.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除员工: "+JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(hrTalentStaffs),
            jwtUserDTO
        );
        log.info("异步处理员工删除结束：{}",System.currentTimeMillis() - l);
    }

    @Override
    @Async
    public void deleteBatchIds(List<String> ids, JWTUserDTO jwtUserDTO) {
        List<HrTalentStaff> hrTalentStaffs = hrTalentStaffRepository.selectBatchIds(ids);
        //删除适配岗位
        hrStaffStationService.remove(new QueryWrapper<HrStaffStation>().in("staff_id", ids));
        //删除其他信息
        hrStaffFieldService.remove(new QueryWrapper<HrStaffField>().in("staff_id", ids));
        //删除员工标签
        hrStaffNoteService.remove(new QueryWrapper<HrStaffNote>().in("staff_id", ids));
        //删除附加信息：工作经历
        hrStaffWorkExperienceRepository.delete(new QueryWrapper<HrStaffWorkExperience>().in("staff_id", ids));
        //删除附加信息：教育经历
        hrStaffEducationService.remove(new QueryWrapper<HrStaffEducation>().in("staff_id", ids));
        //删除附加信息：应试经历
        hrStaffInterviewService.remove(new QueryWrapper<HrStaffInterview>().in("staff_id", ids));
        //删除附加信息：家庭成员
        hrStaffFamilyService.remove(new QueryWrapper<HrStaffFamily>().in("staff_id", ids));
        //删除附加信息：紧急联系人
        hrStaffContactsService.remove(new QueryWrapper<HrStaffContacts>().in("staff_id", ids));
        //删除附加信息：语言能力
        hrStaffLanguageService.remove(new QueryWrapper<HrStaffLanguage>().in("staff_id", ids));
        //删除附加信息：专业技能
        hrStaffProfessionService.remove(new QueryWrapper<HrStaffProfession>().in("staff_id", ids));
        //删除附加信息：职业(工种)资格
        hrStaffQualificationService.remove(new QueryWrapper<HrStaffQualification>().in("staff_id", ids));
        //删除附加信息：职业技术能力
        hrStaffTechniqueService.remove(new QueryWrapper<HrStaffTechnique>().in("staff_id", ids));
        //删除附加信息：证书
        hrStaffCertificateService.remove(new QueryWrapper<HrStaffCertificate>().in("staff_id", ids));
        //删除薪酬参数
        hrStaffEmolumentService.remove(new QueryWrapper<HrStaffEmolument>().in("staff_id", ids));
        List<String> collect = hrTalentStaffs.stream().map(HrTalentStaff::getSystemNum).collect(Collectors.toList());
        hrTalentStaffRepository.deleteTalentStaff(ids);
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.TALENTS.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除人才: "+JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(hrTalentStaffs),
            jwtUserDTO
        );
    }
}
