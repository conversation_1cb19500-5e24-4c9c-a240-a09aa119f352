package cn.casair.service;
import cn.casair.domain.HrDocking;
import cn.casair.dto.HrDockingDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 客户关联对接人表服务类
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
public interface HrDockingService extends IService<HrDocking> {


    /**
     * 创建客户关联对接人表
     * @param hrDockingDTO
     * @return
     */
    HrDockingDTO createHrDocking(HrDockingDTO hrDockingDTO);

    /**
     * 修改客户关联对接人表
     * @param hrDockingDTO
     * @return
     */
    Optional<HrDockingDTO> updateHrDocking(HrDockingDTO hrDockingDTO);

    /**
     * 查询客户关联对接人表详情
     * @param id
     * @return
     */
    HrDockingDTO getHrDocking(String id);

    /**
     * 删除客户关联对接人表
     * @param id
     */
    void deleteHrDocking(String id);

    /**
     * 批量删除客户关联对接人表
     * @param ids
     */
    void deleteHrDocking(List<String> ids);

    /**
     * 分页查询客户关联对接人表
     * @param hrDockingDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrDockingDTO hrDockingDTO,Long pageNumber,Long pageSize);
    }
