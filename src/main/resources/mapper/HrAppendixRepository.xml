<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrAppendixRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, origin_name, file_type, file_path, file_url,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrAppendix">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="origin_name" property="originName"/>
        <result column="file_type" property="fileType"/>
        <result column="file_path" property="filePath"/>
        <result column="file_url" property="fileUrl"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByFileUrl" resultType="cn.casair.domain.HrAppendix">
        SELECT * FROM hr_appendix WHERE file_url = #{fileUrl}
    </select>

    <select id="getHrAppendixListByIds" resultType="cn.casair.dto.HrAppendixDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
             hr_appendix
        WHERE id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        ORDER BY
            created_date
    </select>

    <insert id="saveAppendixUnion">
        INSERT INTO hr_appendix_union (id, union_id, appendix_id)
        VALUES (#{id}, #{unionId}, #{appendixId})
    </insert>

    <select id="getByUnionId" resultType="cn.casair.dto.HrAppendixDTO">
        SELECT * FROM `hr_appendix` ha WHERE ha.`id` IN (SELECT appendix_id FROM hr_appendix_union hau WHERE hau.`union_id` = #{unionId})
    </select>

    <delete id="deleteUnion">
        DELETE FROM hr_appendix_union WHERE appendix_id = #{appendixId}
    </delete>
</mapper>
