package cn.casair.repository;

import cn.casair.domain.HrApplyDeparture;
import cn.casair.dto.HrApplyDepartureDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 离职服务数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
@Repository
public interface HrApplyDepartureRepository extends BaseMapper<HrApplyDeparture> {

    /**
     * 离职服务列表
     * @param page
     * @param hrApplyDepartureDTO
     * @return
     */
    IPage<HrApplyDepartureDTO> findPage(Page<HrApplyDeparture> page, @Param("param") HrApplyDepartureDTO hrApplyDepartureDTO);

    /**
     * 查看离职服务
     * @param id
     * @return
     */
    HrApplyDepartureDTO findById(@Param("id") String id);
}
