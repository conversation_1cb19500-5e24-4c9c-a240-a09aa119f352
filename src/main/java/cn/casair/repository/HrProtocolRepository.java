package cn.casair.repository;

import cn.casair.domain.HrClient;
import cn.casair.domain.HrDocking;
import cn.casair.domain.HrProtocol;
import cn.casair.dto.HrProtocolDTO;
import cn.casair.dto.HrRemindConfDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据库操作类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Repository
public interface HrProtocolRepository extends BaseMapper<HrProtocol> {

    /**
     * 查询客户当前使用的协议
     * <p>创建账单客户多协议校验</p>
     *
     * @param clientId
     * @return java.util.List<cn.casair.domain.HrProtocol>
     * <AUTHOR>
     * @date 2022/1/19
     **/
    List<HrProtocol> selectUseingProtocolByClientId(@Param("clientId") String clientId);

    void updateClient(@Param("param") HrProtocolDTO hrProtocolDTO);

    String selectUserid(String clientId);


    void updateUser(String userId);

    void updateProtocols(HrProtocol hrProtocols);


    IPage<HrProtocolDTO> selectPageProtocol(Page<HrProtocol> page,@Param("param1") HrProtocolDTO hrProtocolDTO);


    List<HrProtocolDTO> selectappendixId(String id);

    HrProtocol selectnumber(String unitNumber);

    /**
     * 修改员工登录状态为禁用
     * @param id 协议ID
     */
    void updateTalentStaff(String id);

    List<HrProtocolDTO> selectstatrsum(String clientId);

    List<HrDocking> selectHrDocking(String id);

    HrProtocol selectClientId(String id);

    void deleteClient(String id);

    void updateAppendixId(@Param("param1")HrProtocolDTO hrProtocolDTO);


    String selecrClientName(String agreementOwnedCustomer);

    List<String> selectAgreementNumber(String agreementNumber);

    String getSelectProtocolId(String clientId);

    /**
     * 根据协议编号更改协议状态
     * @param hrProtocols
     */
    void updateProtocolstates(HrProtocol hrProtocols);
    void updateProtocolstatess(HrProtocol hrProtocols);

    Integer selecttime(String customer_agreement);
    HrRemindConfDTO selecttimes(String customer_agreement);
    void updateRemark(HrProtocolDTO hrProtocolDTO);


    /**
     * 查询 [指定客户id] 的 所有父级客户List (包含指定客户)
     *
     *  <p>不建议直接使用!!!!!!   不建议使用</p>
     *  <p>,系统内客户层级可以整体移动 sql 并不是总能满足预期功能</p>
     * @param id 客户id
     * @return 所有父级客户List 【按照层级深度依次排序,  父级排在子集后面】
     */
    @Deprecated
    List<HrClient> selectAllParentClientById(String id);

    /**
     * 根据客户ID修改协议使用状为使用结束
     * @param clientId 客户ID
     */
    void updateProtocolsStatus(String clientId);


    List<HrProtocolDTO> selecthrProtocolsum(String agreementNumber);

    /**
     * 使用协议
     * @param id 协议ID
     */
    void updateProtocolRenewType(String id);

    /**
     * 修改老协议是否续签状态
     * @param protocolId 续签协议ID（过期协议ID）
     */
    void upDateProtocol(String protocolId);

    void updateUserStatus(String userId);

    /**
     * 修改员工协议以及登录状态启用
     * @param pid 协议ID
     * @param cid 客户ID
     * @param staffStatus 离职状态
     */
    void updateStall(@Param("pid")String pid, @Param("cid")String cid, @Param("staffStatus") Integer staffStatus);


    void updateDelect(@Param("agreementNumber")String agreementNumber, @Param("clientId")String clientId);

    @Deprecated
    void updateus(String clientId);

    List<HrProtocolDTO> selectListClentId(@Param("clientIdLista")List<String> clientIdLista);

//    void updateProtocolss(@Param("id")String id, @Param("clientId")String clientId);

    HrProtocolDTO getClientProtocolList(@Param("clientId")String clientId);

    /**
     * 根据协议ID修改使用状态为使用结束
     * @param id 协议ID
     */
    void upDateProtocols(String id);

    /**
     * 将使用协议状态改为使用结束
     * @param hrProtocolDTO 客户id、协议编号
     */
    void updateProtocolUseStatus(HrProtocolDTO hrProtocolDTO);


    void updateFirstSignTime(HrProtocolDTO hrProtocolDTO);

    /**
     * 查询客户协议--不带分页
     * @param protocolDTO
     * @return
     */
    List<HrProtocolDTO> findList(@Param("param1")HrProtocolDTO protocolDTO);

    /**
     * 查询客户正在使用的协议
     * @param clientId
     * @return
     */
    List<HrProtocol> getIsUsedProtocol(@Param("clientId") String clientId);

    /**
     * 查询协议列表
     * @param states
     * @return
     */
    List<HrProtocolDTO> selectProtocolByStates(@Param("states") List<Integer> states);

    /**
     * 更新归档信息
     * @param hrProtocolDTO
     */
    void updateArchiveInfo(HrProtocolDTO hrProtocolDTO);

    /**
     * 更新 续签的原始协议 为 已续签
     *
     * @param clientId        客户id
     * @param agreementNumber 协议号
     */
    void updateOriginalProtocol(@Param("clientId") String clientId, @Param("agreementNumber") String agreementNumber);
}
