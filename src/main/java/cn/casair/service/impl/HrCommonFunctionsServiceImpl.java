package cn.casair.service.impl;

import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.DateUtils;
import cn.casair.common.utils.EnumUtils;
import cn.casair.common.utils.StringUtils;
import cn.casair.common.utils.UrlSchemeUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.mapper.HrCommonFunctionsMapper;
import cn.casair.mapper.HrNotificationUserContentMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 常用功能服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class HrCommonFunctionsServiceImpl extends ServiceImpl<HrCommonFunctionsRepository, HrCommonFunctions> implements HrCommonFunctionsService {

    private static final Logger log = LoggerFactory.getLogger(HrCommonFunctionsServiceImpl.class);
    private final HrProtocolRepository hrProtocolRepository;
    private final HrCommonFunctionsRepository hrCommonFunctionsRepository;
    private final HrContractRepository hrContractRepository;
    private final cn.casair.service.HrClientService HrClientService;
    private final HrStaffTurnPositiveRepository hrStaffTurnPositiveRepository;
    private final HrCommonFunctionsMapper hrCommonFunctionsMapper;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrApplyEntryStaffRepository hrApplyEntryStaffRepository;
    private final HrWorkInjuryRepository hrWorkInjuryRepository;
    private final HrRetireRepository hrRetireRepository;
    private final HrFertilityService hrFertilityService;
    private final HrApplyDepartureStaffService hrApplyDepartureStaffService;
    private final HrCertificateIssuanceService hrCertificateIssuanceService;
    private final HrCertificateIssuanceRepository hrCertificateIssuanceRepository;
    private final HrLendingApplyService hrLendingApplyService;
    private final HrDataModificationService hrDataModificationService;
    private final HrApplyEntryService hrApplyEntryService;
    private final HrClientService hrClientService;
    private final HrNotificationUserContentRepository hrNotificationUserContentRepository;
    private final HrNotificationUserContentMapper hrNotificationUserContentMapper;
    private final HrBillInvoiceRepository hrBillInvoiceRepository;
    private final HrBillReimbursementApplyRepository hrBillReimbursementApplyRepository;
    private final HrFeeReviewRepository hrFeeReviewRepository;
    private final HrContractViewRepository hrContractViewRepository;
    private final HrStaffSecondmentRepository hrStaffSecondmentRepository;
    private final HrMaternityAllowanceRepository hrMaternityAllowanceRepository;
    private final HrProtocolService hrProtocolService;

    @Value("${mini.appId}")
    private String appId;
    @Value("${mini.secret}")
    private String secret;
    @Value("${mini.getAccessToken}")
    private String accessTokenUrl;
    @Value("${mini.generateLinkURL}")
    private String linkURL;
    /**
     * 创建常用功能
     *
     * @param hrCommonFunctionsDTO
     * @return
     */
    @Override
    public HrCommonFunctionsDTO createHrCommonFunctions(HrCommonFunctionsDTO hrCommonFunctionsDTO) {
        log.info("Create new HrCommonFunctions:{}", hrCommonFunctionsDTO);
        HrCommonFunctions hrCommonFunctions = this.hrCommonFunctionsMapper.toEntity(hrCommonFunctionsDTO);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        this.hrCommonFunctionsRepository.deleteUserId(jwtUserDTO.getId());
        hrCommonFunctions.setUserId(jwtUserDTO.getId());
        this.hrCommonFunctionsRepository.insert(hrCommonFunctions);
        return this.hrCommonFunctionsMapper.toDto(hrCommonFunctions);
    }


    /**
     * 查询常用功能详情
     *
     * @param
     * @return
     */
    @Override
    public HrCommonFunctionsDTO getHrCommonFunctions() {
        log.info("Get HrCommonFunctions :{}");
        QueryWrapper<HrCommonFunctions> qw = new QueryWrapper<>();
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        qw.eq("user_id", jwtUserDTO.getId());
        qw.ne("is_delete", 1);
        HrCommonFunctions hrCommonFunctions = this.hrCommonFunctionsRepository.selectOne(qw);
        if (hrCommonFunctions != null) {
            return this.hrCommonFunctionsMapper.toDto(hrCommonFunctions);
        } else {
            HrCommonFunctionsDTO hrCommonFunctionsDTO = new HrCommonFunctionsDTO();
            return hrCommonFunctionsDTO;
        }
    }

    @Override
    public List<HrRemindDTO> getHrRemindFege() throws ParseException {
        List<String> clientIdList = this.HrClientService.selectClientIdByUserId();
        if (CollectionUtils.isEmpty(clientIdList)) {
            return new ArrayList<>();
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        QueryWrapper<HrProtocol> qw = new QueryWrapper<>();
        QueryWrapper<HrContract> hc = new QueryWrapper<>();
        QueryWrapper<HrWorkInjury> hw = new QueryWrapper<>();
        QueryWrapper<HrTalentStaff> hs = new QueryWrapper<>();
        List<HrRemindDTO> hrRemindDTOS = new ArrayList<>();

        if (jwtUserDTO != null) {
            List<HrRemindConfDTO> remindKey = this.hrCommonFunctionsRepository.selectRemindKey(jwtUserDTO.getCurrentRoleId());
            for (HrRemindConfDTO hrRemindConfDTO : remindKey) {
                //客户协议即将到期
                if (hrRemindConfDTO.getRemindKey().equals("customer_agreement")) {
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    List<String> statusList = new ArrayList<>();
                    statusList.add("5");
                    List<Integer> statesList = statusList.stream().map(Integer::parseInt).collect(Collectors.toList());
                    List<HrProtocolDTO> hrProtocols = hrProtocolService.findList(new HrProtocolDTO().setStatesList(statesList));
                    hrRemindDTO.setStatus(statusList);
                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setContent("有" + hrProtocols.size() + "个客户协议即将到期");
                    hrRemindDTO.setType("customer_agreement");
                    if (CollectionUtils.isNotEmpty(hrProtocols)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    continue;
                }
                //客户协议已到期
                if (hrRemindConfDTO.getRemindKey().equals("customer_agreement_expire")) {
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    List<String> statusList = new ArrayList<>();
                    statusList.add("2");
                    List<Integer> statesList = statusList.stream().map(Integer::parseInt).collect(Collectors.toList());
                    List<HrProtocolDTO> hrProtocols = hrProtocolService.findList(new HrProtocolDTO().setStatesList(statesList));
                    hrRemindDTO.setStatus(statusList);
                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setContent("有" + hrProtocols.size() + "个客户协议已到期");
                    hrRemindDTO.setType("customer_agreement_expire");
                    if (CollectionUtils.isNotEmpty(hrProtocols)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    continue;
                }
                //员工合同即将到期
                if (hrRemindConfDTO.getRemindKey().equals("staff_contract")) {
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    HrTalentStaffDTO staffDTO = new HrTalentStaffDTO();
                    staffDTO.setContractState(ContractEnum.ContractState.EXPIRING_SOON.getKey());
                    List<HrTalentStaffDTO> hrTalentStaffsList = hrTalentStaffRepository.findNotPageHrTalentStaff(staffDTO, clientIdList);
                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setContent("有" + hrTalentStaffsList.size() + "个员工劳动合同即将到期");
                    hrRemindDTO.setType("staff_contract");
                    if (CollectionUtils.isNotEmpty(hrTalentStaffsList)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    continue;
                }
                //员工合同已到期
                if (hrRemindConfDTO.getRemindKey().equals("staff_contract_expire")) {
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    HrTalentStaffDTO staffDTO = new HrTalentStaffDTO();
                    staffDTO.setContractState(ContractEnum.ContractState.EXPIRED.getKey())
                        .setRuleDay(hrRemindConfDTO.getRuleDay());
                    List<HrTalentStaffDTO> hrTalentStaffsList = hrTalentStaffRepository.findNotPageHrTalentStaff(staffDTO, clientIdList);
                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setContent("有" + hrTalentStaffsList.size() + "个员工劳动合同已到期");
                    hrRemindDTO.setType("staff_contract_expire");
                    if (CollectionUtils.isNotEmpty(hrTalentStaffsList)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    continue;
                }
                //档案借阅
                if (hrRemindConfDTO.getRemindKey().equals("archives_borrowing")) {
                    long methodInTime = System.currentTimeMillis();
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    List<String> statusList = new ArrayList<>();
                    statusList.add("1");
                    List<HrArchivesBring> hrArchivesBring = this.hrCommonFunctionsRepository.selectHrArchivesBring(clientIdList, statusList);
                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setStatus(statusList);
                    hrRemindDTO.setContent("有" + hrArchivesBring.size() + "份档案超期未归还");
                    hrRemindDTO.setType("archives_borrowing");
                    if (CollectionUtils.isNotEmpty(hrArchivesBring)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    long timeCost = System.currentTimeMillis() - methodInTime;
                    continue;
                }
                //员工退休
                if (hrRemindConfDTO.getRemindKey().equals("staff_retire")) {
                    long methodInTime = System.currentTimeMillis();
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    List<String> statusList = new ArrayList<>();
                    statusList.add("3");
                    List<HrTalentStaff> hrRetire = this.hrCommonFunctionsRepository.selectHrRetire(clientIdList, statusList);
                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setContent("有" + hrRetire.size() + "个员工待办理退休");
                    hrRemindDTO.setType("staff_retire");
                    if (CollectionUtils.isNotEmpty(hrRetire)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    long timeCost = System.currentTimeMillis() - methodInTime;
                    continue;
                }
                //工伤处理
                if (hrRemindConfDTO.getRemindKey().equals("staff_injure")) {
                    long methodInTime = System.currentTimeMillis();
                    //获取当前日期
                    DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd");
                    LocalDate newdate = LocalDate.now();
                    //获取工伤提醒时间
                    Integer time = this.hrProtocolRepository.selecttime("staff_injure");
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    List<String> statusList = new ArrayList<>();
                    List<String> staffId = this.hrCommonFunctionsRepository.selectClient(clientIdList);
                    statusList.add("1");
                    hw.in(CollectionUtils.isNotEmpty(statusList), "status", statusList);
                    hw.eq("is_delete", "0");
                    if (staffId.size() > 0) {
                        hw.in("staff_id", staffId);
                    }
                    hw.isNull("declare_date");
                    List<HrWorkInjury> hrWorkInjuryDTO = this.hrWorkInjuryRepository.selectList(hw);
                    List<String> staffIdList = new ArrayList<>();
                    for (HrWorkInjury hrWorkInjury : hrWorkInjuryDTO) {
                        LocalDate createddate = LocalDate.from(hrWorkInjury.getCreatedDate());

                        //时间加
                        Date d = newtime.parse(String.valueOf(createddate));
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(d);
                        calendar.add(calendar.DATE, +time);
                        //工伤提醒日期
                        LocalDate terminationtime = LocalDate.parse(newtime.format(calendar.getTime()));
                        if (terminationtime.isBefore(newdate)) {
                            staffIdList.add(hrWorkInjury.getStaffId());
                        }
                    }
                    if (staffIdList.size() != 0) {
                        LinkedList<LocalDateTime> injuryDate = this.hrCommonFunctionsRepository.selectInjuryDate(staffIdList);
                        if (injuryDate.size() != 0) {
                            List<LocalDate> LOD = new ArrayList<>();
                            LOD.add(LocalDate.from(injuryDate.getFirst()));
                            LOD.add(LocalDate.from(injuryDate.getLast()));
                            hrRemindDTO.setStartDate(LOD);
                        }
                    }

                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setContent("有" + staffIdList.size() + "个员工工伤申请待处理");
                    hrRemindDTO.setType("staff_injure");
                    if (CollectionUtils.isNotEmpty(staffIdList)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    long timeCost = System.currentTimeMillis() - methodInTime;
                    continue;
                }
                //医疗备案
                if (hrRemindConfDTO.getRemindKey().equals("staff_medical")) {
                    long methodInTime = System.currentTimeMillis();
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    List<String> statusList = new ArrayList<>();
                    hs.eq("is_delete", "0");
                    hs.in(CollectionUtils.isNotEmpty(clientIdList), "client_id", clientIdList);
                    hs.eq("staff_status", "4");
                    hs.isNotNull("medical_record_date");
                    List<HrTalentStaff> hrTalentStaffs = this.hrTalentStaffRepository.selectList(hs);
                    DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd");
                    LocalDate newdate = LocalDate.now();

                    for (HrTalentStaff hrTalentStaff : hrTalentStaffs) {
                        if (hrTalentStaff.getMedicalRecordDate() != null) {
                            int old = DateUtils.calculateYearDifference(newdate, hrTalentStaff.getMedicalRecordDate());
                            if (old >= 3) {
                                statusList.add(hrTalentStaff.getId());
                            }
                        }
                    }
                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setContent("有" + statusList.size() + "个员工医疗信息待备案");
                    hrRemindDTO.setType("staff_medical");
                    if (CollectionUtils.isNotEmpty(statusList)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    long timeCost = System.currentTimeMillis() - methodInTime;
                    continue;

                }
                //生育薪资
                if (hrRemindConfDTO.getRemindKey().equals("staff_birth")) {
                    long methodInTime = System.currentTimeMillis();
                    Integer time = this.hrProtocolRepository.selecttime("staff_birth");
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    List<String> staffId = new ArrayList<>();
                    //获取到产假开始时间
                    List<HrFertility> hrFertilityList = this.hrCommonFunctionsRepository.selectHrFertilityList(clientIdList);
                    //获取当前日期
                    DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd");
                    LocalDate newdate = LocalDate.now();
                    for (HrFertility hrFertility : hrFertilityList) {
                        //时间加
                        Date d = newtime.parse(String.valueOf(hrFertility.getMaternityLeaveStartDate()));
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(d);
                        calendar.add(calendar.DATE, +time);
                        //产假提醒日期
                        LocalDate terminationtime = LocalDate.parse(newtime.format(calendar.getTime()));
                        if (hrFertility.getMaternityLeaveStartDate().isBefore(newdate) && (newdate.isBefore(terminationtime) || newdate.equals(terminationtime)) && terminationtime.isBefore(hrFertility.getMaternityLeaveEndDate())) {
                            staffId.add(hrFertility.getStaffId());
                        }
                    }
                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setContent("有" + staffId.size() + "个产假员工需计算薪资");
                    hrRemindDTO.setType("staff_birth");
                    if (CollectionUtils.isNotEmpty(staffId)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    long timeCost = System.currentTimeMillis() - methodInTime;
                    continue;

                }
                //转正提醒
                if (hrRemindConfDTO.getRemindKey().equals("staff_turn")) {
                    long methodInTime = System.currentTimeMillis();
                    HrRemindDTO hrRemindDTO = new HrRemindDTO();
                    List<String> staffId = new ArrayList<>();
                    List<String> staffIdList = new ArrayList<>();
                    List<String> parSum = new ArrayList<>();
                    HrStaffTurnPositiveDTO hrStaffTurnPositiveDTOs = new HrStaffTurnPositiveDTO();
                    hrStaffTurnPositiveDTOs.setClientList(clientIdList);
                    List<HrTalentStaffDTO> hrTalentStaffDTO = this.hrApplyEntryStaffRepository.selectClientId(hrStaffTurnPositiveDTOs);
                    //获取当前日期
                    DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd");
                    LocalDate newdate = LocalDate.now();
                    Integer time = this.hrStaffTurnPositiveRepository.selecttime("staff_turn");
                    if (time == null) {
                        time = 0;
                    }

                    //查询出客户id的所有员工
                    List<String> stature = new ArrayList<>();
                    stature.add("3");
                    stature.add("9");
                    hrStaffTurnPositiveDTOs.setStaffStatusList(stature);
                    List<HrTalentStaffDTO> hrTalentStaffDTOas = this.hrApplyEntryStaffRepository.selectClientId(hrStaffTurnPositiveDTOs);
                    List<String> staffIds = hrTalentStaffDTOas.stream().map(HrTalentStaffDTO::getId).distinct().collect(Collectors.toList());
                    List<String> staffidLists = new ArrayList<>();
                    if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(staffIds)) {
                        //获取主动提交的
                        QueryWrapper<HrStaffTurnPositive> dw = new QueryWrapper<>();
                        dw.eq("type", 1);
                        dw.in("staff_id", staffIds);
                        List<HrStaffTurnPositive> hrStaffTurnPositives = this.hrStaffTurnPositiveRepository.selectList(dw);
                        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(hrStaffTurnPositives)) {
                            List<String> staffIda = hrStaffTurnPositives.stream().map(HrStaffTurnPositive::getStaffId).distinct().collect(Collectors.toList());
                            staffId.addAll(staffIda);
                        }
                    }
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(calendar.DATE, time);
                    LocalDate terminationtime = LocalDate.parse(newtime.format(calendar.getTime()));
                    for (HrTalentStaffDTO talentStaffDTO : hrTalentStaffDTO) {
                        //获取时间差
                        if (talentStaffDTO.getInternshipDate() != null) {
                            long date = newdate.until(talentStaffDTO.getInternshipDate(), ChronoUnit.DAYS);
                            if (date <= time) {
                                if (talentStaffDTO.getId() != null) {
                                    staffId.add(talentStaffDTO.getId());
                                }
                            }
                        }
                    }
                    staffIdList.addAll(staffId);
                    parSum = this.hrCommonFunctionsRepository.selectStaffs(staffIdList);
                    hrRemindDTO.setTitle(hrRemindConfDTO.getTitle());
                    hrRemindDTO.setContent("有" + parSum.size() + "个员工待转正");
                    hrRemindDTO.setType("staff_turn");
                    if (CollectionUtils.isNotEmpty(parSum)) {
                        hrRemindDTOS.add(hrRemindDTO);
                    }
                    long timeCost = System.currentTimeMillis() - methodInTime;
                }
            }
        }

        return hrRemindDTOS;
    }

    @Override
    public List<HrMessageListDTO> getHrRemindMessage() {
        // List<String> clientIdList = this.HrClientService.selectClientIdByUserId();
        JWTUserDTO jwtUserDTO = null;
        try {
            jwtUserDTO = SecurityUtils.getCurrentUser().get();
        } catch (Exception exception) {
            log.error("查询消息列表: 用户token信息获取失败!");
            return null;
        }

        /*List<String> userId = this.hrCommonFunctionsRepository.selectUserID(jwtUserDTO.getId());
        userId.add(jwtUserDTO.getId());*/
        return this.hrCommonFunctionsRepository.selectHrMessageList(jwtUserDTO.getId());
    }

    @Override
    public void getHrRemindMessageUpdate(String id) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        this.hrCommonFunctionsRepository.getHrRemindMessageUpdate(id, jwtUserDTO.getId());
    }

    @Override
    public HrBusinessIndicatorsDTO getBusinessIndicators() {
        HrBusinessIndicatorsDTO hrBusinessIndicatorsDTO = new HrBusinessIndicatorsDTO();
        List<HrServiceDTO> hrServiceDTOs = new ArrayList<>();
        List<String> clientIdList = this.HrClientService.selectClientIdByUserId();
        LocalDate currentDate = LocalDate.now();
        LocalDate lastDate = currentDate.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate thisDate = currentDate.with(TemporalAdjusters.lastDayOfMonth());
        //入职
        hrBusinessIndicatorsDTO.setHrOnboardingDTO(this.hrCommonFunctionsRepository.selectOnboarding(clientIdList, lastDate, thisDate));
        //离职
        hrBusinessIndicatorsDTO.setHrResignDTO(this.hrCommonFunctionsRepository.selectHrResign(clientIdList, lastDate, thisDate));
        //服务-入职
        List<HrServiceDTO> hrServiceDTOOnboarding = this.hrCommonFunctionsRepository.selectHrServiceDTO(clientIdList, lastDate, thisDate);
        hrServiceDTOs.addAll(hrServiceDTOOnboarding);
        //服务-离职
        List<HrServiceDTO> hrServiceDTOResign = this.hrCommonFunctionsRepository.selecthrServiceDTOResign(clientIdList, lastDate, thisDate);
        hrServiceDTOs.addAll(hrServiceDTOResign);
        //服务-转正
        List<HrServiceDTO> hrServiceDTOPositive = this.hrCommonFunctionsRepository.selecthrServiceDTOPositive(clientIdList, lastDate, thisDate);
        hrServiceDTOs.addAll(hrServiceDTOPositive);
        //服务-退休
        List<HrServiceDTO> hrServiceDTORetire = this.hrCommonFunctionsRepository.selecthrServiceDTORetire(clientIdList, lastDate, thisDate);
        hrServiceDTOs.addAll(hrServiceDTORetire);
        //服务-生育
        List<HrServiceDTO> hrServiceDTORetireFertility = this.hrCommonFunctionsRepository.selectListtime(clientIdList, lastDate, thisDate);
        hrServiceDTOs.addAll(hrServiceDTORetireFertility);
        //服务-医疗
        List<HrServiceDTO> hrServiceDTOMedical = this.hrCommonFunctionsRepository.selectListgetId(clientIdList, lastDate, thisDate);
        hrServiceDTOs.addAll(hrServiceDTOMedical);
        //服务-工伤
        List<HrServiceDTO> hrServiceDTOWork = this.hrCommonFunctionsRepository.hrServiceDTOWork(clientIdList, lastDate, thisDate);
        hrServiceDTOs.addAll(hrServiceDTOWork);
        //服务-证明开具
        List<HrServiceDTO> hrServiceDTOProve = this.hrCommonFunctionsRepository.hrServiceDTOProve(clientIdList, lastDate, thisDate);
        hrServiceDTOs.addAll(hrServiceDTOProve);
        //薪资
        hrBusinessIndicatorsDTO.setHrSalaryDTO(this.hrCommonFunctionsRepository.selectHrSalaryDTO(clientIdList, lastDate, thisDate));
        //福利
        hrBusinessIndicatorsDTO.setHrWelfareDTO(this.hrCommonFunctionsRepository.selectHrWelfareDTO(clientIdList, lastDate, thisDate));
        hrBusinessIndicatorsDTO.setHrServiceDTO(hrServiceDTOs);
        return hrBusinessIndicatorsDTO;
    }

    /**
     * 申请列表
     *
     * @return
     */
    @Override
    public List<ApplicationListDTO> getApplicationList() {
        //查询数据权限
        List<String> clientId = hrClientService.selectClientIdByUserId();
        if (CollectionUtils.isEmpty(clientId)) {
            return new ArrayList<>();
        }
        //创建返回的数据
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        ArrayList<ApplicationListDTO> applicationListDTOS = new ArrayList<>();
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            // 查询工伤的申请列表中的数据
            List<ApplicationListDTO> workInjuryApplication = getWorkInjuryApplication(clientId);
            applicationListDTOS.addAll(workInjuryApplication);
        }
        //生育
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_STAFF.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            List<ApplicationListDTO> fertilityApplication = getFertilityApplication(clientId);
            applicationListDTOS.addAll(fertilityApplication);
        }
        //离职
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            List<ApplicationListDTO> departureApplication = getDepartureApplication(clientId,jwtUserDTO);
            applicationListDTOS.addAll(departureApplication);
        }
        //证明开具
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_STAFF.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.GENERAL_MANAGER.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.FINANCIAL_DIRECTOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.XINCHOUZHUANYUANB.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.DAIYUHEGUIZHUANYUAN.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            List<ApplicationListDTO> certificateApplication = getCertificateApplication(clientId,jwtUserDTO);
            applicationListDTOS.addAll(certificateApplication);
        }
        //档案借阅
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_STAFF.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            List<ApplicationListDTO> lendingApplication = getLendingApplication(clientId,jwtUserDTO);
            applicationListDTOS.addAll(lendingApplication);
        }
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_STAFF.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            //资料修改
            List<ApplicationListDTO> auditLnApplication = getAuditLnApplication(clientId);
            applicationListDTOS.addAll(auditLnApplication);
        }
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_STAFF.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){

            //入职
            List<ApplicationListDTO> approvalApplication = getApprovalApplication(clientId);
            applicationListDTOS.addAll(approvalApplication);
            //退休
            List<ApplicationListDTO> retire = getRetire(clientId);
            applicationListDTOS.addAll(retire);
        }
        //转正
        List<ApplicationListDTO> staffTurnApplication = getStaffTurnApplication(clientId);
        applicationListDTOS.addAll(staffTurnApplication);

        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.ACCOUNTING.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            //开票申请
            List<ApplicationListDTO> billInvoicesApplication = getBillInvoicesApplication(clientId,jwtUserDTO);
            applicationListDTOS.addAll(billInvoicesApplication);
            //报销申请
            List<ApplicationListDTO> billReimbursementApplication = getBillReimbursementApplication(clientId,jwtUserDTO);
            applicationListDTOS.addAll(billReimbursementApplication);
        }
        //结算单申请
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            List<ApplicationListDTO> feeReviewApplication = getFeeReviewApplication(clientId);
            applicationListDTOS.addAll(feeReviewApplication);
        }
        //作废申请
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            List<ApplicationListDTO> cancelApplication = getCancelApplication(clientId);
            applicationListDTOS.addAll(cancelApplication);
        }
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())
            ||jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_STAFF.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            //合同查看申请
            List<ApplicationListDTO> contractViewApplication = getContractViewApplication(clientId,jwtUserDTO);
            applicationListDTOS.addAll(contractViewApplication);
            //员工借调服务
            List<ApplicationListDTO> staffSecondmentApplication = getStaffSecondmentApplication(clientId,jwtUserDTO);
            applicationListDTOS.addAll(staffSecondmentApplication);
        }
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_STAFF.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            //续签服务
            List<ApplicationListDTO> renewalServiceApplication = getRenewalServiceApplication(clientId,jwtUserDTO);
            applicationListDTOS.addAll(renewalServiceApplication);
        }
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.DAIYUHEGUIZHUANYUAN.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.SUPER_ADMINISTRATOR.getKey())
            || jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.MAINTENANCE.getKey())){
            //生育津贴
            List<ApplicationListDTO> renewalServiceApplication = getMaternityApplication(clientId);
            applicationListDTOS.addAll(renewalServiceApplication);
        }

        //排序
        Collections.sort(applicationListDTOS, new Comparator<ApplicationListDTO>() {
            @Override
            public int compare(ApplicationListDTO user1, ApplicationListDTO user2) {
                try {
                    LocalDateTime dt1 = user1.getDate();
                    LocalDateTime dt2 = user2.getDate();
                    if (dt1.isBefore(dt2)) {
                        return 1;
                    } else if (dt1.isAfter(dt2)) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
        return applicationListDTOS;
    }

    private List<ApplicationListDTO> getMaternityApplication(List<String> clientId) {
        List<Integer> integers = Arrays.asList(ServiceCenterEnum.MaternityAllowanceStateEnum.INITIATE_APPLICATION.getKey());
        QueryWrapper<HrMaternityAllowance> qw = new QueryWrapper<>();
        qw.in("state",integers);
        qw.in("client_id",clientId);
        List<HrMaternityAllowance> list = hrMaternityAllowanceRepository.selectList(qw);
        List<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrMaternityAllowance dto : list) {
            String clientName = getClientName(dto.getClientId());
            ApplicationListDTO applicationDTO = new ApplicationListDTO();
            applicationDTO.setType("生育津贴");
            applicationDTO.setTitle("来自" + clientName + "生育津贴");
            applicationDTO.setDate(dto.getLastModifiedDate());
            applicationDTO.setStateList(integers);
            applicationDTO.setJumpPage(ServiceCenterEnum.MATERNITY_ALLOWANCE.getKey());
            applicationListDTOArrayList.add(applicationDTO);
        }
        return applicationListDTOArrayList;
    }

    private List<ApplicationListDTO> getRenewalServiceApplication(List<String> clientId, JWTUserDTO jwtUserDTO) {
        List<Integer> states = Arrays.asList(StaffEnum.RenewalProcessEnum.TO_BE_REVIEWED.getKey());
        QueryWrapper<HrTalentStaff> qw = new QueryWrapper<>();
        qw.in("client_id",clientId);
        qw.in("renewal_process", states);
        qw.orderByDesc("created_date");
        qw.last("LIMIT 5");
        List<HrTalentStaff> hrTalentStaffList = hrTalentStaffRepository.selectList(qw);
        List<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrTalentStaff hrTalentStaff : hrTalentStaffList) {
            String clientName = getClientName(hrTalentStaff.getClientId());
            ApplicationListDTO workInjuryApplication = new ApplicationListDTO();
            workInjuryApplication.setType("续签服务");
            workInjuryApplication.setTitle("来自" + clientName + "续签服务");
            workInjuryApplication.setDate(hrTalentStaff.getLastModifiedDate());
            workInjuryApplication.setStateList(states);
            workInjuryApplication.setJumpPage(ServiceCenterEnum.RENEWAL_SERVICE.getKey());
            applicationListDTOArrayList.add(workInjuryApplication);
        }
        return applicationListDTOArrayList;
    }

    private List<ApplicationListDTO> getStaffSecondmentApplication(List<String> clientId, JWTUserDTO jwtUserDTO) {
        List<Integer> states = Arrays.asList(
            ServiceCenterEnum.SecondmentStatesEnum.TO_BE_SECONDMENT.getKey(),
            ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_ON.getKey());
        List<Integer> step = new ArrayList<>();
        UserRoleTypeEnum enumByKey = EnumUtils.getEnumByKey(UserRoleTypeEnum.class, jwtUserDTO.getCurrentRoleKey());
        switch (enumByKey){
            case CLIENT:
                step.add(ServiceCenterEnum.SecondmentStepEnum.NEW_COMPANY_CONFIRMED.getKey());
                step.add(ServiceCenterEnum.SecondmentStepEnum.OLD_COMPANY_CONFIRMED.getKey());
                break;
            case CUSTOMER_SERVICE_MANAGER:
            case CUSTOMER_SERVICE_STAFF:
                step.add(ServiceCenterEnum.SecondmentStepEnum.MANAGER_REVIEW.getKey());
                step.add(ServiceCenterEnum.SecondmentStepEnum.MANAGER_REVIEW_REPEAT.getKey());
                break;
            default:
                step.add(ServiceCenterEnum.SecondmentStepEnum.NEW_COMPANY_CONFIRMED.getKey());
                step.add(ServiceCenterEnum.SecondmentStepEnum.OLD_COMPANY_CONFIRMED.getKey());
                step.add(ServiceCenterEnum.SecondmentStepEnum.MANAGER_REVIEW.getKey());
                step.add(ServiceCenterEnum.SecondmentStepEnum.MANAGER_REVIEW_REPEAT.getKey());
                break;
        }
        HrStaffSecondmentDTO hrStaffSecondmentDTO = new HrStaffSecondmentDTO();
        hrStaffSecondmentDTO.setStatesList(states).setStepList(step);
        List<HrStaffSecondment> hrStaffSecondments = hrStaffSecondmentRepository.findByApply(hrStaffSecondmentDTO,clientId);
        List<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrStaffSecondment hrStaffSecondment : hrStaffSecondments) {
            String clientName = getClientName(hrStaffSecondment.getIsDefault() == 0 ? hrStaffSecondment.getOldClientId() : hrStaffSecondment.getNewClientId());
            ApplicationListDTO applicationListDTO = new ApplicationListDTO();
            applicationListDTO.setType("员工借调服务");
            applicationListDTO.setTitle("来自" + clientName + "员工借调服务");
            applicationListDTO.setDate(hrStaffSecondment.getLastModifiedDate());
            applicationListDTO.setStateList(states);
            applicationListDTO.setStepList(step);
            applicationListDTO.setJumpPage(ServiceCenterEnum.STAFF_SECONDMENT.getKey());
            applicationListDTOArrayList.add(applicationListDTO);
        }
        return applicationListDTOArrayList;
    }

    private List<ApplicationListDTO> getContractViewApplication(List<String> clientId, JWTUserDTO jwtUserDTO) {
        List<Integer> integers = new ArrayList<>();
        UserRoleTypeEnum enumByKey = EnumUtils.getEnumByKey(UserRoleTypeEnum.class, jwtUserDTO.getCurrentRoleKey());
        switch (enumByKey){
            case CLIENT: integers.add(ServiceCenterEnum.ContractViewStateEnum.TO_BE_CUSTOMER_REVIEWED.getKey()); break;
            case CUSTOMER_SERVICE_MANAGER:
            case CUSTOMER_SERVICE_STAFF:
                integers.add(ServiceCenterEnum.ContractViewStateEnum.TO_BE_REVIEWED.getKey()); break;
            default:
                integers.add(ServiceCenterEnum.ContractViewStateEnum.TO_BE_CUSTOMER_REVIEWED.getKey());
                integers.add(ServiceCenterEnum.ContractViewStateEnum.TO_BE_REVIEWED.getKey());
                break;
        }
        QueryWrapper<HrContractView> qw = new QueryWrapper<>();
        qw.in("client_id",clientId);
        qw.in("states", integers);
        qw.orderByDesc("created_date");
        qw.last("LIMIT 5");
        List<HrContractView> hrContractViews = hrContractViewRepository.selectList(qw);
        List<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrContractView hrContractView : hrContractViews) {
            String clientName = getClientName(hrContractView.getClientId());
            ApplicationListDTO workInjuryApplication = new ApplicationListDTO();
            workInjuryApplication.setType("员工合同服务");
            workInjuryApplication.setTitle("来自" + clientName + "员工合同服务");
            workInjuryApplication.setDate(hrContractView.getLastModifiedDate());
            workInjuryApplication.setStateList(integers);
            workInjuryApplication.setJumpPage(ServiceCenterEnum.CONTRACT_VIEW_DOWNLOAD.getKey());
            applicationListDTOArrayList.add(workInjuryApplication);
        }
        return applicationListDTOArrayList;
    }

    private List<ApplicationListDTO> getFeeReviewApplication(List<String> clientId) {
        List<Integer> integers = Arrays.asList(BillEnum.FeeReviewState.TO_BE_REVIEWED.getKey());
        QueryWrapper<HrFeeReview> qw = new QueryWrapper<>();
        qw.in("client_id",clientId);
        qw.in("status", integers);
        qw.orderByDesc("created_date");
        qw.last("LIMIT 5");
        List<HrFeeReview> hrFeeReviews = hrFeeReviewRepository.selectList(qw);
        List<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrFeeReview hrFeeReview : hrFeeReviews) {
            String clientName = getClientName(hrFeeReview.getClientId());
            //费用审核返回的数据
            ApplicationListDTO workInjuryApplication = new ApplicationListDTO();
            workInjuryApplication.setType("费用审核");
            workInjuryApplication.setTitle("来自" + clientName + "费用审核");
            workInjuryApplication.setDate(hrFeeReview.getLastModifiedDate());
            workInjuryApplication.setStateList(integers);
            workInjuryApplication.setJumpPage(ServiceCenterEnum.FEE_REVIEW.getKey());
            applicationListDTOArrayList.add(workInjuryApplication);
        }
        return applicationListDTOArrayList;
    }

    private List<ApplicationListDTO> getCancelApplication(List<String> clientId) {
        QueryWrapper<HrFeeReview> qw = new QueryWrapper<>();
        qw.in("client_id",clientId);
        qw.eq("status", BillEnum.FeeReviewState.TO_BE_CANCEL.getKey());
        qw.orderByDesc("created_date");
        qw.last("LIMIT 5");
        List<Integer> integers = Arrays.asList(BillEnum.FeeReviewState.TO_BE_CANCEL.getKey());
        List<HrFeeReview> hrFeeReviews = hrFeeReviewRepository.selectList(qw);
        List<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrFeeReview hrFeeReview : hrFeeReviews) {
            String clientName = getClientName(hrFeeReview.getClientId());
            //作废申请返回的数据
            ApplicationListDTO workInjuryApplication = new ApplicationListDTO();
            workInjuryApplication.setType("作废申请");
            workInjuryApplication.setTitle("来自" + clientName + "作废申请");
            workInjuryApplication.setDate(hrFeeReview.getLastModifiedDate());
            workInjuryApplication.setStateList(integers);
            workInjuryApplication.setJumpPage(ServiceCenterEnum.CANCEL_APPLY.getKey());
            applicationListDTOArrayList.add(workInjuryApplication);
        }
        return applicationListDTOArrayList;
    }

    private List<ApplicationListDTO> getBillReimbursementApplication(List<String> clientId,JWTUserDTO curUser) {
        HrBillReimbursementApplyDTO billReimbursementApplyDTO = new HrBillReimbursementApplyDTO();
        List<Integer> integers = new ArrayList<>();
        if (curUser.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey())){
            integers.add(BillReimbApproveEnums.ON_CUSTOMER_MANAGER.getKey());
        }else if (curUser.getCurrentRoleKey().equals(UserRoleTypeEnum.ACCOUNTING.getKey())){
            integers.add(BillReimbApproveEnums.ON_ACCOUNTING.getKey());
        }else {
            integers.add(BillReimbApproveEnums.ON_CUSTOMER_MANAGER.getKey());
            integers.add(BillReimbApproveEnums.ON_ACCOUNTING.getKey());
        }
        billReimbursementApplyDTO.setReimbursementState(BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey())
            .setAllowClientIds(clientId)
            .setApproveStatusList(integers);
        List<HrBillReimbursementApplyDTO> hrBillReimbursementApplies = hrBillReimbursementApplyRepository.findCommonApplyList(billReimbursementApplyDTO);
        List<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrBillReimbursementApplyDTO hrBillReimbursementApply : hrBillReimbursementApplies) {
            String title = hrBillReimbursementApply.getClientName() != null ? hrBillReimbursementApply.getClientName() : hrBillReimbursementApply.getAccountNumber();
            if (StringUtils.isBlank(title) || title.equals("null")){
                title = hrBillReimbursementApply.getTitle();
            }
            //报销申请返回的数据
            ApplicationListDTO workInjuryApplication = new ApplicationListDTO();
            workInjuryApplication.setType("报销申请");
            workInjuryApplication.setTitle("来自" + title + "报销申请");
            workInjuryApplication.setDate(hrBillReimbursementApply.getLastModifiedDate());
            workInjuryApplication.setStateList(integers);
            workInjuryApplication.setJumpPage(ServiceCenterEnum.REIMBURSEMENT_APPLY.getKey());
            applicationListDTOArrayList.add(workInjuryApplication);
        }
        return applicationListDTOArrayList;
    }

    private List<ApplicationListDTO> getBillInvoicesApplication(List<String> clientId,JWTUserDTO curUser) {
        QueryWrapper<HrBillInvoice> qw = new QueryWrapper<>();
        qw.eq("invoice_state",BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey());
        qw.in("client_id",clientId);
        // 获取当前用户的可审批状态
        BillInvoiceApproveEnums approveEnums = EnumUtils.getEnum(BillInvoiceApproveEnums.class, "getRoleKey", curUser.getCurrentRoleKey());
        if(approveEnums != null) {
            qw.ge("approve_status",approveEnums.getKey());
        }
        List<Integer> integers = new ArrayList<>();
        if (curUser.getCurrentRoleKey().equals(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey())){
            integers.add(BillInvoiceApproveEnums.ON_CUSTOMER_MANAGER.getKey());
        }else if (curUser.getCurrentRoleKey().equals(UserRoleTypeEnum.ACCOUNTING.getKey())){
            integers.add(BillInvoiceApproveEnums.ON_ACCOUNTING.getKey());
        }else {
            integers.add(BillInvoiceApproveEnums.ON_CUSTOMER_MANAGER.getKey());
            integers.add(BillInvoiceApproveEnums.ON_ACCOUNTING.getKey());
        }
        qw.in("approve_status",integers);
        qw.orderByDesc("created_date");
        qw.last("LIMIT 5");
        List<HrBillInvoice> hrBillInvoices = hrBillInvoiceRepository.selectList(qw);
        List<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrBillInvoice hrBillInvoice : hrBillInvoices) {
            String clientName = getClientName(hrBillInvoice.getClientId());
            //开票申请返回的数据
            ApplicationListDTO workInjuryApplication = new ApplicationListDTO();
            workInjuryApplication.setType("开票申请");
            workInjuryApplication.setTitle("来自" + clientName + "开票申请");
            workInjuryApplication.setDate(hrBillInvoice.getLastModifiedDate());
            workInjuryApplication.setStateList(integers);
            workInjuryApplication.setJumpPage(ServiceCenterEnum.INVOICE_APPLY.getKey());
            applicationListDTOArrayList.add(workInjuryApplication);
        }
        return applicationListDTOArrayList;
    }

    private List<ApplicationListDTO> getRetire(List<String> clientId) {
        //查询所有审核的数据
        QueryWrapper<HrRetire> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", 4);
        queryWrapper.in("client_id ", clientId);
        queryWrapper.orderByDesc("created_date");
        queryWrapper.last("limit 5");
        //获取所有符合条件的所有数据
        List<HrRetire> list = hrRetireRepository.selectList(queryWrapper);
        //用于搜索的条件
        ArrayList<Integer> stateList = new ArrayList<>();
        stateList.add(4);
        //将数据重新进行封装
        ArrayList<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrRetire hrRetire : list) {
            String clientName = getClientName(hrRetire.getClientId());
            //服务返回的数据
            ApplicationListDTO applicationListDTO = new ApplicationListDTO();
            applicationListDTO.setType("退休服务");
            applicationListDTO.setTitle("来自" + clientName + "退休申请");
            applicationListDTO.setDate(hrRetire.getLastModifiedDate());
            applicationListDTO.setStateList(stateList);
            applicationListDTO.setJumpPage(ServiceCenterEnum.RETIRE_APPLICATIONS.getKey());
            applicationListDTOArrayList.add(applicationListDTO);

        }
        return applicationListDTOArrayList;
    }


    //提示框
    @Override
    public List<HrNotificationUserContentDTO> getApplicationListReminder() {
        JWTUserDTO jwtUserDTO = null;
        try {
            jwtUserDTO = SecurityUtils.getCurrentUser().get();
        } catch (Exception exception) {
            log.error("提醒框: 用户token信息获取失败!");
            return null;
        }
        List<HrNotificationUserContent> List = this.hrCommonFunctionsRepository.selectLists(jwtUserDTO.getId(), jwtUserDTO.getUserName());
        return this.hrNotificationUserContentMapper.toDto(List);
    }

    //提示框已读
    @Override
    public void getHrRemindMessageUpdateReminder(String id) {
        this.hrCommonFunctionsRepository.getHrRemindMessageUpdateReminder(id);
    }

    /**
     * 获取小程序 scheme 码
     * @param envVersion 打开的小程序版本
     * @return scheme 码
     */
    @Override
    public JSONObject getAppletSchemeCode(String envVersion) {
        try {
            //获取token
            String accessToken = UrlSchemeUtils.getAccessToken(accessTokenUrl, appId, secret);
            log.info("accessToken=========>{}",accessToken);
            JSONObject jumpWxa  = new JSONObject();
            jumpWxa.put("path","pages/recruitHome/index");
            jumpWxa.put("query","");
            jumpWxa.put("env_version",envVersion);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("jump_wxa",jumpWxa);
            jsonObject.put("expire_type",1);
            jsonObject.put("expire_interval",30);
            //获取scheme 码
            String sendPost = UrlSchemeUtils.sendPost(linkURL, accessToken, jsonObject.toJSONString());
            return JSONObject.parseObject(sendPost);
        } catch (Exception e) {
            throw new CommonException("获取小程序 scheme 码发生异常");
        }
    }

    /**
     * 查询工伤的申请列表中的数据
     *
     * @return
     * @param clientId
     */
    private List<ApplicationListDTO> getWorkInjuryApplication(List<String> clientId) {
        //用于搜索的条件
        ArrayList<Integer> stateList = new ArrayList<>();
        stateList.add(WorkInjuryServiceEnum.STAY_REVIEWED.getKey());
        //查询工伤的所有审核的数据
        QueryWrapper<HrWorkInjury> workInjuryQueryWrapper = new QueryWrapper<>();
        workInjuryQueryWrapper.in("status", stateList);
        workInjuryQueryWrapper.in("client_id ", clientId);
        workInjuryQueryWrapper.orderByDesc("created_date");
        workInjuryQueryWrapper.last("limit 5");
        //获取所有符合条件的所有工伤数据
        List<HrWorkInjury> hrWorkInjuryList = hrWorkInjuryRepository.selectList(workInjuryQueryWrapper);
        //将工伤的数据重新进行封装
        ArrayList<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrWorkInjury hrWorkInjury : hrWorkInjuryList) {
            String clientName = getClientName(hrWorkInjury.getClientId());
            //工伤服务返回的数据
            ApplicationListDTO workInjuryApplication = new ApplicationListDTO();
            workInjuryApplication.setType("工伤服务");
            workInjuryApplication.setTitle("来自" + clientName + "工伤申请");
            workInjuryApplication.setDate(hrWorkInjury.getLastModifiedDate());
            workInjuryApplication.setStateList(stateList);
            workInjuryApplication.setJumpPage(ServiceCenterEnum.WORK_INJURY_APPLICATIONS.getKey());
            applicationListDTOArrayList.add(workInjuryApplication);

        }
        return applicationListDTOArrayList;
    }

    /**
     * 根据客户id获取客户名称
     *
     * @param clientId
     * @return
     */
    private String getClientName(String clientId) {
        String clientName = "";
        if (StringUtils.isNotBlank(clientId)) {
            HrClient hrClient = HrClientService.getById(clientId);
            if (hrClient != null) {
                clientName = hrClient.getClientName();
            }
        }

        return clientName;
    }

    /**
     * 查询生育的申请列表中的数据
     *
     * @return
     * @param clientId
     */
    private List<ApplicationListDTO> getFertilityApplication(List<String> clientId) {
        //查询所有审核的数据
        QueryWrapper<HrFertility> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", 0);
        queryWrapper.in("client_id ", clientId);
        queryWrapper.orderByDesc("created_date");
        queryWrapper.last("limit 5");
        //获取所有符合条件的所有数据
        List<HrFertility> list = hrFertilityService.list(queryWrapper);
        //用于搜索的条件
        ArrayList<Integer> stateList = new ArrayList<>();
        stateList.add(0);
        //将数据重新进行封装
        ArrayList<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrFertility hrFertility : list) {
            String clientName = getClientName(hrFertility.getClientId());
            //服务返回的数据
            ApplicationListDTO applicationListDTO = new ApplicationListDTO();
            applicationListDTO.setType("生育服务");
            applicationListDTO.setTitle("来自" + clientName + "生育申请");
            applicationListDTO.setDate(hrFertility.getLastModifiedDate());
            applicationListDTO.setStateList(stateList);
            applicationListDTO.setJumpPage(ServiceCenterEnum.Fertility_APPLICATIONS.getKey());
            applicationListDTOArrayList.add(applicationListDTO);

        }
        return applicationListDTOArrayList;
    }

    /**
     * 查询离职的申请列表中的数据
     *
     * @return
     * @param clientId
     */
    private List<ApplicationListDTO> getDepartureApplication(List<String> clientId,JWTUserDTO jwtUserDTO) {
        //用于搜索的条件
        ArrayList<Integer> stateList = new ArrayList<>();
        UserRoleTypeEnum enumByKey = EnumUtils.getEnumByKey(UserRoleTypeEnum.class, jwtUserDTO.getCurrentRoleKey());
        switch (enumByKey){
            case CLIENT: stateList.add(DepartureServiceEnum.STAY_REVIEWED.getKey()); break;
            case CUSTOMER_SERVICE_MANAGER: stateList.add(DepartureServiceEnum.STAY_MANAGER_REVIEW.getKey()); break;
            default:
                stateList.add(DepartureServiceEnum.STAY_REVIEWED.getKey());
                stateList.add(DepartureServiceEnum.STAY_MANAGER_REVIEW.getKey());
                break;
        }
        //查询所有审核的数据
        QueryWrapper<HrApplyDepartureStaff> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("departure_staff_status", stateList);
        queryWrapper.in("client_id ", clientId);
        queryWrapper.orderByDesc("last_modified_date");
        queryWrapper.last("limit 5");
        //获取所有符合条件的所有数据
        List<HrApplyDepartureStaff> list = hrApplyDepartureStaffService.list(queryWrapper);
        //将数据重新进行封装
        ArrayList<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrApplyDepartureStaff hrApplyDeparture : list) {
            String clientName = getClientName(hrApplyDeparture.getClientId());
            //服务返回的数据
            ApplicationListDTO applicationListDTO = new ApplicationListDTO();
            applicationListDTO.setType("离职服务");
            applicationListDTO.setTitle("来自" + clientName + "离职申请");
            applicationListDTO.setDate(hrApplyDeparture.getLastModifiedDate());
            applicationListDTO.setStateList(stateList);
            applicationListDTO.setJumpPage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
            applicationListDTOArrayList.add(applicationListDTO);

        }
        return applicationListDTOArrayList;
    }

    /**
     * 查询证明开具的申请列表中的数据
     *
     * @return
     * @param clientIdList
     * @param jwtUserDTO
     */
    private List<ApplicationListDTO> getCertificateApplication(List<String> clientIdList, JWTUserDTO jwtUserDTO) {
        List<Integer> stateList = new ArrayList<>();
        UserRoleTypeEnum enumByKey = EnumUtils.getEnumByKey(UserRoleTypeEnum.class, jwtUserDTO.getCurrentRoleKey());
        Integer roleKey = null;
        String title = null;
        switch (enumByKey){
            case CLIENT: stateList.add(CertificateTypeEnum.CertificateStatus.TO_BE_REVIEWED.getKey());break;
            case CUSTOMER_SERVICE_MANAGER: stateList.add(CertificateTypeEnum.CertificateStatus.MANAGER_REVIEW.getKey());break;
            case GENERAL_MANAGER:
                roleKey = 1;title = "户口迁出|落户|档案调函|暂住证";
                stateList.add(CertificateTypeEnum.CertificateStatus.TO_BE_MANAGER_REVIEWED.getKey());break;
            case CUSTOMER_SERVICE_STAFF:
                roleKey = 2;title = "在职|工龄认定";
                stateList.add(CertificateTypeEnum.CertificateStatus.TO_BE_MANAGER_REVIEWED.getKey());break;
            case DAIYUHEGUIZHUANYUAN:
                roleKey = 3;title = "退休|生育|工伤|死亡|职称|派遣制|未婚|初婚未育|介绍信";
                stateList.add(CertificateTypeEnum.CertificateStatus.TO_BE_MANAGER_REVIEWED.getKey());break;
            case FINANCIAL_DIRECTOR:
            case XINCHOUZHUANYUANB:
                roleKey = 4;title = "收入";
                stateList.add(CertificateTypeEnum.CertificateStatus.TO_BE_MANAGER_REVIEWED.getKey());break;
            default:
                stateList.add(CertificateTypeEnum.CertificateStatus.TO_BE_REVIEWED.getKey());
                stateList.add(CertificateTypeEnum.CertificateStatus.MANAGER_REVIEW.getKey());
                stateList.add(CertificateTypeEnum.CertificateStatus.TO_BE_MANAGER_REVIEWED.getKey());
                break;
        }
        //获取所有符合条件的所有数据
        HrCertificateIssuanceDTO issuanceDTO = new HrCertificateIssuanceDTO();
        issuanceDTO.setClientIds(clientIdList)
            .setCertificateStatusList(stateList)
            .setRoleKey(roleKey)
            .setTitle(title);
        List<HrCertificateIssuance> list = hrCertificateIssuanceRepository.selectApplyListByRole(issuanceDTO);
        //将数据重新进行封装
        ArrayList<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrCertificateIssuance hrCertificate : list) {
            String clientName = getClientName(hrCertificate.getClientId());
            //服务返回的数据
            ApplicationListDTO applicationListDTO = new ApplicationListDTO();
            applicationListDTO.setType("证明开具服务");
            applicationListDTO.setTitle("来自" + clientName + "证明开具申请");
            applicationListDTO.setDate(hrCertificate.getLastModifiedDate());
            applicationListDTO.setStateList(stateList);
            applicationListDTO.setJumpPage(ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey());
            applicationListDTOArrayList.add(applicationListDTO);

        }
        return applicationListDTOArrayList;
    }

    /**
     * 查询档案借阅申请列表中的数据
     *
     * @return
     * @param clientId
     * @param jwtUserDTO
     */
    private List<ApplicationListDTO> getLendingApplication(List<String> clientId, JWTUserDTO jwtUserDTO) {
        //用于搜索的条件
        List<Integer> stateList = new ArrayList<>();
        List<Integer> findStateList = new ArrayList<>();
        UserRoleTypeEnum enumByKey = EnumUtils.getEnumByKey(UserRoleTypeEnum.class, jwtUserDTO.getCurrentRoleKey());
        switch (enumByKey){
            case CLIENT:
                stateList.add(LendingApplyEnum.LendingStateEnum.TO_BE_CUSTOMER_REVIEWED.getKey());
                findStateList.add( LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey() );
                break;
            case CUSTOMER_SERVICE_STAFF:
                stateList.add(LendingApplyEnum.LendingStateEnum.TO_BE_MANAGER_REVIEWED.getKey());
                findStateList.add(LendingApplyEnum.LendingApplyType.TO_BE_MANAGER_REVIEWED.getKey() );
                break;
            case GENERAL_MANAGER:
                stateList.add(LendingApplyEnum.LendingStateEnum.TO_BE_FILE_MANAGER_REVIEWED.getKey());
                findStateList.add( LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey() );
                break;
            default:
                stateList.add(LendingApplyEnum.LendingStateEnum.TO_BE_CUSTOMER_REVIEWED.getKey());
                stateList.add(LendingApplyEnum.LendingStateEnum.TO_BE_MANAGER_REVIEWED.getKey());
                stateList.add(LendingApplyEnum.LendingStateEnum.TO_BE_FILE_MANAGER_REVIEWED.getKey());

                findStateList.add( LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey() );
                findStateList.add(LendingApplyEnum.LendingApplyType.TO_BE_MANAGER_REVIEWED.getKey() );
                findStateList.add( LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey() );
                break;
        }
        //查询所有审核的数据
        QueryWrapper<HrLendingApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", findStateList);
        queryWrapper.in("client_id ", clientId);
        queryWrapper.orderByDesc("last_modified_date");
        queryWrapper.last("limit 5");
        //获取所有符合条件的所有数据
        List<HrLendingApply> list = hrLendingApplyService.list(queryWrapper);
        //将数据重新进行封装
        ArrayList<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrLendingApply hrLendingApply : list) {
            String clientName = getClientName(hrLendingApply.getClientId());
            //服务返回的数据
            ApplicationListDTO applicationListDTO = new ApplicationListDTO();
            applicationListDTO.setType("档案借阅服务");
            applicationListDTO.setTitle("来自" + clientName + "档案借阅申请");
            applicationListDTO.setDate(hrLendingApply.getLastModifiedDate());
            applicationListDTO.setStateList(stateList);
            applicationListDTO.setJumpPage(ServiceCenterEnum.LENDING_APPLY.getKey());
            applicationListDTOArrayList.add(applicationListDTO);

        }
        return applicationListDTOArrayList;
    }

    /**
     * 查询资料修改申请列表中的数据
     *
     * @return
     * @param clientId
     */
    private List<ApplicationListDTO> getAuditLnApplication(List<String> clientId) {
        //查询所有审核的数据
        QueryWrapper<HrDataModification> queryWrapper = new QueryWrapper<>();
        //用于搜索的条件
        ArrayList<Integer> stateList = new ArrayList<>();
        stateList.add(0);
        queryWrapper.in("apply_status", stateList);
        queryWrapper.in("client_id ", clientId);
        queryWrapper.orderByDesc("last_modified_date");
        queryWrapper.last("limit 5");
        //获取所有符合条件的所有数据
        List<HrDataModification> list = hrDataModificationService.list(queryWrapper);
        //将数据重新进行封装
        ArrayList<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrDataModification hrDataModification : list) {
            String clientName = getClientName(hrDataModification.getClientId());
            //服务返回的数据
            ApplicationListDTO applicationListDTO = new ApplicationListDTO();
            applicationListDTO.setType("资料修改服务");
            applicationListDTO.setTitle("来自" + clientName + "资料修改申请");
            applicationListDTO.setDate(hrDataModification.getLastModifiedDate());
            applicationListDTO.setStateList(stateList);
            applicationListDTO.setJumpPage(ServiceCenterEnum.DATA_MODIFICATION.getKey());
            applicationListDTOArrayList.add(applicationListDTO);

        }
        return applicationListDTOArrayList;
    }

    /**
     * 查询入职申请列表中的数据
     *
     * @return
     * @param clientId
     */
    private List<ApplicationListDTO> getApprovalApplication(List<String> clientId) {
        //查询所有审核的数据
        QueryWrapper<HrApplyEntry> queryWrapper = new QueryWrapper<>();
        //用于搜索的条件
        ArrayList<Integer> stateList = new ArrayList<>();
        stateList.add(StaffEnum.ApplyStatusEnum.PENDING_REVIEW.getKey());
        queryWrapper.in("apply_status", stateList);
        queryWrapper.in("client_id ", clientId);
        queryWrapper.orderByDesc("last_modified_date");
        queryWrapper.last("limit 5");
        //获取所有符合条件的所有数据
        List<HrApplyEntry> list = hrApplyEntryService.list(queryWrapper);
        //将数据重新进行封装
        ArrayList<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrApplyEntry hrApplyEntryStaff : list) {
            String clientName = getClientName(hrApplyEntryStaff.getClientId());
            //服务返回的数据
            ApplicationListDTO applicationListDTO = new ApplicationListDTO();
            applicationListDTO.setType("入职服务");
            applicationListDTO.setTitle("来自" + clientName + "入职申请");
            applicationListDTO.setDate(hrApplyEntryStaff.getLastModifiedDate());
            applicationListDTO.setStateList(stateList);
            applicationListDTO.setJumpPage(ServiceCenterEnum.ENTRY_APPLICATIONS.getKey());
            applicationListDTOArrayList.add(applicationListDTO);

        }
        return applicationListDTOArrayList;
    }

    /**
     * 查询转正申请列表中的数据
     *
     * @return
     * @param clientId
     */
    private List<ApplicationListDTO> getStaffTurnApplication(List<String> clientId) {
        //如果是客户登录返回null
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String currentRoleKey = jwtUserDTO.getCurrentRoleKey();
        if ("client".equals(currentRoleKey)) {
            return new ArrayList<ApplicationListDTO>();
        }
        //查询所有审核的数据
        QueryWrapper<HrStaffTurnPositive> queryWrapper = new QueryWrapper<>();
        //用于搜索的条件
        //ArrayList<Integer> stateList = new ArrayList<>();
        queryWrapper.eq("states", 1);
        queryWrapper.eq("enterprise_states", 0);
        queryWrapper.in("client_id ", clientId);
        queryWrapper.orderByDesc("created_date");
        queryWrapper.last("limit 5");
        //获取所有符合条件的所有数据
        List<HrStaffTurnPositive> list = hrStaffTurnPositiveRepository.select(queryWrapper);
        //将数据重新进行封装
        ArrayList<ApplicationListDTO> applicationListDTOArrayList = new ArrayList<>();
        for (HrStaffTurnPositive hrStaffTurnPositive : list) {
            HrTalentStaffDTO staffBasicInfo = hrTalentStaffRepository.getStaffBasicInfo(hrStaffTurnPositive.getStaffId());
            String clientName = "";
            if (staffBasicInfo != null) {
                clientName = staffBasicInfo.getClientName();
            }
            //服务返回的数据
            ApplicationListDTO applicationListDTO = new ApplicationListDTO();
            applicationListDTO.setType("转正服务");
            applicationListDTO.setTitle("来自" + clientName + "转正申请");
            applicationListDTO.setDate(hrStaffTurnPositive.getLastModifiedDate());
            applicationListDTO.setJumpPage(ServiceCenterEnum.GET_APPLICATIONS.getKey());
            //applicationListDTO.setStateList(stateList);
            applicationListDTOArrayList.add(applicationListDTO);

        }
        return applicationListDTOArrayList;
    }

}
