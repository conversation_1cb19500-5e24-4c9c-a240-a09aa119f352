package cn.casair.service.impl;

import cn.casair.common.enums.BillInvoiceApproveEnums;
import cn.casair.common.enums.BillReimbApproveEnums;
import cn.casair.common.enums.SpecialBillClient;
import cn.casair.common.utils.BigDecimalCompare;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.EnumUtils;
import cn.casair.domain.HrBillReimbursementApply;
import cn.casair.domain.HrBillReimbursementApplyDetail;
import cn.casair.domain.HrClient;
import cn.casair.dto.HrBillReimbursementApplyDTO;
import cn.casair.dto.HrBillReimbursementApplyDetailDTO;
import cn.casair.dto.HrBillReimbursementClientDTO;
import cn.casair.mapper.HrBillReimbursementApplyDetailMapper;
import cn.casair.repository.HrBillReimbursementApplyDetailRepository;
import cn.casair.repository.HrBillReimbursementApplyRepository;
import cn.casair.repository.HrClientRepository;
import cn.casair.service.HrBillReimbursementApplyDetailService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 报销申请费用明细服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrBillReimbursementApplyDetailServiceImpl extends ServiceImpl<HrBillReimbursementApplyDetailRepository, HrBillReimbursementApplyDetail> implements HrBillReimbursementApplyDetailService {


    private final HrBillReimbursementApplyDetailRepository hrBillReimbursementApplyDetailRepository;
    private final HrBillReimbursementApplyDetailMapper hrBillReimbursementApplyDetailMapper;
    private final HrBillReimbursementApplyRepository hrBillReimbursementApplyRepository;
    private final HrClientRepository hrClientRepository;

    public HrBillReimbursementApplyDetailServiceImpl(HrBillReimbursementApplyDetailRepository hrBillReimbursementApplyDetailRepository, HrBillReimbursementApplyDetailMapper hrBillReimbursementApplyDetailMapper, HrBillReimbursementApplyRepository hrBillReimbursementApplyRepository, HrClientRepository hrClientRepository) {
        this.hrBillReimbursementApplyDetailRepository = hrBillReimbursementApplyDetailRepository;
        this.hrBillReimbursementApplyDetailMapper = hrBillReimbursementApplyDetailMapper;
        this.hrBillReimbursementApplyRepository = hrBillReimbursementApplyRepository;
        this.hrClientRepository = hrClientRepository;
    }

    /**
     * 创建报销申请费用明细
     *
     * @param hrBillReimbursementApplyDetailDTO
     * @return
     */
    @Override
    public HrBillReimbursementApplyDetailDTO createHrBillReimbursementApplyDetail(HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO) {
        log.info("Create new HrBillReimbursementApplyDetail:{}", hrBillReimbursementApplyDetailDTO);

        HrBillReimbursementApplyDetail hrBillReimbursementApplyDetail = this.hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO);
        this.hrBillReimbursementApplyDetailRepository.insert(hrBillReimbursementApplyDetail);
        return this.hrBillReimbursementApplyDetailMapper.toDto(hrBillReimbursementApplyDetail);
    }

    /**
     * 修改报销申请费用明细
     *
     * @param hrBillReimbursementApplyDetailDTO
     * @return
     */
    @Override
    public Optional<HrBillReimbursementApplyDetailDTO> updateHrBillReimbursementApplyDetail(HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO) {
        return Optional.ofNullable(this.hrBillReimbursementApplyDetailRepository.selectById(hrBillReimbursementApplyDetailDTO.getId()))
            .map(roleTemp -> {
                HrBillReimbursementApplyDetail hrBillReimbursementApplyDetail = this.hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO);
                this.hrBillReimbursementApplyDetailRepository.updateById(hrBillReimbursementApplyDetail);
                log.info("Update HrBillReimbursementApplyDetail:{}", hrBillReimbursementApplyDetailDTO);
                return hrBillReimbursementApplyDetailDTO;
            });
    }

    /**
     * 查询报销申请费用明细详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillReimbursementApplyDetailDTO getHrBillReimbursementApplyDetail(String id) {
        log.info("Get HrBillReimbursementApplyDetail :{}", id);

        HrBillReimbursementApplyDetail hrBillReimbursementApplyDetail = this.hrBillReimbursementApplyDetailRepository.selectById(id);
        return this.hrBillReimbursementApplyDetailMapper.toDto(hrBillReimbursementApplyDetail);
    }

    /**
     * 删除报销申请费用明细
     *
     * @param id
     */
    @Override
    public void deleteHrBillReimbursementApplyDetail(String id) {
        Optional.ofNullable(this.hrBillReimbursementApplyDetailRepository.selectById(id))
            .ifPresent(hrBillReimbursementApplyDetail -> {
                this.hrBillReimbursementApplyDetailRepository.deleteById(id);
                log.info("Delete HrBillReimbursementApplyDetail:{}", hrBillReimbursementApplyDetail);
            });
    }

    /**
     * 批量删除报销申请费用明细
     *
     * @param ids
     */
    @Override
    public void deleteHrBillReimbursementApplyDetail(List<String> ids) {
        log.info("Delete HrBillReimbursementApplyDetails:{}", ids);
        this.hrBillReimbursementApplyDetailRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询报销申请费用明细
     *
     * @param hrBillReimbursementApplyDetailDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO, Long pageNumber, Long pageSize) {
        Page<HrBillReimbursementApplyDetail> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrBillReimbursementApplyDetail> qw = new QueryWrapper<>(this.hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrBillReimbursementApplyDetailRepository.selectPage(page, qw);
        iPage.setRecords(this.hrBillReimbursementApplyDetailMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    /**
     * 异步处理审核时上级报销数据
     * @param applyDTO
     * @param type
     */
    @Override
    public void handleParentInvoiceData(HrBillReimbursementApplyDTO applyDTO, Integer type) {
        long l = System.currentTimeMillis();
        log.info("异步处理开始,{}",l);
        List<HrBillReimbursementApplyDetailDTO> detailDTOList = hrBillReimbursementApplyDetailRepository.getByApplyId(applyDTO.getId());
        //审核通过扣减上级金额
        if (applyDTO.getParentId() != null && applyDTO.getApproveStatus().equals(BillReimbApproveEnums.SUCCESS.getKey())){
            BigDecimal reduce = detailDTOList.stream().map(HrBillReimbursementApplyDetailDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            HrBillReimbursementApply hrBillReimbursementApply = hrBillReimbursementApplyRepository.selectById(applyDTO.getParentId());
            hrBillReimbursementApply.setAmount(CalculateUtils.decimalSubtraction(hrBillReimbursementApply.getAmount(),reduce));
                /*//查看上级报销明细发起记录的审核状态
                List<HrBillReimbursementApplyDetailDTO> list = hrBillReimbursementApplyDetailRepository.getDetailStatusByApplyId(applyDTO.getParentId());
                List<HrBillReimbursementApplyDetailDTO> collect = list.stream().filter(ls -> ls.getApproveStatus() != null && ls.getApproveStatus().equals(BillReimbApproveEnums.SUCCESS.getKey())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect) && collect.size() == list.size()){
                    hrBillReimbursementApply.setIsShow(1);
                }*/
            if (BigDecimalCompare.of(hrBillReimbursementApply.getAmount()).eq(BigDecimal.ZERO)){
                hrBillReimbursementApply.setIsShow(1);
            }
            hrBillReimbursementApplyRepository.updateById(hrBillReimbursementApply);
        }
        //审核不通过解锁
        if (applyDTO.getParentId() != null && type == 2){
            HrClient rootParentClient = applyDTO.getClientId() != null ? hrClientRepository.getRootParentClient(applyDTO.getClientId()) : null;
            hrBillReimbursementApplyRepository.updateLockState(applyDTO.getParentId(), BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey());
            for (HrBillReimbursementApplyDetailDTO detailDTO : detailDTOList) {
                if (applyDTO.getAccountType() != null && applyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())){
                    //特殊客户解除明细账单对应锁定状态
                    List<String> ids = Arrays.asList(detailDTO.getId(),detailDTO.getParentId());
                    hrBillReimbursementApplyDetailRepository.updateState(ids, BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey());
                    this.updateDetailClientLock(detailDTO.getId(),detailDTO.getInvoiceType(),BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey(), rootParentClient);
                }else {
                    HrBillReimbursementApplyDetail applyDetail = hrBillReimbursementApplyDetailRepository.selectOne(new QueryWrapper<HrBillReimbursementApplyDetail>()
                        .eq("apply_id", applyDTO.getParentId()).eq("level_id", detailDTO.getId()).orderByDesc("created_date").last("LIMIT 1"));
                    List<String> ids = Arrays.asList(detailDTO.getId(),applyDetail.getId());
                    hrBillReimbursementApplyDetailRepository.updateState(ids, BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey());
                }
            }
        }
        log.info("异步处理结束,{}",System.currentTimeMillis() - l);
    }

    /**
     * 特殊客户处理明细账单对应锁定状态
     * @param detailId 报销明细ID
     * @param invoiceType 报销项类型
     * @param lockState 锁定状态
     * @param rootParentClient 顶级客户信息
     */
    @Override
    public void updateDetailClientLock(String detailId, Integer invoiceType, Integer lockState, HrClient rootParentClient) {
        List<HrBillReimbursementClientDTO> dtoList = hrBillReimbursementApplyDetailRepository.getDetailClientById(Collections.singletonList(detailId),null);
        List<String> detailClientIds = dtoList.stream().map(HrBillReimbursementClientDTO::getClientBillId).collect(Collectors.toList());
        BillReimbApproveEnums.InvoiceTypeEnum enumByKey = EnumUtils.getEnumByKey(BillReimbApproveEnums.InvoiceTypeEnum.class, invoiceType);
        switch (enumByKey){
            case DF_SALARY:
                if (rootParentClient != null && !rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())){
                    hrBillReimbursementApplyRepository.updateClientLock(lockState,null,null,null,detailClientIds);
                }
                break;
            case DF_SOCIAL_SECURITY:
                hrBillReimbursementApplyRepository.updateClientLock(null, lockState,null,null,detailClientIds);
                break;
            case DF_MEDICAL_INSURANCE:
                hrBillReimbursementApplyRepository.updateClientLock(null,null, lockState,null,detailClientIds);
                break;
            case DF_ACCUMULATION_FOUND:
                hrBillReimbursementApplyRepository.updateClientLock(null,null,null, lockState,detailClientIds);
                break;
            default:break;
        }
    }

}
