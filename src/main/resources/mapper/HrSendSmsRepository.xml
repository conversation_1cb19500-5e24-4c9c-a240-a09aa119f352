<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrSendSmsRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, send_phone, send_name, send_content, send_title, respons_body, iz_succeed,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrSendSms">
                    <id column="id" property="id"/>
                    <result column="send_phone" property="sendPhone"/>
                    <result column="send_name" property="sendName"/>
                    <result column="send_content" property="sendContent"/>
                    <result column="send_title" property="sendTitle"/>
                    <result column="respons_body" property="responsBody"/>
                    <result column="iz_succeed" property="izSucceed"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

</mapper>
