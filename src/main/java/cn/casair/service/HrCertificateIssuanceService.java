package cn.casair.service;

import cn.casair.domain.HrAppendix;
import cn.casair.domain.HrCertificateIssuance;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrCertificateIssuanceDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 证明开具服务类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
public interface HrCertificateIssuanceService extends IService<HrCertificateIssuance> {


    /**
     * 创建证明开具
     *
     * @param hrCertificateIssuanceDTO
     * @param requestFromWx 是否是来自小程序的请求
     * @return
     */
    HrCertificateIssuanceDTO createHrCertificateIssuance(HrCertificateIssuanceDTO hrCertificateIssuanceDTO, Boolean requestFromWx);

    /**
     * 修改证明开具
     *
     * @param hrCertificateIssuanceDTO
     * @return
     */
    Optional<HrCertificateIssuanceDTO> updateHrCertificateIssuance(HrCertificateIssuanceDTO hrCertificateIssuanceDTO);

    /**
     * 查询证明开具详情
     *
     * @param id
     * @return
     */
    HrCertificateIssuanceDTO getHrCertificateIssuance(String id);

    /**
     * 删除证明开具
     *
     * @param id
     */
    void deleteHrCertificateIssuance(String id);

    /**
     * 批量删除证明开具
     *
     * @param ids
     */
    void deleteHrCertificateIssuance(List<String> ids);

    /**
     * 分页查询证明开具
     *
     * @param hrCertificateIssuanceDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrCertificateIssuanceDTO hrCertificateIssuanceDTO, Long pageNumber, Long pageSize);

    /**
     * 开具完成，线下签章
     * @param batchOptDTO
     * @return
     */
    ResponseEntity issueCompleteCertificate(BatchOptDTO batchOptDTO);

    /**
     * 员工查看证明开具
     * @return
     */
    List<HrCertificateIssuanceDTO> seeCertificateIssuance();

    /**
     * 上传文档，电子盖章
     * @param params
     * @return
     */
    HrAppendix uploadDocumentFile(Map<String, Object> params);

    /**
     * 导出证明开具
     * @param hrCertificateIssuanceDTO
     * @param response
     * @return
     */
    String exportCertificateIssuance(HrCertificateIssuanceDTO hrCertificateIssuanceDTO, HttpServletResponse response);

    /**
     * 批量操作
     * @param batchOptDTO
     * @return
     */
    ResponseEntity batchPassCertificate(BatchOptDTO batchOptDTO);

    /**
     * 专管员审核
     * @param batchOptDTO
     * @return
     */
    HrCertificateIssuanceDTO reviewedSpecialSupervisor(BatchOptDTO batchOptDTO);

    /**
     * 预处理证明模板文件
     * @param hrCertificateIssuanceDTO
     * @return
     */
    Map<String, Object> proofTemplatePretreatment(HrCertificateIssuanceDTO hrCertificateIssuanceDTO);

    /**
     * 一键生成证明模板
     * @param params
     * @return
     */
    HrAppendix makeTemplate(Map<String, Object> params);

}
