package cn.casair.repository;

import cn.casair.domain.HrBill;
import cn.casair.domain.HrBillTotal;
import cn.casair.dto.HrArrivalRecordDTO;
import cn.casair.dto.HrBillTotalDTO;
import cn.casair.dto.excel.HrBillTotalExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 账单汇总数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Repository
public interface HrBillTotalRepository extends BaseMapper<HrBillTotal> {

    /**
     * 统计报表导出
     *
     * @param hrBillTotalDTO
     * @param clientIds
     * @return java.util.List<cn.casair.dto.excel.HrBillTotalExport>
     * <AUTHOR>
     * @date 2021/11/22
     **/
    List<HrBillTotalExport> getBillTotalExportList(@Param("params") HrBillTotalDTO hrBillTotalDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 获取客户年度曲线
     *
     * @param hrBillTotalDTO
     * @return java.util.List<cn.casair.dto.HrBillTotalDTO>
     * <AUTHOR>
     * @date 2021/11/17
     **/
    List<HrBillTotalDTO> getClientAnnualCurve(@Param("params") HrBillTotalDTO hrBillTotalDTO);

    /**
     * 获取客户公司中员工的计算相关的参数
     *
     * @param billId
     * @return
     */
    HrBillTotal createByBillId(@Param("billId") String billId);

    @Update("UPDATE hr_bill_total ht SET ht.is_delete = 1 WHERE ht.bill_id = #{billId}")
    void deleteByBillId(String billId);

    /**
     * 获取客户缴费年月已经审核通过的账单统计
     *
     * @param hrArrivalRecordDTO
     * @return cn.casair.dto.HrBillTotalDTO
     * <AUTHOR>
     * @date 2021/11/16
     **/
    HrBillTotalDTO getClientPaymentDateTotalAmount(@Param("params") HrArrivalRecordDTO hrArrivalRecordDTO);

    /**
     * 分页查询账单汇总
     *
     * @param page
     * @param hrBillTotalDTO
     * @param clientIds
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrBillTotalDTO>
     * <AUTHOR>
     * @date 2021/11/17
     **/
    IPage<HrBillTotalDTO> findPage(Page<HrBillTotalDTO> page, @Param("params") HrBillTotalDTO hrBillTotalDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 获取数据统计
     *
     * @param hrBillTotalDTO
     * @param clientIds
     * @return
     */
    List<HrBillTotalDTO> getBillStatistics(@Param("params") HrBillTotalDTO hrBillTotalDTO, @Param("permissionClient") List<String> clientIds);

    IPage<HrBillTotalDTO> getBillTotalPageByClient(Page<HrBill> page, @Param("clientId") String clientId);

    HrBillTotalDTO getByClientIdAndDate(@Param("clientId") String clientId, @Param("payYear") Integer payYear, @Param("payMonthly") Integer payMonthly);

    /**
     * 获取多个账单的汇总账单
     * @param billIdList
     * @return
     */
    HrBillTotalDTO getBillTotalByBatchBill(@Param("billIdList") List<String> billIdList);

    HrBillTotal saveByBillId(@Param("billId") String billId);

    /**
     * 获取到账记录金额
     * @param billTotalDTO
     * @return
     */
    List<HrBillTotalDTO> getClientAccountsReceivable(@Param("params") HrBillTotalDTO billTotalDTO);

    /**
     * 中石化账单汇总统计
     * @param billId 账单ID
     * @param projectType 项目部类型
     * @return
     */
    HrBillTotal statisticCount(@Param("billIds") List<String> billId, @Param("projectType") Integer projectType);

    /**
     * 查询账单汇总数据合计
     * @param hrBillTotalDTO
     * @param clientIds
     * @return
     */
    HrBillTotalDTO getTotalData(@Param("params") HrBillTotalDTO hrBillTotalDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 根据账单ID查询账单汇总
     * @param billIds
     * @return
     */
    List<HrBillTotalDTO> getBillTotalByBillId(@Param("billIds") List<String> billIds);

    /**
     * 获取工资汇总
     * @param billIdList
     * @return
     */
    HrBillTotalDTO getBillTotalBySalaryBill(@Param("billIdList") List<String> billIdList);

}
