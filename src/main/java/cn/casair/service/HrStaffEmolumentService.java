package cn.casair.service;

import cn.casair.domain.HrStaffEmolument;
import cn.casair.dto.HrEmployeeWelfareDTO;
import cn.casair.dto.HrStaffEmolumentDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 薪酬参数服务类
 *
 * <AUTHOR>
 * @since 2021-09-04
 */
public interface HrStaffEmolumentService extends IService<HrStaffEmolument> {


    /**
     * 查询员工银行卡信息
     *
     * @param staffId
     * @return
     */
    HrStaffEmolumentDTO selectStaffSalaryBankInfo(String staffId);

    /**
     * 更新员工薪酬参数
     *
     * @param hrStaffEmolument
     * @return int
     * <AUTHOR>
     * @date 2021/10/21
     **/
    int updateStaffEmolumentByStaffId(HrStaffEmolument hrStaffEmolument);

    /**
     * 根据员工id获取薪酬参数
     *
     * @param staffIds
     * @return java.util.List<cn.casair.domain.HrStaffEmolument>
     * <AUTHOR>
     * @date 2021/10/20
     **/
    List<HrStaffEmolument> getHrStaffEmolumentListByIds(List<String> staffIds);

    /**
     * 根据员工id获取薪酬参数
     *
     * @param staffId
     * @return cn.casair.domain.HrStaffEmolument
     * <AUTHOR>
     * @date 2021/10/19
     **/
    HrStaffEmolument getHrStaffEmolumentByStaffId(String staffId);

    /**
     * 根据staffId更新薪酬参数
     *
     * @param hrEmployeeWelfareDTO
     * @return int
     * <AUTHOR>
     * @date 2021/10/18
     **/
    int updateByStaffId(HrEmployeeWelfareDTO hrEmployeeWelfareDTO);

    /**
     * 创建薪酬参数
     *
     * @param hrStaffEmolumentDTO
     * @return
     */
    HrStaffEmolumentDTO createHrStaffEmolument(HrStaffEmolumentDTO hrStaffEmolumentDTO);

    /**
     * 计算工龄工资
     * @param params 入职时间、工资工龄基数
     * @return 工龄工资
     */
    Map<String,Object> calculateSenioritySalary(Map<String, Object> params);

    /**
     * 修改薪酬参数
     *
     * @param hrStaffEmolumentDTO
     * @return
     */
    Optional<HrStaffEmolumentDTO> updateHrStaffEmolument(HrStaffEmolumentDTO hrStaffEmolumentDTO);

    /**
     * 查询薪酬参数详情
     *
     * @param staffId 员工ID
     * @return
     */
    HrStaffEmolumentDTO getHrStaffEmolument(String staffId);

    /**
     * 删除薪酬参数
     *
     * @param id
     */
    void deleteHrStaffEmolument(String id);

    /**
     * 批量删除薪酬参数
     *
     * @param ids
     */
    void deleteHrStaffEmolument(List<String> ids);

    /**
     * 分页查询薪酬参数
     *
     * @param hrStaffEmolumentDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrStaffEmolumentDTO hrStaffEmolumentDTO, Long pageNumber, Long pageSize);

}
