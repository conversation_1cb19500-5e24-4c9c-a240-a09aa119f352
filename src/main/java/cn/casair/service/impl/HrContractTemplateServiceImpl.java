package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.CompanyInfoEnum;
import cn.casair.common.enums.ContractEnum;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.DateUtils;
import cn.casair.common.utils.PdfUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.common.utils.StringUtil;
import cn.casair.domain.HrAppendix;
import cn.casair.domain.HrContract;
import cn.casair.domain.HrContractAppendix;
import cn.casair.domain.HrContractTemplate;
import cn.casair.dto.HrContractTemplateDTO;
import cn.casair.dto.formdata.*;
import cn.casair.dto.pdf.ContractInfoForPdf;
import cn.casair.mapper.HrContractTemplateMapper;
import cn.casair.repository.HrContractTemplateRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrContractTemplateService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 合同模板服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrContractTemplateServiceImpl extends ServiceImpl<HrContractTemplateRepository, HrContractTemplate> implements HrContractTemplateService {

    @Value("${file.temp-path}")
    private String tempPath;

    private final RedisCache redisCache;
    private final SysOperLogService sysOperLogService;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrAppendixService hrAppendixService;
    private final HrContractTemplateRepository hrContractTemplateRepository;
    private final HrContractTemplateMapper hrContractTemplateMapper;

    @Override
    public int usageCountPlus(String templateId) {
        return this.hrContractTemplateRepository.usageCountPlus(templateId);
    }

    @Override
    public HrAppendix makeTemplate(HrContractAppendix hrContractAppendix) {
        Map<String, Object> templateMap = new LinkedHashMap<>();
        if (StringUtils.isNotBlank(hrContractAppendix.getFirstPartInfo())) {
            Map<String, Object> firstPartInfo = this.dealFormFieldListToMap(hrContractAppendix.getFirstPartInfo());
            firstPartInfo.forEach((k, v) -> this.dealFormFieldData(k, String.valueOf(v), templateMap));
        }
        if (StringUtils.isNotBlank(hrContractAppendix.getSecondsPartInfo())) {
            Map<String, Object> secondPartInfo = this.dealFormFieldListToMap(hrContractAppendix.getSecondsPartInfo());
            secondPartInfo.forEach((k, v) -> this.dealFormFieldData(k, String.valueOf(v), templateMap));
        }
        return this.fillContentToPdf(templateMap, hrContractAppendix.getOriginalPath());
    }

    /**
     * list表单域转为map
     *
     * @return
     */
    private Map<String, Object> dealFormFieldListToMap(String formFieldListStr) {
        List<InputLabel> inputLabelList = JSON.parseArray(formFieldListStr, InputLabel.class);
        Map<String, Object> mapInfo = new HashMap<>();
        inputLabelList.forEach(l -> mapInfo.put(l.getName(), l.getValue()));
        return mapInfo;
    }


    @Override
    public HrAppendix makeTemplate(Map<String, Object> params) {
        String templateId = String.valueOf(params.get("templateId"));
        if (StringUtils.isBlank(templateId)) {
            throw new CommonException("模板id不能为空！");
        }
        HrContractTemplate hrContractTemplate = this.hrContractTemplateRepository.selectById(templateId);

        Map<String, Object> templateMap = new LinkedHashMap<>();
        // 判断表单域
        if (params.get("inputLabel") != null) {
            List<InputLabel> labels = new LinkedList<>();
            Map<String, Object> tempMap;
            // 获取表单信息(前台后台的入参不同，前台为map，后台为list)
            try {
                labels = JSON.parseArray(JSON.toJSONString(params.get("inputLabel")), InputLabel.class);
            } catch (Exception e) {
                // param中的参数为前台参数，需要将map处理为list
                tempMap = JSON.parseObject(JSON.toJSONString(params.get("inputLabel")), Map.class);
                for (Map.Entry<String, Object> entry : tempMap.entrySet()) {
                    InputLabel inputLabel = new InputLabel();
                    inputLabel.setName(entry.getKey());
                    inputLabel.setValue(entry.getValue());
                    labels.add(inputLabel);
                }
            }
            this.handleInputLabelMap(templateMap, labels);
        }
        return this.fillContentToPdf(templateMap, hrContractTemplate.getTemplatePath());
    }

    /**
     * 填充内容到PDF并上传
     *
     * @param templateMap
     * @param templatePath
     * @return
     */
    private HrAppendix fillContentToPdf(Map<String, Object> templateMap, String templatePath) {
        // 填充内容到PDF
        String tempPdfPath = PdfUtils.fillTextOrImageToPdf(templateMap, templatePath, tempPath, true);
        if (StringUtils.isBlank(tempPdfPath)) {
            throw new CommonException("PDF文件制作异常！");
        }
        // 上传到文件服务器
        return this.hrAppendixService.uploadTempPdf(tempPdfPath);
    }


    @Override
    public List<HrContractTemplateDTO> getContractTemplateList() {
        List<HrContractTemplateDTO> hrContractTemplateList = this.hrContractTemplateRepository.getContractTemplateList();
        hrContractTemplateList.forEach(ls -> ls.setTypeStr(ContractEnum.TemplateType.getValueByKey(ls.getType())));
        return hrContractTemplateList;
    }

    /**
     * 创建
     *
     * @param hrContractTemplateDTO
     * @return
     */
    @Override
    public HrContractTemplateDTO createHrContractTemplate(HrContractTemplateDTO hrContractTemplateDTO) {
        if (hrContractTemplateDTO.getId() != null) {
            throw new CommonException("hrContractTemplate", "添加时不能设置Id");
        }
        // 检测模板名称是否重复
        QueryWrapper<HrContractTemplate> qw = new QueryWrapper<>();
        qw.eq("title", hrContractTemplateDTO.getTitle());
        List<HrContractTemplate> hrContractTemplates = this.hrContractTemplateRepository.selectList(qw);
        if (!hrContractTemplates.isEmpty()) {
            throw new CommonException("该模板标题已存在！");
        }
        // 检测PDF模板文件表单域
        Set<String> formFieldList = PdfUtils.getAllFormField(hrContractTemplateDTO.getTemplatePath());
        if (formFieldList == null || formFieldList.isEmpty()) {
            throw new CommonException("请上传有效的PDF模板！");
        }

        hrContractTemplateDTO.setUpdateTime(LocalDateTime.now());
        HrContractTemplate hrContractTemplate = this.hrContractTemplateMapper.toEntity(hrContractTemplateDTO);
        this.hrContractTemplateRepository.insert(hrContractTemplate);

        this.setCache(hrContractTemplate, formFieldList);

        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.CONTRACT_TEMPLATE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrContractTemplateDTO),
            HrContractTemplateDTO.class,
            hrContractTemplateDTO.getTemplatePath(),
            JSON.toJSONString(hrContractTemplate)
        );

        return this.hrContractTemplateMapper.toDto(hrContractTemplate);
    }

    /**
     * 设置表单域缓存
     *
     * @param hrContractTemplate
     * @param formFieldList
     */
    private void setCache(HrContractTemplate hrContractTemplate, Set<String> formFieldList) {
        String redisKey = RedisKeyEnum.currencyKey.PDF_FORM_FIELD.getValue() + hrContractTemplate.getId();
        this.redisCache.setCacheSet(redisKey, formFieldList);

        String redisKey1 = RedisKeyEnum.currencyKey.PDF_SIGN_REGION.getValue() + hrContractTemplate.getId() + ":" + ContractEnum.FormField.firstPartSign.getKey();
        SignRegionDTO signRegion1 = PdfUtils.getPdfSignRegion(hrContractTemplate.getTemplatePath(), ContractEnum.FormField.firstPartSign.getKey());
        if (signRegion1 != null) {
            this.redisCache.setCacheObject(redisKey1, JSON.toJSONString(signRegion1));
        }

        String redisKey2 = RedisKeyEnum.currencyKey.PDF_SIGN_REGION.getValue() + hrContractTemplate.getId() + ":" + ContractEnum.FormField.secondPartSign.getKey();
        SignRegionDTO signRegion2 = PdfUtils.getPdfSignRegion(hrContractTemplate.getTemplatePath(), ContractEnum.FormField.secondPartSign.getKey());
        if (signRegion2 != null) {
            this.redisCache.setCacheObject(redisKey2, JSON.toJSONString(signRegion2));
        }
    }

    /**
     * 修改
     *
     * @param hrContractTemplateDTO
     * @return
     */
    @Override
    public Optional<HrContractTemplateDTO> updateHrContractTemplate(HrContractTemplateDTO hrContractTemplateDTO) {
        return Optional.ofNullable(this.hrContractTemplateRepository.selectById(hrContractTemplateDTO.getId()))
                .map(roleTemp -> {
                    HrContractTemplate hrContractTemplate = this.hrContractTemplateMapper.toEntity(hrContractTemplateDTO);
                    this.hrContractTemplateRepository.updateById(hrContractTemplate);
                    // 检测模板名称是否重复
                    QueryWrapper<HrContractTemplate> qw = new QueryWrapper<>();
                    qw.eq("title", hrContractTemplateDTO.getTitle());
                    List<HrContractTemplate> hrContractTemplates = this.hrContractTemplateRepository.selectList(qw);
                    if (hrContractTemplates.size() > 1) {
                        throw new CommonException("此类型模板标题已存在！");
                    }
                    // 检测PDF模板文件表单域
                    Set<String> formFieldList = PdfUtils.getAllFormField(hrContractTemplateDTO.getTemplatePath());
                    if (formFieldList == null || formFieldList.isEmpty()) {
                        throw new CommonException("请上传有效的PDF模板！");
                    }

                    this.setCache(hrContractTemplate, formFieldList);

                    // 操作日志
                    this.sysOperLogService.insertSysOperLog(
                        ModuleTypeEnum.CONTRACT_TEMPLATE.getValue(),
                        BusinessTypeEnum.UPDATE.getKey(),
                        JSON.toJSONString(hrContractTemplateDTO),
                        HrContractTemplateDTO.class,
                        hrContractTemplateDTO.getTemplatePath(),
                        JSON.toJSONString(roleTemp),
                        JSON.toJSONString(hrContractTemplateDTO),
                        null,
                        HrContractTemplateDTO.class
                    );

                    return hrContractTemplateDTO;
                });
    }

    @Override
    public FormFieldDealDTO getHrContractTemplate(HrContract hrContract, HrContractTemplate hrContractTemplate, boolean contractStartDateFlag) {
        // 入职合同会走这里 单人续签会走这里
        FormFieldDealDTO result = new FormFieldDealDTO();
        // 甲方表单
        List<InputLabel> firstPartLabels = new LinkedList<>();
        // 乙方表单
        List<InputLabel> secondPartLabels = new LinkedList<>();
        result.setTemplateId(hrContractTemplate.getId());
        result.setStaffId(hrContract.getStaffId());
        result.setType(hrContractTemplate.getType());
        result.setTemplatePath(hrContractTemplate.getTemplatePath());
        result.setContractNo(RandomUtil.generateContractNo());
        // 获取表单域
        Set<String> formFieldList = this.getFormFieldList(hrContractTemplate);

        ContractInfoForPdf contractInfo = this.hrTalentStaffRepository.selectSignInfoByStaffId(hrContract.getStaffId());
        LocalDate startDate = hrContract.getContractStartDate();
        LocalDate endDate = hrContract.getContractEndDate();
        // 判断模板类型
        ContractEnum.TemplateType templateTypeEnum = ContractEnum.TemplateType.getEnumByKey(hrContractTemplate.getType());
        switch (Objects.requireNonNull(templateTypeEnum)) {
            // 劳动合同
            case LABOR_CONTRACT:
                int contractDeadlineMonth = DateUtils.localDateBetweenMonth(startDate, endDate);
                int deadlineYears = BigDecimal.valueOf(contractDeadlineMonth).divide(BigDecimal.valueOf(12), 0, BigDecimal.ROUND_UP).intValue();
                Integer probationDeadlineMonth = null;
                LocalDate probationEndDate = contractInfo.getInternshipDate();
                if (probationEndDate != null) {
                    probationDeadlineMonth = DateUtils.localDateBetweenMonth(startDate, probationEndDate);
                }

                // 处理员工通信地址
                Map<String, String> addressMap = StringUtil.addressResolution(contractInfo.getSecondPartAddress());
                // 遍历模板表单域 填充默认数据
                Integer finalProbationDeadlineMonth = probationDeadlineMonth;
                formFieldList.forEach(field -> {
                    Object o = null;
                    switch (field) {
                        case "contractNo":
                            o = result.getContractNo();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractNo.getName()).setName(ContractEnum.FormField.contractNo.getKey()).setValue(o).setDisabled(true).setSort(1));
                            break;
                        case "firstPartName":
                            o = CompanyInfoEnum.FIRST_PART_NAME.getValue();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartName.getName()).setName(ContractEnum.FormField.firstPartName.getKey()).setValue(o).setSort(2));
                            break;
                        case "legalPersonName":
                            o = CompanyInfoEnum.LEGAL_PERSON_NAME.getValue();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.legalPersonName.getName()).setName(ContractEnum.FormField.legalPersonName.getKey()).setValue(o).setSort(3));
                            break;
                        case "firstPartResidence":
                            o = CompanyInfoEnum.FIRST_PART_RESIDENCE.getValue();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartResidence.getName()).setName(ContractEnum.FormField.firstPartResidence.getKey()).setValue(o).setSort(4));
                            break;
                        case "firstPartPhone":
                            o = CompanyInfoEnum.FIRST_PART_PHONE.getValue();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartPhone.getName()).setName(ContractEnum.FormField.firstPartPhone.getKey()).setValue(o).setSort(5));
                            break;
                        case "secondPartName":
                            o = contractInfo.getSecondPartName();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartName.getName()).setName(ContractEnum.FormField.secondPartName.getKey()).setValue(o).setDisabled(true).setSort(6));
                            break;
                        case "secondPartIdNo":
                            o = contractInfo.getSecondPartIdNo();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdNo.getName()).setName(ContractEnum.FormField.secondPartIdNo.getKey()).setValue(o).setDisabled(true).setSort(7));
                            break;
                        case "secondPartSex":
                            o = contractInfo.getSecondPartSex();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSex.getName()).setName(ContractEnum.FormField.secondPartSex.getKey()).setValue(o).setDisabled(true).setSort(8));
                            break;
                        case "sex":
                            o = contractInfo.getSecondPartSex();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.sex.getName()).setName(ContractEnum.FormField.sex.getKey()).setValue(o).setDisabled(true).setSort(8));
                            break;
                        case "birthday":
                            o = contractInfo.getBirthday();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBirthday.getName()).setName(ContractEnum.FormField.secondPartBirthday.getKey()).setValue(o).setDisabled(true).setSort(9));
                            break;
                        case "secondPartHometown":
                            o = contractInfo.getSecondPartHometown();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartHometown.getName()).setName(ContractEnum.FormField.secondPartHometown.getKey()).setValue(o).setSort(9));
                            break;
                        case "secondPartAddress":
                            o = contractInfo.getSecondPartAddress();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartAddress.getName()).setName(ContractEnum.FormField.secondPartAddress.getKey()).setValue(o).setSort(10));
                            String addressPrefix = null;
                            String addressDetail = null;
                            if (addressMap != null && addressMap.size() > 0) {
                                String province = addressMap.get("province");
                                String city = addressMap.get("city");
                                String county = addressMap.get("county");
                                addressPrefix = (province == null ? "" : province) + (city == null ? "" : city) + (county == null ? "" : county);
                                // 详细地址
                                String town = addressMap.get("town");
                                String village = addressMap.get("village");
                                String houseNumber = addressMap.get("houseNumber");
                                addressDetail = (town == null ? "" : town) + (village == null ? "" : village) + (houseNumber == null ? "" : houseNumber);
                            }
                            secondPartLabels.add(new InputLabel().setLabel("通讯地址(省市区)").setName("secondPartAddressPrefix").setValue(addressPrefix).setType("change").setRequired(true).setSort(11));
                            secondPartLabels.add(new InputLabel().setLabel("通讯地址(详细地址)").setName("secondPartAddressDetail").setValue(addressDetail).setRequired(true).setSort(12));
                            break;
                        case "secondPartIdProvince":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdProvince.getName()).setName(ContractEnum.FormField.secondPartIdProvince.getKey()).setValue(addressMap == null ? o : addressMap.get("province")).setType("change").setRequired(true).setSort(11));
                            break;
                        case "secondPartIdCity":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdCity.getName()).setName(ContractEnum.FormField.secondPartIdCity.getKey()).setValue(addressMap == null ? o : addressMap.get("city")).setType("change").setRequired(true).setSort(12));
                            break;
                        case "secondPartIdCounty":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdCounty.getName()).setName(ContractEnum.FormField.secondPartIdCounty.getKey()).setValue(addressMap == null ? o : addressMap.get("county")).setType("change").setRequired(true).setSort(13));
                            break;
                        case "secondPartIdTown":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdTown.getName()).setName(ContractEnum.FormField.secondPartIdTown.getKey()).setValue(addressMap == null ? o : addressMap.get("town")).setRequired(true).setSort(14));
                            break;
                        case "secondPartIdVillage":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdVillage.getName()).setName(ContractEnum.FormField.secondPartIdVillage.getKey()).setValue(addressMap == null ? o : addressMap.get("village")).setRequired(true).setSort(15));
                            break;
                        case "secondPartHouseNo":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartHouseNo.getName()).setName(ContractEnum.FormField.secondPartHouseNo.getKey()).setValue(addressMap == null ? o : addressMap.get("houseNumber")).setRequired(true).setSort(16));
                            break;
                        case "secondPartBuildingNo":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBuildingNo.getName()).setName(ContractEnum.FormField.secondPartBuildingNo.getKey()).setValue(null).setRequired(true).setSort(17));
                            break;
                        case "secondPartBuildingUnitNo":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBuildingUnitNo.getName()).setName(ContractEnum.FormField.secondPartBuildingUnitNo.getKey()).setValue(null).setRequired(true).setSort(18));
                            break;
                        case "secondPartBuildingRoomNo":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBuildingRoomNo.getName()).setName(ContractEnum.FormField.secondPartBuildingRoomNo.getKey()).setValue(null).setRequired(true).setSort(19));
                            break;
                        case "secondPartPhone":
                            o = contractInfo.getSecondPartPhone();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartPhone.getName()).setName(ContractEnum.FormField.secondPartPhone.getKey()).setValue(o).setDisabled(true).setSort(20));
                            break;
                        case "secondPartFamily":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartFamily.getName()).setName(ContractEnum.FormField.secondPartFamily.getKey()).setValue(o).setSort(21));
                            break;
                        case "secondPartZipCode":
                            o = contractInfo.getSecondPartZipCode();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartZipCode.getName()).setName(ContractEnum.FormField.secondPartZipCode.getKey()).setValue(o).setSort(22));
                            break;
                        case "contractDeadlineYears":
                            o = deadlineYears;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDeadlineYears.getName()).setName(ContractEnum.FormField.contractDeadlineYears.getKey()).setValue(o).setSort(6).setSort(23));
                            break;
                        case "contractStartDateYear":
                            o = startDate;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractStartDate.getName()).setName(ContractEnum.FormField.contractStartDate.getKey()).setValue(o).setType("date").setDisabled(contractStartDateFlag).setSort(24));
                            break;
                        case "contractEndDateYear":
                            o = endDate;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractEndDate.getName()).setName(ContractEnum.FormField.contractEndDate.getKey()).setValue(o).setType("date").setSort(25));
                            break;
                        case "contractDeadlineMonth":
                            o = contractDeadlineMonth;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDeadlineMonth.getName()).setName(ContractEnum.FormField.contractDeadlineMonth.getKey()).setValue(o).setSort(26));
                            break;
                        case "sendStartDateYear":
                            o = startDate;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.sendStartDate.getName()).setName(ContractEnum.FormField.sendStartDate.getKey()).setValue(o).setType("date").setSort(27));
                            break;
                        case "probation":
                            o = finalProbationDeadlineMonth;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.probation.getName()).setName(ContractEnum.FormField.probation.getKey()).setValue(o).setSort(28));
                            break;
                        case "probationStartDateYear":
                            if(finalProbationDeadlineMonth!=null){
                                o = startDate;
                            }
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.probationStartDate.getName()).setName(ContractEnum.FormField.probationStartDate.getKey()).setValue(o).setType("date").setSort(29));
                            break;
                        case "probationEndDateYear":
                            o = probationEndDate;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.probationEndDate.getName()).setName(ContractEnum.FormField.probationEndDate.getKey()).setValue(o).setType("date").setSort(30));
                            break;
                        case "employerName":
                            o = contractInfo.getEmployerName();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.employerName.getName()).setName(ContractEnum.FormField.employerName.getKey()).setValue(o).setSort(31));
                            break;
                        case "positionName":
                            o = contractInfo.getPostName();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.positionName.getName()).setName(ContractEnum.FormField.positionName.getKey()).setValue(o).setSort(32));
                            break;
                        case "workPlace":
                            o = contractInfo.getWorkPlace();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workPlace.getName()).setName(ContractEnum.FormField.workPlace.getKey()).setValue(o).setSort(33));
                            break;
                        case "workPlaceTownOrStreet":
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workPlaceTownOrStreet.getName()).setName(ContractEnum.FormField.workPlaceTownOrStreet.getKey()).setValue(o).setSort(34));
                            break;
                        case "workplaceVillageOrCommunity":
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workplaceVillageOrCommunity.getName()).setName(ContractEnum.FormField.workplaceVillageOrCommunity.getKey()).setValue(o).setSort(35));
                            break;
                        case "workDaysPerWeek":
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workDaysPerWeek.getName()).setName(ContractEnum.FormField.workDaysPerWeek.getKey()).setValue(o).setSort(36));
                            break;
                        case "workHoursPerDay":
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workHoursPerDay.getName()).setName(ContractEnum.FormField.workHoursPerDay.getKey()).setValue(o).setSort(37));
                            break;
                        case "salary":
                            o = contractInfo.getSalary();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.salary.getName()).setName(ContractEnum.FormField.salary.getKey()).setValue(o).setSort(38));
                            break;
                        case "dailyWage":
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.dailyWage.getName()).setName(ContractEnum.FormField.dailyWage.getKey()).setValue(o).setSort(39));
                            break;
                        case "payday":
                            o = contractInfo.getPayday();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.payday.getName()).setName(ContractEnum.FormField.payday.getKey()).setValue(o).setSort(40));
                            break;
                        case "agreedMatters":
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.agreedMatters.getName()).setName(ContractEnum.FormField.agreedMatters.getKey()).setValue(o).setSort(41));
                            break;
                        case "mainResponsible":
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.mainResponsible.getName()).setName(ContractEnum.FormField.mainResponsible.getKey()).setValue(o).setSort(42));
                            break;
                        case "firstPartSignDate":
                            o = startDate;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartSignDate.getName()).setName(ContractEnum.FormField.firstPartSignDate.getKey()).setValue(o).setType("date").setSort(43));
                            break;
                        case "secondPartSignDate":
                            o = startDate;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(o).setType("date").setSort(44));
                            break;
                        case "scopen":
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.scopen.getName()).setName(ContractEnum.FormField.scopen.getKey()).setValue(o).setSort(45));
                            break;
                        case "allowance":
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.allowance.getName()).setName(ContractEnum.FormField.allowance.getKey()).setValue(o).setSort(46));
                            break;
                        default:
                            break;
                    }
                });
                break;
            // 承诺书
            case COMMITMENT:
                formFieldList.forEach(field -> {
                    Object o = null;
                    switch (field) {
                        case "secondPartName":
                            o = contractInfo.getSecondPartName();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartName.getName()).setName(ContractEnum.FormField.secondPartName.getKey()).setValue(o).setDisabled(true).setSort(1));
                            break;
                        case "secondPartIdNo":
                            o = contractInfo.getSecondPartIdNo();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdNo.getName()).setName(ContractEnum.FormField.secondPartIdNo.getKey()).setValue(o).setDisabled(true).setSort(2));
                            break;
                        case "reason":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.reason.getName()).setName(ContractEnum.FormField.reason.getKey()).setValue(o).setSort(3));
                            break;
                        case "socialSecurity":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.socialSecurity.getName()).setName(ContractEnum.FormField.socialSecurity.getKey()).setValue(o).setSort(4));
                            break;
                        case "expirationDate":
                            o = startDate;
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.expirationDate.getName()).setName(ContractEnum.FormField.expirationDate.getKey()).setValue(o).setType("date").setSort(5));
                            break;
                        case "promiseDateYear":
                            o = startDate;
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.promiseDate.getName()).setName(ContractEnum.FormField.promiseDate.getKey()).setValue(o).setType("date").setSort(6));
                            break;
                        case "employerId":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.employerId.getName()).setName(ContractEnum.FormField.employerId.getKey()).setValue(o).setSort(7));
                            break;
                        case "secondPartSignDate":
                            o = startDate;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(o).setType("date").setSort(8));
                            break;
                        default:
                            break;
                    }
                });
                break;
            // 确认函
            case CONFIRMATION:
                formFieldList.forEach(field -> {
                    Object o;
                    switch (field) {
                        case "secondPartName":
                            o = contractInfo.getSecondPartName();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartName.getName()).setName(ContractEnum.FormField.secondPartName.getKey()).setValue(o).setDisabled(true).setSort(1));
                            break;
                        case "secondPartIdNo":
                            o = contractInfo.getSecondPartIdNo();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdNo.getName()).setName(ContractEnum.FormField.secondPartIdNo.getKey()).setValue(o).setDisabled(true).setSort(2));
                            break;
                        case "secondPartPhone":
                            o = contractInfo.getSecondPartPhone();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartPhone.getName()).setName(ContractEnum.FormField.secondPartPhone.getKey()).setValue(o).setDisabled(true).setSort(3));
                            break;
                        case "sex":
                            o = contractInfo.getSecondPartSex();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.sex.getName()).setName(ContractEnum.FormField.sex.getKey()).setValue(o).setDisabled(true).setSort(3));
                            break;
                        case "birthday":
                            o = contractInfo.getBirthday();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBirthday.getName()).setName(ContractEnum.FormField.secondPartBirthday.getKey()).setValue(o).setDisabled(true).setSort(3));
                            break;
                        case "positionName":
                            o = contractInfo.getEmployerName();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.positionName.getName()).setName(ContractEnum.FormField.positionName.getKey()).setValue(o).setSort(4));
                            break;
                        case "secondPartAddress":
                            o = contractInfo.getSecondPartAddress();
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartAddress.getName()).setName(ContractEnum.FormField.secondPartAddress.getKey()).setValue(o).setSort(4));
                            break;
                        case "secondPartSignDate":
                            o = startDate;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(o).setType("date").setSort(5));
                            break;
                        case "countExist":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.countExist.getName()).setName(ContractEnum.FormField.countExist.getKey()).setValue(null).setSort(6));
                            break;
                        case "countAgree":
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.countAgree.getName()).setName(ContractEnum.FormField.countAgree.getKey()).setValue(null).setSort(7));
                            break;
                        case "employerName":
                            o = contractInfo.getEmployerName();
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.employerName.getName()).setName(ContractEnum.FormField.employerName.getKey()).setValue(o).setSort(8));
                            break;
                        case "contractDischargeDate":
                            o = startDate;
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDischargeDate.getName()).setName(ContractEnum.FormField.contractDischargeDate.getKey()).setValue(o).setType("date").setSort(9));
                            break;
                        case "contractDischargeReason":
                            o = "个人发展原因";
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDischargeReason.getName()).setName(ContractEnum.FormField.contractDischargeReason.getKey()).setValue(o).setSort(10));
                            break;
                        default:
                            break;
                    }
                });
                break;
            // 规章制度、一票否决、员工手册
            case RULES_REGULATIONS:
            case EMPLOYEE_HANDBOOK:
            case ONE_VOTE_VETO:
                formFieldList.forEach(field -> {
                    if ("secondPartName".equals(field)) {
                        secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartName.getName()).setName(ContractEnum.FormField.secondPartName.getKey()).setValue(contractInfo.getSecondPartName()).setDisabled(true).setSort(1));
                    }
                    if ("secondPartSignDate".equals(field)) {
                        firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(startDate).setType("date").setSort(2));
                    }
                });
                break;
            default:
                break;
        }

        firstPartLabels.sort(Comparator.comparingInt(InputLabel::getSort));
        secondPartLabels.sort(Comparator.comparingInt(InputLabel::getSort));

        InputLabelDTO inputLabel = new InputLabelDTO();
        inputLabel.setFirstPartLabels(firstPartLabels);
        inputLabel.setSecondPartLabels(secondPartLabels);
        result.setInputLabel(inputLabel);
        return result;
    }

    /**
     * 获取模板表单域
     *
     * @param hrContractTemplate
     * @return
     */
    private Set<String> getFormFieldList(HrContractTemplate hrContractTemplate) {
        Set<String> formFieldList;
        String redisKey = RedisKeyEnum.currencyKey.PDF_FORM_FIELD.getValue() + hrContractTemplate.getId();
        if (this.redisCache.hasKey(redisKey)) {
            formFieldList = this.redisCache.getCacheSet(redisKey);
        } else {
            formFieldList = PdfUtils.getAllFormField(hrContractTemplate.getTemplatePath());
            if (formFieldList == null) {
                throw new CommonException("模板表单域获取异常，请检查模板:" + hrContractTemplate.getTitle() + " 是否有效！");
            }
            this.redisCache.setCacheSet(redisKey, formFieldList);
        }
        this.checkFormFieldList(formFieldList, hrContractTemplate.getTitle());
        return formFieldList;
    }

    /**
     * 检查PDF表单域
     *
     * @param formFieldList
     * @param title
     */
    private void checkFormFieldList(Set<String> formFieldList, String title) {
        if (formFieldList.isEmpty()) {
            throw new CommonException("模板表单域获取异常，请检查模板:" + title + " 是否有效！");
        }
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleteHrContractTemplate(String id) {
        Optional.ofNullable(this.hrContractTemplateRepository.selectById(id))
            .ifPresent(hrContractTemplate -> {
                this.hrContractTemplateRepository.deleteById(id);
                log.info("Delete HrContractTemplate:{}", hrContractTemplate);
            });
    }

    /**
     * 批量删除
     *
     * @param ids
     */
    @Override
    public void deleteHrContractTemplate(List<String> ids) {
        log.info("Delete HrContractTemplates:{}", ids);
        this.hrContractTemplateRepository.deleteBatchIds(ids);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CONTRACT_TEMPLATE.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
    }

    /**
     * 分页查询
     *
     * @param hrContractTemplateDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrContractTemplateDTO hrContractTemplateDTO, Long pageNumber, Long pageSize) {
        Page<HrContractTemplateDTO> page = new Page<>(pageNumber, pageSize);
        IPage<HrContractTemplateDTO> iPage = this.hrContractTemplateRepository.selectPageByObjet(page, hrContractTemplateDTO);
        iPage.getRecords().forEach(ls -> ls.setTypeStr(ContractEnum.TemplateType.getValueByKey(ls.getType())));
        return iPage;
    }

    /**
     * 批量下载合同模板
     * @param hrContractTemplateDTO
     * @return
     */
    @Override
    public List<HrContractTemplateDTO> downloadContractTemplatesBatch(HrContractTemplateDTO hrContractTemplateDTO) {
        List<HrContractTemplateDTO> hrContractTemplateDTOList = hrContractTemplateRepository.findList(hrContractTemplateDTO);
        return hrContractTemplateDTOList;
    }

    @Override
    public FormFieldDealDTO getRenewalContractTemplate(List<String> clientNames, HrContractTemplate hrContractTemplate) {
        // 批量续签会走这里
        FormFieldDealDTO result = new FormFieldDealDTO();
        // 获取表单域
        Set<String> formFieldList = this.getFormFieldList(hrContractTemplate);

        List<InputLabel> firstPartLabels = new LinkedList<>();
        result.setTemplateId(hrContractTemplate.getId());
        result.setType(hrContractTemplate.getType());
        result.setTemplatePath(hrContractTemplate.getTemplatePath());
        if (formFieldList != null) {
            LocalDate startDate = LocalDate.now();
            // 判断模板类型
            ContractEnum.TemplateType templateTypeEnum = ContractEnum.TemplateType.getEnumByKey(hrContractTemplate.getType());
            switch (Objects.requireNonNull(templateTypeEnum)) {
                // 劳动合同
                case LABOR_CONTRACT:
                    List<SelectionOption> selectionOptions = new ArrayList<>();
                    selectionOptions.add(new SelectionOption().setValue("1").setLabel("是"));
                    selectionOptions.add(new SelectionOption().setValue("2").setLabel("否"));
                    firstPartLabels.add(new InputLabel().setLabel("统一合同开始日期").setName("unifiedStart").setValue(selectionOptions.get(1).getValue()).setType("change").setOptions(selectionOptions).setRequired(true).setSort(2));
                    List<SelectionOption> selection = new ArrayList<>();
                    selection.add(new SelectionOption().setValue("1").setLabel("是"));
                    selection.add(new SelectionOption().setValue("2").setLabel("否"));
                    firstPartLabels.add(new InputLabel().setLabel("统一合同结束日期").setName("unified").setValue(selection.get(1).getValue()).setType("change").setOptions(selection).setRequired(true).setSort(4));
                    formFieldList.forEach(field -> {
                        Object o = null;
                        switch (field) {
                            case "employerName":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.employerName.getName()).setName(ContractEnum.FormField.employerName.getKey()).setValue(clientNames.size() > 1 ? "多个" : clientNames.get(0)).setDisabled(false).setSort(1));
                                break;
                            case "contractStartDateYear":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractStartDate.getName()).setName(ContractEnum.FormField.contractStartDate.getKey()).setValue(o).setType("date").setSort(3));
                                break;
                            case "contractEndDateYear":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractEndDate.getName()).setName(ContractEnum.FormField.contractEndDate.getKey()).setValue(o).setType("date").setSort(5));
                                break;
                            case "contractDeadlineYears":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDeadlineYears.getName()).setName(ContractEnum.FormField.contractDeadlineYears.getKey()).setSort(6));
                                break;
                            case "contractDeadlineMonth":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDeadlineMonth.getName()).setName(ContractEnum.FormField.contractDeadlineMonth.getKey()).setSort(7));
                                break;
                            case "payday":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.payday.getName()).setName(ContractEnum.FormField.payday.getKey()).setValue(o).setSort(8));
                                break;
                            case "salary":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.salary.getName()).setName(ContractEnum.FormField.salary.getKey()).setValue(o).setSort(9));
                                break;
                            case "firstPartName":
                                o = CompanyInfoEnum.FIRST_PART_NAME.getValue();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartName.getName()).setName(ContractEnum.FormField.firstPartName.getKey()).setValue(o).setSort(10));
                                break;
                            case "legalPersonName":
                                o = CompanyInfoEnum.LEGAL_PERSON_NAME.getValue();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.legalPersonName.getName()).setName(ContractEnum.FormField.legalPersonName.getKey()).setValue(o).setSort(11));
                                break;
                            case "firstPartResidence":
                                o = CompanyInfoEnum.FIRST_PART_RESIDENCE.getValue();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartResidence.getName()).setName(ContractEnum.FormField.firstPartResidence.getKey()).setValue(o).setSort(12));
                                break;
                            case "firstPartPhone":
                                o = CompanyInfoEnum.FIRST_PART_PHONE.getValue();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartPhone.getName()).setName(ContractEnum.FormField.firstPartPhone.getKey()).setValue(o).setSort(13));
                                break;
                            case "probation":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.probation.getName()).setName(ContractEnum.FormField.probation.getKey()).setValue(o).setSort(25));
                                break;
                            case "probationStartDateYear":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.probationStartDate.getName()).setName(ContractEnum.FormField.probationStartDate.getKey()).setValue(o).setType("date").setSort(26));
                                break;
                            case "probationEndDateYear":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.probationEndDate.getName()).setName(ContractEnum.FormField.probationEndDate.getKey()).setValue(o).setType("date").setSort(27));
                                break;
                            case "positionName":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.positionName.getName()).setName(ContractEnum.FormField.positionName.getKey()).setValue(o).setSort(29));
                                break;
                            case "workPlace":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workPlace.getName()).setName(ContractEnum.FormField.workPlace.getKey()).setValue(o).setSort(30));
                                break;
                            case "workPlaceTownOrStreet":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workPlaceTownOrStreet.getName()).setName(ContractEnum.FormField.workPlaceTownOrStreet.getKey()).setValue(o).setSort(14));
                                break;
                            case "workplaceVillageOrCommunity":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workplaceVillageOrCommunity.getName()).setName(ContractEnum.FormField.workplaceVillageOrCommunity.getKey()).setValue(o).setSort(15));
                                break;
                            case "workDaysPerWeek":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workDaysPerWeek.getName()).setName(ContractEnum.FormField.workDaysPerWeek.getKey()).setValue(o).setSort(16));
                                break;
                            case "workHoursPerDay":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workHoursPerDay.getName()).setName(ContractEnum.FormField.workHoursPerDay.getKey()).setValue(o).setSort(17));
                                break;
                            case "dailyWage":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.dailyWage.getName()).setName(ContractEnum.FormField.dailyWage.getKey()).setValue(o).setSort(18));
                                break;
                            case "agreedMatters":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.agreedMatters.getName()).setName(ContractEnum.FormField.agreedMatters.getKey()).setValue(o).setSort(19));
                                break;
                            case "mainResponsible":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.mainResponsible.getName()).setName(ContractEnum.FormField.mainResponsible.getKey()).setValue(o).setSort(20));
                                break;
                            case "firstPartSignDate":
                                o = startDate;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartSignDate.getName()).setName(ContractEnum.FormField.firstPartSignDate.getKey()).setValue(o).setType("date").setSort(21));
                                break;
                            case "secondPartSignDate":
                                o = startDate;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(o).setType("date").setSort(22));
                                break;
                            case "scopen":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.scopen.getName()).setName(ContractEnum.FormField.scopen.getKey()).setValue(o).setSort(42));
                                break;
                            case "allowance":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.allowance.getName()).setName(ContractEnum.FormField.allowance.getKey()).setValue(o).setSort(43));
                                break;
                            default:
                                break;
                        }
                    });
                    break;
                case COMMITMENT:
                case CONFIRMATION:
                case RULES_REGULATIONS:
                case EMPLOYEE_HANDBOOK:
                case ONE_VOTE_VETO:
                    firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(startDate).setType("date").setSort(1));
                    formFieldList.forEach(field -> {
                        Object o = null;
                        switch (field) {
                            case "employerName":
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.employerName.getName()).setName(ContractEnum.FormField.employerName.getKey()).setValue(clientNames.size() > 1 ? "多个" : clientNames.get(0)).setDisabled(false).setSort(2));
                                break;
                            case "contractDischargeDate":
                                o = startDate;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDischargeDate.getName()).setName(ContractEnum.FormField.contractDischargeDate.getKey()).setValue(o).setType("date").setSort(3));
                                break;
                            case "contractDischargeReason":
                                o = "个人发展原因";
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDischargeReason.getName()).setName(ContractEnum.FormField.contractDischargeReason.getKey()).setValue(o).setSort(4));
                                break;
                            default:
                                break;
                        }
                    });
                    break;
                default:
                    break;
            }
        }
        firstPartLabels.sort(Comparator.comparingInt(InputLabel::getSort));
        InputLabelDTO inputLabel = new InputLabelDTO();
        inputLabel.setFirstPartLabels(firstPartLabels);
        result.setInputLabel(inputLabel);
        return result;
    }

    /**
     * 处理表单域到MAP
     *
     * @param templateMap
     * @param labels
     */
    @Override
    public void handleInputLabelMap(Map<String, Object> templateMap, List<InputLabel> labels) {
        // 处理表单信息 添加时间等附属信息
        labels.forEach(ls -> {
            String name = ls.getName();
            String value = String.valueOf(ls.getValue());
            // 处理表单域
            this.dealFormFieldData(name, value, templateMap);
        });
    }

    /**
     * 处理表单域
     *
     * @param name
     * @param value
     * @param templateMap
     */
    private void dealFormFieldData(String name, String value, Map<String, Object> templateMap) {
        switch (name) {
            case "contractStartDate":
                if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                    LocalDate date = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    templateMap.put("contractStartDateYear", date.getYear());
                    templateMap.put("contractStartDateMonth", date.getMonthValue());
                    templateMap.put("contractStartDateDay", date.getDayOfMonth());
                }
                break;
            case "contractEndDate":
                if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                    LocalDate date1 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    templateMap.put("contractEndDateYear", date1.getYear());
                    templateMap.put("contractEndDateMonth", date1.getMonthValue());
                    templateMap.put("contractEndDateDay", date1.getDayOfMonth());
                }
                break;
            case "sendStartDate":
                if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                    LocalDate date2 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    templateMap.put("sendStartDateYear", date2.getYear());
                    templateMap.put("sendStartDateMonth", date2.getMonthValue());
                    templateMap.put("sendStartDateDay", date2.getDayOfMonth());
                }
                break;
            case "probationStartDate":
                if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                    LocalDate date3 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    templateMap.put("probationStartDateYear", date3.getYear());
                    templateMap.put("probationStartDateMonth", date3.getMonthValue());
                    templateMap.put("probationStartDateDay", date3.getDayOfMonth());
                }
                break;
            case "probationEndDate":
                if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                    LocalDate date4 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    templateMap.put("probationEndDateYear", date4.getYear());
                    templateMap.put("probationEndDateMonth", date4.getMonthValue());
                    templateMap.put("probationEndDateDay", date4.getDayOfMonth());
                }
                break;
            case "firstPartSignatureDate":
                if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                    LocalDate date5 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    templateMap.put("firstPartSignatureYear", date5.getYear());
                    templateMap.put("firstPartSignatureMonth", date5.getMonthValue());
                    templateMap.put("firstPartSignatureDay", date5.getDayOfMonth());
                }
                break;
            case "expirationDate":
                if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                    LocalDate date6 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    templateMap.put("expirationDateYear", date6.getYear());
                    templateMap.put("expirationDateMonth", date6.getMonthValue());
                    templateMap.put("expirationDateDay", date6.getDayOfMonth());
                }
                break;
            case "promiseDate":
                if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                    LocalDate date7 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    templateMap.put("promiseDateYear", date7.getYear());
                    templateMap.put("promiseDateMonth", date7.getMonthValue());
                    templateMap.put("promiseDateDay", date7.getDayOfMonth());
                }
                break;
            default:
                templateMap.put(name, value);
        }
    }
}
