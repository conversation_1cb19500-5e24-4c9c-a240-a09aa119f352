# hr-server
## 测试环境 integration
1. 后台地址：https://hr-server.cas-air.cn/#/
2. 员工小程序：#小程序://HR云管家/oHOzBdKvStjLztf


## 正式环境
1. mvn clean package 
2. 重命名jar包为 hr-server-0.0.1-SNAPSHOT.jar
3. 登录到服务器，rz 上传 jar 包到 /opt/hr/server/ 目录下
4. 执行命令：./deploy-hr-server.sh
```bash
/opt/hr/server/
# 服务主程序目录（此文件夹内包含主程序后台镜像[hr-server.jar]、主程序后台镜像备份[hr-server-*.jar]、主程序服务配置文件[config]、主程序运行日志记录[log]、主程序升级更新脚本[deploy-hr-server.sh]、）
# 部署步骤 1.上传升级jar包到 [/opt/hr/server/] 目录下 2.执行脚本命令 [./deploy-hr-server.sh]
# 建议对此文件夹进行备份

# 主程序部署命令
docker run -d --restart=unless-stopped -p 10020:10020 -v /etc/timezone:/etc/timezone:ro -v /etc/localtime:/etc/localtime:ro -v /opt/hr/server:/myapp --name hr-server nexus.cas-air.cn:8082/hr/hr-platform-server-integration --spring.profiles.active=prod

# deploy-hr-server.sh脚本内容
#!/bin/bash
echo "hr-server后台部署脚本执行------>"
if [ ! -f /opt/hr/server/hr-server-0.0.1-SNAPSHOT.jar ];then 
    echo "jar包文件不存在"
else
    echo "jar包文件存在"
    mv -f /opt/hr/server/hr-server-0.0.1-SNAPSHOT.jar /opt/hr/server/hr-server.jar
    docker restart hr-server
fi
```
