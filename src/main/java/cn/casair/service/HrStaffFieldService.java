package cn.casair.service;
import cn.casair.domain.HrStaffField;
import cn.casair.dto.HrStaffFieldDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 其他信息服务类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
public interface HrStaffFieldService extends IService<HrStaffField> {


    /**
     * 创建其他信息
     * @param hrStaffFieldDTO
     * @return
     */
    HrStaffFieldDTO createHrField(HrStaffFieldDTO hrStaffFieldDTO);

    /**
     * 修改其他信息
     * @param hrStaffFieldDTO
     * @return
     */
    Optional<HrStaffFieldDTO> updateHrField(HrStaffFieldDTO hrStaffFieldDTO);

    /**
     * 查询其他信息详情
     * @param id
     * @return
     */
    HrStaffFieldDTO getHrField(String id);

    /**
     * 删除其他信息
     * @param id
     */
    void deleteHrField(String id);

    /**
     * 批量删除其他信息
     * @param ids
     */
    void deleteHrField(List<String> ids);

    /**
     * 分页查询其他信息
     * @param hrStaffFieldDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrStaffFieldDTO hrStaffFieldDTO, Long pageNumber, Long pageSize);
    }
