<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrArchivesBringRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, archives_id, ct_type, ct_proposes, ct_reasons, ct_name, ct_phone, ct_id_card, ct_detail, ct_destination, return_on_time, estimate_return_time, return_state, return_time, ct_remark,
        is_delete, last_modified_by, last_modified_date, created_by, created_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrArchivesBring">
        <id column="id" property="id"/>
        <result column="archives_id" property="archivesId"/>
        <result column="ct_type" property="ctType"/>
        <result column="ct_proposes" property="ctProposes"/>
        <result column="ct_reasons" property="ctReasons"/>
        <result column="ct_name" property="ctName"/>
        <result column="ct_phone" property="ctPhone"/>
        <result column="ct_id_card" property="ctIdCard"/>
        <result column="ct_detail" property="ctDetail"/>
        <result column="ct_destination" property="ctDestination"/>
        <result column="return_on_time" property="returnOnTime"/>
        <result column="estimate_return_time" property="estimateReturnTime"/>
        <result column="return_state" property="returnState"/>
        <result column="return_time" property="returnTime"/>
        <result column="ct_remark" property="ctRemark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
    </resultMap>

    <resultMap id="ArchivesBring" type="cn.casair.dto.HrArchivesBringDTO">
        <id column="id" property="id"/>
        <result column="archives_num" property="archivesNum"/>
        <result column="emp_unit" property="empUnit"/>
        <result column="archives_name" property="archivesName"/>
        <result column="archives_type" property="archivesType"/>
        <result column="certificate_num" property="certificateNum"/>
        <result column="system_num" property="systemNum"/>
        <result column="phone" property="phone"/>
        <result column="archives_id" property="archivesId"/>
        <result column="ct_type" property="ctType"/>
        <result column="ct_proposes" property="ctProposes"/>
        <result column="ct_reasons" property="ctReasons"/>
        <result column="ct_name" property="ctName"/>
        <result column="ct_phone" property="ctPhone"/>
        <result column="ct_id_card" property="ctIdCard"/>
        <result column="ct_detail" property="ctDetail"/>
        <result column="ct_destination" property="ctDestination"/>
        <result column="return_on_time" property="returnOnTime"/>
        <result column="estimate_return_time" property="estimateReturnTime"/>
        <result column="return_state" property="returnState"/>
        <result column="return_time" property="returnTime"/>
        <result column="ct_remark" property="ctRemark"/>
        <result column="created_date" property="createdDate"/>
        <collection property="archivesDetailList" ofType="cn.casair.dto.HrArchivesDetailDTO">
            <result column="detailId" property="id"/>
            <result column="name" property="name"/>
            <result column="state" property="state"/>
            <result column="type" property="type"/>
            <collection property="appendixList" javaType="java.util.ArrayList" ofType="cn.casair.dto.HrAppendixDTO">
                <result column="detailAppendixId" property="id"/>
                <result column="detailAppendixName" property="name"/>
                <result column="detailOriginName" property="originName"/>
                <result column="detailFileType" property="fileType"/>
                <result column="detailFileUrl" property="fileUrl"/>
            </collection>
        </collection>
        <collection property="appendixList" javaType="java.util.ArrayList" ofType="cn.casair.dto.HrAppendixDTO">
            <result column="appendixId" property="id"/>
            <result column="appendixName" property="name"/>
            <result column="origin_name" property="originName"/>
            <result column="file_type" property="fileType"/>
            <result column="file_url" property="fileUrl"/>
        </collection>
    </resultMap>

    <resultMap id="ArchivesBringList" type="cn.casair.dto.HrArchivesBringDTO">
        <id column="id" property="id"/>
        <result column="archives_id" property="archivesId"/>
        <result column="archives_num" property="archivesNum"/>
        <result column="emp_unit" property="empUnit"/>
        <result column="archives_name" property="archivesName"/>
        <result column="archives_type" property="archivesType"/>
        <result column="ct_type" property="ctType"/>
        <result column="ct_proposes" property="ctProposes"/>
        <result column="ct_reasons" property="ctReasons"/>
        <result column="ct_detail" property="ctDetail"/>
        <result column="return_state" property="returnState"/>
        <result column="created_date" property="createdDate"/>
    </resultMap>

    <insert id="insertArchivesBringDetail">
        INSERT INTO hr_archives_bring_detail (id, bring_id, detail_id)
        VALUES (#{id}, #{bringId}, #{detailId})
    </insert>

    <select id="getByIds" resultMap="ArchivesBringList">
        SELECT
        hab.id,
        hab.ct_type,
        hab.ct_proposes,
        ham.archives_num,
        ham.archives_name
        FROM hr_archives_bring hab
        LEFT JOIN hr_archives_manage ham ON ham.id = hab.archives_id
        WHERE hab.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="findPage" resultMap="ArchivesBringList">
        SELECT a.*
        FROM (
            SELECT
            hab.id,
            hab.archives_id,
            ham.archives_num,
            hc.id client_id,
            hc.client_name emp_unit,
            ham.archives_name,
            ham.archives_type,
            hab.ct_type,
            hab.ct_proposes,
            hab.ct_reasons,
            hab.ct_detail,
            hab.created_date,
            hab.return_state,
            hab.return_on_time,
            hab.estimate_return_time
            FROM
            hr_archives_bring hab
            LEFT JOIN hr_archives_manage ham ON ham.id = hab.archives_id
            LEFT JOIN hr_talent_staff hts ON hts.id = ham.staff_id
            LEFT JOIN hr_client hc ON hc.id = hts.client_id
            AND hts.is_delete = 0
            WHERE
            hab.is_delete = 0
            AND ham.is_delete = 0
            AND hts.is_delete = 0
            <if test="permissionClient!=null and permissionClient.size()>0">
                AND hc.id IN
                <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            UNION
            SELECT
            hab.id,
            hab.archives_id,
            ham.archives_num,
            hc.id client_id,
            hc.client_name emp_unit,
            ham.archives_name,
            ham.archives_type,
            hab.ct_type,
            hab.ct_proposes,
            hab.ct_reasons,
            hab.ct_detail,
            hab.created_date,
            hab.return_state,
            hab.return_on_time,
            hab.estimate_return_time
            FROM
            hr_archives_bring hab
            LEFT JOIN hr_archives_manage ham ON ham.id = hab.archives_id
            LEFT JOIN hr_client hc ON hc.id = ham.client_id
            WHERE
            hab.is_delete = 0
            AND ham.is_delete = 0
            <if test="permissionClient!=null and permissionClient.size()>0">
                AND hc.id IN
                <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
        ) a
        <where>
            <if test="params.archivesId!=null and params.archivesId!=''">
                AND a.archives_id = #{params.archivesId}
            </if>
            <if test="params.archivesNum!=null and params.archivesNum!=''">
                AND a.archives_num LIKE CONCAT('%', #{params.archivesNum}, '%')
            </if>
            <if test="params.clientId!=null and params.clientId!=''">
                AND a.client_id = #{params.clientId}
            </if>
            <if test="params.archivesName!=null and params.archivesName!=''">
                AND a.archives_name LIKE CONCAT('%', #{params.archivesName}, '%')
            </if>
            <if test="params.archivesType!=null">
                AND a.archives_type = #{params.archivesType}
            </if>
            <if test="params.ctType!=null">
                AND a.ct_type = #{params.ctType}
            </if>
            <if test="params.ctProposes!=null">
                AND a.ct_proposes = #{params.ctProposes}
            </if>
            <if test="params.ctReasons!=null and params.ctReasons!=''">
                AND a.ct_reasons LIKE CONCAT('%', #{params.ctReasons}, '%')
            </if>
            <if test="params.createdDateQueryStart!=null">
                AND a.created_date &gt;= #{params.createdDateQueryStart}
            </if>
            <if test="params.createdDateQueryEnd!=null">
                AND a.created_date &lt;= #{params.createdDateQueryEnd}
            </if>
            <if test="params.isOverduePayment!=null and params.isOverduePayment==1">
                AND a.return_on_time = 0
                AND a.return_state = 1
                AND a.estimate_return_time &lt; #{params.remainDate}
            </if>
        </where>
            <choose>
                <when test="params.field!=null and params.field!=''">
                    <choose>
                        <when test="params.field=='ct_proposes_str'">
                            ORDER BY a.ct_proposes ${params.order}
                        </when>
                        <when test="params.field=='ct_type_str'">
                            ORDER BY a.ct_type ${params.order}
                        </when>
                        <when test="params.field=='archives_type_str'">
                            ORDER BY a.archives_type ${params.order}
                        </when>
                        <otherwise>
                            ORDER BY ${params.field} ${params.order}
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    ORDER BY a.created_date DESC
                </otherwise>
            </choose>
    </select>

    <select id="getCompleteById" resultMap="ArchivesBring">
        SELECT
            ham.archives_num,
            ham.archives_name,
            ham.archives_type,
            hts.certificate_num,
            hts.phone,
            hab.id,
            hab.archives_id,
            hab.ct_type,
            hab.ct_proposes,
            hab.ct_reasons,
            hab.ct_name,
            hab.ct_phone,
            hab.ct_id_card,
            hab.ct_detail,
            hab.ct_destination,
            hab.return_on_time,
            hab.estimate_return_time,
            hab.return_state,
            hab.return_time,
            hab.ct_remark,
            hab.created_date,
            had.id detailId,
            had.`name`,
            had.state,
            had.type,
            ha1.id detailAppendixId,
            ha1.`name` detailAppendixName,
            ha1.origin_name detailOriginName,
            ha1.file_type detailFileType,
            ha1.file_url detailFileUrl,
            ha2.id appendixId,
            ha2.`name` appendixName,
            ha2.origin_name originName,
            ha2.file_type fileType,
            ha2.file_url fileUrl
        FROM
            hr_archives_bring hab
            LEFT JOIN hr_archives_manage ham ON ham.id = hab.archives_id
            AND ham.is_delete = 0
            LEFT JOIN hr_talent_staff hts ON hts.id = ham.staff_id
            AND hts.is_delete = 0
            LEFT JOIN hr_archives_bring_detail habd ON habd.bring_id = hab.id
            LEFT JOIN hr_archives_detail had ON had.id = habd.detail_id
            AND had.is_delete = 0
            LEFT JOIN hr_appendix_union hau1 ON hau1.union_id = had.id
            LEFT JOIN hr_appendix ha1 ON ha1.id = hau1.appendix_id
            AND ha1.is_delete = 0
            LEFT JOIN hr_appendix_union hau2 on hau2.union_id = hab.id
            LEFT JOIN hr_appendix ha2 ON ha2.id = hau2.appendix_id
            AND ha2.is_delete = 0
        WHERE
            hab.is_delete = 0
            AND hts.is_delete = 0
            AND hab.id = #{id}
    </select>

    <select id="getExportList" resultType="cn.casair.dto.excel.HrArchivesBringExport">
        SELECT
            ham.id archivesId,
            ham.archives_num,
            ham.archives_type,
            hc.unit_number,
            hc.client_name empUnit,
            hts.system_num,
            hts.`name` staffName,
            hts.staff_status,
            hts.certificate_type,
            hts.certificate_num,
            hts.phone,
            hab.id,
            hab.ct_type,
            hab.ct_proposes,
            hab.ct_detail,
            hab.ct_destination,
            hab.created_date,
            hab.ct_remark
        FROM
            hr_archives_bring hab
            LEFT JOIN hr_archives_manage ham ON ham.id = hab.archives_id
            LEFT JOIN hr_talent_staff hts ON hts.id = ham.staff_id
            LEFT JOIN hr_client hc ON hc.id = hts.client_id
            AND hc.is_delete = 0
        WHERE
            hab.is_delete = 0
            AND ham.is_delete = 0 AND hts.is_delete = 0
        <if test="params.ids != null and params.ids.size() > 0">
            AND hab.id IN
            <foreach collection="params.ids" open="(" close=")" separator="," item="i">
                #{i}
            </foreach>
        </if>
        <if test="permissionClient!=null and permissionClient.size()>0">
            AND hc.id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.archivesId!=null and params.archivesId!=''">
            AND hab.archives_id = #{params.archivesId}
        </if>
        <if test="params.archivesNum!=null and params.archivesNum!=''">
            AND ham.archives_num LIKE CONCAT('%', #{params.archivesNum}, '%')
        </if>
        <if test="params.clientId!=null and params.clientId!=''">
            AND hc.id = #{params.clientId}
        </if>
        <if test="params.archivesName!=null and params.archivesName!=''">
            AND ham.archives_name LIKE CONCAT('%', #{params.archivesName}, '%')
        </if>
        <if test="params.archivesType!=null">
            AND ham.archives_type = #{params.archivesType}
        </if>
        <if test="params.ctType!=null">
            AND hab.ct_type = #{params.ctType}
        </if>
        <if test="params.ctProposes!=null">
            AND hab.ct_proposes = #{params.ctProposes}
        </if>
        <if test="params.ctReasons!=null and params.ctReasons!=''">
            AND hab.ct_reasons LIKE CONCAT('%', #{params.ctReasons}, '%')
        </if>
        <if test="params.createdDateQueryStart!=null">
            AND hab.created_date &gt;= #{params.createdDateQueryStart}
        </if>
        <if test="params.createdDateQueryEnd!=null">
            AND hab.created_date &lt;= #{params.createdDateQueryEnd}
        </if>
        <if test="params.isOverduePayment!=null and params.isOverduePayment==1">
            AND hab.return_on_time = 0
            AND hab.return_state = 1
            AND hab.estimate_return_time &lt; #{params.remainDate}
        </if>
        ORDER BY
            hab.created_date DESC
    </select>

    <select id="getHrArchivesBringById" resultMap="ArchivesBring">
        SELECT ham.archives_num,
               hc.client_name emp_unit,
               ham.archives_name,
               ham.archives_type,
               hts.certificate_num,
               hts.system_num,
               hts.phone,
               hab.id,
               hab.archives_id,
               hab.ct_type,
               hab.ct_proposes,
               hab.ct_reasons,
               hab.ct_name,
               hab.ct_phone,
               hab.ct_id_card,
               hab.ct_detail,
               hab.ct_destination,
               hab.return_on_time,
               hab.estimate_return_time,
               hab.return_state,
               hab.return_time,
               hab.ct_remark,
               hab.created_date,
               had.id          detailId,
               had.`name`,
               had.state,
               had.type,
               ha1.id          detailAppendixId,
               ha1.name        detailAppendixName,
               ha1.origin_name detailOriginName,
               ha1.file_type   detailFileType,
               ha1.file_url    detailFileUrl,
               ha2.id          appendixId,
               ha2.name        appendixName,
               ha2.origin_name,
               ha2.file_type,
               ha2.file_url
        FROM hr_archives_bring hab
                 LEFT JOIN hr_archives_manage ham ON ham.id = hab.archives_id
            AND ham.is_delete = 0
                 LEFT JOIN hr_archives_bring_detail habd ON habd.bring_id = hab.id
                 LEFT JOIN hr_archives_detail had ON had.id = habd.detail_id
            AND had.is_delete = 0
                 LEFT JOIN hr_appendix_union hau1 ON hau1.union_id = had.id
                 LEFT JOIN hr_appendix ha1 ON ha1.id = hau1.appendix_id
            AND ha1.is_delete = 0
                 LEFT JOIN hr_appendix_union hau2 on hau2.union_id = hab.id
                 LEFT JOIN hr_appendix ha2 ON ha2.id = hau2.appendix_id
            AND ha2.is_delete = 0
                 LEFT JOIN hr_client hc ON hc.id = ham.client_id
                 LEFT JOIN hr_talent_staff hts ON hts.id = ham.staff_id AND hts.is_delete = 0
        WHERE
            hab.id = #{params.id}
    </select>

    <delete id="deleteBringDetailByBringIds">
        DELETE FROM hr_archives_bring_detail WHERE bring_id IN
        <foreach collection="ids" open="(" close=")" separator="," item="i">
            #{i}
        </foreach>
    </delete>

    <update id="deleteBatchByArchivesId">
        UPDATE hr_archives_bring
        SET is_delete = 1
        WHERE archives_id IN
        <foreach collection="archivesIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

</mapper>
