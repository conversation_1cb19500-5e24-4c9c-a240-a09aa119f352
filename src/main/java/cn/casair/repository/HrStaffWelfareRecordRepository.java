package cn.casair.repository;

import cn.casair.domain.HrStaffWelfareRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 员工每月福利配置数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Repository
public interface HrStaffWelfareRecordRepository extends BaseMapper<HrStaffWelfareRecord> {

    /**
     * 查询员工当前年月福利记录
     *
     * @return cn.casair.domain.HrStaffWelfareRecord
     * <AUTHOR>
     * @date 2021/11/15
     **/
    HrStaffWelfareRecord selectByObject(@Param("query") Map<String, Object> query);
}
