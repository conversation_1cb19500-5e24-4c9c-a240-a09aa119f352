<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRegistrationInfoRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, brochure_id, station_id, client_id, protocol_id, system_num, name, nationality, nation, certificate_type, certificate_num, birthday, sex, phone, emergency_call, native_place, height, email, marital_status, highest_education, household_registration, domicile_place, census_register_address, census_register_postcode, politics_status, party_date, party_branch, contact_address, contact_postcode, iz_military, military_start_date, military_end_date, corps, rewards_punishment, remark, staff_status, company_name, personnel_type, signature_status, work_status, nick_name, status, avatar, alias_name, password, apply_staff_id, iz_default, photo_appendix_id, card_appendix_id, education_appendix_id, degree_appendix_id, iz_start_end, iz_preserve, iz_insured, open_id, departure_staff_id, resignation_date, iz_retire, medical_record_date, renewal_process, firsts_chool, highest_school, study_experience, office_experience, registration_json,
is_delete                    ,created_date                    ,created_by                    ,last_modified_by                    ,last_modified_date
        </sql>


    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrRegistrationInfo">
                    <id column="id" property="id"/>
                    <result column="brochure_id" property="brochureId"/>
                    <result column="station_id" property="stationId"/>
                    <result column="client_id" property="clientId"/>
                    <result column="protocol_id" property="protocolId"/>
                    <result column="system_num" property="systemNum"/>
                    <result column="name" property="name"/>
                    <result column="nationality" property="nationality"/>
                    <result column="nation" property="nation"/>
                    <result column="certificate_type" property="certificateType"/>
                    <result column="certificate_num" property="certificateNum"/>
                    <result column="birthday" property="birthday"/>
                    <result column="sex" property="sex"/>
                    <result column="phone" property="phone"/>
                    <result column="emergency_call" property="emergencyCall"/>
                    <result column="native_place" property="nativePlace"/>
                    <result column="height" property="height"/>
                    <result column="email" property="email"/>
                    <result column="marital_status" property="maritalStatus"/>
                    <result column="highest_education" property="highestEducation"/>
                    <result column="household_registration" property="householdRegistration"/>
                    <result column="domicile_place" property="domicilePlace"/>
                    <result column="census_register_address" property="censusRegisterAddress"/>
                    <result column="census_register_postcode" property="censusRegisterPostcode"/>
                    <result column="politics_status" property="politicsStatus"/>
                    <result column="party_date" property="partyDate"/>
                    <result column="party_branch" property="partyBranch"/>
                    <result column="contact_address" property="contactAddress"/>
                    <result column="contact_postcode" property="contactPostcode"/>
                    <result column="iz_military" property="izMilitary"/>
                    <result column="military_start_date" property="militaryStartDate"/>
                    <result column="military_end_date" property="militaryEndDate"/>
                    <result column="corps" property="corps"/>
                    <result column="rewards_punishment" property="rewardsPunishment"/>
                    <result column="remark" property="remark"/>
                    <result column="staff_status" property="staffStatus"/>
                    <result column="company_name" property="companyName"/>
                    <result column="personnel_type" property="personnelType"/>
                    <result column="signature_status" property="signatureStatus"/>
                    <result column="work_status" property="workStatus"/>
                    <result column="nick_name" property="nickName"/>
                    <result column="status" property="status"/>
                    <result column="avatar" property="avatar"/>
                    <result column="alias_name" property="aliasName"/>
                    <result column="password" property="password"/>
                    <result column="apply_staff_id" property="applyStaffId"/>
                    <result column="iz_default" property="izDefault"/>
                    <result column="photo_appendix_id" property="photoAppendixId"/>
                    <result column="card_appendix_id" property="cardAppendixId"/>
                    <result column="education_appendix_id" property="educationAppendixId"/>
                    <result column="degree_appendix_id" property="degreeAppendixId"/>
                    <result column="iz_start_end" property="izStartEnd"/>
                    <result column="iz_preserve" property="izPreserve"/>
                    <result column="iz_insured" property="izInsured"/>
                    <result column="open_id" property="openId"/>
                    <result column="departure_staff_id" property="departureStaffId"/>
                    <result column="resignation_date" property="resignationDate"/>
                    <result column="iz_retire" property="izRetire"/>
                    <result column="medical_record_date" property="medicalRecordDate"/>
                    <result column="renewal_process" property="renewalProcess"/>
                    <result column="firsts_chool" property="firstsChool"/>
                    <result column="highest_school" property="highestSchool"/>
                    <result column="study_experience" property="studyExperience"/>
                    <result column="office_experience" property="officeExperience"/>
                    <result column="registration_json" property="registrationJson"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_date" property="createdDate"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

        <select id="selectRecruitmentNum" resultType="java.lang.Integer">
            SELECT recruitment_num FROM `hr_recruitment_station` where service_id=#{brochureId} and is_delete=0 and recruitment_station_name=#{stationName}
        </select>
    <select id="selectSum" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM `hr_registration_info` where is_delete=0
    </select>


    <update id="uodateRecruitmentNum">
            UPDATE hr_recruitment_station set recruitment_num =#{recruitmentNum} where service_id=#{stationId} and is_delete=0  and recruitment_station_name=#{stationName}
    </update>

    <update id="uodateOpenId">
           UPDATE hr_talent_staff set open_id =null where open_id=#{id} and is_delete=0
    </update>
    <select id="selectInfo" resultType="cn.casair.dto.HrRegistrationInfoDTO">
        SELECT
            hri.id,
            hri.brochure_id,
            hri.registration_json AS registrationJson,
            hri.station_name,
            hri.appendix_json,
            hrs.is_need_pay AS isNeedPay,
            hrd.`status` AS STATUS,
            hrd.admission_ticket_info AS admissionTicketInfo,
            hrd.admission_ticket_url AS admissionTicketUrl,
            hrd.denial_reason as denialReason,
            hrs.exam_format as examNotice,
            hrd.id AS detailsId
        FROM
            hr_registration_info hri
                LEFT JOIN hr_registration_details hrd ON hrd.info_id = hri.id
                AND hrd.is_delete = 0
                LEFT JOIN hr_recruitment_station hrs ON hrd.station_id = hrs.id
                AND hrs.is_delete = 0
        WHERE
            hri.station_name = #{stationName}
          AND hri.brochure_id = #{brochureId}
          AND hri.open_id = #{staffId}
        ORDER BY hri.created_date DESC LIMIT 1
    </select>

        <update id="updatePhone">
            UPDATE hr_talent_staff set phone=null where id=#{id}
    </update>

        <select id="getCertificateNum" resultType="cn.casair.domain.HrStaffSignCert">
            SELECT
            *
            FROM hr_talent_staff
            WHERE is_delete = 0
            AND certificate_num = #{certificateNum}
    </select>

    <select id="queryDuplicateData" resultType="java.lang.Integer">
        SELECT
            COUNT(hrd.id)
        FROM
            hr_registration_details hrd
        LEFT JOIN hr_talent_staff hts ON hrd.staff_id = hts.id  AND hts.is_delete = 0
        WHERE
            hrd.is_delete = 0 AND hts.is_delete = 0 AND hrd.`status` != 1
            AND hts.phone = #{param.phone}
            AND hts.certificate_num = #{param.certificateNum}
            AND hts.`name` = #{param.name}
            AND hrd.brochure_id = #{brochureId}
            AND hrd.station_name = #{stationName}
    </select>

    <update id="updateHrStaffSignCert">
            UPDATE hr_staff_sign_cert set   mobile_phone =#{param1.certificateNum} ,user_num =#{param1.certificateNum} where  staff_id=#{param1.staffId}
    </update>
</mapper>
