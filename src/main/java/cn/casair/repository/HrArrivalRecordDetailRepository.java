package cn.casair.repository;

import cn.casair.domain.HrArrivalRecordDetail;
import cn.casair.dto.HrArrivalRecordDetailDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 到账记录明细数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Repository
public interface HrArrivalRecordDetailRepository extends BaseMapper<HrArrivalRecordDetail> {

    /**
     * 根基到账记录id删除到账记录明细
     *
     * @param arrivalId
     * @return int
     * <AUTHOR>
     * @date 2021/11/22
     **/
    int deleteByArrivalId(@Param("arrivalId") String arrivalId);

    /**
     * 根据到账记录id删除关联明细记录
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @date 2021/11/16
     **/
    int deleteByArrivalIds(@Param("ids") List<String> ids);


    /**
     * 根据到账记录明细id删除到账记录明细
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @date 2021/11/16
     **/
    int deleteByArrivalDetailIds(@Param("ids") List<String> ids);

    /**
     * 根据arrivalId获取到账后记录明细
     *
     * @param arrivalId
     * @return java.util.List<cn.casair.dto.HrArrivalRecordDetailDTO>
     * <AUTHOR>
     * @date 2021/11/17
     **/
    List<HrArrivalRecordDetailDTO> getByArrivalId(@Param("arrivalId") String arrivalId);

    /**
     * 获取到账记录明细列表
     *
     * @param ids
     * @return java.util.List<cn.casair.domain.HrArrivalRecordDetail>
     * <AUTHOR>
     * @date 2021/11/18
     **/
    List<HrArrivalRecordDetail> selectByIds(@Param("ids") List<String> ids);


    /**
     * 根据开票记录id查询账记录明细
     *
     * @param billInvoiceId
     * @return
     */
    List<HrArrivalRecordDetailDTO> getListByBillInvoiceId(@Param("billInvoiceId") String billInvoiceId);
}
