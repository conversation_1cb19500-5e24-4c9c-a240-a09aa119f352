package cn.casair.ncc;

import cn.casair.ncc.dto.NccAccountAuxiliaryDTO;
import cn.casair.ncc.dto.NccCustomerCreateResultDTO;
import cn.casair.ncc.dto.NccCustomerDTO;
import cn.casair.ncc.dto.NccVoucherDTO;

import java.util.List;

public interface NccService {
    /**
     * 创建ncc客户
     *
     * @param name
     * @param type
     * @return
     */
    NccCustomerCreateResultDTO createCustomer(String name, String type);


    /**
     * 创建ncc凭证
     *
     * @param nccVoucherDTO
     * @return
     */
    NccVoucherDTO createVoucher(NccVoucherDTO nccVoucherDTO);

    /**
     * 查询会计科目及辅助
     *
     * @param name
     * @return
     */
    List<NccAccountAuxiliaryDTO> qryAccountAuxiliaryList(String code, String name);

    /**
     * 查询客户档案
     *
     * @param code
     * @param name
     * @return
     */
    List<NccCustomerDTO> qryCustomerList(String code, String name);

}
