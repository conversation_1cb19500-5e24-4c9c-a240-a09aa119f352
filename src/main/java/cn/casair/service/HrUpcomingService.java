package cn.casair.service;
import cn.casair.domain.HrUpcoming;
import cn.casair.dto.HrUpcomingDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 待办表服务类
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface HrUpcomingService extends IService<HrUpcoming> {


    /**
     * 创建待办表
     * @param hrUpcomingDTO
     * @return
     */
    HrUpcomingDTO createHrUpcoming(HrUpcomingDTO hrUpcomingDTO);

    /**
     * 修改待办表
     * @param hrUpcomingDTO
     * @return
     */
    Optional<HrUpcomingDTO> updateHrUpcoming(HrUpcomingDTO hrUpcomingDTO);

    /**
     * 查询待办表详情
     * @param id
     * @return
     */
    HrUpcomingDTO getHrUpcoming(String id);

    /**
     * 删除待办表
     * @param id
     */
    void deleteHrUpcoming(String id);

    /**
     * 批量删除待办表
     * @param ids
     */
    void deleteHrUpcoming(List<String> ids);

    /**
     * 分页查询待办表
     * @param hrUpcomingDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrUpcomingDTO hrUpcomingDTO,Long pageNumber,Long pageSize);

    List<HrUpcomingDTO> selectIndex();

    List<HrUpcomingDTO> selectCalender(String  date);

    List<HrUpcomingDTO> selectNowDate(String date);

    /**
     * 查询当天要提醒的数据
     * @return
     */
    List<HrUpcomingDTO> selectWarn();

    /**
     * 服务生成待办
     * @param ServiceId 服务id
     * @param staffId 员工id
     * @param title 待办标题
     * @param date 待办日期
     * @param type 0 给专管员生成待办  1 给客户生成待办 2客服部经理生成待办 3档案管理员生成代办 4待遇合规专员 5招聘部部长 6薪酬专员 7入离职专员、待遇合规专员、招聘部部长、薪酬专员
     */
    void createServiceUpcoming(String ServiceId, String staffId, String title, LocalDate date,int type);

    /**
     * 将服务都修改为已办
     * @param ServiceId 服务id
     */
    HrUpcoming updateUpcoming(String ServiceId);

    /**
     * 服务生成待办
     * @param serviceId 服务id
     * @param clientId 客户id
     * @param title 待办标题
     * @param date 待办日期
     * @param type 0 给专管员生成待办  1 给客户生成待办 2客服部经理生成待办
     */
    void saveServiceUpcoming(String serviceId,String clientId, String title, LocalDate date,int type);

}
