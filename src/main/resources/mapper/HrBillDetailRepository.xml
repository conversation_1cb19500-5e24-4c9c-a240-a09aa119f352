<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillDetailRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, system_num, `name`, certificate_num, phone, bill_id,guarantee_bill_id, staff_id, pay_year, pay_monthly, social_security_area, staff_status, personnel_type, iz_insured, social_security_num, social_security_cardinal,
        medical_insurance_num, medical_insurance_cardinal, accumulation_fund_num, accumulation_fund_cardinal, unit_pension, unit_unemployment, unit_medical,unit_maternity, work_injury, unit_social_security_make_up, unit_subtotal, personal_pension,
        personal_unemployment, personal_medical, personal_social_security_make_up, personal_subtotal, social_security_total, unit_accumulation_fund_make_up, unit_accumulation_fund, personal_accumulation_fund, personal_accumulation_fund_make_up,
        accumulation_fund_total, service_fee, total, bill_used, reason, is_used, salary, pre_tax_salary, personal_tax, real_salary,
        social_security_cardinal_personal,medical_insurance_cardinal_personal,tax_calculation_method,personal_tax_make_up,current_year,project_code,project_name,people_num,total_occurrence,
        unit_pension_cardinal,unit_unemployment_cardinal,unit_maternity_cardinal,work_injury_cardinal,unit_large_medical_expense,replenish_work_injury_expense,personal_pension_cardinal,personal_unemployment_cardinal,personal_large_medical_expense,
        unit_late_fee,unit_other_fee,personal_other_fee,personal_maternity_cardinal,personal_maternity,taxation_fee, bill_detail_grant_id, emolument_multiple,unit_enterprise_annuity,personal_enterprise_annuity,commercial_insurance,
        is_delete, created_by, last_modified_by, created_date, last_modified_date, other_salary, sort_value
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillDetail">
        <id column="id" property="id"/>
        <result column="bill_id" property="billId"/>
        <result column="guarantee_bill_id" property="guaranteeBillId"/>
        <result column="system_num" property="systemNum"/>
        <result column="name" property="name"/>
        <result column="certificate_num" property="certificateNum"/>
        <result column="phone" property="phone"/>
        <result column="staff_id" property="staffId"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="social_security_area" property="socialSecurityArea"/>
        <result column="staff_status" property="staffStatus"/>
        <result column="personnel_type" property="personnelType"/>
        <result column="iz_insured" property="izInsured"/>
        <result column="social_security_num" property="socialSecurityNum"/>
        <result column="social_security_cardinal" property="socialSecurityCardinal"/>
        <result column="social_security_cardinal_personal" property="socialSecurityCardinalPersonal"/>
        <result column="medical_insurance_num" property="medicalInsuranceNum"/>
        <result column="medical_insurance_cardinal" property="medicalInsuranceCardinal"/>
        <result column="medical_insurance_cardinal_personal" property="medicalInsuranceCardinalPersonal"/>
        <result column="accumulation_fund_num" property="accumulationFundNum"/>
        <result column="accumulation_fund_cardinal" property="accumulationFundCardinal"/>
        <result column="unit_pension_cardinal" property="unitPensionCardinal"/>
        <result column="unit_unemployment_cardinal" property="unitUnemploymentCardinal"/>
        <result column="unit_maternity_cardinal" property="unitMaternityCardinal"/>
        <result column="work_injury_cardinal" property="workInjuryCardinal"/>
        <result column="unit_large_medical_expense" property="unitLargeMedicalExpense"/>
        <result column="replenish_work_injury_expense" property="replenishWorkInjuryExpense"/>
        <result column="personal_pension_cardinal" property="personalPensionCardinal"/>
        <result column="personal_unemployment_cardinal" property="personalUnemploymentCardinal"/>
        <result column="personal_large_medical_expense" property="personalLargeMedicalExpense"/>
        <result column="personal_maternity_cardinal" property="personalMaternityCardinal"/>
        <result column="personal_maternity" property="personalMaternity"/>
        <result column="unit_maternity" property="unitMaternity"/>
        <result column="unit_late_fee" property="unitLateFee"/>
        <result column="unit_other_fee" property="unitOtherFee"/>
        <result column="personal_other_fee" property="personalOtherFee"/>
        <result column="unit_pension" property="unitPension"/>
        <result column="unit_unemployment" property="unitUnemployment"/>
        <result column="unit_medical" property="unitMedical"/>
        <result column="work_injury" property="workInjury"/>
        <result column="unit_social_security_make_up" property="unitSocialSecurityMakeUp"/>
        <result column="unit_subtotal" property="unitSubtotal"/>
        <result column="personal_pension" property="personalPension"/>
        <result column="personal_unemployment" property="personalUnemployment"/>
        <result column="personal_medical" property="personalMedical"/>
        <result column="personal_social_security_make_up" property="personalSocialSecurityMakeUp"/>
        <result column="personal_subtotal" property="personalSubtotal"/>
        <result column="social_security_total" property="socialSecurityTotal"/>
        <result column="unit_accumulation_fund_make_up" property="unitAccumulationFundMakeUp"/>
        <result column="unit_accumulation_fund" property="unitAccumulationFund"/>
        <result column="personal_accumulation_fund" property="personalAccumulationFund"/>
        <result column="personal_accumulation_fund_make_up" property="personalAccumulationFundMakeUp"/>
        <result column="accumulation_fund_total" property="accumulationFundTotal"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="total" property="total"/>
        <result column="bill_used" property="billUsed"/>
        <result column="reason" property="reason"/>
        <result column="is_used" property="isUsed"/>
        <result column="salary" property="salary"/>
        <result column="pre_tax_salary" property="preTaxSalary"/>
        <result column="personal_tax" property="personalTax"/>
        <result column="personal_tax_make_up" property="personalTaxMakeUp"/>
        <result column="real_salary" property="realSalary"/>
        <result column="other_salary" property="otherSalary"/>
        <result column="sort_value" property="sortValue"/>
        <result column="tax_calculation_method" property="taxCalculationMethod"/>
        <result column="current_year" property="currentYear"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="people_num" property="peopleNum"/>
        <result column="total_occurrence" property="totalOccurrence"/>
        <result column="taxation_fee" property="taxationFee"/>
        <result column="bill_detail_grant_id" property="billDetailGrantId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <resultMap id="UnrecordedEmployee" type="cn.casair.dto.HrBillDetailDTO">
        <id column="id" property="id"/>
        <result column="bill_id" property="billId"/>
        <result column="client_id" property="clientId"/>
        <result column="guarantee_bill_id" property="guaranteeBillId"/>
        <result column="system_num" property="systemNum"/>
        <result column="name" property="name"/>
        <result column="certificate_num" property="certificateNum"/>
        <result column="phone" property="phone"/>
        <result column="staff_id" property="staffId"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="social_security_area" property="socialSecurityArea"/>
        <result column="staff_status" property="staffStatus"/>
        <result column="personnel_type" property="personnelType"/>
        <result column="iz_insured" property="izInsured"/>
        <result column="social_security_num" property="socialSecurityNum"/>
        <result column="social_security_cardinal" property="socialSecurityCardinal"/>
        <result column="social_security_cardinal_personal" property="socialSecurityCardinalPersonal"/>
        <result column="medical_insurance_num" property="medicalInsuranceNum"/>
        <result column="medical_insurance_cardinal" property="medicalInsuranceCardinal"/>
        <result column="medical_insurance_cardinal_personal" property="medicalInsuranceCardinalPersonal"/>
        <result column="accumulation_fund_num" property="accumulationFundNum"/>
        <result column="accumulation_fund_cardinal" property="accumulationFundCardinal"/>
        <result column="unit_pension" property="unitPension"/>
        <result column="unit_unemployment" property="unitUnemployment"/>
        <result column="unit_medical" property="unitMedical"/>
        <result column="work_injury" property="workInjury"/>
        <result column="unit_social_security_make_up" property="unitSocialSecurityMakeUp"/>
        <result column="unit_subtotal" property="unitSubtotal"/>
        <result column="personal_pension" property="personalPension"/>
        <result column="personal_unemployment" property="personalUnemployment"/>
        <result column="personal_medical" property="personalMedical"/>
        <result column="personal_social_security_make_up" property="personalSocialSecurityMakeUp"/>
        <result column="personal_subtotal" property="personalSubtotal"/>
        <result column="social_security_total" property="socialSecurityTotal"/>
        <result column="unit_accumulation_fund_make_up" property="unitAccumulationFundMakeUp"/>
        <result column="unit_accumulation_fund" property="unitAccumulationFund"/>
        <result column="personal_accumulation_fund" property="personalAccumulationFund"/>
        <result column="personal_accumulation_fund_make_up" property="personalAccumulationFundMakeUp"/>
        <result column="accumulation_fund_total" property="accumulationFundTotal"/>
        <result column="unit_pension_cardinal" property="unitPensionCardinal"/>
        <result column="unit_unemployment_cardinal" property="unitUnemploymentCardinal"/>
        <result column="unit_maternity_cardinal" property="unitMaternityCardinal"/>
        <result column="work_injury_cardinal" property="workInjuryCardinal"/>
        <result column="unit_large_medical_expense" property="unitLargeMedicalExpense"/>
        <result column="replenish_work_injury_expense" property="replenishWorkInjuryExpense"/>
        <result column="personal_pension_cardinal" property="personalPensionCardinal"/>
        <result column="personal_unemployment_cardinal" property="personalUnemploymentCardinal"/>
        <result column="personal_large_medical_expense" property="personalLargeMedicalExpense"/>
        <result column="personal_maternity_cardinal" property="personalMaternityCardinal"/>
        <result column="unit_enterprise_annuity" property="unitEnterpriseAnnuity"/>
        <result column="personal_enterprise_annuity" property="personalEnterpriseAnnuity"/>
        <result column="commercial_insurance" property="commercialInsurance"/>
        <result column="emolument_multiple" property="emolumentMultiple"/>
        <result column="personal_maternity" property="personalMaternity"/>
        <result column="unit_maternity" property="unitMaternity"/>
        <result column="unit_late_fee" property="unitLateFee"/>
        <result column="unit_other_fee" property="unitOtherFee"/>
        <result column="personal_other_fee" property="personalOtherFee"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="total" property="total"/>
        <result column="bill_used" property="billUsed"/>
        <result column="reason" property="reason"/>
        <result column="is_used" property="isUsed"/>
        <result column="salary" property="salary"/>
        <result column="pre_tax_salary" property="preTaxSalary"/>
        <result column="personal_tax" property="personalTax"/>
        <result column="personal_tax_make_up" property="personalTaxMakeUp"/>
        <result column="real_salary" property="realSalary"/>
        <result column="other_salary" property="otherSalary"/>
        <result column="tax_calculation_method" property="taxCalculationMethod"/>
        <result column="current_year" property="currentYear"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="project_type" property="projectType"/>
        <result column="people_num" property="peopleNum"/>
        <result column="total_occurrence" property="totalOccurrence"/>
        <result column="highest_education" property="highestEducation"/>
        <result column="board_date" property="boardDate"/>
        <result column="resignationDate" property="resignationDate"/>
        <result column="taxation_fee" property="taxationFee"/>
        <result column="contract_no" property="contractNo"/>
        <collection property="hrBillDetailItemsList" javaType="java.util.ArrayList"
                    ofType="cn.casair.dto.HrBillDetailItemsDTO">
            <result column="itemId" property="id"/>
            <result column="expense_name" property="expenseName"/>
            <result column="expense_type" property="expenseType"/>
            <result column="calculation_num" property="calculationNum"/>
            <result column="amount" property="amount"/>
            <result column="is_default" property="isDefault"/>
        </collection>
    </resultMap>

    <select id="exportBankStatement" resultType="cn.casair.dto.excel.HrBankStatementExport">
        SELECT
            hse.owned_bank,
            hse.salary_card_num,
            hbd.`name`,
            hbd.real_salary
        FROM hr_bill_detail hbd
            LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hbd.staff_id
            AND hse.is_delete = 0
        WHERE hbd.is_delete = 0
            <if test="billIds!=null and billIds.size()>0">
                AND hbd.bill_id IN
                <foreach collection="billIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="billDetailIds!=null and billDetailIds.size()>0">
                AND hbd.id IN
                <foreach collection="billDetailIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            AND hbd.is_used = 1
    </select>

    <select id="getByBillId" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_bill_detail
        WHERE is_delete = 0
        AND bill_id = #{billId}
    </select>

    <select id="getBillDetailByBill" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            hbd.*,
            hb.created_date AS billCreatedDate
        FROM
            hr_bill_detail hbd
            LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        WHERE
            hbd.is_delete = 0
            AND hb.is_delete = 0
            AND hb.pay_year = #{params.payYear}
            AND hb.pay_monthly = #{params.payMonthly}
            AND hb.bill_type = #{params.billType}
            AND hb.bill_purpose = #{params.billPurpose}
            AND hb.is_official = 1
            AND hbd.is_used = 1
        ORDER BY hbd.created_date DESC
    </select>

    <select id="getNormalSalaryExportList" resultType="cn.casair.dto.excel.HrBillDetailExport">
        SELECT
            GROUP_CONCAT(DISTINCT nsd.id) ids,
            ANY_VALUE (nsd.name) name,
            ANY_VALUE (hts.staff_status) staff_status,
            ANY_VALUE (hts.certificate_type) certificate_type,
            ANY_VALUE (nsd.certificate_num) certificate_num,
            ANY_VALUE (nsd.pay_year) pay_year,
            ANY_VALUE (nsd.pay_monthly) pay_monthly,
            SUM(nsd.salary) salary,
            SUM(nsd.personal_pension) + SUM(nsd.personal_social_security_make_up) personal_pension,
            SUM(nsd.personal_unemployment) personal_unemployment,
            SUM(nsd.personal_medical) personal_medical,
            SUM(nsd.personal_maternity) personal_maternity,
            SUM(nsd.personal_accumulation_fund) + SUM(nsd.personal_accumulation_fund_make_up) personal_accumulation_fund,
            SUM( IF ( ISNULL( hsd.children_education ), 0, hsd.children_education ) ) children_education,
            SUM( IF ( ISNULL( hsd.continuing_education ), 0, hsd.continuing_education ) ) continuing_education,
            SUM( IF ( ISNULL( hsd.housing_loan ), 0, hsd.housing_loan ) ) housing_loan,
            SUM( IF ( ISNULL( hsd.housing_rent ), 0, hsd.housing_rent ) ) housing_rent,
            SUM( IF ( ISNULL( hsd.support_elderly ), 0, hsd.support_elderly ) ) support_elderly,
            SUM( IF ( ISNULL( hsd.cumulative_infant_care ), 0, hsd.cumulative_infant_care ) ) cumulative_infant_care
        FROM
             bill_normal_salary_detail nsd
            LEFT JOIN hr_talent_staff hts ON hts.id = nsd.staff_id AND hts.is_delete = 0
            LEFT JOIN hr_special_deduction hsd ON hsd.client_id = nsd.client_id AND hsd.talent_staff_id = nsd.staff_id AND YEAR ( hsd.start_date ) = nsd.pay_year AND MONTH ( hsd.start_date ) = nsd.pay_monthly AND hsd.is_delete = 0
        WHERE
            1 = 1
            <if test="params.ids!=null and params.ids.size()>0">
                AND nsd.id IN
                <foreach collection="params.ids" open="(" separator="," close=")" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="permissionClient!=null and permissionClient.size()>0">
                AND nsd.client_id IN
                <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.clientIds!=null and params.clientIds.size()>0">
                AND nsd.client_id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.payYear!=null">
                AND nsd.pay_year = #{params.payYear}
            </if>
            <if test="params.payMonthly!=null">
                AND nsd.pay_monthly = #{params.payMonthly}
            </if>
            <if test="params.isExport != null">
                AND nsd.is_export = #{params.isExport}
            </if>
        GROUP BY nsd.certificate_num
    </select>


    <select id="getDetailNormalSalary" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            ANY_VALUE ( hbd.`name` ) `name`,
            ANY_VALUE ( hts.certificate_type ) certificate_type,
            ANY_VALUE ( hbd.certificate_num ) certificate_num,
            ANY_VALUE ( hbd.bill_id ) bill_id,
            ANY_VALUE (IF(ISNULL(bdg.grant_state),0,bdg.grant_state)) grant_state,
            ANY_VALUE ( hbd.pay_year ) pay_year,
            ANY_VALUE ( hbd.pay_monthly ) pay_monthly,
            SUM( IF (hbd.salary = 0 OR hbd.salary IS NULL,
                ( hbd.personal_pension + hbd.personal_unemployment + hbd.personal_medical + hbd.personal_maternity + hbd.personal_accumulation_fund ),
                ( hbd.salary + hbd.personal_social_security_make_up + hbd.personal_accumulation_fund_make_up ))) salary,
            SUM(hbd.personal_pension) personal_pension,
            SUM(hbd.personal_unemployment) personal_unemployment,
            SUM(hbd.personal_medical) personal_medical,
            SUM(hbd.personal_maternity) personal_maternity,
            SUM(hbd.personal_accumulation_fund) personal_accumulation_fund
        FROM
            hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        LEFT JOIN hr_talent_staff hts ON hts.id = hbd.staff_id AND hts.is_delete = 0
        LEFT JOIN hr_bill_detail_grant bdg on bdg.id = hbd.bill_detail_grant_id
        WHERE hbd.is_delete = 0 AND hb.is_delete = 0 AND hb.review_state = 1
            AND hb.is_official = 1 AND hbd.is_used = 1 AND hb.bill_type = 0
            AND hbd.certificate_num IN
            <foreach collection="idNoList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        GROUP BY hbd.bill_id,hbd.certificate_num
        ORDER BY pay_year,pay_monthly DESC
    </select>

    <select id="getStaffSalaryList" resultType="cn.casair.dto.HrStaffSalaryDTO">
        SELECT
            hbd.id,
            hbd.pay_year,
            hbd.pay_monthly
        FROM
            hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hb.id = hbd.bill_id
        LEFT JOIN hr_bill_detail_grant hbdg ON hbd.id = hbdg.bill_detail_id
        WHERE
            hbd.is_delete = 0
          AND hbd.staff_id = #{staffId}
          AND hb.review_state = 1
          AND hb.is_official = 1
          AND hb.is_delete = 0
          AND hb.bill_type = 0
          AND hbdg.is_delete = 0
          AND hbdg.grant_state = 1
        ORDER BY
            hbd.pay_year,
            hbd.pay_monthly
    </select>

    <select id="getStaffParamsByClientId" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            hts.id   AS staffId,
            hts.system_num,
            hts.name,
            hts.certificate_num,
            hts.phone,
            hts.client_id,
            hts.staff_status,
            hts.personnel_type,
            hts.iz_insured,
            hts.highest_education,
            IF(swe.board_date IS NULL ,hswe.board_date,swe.board_date) board_date,
            IF ( hts.staff_status IN ( 10, 11 ), hads.departure_date, hswe.departure_date ) resignationDate,
            hts.supplementary_payment,
            hse.pay_year emolumentPayYear,
            hse.pay_monthly emolumentPayMonth,
            hse.social_security_num,
            hse.social_security_cardinal,
            hse.social_security_cardinal_personal,
            hse.medical_insurance_num,
            hse.medical_insurance_cardinal,
            hse.medical_insurance_cardinal_personal,
            hse.accumulation_fund_num,
            hse.accumulation_fund_cardinal,
            hse.unit_pension_cardinal,
            hse.unit_unemployment_cardinal,
            hse.unit_maternity_cardinal,
            hse.work_injury_cardinal,
            hse.unit_large_medical_expense,
            hse.replenish_work_injury_expense,
            hse.personal_pension_cardinal,
            hse.personal_unemployment_cardinal,
            hse.personal_large_medical_expense,
            hse.personal_maternity_cardinal,
            hse.unit_enterprise_annuity,
            hse.personal_enterprise_annuity,
            hse.commercial_insurance,
            hc.social_security_type_id,
            hss.area AS socialSecurityArea,
            hr.actual_retire_date,
            hse.basic_wage,
            hse.salary
        FROM hr_talent_staff hts
        LEFT JOIN hr_staff_emolument hse ON hts.id = hse.staff_id AND hse.is_delete = 0
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        LEFT JOIN hr_social_security hss ON hc.social_security_type_id = hss.id
        LEFT JOIN hr_retire hr ON hts.id = hr.staff_id
        LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id AND hts.client_id = hswe.client_id  AND (CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END) AND hswe.is_delete = 0
        LEFT JOIN hr_staff_work_experience swe ON swe.staff_id = hts.id AND swe.client_id = hts.client_id AND swe.iz_default = 1 and swe.is_delete = 0
        LEFT JOIN hr_apply_departure_staff hads ON hads.id = hts.departure_staff_id
        WHERE hts.is_delete = 0
          AND hts.iz_default = 0
          AND hts.client_id IN
        <foreach collection="clientIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        ORDER BY hts.created_date DESC
    </select>

    <select id="getStaffParamsByStaffId" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT hts.id   AS staffId,
               hts.staff_status,
               hts.personnel_type,
               hts.iz_insured,
               hts.resignation_date,
               hse.social_security_num,
               hse.social_security_cardinal,
               hse.social_security_cardinal_personal,
               hse.medical_insurance_num,
               hse.medical_insurance_cardinal,
               hse.medical_insurance_cardinal_personal,
               hse.accumulation_fund_num,
               hse.accumulation_fund_cardinal,
               hse.unit_pension_cardinal,
               hse.unit_unemployment_cardinal,
               hse.unit_maternity_cardinal,
               hse.work_injury_cardinal,
               hse.unit_large_medical_expense,
               hse.replenish_work_injury_expense,
               hse.personal_pension_cardinal,
               hse.personal_unemployment_cardinal,
               hse.personal_large_medical_expense,
               hse.personal_maternity_cardinal,
               hse.unit_enterprise_annuity,
               hse.personal_enterprise_annuity,
               hse.commercial_insurance,
               hss.area AS socialSecurityArea,
               hp.service_charge_type,
               hp.service_charge,
               hp.agreement_start_date
        FROM hr_talent_staff hts
                 LEFT JOIN hr_staff_emolument hse ON hts.id = hse.staff_id AND hse.is_delete = 0
                 LEFT JOIN hr_client hc ON hts.client_id = hc.id
                 LEFT JOIN hr_social_security hss ON hc.social_security_type_id = hss.id
                 LEFT JOIN hr_protocol hp ON hts.protocol_id = hp.id
        WHERE hts.id = #{staffId}
    </select>

    <select id="getListByBillId" resultMap="UnrecordedEmployee">
        SELECT
            hbd.*,
            hb.client_id,
            hbdi.id itemId,
            hbdi.expense_name,
            hbdi.expense_type,
            hbdi.calculation_num,
            hbdi.amount,
            hbdi.is_default,
            hts.highest_education,
            IF(swe.board_date IS NULL ,hswe.board_date,swe.board_date) board_date,
            IF ( hts.staff_status IN ( 10, 11 ), hads.departure_date, hswe.departure_date ) resignationDate
        FROM hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON  hbd.bill_id = hb.id
        LEFT JOIN hr_bill_detail_items hbdi ON hbdi.bill_detail_id = hbd.id AND hbdi.is_delete = 0
        LEFT JOIN hr_talent_staff hts ON hbd.staff_id = hts.id
        LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id AND (CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END) AND hswe.is_delete = 0
        LEFT JOIN hr_staff_work_experience swe ON swe.staff_id = hts.id AND swe.client_id = hts.client_id AND swe.iz_default = 1 AND swe.is_delete = 0
        LEFT JOIN hr_apply_departure_staff hads ON hads.id = hts.departure_staff_id
        WHERE
            hbd.is_delete = 0
            AND hbd.bill_id = #{billId}
            <if test="isUsed != null">
                AND hbd.is_used = #{isUsed}
            </if>
        ORDER BY
            hbd.sort_value, hbd.created_date DESC
    </select>

    <select id="getListByIds" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT hbd.*,
            hb.unit_pension_scale,
            hb.unit_unemployment_scale,
            hb.unit_medical_scale,
            hb.work_injury_scale,
            hb.personal_pension_scale,
            hb.personal_unemployment_scale,
            hb.personal_medical_scale,
            hb.unit_accumulation_fund_scale,
            hb.personal_accumulation_fund_scale,
            hb.client_id,
            hts.highest_education,
            hts.supplementary_payment,
            hse.pay_year emolumentPayYear,
            hse.pay_monthly emolumentPayMonth,
            IF(swe.board_date IS NULL ,hswe.board_date,swe.board_date) board_date,
            IF( hts.staff_status IN ( 10, 11 ), hads.departure_date, hswe.departure_date ) resignationDate
        FROM hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        LEFT JOIN hr_talent_staff hts ON hts.id = hbd.staff_id
        LEFT JOIN hr_staff_emolument hse ON hts.id = hse.staff_id AND hse.is_delete = 0
        LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id AND (CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END) AND hswe.is_delete = 0
        LEFT JOIN hr_staff_work_experience swe ON swe.staff_id = hts.id AND swe.client_id = hts.client_id AND swe.iz_default = 1 AND swe.is_delete = 0
        LEFT JOIN hr_apply_departure_staff hads ON hads.id = hts.departure_staff_id
        WHERE 1 = 1
        <if test="ids != null and ids.size > 0 ">
            AND hbd.id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        ORDER BY hbd.created_date DESC
    </select>

    <select id="getStaffSalaryDetailById" resultType="cn.casair.dto.HrStaffSalaryDetailDTO">
        SELECT hbd.id,
               hbd.pay_year,
               hbd.pay_monthly,
               hc.client_name,
               hc.enterprise_nature,
               hts.`name` staffName,
               hts.certificate_num,
               hbd.salary,
               hb.unit_pension_scale,
               hbd.unit_pension,
               hb.unit_unemployment_scale,
               hbd.unit_unemployment,
               hb.unit_medical_scale,
               hbd.unit_medical,
               hb.work_injury_scale,
               hbd.work_injury,
               hbd.unit_subtotal,
               hb.personal_pension_scale,
               hbd.personal_pension,
               hb.personal_unemployment_scale,
               hbd.personal_unemployment,
               hb.personal_medical_scale,
               hbd.personal_medical,
               hbd.personal_subtotal,
               hb.unit_accumulation_fund_scale,
               hbd.unit_accumulation_fund,
               hb.personal_accumulation_fund_scale,
               hbd.personal_accumulation_fund,
               hbd.personal_social_security_make_up,
               hbd.personal_accumulation_fund_make_up,
               hbd.personal_tax,
               hbd.real_salary
        FROM hr_bill_detail hbd
                 LEFT JOIN hr_bill hb ON hb.id = hbd.bill_id
                 LEFT JOIN hr_talent_staff hts ON hts.id = hbd.staff_id
            AND hts.is_delete = 0
                 LEFT JOIN hr_client hc ON hc.id = hts.client_id
            AND hc.is_delete = 0
        WHERE hbd.is_delete = 0
          AND hb.review_state = 1
          AND hb.is_official = 1
          AND hb.is_delete = 0
          AND hb.bill_state = 1
          AND hbd.id = #{billDetailId}
    </select>

    <select id="getListByDynamicFields" resultType="java.util.HashMap">
        SELECT
            hl.bill_id AS billId,
            hl.staff_id AS staffId,
            <foreach collection="sqlFields" open="" separator="," close="," item="val">
                IF(${val.sqlField} IS NULL , 0, ${val.sqlField} ) as ${val.key}
            </foreach>
            hl.id
        FROM hr_bill_detail hl
        WHERE hl.is_delete = 0
          and hl.is_used = 1
          AND hl.bill_id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <sql id="find_sql">
        SELECT
            *
        FROM
        (
            SELECT
                hbd.id,
                hbd.staff_id,
                hfr.client_id,
                hc.unit_number,
                hc.client_name,
                hbd.name,
                hbd.certificate_num,
                hbd.pay_year,
                hbd.pay_monthly,
                STR_TO_DATE( CONCAT( ANY_VALUE ( hbd.pay_year ), '-', ANY_VALUE ( hbd.pay_monthly ), '-', 01 ), '%Y-%m-%d' ) payment_date,
                hbd.salary,
                hbd.personal_subtotal,
                hbd.unit_subtotal,
                hbd.social_security_total,
                hbd.unit_accumulation_fund + hbd.unit_accumulation_fund_make_up AS unitAccumulationFundSubtotal,
                hbd.personal_accumulation_fund + hbd.personal_accumulation_fund_make_up AS personalAccumulationFundSubtotal,
                hbd.accumulation_fund_total,
                hbd.other_salary,
                hbd.service_fee,
                hbd.total,
                hbd.is_used
            FROM
                hr_bill_detail hbd
            LEFT JOIN hr_fee_review hfr ON hfr.id = hbd.bill_id
            LEFT JOIN hr_client hc ON hc.id = hfr.client_id AND hc.is_delete = 0
            WHERE hbd.is_delete = 0 AND hbd.is_used = 1 AND hfr.`status` = 1
        ) hbd
        <where>
            <if test="params.clientIdList!=null and params.clientIdList.size()>0">
                AND hbd.client_id IN
                <foreach collection="params.clientIdList" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.unitNumber!=null and params.unitNumber!=''">
                AND hbd.unit_number LIKE CONCAT('%', #{params.unitNumber}, '%')
            </if>
            <if test="params.clientId!=null and params.clientId!=''">
                AND hbd.client_id = #{params.clientId}
            </if>
            <if test="params.name!=null and params.name!=''">
                AND hbd.name LIKE CONCAT('%', #{params.name}, '%')
            </if>
            <if test="params.certificateNum!=null and params.certificateNum!=''">
                AND hbd.certificate_num LIKE CONCAT('%', #{params.certificateNum}, '%')
            </if>
            <if test="params.ids!=null and params.ids.size>0">
                AND hbd.id IN
                <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.payYear!=null">
                AND hbd.pay_year = #{params.payYear}
            </if>
            <if test="params.payMonthly!=null">
                AND hbd.pay_monthly = #{params.payMonthly}
            </if>
            <if test="params.paymentDateStart!=null">
                AND hbd.payment_date &gt;= #{params.paymentDateStart}
            </if>
            <if test="params.paymentDateEnd!=null">
                AND hbd.payment_date &lt;= #{params.paymentDateEnd}
            </if>
        </where>
    </sql>

    <select id="findPage" resultType="cn.casair.dto.HrBillDetailDTO">
        <include refid="find_sql"/>
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY hbd.pay_year ${params.order}, hbd.pay_monthly ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY
                hbd.pay_year DESC,
                hbd.pay_monthly DESC
            </otherwise>
        </choose>
    </select>

    <select id="getAvgSalaryMonthly" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
        hbd.certificate_num,
        AVG(hbd.salary) AS avgSalary
        FROM
        hr_bill_detail hbd
        LEFT JOIN hr_fee_review hfr ON hfr.id = hbd.bill_id
        LEFT JOIN hr_client hc ON hc.id = hfr.client_id AND hc.is_delete = 0
        WHERE
        hbd.is_delete = 0 AND hbd.is_used = 1 AND hfr.`status` = 1
        <if test="params.clientIdList!=null and params.clientIdList.size()>0">
            AND hfr.client_id IN
            <foreach collection="params.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientId!=null and params.clientId!=''">
            AND hfr.client_id = #{params.clientId}
        </if>
        <if test="params.payYear!=null">
            AND hfr.pay_year = #{params.payYear}
        </if>
        <if test="params.startMonth!=null">
            AND hfr.pay_monthly <![CDATA[>=]]> #{params.startMonth}
        </if>
        <if test="params.endMonth!=null">
            AND hfr.pay_monthly <![CDATA[<=]]> #{params.endMonth}
        </if>
        GROUP BY hbd.certificate_num
    </select>


    <select id="getDataByIds" resultType="cn.casair.dto.HrDataDetailDTO">
        <include refid="find_sql"/>
    </select>

    <select id="getBillDetailByIds" resultMap="UnrecordedEmployee">
        SELECT
        hbd.*,
        hbdi.id itemId,
        hbdi.expense_name,
        hbdi.expense_type,
        hbdi.amount,
        hbdi.is_default
        FROM hr_bill_detail hbd
        LEFT JOIN hr_bill_detail_items hbdi ON hbdi.bill_detail_id = hbd.id
        AND hbdi.is_delete = 0
        WHERE
        hbd.is_delete = 0 AND hbd.is_used = 1
        AND hbd.bill_id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        <if test="billDetailIds!=null and billDetailIds.size() > 0">
            AND hbd.id IN
            <foreach collection="billDetailIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        ORDER BY
        hbd.sort_value, hbd.created_date DESC
    </select>

    <select id="getListByBillIdAndStaffId" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
        hbd.*,
        hbdi.id itemId,
        hbdi.expense_name,
        hbdi.expense_type,
        hbdi.amount
        FROM hr_bill_detail hbd
        LEFT JOIN hr_bill_detail_items hbdi ON hbdi.bill_detail_id = hbd.id
        AND hbdi.is_delete = 0
        WHERE
        hbd.is_delete = 0
        <if test="billIds!=null and billIds.size() > 0">
            AND hbd.bill_id IN
            <foreach collection="billIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="staffIds!=null and staffIds.size() > 0">
            AND hbd.staff_id IN
            <foreach collection="staffIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="isUsed != null">
            AND hbd.is_used = #{isUsed}
        </if>
        ORDER BY
        hbd.sort_value, hbd.created_date DESC
    </select>

    <select id="selectSecurityBill" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            hbd.*,
            hb.created_date AS billCreatedDate
        FROM
            hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        WHERE hbd.is_delete = 0 AND hb.is_delete = 0 AND hbd.is_used = 1
        AND hb.bill_type = 1 AND hb.is_official = 1
        AND hbd.pay_year = #{params.payYear}
        AND hbd.pay_monthly = #{params.payMonthly}
        AND hbd.staff_id = #{params.staffId}
        <if test="params.clientId != null">
            AND hb.client_id = #{params.clientId}
        </if>
        ORDER BY hbd.created_date DESC LIMIT 1
    </select>

    <select id="getListByBillIdBatch" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            hc.id AS clientId,
            hb.bill_type,
            hbd.*,
            hts.highest_education,
            IF(swe.board_date IS NULL ,hswe.board_date,swe.board_date) board_date,
            IF( hts.staff_status IN ( 10, 11 ), hads.departure_date, hswe.departure_date ) resignationDate
        FROM
            hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id AND hb.is_delete = 0
        LEFT JOIN hr_client hc ON hb.client_id = hc.id
        LEFT JOIN hr_talent_staff hts ON hts.id = hbd.staff_id
        LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id AND (CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END) AND hswe.is_delete = 0
        LEFT JOIN hr_staff_work_experience swe ON swe.staff_id = hts.id AND swe.client_id = hts.client_id AND swe.iz_default = 1 AND swe.is_delete = 0
        LEFT JOIN hr_apply_departure_staff hads ON hads.id = hts.departure_staff_id
        WHERE
            hbd.is_delete = 0
        AND hbd.is_used = #{isUsed}
        AND hbd.bill_id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        ORDER BY
            hbd.sort_value,
            hbd.created_date DESC
    </select>

    <select id="getBonusExportList" resultType="cn.casair.dto.excel.HrAnnualBonusExport">
        SELECT
            hbd.bill_id,
            hbd.`name`,
            hts.staff_status,
            hts.certificate_type,
            hbd.certificate_num,
            hbd.total total
        FROM
            `hr_bill_detail` hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        LEFT JOIN hr_talent_staff hts ON hts.id = hbd.staff_id AND hts.is_delete = 0
        LEFT JOIN hr_client hc ON hc.id = hts.client_id AND hc.is_delete = 0
        WHERE
            hbd.is_delete = 0
        AND hb.is_delete = 0
        AND hb.is_official = 1
        AND hbd.tax_calculation_method = 1
        <if test="params.ids!=null and params.ids.size()>0">
            AND hbd.bill_id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="permissionClient!=null and permissionClient.size()>0">
            AND hb.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientIds!=null and params.clientIds.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.payYear!=null">
            AND hb.pay_year = #{params.payYear}
        </if>
        <if test="params.payMonthly!=null">
            AND hb.pay_monthly = #{params.payMonthly}
        </if>
        <if test="params.isBonusExport!=null">
            AND hb.is_bonus_export = #{params.isBonusExport}
        </if>
        ORDER BY hc.id,
        hbd.staff_id,
        hbd.pay_year,
        hbd.pay_monthly
    </select>

    <select id="findStaffInfo" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            hc.client_name,
            hc.unit_number,
            hc2.client_name parentClientName,
            hc2.unit_number parentUnitNumber,
            hts.id AS staffId,
            hts.system_num,
            hts.`name`,
            hts.certificate_num,
            hts.phone,
            hts.client_id,
            hts.staff_status,
            hts.personnel_type,
            hts.iz_insured
        FROM
            hr_talent_staff hts
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        LEFT JOIN hr_client hc2 ON hc.parent_id = hc2.id
        WHERE hts.is_delete = 0 AND hts.iz_default = 0
        <if test="staffIds!=null and staffIds.size()>0">
            AND hts.id IN
            <foreach collection="staffIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        ORDER BY
            hts.created_date DESC
    </select>

    <select id="selectSalaryDetail" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            hc.client_name,
            hc.unit_number,
            hc2.client_name parentClientName,
            hc2.unit_number parentUnitNumber,
            hbd.*
        FROM
            hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        LEFT JOIN hr_talent_staff hts ON hbd.staff_id = hts.id
        LEFT JOIN hr_client hc ON hb.client_id = hc.id
        LEFT JOIN hr_client hc2 ON hc.parent_id = hc2.id
        WHERE
            hts.is_delete = 0
        AND hts.iz_default = 0
        AND hbd.bill_id = #{id}
        ORDER BY
            hbd.created_date DESC
    </select>

    <select id="exportSalaryPayment" resultType="cn.casair.dto.excel.HrBankStatementExport">
        SELECT
            hse.owned_bank owned_bank,
            ct.item_name owned_bank_label,
            hse.salary_card_num salary_card_num,
            hbd.`name` `name`,
            hbd.real_salary real_salary
        FROM
            hr_bill_detail hbd
        LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hbd.staff_id AND hse.is_delete = 0
        LEFT JOIN code_table ct ON ct.parent_id = (SELECT id FROM code_table WHERE inner_name = 'ownedBank') AND ct.item_value = hse.owned_bank
        WHERE
            hbd.is_delete = 0
        AND hbd.is_used = 1
        <if test="billIdList != null and billIdList.size() > 0">
            AND hbd.bill_id IN
            <foreach collection="billIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="billDetailIdList != null and billDetailIdList.size() > 0">
            AND hbd.id IN
            <foreach collection="billDetailIdList" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="averageMonthlyIncome" resultType="cn.casair.dto.HrBillDetailDTO">
        WITH a AS (
            SELECT
                hbd.`name` `name`, hbd.staff_id staff_id, hbd.salary salary, hbd.real_salary real_salary,hbd.pay_year pay_year, hbd.pay_monthly pay_monthly,
                STR_TO_DATE( concat( ANY_VALUE ( hbd.pay_year ), '-', ANY_VALUE ( hbd.pay_monthly ), '-', 01 ), '%Y-%m-%d' ) payment_date
            FROM
                hr_bill_detail hbd
            LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
            WHERE hbd.is_delete = 0 AND hb.is_delete = 0 AND hbd.is_used = 1 AND hb.is_official = 1
            AND hbd.is_used = 1 AND hb.bill_type = 0  AND hbd.tax_calculation_method = 0
            AND hbd.staff_id = #{staffId}
        )SELECT
            ANY_VALUE(`name`) `name`,
            staff_id,
            ANY_VALUE(salary) salary,
            ANY_VALUE(real_salary) real_salary,
            ANY_VALUE(pay_year) pay_year,
            ANY_VALUE(pay_monthly) pay_monthly,
            payment_date
        FROM
            a
        WHERE a.payment_date >= #{dateMin} AND a.payment_date <![CDATA[ <= ]]> #{dateMax}
        GROUP BY a.staff_id,a.payment_date ORDER BY a.staff_id,a.payment_date DESC
    </select>

    <select id="getPreTaxSalaryTotal" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            SUM( hbd.pre_tax_salary ) pre_tax_salary
        FROM
            hr_bill_detail hbd
            LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        WHERE
            hbd.is_delete = 0
            AND hbd.is_used = 1
            AND hb.bill_type = 0
            AND hbd.tax_calculation_method = 0
            AND hbd.bill_id IN
            <foreach collection="billList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>

    <select id="selectSalaryBill" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            hbd.*
        FROM
            hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        WHERE hbd.is_delete = 0 AND hbd.is_used = 1
        AND hb.bill_type = 0 AND hb.is_official = 1
        AND hbd.pay_year = #{params.payYear}
        AND hbd.pay_monthly = #{params.payMonthly}
        <!--AND hbd.staff_id = #{params.staffId}
        <if test="params.clientId != null">
            AND hb.client_id = #{params.clientId}
        </if>
        <if test="params.id != null">
            AND hbd.id != #{params.id}
        </if>-->
        ORDER BY hbd.created_date DESC
    </select>

    <select id="getTotalData" resultType="cn.casair.dto.HrBillDetailDTO">
        WITH a AS (
            <include refid="find_sql"/>
        ) SELECT
            SUM(salary) salary,
            SUM(personal_subtotal) personal_subtotal,
            SUM(unit_subtotal) unit_subtotal,
            SUM(social_security_total) social_security_total,
            SUM(unitAccumulationFundSubtotal) unitAccumulationFundSubtotal,
            SUM(personalAccumulationFundSubtotal) personalAccumulationFundSubtotal,
            SUM(accumulation_fund_total) accumulation_fund_total,
            SUM(other_salary) other_salary,
            SUM(service_fee) service_fee,
            SUM(total) total
        FROM
            a
    </select>

    <select id="getTotalByClientId" resultType="cn.casair.domain.HrBillDetail">
        SELECT
            SUM( social_security_total ) social_security_total,
            SUM( accumulation_fund_total ) accumulation_fund_total,
            SUM( real_salary ) real_salary
        FROM
            hr_bill_detail hbd
            LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        WHERE hbd.is_delete = 0 AND hb.is_delete = 0 AND hb.review_state = 1
            AND hb.is_official = 1 AND hbd.is_used = 1
            AND hb.bill_type = #{billType} AND hb.pay_year = #{payYear} AND hb.pay_monthly = #{payMonth}
            AND hb.client_id IN
            <foreach collection="clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>

    <select id="getListBatch" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT * FROM (
            SELECT
                hc1.client_name,
                hc1.unit_number,
                hc2.client_name parentClientName,
                hc2.unit_number parentUnitNumber,
                hc.id AS clientId,
                hb.bill_type,
                hbd.*,
                CONCAT(hbd.pay_year,'-',IF(hbd.pay_monthly>9,hbd.pay_monthly,CONCAT('0',hbd.pay_monthly))) AS payment_date
            FROM
                hr_bill_detail hbd
            LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id AND hb.is_delete = 0
            LEFT JOIN hr_client hc ON hb.client_id = hc.id
            LEFT JOIN hr_talent_staff hts ON hbd.staff_id = hts.id AND hts.is_delete = 0
            LEFT JOIN hr_client hc1 ON hts.client_id = hc1.id AND hc1.is_delete = 0
            LEFT JOIN hr_client hc2 ON hc1.parent_id = hc2.id AND hc2.is_delete = 0
            WHERE hbd.is_delete = 0 AND hbd.is_used = #{isUsed}
        ) hbd
        WHERE hbd.is_delete = 0 AND hbd.bill_id IN
        <foreach collection="params.billIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        <if test="params.feeReviewDate != null">
            AND hbd.payment_date = #{params.feeReviewDate}
        </if>
        <if test="params.paymentDateStart != null">
            AND hbd.payment_date >= #{params.paymentDateStart}
        </if>
        <if test="params.paymentDateEnd != null">
            AND hbd.payment_date <![CDATA[ <= ]]> #{params.paymentDateEnd}
        </if>
        ORDER BY hbd.sort_value, hbd.created_date DESC
    </select>

    <select id="findByBillId" resultType="cn.casair.domain.HrBillDetail">
        SELECT
            a.*
        FROM
            hr_bill_detail a
            LEFT JOIN hr_bill b ON a.bill_id = b.id
        WHERE
            a.is_delete = 0 AND a.is_used = 1
            AND b.is_official = 1
            AND b.bill_type = #{billType}
            <if test="billIds != null and billIds.size() > 0">
                AND a.bill_id NOT IN
                <foreach collection="billIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
        ORDER BY a.created_date DESC
    </select>

    <select id="findListByBillId" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT hbd.*,
               bdg.grant_state
        FROM hr_bill_detail hbd
                 LEFT JOIN hr_bill_detail_grant bdg ON bdg.id = hbd.bill_detail_grant_id
        WHERE hbd.is_delete = 0
          AND hbd.bill_id = #{billId}
          AND hbd.is_used = #{isUsed}
        ORDER BY hbd.sort_value, hbd.created_date DESC
    </select>
    <select id="findSalaryPaymentList" resultType="cn.casair.dto.HrBillDetailDTO">
        WITH a AS (
        SELECT
        hbd.id,
        hbd.name,
        hbd.certificate_num,
        hse.salary_card_num,
        hbd.real_salary,
        IF
        ( isnull( bdg.grant_state ), 0, bdg.grant_state ) grantState,
        bdg.grant_date,
        IF
        ( bdg.grant_state = 1, concat( bdg.pay_year, '-', IF ( bdg.pay_monthly > 9, bdg.pay_monthly, concat( '0', bdg.pay_monthly )) ), NULL ) taxPeriod
        FROM
        hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        LEFT JOIN hr_client hc ON hc.id = hb.client_id
        LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hbd.staff_id
        LEFT JOIN hr_bill_detail_grant bdg ON bdg.id = hbd.bill_detail_grant_id
        WHERE
        hb.is_delete = 0
        AND hse.is_delete = 0
        AND hb.bill_type = 0
        AND hb.is_official = 1
        AND hb.review_state = 1
        AND hb.salary_payment_state = 1
        AND hbd.is_delete = 0
        AND hbd.is_used = 1
        AND hb.id = #{params.billId}
        <if test="params.name != null and params.name != ''">
            AND hbd.name like concat('%', #{params.name}, '%')
        </if>
        <if test="params.certificateNum != null and params.certificateNum != ''">
            AND hbd.certificate_num like concat('%', #{params.certificateNum}, '%')
        </if>
        <if test="params.salaryCardNum != null and params.salaryCardNum != ''">
            AND hse.salary_card_num like concat('%', #{params.salaryCardNum}, '%')
        </if>
        <if test="params.ids != null and params.ids.size() > 0">
            AND hbd.id in
            <foreach collection="params.ids" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
        ) SELECT
        a.*
        FROM
        a
        WHERE
        1 =1
        <if test="params.grantState != null">
            AND a.grantState = #{params.grantState}
        </if>
    </select>

    <select id="findListBatch" resultMap="UnrecordedEmployee">
        SELECT
            hc.id AS clientId,
            hb.bill_type,
            hbd.*,
            hts.highest_education,
            IF(swe.board_date IS NULL ,hswe.board_date,swe.board_date) board_date,
            IF( hts.staff_status IN ( 10, 11 ), hads.departure_date, hswe.departure_date ) resignationDate,
            hbdi.id itemId,
            hbdi.expense_name,
            hbdi.expense_type,
            hbdi.calculation_num,
            hbdi.amount,
            hbdi.is_default,
            hbsc.contract_no
        FROM
            hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id AND hb.is_delete = 0
        LEFT JOIN hr_bill_detail_items hbdi ON hbdi.bill_detail_id = hbd.id AND hbdi.is_delete = 0
        LEFT JOIN hr_bill_sinopec_contract hbsc ON hbd.project_name = hbsc.project_name AND hbsc.is_delete = 0
        LEFT JOIN hr_client hc ON hb.client_id = hc.id
        LEFT JOIN hr_talent_staff hts ON hts.id = hbd.staff_id
        LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id AND (CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END) AND hswe.is_delete = 0
        LEFT JOIN hr_staff_work_experience swe ON swe.staff_id = hts.id AND swe.client_id = hts.client_id AND swe.iz_default = 1 AND swe.is_delete = 0
        LEFT JOIN hr_apply_departure_staff hads ON hads.id = hts.departure_staff_id
        WHERE
            hbd.is_delete = 0
            AND hbd.is_used = #{isUsed}
            AND hbd.bill_id IN
            <foreach collection="billIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        ORDER BY hbd.sort_value, hbd.created_date DESC
    </select>

    <select id="getSalaryBillBatchClientId" resultMap="UnrecordedEmployee">
        SELECT
            hbd.*,
            hbdi.id itemId,
            hbdi.expense_name,
            hbdi.expense_type,
            hbdi.amount,
            hbdi.is_default
        FROM hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        LEFT JOIN hr_bill_detail_items hbdi ON hbdi.bill_detail_id = hbd.id AND hbdi.is_delete = 0
        WHERE hbd.is_delete = 0 AND hb.is_delete = 0
        AND hbd.is_used = 1 AND hb.bill_type = 0 AND hb.is_official = 1
        AND hbd.pay_year = #{payYear}
        AND hbd.pay_monthly = #{payMonth}
        <if test="clientIdList != null and clientIdList.size() > 0">
            AND hb.client_id IN
            <foreach collection="clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
       <if test="idCardList != null and idCardList.size() > 0">
           AND hbd.certificate_num IN
           <foreach collection="idCardList" open="(" separator="," close=")" item="i">
               #{i}
           </foreach>
       </if>
        ORDER BY hbd.sort_value, hbd.created_date DESC
    </select>

    <select id="getTotalBySalaryBill" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
            SUM( hbd.total ) total
        FROM
            hr_bill_detail hbd
            LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        WHERE
            hbd.is_delete = 0
            AND hbd.is_used = 1
            AND hb.bill_type = 0
            AND hbd.tax_calculation_method = 1
            AND hbd.bill_id IN
            <foreach collection="billIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>

    <select id="countTaxCalculationMethod" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT hbd.tax_calculation_method) tax_calculation_method
        FROM
            hr_bill_detail hbd
        WHERE
            hbd.is_delete = 0
            AND hbd.is_used = 1
            AND hbd.bill_id IN
            <foreach collection="billIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>

    <!-- 批量插入 -->
    <insert id ="batchSave" parameterType="java.util.List" >
        insert into hr_bill_detail
        (
            id, system_num, name, certificate_num, phone, bill_id, staff_id, pay_year, pay_monthly, social_security_area,
            staff_status, personnel_type, iz_insured, social_security_num, social_security_cardinal,social_security_cardinal_personal, medical_insurance_num, medical_insurance_cardinal,medical_insurance_cardinal_personal, accumulation_fund_num, accumulation_fund_cardinal, unit_pension,
            unit_unemployment, unit_medical, work_injury, unit_social_security_make_up, unit_subtotal, personal_pension, personal_unemployment, personal_medical, personal_social_security_make_up, personal_subtotal,
            social_security_total, unit_accumulation_fund_make_up, unit_accumulation_fund, personal_accumulation_fund, personal_accumulation_fund_make_up, accumulation_fund_total, service_fee, total, bill_used, reason,
            is_used, salary, pre_tax_salary, personal_tax, real_salary, other_salary, sort_value, is_delete, created_by, last_modified_by,
            created_date, last_modified_date,current_year,project_code,project_name,people_num,total_occurrence,check_on,emolument_multiple,
            unit_pension_cardinal,unit_unemployment_cardinal,work_injury_cardinal,unit_maternity_cardinal,personal_pension_cardinal,personal_unemployment_cardinal,personal_maternity_cardinal,unit_maternity,personal_maternity,
            unit_large_medical_expense,replenish_work_injury_expense,personal_large_medical_expense,type,unit_enterprise_annuity,personal_enterprise_annuity,commercial_insurance,personal_tax_make_up,enterprise_tax,special_other,membership_fee,guarantee_bill_id
        )
        values
        <foreach collection ="list" item="item" index= "index" separator =",">
            (
            #{item.id}, #{item.systemNum}, #{item.name}, #{item.certificateNum}, #{item.phone}, #{item.billId}, #{item.staffId}, #{item.payYear}, #{item.payMonthly}, #{item.socialSecurityArea},
            #{item.staffStatus}, #{item.personnelType}, #{item.izInsured}, #{item.socialSecurityNum}, #{item.socialSecurityCardinal}, #{item.socialSecurityCardinalPersonal}, #{item.medicalInsuranceNum}, #{item.medicalInsuranceCardinal},#{item.medicalInsuranceCardinalPersonal}, #{item.accumulationFundNum}, #{item.accumulationFundCardinal}, #{item.unitPension},
            #{item.unitUnemployment}, #{item.unitMedical}, #{item.workInjury}, #{item.unitSocialSecurityMakeUp}, #{item.unitSubtotal}, #{item.personalPension}, #{item.personalUnemployment}, #{item.personalMedical}, #{item.personalSocialSecurityMakeUp}, #{item.personalSubtotal},
            #{item.socialSecurityTotal}, #{item.unitAccumulationFundMakeUp}, #{item.unitAccumulationFund}, #{item.personalAccumulationFund}, #{item.personalAccumulationFundMakeUp}, #{item.accumulationFundTotal}, #{item.serviceFee}, #{item.total}, #{item.billUsed}, #{item.reason},
            #{item.isUsed}, #{item.salary}, #{item.preTaxSalary}, #{item.personalTax}, #{item.realSalary}, #{item.otherSalary}, #{item.sortValue}, 0, #{item.createdBy}, #{item.lastModifiedBy},
            #{item.createdDate}, #{item.lastModifiedDate},#{item.currentYear},#{item.projectCode},#{item.projectName},#{item.peopleNum},#{item.totalOccurrence},#{item.checkOn},#{item.emolumentMultiple},
            #{item.unitPensionCardinal},#{item.unitUnemploymentCardinal},#{item.workInjuryCardinal},#{item.unitMaternityCardinal},#{item.personalPensionCardinal},#{item.personalUnemploymentCardinal},#{item.personalMaternityCardinal},#{item.unitMaternity},#{item.personalMaternity},
            #{item.unitLargeMedicalExpense},#{item.replenishWorkInjuryExpense},#{item.personalLargeMedicalExpense},#{item.type},#{item.unitEnterpriseAnnuity},#{item.personalEnterpriseAnnuity},#{item.commercialInsurance},#{item.personalTaxMakeUp},#{item.enterpriseTax},#{item.specialOther},#{item.membershipFee},#{item.guaranteeBillId}
            )
        </foreach >
    </insert >

    <delete id="deleteByBillIdBatch">
        DELETE FROM hr_bill_detail WHERE bill_id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </delete>

    <update id="updateGrantRelation">
        update hr_bill_detail set bill_detail_grant_id = #{billDetailGrantId} where id = #{billDetailId}
    </update>


    <select id="getProjectContractNo" resultType="cn.casair.dto.HrBillSinopecContractDTO">
        SELECT * FROM hr_bill_sinopec_contract WHERE is_delete = 0
    </select>

    <select id="findNotAccordStaff" resultType="cn.casair.dto.HrBillCompareResultDetailDTO">
        SELECT
            hc.client_name,
            hc.unit_number,
            hp.agreement_number,
            hbd.`name` AS staffName,
            hbd.certificate_num AS idNo,
            hbd.phone,
            hbd.staff_status,
            2 usedType
        FROM
            hr_bill_detail hbd
        LEFT JOIN hr_bill hb ON hb.id = hbd.bill_id
        LEFT JOIN hr_client hc ON hb.client_id = hc.id AND hc.is_delete = 0
        LEFT JOIN hr_protocol hp ON hp.client_id = hc.id AND hp.is_delete = 0 AND hp.use_status = 1
        WHERE hbd.is_delete = 0 AND hbd.is_used = 1
        AND hbd.certificate_num NOT IN
        <foreach collection="idCardList" open="(" close=")" separator="," item="i">
            #{i}
        </foreach>
        AND hbd.bill_id IN
        <foreach collection="billIds" open="(" close=")" separator="," item="i">
            #{i}
        </foreach>
    </select>

    <update id="updateSinopecContractNo">
        UPDATE hr_bill_sinopec_contract SET contract_no = #{contractNo}, last_modified_by = #{userName}, last_modified_date = now() WHERE is_delete = 0 AND project_name = #{projectName}
    </update>

    <insert id="insertSinopecContractNo">
       INSERT INTO `hr_bill_sinopec_contract` (`id`, `project_name`, `contract_no`, `created_by`, `created_date`) VALUES ( #{id}, #{projectName}, #{contractNo}, #{userName}, now());
    </insert>

    <select id="averageMonthlySecurity" resultMap="UnrecordedEmployee">
        WITH a AS (SELECT hbd.`name`                                                                                         `name`,
                          hbd.staff_id                                                                                       staff_id,
                          hbd.emolument_multiple                                                                             emolument_multiple,
                          hbd.unit_accumulation_fund                                                                         unit_accumulation_fund,
                          hbd.unit_accumulation_fund_make_up                                                                 unit_accumulation_fund_make_up,
                          hbd.pay_year                                                                                       pay_year,
                          hbd.pay_monthly                                                                                    pay_monthly,
                          STR_TO_DATE(concat(ANY_VALUE(hbd.pay_year), '-', ANY_VALUE(hbd.pay_monthly), '-', 01), '%Y-%m-%d') payment_date
                   FROM hr_bill_detail hbd
                            LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
                   WHERE hbd.is_delete = 0
                     AND hb.is_delete = 0
                     AND hbd.is_used = 1
                     AND hb.is_official = 1
                     AND hbd.is_used = 1
                     AND hb.bill_type = 1
                     AND hbd.staff_id = #{staffId})
        SELECT ANY_VALUE(`name`)                         `name`,
               staff_id,
               ANY_VALUE(emolument_multiple)             emolument_multiple,
               ANY_VALUE(unit_accumulation_fund)         unit_accumulation_fund,
               ANY_VALUE(unit_accumulation_fund_make_up) unit_accumulation_fund_make_up,
               ANY_VALUE(pay_year)                       pay_year,
               ANY_VALUE(pay_monthly)                    pay_monthly,
               payment_date
        FROM a
        WHERE a.payment_date >= #{dateMin}
          AND a.payment_date <![CDATA[ <= ]]> #{dateMax}
        GROUP BY a.staff_id, a.payment_date
        ORDER BY a.staff_id, a.payment_date DESC
    </select>
</mapper>
