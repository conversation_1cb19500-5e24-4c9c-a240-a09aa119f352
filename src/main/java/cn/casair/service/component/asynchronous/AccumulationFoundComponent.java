package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.HrAccumulationFund;
import cn.casair.domain.HrPlatformAccount;
import cn.casair.domain.HrSocialSecurity;
import cn.casair.dto.HrPlatformAccountDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrAccumulationFundImport;
import cn.casair.dto.excel.HrSocialSecurityImport;
import cn.casair.mapper.HrPlatformAccountMapper;
import cn.casair.repository.HrAccumulationFundRepository;
import cn.casair.repository.HrPlatformAccountRepository;
import cn.casair.repository.HrSocialSecurityRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrAppendixService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 公积金 社保 平台账号 导入
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class AccumulationFoundComponent {
    @Value("${file.temp-path}")
    private String tempPath;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final RedisCache redisCache;
    private final HrPlatformAccountMapper hrPlatformAccountMapper;
    //@Autowired
    private final HrAccumulationFundRepository hrAccumulationFundRepository;

    //@Autowired
    private final HrSocialSecurityRepository hrSocialSecurityRepository;

    //@Autowired
    private final HrPlatformAccountRepository hrPlatformAccountRepository;
    private final CodeTableService codeTableService;

    private static HashMap<String, String> platformTypeHashMap;


    @Async
    public void accumulationImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrAccumulationFundImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrAccumulationFundImport.class);

            ImportResultDTO importResult;

            int scale = 0;
            int listSize = result.getList().size();
            Map<String, List<HrAccumulationFundImport>> map = result.getList().stream().collect(Collectors.groupingBy(HrAccumulationFundImport::getTypeName));

            StringBuilder sbError = new StringBuilder();
            for (HrAccumulationFundImport hrAccumulationFundDTO : result.getList()) {

                HrAccumulationFund hrAccumulationFund = new HrAccumulationFund();
                try {
                    //重复数据
                    List<HrAccumulationFundImport> mapList = map.get(hrAccumulationFundDTO.getTypeName());
                    if (CollectionUtils.isNotEmpty(mapList) && mapList.size() > 1) {
                        throw new CommonException("导入Excel中该数据公积金类型名称重复!");
                    }
                    List<HrAccumulationFund> list = getHrAccumulationFunds(hrAccumulationFundDTO.getTypeName());
                    if (CollectionUtils.isNotEmpty(list)) {
                        sbError.append("公积金类型名称在数据库中已存在！");
                    }
                    Map<String, Integer> ownedBank = codeTableService.findCodeTableByInnerValue("ownedBank");
                    if (StringUtils.isNotBlank(hrAccumulationFundDTO.getPayeeBank())) {
                        Integer ownedBankValue = ownedBank.get(hrAccumulationFundDTO.getPayeeBank());
                        if (ownedBankValue == null) {
                            sbError.append("银行在系统中不存在!");
                        }
                    }
                    if (StringUtils.isEmpty(sbError)) {
                        //单独处理百分号
                        hrAccumulationFund = getHrAccumulationFund(hrAccumulationFundDTO);
                        hrAccumulationFundRepository.insert(hrAccumulationFund);
                    }
                } catch (Exception e) {
                    log.error("保存公积金类型异常:{}", e.getMessage());
                    if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("已存在")) {
                        hrAccumulationFundDTO.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    scale++;
                    hrAccumulationFundDTO.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }

            }

            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("公积金类型批量导入", HrAccumulationFundImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.ACCUMULATION_FUND_TYPE.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("公积金类型异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "公积金类型导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }


    }

    static {
        platformTypeHashMap = new HashMap<>();
        platformTypeHashMap.put("社保缴纳账户", "1");
        platformTypeHashMap.put("医保缴纳账户", "2");
        platformTypeHashMap.put("公积金缴纳账户", "3");
        platformTypeHashMap.put("工资发放账户", "4");
        platformTypeHashMap.put("其他", "0");
    }

    /**
     * 根据公积金名称查询
     *
     * @param typeName
     * @return
     */
    private List<HrAccumulationFund> getHrAccumulationFunds(String typeName) {
        //公积金名称不可以重复
        QueryWrapper<HrAccumulationFund> hrAccumulationFundQueryWrapper = new QueryWrapper<>();
        hrAccumulationFundQueryWrapper.eq("type_name", typeName);
        return hrAccumulationFundRepository.selectList(hrAccumulationFundQueryWrapper);
    }

    /**
     * 将百分号转化成小数或验证小数
     *
     * @param hrAccumulationFundDTO
     * @return
     */
    private HrAccumulationFund getHrAccumulationFund(HrAccumulationFundImport hrAccumulationFundDTO) {
        String personageScaleExcel = hrAccumulationFundDTO.getPersonageScaleExcel();

        BigDecimal personageScale = null;
        //如果包含% 并去掉 在转化成小数
        if (personageScaleExcel.contains("%")) {
            personageScaleExcel = personageScaleExcel.replace("%", "").replaceAll(" ", "");
            BigDecimal bigDecimal = null;
            try {
                bigDecimal = new BigDecimal(personageScaleExcel);
            } catch (Exception e) {
                throw new CommonException("个人比例数据格式不正确");
            }
            personageScale = extracted(1, bigDecimal);
        }
        //如果不包含%判断是否小于1
        else {
            try {
                personageScale = new BigDecimal(personageScaleExcel);
            } catch (Exception e) {
                throw new CommonException("个人比例数据格式不正确");
            }
            BigDecimal bigDecimal = new BigDecimal("1.00");
            if (personageScale.compareTo(bigDecimal) == 1) {
                throw new CommonException("个人比例数据格式不正确");
            }
        }

        BigDecimal unitScale = null;
        String unitScaleExcel = hrAccumulationFundDTO.getUnitScaleExcel();
        if (unitScaleExcel.contains("%")) {
            unitScaleExcel = unitScaleExcel.replace("%", "").replaceAll(" ", "");

            try {
                unitScale = new BigDecimal(unitScaleExcel);
            } catch (Exception e) {
                throw new CommonException("单位比例数据格式不正确");
            }
            unitScale = extracted(1, unitScale);
        } else {
            try {
                unitScale = new BigDecimal(unitScaleExcel);
            } catch (Exception e) {
                throw new CommonException("单位比例数据格式不正确");
            }
            BigDecimal bigDecimal = new BigDecimal("1.00");
            if (unitScale.compareTo(bigDecimal) == 1) {
                throw new CommonException("单位比例数据格式不正确");
            }
        }

        //HrAccumulationFund hrAccumulationFund = hrAccumulationFundMapper.toEntity(hrAccumulationFundDTO);
        HrAccumulationFund hrAccumulationFund = new HrAccumulationFund();
        BeanUtils.copyProperties(hrAccumulationFundDTO, hrAccumulationFund);
        hrAccumulationFund.setPersonageScale(personageScale);
        hrAccumulationFund.setUnitScale(unitScale);
        return hrAccumulationFund;
    }

    /**
     * 处理小数
     *
     * @param type                0 乘法 1除法
     * @param personageScaleExcel
     * @return
     */
    private BigDecimal extracted(int type, BigDecimal personageScaleExcel) {
        BigDecimal bignum = new BigDecimal("100");
        BigDecimal multiply = null;
        if (type == 0) {
            multiply = personageScaleExcel.multiply(bignum).setScale(2, BigDecimal.ROUND_DOWN);
        } else {
            multiply = personageScaleExcel.divide(bignum, 4, BigDecimal.ROUND_DOWN);
        }
        return multiply;
    }

    @Async
    public void socialSecurityImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        ExcelImportResult<HrSocialSecurityImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrSocialSecurityImport.class);
        ImportResultDTO importResult;
        try {
            int scale = 0;
            int listSize = result.getList().size();
            List<HrSocialSecurityImport> list = result.getList();
            Map<String, List<HrSocialSecurityImport>> map = list.stream().collect(Collectors.groupingBy(HrSocialSecurityImport::getSocialSecurityName));
            StringBuilder sbError = new StringBuilder();
            for (HrSocialSecurityImport hrSocialSecurityDTO : list) {
                sbError.delete(0, sbError.length());
                HrSocialSecurity hrSocialSecurity = new HrSocialSecurity();
                try {
                    //重复数据
                    List<HrSocialSecurityImport> mapList = map.get(hrSocialSecurityDTO.getSocialSecurityName());
                    if (CollectionUtils.isNotEmpty(mapList) && mapList.size() > 1) {
                        throw new CommonException("导入Excel中该数据社保类型名称重复!");
                    }
                    List<HrSocialSecurity> hrSocialSecurityList = getHrSocialSecurities(hrSocialSecurityDTO.getSocialSecurityName());
                    if (CollectionUtils.isNotEmpty(hrSocialSecurityList)) {
                        sbError.append("社保类型名称在数据库中已存在！");
                    }
                    Map<String, Integer> ownedBank = codeTableService.findCodeTableByInnerValue("ownedBank");
                    if (StringUtils.isNotBlank(hrSocialSecurityDTO.getAccountBank())) {
                        Integer ownedBankValue = ownedBank.get(hrSocialSecurityDTO.getAccountBank());
                        if (ownedBankValue == null) {
                            sbError.append("银行在系统中不存在!");
                        }
                    }
                    //验证方法
                    hrSocialSecurity = getVerify(hrSocialSecurityDTO);
                    if (StringUtils.isEmpty(sbError)) {
                        hrSocialSecurityRepository.insert(hrSocialSecurity);
                    }
                } catch (Exception e) {
                    log.error("社保类型异常:{}", e.getMessage());
                    if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("已存在")) {
                        hrSocialSecurityDTO.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    scale++;
                    hrSocialSecurityDTO.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }
            }

            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("社保类型批量导入", HrSocialSecurityImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.SOCIAL_SECURITY.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("社保类型异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "社保类型导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }

    }

    private List<HrSocialSecurity> getHrSocialSecurities(String socialSecurityName) {
        QueryWrapper<HrSocialSecurity> hrSocialSecurityQueryWrapper = new QueryWrapper<>();
        hrSocialSecurityQueryWrapper.eq("social_security_name", socialSecurityName);
        List<HrSocialSecurity> list = hrSocialSecurityRepository.selectList(hrSocialSecurityQueryWrapper);
        return list;
    }

    /**
     * 验证比例数据
     *
     * @param hrSocialSecurityDTO
     */
    private HrSocialSecurity getVerify(HrSocialSecurityImport hrSocialSecurityDTO) {
        HrSocialSecurity hrSocialSecurity = new HrSocialSecurity();
        BeanUtils.copyProperties(hrSocialSecurityDTO, hrSocialSecurity);
        //HrSocialSecurity hrSocialSecurity = hrSocialSecurityMapper.toEntity(hrSocialSecurityDTO);
        BigDecimal unitPension = null;
        if (!hrSocialSecurityDTO.getUnitPensionExcel().contains("%")) {
            unitPension = importVerify(hrSocialSecurityDTO.getUnitPensionExcel(), "单位养老比例数据格式不正确");
        } else {
            unitPension = importProcessing(hrSocialSecurityDTO.getUnitPensionExcel(), "单位养老比例数据格式不正确");
        }

        BigDecimal workInjury = null;
        if (!hrSocialSecurityDTO.getWorkInjuryExcel().contains("%")) {
            workInjury = importVerify(hrSocialSecurityDTO.getWorkInjuryExcel(), "单位工伤比例数据格式不正确");
        } else {
            workInjury = importProcessing(hrSocialSecurityDTO.getWorkInjuryExcel(), "单位工伤比例数据格式不正确");
        }


        BigDecimal personalMedical = null;
        if (!hrSocialSecurityDTO.getPersonalMedicalExcel().contains("%")) {
            personalMedical = importVerify(hrSocialSecurityDTO.getPersonalMedicalExcel(), "个人医疗比例数据格式不正确");
        } else {
            personalMedical = importProcessing(hrSocialSecurityDTO.getPersonalMedicalExcel(), "个人医疗比例数据格式不正确");
        }

        BigDecimal unitUnemployment = null;
        if (!hrSocialSecurityDTO.getUnitUnemploymentExcel().contains("%")) {
            unitUnemployment = importVerify(hrSocialSecurityDTO.getUnitUnemploymentExcel(), "单位失业比例数据格式不正确");

        } else {
            unitUnemployment = importProcessing(hrSocialSecurityDTO.getUnitUnemploymentExcel(), "单位失业比例数据格式不正确");
        }

        BigDecimal unitMedical = null;
        if (!hrSocialSecurityDTO.getUnitMedicalExcel().contains("%")) {
            unitMedical = importVerify(hrSocialSecurityDTO.getUnitMedicalExcel(), "单位医疗比例数据格式不正确");

        } else {
            unitMedical = importProcessing(hrSocialSecurityDTO.getUnitMedicalExcel(), "单位医疗比例数据格式不正确");
        }

        BigDecimal personalPension = null;
        if (!hrSocialSecurityDTO.getPersonalPensionExcel().contains("%")) {
            personalPension = importVerify(hrSocialSecurityDTO.getPersonalPensionExcel(), "个人养老比例数据格式不正确");

        } else {
            personalPension = importProcessing(hrSocialSecurityDTO.getPersonalPensionExcel(), "个人养老比例数据格式不正确");
        }

        BigDecimal personalUnemployment = null;
        if (!hrSocialSecurityDTO.getPersonalUnemploymentExcel().contains("%")) {
            personalUnemployment = importVerify(hrSocialSecurityDTO.getPersonalUnemploymentExcel(), "个人失业比例数据格式不正确");

        } else {
            personalUnemployment = importProcessing(hrSocialSecurityDTO.getPersonalUnemploymentExcel(), "个人失业比例数据格式不正确");
        }

        BigDecimal unitMaternity = null;
        if (!hrSocialSecurityDTO.getUnitMaternityExcel().contains("%")) {
            unitMaternity = importVerify(hrSocialSecurityDTO.getUnitMaternityExcel(), "单位生育比例数据格式不正确");
        } else {
            unitMaternity = importProcessing(hrSocialSecurityDTO.getUnitMaternityExcel(), "单位生育比例数据格式不正确");
        }

        BigDecimal personalMaternity = null;
        if (!hrSocialSecurityDTO.getPersonalMaternityExcel().contains("%")) {
            personalMaternity = importVerify(hrSocialSecurityDTO.getPersonalMaternityExcel(), "个人生育比例数据格式不正确");
        } else {
            personalMaternity = importProcessing(hrSocialSecurityDTO.getPersonalMaternityExcel(), "个人生育比例数据格式不正确");
        }

        hrSocialSecurity.setUnitPension(unitPension);
        hrSocialSecurity.setWorkInjury(workInjury);
        hrSocialSecurity.setPersonalMedical(personalMedical);
        hrSocialSecurity.setUnitUnemployment(unitUnemployment);
        hrSocialSecurity.setUnitMedical(unitMedical);
        hrSocialSecurity.setPersonalPension(personalPension);
        hrSocialSecurity.setPersonalUnemployment(personalUnemployment);
        hrSocialSecurity.setUnitMaternity(unitMaternity);
        hrSocialSecurity.setPersonalMaternity(personalMaternity);
        return hrSocialSecurity;
    }

    /**
     * 验证是否大一1
     *
     * @param unitPension
     * @param message
     */
    private BigDecimal importVerify(String unitPension, String message) {
        BigDecimal unitPensionBig = null;
        try {
            unitPensionBig = new BigDecimal(unitPension);
        } catch (Exception e) {
            throw new CommonException(message);
        }
        BigDecimal bigDecimal = new BigDecimal("1.00");
        if (unitPensionBig.compareTo(bigDecimal) == 1) {
            throw new CommonException(message);
        }
        return unitPensionBig;
    }

    /**
     * 处理导入百分号方法
     *
     * @param unitPensionExcel
     * @param message
     * @return
     */
    private BigDecimal importProcessing(String unitPensionExcel, String message) {
        unitPensionExcel = unitPensionExcel.replaceAll("%", "").replaceAll(" ", "");
        BigDecimal bigDecimal = null;
        try {
            bigDecimal = new BigDecimal(unitPensionExcel);
        } catch (Exception e) {
            throw new CommonException(message);
        }
        BigDecimal unitPension = extracted(1, bigDecimal);
        //importVerify(unitPension.toString(),message);
        return unitPension;
    }

    @Async
    public void platformAccountImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        ExcelImportResult<HrPlatformAccountDTO> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrPlatformAccountDTO.class);
        ImportResultDTO importResult;
        try {
            int scale = 0;
            int listSize = result.getList().size();
            List<HrPlatformAccountDTO> list = result.getList();
            //List<HrPlatformAccount> hrPlatformAccounts = hrPlatformAccountMapper.toEntity(list);
            Map<String, Integer> ownedBank = codeTableService.findCodeTableByInnerValue("ownedBank");
            Map<String, List<HrPlatformAccountDTO>> map = list.stream().collect(Collectors.groupingBy(o -> o.getAccountType() + "_" + o.getAccountNumber()));
            StringBuilder sbError = new StringBuilder();
            for (HrPlatformAccountDTO hrPlatformAccount : list) {
                try {
                    sbError.delete(0, sbError.length());
                    List<HrPlatformAccountDTO> mapList = map.get(hrPlatformAccount.getAccountType() + "_" + hrPlatformAccount.getAccountNumber());
                    if (CollectionUtils.isNotEmpty(mapList) && mapList.size() > 1) {
                        throw new CommonException("导入Excel中该数据账号类型和账户号重复!");
                    }
                    //账户号相同和账户类型不能重复
                    List<HrPlatformAccount> hrPlatformAccountList = getHrPlatformAccounts(hrPlatformAccount);
                    if (CollectionUtils.isNotEmpty(hrPlatformAccountList)) {
                        sbError.append("同种账户类型，账户号已存在！");
                    }
                    if (cn.casair.common.utils.StringUtils.isNotBlank(hrPlatformAccount.getIssuingBank())) {
                        Integer ownedBankValue = ownedBank.get(hrPlatformAccount.getIssuingBank());
                        if (ownedBankValue == null) {
                            sbError.append("银行在系统中不存在!");
                        }
                    }
                    String plateForm = platformTypeHashMap.get(hrPlatformAccount.getAccountType());
                    if (cn.casair.common.utils.StringUtils.isBlank(plateForm)) {
                        sbError.append("暂不支持该账户类型");
                    }
                    hrPlatformAccount.setPlatformType(plateForm);
                    HrPlatformAccount platformAccount = hrPlatformAccountMapper.toEntity(hrPlatformAccount);
                    if (StringUtils.isEmpty(sbError)) {
                        hrPlatformAccountRepository.insert(platformAccount);
                    }
                } catch (Exception e) {
                    log.error("保存平台账号异常:{}", e.getMessage());
                    if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("已存在")) {
                        hrPlatformAccount.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    scale++;
                    hrPlatformAccount.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }
            }

            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("平台账号批量导入", HrPlatformAccountDTO.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.PLATFORM_ACCOUNT.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("平台账号异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "平台账号导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    /**
     * 账户号相同和账户类型不能重复
     *
     * @param hrPlatformAccountDTO
     * @return
     */
    private List<HrPlatformAccount> getHrPlatformAccounts(HrPlatformAccountDTO hrPlatformAccountDTO) {
        QueryWrapper<HrPlatformAccount> hrPlatformAccountQueryWrapper = new QueryWrapper<>();
        hrPlatformAccountQueryWrapper.eq("account_type", hrPlatformAccountDTO.getAccountType());
        hrPlatformAccountQueryWrapper.eq("account_number", hrPlatformAccountDTO.getAccountNumber());
        List<HrPlatformAccount> list = hrPlatformAccountRepository.selectList(hrPlatformAccountQueryWrapper);
        return list;
    }
}
