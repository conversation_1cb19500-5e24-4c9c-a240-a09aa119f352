package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.Constants;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.PdfUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrApplyDepartureStaffExport;
import cn.casair.mapper.HrAppendixMapper;
import cn.casair.mapper.HrApplyDepartureStaffMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.ecloud.ECloudComponent;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.DocumentException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 待离职员工服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrApplyDepartureStaffServiceImpl extends ServiceImpl<HrApplyDepartureStaffRepository, HrApplyDepartureStaff> implements HrApplyDepartureStaffService {


    private final HrApplyDepartureStaffRepository hrApplyDepartureStaffRepository;
    private final HrApplyDepartureStaffMapper hrApplyDepartureStaffMapper;

    public HrApplyDepartureStaffServiceImpl(HrApplyDepartureStaffRepository hrApplyDepartureStaffRepository, HrApplyDepartureStaffMapper hrApplyDepartureStaffMapper) {
        this.hrApplyDepartureStaffRepository = hrApplyDepartureStaffRepository;
        this.hrApplyDepartureStaffMapper = hrApplyDepartureStaffMapper;
    }

    @Value("${file.temp-path}")
    private String tempPath;
    @Resource
    private HrApplyOpLogsService hrApplyOpLogsService;
    @Resource
    private HrTalentStaffRepository hrTalentStaffRepository;
    @Resource
    private HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;
    @Resource
    private HrApplyDepartureService hrApplyDepartureService;
    @Resource
    private HrApplyDepartureRepository hrApplyDepartureRepository;
    @Resource
    private HrSmsTemplateService hrSmsTemplateService;
    @Resource
    private UserService userService;
    @Resource
    private HrAppendixService hrAppendixService;
    @Resource
    private ECloudComponent eCloudComponent;
    @Resource
    private HrNotificationUserService hrNotificationUserService;
    @Resource
    private HrMaterialRepository hrMaterialRepository;
    @Resource
    private HrClientService hrClientService;
    @Resource
    private HrAppletMessageService hrAppletMessageService;
    @Resource
    private HrContractRepository hrContractRepository;
    @Resource
    private HrStaffSignCertService hrStaffSignCertService;
    @Resource
    private SysOperLogService sysOperLogService;
    @Resource
    private HrUpcomingService hrUpcomingService;
    @Resource
    private HrAppendixMapper hrAppendixMapper;
    @Resource
    private HrStaffSecondmentRepository hrStaffSecondmentRepository;


    /**
     * 创建待离职员工--添加判断
     *
     * @param hrApplyDepartureStaffDTO
     * @return
     */
    @Override
    public String createHrApplyDepartureStaff(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO) {
        log.info("Create new HrApplyDepartureStaff:{}", hrApplyDepartureStaffDTO);
        //判断选择的员工是否已经发起了离职申请
        List<HrApplyDepartureStaff> hrApplyDepartureStaffList = this.hrApplyDepartureStaffRepository.selectList(new QueryWrapper<HrApplyDepartureStaff>()
            .eq("client_id", hrApplyDepartureStaffDTO.getClientId()).ne("id",hrApplyDepartureStaffDTO.getId())
            .eq("staff_id", hrApplyDepartureStaffDTO.getStaffId()).notIn("departure_staff_status", 6,7,8));
        if (CollectionUtils.isNotEmpty(hrApplyDepartureStaffList)) {
            throw new CommonException("该员工已经存在没有离职完成的离职申请，不可重复提交离职申请。");
        }
        return hrApplyDepartureStaffDTO.getStaffId();
    }

    /**
     * 创建待离职员工--小程序
     *
     * @param hrApplyDepartureStaffDTO 离职信息
     * @return
     */
    @Override
    public HrApplyDepartureStaffDTO saveHrApplyDepartureStaff(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        //查询是否有正在申请的离职申请
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(jwtMiniDTO.getId());
        //由员工小程序添加离职申请
        hrApplyDepartureStaffDTO.setDepartureApplicant(1).setClientId(jwtMiniDTO.getClientId()).setStaffId(jwtMiniDTO.getId())
            .setWebsiteDepartureSchedule(DepartureServiceEnum.DepartureScheduleEnum.INITIATE_APPLICATION.getKey()).setLastModifiedDate(LocalDateTime.now());
        //查询该员工的在职信息
        HrStaffWorkExperience workExperience = hrStaffWorkExperienceRepository.selectStaffNewstWorkExperience(hrTalentStaff.getId(), hrTalentStaff.getClientId());
        if (workExperience != null) {
            hrApplyDepartureStaffDTO.setStationId(workExperience.getStationId());
        }
        HrApplyDepartureStaff hrApplyDepartureStaff = this.hrApplyDepartureStaffMapper.toEntity(hrApplyDepartureStaffDTO);
        this.hrApplyDepartureStaffRepository.insert(hrApplyDepartureStaff);
        this.createHrApplyDepartureStaff(hrApplyDepartureStaffDTO);
        HrApplyDepartureStaffDTO departureStaffDTO = this.hrApplyDepartureStaffMapper.toDto(hrApplyDepartureStaff);
        //修改员工信息
        hrTalentStaff.setDepartureStaffId(departureStaffDTO.getId());
        hrTalentStaffRepository.updateById(hrTalentStaff);
        //添加操作信息--小程序
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("departureStatus",DepartureServiceEnum.STAY_REVIEWED.getKey());
        jsonObject.put("message","离职申请已提交。您的离职日期是"+hrApplyDepartureStaffDTO.getDepartureDate());
        String message = jwtMiniDTO.getName()+"发起了离职申请。####"+jsonObject.toJSONString();
        this.hrApplyOpLogsService.saveHrApplyOpLogsMiniPhaseTWO(null, departureStaffDTO.getId(), jwtMiniDTO.getId(), message, true, ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),hrApplyDepartureStaff.getId(),hrApplyDepartureStaff.getStaffId(),String.valueOf(jsonObject.get("message")),false,null);
        this.hrNotificationUserService.saveRemindContent(departureStaffDTO.getClientId(),ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentDepartureEnum.INITIATE_APPLICATION.getKey(),jwtMiniDTO.getName()+"通过小程序发起了离职申请。等待客户审核",jwtMiniDTO.getId());
        hrUpcomingService.createServiceUpcoming(departureStaffDTO.getId(),hrTalentStaff.getId(), "离职服务-审核" + hrTalentStaff.getName() + "通过小程序发起的离职申请",LocalDate.now(),1);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.RESIGNATION_APPLICATION.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrApplyDepartureStaffDTO),
            HrApplyDepartureStaffDTO.class,
            null,
            JSON.toJSONString(hrApplyDepartureStaffDTO)
        );
        return departureStaffDTO;
    }

    /**
     * 修改待离职员工
     *
     * @param hrApplyDepartureStaffDTO
     * @return
     */
    @Override
    public Optional<HrApplyDepartureStaffDTO> updateHrApplyDepartureStaff(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO) {
        return Optional.ofNullable(this.hrApplyDepartureStaffRepository.selectById(hrApplyDepartureStaffDTO.getId()))
            .map(roleTemp -> {
                HrApplyDepartureStaff hrApplyDepartureStaff = this.hrApplyDepartureStaffMapper.toEntity(hrApplyDepartureStaffDTO);
                this.hrApplyDepartureStaffRepository.updateById(hrApplyDepartureStaff);
                log.info("Update HrApplyDepartureStaff:{}", hrApplyDepartureStaffDTO);
                return hrApplyDepartureStaffDTO;
            });
    }

    /**
     * 查询待离职员工详情
     *
     * @param id
     * @return
     */
    @Override
    public HrApplyDepartureStaffDTO getHrApplyDepartureStaff(String id) {
        log.info("Get HrApplyDepartureStaff :{}", id);

        HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO = this.hrApplyDepartureStaffRepository.findById(id);
        if (hrApplyDepartureStaffDTO == null){
            throw new CommonException("未查询对应数据！");
        }
        this.setDict(hrApplyDepartureStaffDTO);
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(null, id);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)){
            hrApplyDepartureStaffDTO.setOpLogsList(applyOpLogsList);
        }
        return hrApplyDepartureStaffDTO;
    }

    /**
     * 删除待离职员工
     *
     * @param id
     */
    @Override
    public void deleteHrApplyDepartureStaff(String id) {
        Optional.ofNullable(this.hrApplyDepartureStaffRepository.selectById(id))
            .ifPresent(hrApplyDepartureStaff -> {
                this.hrApplyDepartureStaffRepository.deleteById(id);
                log.info("Delete HrApplyDepartureStaff:{}", hrApplyDepartureStaff);
            });
    }

    /**
     * 批量删除待离职员工
     *
     * @param ids
     */
    @Override
    public void deleteHrApplyDepartureStaff(List<String> ids) {
        log.info("Delete HrApplyDepartureStaffs:{}", ids);
        this.hrApplyDepartureStaffRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询待离职员工
     *
     * @param hrApplyDepartureStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO, Long pageNumber, Long pageSize) {
        Page<HrApplyDepartureStaff> page = new Page<>(pageNumber, pageSize);
        if(CollectionUtils.isEmpty(hrApplyDepartureStaffDTO.getClientIds())){
            List<String> clientIds = hrClientService.selectClientIdByUserId();
            if (CollectionUtils.isEmpty(clientIds)){
                clientIds.add("");
            }
            hrApplyDepartureStaffDTO.setClientIds(clientIds);
        }
        IPage<HrApplyDepartureStaffDTO> iPage = this.hrApplyDepartureStaffRepository.findPage(page, hrApplyDepartureStaffDTO);
        iPage.getRecords().forEach(this::setDict);
        return iPage;
    }

    /**
     * 赋值字典值
     *
     * @param hrApplyDepartureStaffDTO
     */
    private void setDict(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO) {
        if (hrApplyDepartureStaffDTO.getDepartureStaffStatus() != null) {//状态
            hrApplyDepartureStaffDTO.setDepartureStaffStatusLabel(DepartureServiceEnum.getValueByKey(hrApplyDepartureStaffDTO.getDepartureStaffStatus()));
        }
        if (hrApplyDepartureStaffDTO.getDepartureApplyStatus() != null) {//审核结果
            hrApplyDepartureStaffDTO.setDepartureApplyStatusLabel(StaffApplyStatusEnum.AuditResultEnum.getValueByKey(hrApplyDepartureStaffDTO.getDepartureApplyStatus()));
        }
        if (hrApplyDepartureStaffDTO.getPersonnelType() != null) {//人员类型
            hrApplyDepartureStaffDTO.setPersonnelTypeLabel(StaffEnum.PersonnelTypeEnum.getValueByKey(hrApplyDepartureStaffDTO.getPersonnelType()));
        }
        if (hrApplyDepartureStaffDTO.getStopPayYear() != null && hrApplyDepartureStaffDTO.getStopPayMonthly() != null) {//缴费年月
            hrApplyDepartureStaffDTO.setStopPaymentDate(hrApplyDepartureStaffDTO.getStopPayYear() + "-" + hrApplyDepartureStaffDTO.getStopPayMonthly());
        }
    }

    /**
     * 客户审核--待离职员工列表
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity auditInForStaff(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> successList = new ArrayList<>();//成功集合
        List<String> errorStatusList = new ArrayList<>();//失败集合

        //查询所有待离职员工
        for (String departureStaffId : batchOptDTO.getApplyStaffIdList()) {
            HrApplyDepartureStaffDTO hrApplyDepartureStaff = this.getHrApplyDepartureStaff(departureStaffId);
            String message = "";
            if (!hrApplyDepartureStaff.getDepartureStaffStatus().equals(DepartureServiceEnum.STAY_REVIEWED.getKey())) {
                errorStatusList.add(hrApplyDepartureStaff.getName());
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            if (batchOptDTO.getOpt()) {//审核通过，待建离职
                hrApplyDepartureStaff.setDepartureStaffStatus(DepartureServiceEnum.STAY_RESIGNATION.getKey())
                    .setWebsiteDepartureSchedule(DepartureServiceEnum.DepartureScheduleEnum.CONFIRM_RESIGNATION.getKey());
                jsonObject.put("departureStatus",DepartureServiceEnum.STAY_MANAGER_REVIEW.getKey());
                jsonObject.put("message","离职申请用过单位审核通过");
                message = jwtUserDTO.getRealName()+"通过了"+hrApplyDepartureStaff.getName()+"的离职申请。####"+jsonObject.toJSONString();
                //修改员工状态为待确认离职
                hrTalentStaffRepository.updateStaffStatus(hrApplyDepartureStaff.getStaffId(), StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION.getKey());
            } else {//客户审核拒绝
                hrApplyDepartureStaff.setDepartureStaffStatus(DepartureServiceEnum.CUSTOMER_REJECTED.getKey()).setDepartureApplyStatus(StaffApplyStatusEnum.AuditResultEnum.AUDIT_REFUSE.getKey())
                    .setWebsiteDepartureSchedule(DepartureServiceEnum.DepartureScheduleEnum.END_RESIGNATION.getKey());
                jsonObject.put("departureStatus",DepartureServiceEnum.CUSTOMER_REJECTED.getKey());
                jsonObject.put("message","您的离职申请用过单位审核未通过，请重新发起申请。");
                jsonObject.put("checkerReason",batchOptDTO.getCheckerReason());
                message = jwtUserDTO.getRealName()+"拒绝了"+hrApplyDepartureStaff.getName()+"的离职申请。####"+jsonObject.toJSONString();
                //查找员工是否借调信息，如果存在在判断是否正在借调
                Integer staffStatus = StaffEnum.StaffStatusEnum.ON_JOB.getKey();
                HrStaffSecondment hrStaffSecondment = hrStaffSecondmentRepository.selectOne(new QueryWrapper<HrStaffSecondment>().eq("is_delete", 0)
                    .eq("staff_id", hrApplyDepartureStaff.getStaffId()).lt("step", ServiceCenterEnum.SecondmentStepEnum.SECONDMENT_END.getKey())
                    .orderByDesc("created_date").last("LIMIT 1"));
                if (hrStaffSecondment != null){
                    staffStatus = StaffEnum.StaffStatusEnum.BORROWING.getKey();
                }
                //修改员工状态为在职
                hrTalentStaffRepository.updateStaffStatus(hrApplyDepartureStaff.getStaffId(), staffStatus);
            }
            hrApplyDepartureStaffRepository.updateById(hrApplyDepartureStaffMapper.toEntity(hrApplyDepartureStaff));
            //创建操作日志
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrApplyDepartureStaff.getApplyDepartureId(),hrApplyDepartureStaff.getId(),jwtUserDTO.getId(),message,batchOptDTO.getRemark(),false,null, ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
            this.hrNotificationUserService.saveRemindContent(hrApplyDepartureStaff.getClientId(),ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentDepartureEnum.CUSTOMER_REVIEW.getKey(),jwtUserDTO.getRealName()+"审核了"+hrApplyDepartureStaff.getName()+"通过小程序申请的离职申请。等待发起离职",jwtUserDTO.getId());
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),hrApplyDepartureStaff.getId(),hrApplyDepartureStaff.getStaffId(),String.valueOf(jsonObject.get("message")),false,null);
            hrUpcomingService.createServiceUpcoming(hrApplyDepartureStaff.getId(),hrApplyDepartureStaff.getStaffId(), "离职服务-对" + hrApplyDepartureStaff.getName() + "发起离职",LocalDate.now(),1);
            successList.add(hrApplyDepartureStaff.getName());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 查询待离职员工信息
     *
     * @param departureId          离职服务ID
     * @param departureApplyStatus 待离职员工审核结果
     * @param departureStaffIds    待离职员工ID
     * @return 待离职员工信息
     */
    @Override
    public List<HrApplyDepartureStaffDTO> viewResignationInFor(String departureId, Integer departureApplyStatus, List<String> departureStaffIds) {
        List<HrApplyDepartureStaffDTO> hrApplyDepartureStaffDTOS = this.hrApplyDepartureStaffRepository.viewResignationInFor(departureId, departureApplyStatus, departureStaffIds);
        hrApplyDepartureStaffDTOS.forEach(this::setDict);
        return hrApplyDepartureStaffDTOS;
    }

    /**
     * 经理审核--离职服务查询审核
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity managerReviewStaff(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        //操作待离职员工信息
        for (String departureStaffId : batchOptDTO.getApplyStaffIdList()) {
            HrApplyDepartureStaffDTO departureStaffDTO = this.hrApplyDepartureStaffRepository.findById(departureStaffId);
            if (departureStaffDTO.getDepartureApplyStatus().equals(StaffApplyStatusEnum.AuditResultEnum.STAY_AUDIT.getKey())) {
                this.hrApplyDepartureService.updateDepartureStaffStatus(jwtUserDTO,departureStaffDTO,batchOptDTO);
            }
        }
        //操作离职服务
        HrApplyDeparture hrApplyDeparture = hrApplyDepartureRepository.selectById(batchOptDTO.getApplyId());
        List<HrApplyDepartureStaffDTO> hrApplyDepartureStaffDTOS = hrApplyDepartureStaffRepository.viewResignationInFor(batchOptDTO.getApplyId(), null, null);
        if (CollectionUtils.isNotEmpty(hrApplyDepartureStaffDTOS)) {
            //根据员工的审核结果计算申请的审批状态
            Boolean flag = this.calculateApplyStatus(hrApplyDeparture, hrApplyDepartureStaffDTOS);
            hrApplyDepartureRepository.updateById(hrApplyDeparture);
            //添加操作日志
            String appendixId = null;
            if (batchOptDTO.getAppendixIdList() != null){
                appendixId= String.join(",",batchOptDTO.getAppendixIdList());
            }
            //先删除之前的操作信息重新生成一条
            hrApplyOpLogsService.remove(new QueryWrapper<HrApplyOpLogs>().eq("apply_id",hrApplyDeparture.getId()).isNull("apply_staff_id"));
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyDeparture.getId(), null, jwtUserDTO.getId(), batchOptDTO.getApplyRemark(), appendixId, ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
            if (flag) {
                HrClient hrClient = hrClientService.getById(hrApplyDeparture.getClientId());
                this.hrNotificationUserService.saveRemindContent(hrApplyDeparture.getClientId(),ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentDepartureEnum.MANAGER_REVIEW.getKey(),jwtUserDTO.getRealName()+"审核了"+hrClient.getClientName()+"离职服务。等待专管员通知办理离职",jwtUserDTO.getId());
                hrUpcomingService.updateUpcoming(hrApplyDeparture.getId());
            }
        }
        return ResponseUtil.buildSuccess();
    }

    /**
     * 赋值申请审核状态
     *
     * @param hrApplyDeparture          申请信息
     * @param hrApplyDepartureStaffDTOS 申请审核员工信息
     */
    public Boolean calculateApplyStatus(HrApplyDeparture hrApplyDeparture, List<HrApplyDepartureStaffDTO> hrApplyDepartureStaffDTOS) {
        //员工中待审核的信息
        List<HrApplyDepartureStaffDTO> stayAuditStaff = hrApplyDepartureStaffDTOS.stream().filter(hrApplyDepartureStaffDTO -> hrApplyDepartureStaffDTO.getDepartureApplyStatus() == StaffApplyStatusEnum.AuditResultEnum.STAY_AUDIT.getKey()).collect(Collectors.toList());
        //员工中通过的信息
        List<HrApplyDepartureStaffDTO> auditPassStaff = hrApplyDepartureStaffDTOS.stream().filter(hrApplyDepartureStaffDTO -> hrApplyDepartureStaffDTO.getDepartureApplyStatus() == StaffApplyStatusEnum.AuditResultEnum.AUDIT_PASS.getKey()).collect(Collectors.toList());
        //员工中拒绝的信息
        List<HrApplyDepartureStaffDTO> auditRefuseStaff = hrApplyDepartureStaffDTOS.stream().filter(hrApplyDepartureStaffDTO -> hrApplyDepartureStaffDTO.getDepartureApplyStatus() == StaffApplyStatusEnum.AuditResultEnum.AUDIT_REFUSE.getKey()).collect(Collectors.toList());

        if (stayAuditStaff.size() != hrApplyDeparture.getStaffNum()) {//已经存在审核的员工
            if ((auditPassStaff.size() + auditRefuseStaff.size()) == hrApplyDeparture.getStaffNum()) {//已审核通过+已审核拒绝的员工数据等于申请员工总数 ，该申请已审核完成
                if (auditPassStaff.size() == 0) {//全部审核完成没有通过的员工，申请状态为已拒绝
                    hrApplyDeparture.setApplyStatus(StaffEnum.ApplyStatusEnum.REJECTED.getKey());
                }
                if (auditRefuseStaff.size() == 0) {//全部审核完成没有拒绝的员工，申请状态为已通过
                    hrApplyDeparture.setApplyStatus(StaffEnum.ApplyStatusEnum.ALREADY_PASSED.getKey());
                }
                if (auditPassStaff.size() > 0 && auditRefuseStaff.size() > 0) {//全部审核完成 有通过的以及拒绝的，申请状态为部分通过
                    hrApplyDeparture.setApplyStatus(StaffEnum.ApplyStatusEnum.PORTION_PASSED.getKey());
                }
                return true;
            } else {//已经审核了一部分还有未审核的，申请状态为部分已审
                hrApplyDeparture.setApplyStatus(StaffEnum.ApplyStatusEnum.PORTION__REVIEW.getKey());
                return false;
            }
        }
        return false;
    }

    /**
     * 离职通知
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity resignationNoticeStaff(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        //查询用户信息
        UserDTO user = userService.getUser(jwtUserDTO.getId());
        List<String> errorStatusList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        List<String> successList = new ArrayList<>();
        //查找对应的离职材料
        HrMaterial hrMaterial = hrMaterialRepository.selectOne(new QueryWrapper<HrMaterial>().eq("type", 1).eq("name", "辞职申请书"));
        if (hrMaterial == null){
            throw new CommonException("系统中没有对应的离职材料，请先到材料管理配置相关的材料信息！");
        }else {
            if (StringUtils.isBlank(hrMaterial.getFileId())){
                throw new CommonException("系统中没有对应的离职材料，请先到材料管理配置相关的材料信息！");
            }
        }
        List<HrApplyDepartureStaffDTO> hrApplyDepartureStaffDTOS = this.hrApplyDepartureStaffRepository.viewResignationInFor(null, null, batchOptDTO.getApplyStaffIdList());
        for (HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO : hrApplyDepartureStaffDTOS) {
            if (!hrApplyDepartureStaffDTO.getDepartureStaffStatus().equals( DepartureServiceEnum.TO_NOTIFIED_HANDLE_RESIGNATION.getKey())) {
                errorStatusList.add(hrApplyDepartureStaffDTO.getName());
                continue;
            }
            // 用户点击离职通知后，弹窗提示“通知发送成功”，离职通知仅可发送一次
            // 离职通知会给员工发送短信和小程序通知，同时员工可以在小程序查看离职申请进度 todo 发送离职短信模板
            HashMap<Integer, String> params = new HashMap<>();
            params.put(1, hrApplyDepartureStaffDTO.getName());
            params.put(2, hrApplyDepartureStaffDTO.getClientName());
            /*params.put(2, LocalDate.now().plusDays(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            params.put(3, CompanyInfoEnum.FIRST_PART_PHONE.getValue()+","+ CompanyInfoEnum.FIRST_PART_PHONE_TWO.getValue());*/
            String sendMessage = this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.RESIGNATION_NOTICE_USE.getTemplateCode(), hrApplyDepartureStaffDTO.getPhone());
            if (sendMessage.equalsIgnoreCase(Constants.SEND_EXCEPTION)) {
                errorList.add(hrApplyDepartureStaffDTO.getName());
                String message = jwtUserDTO.getRealName() + "向 " + hrApplyDepartureStaffDTO.getName() + " 发送了离职短信通知。发送失败，失败原因：" + Constants.SEND_EXCEPTION;
                this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyDepartureStaffDTO.getApplyDepartureId(), hrApplyDepartureStaffDTO.getId(), jwtUserDTO.getId(), message, null, 0);
                continue;
            }

            //发送离职通知 状态更新为已通知,待办理离职 进度更新为员工办理离职手续
            hrApplyDepartureStaffDTO.setDepartureStaffStatus(DepartureServiceEnum.NOTIFIED_HANDLE_RESIGNATION.getKey())
                .setWebsiteDepartureSchedule(DepartureServiceEnum.DepartureScheduleEnum.HANDLE_PROCEDURES.getKey()).setMaterialId(hrMaterial.getId());
            hrApplyDepartureStaffRepository.updateById(hrApplyDepartureStaffMapper.toEntity(hrApplyDepartureStaffDTO));
            //添加操作日志
            String address = "我们办公地点";
            String workPhone = "";
            if (StringUtils.isNotBlank(user.getWorkAddress()) && StringUtils.isNotBlank(user.getWorkPhone())){
                address = user.getWorkAddress() + user.getWorkPhone();
                workPhone = "联系电话："+user.getWorkPhone();
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("departureStatus",DepartureServiceEnum.NOTIFIED_HANDLE_RESIGNATION.getKey());
            jsonObject.put("message","请进行辞职申请书的电子签或到"+address+"现场签订辞职申请书。"+workPhone);
            String message = jwtUserDTO.getRealName() + "通知了"+hrApplyDepartureStaffDTO.getName()+"签订辞职申请书。####"+jsonObject.toJSONString();
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyDepartureStaffDTO.getApplyDepartureId(), hrApplyDepartureStaffDTO.getId(), jwtUserDTO.getId(), message, null, ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
            this.hrNotificationUserService.saveRemindContent(hrApplyDepartureStaffDTO.getClientId(),ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentDepartureEnum.HANDLE_RESIGNATION.getKey(),jwtUserDTO.getRealName() + "向"+hrApplyDepartureStaffDTO.getName()+"发送了离职通知。等待"+hrApplyDepartureStaffDTO.getName()+"在小程序电子签或者到公司线下签",jwtUserDTO.getId());
            //微信通知+消息中心
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),hrApplyDepartureStaffDTO.getId(),hrApplyDepartureStaffDTO.getStaffId(),String.valueOf(jsonObject.get("message")),true,ServiceCenterEnum.DEPARTURE_APPLICATIONS.getValue());
            hrUpcomingService.createServiceUpcoming(hrApplyDepartureStaffDTO.getId(),hrApplyDepartureStaffDTO.getStaffId(), "离职服务-确认员工-"+hrApplyDepartureStaffDTO.getName()+"离职",LocalDate.now(),0);

            successList.add(hrApplyDepartureStaffDTO.getName());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "不是待通知员工办理离职状态");
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            jsonObject.put("error_message", "选择的数据中" + String.join(",", errorList) + "发送通知短信失败，失败原因：" + Constants.SEND_EXCEPTION);
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }


    /**
     * 首頁：统计当前年每月离职数量
     *
     * @param particularYear 年份
     * @return
     */
    @Override
    public List<Map<String, Object>> resignedCountByYear(String particularYear) {
        List<Map<String, Object>> mapList = this.hrApplyDepartureStaffRepository.resignedCountByYear(particularYear);
        log.info("离职数量 {}", mapList);
        return mapList;
    }

    /**
     * 导出待离职员工列表
     *
     * @param hrApplyDepartureStaffDTO 待离职员工ID
     * @param response
     * @return
     */
    @Override
    public String exportDepartureStaff(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO, HttpServletResponse response) {
        if(CollectionUtils.isEmpty(hrApplyDepartureStaffDTO.getClientIds())){
            List<String> clientIds = hrClientService.selectClientIdByUserId();
            if (CollectionUtils.isEmpty(clientIds)){
                clientIds.add("");
            }
            hrApplyDepartureStaffDTO.setClientIds(clientIds);
        }
        List<HrApplyDepartureStaffExport> hrApplyDepartureStaffTemplates = hrApplyDepartureStaffRepository.findList(hrApplyDepartureStaffDTO);
        if (hrApplyDepartureStaffTemplates.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        int listSize = hrApplyDepartureStaffTemplates.size();
        List<String> ids = hrApplyDepartureStaffTemplates.stream().map(HrApplyDepartureStaffExport::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(hrApplyDepartureStaffTemplates, "待离职员工信息", HrApplyDepartureStaffExport.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.RESIGNATION_APPLICATION.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 获取辞职申请书模板
     * @return
     */
    @Override
    public HrAppendixDTO resignationApplyTemplate() {
        HrApplyDepartureStaffDTO resignationProgressStaff = this.getResignationProgressStaff();
        //查询对应的离职材料
        HrMaterial hrMaterial = hrMaterialRepository.selectById(resignationProgressStaff.getMaterialId());
        return hrAppendixService.getHrAppendix(hrMaterial.getFileId());
    }

    /**
     * 上传辞职申请书
     * @param partFile
     * @param response
     */
    @Override
    public HrAppendixDTO pictureTransferPDF(MultipartFile partFile, HttpServletResponse response) throws IOException, DocumentException {
        //通过附件查询详情
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(jwtMiniDTO.getId());
        String file = PdfUtils.generatePdfFile(partFile);//图片转PDF

       /* DiskFileItem fileItem = new DiskFileItem("mainFile", Files.probeContentType(file.toPath()), false, file.getName(), (int) file.length(), file.getParentFile());
        InputStream input = new FileInputStream(file);
        OutputStream os = fileItem.getOutputStream();
        IOUtils.copy(input, os);
        MultipartFile multipartFile = new CommonsMultipartFile(fileItem);

        String fileName =  "辞职申请书.pdf";
        String filePath = tempPath + File.separator + fileName;
        try (FileOutputStream out = new FileOutputStream(filePath)) {
            out.write(multipartFile.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("文件上传失败!");
        }*/

        // 检查并生成用户个人证书
        this.hrStaffSignCertService.checkAndGetStaffSignCert(hrTalentStaff.getId(), hrTalentStaff.getClientId(), hrTalentStaff.getName(), hrTalentStaff.getCertificateNum(), hrTalentStaff.getPhone());
        HrAppendixDTO hrAppendixDTO = hrAppendixMapper.toDto(hrAppendixService.uploadImportFile(file));
        String contractNum = this.eCloudComponent.uploadResignationUrl("辞职申请书", hrAppendixDTO.getFileUrl(),jwtMiniDTO.getPhone());
        //根据员工信息查找离职信息
        HrApplyDepartureStaff hrApplyDepartureStaff = hrApplyDepartureStaffRepository.selectById(hrTalentStaff.getDepartureStaffId());
        if (hrApplyDepartureStaff != null){
            hrApplyDepartureStaff.setContractNum(contractNum);
            hrApplyDepartureStaffRepository.updateById(hrApplyDepartureStaff);
        }

        return hrAppendixDTO;
    }

    /**
     * 员工小程序查看进度
     * @return
     */
    @Override
    public HrApplyDepartureStaffDTO getResignationProgressStaff() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrApplyDepartureStaff applyDepartureStaff = hrApplyDepartureStaffRepository.selectOne(new QueryWrapper<HrApplyDepartureStaff>().eq("staff_id", jwtMiniDTO.getId()).orderByDesc("created_date").last("LIMIT 1"));
        if (applyDepartureStaff == null){
            throw new CommonException("未查询离职信息");
        }
        HrApplyDepartureStaffDTO hrApplyDepartureStaff = this.getHrApplyDepartureStaff(applyDepartureStaff.getId());
        List<HrApplyOpLogsDTO> opLogsList = hrApplyDepartureStaff.getOpLogsList();
        if (opLogsList != null && !opLogsList.isEmpty()){
            List<HrAppendix> hrAppendixArrayList = new ArrayList<>();
            opLogsList.forEach(logs ->{
                List<HrAppendix> hrAppendixList = logs.getHrAppendixList();
                if (hrAppendixList != null && !hrAppendixList.isEmpty()){
                    hrAppendixArrayList.addAll(hrAppendixList);
                }
            });
            hrApplyDepartureStaff.setHrAppendixList(hrAppendixArrayList);
        }
        return hrApplyDepartureStaff;
    }

    /**
     * 审核线上电签信息
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity auditOnlineSignInFor(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        //根据applyStaffIdList查询待离职员工信息
        List<String> errorStatusList = new ArrayList<>();//员工状态不匹配集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String applyStaffId : batchOptDTO.getApplyStaffIdList()) {
            String message = "";
            String msg = "";
            HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO = hrApplyDepartureStaffRepository.findById(applyStaffId);
            if (!hrApplyDepartureStaffDTO.getDepartureStaffStatus().equals(DepartureServiceEnum.NOTIFIED_HANDLE_RESIGNATION.getKey())){
                errorStatusList.add(hrApplyDepartureStaffDTO.getName());
                continue;
            }

            JSONObject jsonObject = new JSONObject();
            if (batchOptDTO.getOpt()){//通过
                hrApplyDepartureStaffDTO.setDepartureStaffStatus(DepartureServiceEnum.COMPLETE_RESIGNATION.getKey());
                hrApplyDepartureStaffDTO.setWebsiteDepartureSchedule(DepartureServiceEnum.DepartureScheduleEnum.COMPLETION_RESIGNATION.getKey());
                hrApplyDepartureStaffDTO.setIzRefundSocialSecurity(batchOptDTO.getIzRefundSocialSecurity());//是否本月退缴
                hrApplyDepartureStaffDTO.setIzOnlineContract(true);
                this.updateStaff(hrApplyDepartureStaffDTO.getStaffId(),hrApplyDepartureStaffDTO,batchOptDTO.getIzRefundSocialSecurity(), jwtUserDTO);
                jsonObject.put("departureStatus",DepartureServiceEnum.COMPLETE_RESIGNATION.getKey());
                jsonObject.put("message","您的离职手续已办理完成，如您还有未完成业务请尽快办理，45天后您将无法登录小程序");
                message = jwtUserDTO.getRealName() + "审核通过了"+hrApplyDepartureStaffDTO.getName()+"的电子签信息。####"+jsonObject.toJSONString();
                msg = jwtUserDTO.getRealName()+"审核通过了"+hrApplyDepartureStaffDTO.getName()+"的电子签信息。"+hrApplyDepartureStaffDTO.getName()+"离职完成";
                hrUpcomingService.updateUpcoming(hrApplyDepartureStaffDTO.getId());
            }else {//拒绝
                hrApplyDepartureStaffDTO.setDepartureStaffStatus(DepartureServiceEnum.AUDIT_FAILED.getKey());
                hrApplyDepartureStaffDTO.setWebsiteDepartureSchedule(DepartureServiceEnum.DepartureScheduleEnum.HANDLE_PROCEDURES.getKey());
                jsonObject.put("departureStatus",DepartureServiceEnum.AUDIT_FAILED.getKey());
                jsonObject.put("message","您的电子签审核未通过，请继续签订");
                message = jwtUserDTO.getRealName()+"审核拒绝了"+hrApplyDepartureStaffDTO.getName()+"的电子签信息。拒绝原因："+batchOptDTO.getCheckerReason()+"。####"+jsonObject.toJSONString();
                msg = jwtUserDTO.getRealName()+"审核拒绝了"+hrApplyDepartureStaffDTO.getName()+"的电子签信息。等待"+hrApplyDepartureStaffDTO.getName()+"重新签订辞职申请书";
            }
            hrApplyDepartureStaffRepository.updateById(hrApplyDepartureStaffMapper.toEntity(hrApplyDepartureStaffDTO));
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrApplyDepartureStaffDTO.getApplyDepartureId(),hrApplyDepartureStaffDTO.getId(),jwtUserDTO.getId(),message,batchOptDTO.getRemark(),false,null, ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
            this.hrNotificationUserService.saveRemindContent(hrApplyDepartureStaffDTO.getClientId(),ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentDepartureEnum.CONTRACT_CONFIRMATION.getKey(),msg,jwtUserDTO.getId());
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),hrApplyDepartureStaffDTO.getId(),hrApplyDepartureStaffDTO.getStaffId(),String.valueOf(jsonObject.get("message")),true,ServiceCenterEnum.DEPARTURE_APPLICATIONS.getValue());
            successList.add(hrApplyDepartureStaffDTO.getName());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success",successList);
        if (CollectionUtils.isNotEmpty(errorStatusList)){
            jsonObject.put("error_status","选择的数据中"+String.join(",",errorStatusList)+"的状态值不对");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 审核线下电签信息
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity auditOfflineSignInFor(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        //根据applyStaffIdList查询待离职员工信息
        List<String> errorStatusList = new ArrayList<>();//员工状态不匹配集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String applyStaffId : batchOptDTO.getApplyStaffIdList()) {
            String message = "";
            HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO = hrApplyDepartureStaffRepository.findById(applyStaffId);
//            if (!hrApplyDepartureStaffDTO.getDepartureStaffStatus().equals(DepartureServiceEnum.NOTIFIED_HANDLE_RESIGNATION.getKey())){
//                errorStatusList.add(hrApplyDepartureStaffDTO.getName());
//                continue;
//            }
            hrApplyDepartureStaffDTO.setIzRefundSocialSecurity(batchOptDTO.getIzRefundSocialSecurity())//是否本月退缴
                .setIzOnlineContract(false)
                .setDepartureStaffStatus(DepartureServiceEnum.COMPLETE_RESIGNATION.getKey())
                .setWebsiteDepartureSchedule(DepartureServiceEnum.DepartureScheduleEnum.COMPLETION_RESIGNATION.getKey());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("departureStatus",DepartureServiceEnum.COMPLETE_RESIGNATION.getKey());
            jsonObject.put("message","您的离职手续已办理完成，如您还有未完成业务请尽快办理，45天后您将无法登录小程序");
            message = jwtUserDTO.getRealName()+"审核通过了"+hrApplyDepartureStaffDTO.getName()+"的线下签信息。备注："+batchOptDTO.getApplyRemark()+"####"+jsonObject.toJSONString();
            hrApplyDepartureStaffRepository.updateById(hrApplyDepartureStaffMapper.toEntity(hrApplyDepartureStaffDTO));
            this.updateStaff(hrApplyDepartureStaffDTO.getStaffId(),hrApplyDepartureStaffDTO,batchOptDTO.getIzRefundSocialSecurity(),jwtUserDTO);
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrApplyDepartureStaffDTO.getApplyDepartureId(),hrApplyDepartureStaffDTO.getId(),jwtUserDTO.getId(),message,batchOptDTO.getRemark(),false,String.join(",",batchOptDTO.getAppendixIdList()), ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
            this.hrNotificationUserService.saveRemindContent(hrApplyDepartureStaffDTO.getClientId(),ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentDepartureEnum.CONTRACT_CONFIRMATION.getKey(),jwtUserDTO.getRealName()+"审核通过了"+hrApplyDepartureStaffDTO.getName()+"的线下签信息。"+hrApplyDepartureStaffDTO.getName()+"已离职完成",jwtUserDTO.getId());
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),hrApplyDepartureStaffDTO.getId(),hrApplyDepartureStaffDTO.getStaffId(),String.valueOf(jsonObject.get("message")),true,ServiceCenterEnum.DEPARTURE_APPLICATIONS.getValue());
            hrUpcomingService.updateUpcoming(hrApplyDepartureStaffDTO.getId());
            successList.add(hrApplyDepartureStaffDTO.getName());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success",successList);
        if (CollectionUtils.isNotEmpty(errorStatusList)){
            jsonObject.put("error_status","选择的数据中"+String.join(",",errorStatusList)+"的状态值不对");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    private void updateStaff(String staffId, HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO,Integer izRefundSocialSecurity,JWTUserDTO jwtUserDTO) {
        //更新员工社保状态 todo
        //审核通过离职完成
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(staffId);
        if (izRefundSocialSecurity.equals(1)) {
            //本月停保
            hrTalentStaff.setIzInsured(StaffEnum.InsuredStatusEnum.STOP_INSURED.getKey());
        }
        hrTalentStaff.setStaffStatus(StaffEnum.StaffStatusEnum.SEPARATION.getKey()).setResignationDate(hrApplyDepartureStaffDTO.getDepartureDate());
        hrTalentStaffRepository.updateById(hrTalentStaff);

        //查询在职信息更新为工作经历
        HrStaffWorkExperience workExperience = hrStaffWorkExperienceRepository.selectStaffNewstWorkExperience(hrTalentStaff.getId(), hrTalentStaff.getClientId());
        if (workExperience != null) {
            workExperience.setIzDefault(false).setDepartureDate(hrApplyDepartureStaffDTO.getDepartureDate())
                .setStaffStatus(hrTalentStaff.getStaffStatus()).setIzInsured(hrTalentStaff.getIzInsured());
            hrStaffWorkExperienceRepository.updateById(workExperience);
        }
        HrContract hrContract = hrContractRepository.selectNewestRecord(hrTalentStaff.getId(), hrTalentStaff.getClientId());
        if (hrContract!=null){
            hrContract.setState(ContractEnum.ContractState.DISMISSED.getKey());
            hrContractRepository.updateById(hrContract);
        }
        //结束所有正在进行的服务
        //借调服务
        List<HrStaffSecondment> hrStaffSecondments = hrStaffSecondmentRepository.selectList(new QueryWrapper<HrStaffSecondment>().eq("staff_id", staffId)
            .lt("states",ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_END.getKey())
            .lt("step", ServiceCenterEnum.SecondmentStepEnum.SECONDMENT_END.getKey()));
        hrStaffSecondments.forEach(hrStaffSecondment -> {
            hrStaffSecondment.setStates(ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_END.getKey())
                .setStep(ServiceCenterEnum.SecondmentStepEnum.SECONDMENT_END.getKey())
                .setIsDefault(1);
            hrStaffSecondmentRepository.updateById(hrStaffSecondment);
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrStaffSecondment.getId(), null, jwtUserDTO.getId(), "离职完成结束服务。",null, false, null, ServiceCenterEnum.STAFF_SECONDMENT.getKey());
        });
    }

    /**
     * 检查辞职申请书签订状态
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> checkDepartureItemState(Map<String, Object> params) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrTalentStaff staff = hrTalentStaffRepository.selectById(jwtMiniDTO.getId());
        HrApplyDepartureStaffDTO hrApplyDepartureStaff = this.getHrApplyDepartureStaff(staff.getDepartureStaffId());
        JSONObject jsonObject = this.eCloudComponent.getContractDetail(jwtMiniDTO.getPhone(), hrApplyDepartureStaff.getContractNum());
        Map<String, Object> result = JSON.parseObject(JSON.toJSONString(jsonObject.get("contractInfo")), Map.class);
        Integer status = Integer.valueOf(String.valueOf(result.get("status")));
        params.put("status", status);
        // 获取易云章合同文件
        HrAppendix hrAppendix = this.eCloudComponent.downloadContract(hrApplyDepartureStaff.getContractNum(), jwtMiniDTO.getPhone());
        // 合同存证下载 易云章合同存根查看地址特殊处理
        // JSONObject evidenceData = this.eCloudComponent.getEvidenceData(jwtMiniDTO.getPhone(), hrApplyDepartureStaff.getContractNum());
        //保存下载的签字辞职申请书
        hrApplyDepartureStaff.setIzOnlineContract(true).setDepartureStaffStatus(DepartureServiceEnum.NOTIFIED_HANDLE_RESIGNATION.getKey());
        hrApplyDepartureStaffRepository.updateById(hrApplyDepartureStaffMapper.toEntity(hrApplyDepartureStaff));
        JSONObject json = new JSONObject();
        json.put("departureStatus",DepartureServiceEnum.NOTIFIED_HANDLE_RESIGNATION.getKey());
        json.put("message","等待电子签审核");
        String message = jwtMiniDTO.getName() + "完成了辞职申请书的电签。####"+json;
        this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrApplyDepartureStaff.getApplyDepartureId(), hrApplyDepartureStaff.getId(), jwtMiniDTO.getId(), message,null, true,hrAppendix.getId(), ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
        this.hrNotificationUserService.saveRemindContent(hrApplyDepartureStaff.getClientId(),ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentDepartureEnum.ELECTRONIC_SIGNATURE.getKey(),jwtMiniDTO.getName() + "完成了辞职申请书的电签。等待专管员审核",jwtMiniDTO.getId());
        hrUpcomingService.createServiceUpcoming(hrApplyDepartureStaff.getId(),staff.getId(), "离职服务-审核" + staff.getName() + "的辞职申请书",LocalDate.now(),0);
        return params;
    }

}
