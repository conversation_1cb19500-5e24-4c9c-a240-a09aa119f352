<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.DistinguishOcrRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, type, card_type, file_url, `code`, message, result_data, distinguish_time,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.DistinguishOcr">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="type" property="type"/>
        <result column="card_type" property="cardType"/>
        <result column="file_url" property="fileUrl"/>
        <result column="code" property="code"/>
        <result column="message" property="message"/>
        <result column="result_data" property="resultData"/>
        <result column="distinguish_time" property="distinguishTime"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

</mapper>
