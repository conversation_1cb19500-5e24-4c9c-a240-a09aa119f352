package cn.casair.service.impl;

import cn.casair.domain.HrBillReimbursementClient;
import cn.casair.repository.HrBillReimbursementClientRepository;
import cn.casair.service.HrBillReimbursementClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 报销客户关联表
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrBillReimbursementClientServiceImpl extends ServiceImpl<HrBillReimbursementClientRepository, HrBillReimbursementClient> implements HrBillReimbursementClientService {

}
