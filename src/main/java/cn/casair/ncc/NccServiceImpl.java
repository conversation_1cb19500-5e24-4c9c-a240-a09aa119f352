package cn.casair.ncc;

import cn.casair.cache.RedisCache;
import cn.casair.common.errors.CommonException;
import cn.casair.ncc.dto.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import nccloud.open.api.auto.token.itf.IAPIUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@AllArgsConstructor
public class NccServiceImpl implements NccService {
    private final IAPIUtils iapiUtils;
    private final RedisCache redisCache;

    private static final String NCC_TOKEN_CACHE_KEY = "ncc:token";

    private static final String URL_CREATE_CUSTOMER = "nccloud/api/uapbd/customermanage/customer/add";
    // http://ip:port/nccloud/api/gl/voucher/insert
    private static final String URL_CREATE_VOUCHER = "nccloud/api/gl/voucher/insert";
    // nccloud/api/gl/accountService/qryAccountAuxiliary
    private static final String URL_QRY_ACCOUNT_AUXILIARY = "nccloud/api/gl/accountService/qryAccountAuxiliary";
    // http://ip:port/nccloud/api/uapbd/service/qryCustSupp
    private static final String URL_QRY_CUSTOMER = "nccloud/api/uapbd/service/qryCustSupp";

    @Override
    public NccCustomerCreateResultDTO createCustomer(String name, String type) {
        return post(URL_CREATE_CUSTOMER, NccCustomerCreateResultDTO.build(name, type), new TypeReference<NccResultDTO<NccCustomerCreateResultDTO>>() {
        }).getData();
    }

    @Override
    public NccVoucherDTO createVoucher(NccVoucherDTO nccVoucherDTO) {
        return post(URL_CREATE_VOUCHER, nccVoucherDTO, new TypeReference<NccResultDTO<NccVoucherDTO>>() {
        }).getData();
    }

    @Override
    public List<NccAccountAuxiliaryDTO> qryAccountAuxiliaryList(String code, String name) {
        NccAccountAuxiliaryDTO nccAccountAuxiliaryDTO = new NccAccountAuxiliaryDTO();
        nccAccountAuxiliaryDTO.setCode(code);
        nccAccountAuxiliaryDTO.setName(name);
        return post(URL_QRY_ACCOUNT_AUXILIARY, nccAccountAuxiliaryDTO, new TypeReference<NccResultDTO<List<NccAccountAuxiliaryDTO>>>() {
        }).getData();
    }

    @Override
    public List<NccCustomerDTO> qryCustomerList(String code, String name) {
        NccCustomerDTO nccCustomerDTO = new NccCustomerDTO();
        nccCustomerDTO.setGroupCode("00");
        nccCustomerDTO.setCode(code);
        nccCustomerDTO.setName(name);
        return post(URL_QRY_CUSTOMER, nccCustomerDTO, new TypeReference<NccResultDTO<List<NccCustomerDTO>>>() {
        }).getData();
    }

    public <T> T post(String url, Object requestBody, TypeReference<T> type) {
        String result;
        T response;
        try {
            log.info("NCC API请求开始 - URL: {}, 请求参数: {}", url, JSON.toJSONString(requestBody));
            iapiUtils.setApiUrl(url);
            String bodyJson = JSON.toJSONString(requestBody);
            result = iapiUtils.getAPIRetrun(getToken(), bodyJson);
            log.info("NCC API请求成功 - URL: {}, 响应结果: {}", url, result);
            response = JSON.parseObject(result, type);
        } catch (Exception e) {
            log.error("NCC API请求失败 - URL: {}, 请求参数: {}, 错误信息: {}", url, JSON.toJSONString(requestBody), e.getMessage(), e);
            throw new CommonException("ncc请求失败," + e.getMessage());
        }

        // 检查响应结果是否成功
        if (response instanceof NccResultDTO) {
            NccResultDTO<?> nccResult = (NccResultDTO<?>) response;
            if (!nccResult.isSuccess()) {
                log.error("NCC API业务失败 - URL: {}, 错误信息: {}", url, nccResult.getMessage());
                throw new CommonException("ncc请求失败," + nccResult.getMessage());
            }
        }
        return response;
    }

    private String getToken() throws Exception {
        // 先从缓存获取token
        String cachedToken = redisCache.getCacheObject(NCC_TOKEN_CACHE_KEY);
        if (cachedToken != null) {
            log.info("从缓存获取NCC Token成功");
            return cachedToken;
        }

        // 缓存中没有，重新获取
        String tokenJson = iapiUtils.getToken();
        NccResultDTO<NccTokenDTO> resultDTO = JSON.parseObject(tokenJson, new TypeReference<NccResultDTO<NccTokenDTO>>() {
        });

        if (resultDTO.isSuccess()) {
            String accessToken = resultDTO.getData().getAccessToken();
            Long expiresIn = resultDTO.getData().getExpiresIn();

            // 缓存token，过期时间比实际过期时间提前5分钟
            long cacheExpire = expiresIn - 300;
            redisCache.setCacheObject(NCC_TOKEN_CACHE_KEY, accessToken, Math.toIntExact(cacheExpire), TimeUnit.SECONDS);

            log.info("获取并缓存NCC Token成功，过期时间: {}秒", cacheExpire);
            return accessToken;
        }
        return null;
    }

}
