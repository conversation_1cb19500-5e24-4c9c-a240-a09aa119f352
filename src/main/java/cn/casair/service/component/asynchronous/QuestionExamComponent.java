package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.aop.logging.enums.QuestionTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RecruitmentBrochure;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.DataUtils;
import cn.casair.common.utils.StringUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.HrClient;
import cn.casair.domain.HrExam;
import cn.casair.domain.HrExamDetails;
import cn.casair.domain.HrExamResult;
import cn.casair.domain.HrPaperManagement;
import cn.casair.domain.HrQuestion;
import cn.casair.domain.HrQuestionStation;
import cn.casair.domain.HrRecruitmentBrochure;
import cn.casair.domain.HrRecruitmentBulletin;
import cn.casair.domain.HrRecruitmentStation;
import cn.casair.domain.HrRegistrationDetails;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.HrRegistrationDetailsDTO;
import cn.casair.dto.HrStationDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.QuestionItemDTO;
import cn.casair.dto.excel.HrExamResultImport;
import cn.casair.dto.excel.HrProfileGradesImport;
import cn.casair.dto.excel.HrQuestionExport;
import cn.casair.mapper.HrRegistrationDetailsMapper;
import cn.casair.repository.HrExamDetailsRepository;
import cn.casair.repository.HrExamRepository;
import cn.casair.repository.HrExamResultRepository;
import cn.casair.repository.HrQuestionRepository;
import cn.casair.repository.HrRecruitmentBrochureRepository;
import cn.casair.repository.HrRecruitmentBulletinRepository;
import cn.casair.repository.HrRecruitmentStationRepository;
import cn.casair.repository.HrRegistrationDetailsRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrApplyOpLogsService;
import cn.casair.service.HrClientService;
import cn.casair.service.HrPaperManagementService;
import cn.casair.service.HrQuestionStationService;
import cn.casair.service.HrRecruitmentBulletinService;
import cn.casair.service.HrStationService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 试题 考试结果 简章报名情况导入
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QuestionExamComponent {


    @Value("${file.temp-path}")
    private String tempPath;

    private final SysOperLogService sysOperLogService;
    private final HrAppendixService hrAppendixService;
    private final RedisCache redisCache;
    private final HrQuestionRepository hrQuestionRepository;
    private final HrQuestionStationService hrQuestionStationService;
    private final HrStationService hrStationService;
    private final HrPaperManagementService hrPaperManagementService;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrClientService hrClientService;
    private final HrExamResultRepository hrExamResultRepository;
    private final HrExamDetailsRepository hrExamDetailsRepository;
    private final HrExamRepository hrExamRepository;
    private final HrRecruitmentBrochureRepository hrRecruitmentBrochureRepository;
    private final HrRegistrationDetailsRepository hrRegistrationDetailsRepository;
    private final HrRecruitmentBulletinRepository hrRecruitmentBulletinRepository;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrRecruitmentStationRepository hrRecruitmentStationRepository;
    private final HrRegistrationDetailsMapper hrRegistrationDetailsMapper;
    private final HrRecruitmentBulletinService hrRecruitmentBulletinService;

    @Async
    public void questionImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrQuestionExport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrQuestionExport.class);
            ImportResultDTO importResult;
            int scale = 0;
            int listSize = result.getList().size();
            Map<String, List<HrQuestionExport>> map = result.getList().stream().collect(Collectors.groupingBy(o -> o.getQuestionType() + "_" + o.getTitle()));
            StringBuilder sbError = new StringBuilder();
            for (HrQuestionExport hrQuestionTemplate : result.getList()) {
                try {
                    sbError.delete(0, sbError.length());
                    //重复数据
                    List<HrQuestionExport> mapList = map.get(hrQuestionTemplate.getQuestionType() + "_" + hrQuestionTemplate.getTitle());
                    if (CollectionUtils.isNotEmpty(mapList) && mapList.size() > 1) {
                        throw new CommonException("导入Excel中该数据题目类型和题干名称重复!");
                    }
                    //进行重复校验
                    String title = hrQuestionTemplate.getTitle();
                    Integer questionType = hrQuestionTemplate.getQuestionType();
                    QueryWrapper<HrQuestion> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("question_type", questionType);
                    queryWrapper.eq("title", title);
                    List<HrQuestion> list = hrQuestionRepository.selectList(queryWrapper);
                    if (CollectionUtils.isNotEmpty(list)) {
                        throw new CommonException("系统中该题目类型和题干已存在");
                    }
                    //进行校验并且赋值
                    HrQuestion hrQuestion = getVerify(hrQuestionTemplate);
                    //单独处理岗位
                    ArrayList<String> stationIDList = new ArrayList<>();
                    if (cn.casair.common.utils.StringUtils.isNotBlank(hrQuestionTemplate.getApplicablePost())) {
                        //获取所有岗位的map
                        HashMap<String, String> hashMap = getStationMap();
                        String regex = ",|，|\\s+";
                        List<String> applicablePostList = Arrays.asList((hrQuestionTemplate.getApplicablePost().split(regex)));
                        String applicable = "";
                        //验证岗位
                        for (String applicablePost : applicablePostList) {
                            String applicablePostID = hashMap.get(applicablePost);
                            if (cn.casair.common.utils.StringUtils.isBlank(applicablePostID)) {
                                sbError.append("系统中不存在该岗位");
                            }
                            stationIDList.add(applicablePostID);
                            applicable += applicablePostID + ",";
                        }
                        if (StringUtils.isNotBlank(applicable)) {
                            applicable = applicable.substring(0, applicable.length() - 1);
                        }
                        hrQuestion.setApplicablePost(applicable);
                    }
                    if (StringUtils.isEmpty(sbError)) {
                        //保存题库
                        hrQuestionRepository.insert(hrQuestion);
                        //添加到关联表
                        for (String id : stationIDList) {
                            HrQuestionStation hrQuestionStation = new HrQuestionStation();
                            hrQuestionStation.setStationId(id);
                            hrQuestionStation.setQuestionId(hrQuestion.getId());
                            hrQuestionStationService.save(hrQuestionStation);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("保存试题库型异常:{}", e.getMessage());
                    if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("已存在")) {
                        hrQuestionTemplate.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    scale++;
                    hrQuestionTemplate.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }
            }
            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("试题库批量导入", HrQuestionExport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.QUESTION.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("试题库异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "试题库导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    /**
     * 校验并且赋值
     *
     * @param hrQuestionTemplate
     * @return
     */
    private HrQuestion getVerify(HrQuestionExport hrQuestionTemplate) {
        LinkedList<QuestionItemDTO> questionItemDTOList = new LinkedList<>();
        //如果是单选或多选判断 进行顺序校验
        if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.SINGLE_CHOICE.getValue()) || hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.MULTIPLE_CHOICE.getValue()) || hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
            //添加选项
            if (StringUtils.isBlank(hrQuestionTemplate.getOptionsA())) {
                throw new CommonException("A选项没有值");

            }
            QuestionItemDTO questionItemDTO = new QuestionItemDTO();
            questionItemDTO.setPrefix("A");
            if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                questionItemDTO.setPrefix("1");
            }
            questionItemDTO.setContent(hrQuestionTemplate.getOptionsA());
            questionItemDTOList.add(questionItemDTO);
            //单独处理填空 填空必填项只有一个 选择必填项为两个
            if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsB()) && StringUtils.isNotBlank(hrQuestionTemplate.getOptionsA())) {
                    QuestionItemDTO questionItemB = new QuestionItemDTO();
                    questionItemB.setPrefix("2");
                    questionItemB.setContent(hrQuestionTemplate.getOptionsB());
                    questionItemDTOList.add(questionItemB);
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsB()) && StringUtils.isBlank(hrQuestionTemplate.getOptionsA())) {
                    throw new CommonException("没有按顺序填写选项");
                }
            }
            //这里处理单选和多选
            else {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsB())) {
                    throw new CommonException("B选项没有值");

                }
                QuestionItemDTO questionItemB = new QuestionItemDTO();
                questionItemB.setPrefix("B");
                questionItemB.setContent(hrQuestionTemplate.getOptionsB());
                questionItemDTOList.add(questionItemB);
                //同时处理正确答案不能为空
                String correct = hrQuestionTemplate.getCorrect();
                if (StringUtils.isBlank(correct)) {
                    throw new CommonException("单选或多选时正确答案不能为空");
                }
            }

            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsC()) && StringUtils.isNotBlank(hrQuestionTemplate.getOptionsB())) {
                QuestionItemDTO questionItemC = new QuestionItemDTO();
                questionItemC.setPrefix("C");
                if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                    questionItemC.setPrefix("3");
                }
                questionItemC.setContent(hrQuestionTemplate.getOptionsC());
                questionItemDTOList.add(questionItemC);
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsC()) && StringUtils.isBlank(hrQuestionTemplate.getOptionsB())) {
                throw new CommonException("没有按顺序填写选项");
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsD()) && StringUtils.isNotBlank(hrQuestionTemplate.getOptionsC())) {
                QuestionItemDTO questionItemD = new QuestionItemDTO();
                questionItemD.setPrefix("D");
                if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                    questionItemD.setPrefix("4");
                }
                questionItemD.setContent(hrQuestionTemplate.getOptionsD());
                questionItemDTOList.add(questionItemD);
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsD()) && StringUtils.isBlank(hrQuestionTemplate.getOptionsC())) {
                throw new CommonException("没有按顺序填写选项");
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsE()) && StringUtils.isNotBlank(hrQuestionTemplate.getOptionsD())) {
                QuestionItemDTO questionItemE = new QuestionItemDTO();
                questionItemE.setPrefix("E");
                if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                    questionItemDTO.setPrefix("5");
                }
                questionItemE.setContent(hrQuestionTemplate.getOptionsE());
                questionItemDTOList.add(questionItemE);
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsE()) && StringUtils.isBlank(hrQuestionTemplate.getOptionsD())) {
                throw new CommonException("没有按顺序填写选项");
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsF()) && StringUtils.isNotBlank(hrQuestionTemplate.getOptionsE())) {
                QuestionItemDTO questionItemF = new QuestionItemDTO();
                questionItemF.setPrefix("F");
                if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                    questionItemDTO.setPrefix("6");
                }
                questionItemF.setContent(hrQuestionTemplate.getOptionsF());
                questionItemDTOList.add(questionItemF);
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsF()) && StringUtils.isBlank(hrQuestionTemplate.getOptionsE())) {
                throw new CommonException("没有按顺序填写选项");
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsG()) && StringUtils.isNotBlank(hrQuestionTemplate.getOptionsF())) {
                QuestionItemDTO questionItemG = new QuestionItemDTO();
                questionItemG.setPrefix("G");
                if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                    questionItemDTO.setPrefix("7");
                }
                questionItemG.setContent(hrQuestionTemplate.getOptionsG());
                questionItemDTOList.add(questionItemG);
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsG()) && StringUtils.isBlank(hrQuestionTemplate.getOptionsF())) {
                throw new CommonException("没有按顺序填写选项");
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsH()) && StringUtils.isNotBlank(hrQuestionTemplate.getOptionsG())) {
                QuestionItemDTO questionItemH = new QuestionItemDTO();
                questionItemH.setPrefix("H");
                if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                    questionItemDTO.setPrefix("8");
                }
                questionItemH.setContent(hrQuestionTemplate.getOptionsH());
                questionItemDTOList.add(questionItemH);
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsH()) && StringUtils.isBlank(hrQuestionTemplate.getOptionsG())) {
                throw new CommonException("没有按顺序填写选项");
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsI()) && StringUtils.isNotBlank(hrQuestionTemplate.getOptionsH())) {
                QuestionItemDTO questionItemI = new QuestionItemDTO();
                questionItemI.setPrefix("I");
                if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                    questionItemDTO.setPrefix("9");
                }
                questionItemI.setContent(hrQuestionTemplate.getOptionsI());
                questionItemDTOList.add(questionItemI);
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsI()) && StringUtils.isBlank(hrQuestionTemplate.getOptionsH())) {
                throw new CommonException("没有按顺序填写选项");
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsJ()) && StringUtils.isNotBlank(hrQuestionTemplate.getOptionsI())) {
                QuestionItemDTO questionItemJ = new QuestionItemDTO();
                questionItemJ.setPrefix("J");
                if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
                    questionItemDTO.setPrefix("10");
                }
                questionItemJ.setContent(hrQuestionTemplate.getOptionsJ());
                questionItemDTOList.add(questionItemJ);
            }
            if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsJ()) && StringUtils.isBlank(hrQuestionTemplate.getOptionsI())) {
                throw new CommonException("没有按顺序填写选项");
            }
        }
        //正确答案 判断类型进行校验
        if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.JUDGE_CHOICE.getValue())) {

            String correct = hrQuestionTemplate.getCorrect();
            if (StringUtils.isBlank(hrQuestionTemplate.getCorrect())) {
                throw new CommonException("判断类型题的答案不能为空");
            }
            if (!correct.equals("正确") && !correct.equals("错误")) {
                throw new CommonException("判断类型题的答案只能是正确或错误，并且不能为空");
            }


        }
        //正确答案 简答类型 材料写作 论述进行校验
        if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.SHORT_CHOICE.getValue()) || hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.DISCOURSE_CHOICE.getValue()) || hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.MATERIAL_CHOICE.getValue())) {
            String correct = hrQuestionTemplate.getCorrect();
            if (StringUtils.isBlank(correct)) {
                throw new CommonException("该类型题的答案不能为空");
            }
        }

        //填空时 校验几个空对应几个值
        if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
            String title = hrQuestionTemplate.getTitle();
            //计算标题有几个空
            String s1 = title.replaceAll("#{2,}", "##");
            Pattern p = Pattern.compile("##");
            Matcher m = p.matcher(s1);
            int i = 0;
            while (m.find()) {
                i++;
            }
            if (i == 0) {
                throw new CommonException("填空题必须要有一个空");
            }
            if (i == 1) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsA())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsB())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
            if (i == 2) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsB())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsC())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
            if (i == 3) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsC())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsD())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
            if (i == 4) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsD())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsE())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
            if (i == 5) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsE())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsF())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
            if (i == 6) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsF())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsG())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
            if (i == 7) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsG())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsH())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
            if (i == 8) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsH())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsI())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
            if (i == 9) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsI())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
                if (StringUtils.isNotBlank(hrQuestionTemplate.getOptionsJ())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
            if (i == 10) {
                if (StringUtils.isBlank(hrQuestionTemplate.getOptionsJ())) {
                    throw new CommonException("一个填空处，对应一个值，请正确对应");
                }
            }
        }

//        if(hrQuestionTemplate.getScore().compareTo(new BigDecimal("10")) == 1){
//            throw new CommonException("单道题不能大于10");
//        }
        HrQuestion hrQuestion = new HrQuestion();
        BeanUtils.copyProperties(hrQuestionTemplate, hrQuestion);
        String objects = JSON.toJSONString(questionItemDTOList);
        hrQuestion.setQuestionCont(objects);
        //将正确的答案小写转化成大写
        if (!hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.FILL_CHOICE.getValue())) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(hrQuestionTemplate.getCorrect())) {
                hrQuestion.setCorrect(hrQuestionTemplate.getCorrect().toUpperCase());
            }
        }
        //正确答案 简答类型 材料写作 论述 面试 进行校验
        if (hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.SHORT_CHOICE.getValue()) || hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.DISCOURSE_CHOICE.getValue()) || hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.MATERIAL_CHOICE.getValue()) || hrQuestionTemplate.getQuestionType().equals(QuestionTypeEnum.INTERVIEW.getValue())) {
            String correct = hrQuestionTemplate.getCorrect();
            if (StringUtils.isBlank(correct)) {
                throw new CommonException("该类型题的答案不能为空");
            }
            //并将选项获取空赋值为null
            hrQuestion.setQuestionCont(null);
        }
        return hrQuestion;
    }

    /**
     * 获取所有岗位名称
     *
     * @return
     */
    private HashMap<String, String> getStationMap() {
        HashMap<String, String> hashMap = new HashMap<>();
        for (HrStationDTO hrStationDTO : hrStationService.selectList()) {
            hashMap.put(hrStationDTO.getProfessionName(), hrStationDTO.getId());
        }
        return hashMap;
    }

    @Async
    public void examResultImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO, String paperId, String professionName, String examName) {
        try {
            //判断导入试卷是否存在
            HrPaperManagement hrPaperManagement = hrPaperManagementService.getById(paperId);
            if (hrPaperManagement == null) {
                throw new CommonException("试卷不存在");
            }

            ExcelImportResult<HrExamResultImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrExamResultImport.class);


            ImportResultDTO importResult;

            int scale = 0;
            int listSize = result.getList().size();
            for (HrExamResultImport hrExamResultImport : result.getList()) {
                HrExamResult hrExamResult = new HrExamResult();
                try {
                    BeanUtils.copyProperties(hrExamResultImport, hrExamResult);
//                //考查结果 和体检结果
//                Integer physicalResult = hrExamResultImport.getPhysicalExaminationResult();
//                if (physicalResult!=null){
//                    if (physicalResult!=0&&physicalResult!=1){
//                        throw new CommonException("如果有体检结果，只能是合格或不合格");
//                    }
//                }
//                Integer examResult1 = hrExamResultImport.getExamResult();
//                if (examResult1!=null){
//                    if (examResult1!=0&&examResult1!=1){
//                        throw new CommonException("如果有考察结果，只能是合格或不合格");
//                    }
//                }
                    //通过身份证判断员工是否存在
                    QueryWrapper<HrTalentStaff> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("certificate_num", hrExamResultImport.getCard());
                    List<HrTalentStaff> talentStaffList = hrTalentStaffRepository.selectList(queryWrapper);
                    if (CollectionUtils.isEmpty(talentStaffList)) {
                        throw new CommonException("系统中不存在该员工");
                    }
                    //获取员工id
                    HrTalentStaff hrTalentStaff = talentStaffList.get(0);
                    hrExamResult.setStaffId(hrTalentStaff.getId());
                    //单独处理时间
//                try {
//                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
//                    if (hrExamResultImport.getWrittenExamStartTime()!=null){
//                        LocalDateTime writtenTime = LocalDateTime.parse(hrExamResultImport.getWrittenExamStartTime(), fmt);
//                        hrExamResult.setWrittenExamStartTime(writtenTime);
//                    }
//                    if (hrExamResultImport.getInterviewExamStartTime()!=null){
//                        LocalDateTime interviewTime = LocalDateTime.parse(hrExamResultImport.getInterviewExamStartTime(), fmt);
//                        hrExamResult.setInterviewExamStartTime(interviewTime);
//                    }
//                } catch (Exception e) {
//                    throw new CommonException("笔试时间或面试时间格式不正确");
//                }

                    //通过客户名称获取客户id
                    QueryWrapper<HrClient> queryWrapperClient = new QueryWrapper<>();
                    queryWrapperClient.eq("client_name", hrExamResultImport.getClientName());
                    List<HrClient> hrClientList = hrClientService.list(queryWrapperClient);
                    if (CollectionUtils.isEmpty(hrClientList)) {
                        throw new CommonException("系统中不存在该单位名称");
                    }
                    String clientId = hrClientList.get(0).getId();
                    hrExamResult.setClientId(clientId);
                    //赋值岗位
                    HashMap<String, String> stationMap = getStationMap();
                    String stationId = stationMap.get(professionName);
                    if (StringUtils.isBlank(stationId)) {
                        log.error("导入考试结果发现该{}岗位系统中已经不存在", professionName);
                    } else {
                        hrExamResult.setStationId(stationId);
                    }
                    //添加到应试经历
                    //HrStaffInterview hrStaffInterview = new HrStaffInterview();
                    //分数
                    //hrStaffInterview.setInterviewGrade(hrExamResultImport.getScore());
                    hrExamResult.setInterviewLink(1);
                    //hrStaffInterview.setInterviewEvaluate(hrExamResultImport.getEvaluation());
                    //根据试卷id获取试卷及格线
                    int passLine = hrPaperManagementService.getById(paperId).getPassLine();
                    //判断笔试成绩是否为空
                    int scoreResult = 0;
                    if (hrExamResultImport.getScore() != null) {
                        int score = hrExamResultImport.getScore().intValue();
                        if (score >= passLine) {
                            scoreResult = 1;
                            hrExamResult.setInterviewResult(1);
                        } else {
                            hrExamResult.setInterviewResult(2);
                        }
                    }
                    //赋值面试平均成绩
                    String interviewScore = hrExamResultImport.getInterviewScore();
                    if (StringUtils.isNotBlank(interviewScore)) {
                        String regex = ",|，|\\s+";
                        List<String> scoreList = Arrays.asList((interviewScore.split(regex)));
                        //定义面试平均成绩
                        BigDecimal parseInt = new BigDecimal("0");
                        for (String score : scoreList) {
                            try {
                                parseInt = parseInt.add(new BigDecimal(score));
                            } catch (NumberFormatException e) {
                                throw new CommonException("请正确填写面试考官打分，多个考官请已逗号分割");
                            }
                        }
                        BigDecimal size = new BigDecimal(scoreList.size());
                        BigDecimal divide = parseInt.divide(size, 2, BigDecimal.ROUND_HALF_UP);
                        hrExamResult.setInterviewScoreResult(divide);
                    }
                    //
                    //hrStaffInterview.setInterviewTime(hrExamResultImport.getExamDate());
                    //hrStaffInterview.setInterviewUnit(hrExamResultImport.getClientName());
                    // hrStaffInterview.setClientId(clientId);
                    //hrStaffInterview.setStaffId(hrTalentStaff.getId());
                    //hrStaffInterview.setInterviewStationId(stationId);
                    //hrStaffInterviewService.save(hrStaffInterview);
                    hrExamResult.setExamType(0);
                    hrExamResult.setPaperId(paperId);
                    hrExamResult.setExamName(examName);
                    hrExamResult.setProfessionName(professionName);
                    //判断时新增还是修改
                    QueryWrapper<HrExamResult> qw = new QueryWrapper<>();
                    qw.eq("paper_id", paperId);
                    qw.eq("profession_name", professionName);
                    qw.eq("exam_name", examName);
                    qw.eq("staff_id", hrTalentStaff.getId());
                    List<HrExamResult> list = hrExamResultRepository.selectList(qw);
                    if (CollectionUtils.isEmpty(list)) {
                        hrExamResultRepository.insert(hrExamResult);
                    } else {
                        hrExamResult.setId(list.get(0).getId());
                        hrExamResultRepository.updateById(hrExamResult);
                    }
                    //将对应的成绩录入
                    //对应面试环节 1笔试 2 面试 7 考察  8体检
                    //笔试
                    if (hrExamResultImport.getScore() != null) {
                        String writtenLocation = hrExamResultImport.getWrittenLocation();
                        String writtenExamStartTime = hrExamResultImport.getWrittenExamStartTime();
                        addExamDetails(hrExamResultImport.getScore(), hrExamResult, scoreResult, writtenLocation, writtenExamStartTime, 1);
                    }
                    //面试
                    if (hrExamResult.getInterviewScoreResult() != null && hrExamResultImport.getInterviewResult() != null) {
                        String interviewLocation = hrExamResultImport.getInterviewLocation();
                        String interviewExamStartTime = hrExamResultImport.getInterviewExamStartTime();
                        Integer interviewResult = hrExamResultImport.getInterviewResult();
                        addExamDetails(hrExamResult.getInterviewScoreResult(), hrExamResult, interviewResult, interviewLocation, interviewExamStartTime, 2);
                    }
                    //考察
                    if (hrExamResultImport.getExamResult() != null) {
                        String surveyExamStartTime = hrExamResultImport.getSurveyExamStartTime();
                        String surveyLocation = hrExamResultImport.getSurveyLocation();
                        addExamDetails(null, hrExamResult, hrExamResultImport.getExamResult(), surveyLocation, surveyExamStartTime, 7);
                    }
                    //体检
                    if (hrExamResultImport.getPhysicalExaminationResult() != null) {
                        String physicalExamStartTime = hrExamResultImport.getPhysicalExamStartTime();
                        String physicalLocation = hrExamResultImport.getPhysicalLocation();
                        addExamDetails(null, hrExamResult, hrExamResultImport.getPhysicalExaminationResult(), physicalLocation, physicalExamStartTime, 8);
                    }
                } catch (Exception e) {
                    log.error("保存考试结果异常:{}", e.getMessage());
                    hrExamResultImport.setErrorMsg(e.getMessage());
                } finally {
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }

            }
            if (StringUtils.isNotBlank(paperId)) {
                examNumber(paperId, professionName, examName);
            }
            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("考试结果批量导入", HrExamResultImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.EXAM_RESULT.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("考试结果异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "考试结果导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    private void addExamDetails(BigDecimal score, HrExamResult hrExamResult, int scoreResult, String writtenLocation, String writtenExamStartTime, int examType) {
        HrExamDetails hrExamDetails = new HrExamDetails();
        //笔试
        hrExamDetails.setExamType(examType);
        hrExamDetails.setIsPassed(scoreResult);
        hrExamDetails.setScore(score);
        hrExamDetails.setExamResultId(hrExamResult.getId());
        if (StringUtils.isNotBlank(writtenLocation)) {
            hrExamDetails.setExamPlace(writtenLocation);
        }
        if (StringUtils.isNotBlank(writtenExamStartTime)) {
            try {
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                LocalDateTime writtenTime = LocalDateTime.parse(writtenExamStartTime, fmt);
                hrExamDetails.setExamTime(writtenTime);
            } catch (Exception e) {
                //对应面试环节 1笔试 2 面试 7 考察  8体检
                if (examType == 1) {
                    throw new CommonException("笔试时间格式不正确，请重新进行输入");
                }
                if (examType == 2) {
                    throw new CommonException("面试时间格式不正确，请重新进行输入");
                }
                if (examType == 7) {
                    throw new CommonException("考察时间格式不正确，请重新进行输入");
                }
                if (examType == 8) {
                    throw new CommonException("体检时间格式不正确，请重新进行输入");
                }
            }
        }
        QueryWrapper<HrExamDetails> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("exam_result_id", hrExamResult.getId());
        queryWrapper.eq("exam_type", examType);
        List<HrExamDetails> hrExamDetailsList = hrExamDetailsRepository.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(hrExamDetailsList)) {
            String id = hrExamDetailsList.get(0).getId();
            hrExamDetails.setId(id);
            hrExamDetailsRepository.updateById(hrExamDetails);
        } else {
            hrExamDetailsRepository.insert(hrExamDetails);
        }
    }

    /**
     * 重新计算考试人数和通过率
     *
     * @param paperId
     */
    private void examNumber(String paperId, String professionName, String examName) {
        //获取试卷的及格线
        HrPaperManagement hrPaperManagement = hrPaperManagementService.getById(paperId);
        //重新计算考试人数和通过率
        QueryWrapper<HrExamResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("paper_id", paperId);
        queryWrapper.eq(StringUtils.isNotBlank(professionName), "profession_name", professionName);
        queryWrapper.eq(StringUtils.isNotBlank(examName), "exam_name", examName);
        //查询试卷的考试人数
        int totalCount = hrExamResultRepository.selectCount(queryWrapper);
        //查询试卷的及格人数
        queryWrapper.ge("score", hrPaperManagement.getPassLine());
        int passLineCount = hrExamResultRepository.selectCount(queryWrapper);
        //修改试卷的考试人数和通过率
        HrExam hrExam = new HrExam();
        hrExam.setExamsNumber(totalCount);
        //计算通过率
        if (totalCount != 0) {
            BigDecimal bigDecimal = new BigDecimal((float) passLineCount / totalCount).setScale(2, RoundingMode.HALF_UP);
            hrExam.setExamsPassingRate(bigDecimal);
        } else {
            hrExam.setExamsPassingRate(new BigDecimal("0"));
        }
        QueryWrapper<HrExam> qw = new QueryWrapper<>();
        qw.eq("paper_id", paperId);
        qw.eq(StringUtils.isNotBlank(professionName), "profession_name", professionName);
        qw.eq(StringUtils.isNotBlank(examName), "exam_name", examName);
        hrExamRepository.update(hrExam, qw);
    }

    @Async
    public void profileGradesImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO, String examName) {
        try {
            ExcelImportResult<HrProfileGradesImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrProfileGradesImport.class);


            ImportResultDTO importResult;

            int listSize = result.getList().size();
            int scale = 0;
            List<HrExamResult> hrExamResults = new ArrayList<>();
            //通过简章名称获取简章id
            QueryWrapper<HrRecruitmentBrochure> hrRecruitmentBrochureQueryWrapper = new QueryWrapper<>();
            hrRecruitmentBrochureQueryWrapper.eq("recruit_brochure_name", examName);
            HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectOne(hrRecruitmentBrochureQueryWrapper);
            if (hrRecruitmentBrochure == null) {
                throw new CommonException("导入的简章名称不存在");
            }
            //定义map获取导入的岗位，以及导入的成绩类型
            HashMap<String, Integer> hashMap = new HashMap<>();
            for (HrProfileGradesImport hrExamResultImport : result.getList()) {
                HrExamResult hrExamResult = new HrExamResult();
                try {
                    BeanUtils.copyProperties(hrExamResultImport, hrExamResult);
                    //通过身份证判断员工是否存在
                    QueryWrapper<HrTalentStaff> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("certificate_num", hrExamResultImport.getCard());
                    List<HrTalentStaff> talentStaffList = hrTalentStaffRepository.selectList(queryWrapper);
                    if (CollectionUtils.isEmpty(talentStaffList)) {
                        throw new CommonException("系统中不存在该员工");
                    }
                    //赋值岗位
                    HashMap<String, String> stationMap = getStationMap();
                    String stationId = stationMap.get(hrExamResultImport.getProfessionName());
                    if (StringUtils.isBlank(stationId)) {
                        log.error("导入考试结果发现该{}岗位系统中已经不存在", hrExamResultImport.getProfessionName());
                    } else {
                        hrExamResult.setStationId(stationId);
                    }
                    //获取员工id
                    HrTalentStaff hrTalentStaff = talentStaffList.get(0);
                    hrExamResult.setStaffId(hrTalentStaff.getId());
                    //获取该报名简章下该岗位该员工是否存在
                    QueryWrapper<HrRegistrationDetails> hrRegistrationDetailsQueryWrapper = new QueryWrapper<>();
                    hrRegistrationDetailsQueryWrapper.eq("brochure_id", hrRecruitmentBrochure.getId());
                    hrRegistrationDetailsQueryWrapper.eq("station_name", hrExamResultImport.getProfessionName());
                    hrRegistrationDetailsQueryWrapper.eq("staff_id", hrTalentStaff.getId());
                    List<HrRegistrationDetails> hrRegistrationDetails = hrRegistrationDetailsRepository.selectList(hrRegistrationDetailsQueryWrapper);
                    if (CollectionUtils.isEmpty(hrRegistrationDetails)) {
                        throw new CommonException("该人员不存在报名情况");
                    }
                    //获取当前简章岗位的试卷id
                    //获取该简章该岗位下数据
                    QueryWrapper<HrRecruitmentStation> hrRecruitmentStationQueryWrapper = new QueryWrapper<>();
                    hrRecruitmentStationQueryWrapper.eq("service_id", hrRecruitmentBrochure.getId());
                    hrRecruitmentStationQueryWrapper.eq("recruitment_station_name", hrExamResultImport.getProfessionName());
                    List<HrRecruitmentStation> hrRecruitmentStationList = hrRecruitmentStationRepository.selectList(hrRecruitmentStationQueryWrapper);
                    String paperId = hrRecruitmentStationList.get(0).getPaperId();
                    //如果该招聘简章存在
                    String hrRecruitmentBrochureId = hrRecruitmentBrochure.getId();
                    //获取招聘岗位信息
                    HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationList.get(0);
                    //考试形式
                    Integer examFormat = hrRecruitmentStation.getExamFormat();
                    //如果考试是 先笔后面 或先面后笔 获取面试试卷名称
                    if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey() || examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey()) {
                        String interviewPaperId = hrRecruitmentStationList.get(0).getInterviewPaperId();
                        HrPaperManagement hrPaperManagement = hrPaperManagementService.getById(interviewPaperId);
                        if (hrPaperManagement != null && StringUtils.isNotBlank(hrPaperManagement.getPaperName())) {
                            hrExamResult.setBrochurePaper(hrPaperManagement.getPaperName());
                        }
                        //通过试卷id获取名称
                    }
                    //通过客户名称获取客户id
                    QueryWrapper<HrClient> queryWrapperClient = new QueryWrapper<>();
                    queryWrapperClient.eq("client_name", hrExamResultImport.getClientName());
                    List<HrClient> hrClientList = hrClientService.list(queryWrapperClient);
                    if (CollectionUtils.isEmpty(hrClientList)) {
                        throw new CommonException("系统中不存在该单位名称");
                    }
                    String clientId = hrClientList.get(0).getId();
                    hrExamResult.setClientId(clientId);
                    //校验是否能进行导入
                    if (hrExamResultImport.getScore() != null) {
                        //笔试成绩有值是否发布了笔试公告
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins = getHrRecruitmentBulletins(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                            throw new CommonException("没有发布笔试公告，不能录入笔试成绩");
                        }
                        //根据考试形式来判断下一场公告是否已发布
                        if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getBulletins(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                                throw new CommonException("该简章已经发布了笔试成绩公告，不能录入笔试成绩了");
                            }
                        } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey()) {
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                                throw new CommonException("该简章已经发布了最终成绩公告，不能录入笔试成绩了");
                            }
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins2 = getBulletins(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins2)) {
                                throw new CommonException("该简章已经发布了笔试成绩公告，不能录入笔试成绩了");
                            }
                        } else if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey()) {
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getRecruitmentBulletins(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                                throw new CommonException("该简章已经发布了面试成绩公告，不能录入笔试成绩了");
                            }
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins2 = getBulletins(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins2)) {
                                throw new CommonException("该简章已经发布了笔试成绩公告，不能录入笔试成绩了");
                            }
                        }
                    }
                    //赋值面试平均成绩
                    String interviewScore = hrExamResultImport.getInterviewScore();
                    if (StringUtils.isNotBlank(interviewScore)) {
                        //面试成绩有值是否发布了面试公告
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins = getRecruitmentBulletins(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                            throw new CommonException("没有发布面试公告，不能录入面试成绩");
                        }
                        //根据考试形式来判断下一场公告是否已发布
                        if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getHrRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                                throw new CommonException("该简章已经发布了面试成绩公告，不能录入面试成绩了");
                            }
                        } else if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey()) {
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                                throw new CommonException("该简章已经发布了最终成绩公告，不能录入面试成绩了");
                            }
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins2 = getHrRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins2)) {
                                throw new CommonException("该简章已经发布了面试成绩公告，不能录入面试成绩了");
                            }
                        } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey()) {
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getHrRecruitmentBulletins(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                                throw new CommonException("该简章已经发布了笔试公告，不能录入面试成绩了");
                            }
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins2 = getHrRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins2)) {
                                throw new CommonException("该简章已经发布了面试成绩公告，不能录入面试成绩了");
                            }
                        }
                        String regex = ",|，|\\s+";
                        List<String> scoreList = Arrays.asList((interviewScore.split(regex)));
                        //ArrayList<Integer> interviewList = new ArrayList<>();
                        //定义面试平均成绩
                        BigDecimal parseInt = new BigDecimal("0");
                        for (String score : scoreList) {
                            try {
                                //interviewList.add(Integer.parseInt(score));
                                //parseInt += Integer.parseInt(score);
                                parseInt = parseInt.add(new BigDecimal(score));

                            } catch (NumberFormatException e) {
                                throw new CommonException("请正确填写面试考官打分，多个考官请已逗号分割");
                            }
                        }
                        //如果去掉极值
                        BigDecimal size = new BigDecimal(scoreList.size());
                        if (scoreList.size() >= 3 && hrExamResultImport.getIzExtremum() != null && hrExamResultImport.getIzExtremum() == 1) {
                            String max = Collections.max(scoreList);
                            String min = Collections.min(scoreList);
                            parseInt = parseInt.subtract(new BigDecimal(max)).subtract(new BigDecimal(min));
                            size = new BigDecimal(scoreList.size() - 2);
                        } else {
                            hrExamResult.setIzExtremum(0);
                        }
                        BigDecimal divide = parseInt.divide(size, 2, BigDecimal.ROUND_HALF_UP);
                        hrExamResult.setInterviewScoreResult(divide);
                    }
                    if (hrExamResultImport.getPhysicalExaminationResult() != null) {
                        //体检成绩有值是否发布了公告
                        LocalDate checkupDate = hrRegistrationDetails.get(0).getCheckupDate();
                        String checkupPlace = hrRegistrationDetails.get(0).getCheckupPlace();
                        if (StringUtils.isBlank(checkupPlace) || checkupDate == null) {
                            throw new CommonException("没有通知体检，不能录入体检成绩");
                        }
                        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
                        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
                        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
                        hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.PHYSICAL_EXAM_RESULTS.getKey());
                        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins)) {
                            throw new CommonException("发布了体检结果公告，不能录入体检成绩");
                        }
                    }
                    //面试权重
                    BigDecimal interviewScoreWeight = hrRecruitmentStation.getInterviewScoreWeight();
                    //笔试权重
                    BigDecimal writtenScoreWeight = hrRecruitmentStation.getWrittenScoreWeight();
                    //现根据考试形式来判断如何计算最终成绩
//                        if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
//                            BigDecimal score = hrExamResultImport.getScore();
//                            if (score != null) {
//                                BigDecimal multiply = score.multiply(writtenScoreWeight);
//                                hrExamResult.setFinalResult(multiply);
//                            }
//                        } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
//                            BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
//                            if (interviewScoreResult != null) {
//                                BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
//                                hrExamResult.setFinalResult(multiply);
//                            }
//                        } else {
//                            BigDecimal score = hrExamResultImport.getScore();
//                            BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
//                            if (interviewScoreResult != null && score != null) {
//                                BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
//                                BigDecimal bigDecimal = score.multiply(writtenScoreWeight);
//                                BigDecimal add = multiply.add(bigDecimal);
//                                hrExamResult.setFinalResult(add);
//                            }
//                        }
                    if (hrExamResultImport.getExamResult() != null) {
                        if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins = getBulletins(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                                throw new CommonException("没有发布笔试成绩公告，不能录入考察成绩");
                            }
                        } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins = getHrRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                                throw new CommonException("没有发布面试成绩公告，不能录入考察成绩");
                            }
                        } else {
                            List<HrRecruitmentBulletin> hrRecruitmentBulletins = getRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                            if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                                throw new CommonException("没有发布最终成绩公告，不能录入考察成绩");
                            }
                        }
                        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
                        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
                        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
                        hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.INVESTIGATION_RESULTS.getKey());
                        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins)) {
                            throw new CommonException("发布了考察结果公告，不能录入考察成绩");
                        }
                    }

                    //笔试成绩添加日志
                    if (hrExamResultImport.getScore() != null) {
                        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "笔试成绩--" + hrExamResultImport.getScore(), null, ServiceCenterEnum.SIGN_UP.getKey());
                    }
                    //面试成绩添加日志
                    if (hrExamResult.getInterviewScoreResult() != null) {
                        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "面试平均成绩--" + hrExamResult.getInterviewScoreResult(), null, ServiceCenterEnum.SIGN_UP.getKey());
                    }
                    //加试成绩添加日志
                    if (hrExamResultImport.getAddResult() != null) {
                        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "加试成绩--" + hrExamResultImport.getAddResult(), null, ServiceCenterEnum.SIGN_UP.getKey());
                    }
                    //考察添加日志
                    if (hrExamResultImport.getExamResult() != null) {
                        Integer examResult = hrExamResultImport.getExamResult();
                        String results = "";
                        if (examResult == 1) {
                            results = "合格";
                        } else {
                            results = "不合格";
                        }
                        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "考查结果--" + results, null, ServiceCenterEnum.SIGN_UP.getKey());
                    }
                    //体检结果日志
                    if (hrExamResultImport.getPhysicalExaminationResult() != null) {
                        Integer physicalExaminationResult = hrExamResultImport.getPhysicalExaminationResult();
                        String results = "";
                        if (physicalExaminationResult == 1) {
                            results = "合格";
                        } else {
                            results = "不合格";
                        }
                        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "体检结果--" + results, null, ServiceCenterEnum.SIGN_UP.getKey());
                    }

                    hrExamResult.setExamType(0);

                    if (StringUtils.isNotBlank(paperId)) {
                        hrExamResult.setPaperId(paperId);
                    } else {
                        //如果为空 说明这考招聘只能面试，将试卷id赋值为面试试卷id
                        paperId = hrRecruitmentStationList.get(0).getInterviewPaperId();
                        hrExamResult.setPaperId(paperId);
                    }
                    hrExamResult.setExamName(examName);
                    hrExamResult.setProfessionName(hrExamResultImport.getProfessionName());
                    //判断时新增还是修改
                    QueryWrapper<HrExamResult> qw = new QueryWrapper<>();
                    //qw.eq(StringUtils.isNotBlank(paperId),"paper_id", paperId);
                    qw.eq("profession_name", hrExamResultImport.getProfessionName());
                    qw.eq("exam_name", examName);
                    qw.eq("staff_id", hrTalentStaff.getId());
                    List<HrExamResult> list = hrExamResultRepository.selectList(qw);
                    if (CollectionUtils.isEmpty(list)) {
                        hrExamResultRepository.insert(hrExamResult);
                        hrExamResults.add(hrExamResult);
                    } else {
                        hrExamResult.setId(list.get(0).getId());
                        hrExamResultRepository.updateById(hrExamResult);
                        hrExamResults.add(hrExamResult);
                    }
                    ///计算通过率
                    if (StringUtils.isNotBlank(paperId)) {
                        examNumber(paperId, hrExamResultImport.getProfessionName(), examName);
                    }
                    //获取导入的岗位，以及导入的成绩
                    String professionName = hrExamResultImport.getProfessionName();
                    if (!hashMap.containsKey(professionName)) {
                        if (hrExamResultImport.getScore() != null) {
                            hashMap.put(professionName, 1);
                        }
                        if (hrExamResultImport.getInterviewScore() != null) {
                            hashMap.put(professionName, 2);
                        }
                        if (hrExamResultImport.getExamResult() != null) {
                            hashMap.put(professionName, 3);
                        }
                        if (hrExamResultImport.getPhysicalExaminationResult() != null) {
                            hashMap.put(professionName, 4);
                        }
                    }
                    //修改报名情况状态
//                updateTyde(hrExamResultImport.getProfessionName(), hrRecruitmentBrochureId, examName, hrTalentStaff.getId());
                } catch (Exception e) {
                    log.error("保存考试结果异常:{}", e.getMessage());
                    hrExamResultImport.setErrorMsg(e.getMessage());
                } finally {
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }
            }
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(hashMap)) {
                for (String professionName : hashMap.keySet()) {
                    this.updateApplicantStatus(hrRecruitmentBrochure, professionName, hashMap.get(professionName));
                }
            }

            // 封装返回信息
            importResult = ImportResultUtils.writeErrorFile("简章报名结果导入", HrProfileGradesImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.RECRUITMENT_RESULT.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("简章报名结果导入处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "简章报名结果发生异常：" + e.getMessage(), 10, TimeUnit.MINUTES);
        }
    }

    /**
     * 最终成绩公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getRecruitmentBulletinList(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.FINAL_ACHIEVEMENT.getKey());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
        List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
        return hrRecruitmentBulletins;
    }

    /**
     * 面试成绩公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getHrRecruitmentBulletinList(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.INTERVIEW_ACHIEVEMENT.getKey());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
        return hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
    }

    /**
     * 笔试成绩公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getBulletins(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.WRITTEN_ACHIEVEMENT.getKey());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
        return hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
    }

    /**
     * 查询面试公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getRecruitmentBulletins(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.INTERVIEW_ANNOUNCEMENT.getKey());
        List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
        return hrRecruitmentBulletins;
    }

    /**
     * 查询笔试公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getHrRecruitmentBulletins(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.WRITTEN_ANNOUNCEMENT.getKey());
        return hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
    }

    /**导入成绩和单独的修改成绩，最终调用的修改报名人员状态不是同一个方法
     * 修改报名人员状态
     *
     * @param hrRecruitmentBrochure 招聘简章
     * @param professionName 岗位名称
     * @param achievementType 成绩类型
     */
    public void updateApplicantStatus(HrRecruitmentBrochure hrRecruitmentBrochure, String professionName, Integer achievementType) {
        List<HrRecruitmentStation> hrRecruitmentStations = hrRecruitmentStationRepository.selectList(new QueryWrapper<HrRecruitmentStation>()
            .eq("service_id", hrRecruitmentBrochure.getId()).eq("recruitment_station_name", professionName));
        for (HrRecruitmentStation hrRecruitmentStation : hrRecruitmentStations) {
            //招聘人数
            BigDecimal recruitmentPeopleNumber = new BigDecimal(String.valueOf(hrRecruitmentStation.getRecruitmentPeopleNumber()));
            //报名人根据笔试成绩倒序排序，取出进入考察的人员，其他人全部是笔试不合格
            List<Integer> statusList = new ArrayList<>();
            if (achievementType == 1 && (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())
                || hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey()))) {
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_WRITTEN_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
            } else if (achievementType == 1 && hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_WRITTEN_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INTERVIEW_SCOPE.getKey());
            } else if (achievementType == 2 && (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())
                || hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey()))) {
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INTERVIEW_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
            } else if (achievementType == 2 && hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INTERVIEW_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_WRITTEN_EXAM_SCOPE.getKey());
            } else if (achievementType == 3) {//todo 该状态
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INVESTIGATE_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INSPECTION_RESULTS.getKey());
            } else if (achievementType == 4) {//todo 该状态
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_PHYSICAL_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.FAILED_PHYSICAL_EXAM.getKey());
            }
            List<HrRegistrationDetailsDTO> hrRegistrationDetailsDTOS = hrRegistrationDetailsRepository.findDetailsWrittenExam(hrRecruitmentBrochure.getId(), hrRecruitmentStation.getId(), statusList);
            this.calculateFinalScore(hrRecruitmentBrochure, hrRecruitmentStation, hrRegistrationDetailsDTOS);
            //判断这次导入是什么成绩
            switch (achievementType) {
                case 1://笔试成绩
                    //判断岗位形式是什么形式
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getInvestigationRatio()));
                        if (number != null) {
                            //笔试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //笔试成绩合格
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有笔试成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = qualifiedList.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getScore)).collect(Collectors.toList());
                                Collections.reverse(registrationDetailsDTOS);
                                this.inspectorInfo(registrationDetailsDTOS, number, hrRecruitmentStation);
                            }
                        }
                    }
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getInvestigationRatio()));
                        if (number != null) {
                            //最终成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //笔试成绩合格
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有最终成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = qualifiedList.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getFinalResult)).collect(Collectors.toList());
                                Collections.reverse(registrationDetailsDTOS);
                                this.inspectorInfo(registrationDetailsDTOS, number, hrRecruitmentStation);
                            }
                        }
                    }
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                        //计算该岗位下进入考察的人员  根据晋级比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getPromotedRatio()));
                        if (number != null) {
                            //笔试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //笔试成绩合格,大于笔试及格线的人数
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有笔试成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = this.hrRecruitmentBulletinService.mergerList(qualifiedList, achievementType);
                                //进入面试人员
                                List<HrRegistrationDetailsDTO> investigateDetailsDTOS = registrationDetailsDTOS.stream().filter(ls -> ls.getRank() <= number).collect(Collectors.toList());
                                for (HrRegistrationDetailsDTO hrRegistrationDetailsDTO : investigateDetailsDTOS) {
                                    HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                    hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                                    hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                                }
                                List<HrRegistrationDetailsDTO> detailsDTOS = registrationDetailsDTOS.stream().filter(d1 -> investigateDetailsDTOS.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(detailsDTOS)) {
                                    for (HrRegistrationDetailsDTO hrRegistrationDetailsDTO : detailsDTOS) {
                                        HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                        hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.NO_INTERVIEW_SCOPE.getKey());
                                        hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 2://面试成绩
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getInvestigationRatio()));
                        if (number != null) {
                            //面试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) == -1).collect(Collectors.toList());
                            for (HrRegistrationDetailsDTO hrRegistrationDetailsDTO : failList) {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            }
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //面试成绩合格 ，大于面试及格线
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有面试成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = qualifiedList.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getInterviewScoreResult)).collect(Collectors.toList());
                                Collections.reverse(registrationDetailsDTOS);//根据面试成绩排序
                                this.inspectorInfo(registrationDetailsDTOS, number, hrRecruitmentStation);
                            }
                        }
                    }
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getInvestigationRatio()));
                        if (number != null) {
                            //面试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //面试成绩合格
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有最终成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = qualifiedList.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getFinalResult)).collect(Collectors.toList());
                                Collections.reverse(registrationDetailsDTOS);
                                this.inspectorInfo(registrationDetailsDTOS, number, hrRecruitmentStation);
                            }
                        }
                    }
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getPromotedRatio()));
                        if (number != null) {
                            //面试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //笔试成绩合格,大于笔试及格线的人数
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有笔试成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = this.hrRecruitmentBulletinService.mergerList(qualifiedList, achievementType);
                                //进入笔试人员
                                List<HrRegistrationDetailsDTO> investigateDetailsDTOS = registrationDetailsDTOS.stream().filter(ls -> ls.getRank() <= number).collect(Collectors.toList());
                                investigateDetailsDTOS.forEach(hrRegistrationDetailsDTO -> {//进入笔试人员信息
                                    HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                    hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                                    hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                                });
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOList = registrationDetailsDTOS.stream().filter(d1 -> investigateDetailsDTOS.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(registrationDetailsDTOList)) {
                                    registrationDetailsDTOList.forEach(hrRegistrationDetailsDTO -> {
                                        HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                        hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.NO_WRITTEN_EXAM_SCOPE.getKey());
                                        hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                                    });
                                }
                            }
                        }
                    }
                    break;
                case 3://考察成绩
                    List<HrRegistrationDetailsDTO> qualifiedListExamResult = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getExamResult() != null && ls.getExamResult() == 1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(qualifiedListExamResult)) {
                        qualifiedListExamResult.forEach(hrRegistrationDetailsDTO -> {
                            HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                            hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_PHYSICAL.getKey());
                            hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                        });
                    }
                    List<HrRegistrationDetailsDTO> unqualifiedListExamResult = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getExamResult() != null && ls.getExamResult() == 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(unqualifiedListExamResult)) {
                        unqualifiedListExamResult.forEach(hrRegistrationDetailsDTO -> {
                            HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                            hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INSPECTION_RESULTS.getKey());
                            hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                        });
                    }
                    break;
                case 4://体检成绩
                    List<HrRegistrationDetailsDTO> qualifiedListPhysicalExaminationResult = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getPhysicalExaminationResult() != null && ls.getPhysicalExaminationResult() == 1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(qualifiedListPhysicalExaminationResult)) {
                        qualifiedListPhysicalExaminationResult.forEach(hrRegistrationDetailsDTO -> {
                            HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                            hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE_FORMULA_EMPLOYED.getKey());
                            hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                        });
                    }
                    List<HrRegistrationDetailsDTO> unqualifiedListPhysicalExaminationResult = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getPhysicalExaminationResult() != null && ls.getPhysicalExaminationResult() == 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(unqualifiedListPhysicalExaminationResult)) {
                        unqualifiedListPhysicalExaminationResult.forEach(hrRegistrationDetailsDTO -> {
                            HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                            hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.FAILED_PHYSICAL_EXAM.getKey());
                            hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                        });
                    }
                    break;
            }
        }

    }

    /**
     * 计算报名人员最终成绩
     *
     * @param hrRecruitmentBrochure
     * @param hrRecruitmentStation
     * @param hrRegistrationDetailsDTOS
     */
    private void calculateFinalScore(HrRecruitmentBrochure hrRecruitmentBrochure, HrRecruitmentStation hrRecruitmentStation, List<HrRegistrationDetailsDTO> hrRegistrationDetailsDTOS) {
        for (HrRegistrationDetailsDTO hrRegistrationDetailsDTO : hrRegistrationDetailsDTOS) {
            //查询报名人员考试结果实体类
            List<HrExamResult> hrExamResults = hrExamResultRepository.selectList(new QueryWrapper<HrExamResult>().eq("exam_name", hrRecruitmentBrochure.getRecruitBrochureName())
                .eq("profession_name", hrRecruitmentStation.getRecruitmentStationName()).eq("staff_id", hrRegistrationDetailsDTO.getStaffId()));
            HrExamResult hrExamResult = null;
            if (CollectionUtils.isNotEmpty(hrExamResults)) {
                hrExamResult = hrExamResults.get(0);
            }

            if (hrExamResult != null) {
                Integer examFormat = hrRecruitmentStation.getExamFormat();
                BigDecimal writtenScoreWeight = hrRecruitmentStation.getWrittenScoreWeight();
                BigDecimal interviewScoreWeight = hrRecruitmentStation.getInterviewScoreWeight();
                //现根据考试形式来判断如何计算最终成绩
                if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
                    BigDecimal score = hrExamResult.getScore();
                    if (score != null) {
                        BigDecimal multiply = score.multiply(writtenScoreWeight);
                        hrExamResult.setFinalResult(multiply);
                        hrRegistrationDetailsDTO.setFinalResult(multiply);
                    }
                } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
                    BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
                    if (interviewScoreResult != null) {
                        BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
                        hrExamResult.setFinalResult(multiply);
                        hrRegistrationDetailsDTO.setFinalResult(multiply);
                    }
                } else {
                    BigDecimal score = hrExamResult.getScore();
                    BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
                    if (interviewScoreResult != null && score != null) {
                        BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
                        BigDecimal bigDecimal = score.multiply(writtenScoreWeight);
                        BigDecimal add = multiply.add(bigDecimal);
                        hrExamResult.setFinalResult(add);
                        hrRegistrationDetailsDTO.setFinalResult(add);
                    }
                }
                this.hrExamResultRepository.updateHrExamResult(hrExamResult);
            }
        }
    }

    /**
     * 进入考察人员
     *
     * @param registrationDetailsDTOS
     * @param number
     * @param hrRecruitmentStation
     */
    private void inspectorInfo(List<HrRegistrationDetailsDTO> registrationDetailsDTOS, Integer number, HrRecruitmentStation hrRecruitmentStation) {
        //进入考察人员
        List<HrRegistrationDetailsDTO> investigateDetailsDTOS = registrationDetailsDTOS.subList(0, Math.min(registrationDetailsDTOS.size(), number));
        //最高得分
        BigDecimal max = Collections.max(investigateDetailsDTOS.stream().map(HrRegistrationDetailsDTO::getFinalResult).distinct().collect(Collectors.toList()));
        //校验是否是等额考察
        //判断最高得分的人数是否大于招聘人数，如果大于招聘人数，所有人都进入考察范围
        List<HrRegistrationDetailsDTO> dtoList = investigateDetailsDTOS.stream().filter(ls -> ls.getFinalResult().compareTo(max) == 0).collect(Collectors.toList());
        if (dtoList.size() > hrRecruitmentStation.getRecruitmentPeopleNumber()) {
            dtoList.forEach(hrRegistrationDetailsDTO -> {
                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
            });
        } else {//小于招聘人数，判断哪些人进入等额考察，哪些人进入
            if (hrRecruitmentStation.getIsEqualInvestigate()) {
                //如果开启了等额考察，取出和招聘人数相等的人改为已进入等额考察，剩余的人为已进入考察范围
                List<HrRegistrationDetailsDTO> subList = investigateDetailsDTOS.subList(0, Math.min(investigateDetailsDTOS.size(), hrRecruitmentStation.getRecruitmentPeopleNumber()));
                subList.forEach(hrRegistrationDetailsDTO -> {
                    HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                    hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                    hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                });
                List<HrRegistrationDetailsDTO> list = investigateDetailsDTOS.stream().filter(d1 -> subList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(hrRegistrationDetailsDTO -> {
                        HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                        hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                        hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                    });
                }
            } else {
                investigateDetailsDTOS.forEach(hrRegistrationDetailsDTO -> {
                    HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                    hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                    hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                });
            }
        }
        List<HrRegistrationDetailsDTO> noInvestigationList = registrationDetailsDTOS.stream().filter(d1 -> investigateDetailsDTOS.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noInvestigationList)) {
            noInvestigationList.forEach(hrRegistrationDetailsDTO -> {
                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
            });
        }
    }
}
