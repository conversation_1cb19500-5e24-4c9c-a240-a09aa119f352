<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrNotificationMessageRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , notification_name, reminder_method, state,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrNotificationMessage">
        <id column="id" property="id"/>
        <result column="notification_name" property="notificationName"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="Page" resultType="cn.casair.dto.HrNotificationMessageDTO">
        SELECT
        any_value ( hm.id ) id,
        any_value ( hm.notification_name ) notificationName,
        any_value ( b.states ) states,
        any_value ( b.reminderMethod ) reminderMethod,
        any_value ( b.reminderContent ) reminderContent
        FROM
        hr_notification_message hm
        LEFT JOIN
        (
        SELECT
        any_value ( hnm.id ) id,
        any_value ( hnm.notification_name ) notificationName,
        any_value ( a.states ) states,
        any_value ( a.reminderMethod ) reminderMethod,
        any_value ( a.reminderContent ) reminderContent
        FROM
        (
        SELECT
        any_value ( hu.states ) states,
        any_value ( hu.reminder_method ) AS reminderMethod,
        any_value ( hu.notification_id ) notificationId,
        group_concat( hnc.reminder_content SEPARATOR '-' ) AS reminderContent
        FROM
        hr_notification_message_content hnc
        LEFT JOIN hr_notification_user hu ON hnc.id = hu.notification_content_id
        WHERE  hu.is_delete = 0

            AND  hu.user_id =#{param1.userId}

        <if test="param1.reminderMethodList!=null and param1.reminderMethodList.size() > 0">
            AND hu.reminder_method IN
            <foreach collection="param1.reminderMethodList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>

        GROUP BY
        hu.notification_id
        ) a
        LEFT JOIN hr_notification_message hnm ON hnm.id = a.notificationId
        AND hnm.is_delete = 0
        GROUP BY
        hnm.id
        ) b
      on hm.id=b .id

        <if test="param1.notificationName!=null and param1.notificationName!=''">
            where  hm.notification_name like concat('%',#{param1.notificationName},'%')
        </if>
        GROUP BY hm.id

        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </select>

    <select id="selectUserId" resultType="cn.casair.dto.HrNotificationMessageDTO">
        SELECT *
        FROM `hr_notification_user`
        WHERE user_id = #{id}
          AND is_delete = 0
    </select>

    <update id="updeteHrNotificationMessage">
        UPDATE hr_notification_user
        SET states =#{param1.states}
        WHERE notification_id = #{param1.id}
          and user_id = #{param1.userId}
    </update>
    <update id="updateNotificationUser">
        UPDATE hr_notification_user
        SET states                 =#{param1.states},
            notification_content_id=#{param1.notificationContentId},
            reminder_method=#{param1.reminderMethod}
        WHERE notification_id = #{param1.notificationId}
          and user_id = #{param1.userId}
    </update>

    <select id="selectNotificationMessageContent" resultType="cn.casair.dto.HrNotificationMessageDTO">
        SELECT reminder_content AS reminderContent,
               id               as notificationContentId
        FROM hr_notification_message_content
        WHERE notification_id = #{id}
          AND is_delete = 0
    </select>

    <select id="selectHrNotificationUser" resultType="cn.casair.dto.HrNotificationMessageDTO">
        SELECT any_value(id)                                       id,
               any_value(notification_id) AS                       notificationId,
               group_concat(notification_content_id SEPARATOR ',') reminderContent,
               any_value(user_id)         AS                       userId,
               any_value(reminder_method) AS                       reminderMethod,
               any_value(states)          AS                       states
        FROM hr_notification_user
        WHERE user_id = #{param1.userId}
          AND notification_id = #{param1.notificationId}
          AND is_delete = 0
        GROUP BY notification_id
    </select>
    <select id="getHrNotificationMessageSelectContent" resultType="cn.casair.dto.HrNotificationMessageDTO">
        SELECT id as notificationContentId, reminder_content as reminderContent
        from hr_notification_message_content
        where notification_id = #{id}
          and is_delete = 0
    </select>


    <select id="PageReminderMethod" resultType="cn.casair.dto.HrNotificationMessageDTO">
        SELECT
        hnm.notification_name AS notificationName,
        group_concat( hnmc.reminder_content SEPARATOR '-' ) reminderContent,
        any_value ( hnu.states ) states,
        any_value ( hnu.reminder_method ) reminderMethod,
        any_value ( hnm.id ) id
        FROM
        hr_notification_user hnu
        LEFT JOIN hr_notification_message_content hnmc ON hnmc.id = hnu.notification_content_id
        LEFT JOIN hr_notification_message hnm ON hnm.id = hnmc.notification_id
        WHERE hnm.is_delete=0 and hnu.is_delete=0
        <if test="param1.userId != null and param1.userId != ''">
            and hnu.user_id=#{param1.userId}
        </if>

        <if test="param1.reminderMethod!=null and param1.reminderMethod!=''">
            and hnu.reminder_method=#{param1.reminderMethod}
        </if>
        GROUP BY
        hnm.notification_name
    </select>

    <select id="getNotificationMessageId" resultType="java.lang.String">
        SELECT id
        FROM hr_notification_message_content
        WHERE reminder_value = #{reminderValue}
          AND notification_id = #{notificationMessageId}
    </select>

    <select id="getPageReminderMethod" resultType="cn.casair.dto.HrNotificationMessageDTO">

        SELECT
        any_value ( hnm.id ) id,
        any_value ( hnm.notification_name ) notificationName,
        any_value ( a.states ) states,
        any_value ( a.reminderMethod ) reminderMethod,
        any_value ( a.notificationId ) notificationId,
        any_value ( a.reminderContent ) reminderContent
        FROM
        (
        SELECT

        any_value ( hu.states ) states,
        any_value ( hu.reminder_method ) AS reminderMethod,
        any_value ( hu.notification_id ) notificationId,
        GROUP_CONCAT( hnc.reminder_content SEPARATOR '-' ) AS reminderContent
        FROM
        hr_notification_message_content hnc
        LEFT JOIN hr_notification_user hu ON hnc.id = hu.notification_content_id
        WHERE
        hu.is_delete = 0
        AND  hu.user_id =#{param1.userId}
        <if test="param1.reminderMethodList!=null and param1.reminderMethodList.size() > 0">
            AND hu.reminder_method IN
            <foreach collection="param1.reminderMethodList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        GROUP BY
        hu.notification_id
        ) a
        LEFT JOIN hr_notification_message hnm ON hnm.id = a.notificationId
        AND hnm.is_delete = 0
        GROUP BY
        hnm.id

        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </select>
</mapper>
