<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRegistrationOrderRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, info_id, out_trade_no, total_price, pay_status, pay_type, pay_money, pay_time,
is_delete                    ,created_date                    ,created_by                    ,last_modified_by                    ,last_modified_date
        </sql>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrRegistrationOrder">
                    <id column="id" property="id"/>
                    <result column="info_id" property="infoId"/>
                    <result column="out_trade_no" property="outTradeNo"/>
                    <result column="total_price" property="totalPrice"/>
                    <result column="pay_status" property="payStatus"/>
                    <result column="pay_type" property="payType"/>
                    <result column="pay_money" property="payMoney"/>
                    <result column="pay_time" property="payTime"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_date" property="createdDate"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

</mapper>
