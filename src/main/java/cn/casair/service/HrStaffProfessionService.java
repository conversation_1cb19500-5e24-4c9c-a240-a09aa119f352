package cn.casair.service;

import cn.casair.domain.HrStaffProfession;
import cn.casair.dto.HrStaffProfessionDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 专业技能服务类
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
public interface HrStaffProfessionService extends IService<HrStaffProfession> {


    /**
     * 创建专业技能
     * @param hrStaffProfessionDTO
     * @return
     */
    List<HrStaffProfessionDTO> createHrStaffProfession(HrStaffProfessionDTO hrStaffProfessionDTO);

    /**
     * 修改专业技能
     * @param hrStaffProfessionDTO
     * @return
     */
    Optional<List<HrStaffProfessionDTO>> updateHrStaffProfession(HrStaffProfessionDTO hrStaffProfessionDTO);

    /**
     * 查询专业技能详情
     * @param id
     * @return
     */
    HrStaffProfessionDTO getHrStaffProfession(String id);

    /**
     * 删除专业技能
     * @param id
     */
    List<HrStaffProfessionDTO> deleteHrStaffProfession(String id);

    /**
     * 查询专业技能
     * @param staffId 员工ID
     * @return
     */
    List<HrStaffProfessionDTO> findProfessionList(String staffId);

    void setDict(HrStaffProfessionDTO dto);
}
