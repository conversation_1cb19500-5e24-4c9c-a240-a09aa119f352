package cn.casair.service.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrLendingApplyTpyeRelation;
import cn.casair.dto.HrLendingApplyTpyeRelationDTO;
import cn.casair.repository.HrLendingApplyTpyeRelationRepository;
import cn.casair.mapper.HrLendingApplyTpyeRelationMapper;
import cn.casair.service.HrLendingApplyTpyeRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;
/**
 * 借阅申请表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrLendingApplyTpyeRelationServiceImpl extends ServiceImpl<HrLendingApplyTpyeRelationRepository, HrLendingApplyTpyeRelation>implements HrLendingApplyTpyeRelationService {


    private final HrLendingApplyTpyeRelationRepository hrLendingApplyTpyeRelationRepository;
    private final HrLendingApplyTpyeRelationMapper hrLendingApplyTpyeRelationMapper;

    public HrLendingApplyTpyeRelationServiceImpl(HrLendingApplyTpyeRelationRepository hrLendingApplyTpyeRelationRepository, HrLendingApplyTpyeRelationMapper hrLendingApplyTpyeRelationMapper){
    this.hrLendingApplyTpyeRelationRepository = hrLendingApplyTpyeRelationRepository;
    this.hrLendingApplyTpyeRelationMapper= hrLendingApplyTpyeRelationMapper;
    }

    /**
     * 创建借阅申请表
     * @param hrLendingApplyTpyeRelationDTO
     * @return
     */
    @Override
    public HrLendingApplyTpyeRelationDTO createHrLendingApplyTpyeRelation(HrLendingApplyTpyeRelationDTO hrLendingApplyTpyeRelationDTO){
    log.info("Create new HrLendingApplyTpyeRelation:{}", hrLendingApplyTpyeRelationDTO);

    HrLendingApplyTpyeRelation hrLendingApplyTpyeRelation =this.hrLendingApplyTpyeRelationMapper.toEntity(hrLendingApplyTpyeRelationDTO);
    this.hrLendingApplyTpyeRelationRepository.insert(hrLendingApplyTpyeRelation);
    return this.hrLendingApplyTpyeRelationMapper.toDto(hrLendingApplyTpyeRelation);
    }

    /**
     * 修改借阅申请表
     * @param hrLendingApplyTpyeRelationDTO
     * @return
     */
    @Override
    public Optional<HrLendingApplyTpyeRelationDTO>updateHrLendingApplyTpyeRelation(HrLendingApplyTpyeRelationDTO hrLendingApplyTpyeRelationDTO){
    return Optional.ofNullable(this.hrLendingApplyTpyeRelationRepository.selectById(hrLendingApplyTpyeRelationDTO.getId()))
    .map(roleTemp->{
    HrLendingApplyTpyeRelation hrLendingApplyTpyeRelation =this.hrLendingApplyTpyeRelationMapper.toEntity(hrLendingApplyTpyeRelationDTO);
    this.hrLendingApplyTpyeRelationRepository.updateById(hrLendingApplyTpyeRelation);
    log.info("Update HrLendingApplyTpyeRelation:{}", hrLendingApplyTpyeRelationDTO);
    return hrLendingApplyTpyeRelationDTO;
    });
    }

    /**
     * 查询借阅申请表详情
     * @param id
     * @return
     */
    @Override
    public HrLendingApplyTpyeRelationDTO getHrLendingApplyTpyeRelation(String id){
    log.info("Get HrLendingApplyTpyeRelation :{}",id);

    HrLendingApplyTpyeRelation hrLendingApplyTpyeRelation =this.hrLendingApplyTpyeRelationRepository.selectById(id);
    return this.hrLendingApplyTpyeRelationMapper.toDto(hrLendingApplyTpyeRelation);
    }

    /**
     * 删除借阅申请表
     * @param id
     */
    @Override
    public void deleteHrLendingApplyTpyeRelation(String id){
    Optional.ofNullable(this.hrLendingApplyTpyeRelationRepository.selectById(id))
    .ifPresent(hrLendingApplyTpyeRelation ->{
    this.hrLendingApplyTpyeRelationRepository.deleteById(id);
    log.info("Delete HrLendingApplyTpyeRelation:{}", hrLendingApplyTpyeRelation);
    });
    }

    /**
     * 批量删除借阅申请表
     * @param ids
     */
    @Override
    public void deleteHrLendingApplyTpyeRelation(List<String>ids){
    log.info("Delete HrLendingApplyTpyeRelations:{}",ids);
    this.hrLendingApplyTpyeRelationRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询借阅申请表
     * @param hrLendingApplyTpyeRelationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrLendingApplyTpyeRelationDTO hrLendingApplyTpyeRelationDTO,Long pageNumber,Long pageSize){
    Page<HrLendingApplyTpyeRelation>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrLendingApplyTpyeRelation>qw=new QueryWrapper<>(this.hrLendingApplyTpyeRelationMapper.toEntity(hrLendingApplyTpyeRelationDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrLendingApplyTpyeRelationRepository.selectPage(page,qw);
    iPage.setRecords(this.hrLendingApplyTpyeRelationMapper.toDto(iPage.getRecords()));
    return iPage;
    }
}
