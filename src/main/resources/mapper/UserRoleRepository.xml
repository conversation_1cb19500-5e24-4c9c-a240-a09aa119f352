<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.UserRoleRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, role_id, is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.UserRole">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="role_id" property="roleId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectUserRoleListForKeys" resultMap="BaseResultMap">
        SELECT sur.*
        FROM sys_user_role sur
        LEFT JOIN sys_role sr ON sur.role_id = sr.id
        <where>
            <if test="roleKeys != null">
                sr.role_key IN
                <foreach collection="roleKeys" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
            AND sur.is_delete = 0
        </where>
    </select>

</mapper>
