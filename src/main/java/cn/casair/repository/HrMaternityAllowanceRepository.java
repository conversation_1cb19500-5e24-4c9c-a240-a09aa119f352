package cn.casair.repository;

import cn.casair.domain.HrMaternityAllowance;
import cn.casair.dto.HrMaternityAllowanceDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 生育津贴数据库操作类
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
@Repository
public interface HrMaternityAllowanceRepository extends BaseMapper<HrMaternityAllowance> {

    IPage<HrMaternityAllowanceDTO> findPage(Page<HrMaternityAllowance> page, @Param("param") HrMaternityAllowanceDTO hrMaternityAllowanceDTO, @Param("permissionClient") List<String> clientIds);

    List<HrMaternityAllowanceDTO> findList(@Param("param") HrMaternityAllowanceDTO hrMaternityAllowanceDTO,@Param("permissionClient") List<String> clientIds);

    HrMaternityAllowanceDTO findById(@Param("id") String id);

}
