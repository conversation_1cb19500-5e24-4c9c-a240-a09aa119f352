package cn.casair.service;
import cn.casair.domain.HrLendingApplyTpyeRelation;
import cn.casair.dto.HrLendingApplyTpyeRelationDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 借阅申请表服务类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
public interface HrLendingApplyTpyeRelationService extends IService<HrLendingApplyTpyeRelation> {


    /**
     * 创建借阅申请表
     * @param hrLendingApplyTpyeRelationDTO
     * @return
     */
    HrLendingApplyTpyeRelationDTO createHrLendingApplyTpyeRelation(HrLendingApplyTpyeRelationDTO hrLendingApplyTpyeRelationDTO);

    /**
     * 修改借阅申请表
     * @param hrLendingApplyTpyeRelationDTO
     * @return
     */
    Optional<HrLendingApplyTpyeRelationDTO> updateHrLendingApplyTpyeRelation(HrLendingApplyTpyeRelationDTO hrLendingApplyTpyeRelationDTO);

    /**
     * 查询借阅申请表详情
     * @param id
     * @return
     */
    HrLendingApplyTpyeRelationDTO getHrLendingApplyTpyeRelation(String id);

    /**
     * 删除借阅申请表
     * @param id
     */
    void deleteHrLendingApplyTpyeRelation(String id);

    /**
     * 批量删除借阅申请表
     * @param ids
     */
    void deleteHrLendingApplyTpyeRelation(List<String> ids);

    /**
     * 分页查询借阅申请表
     * @param hrLendingApplyTpyeRelationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrLendingApplyTpyeRelationDTO hrLendingApplyTpyeRelationDTO,Long pageNumber,Long pageSize);
    }
