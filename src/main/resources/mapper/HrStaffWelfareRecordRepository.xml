<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffWelfareRecordRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        staff_id,
        pay_year,
        pay_monthly,
        social_security_cardinal_base,
        medical_insurance_cardinal_base,
        social_security_cardinal_base_personal,
        medical_insurance_cardinal_base_personal,
        personal_pension_cardinal_base,
        personal_unemployment_cardinal_base,
        personal_maternity_cardinal_base,
        unit_pension_cardinal_base,
        unit_unemployment_cardinal_base,
        work_injury_cardinal_base,
        unit_maternity_cardinal_base,
        accumulation_fund_cardinal_base,
        unit_large_medical_expense_base,
        replenish_work_injury_expense_base,
        personal_large_medical_expense_base,
        unit_pension_scale,
        unit_medical_scale,
        work_injury_scale,
        unit_unemployment_scale,
        unit_maternity_scale,
        personal_pension_scale,
        personal_medical_scale,
        personal_unemployment_scale,
        personal_maternity_scale,
        unit_accumulation_fund_scale,
        personal_accumulation_fund_scale,
        is_delete,
        created_by,
        created_date,
        last_modified_by,
        last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffWelfareRecord">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="social_security_cardinal_base" property="socialSecurityCardinalBase"/>
        <result column="medical_insurance_cardinal_base" property="medicalInsuranceCardinalBase"/>
        <result column="social_security_cardinal_base_personal" property="socialSecurityCardinalBasePersonal"/>
        <result column="medical_insurance_cardinal_base_personal" property="medicalInsuranceCardinalBasePersonal"/>
        <result column="accumulation_fund_cardinal_base" property="accumulationFundCardinalBase"/>
        <result column="unit_pension_scale" property="unitPensionScale"/>
        <result column="unit_medical_scale" property="unitMedicalScale"/>
        <result column="work_injury_scale" property="workInjuryScale"/>
        <result column="unit_unemployment_scale" property="unitUnemploymentScale"/>
        <result column="personal_pension_scale" property="personalPensionScale"/>
        <result column="personal_medical_scale" property="personalMedicalScale"/>
        <result column="personal_unemployment_scale" property="personalUnemploymentScale"/>
        <result column="unit_accumulation_fund_scale" property="unitAccumulationFundScale"/>
        <result column="personal_accumulation_fund_scale" property="personalAccumulationFundScale"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByObject" resultType="cn.casair.domain.HrStaffWelfareRecord">
        SELECT
            *
        FROM hr_staff_welfare_record
        WHERE is_delete = 0
            AND staff_id = #{query.staffId}
            <if test="query.payYear!=null">
                AND pay_year = #{query.payYear}
            </if>
            <if test="query.payMonthly!=null">
                AND pay_monthly = #{query.payMonthly}
            </if>
    </select>

</mapper>
