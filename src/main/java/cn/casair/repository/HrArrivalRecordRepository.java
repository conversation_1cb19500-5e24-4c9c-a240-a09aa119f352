package cn.casair.repository;

import cn.casair.domain.HrArrivalRecord;
import cn.casair.dto.HrArrivalRecordDTO;
import cn.casair.dto.HrBillTotalDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 到账记录数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Repository
public interface HrArrivalRecordRepository extends BaseMapper<HrArrivalRecord> {

    /**
     * 查询收入
     *
     * @param hrBillTotalDTO
     * @param clientIds
     * @return java.util.List<cn.casair.domain.HrArrivalRecord>
     * <AUTHOR>
     * @date 2021/11/23
     **/
    List<HrArrivalRecordDTO> selectRevenue(@Param("params") HrBillTotalDTO hrBillTotalDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 查询客户缴费年月到账信息
     *
     * @param hrArrivalRecordDTO
     * @return cn.casair.dto.HrArrivalRecordDTO
     * <AUTHOR>
     * @date 2021/11/16
     **/
    HrArrivalRecordDTO selectByEntity(@Param("params") HrArrivalRecordDTO hrArrivalRecordDTO);

    /**
     * 根据id查询到账记录详情
     *
     * @return cn.casair.dto.HrArrivalRecordDTO
     * <AUTHOR>
     * @date 2021/11/16
     **/
    HrArrivalRecordDTO getDetailById(@Param("id") String id);

    /**
     * 查询到账记录分页列表
     *
     * @param page
     * @param hrArrivalRecordDTO
     * @param clientIds
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrArrivalRecordDTO>
     * <AUTHOR>
     * @date 2021/11/16
     **/
    IPage<HrArrivalRecordDTO> selectPageList(Page<HrArrivalRecord> page, @Param("params") HrArrivalRecordDTO hrArrivalRecordDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 更新到账总金额（减）
     *
     * @param arrivalId
     * @param arrivalAmount
     * @return int
     * <AUTHOR>
     * @date 2021/11/18
     **/
    int subtractionTotalArrivalAmount(@Param("arrivalId") String arrivalId, @Param("arrivalAmount") BigDecimal arrivalAmount);

    /**
     * 更新账单总金额（加）
     *
     * @param arrivalId
     * @param arrivalAmount
     * @return int
     * <AUTHOR>
     * @date 2021/11/18
     **/
    int additionTotalArrivalAmount(@Param("arrivalId") String arrivalId, @Param("arrivalAmount") BigDecimal arrivalAmount);

    /**
     * 不分页查询到账记录
     * @param hrArrivalRecordDTO
     * @param clientIds
     * @return
     */
    List<HrArrivalRecordDTO> findList(@Param("params") HrArrivalRecordDTO hrArrivalRecordDTO, @Param("permissionClient") List<String> clientIds);

}
