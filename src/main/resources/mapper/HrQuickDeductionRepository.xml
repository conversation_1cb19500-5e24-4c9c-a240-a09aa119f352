<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrQuickDeductionRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, max_pay_taxes, min_pay_taxes, tax_rate, quick_deduction_type, quick_deduction_number,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrQuickDeduction">
                    <id column="id" property="id"/>
                    <result column="max_pay_taxes" property="maxPayTaxes"/>
                    <result column="min_pay_taxes" property="minPayTaxes"/>
                    <result column="tax_rate" property="taxRate"/>
                    <result column="quick_deduction_type" property="quickDeductionType"/>
                    <result column="quick_deduction_number" property="quickDeductionNumber"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

        <select id="selectBatch" resultType="cn.casair.dto.excel.HrQuickDeductionExport">
            select * from hr_quick_deduction where id in
            <foreach collection="ids" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
            ORDER BY quick_deduction_series
    </select>

    <select id="selectAll" resultType="cn.casair.domain.HrQuickDeduction">
        SELECT
            *
        FROM
            `hr_quick_deduction`
        WHERE
            is_delete = 0
        ORDER BY
            quick_deduction_series
    </select>
</mapper>
