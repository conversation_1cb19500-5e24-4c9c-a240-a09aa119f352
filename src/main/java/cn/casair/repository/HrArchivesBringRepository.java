package cn.casair.repository;

import cn.casair.domain.ArchivesBringDetail;
import cn.casair.domain.HrArchivesBring;
import cn.casair.dto.HrArchivesBringDTO;
import cn.casair.dto.excel.HrArchivesBringExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 档案调入调出表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Repository
public interface HrArchivesBringRepository extends BaseMapper<HrArchivesBring> {

    /**
     * 查询列表
     *
     * @param ids
     * @return java.util.List<cn.casair.dto.HrArchivesBringDTO>
     * <AUTHOR>
     * @date 2022/2/10
     **/
    List<HrArchivesBringDTO> getByIds(@Param("ids") List<String> ids);

    /**
     * 获取档案变更记录详情
     *
     * @param id
     * @return cn.casair.dto.HrArchivesBringDTO
     * <AUTHOR>
     * @date 2021/10/18
     **/
    HrArchivesBringDTO getHrArchivesBringById(String id);

    /**
     * 添加档案明细变更记录关联
     *
     * @param archivesBringDetail
     * @return int
     * <AUTHOR>
     * @date 2021/10/14
     **/
    int insertArchivesBringDetail(ArchivesBringDetail archivesBringDetail);

    /**
     * 分页查询档案变更记录
     *
     * @param page
     * @param hrArchivesBringDTO
     * @param clientIds
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrArchivesBringDTO>
     * <AUTHOR>
     * @date 2021/10/14
     **/
    IPage<HrArchivesBringDTO> findPage(Page<HrArchivesBring> page, @Param("params") HrArchivesBringDTO hrArchivesBringDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 根据变更记录id获取完整的变更记录信息
     *
     * @param id
     * @return cn.casair.dto.HrArchivesBringDTO
     * <AUTHOR>
     * @date 2021/10/14
     **/
    HrArchivesBringDTO getCompleteById(String id);

    /**
     * 根据变更记录ids删除关联表记录
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @date 2021/10/14
     **/
    int deleteBringDetailByBringIds(@Param("ids") List<String> ids);

    /**
     * 获取变更记录导出列表
     *
     * @param hrArchivesBringDTO
     * @param clientIds
     * @return java.util.List<cn.casair.dto.excel.HrArchivesBringExport>
     * <AUTHOR>
     * @date 2021/10/14
     **/
    List<HrArchivesBringExport> getExportList(@Param("params") HrArchivesBringDTO hrArchivesBringDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 根据档案id删除调入调出
     *
     * @param archivesIds
     * @return int
     * <AUTHOR>
     * @date 2021/11/18
     **/
    int deleteBatchByArchivesId(@Param("archivesIds") List<String> archivesIds);
}
