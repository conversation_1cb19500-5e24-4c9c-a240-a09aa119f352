package cn.casair.service;

import cn.casair.domain.HrArrivalRecord;
import cn.casair.dto.HrArrivalRecordDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 到账记录服务类
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
public interface HrArrivalRecordService extends IService<HrArrivalRecord> {


    /**
     * 创建到账记录
     *
     * @param hrArrivalRecordDTO
     * @return
     */
    HrArrivalRecordDTO createHrArrivalRecord(HrArrivalRecordDTO hrArrivalRecordDTO);

    /**
     * 修改到账记录
     *
     * @param hrArrivalRecordDTO
     * @return
     */
    Optional<HrArrivalRecordDTO> updateHrArrivalRecord(HrArrivalRecordDTO hrArrivalRecordDTO);
    /**
     * 修改到账记录
     * 并同步NC
     * @param hrArrivalRecordDTO
     * @return
     */
    Optional<HrArrivalRecordDTO> updateHrArrivalRecordWithNC(HrArrivalRecordDTO hrArrivalRecordDTO);

    /**
     * 查询到账记录详情
     *
     * @param id
     * @return
     */
    HrArrivalRecordDTO getHrArrivalRecord(String id);

    /**
     * 删除到账记录
     *
     * @param id
     */
    void deleteHrArrivalRecord(String id);

    /**
     * 批量删除到账记录
     *
     * @param ids
     */
    void deleteHrArrivalRecord(List<String> ids);

    /**
     * 分页查询到账记录
     *
     * @param hrArrivalRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrArrivalRecordDTO> findPage(HrArrivalRecordDTO hrArrivalRecordDTO, Long pageNumber, Long pageSize);

    /**
     * 获取客户缴费年月账单应收金额
     *
     * @param hrArrivalRecordDTO
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2021/11/16
     **/
    BigDecimal getReceivableAmount(HrArrivalRecordDTO hrArrivalRecordDTO);

    /**
     * 到账记录列表数据汇总
     * @param hrArrivalRecordDTO
     * @return
     */
    HrArrivalRecordDTO findArrivalRecordTotal(HrArrivalRecordDTO hrArrivalRecordDTO);

    /**
     * 导出到账记录
     * @param hrArrivalRecordDTO
     * @return
     */
    String export(HrArrivalRecordDTO hrArrivalRecordDTO);

}
