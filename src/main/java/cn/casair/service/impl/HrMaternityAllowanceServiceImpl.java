package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.enums.UserRoleTypeEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.domain.HrFertility;
import cn.casair.domain.HrMaternityAllowance;
import cn.casair.dto.HrAppendixDTO;
import cn.casair.dto.HrApplyOpLogsDTO;
import cn.casair.dto.HrMaternityAllowanceDTO;
import cn.casair.dto.JWTMiniDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.mapper.HrMaternityAllowanceMapper;
import cn.casair.repository.HrFertilityRepository;
import cn.casair.repository.HrMaternityAllowanceRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrApplyOpLogsService;
import cn.casair.service.HrClientService;
import cn.casair.service.HrMaternityAllowanceService;
import cn.casair.service.HrNotificationUserService;
import cn.casair.service.HrUpcomingService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 生育津贴服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrMaternityAllowanceServiceImpl extends ServiceImpl<HrMaternityAllowanceRepository, HrMaternityAllowance> implements HrMaternityAllowanceService {

    private final HrMaternityAllowanceRepository hrMaternityAllowanceRepository;
    private final HrMaternityAllowanceMapper hrMaternityAllowanceMapper;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrUpcomingService hrUpcomingService;
    private final SysOperLogService sysOperLogService;
    private final HrClientService hrClientService;
    private final HrFertilityRepository hrFertilityRepository;
    private final HrAppendixService hrAppendixService;
    private final HrNotificationUserService hrNotificationUserService;


    /**
     * 创建生育津贴
     *
     * @param hrMaternityAllowanceDTO
     * @return
     */
    @Override
    public HrMaternityAllowanceDTO createHrMaternityAllowance(HrMaternityAllowanceDTO hrMaternityAllowanceDTO) {
        log.info("Create new HrMaternityAllowance:{}", hrMaternityAllowanceDTO);
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        hrMaternityAllowanceDTO.setState(ServiceCenterEnum.MaternityAllowanceStateEnum.INITIATE_APPLICATION.getKey());
        hrMaternityAllowanceDTO.setLastModifiedDate(LocalDateTime.now());
        if (CollectionUtils.isNotEmpty(hrMaternityAllowanceDTO.getBirthAppendixIds())) {
            hrMaternityAllowanceDTO.setBirthAppendixes(String.join(",", hrMaternityAllowanceDTO.getBirthAppendixIds()));
        }
        if (CollectionUtils.isNotEmpty(hrMaternityAllowanceDTO.getLeaveHospitalAppendixIds())) {
            hrMaternityAllowanceDTO.setLeaveHospitalAppendixes(String.join(",", hrMaternityAllowanceDTO.getLeaveHospitalAppendixIds()));
        }
        if (CollectionUtils.isNotEmpty(hrMaternityAllowanceDTO.getFertilityAppendixIds())) {
            hrMaternityAllowanceDTO.setFertilityAppendixes(String.join(",", hrMaternityAllowanceDTO.getFertilityAppendixIds()));
        }
        hrMaternityAllowanceDTO.setStaffId(jwtMiniDTO.getId());
        hrMaternityAllowanceDTO.setClientId(jwtMiniDTO.getClientId());
        HrMaternityAllowance hrMaternityAllowance = this.hrMaternityAllowanceMapper.toEntity(hrMaternityAllowanceDTO);
        this.hrMaternityAllowanceRepository.insert(hrMaternityAllowance);
        HrMaternityAllowanceDTO toDto = this.hrMaternityAllowanceMapper.toDto(hrMaternityAllowance);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("state", ServiceCenterEnum.MaternityAllowanceStateEnum.INITIATE_APPLICATION.getKey());
        jsonObject.put("message", "生育津贴申请已提交");
        String message = jwtMiniDTO.getName() + "发起了生育津贴申请。";
        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(toDto.getId(), null, jwtMiniDTO.getId(), message + "####" + jsonObject.toJSONString(), null, true, null, ServiceCenterEnum.MATERNITY_ALLOWANCE.getKey());
        hrUpcomingService.createServiceUpcoming(toDto.getId(), jwtMiniDTO.getId(), "生育津贴管理-审核" + jwtMiniDTO.getName() + "的生育津贴申请", LocalDate.now(), 4);
        this.hrNotificationUserService.saveRemindContent(jwtMiniDTO.getClientId(), ServiceCenterEnum.MATERNITY_ALLOWANCE.getKey(),
            ServiceCenterEnum.MaternityAllowanceStateEnum.INITIATE_APPLICATION.getKey(), jwtMiniDTO.getName() + "发起了生育津贴申请", jwtMiniDTO.getId());
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.MATERNITY_ALLOWANCE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrMaternityAllowanceDTO),
            HrMaternityAllowanceDTO.class,
            null,
            JSON.toJSONString(toDto)
        );
        return toDto;
    }

    /**
     * 修改生育津贴
     *
     * @param hrMaternityAllowanceDTO
     * @return
     */
    @Override
    public Optional<HrMaternityAllowanceDTO> updateHrMaternityAllowance(HrMaternityAllowanceDTO hrMaternityAllowanceDTO) {
        return Optional.ofNullable(this.hrMaternityAllowanceRepository.selectById(hrMaternityAllowanceDTO.getId()))
            .map(roleTemp -> {
                HrMaternityAllowance hrMaternityAllowance = this.hrMaternityAllowanceMapper.toEntity(hrMaternityAllowanceDTO);
                this.hrMaternityAllowanceRepository.updateById(hrMaternityAllowance);
                log.info("Update HrMaternityAllowance:{}", hrMaternityAllowanceDTO);
                return hrMaternityAllowanceDTO;
            });
    }

    /**
     * 查询生育津贴详情
     *
     * @param id
     * @return
     */
    @Override
    public HrMaternityAllowanceDTO getHrMaternityAllowance(String id) {
        log.info("Get HrMaternityAllowance :{}", id);

        HrMaternityAllowanceDTO hrMaternityAllowanceDTO = this.hrMaternityAllowanceRepository.findById(id);
        //获取最新的生育服务信息
        HrFertility hrFertility = hrFertilityRepository.selectOne(new QueryWrapper<HrFertility>()
            .eq("staff_id", hrMaternityAllowanceDTO.getStaffId()).orderByDesc("created_date").last("LIMIT 1"));
        if (hrFertility != null) {
            hrMaternityAllowanceDTO.setMaternityLeaveStartDate(hrFertility.getMaternityLeaveStartDate());
            hrMaternityAllowanceDTO.setMaternityLeaveEndDate(hrFertility.getMaternityLeaveEndDate());
        }
        if (hrMaternityAllowanceDTO.getBirthAppendixes() != null) {
            List<String> ids = Arrays.asList(hrMaternityAllowanceDTO.getBirthAppendixes().split(","));
            List<HrAppendixDTO> hrAppendixDTOList = hrAppendixService.getHrAppendixListByIds(ids);
            hrMaternityAllowanceDTO.setBirthAppendixList(hrAppendixDTOList);
        }
        if (hrMaternityAllowanceDTO.getLeaveHospitalAppendixes() != null) {
            List<String> ids = Arrays.asList(hrMaternityAllowanceDTO.getLeaveHospitalAppendixes().split(","));
            List<HrAppendixDTO> hrAppendixDTOList = hrAppendixService.getHrAppendixListByIds(ids);
            hrMaternityAllowanceDTO.setLeaveHospitalAppendixList(hrAppendixDTOList);
        }
        if (hrMaternityAllowanceDTO.getFertilityAppendixes() != null) {
            List<String> ids = Arrays.asList(hrMaternityAllowanceDTO.getFertilityAppendixes().split(","));
            List<HrAppendixDTO> hrAppendixDTOList = hrAppendixService.getHrAppendixListByIds(ids);
            hrMaternityAllowanceDTO.setFertilityAppendixList(hrAppendixDTOList);
        }
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(id, null);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)) {
            hrMaternityAllowanceDTO.setApplyOpLogsList(applyOpLogsList);
        }
        return hrMaternityAllowanceDTO;
    }

    /**
     * 删除生育津贴
     *
     * @param id
     */
    @Override
    public void deleteHrMaternityAllowance(String id) {
        Optional.ofNullable(this.hrMaternityAllowanceRepository.selectById(id))
            .ifPresent(hrMaternityAllowance -> {
                this.hrMaternityAllowanceRepository.deleteById(id);
                log.info("Delete HrMaternityAllowance:{}", hrMaternityAllowance);
            });
    }

    /**
     * 批量删除生育津贴
     *
     * @param ids
     */
    @Override
    public void deleteHrMaternityAllowance(List<String> ids) {
        log.info("Delete HrMaternityAllowances:{}", ids);
        this.hrMaternityAllowanceRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询生育津贴
     *
     * @param hrMaternityAllowanceDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrMaternityAllowanceDTO hrMaternityAllowanceDTO, Long pageNumber, Long pageSize) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();

        Page<HrMaternityAllowance> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())) {
            hrMaternityAllowanceDTO.setState(ServiceCenterEnum.MaternityAllowanceStateEnum.COMPLETE.getKey());
        }
        IPage<HrMaternityAllowanceDTO> iPage = this.hrMaternityAllowanceRepository.findPage(page, hrMaternityAllowanceDTO, clientIds);
        return iPage;
    }

    @Override
    public String export(HrMaternityAllowanceDTO hrMaternityAllowanceDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())) {
            hrMaternityAllowanceDTO.setState(ServiceCenterEnum.MaternityAllowanceStateEnum.COMPLETE.getKey());
        }
        List<HrMaternityAllowanceDTO> list = this.hrMaternityAllowanceRepository.findList(hrMaternityAllowanceDTO, clientIds);
        List<String> ids = list.stream().map(HrMaternityAllowanceDTO::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(list, ModuleTypeEnum.MATERNITY_ALLOWANCE.getValue(), HrMaternityAllowanceDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.MATERNITY_ALLOWANCE.getValue(), BusinessTypeEnum.EXPORT.getKey(),
            JSON.toJSONString(ids), ids.size(), fileUrl);
        return fileUrl;
    }

    /**
     * 批量审核
     *
     * @param hrMaternityAllowanceDTO
     */
    @Override
    public void batchApproval(HrMaternityAllowanceDTO hrMaternityAllowanceDTO) {
        if (!hrMaternityAllowanceDTO.getOpt() && hrMaternityAllowanceDTO.getCheckerReason() == null) {
            throw new CommonException("请先填写拒绝原因！");
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrMaternityAllowanceDTO> hrMaternityAllowances = hrMaternityAllowanceRepository.findList(hrMaternityAllowanceDTO, null);
        for (HrMaternityAllowanceDTO hrMaternityAllowance : hrMaternityAllowances) {
            if (!hrMaternityAllowance.getState().equals(ServiceCenterEnum.MaternityAllowanceStateEnum.INITIATE_APPLICATION.getKey())) {
                throw new CommonException("审核的数据存在不需要审核的数据！");
            }
            String message = "";
            JSONObject jsonObject = new JSONObject();
            if (hrMaternityAllowanceDTO.getOpt()) {
                hrMaternityAllowance.setState(ServiceCenterEnum.MaternityAllowanceStateEnum.CONFIRM.getKey());
                jsonObject.put("state", ServiceCenterEnum.MaternityAllowanceStateEnum.CONFIRM.getKey());
                jsonObject.put("message", "您的申请已通过，请注意接收短信通知");
                message = jwtUserDTO.getRealName() + "审核通过了" + hrMaternityAllowance.getName() + "的生育津贴申请。";
                //todo 短信通知
                hrUpcomingService.updateUpcoming(hrMaternityAllowance.getId());
            } else {
                hrMaternityAllowance.setState(ServiceCenterEnum.MaternityAllowanceStateEnum.REVIEW_FAILED.getKey());
                jsonObject.put("state", ServiceCenterEnum.MaternityAllowanceStateEnum.REVIEW_FAILED.getKey());
                jsonObject.put("message", "您的申请未通过");
                jsonObject.put("checkerReason", hrMaternityAllowanceDTO.getCheckerReason());
                message = jwtUserDTO.getRealName() + "拒绝了" + hrMaternityAllowance.getName() + "的生育津贴申请。拒绝理由:" + hrMaternityAllowanceDTO.getCheckerReason();
                hrUpcomingService.createServiceUpcoming(hrMaternityAllowance.getId(), hrMaternityAllowance.getStaffId(), "生育津贴管理-确认" + hrMaternityAllowance.getName() + "的生育津贴申请", LocalDate.now(), 4);
            }
            HrMaternityAllowance entity = hrMaternityAllowanceMapper.toEntity(hrMaternityAllowance);
            hrMaternityAllowanceRepository.updateById(entity);
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrMaternityAllowance.getId(), null, jwtUserDTO.getId(), message + "####" + jsonObject.toJSONString(), hrMaternityAllowanceDTO.getRemark(), false, null, ServiceCenterEnum.MATERNITY_ALLOWANCE.getKey());
            this.hrNotificationUserService.saveRemindContent(hrMaternityAllowance.getClientId(), ServiceCenterEnum.MATERNITY_ALLOWANCE.getKey(), ServiceCenterEnum.MaternityAllowanceStateEnum.CONFIRM.getKey(), message, jwtUserDTO.getId());
        }
    }

    /**
     * 确认生育津贴
     *
     * @param hrMaternityAllowanceDTO
     */
    @Override
    public void confirmMaternityAllowance(HrMaternityAllowanceDTO hrMaternityAllowanceDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrMaternityAllowanceDTO hrMaternityAllowance = hrMaternityAllowanceRepository.findById(hrMaternityAllowanceDTO.getId());
        if (hrMaternityAllowance.getRemark() != null) {
            hrMaternityAllowance.setRemark(null);
        }
        hrMaternityAllowance.setState(ServiceCenterEnum.MaternityAllowanceStateEnum.COMPLETE.getKey())
            .setFertilityAmount(hrMaternityAllowanceDTO.getFertilityAmount())
            .setGrantStartDate(hrMaternityAllowanceDTO.getGrantStartDate())
            .setGrantEndDate(hrMaternityAllowanceDTO.getGrantEndDate());
        HrMaternityAllowance entity = hrMaternityAllowanceMapper.toEntity(hrMaternityAllowance);
        hrMaternityAllowanceRepository.updateById(entity);
        hrUpcomingService.updateUpcoming(hrMaternityAllowance.getId());
        String message = jwtUserDTO.getRealName() + "确认了" + hrMaternityAllowance.getName() + "的生育津贴申请。";
        this.hrNotificationUserService.saveRemindContent(hrMaternityAllowance.getClientId(), ServiceCenterEnum.MATERNITY_ALLOWANCE.getKey(), ServiceCenterEnum.MaternityAllowanceStateEnum.COMPLETE.getKey(), message, jwtUserDTO.getId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("state", ServiceCenterEnum.MaternityAllowanceStateEnum.COMPLETE.getKey());
        jsonObject.put("message", "您的申请已办理完成");
        String msg = message + "金额为：" + (hrMaternityAllowanceDTO.getFertilityAmount() == null ? "" : hrMaternityAllowanceDTO.getFertilityAmount() + "元/天。")
            + "日期为：" + (hrMaternityAllowanceDTO.getGrantDateList() == null || hrMaternityAllowanceDTO.getGrantDateList().isEmpty() ? "" : hrMaternityAllowanceDTO.getGrantStartDate() + "-" + hrMaternityAllowanceDTO.getGrantEndDate());
        String appendix = null;
        if (CollectionUtils.isNotEmpty(hrMaternityAllowanceDTO.getAppendixIdList())) {
            appendix = String.join(",", hrMaternityAllowanceDTO.getAppendixIdList());
        }
        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrMaternityAllowance.getId(), null, jwtUserDTO.getId(), msg + "####" + jsonObject.toJSONString(), hrMaternityAllowanceDTO.getRemark(), false, appendix, ServiceCenterEnum.MATERNITY_ALLOWANCE.getKey());
    }
}
