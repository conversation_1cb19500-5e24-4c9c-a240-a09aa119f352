package cn.casair.service.component.asynchronous;

import cn.casair.common.enums.RecruitmentBrochure;
import cn.casair.common.utils.FileUtil;
import cn.casair.common.utils.ImgUtils;
import cn.casair.common.utils.StringUtil;
import cn.casair.common.utils.WordUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrRegistrationDetailsExport;
import cn.casair.repository.*;
import cn.casair.service.HrAppendixService;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 招聘简章
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/13 9:57
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HrRecruitmentBrochureComponent {

    private final HrAppendixService hrAppendixService;
    private final HrRecruitmentStationRepository hrRecruitmentStationRepository;
    private final HrRecruitmentBulletinRepository hrRecruitmentBulletinRepository;
    private final HrRegistrationDetailsRepository hrRegistrationDetailsRepository;
    private final HrRegistrationInfoRepository hrRegistrationInfoRepository;
    private final HrContentTemplateRepository hrContentTemplateRepository;

    /**
     * 异步处理招聘简章导出
     *
     * @param hrRecruitmentBrochureDTO
     * @param tempRootPath             临时文件根目录
     * @return
     */
    @Async
    public Future<String> dealHrRecruitmentBrochureExport(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO, String tempRootPath) {
        String message;
        try {
            List<HrRecruitmentStationDTO> hrRecruitmentStationDTOList = this.hrRecruitmentStationRepository.selectByServiceId(hrRecruitmentBrochureDTO.getId());
            hrRecruitmentBrochureDTO.setHrRecruitmentStation(hrRecruitmentStationDTOList);

            String recruitmentBrochurePath = tempRootPath + StrUtil.replace(hrRecruitmentBrochureDTO.getClientName().trim(), " ", "") + "-" + StrUtil.replace(hrRecruitmentBrochureDTO.getRecruitBrochureName().trim(), " ", "") + File.separator;

            // 招聘简章
            String generalRulesPath = recruitmentBrochurePath + "招聘简章" + File.separator;
            cn.hutool.core.io.FileUtil.mkdir(generalRulesPath);
            // 招聘简章内容
            if (StringUtils.isNotBlank(hrRecruitmentBrochureDTO.getDetailContent())) {
                WordUtils.generateWordFile("招聘简章内容", hrRecruitmentBrochureDTO.getDetailContent(), generalRulesPath);
            }
            // 招聘简章附件
            this.dealAppendixList(hrRecruitmentBrochureDTO.getAppendixIds(), generalRulesPath);

            // 招聘公告
            List<HrRecruitmentBulletin> recruitmentBulletinList = this.hrRecruitmentBulletinRepository.selectByRecruitmentBrochureId(hrRecruitmentBrochureDTO.getId());
            if (CollectionUtils.isNotEmpty(recruitmentBulletinList)) {
                String noticePath = recruitmentBrochurePath + "招聘公告" + File.separator;
                cn.hutool.core.io.FileUtil.mkdir(noticePath);
                recruitmentBulletinList.forEach(recruitmentBulletin -> {
                    String noticeType = RecruitmentBrochure.NoticeType.getValueByKey(recruitmentBulletin.getNoticeType());
                    String noticePathTemp;
                    if (StringUtils.isNotBlank(recruitmentBulletin.getRecruitmentBulletinName())) {
                        noticePathTemp = noticePath + StrUtil.replace(recruitmentBulletin.getRecruitmentBulletinName().trim(), " ", "") + "-" + noticeType + StrUtil.replace(recruitmentBulletin.getRecruitmentStationName(), " ", "") + "-" + LocalDateTimeUtil.format(recruitmentBulletin.getCreatedDate(), "(yyyyMMddHHmmss)") + File.separator;
                    } else {
                        noticePathTemp = noticePath + noticeType + "-" + StrUtil.replace(recruitmentBulletin.getRecruitmentStationName().trim(), " ", "") + LocalDateTimeUtil.format(recruitmentBulletin.getCreatedDate(), "(yyyyMMddHHmmss)") + File.separator;
                    }
                    cn.hutool.core.io.FileUtil.mkdir(noticePathTemp);

                    // 公告内容
                    if (StringUtils.isNotBlank(recruitmentBulletin.getNoticeContent())) {
                        String wordFileName;
                        if (StringUtils.isNotBlank(recruitmentBulletin.getRecruitmentBulletinName())) {
                            wordFileName = "招聘公告内容-" + recruitmentBulletin.getRecruitmentBulletinName();
                        } else {
                            wordFileName = "招聘公告内容";
                        }
                        WordUtils.generateWordFile(wordFileName, recruitmentBulletin.getNoticeContent(), noticePathTemp);
                    }
                    // 公告附件
                    this.dealAppendixList(recruitmentBulletin.getAppendixIds(), noticePathTemp);
                });
            }

            // 报名情况
            List<HrRegistrationDetails> hrRegistrationDetailsList = this.hrRegistrationDetailsRepository.selectByBrochureId(hrRecruitmentBrochureDTO.getId());
            if (CollectionUtils.isNotEmpty(hrRegistrationDetailsList)) {
                List<String> ids = hrRegistrationDetailsList.stream().map(HrRegistrationDetails::getId).collect(Collectors.toList());
                String registrationPath = recruitmentBrochurePath + "报名情况" + File.separator;
                // 报名人员信息
                String signInInfoPath = registrationPath + "人员信息" + File.separator;
                this.dealSignInInfoExport(hrRegistrationDetailsList, signInInfoPath, ids);

                // 报名记录
                List<HrRegistrationDetailsExport> hrRegistrationDetailsExportList = this.hrRegistrationDetailsRepository.detailsExport(ids);
                ExcelUtils.exportLocal("报名记录", HrRegistrationDetailsExport.class, hrRegistrationDetailsExportList, registrationPath);
            }
            message = "任务执行成功";
        } catch (Exception e) {
            message = e.getMessage();
            log.error("招聘简章异步导出发生异常:{}", e.getMessage());
            cn.hutool.core.io.FileUtil.appendUtf8String(e.getMessage() + "\n", tempRootPath + "导出错误信息.txt");
            e.printStackTrace();
        }
        return new AsyncResult<>(message);
    }

    private void dealSignInInfoExport(List<HrRegistrationDetails> hrRegistrationDetailsList, String signInInfoPath, List<String> ids) {
        //所有文件路径
        List<String> infoIds = hrRegistrationDetailsList.stream().map(HrRegistrationDetails::getInfoId).collect(Collectors.toList());
        List<HrRegistrationInfo> hrRegistrationInfoList = this.hrRegistrationInfoRepository.selectBatchIds(infoIds);
        List<HrTemplateDTO> hrTemplateDTOList = this.hrRegistrationDetailsRepository.selectBybrochureId(null, ids);
        List<HrContentTemplate> contentTemplateList = hrContentTemplateRepository.selectList(new QueryWrapper<HrContentTemplate>().eq("is_delete", 0));
        for (HrRegistrationDetails hrRegistrationDetails : hrRegistrationDetailsList) {
            String infoId = hrRegistrationDetails.getInfoId();
            //获取报名信息
            HrRegistrationInfo hrRegistrationInfo = hrRegistrationInfoList.stream().filter(lst -> lst.getId().equals(infoId)).findFirst().orElse(null);
            if (hrRegistrationInfo == null) {
                continue;
            }

            //获取模板填入的数据
            String registrationJson = hrRegistrationInfo.getRegistrationJson();
            if (cn.casair.common.utils.StringUtils.isBlank(registrationJson)) {
                continue;
            }
            //查询模板数据
            HrTemplateDTO hrTemplateDTO = hrTemplateDTOList.stream().filter(lst -> lst.getId().equals(hrRegistrationDetails.getId())).findFirst().orElse(null);
            if (hrTemplateDTO == null) {
                continue;
            }
            //json模板
            String preview = hrTemplateDTO.getPreview();
            if (cn.casair.common.utils.StringUtils.isBlank(preview)) {
                continue;
            }
            JSONObject hrTalentStaff = JSON.parseObject(registrationJson);
            if (hrTalentStaff == null) {
                continue;
            }
            String staffName = hrTalentStaff.getString("name");
            String signInPaperInfoPath = signInInfoPath + staffName + File.separator;
            cn.hutool.core.io.FileUtil.mkdir(signInPaperInfoPath);
            for (Map.Entry<String, Object> stringObjectEntry : hrTalentStaff.entrySet()) {
                String start = "pname=\"";
                String end = "\">";
                String key = stringObjectEntry.getKey();
                if (key.equals("avatar")) {
                    try {
                        String path = stringObjectEntry.getValue().toString();
                        File file = FileUtil.getFile(path, "picture");
                        String suffix = path.substring(path.lastIndexOf(".") + 1);
                        String name = signInPaperInfoPath + "picture." + suffix;
                        //修改图片大小
                        ImgUtils.zoomImageScale(file, name, 140);
                        //将图片转化成base64
                        String imageStr = ImgUtils.getImageStr(name);
                        //删除临时文件
                        FileUtil.deleteTempFile(name);
                        imageStr = "data:image/jpeg;base64," + imageStr;
                        String a = "><img width=\"7%\" height=\"7%;\" src=\"";
                        preview = preview.replace("pname=\"avatar\">照片", a + imageStr + "\">");
                    } catch (Exception e) {
                        log.error("图像图片出现错误", e);
                    }
                }
                if (stringObjectEntry.getValue().toString().equals("false")) {
                    stringObjectEntry.setValue("否");
                }
                if (stringObjectEntry.getValue().toString().equals("true")) {
                    stringObjectEntry.setValue("是");
                }
                preview = preview.replaceAll(start + key + end, ">" + stringObjectEntry.getValue());

            }
            //将json转化成对象
            HrTalentStaffDTO hrTalentStaffsDTO = JSON.parseObject(registrationJson, HrTalentStaffDTO.class);
            //获取最高x学历list
            List<HrStaffEducationDTO> highestEducationLists = hrTalentStaffsDTO.getHighestEducationLists();
            String highestList = "highestEducationLists";
            preview = StringUtil.replaceHtml(preview, highestEducationLists, highestList);
            //第一学历
            List<HrStaffEducationDTO> firstEducation = hrTalentStaffsDTO.getFirstEducation();
            String firstList = "firstEducation";
            preview = StringUtil.replaceHtml(preview, firstEducation, firstList);
            //工作经历
            List<HrStaffWorkExperienceDTO> workExperience = hrTalentStaffsDTO.getWorkExperience();
            String workList = "workExperience";
            preview = StringUtil.replaceHtml(preview, workExperience, workList);
            //教育经历
            List<HrStaffEducationDTO> staffEducation = hrTalentStaffsDTO.getStaffEducation();
            String staffList = "staffEducation";
            preview = StringUtil.replaceHtml(preview, staffEducation, staffList);
            //家庭成员情况
            List<HrStaffFamilyDTO> staffFamilyList = hrTalentStaffsDTO.getStaffFamily();
            preview = StringUtil.replaceHtml(preview, staffFamilyList, "staffFamily");
            //职业工中
            List<HrStaffQualificationDTO> staffQualification = hrTalentStaffsDTO.getStaffQualification();
            preview = StringUtil.replaceHtml(preview, staffQualification, "staffQualification");
            if (cn.casair.common.utils.StringUtils.isNotBlank(preview)) {
                String wordFileName = "报名简历-" + staffName;
                WordUtils.generateWordFile(wordFileName, preview, signInPaperInfoPath);
            }
            // 获取所有附件
            String appendixJson = hrRegistrationInfo.getAppendixJson();
            if (cn.casair.common.utils.StringUtils.isNotBlank(appendixJson)) {
                JSONObject json = JSON.parseObject(appendixJson);
                for (Map.Entry<String, Object> stringObjectEntry : json.entrySet()) {
                    String key = stringObjectEntry.getKey();
                    HrContentTemplate hrContentTemplate = contentTemplateList.stream().filter(lst -> lst.getName().equals(key)).findFirst().orElse(null);
                    String hrContentTemplateName = hrContentTemplate == null ? "" : hrContentTemplate.getLabel();
                    JSONObject jsonObject = json.getJSONObject(key);
                    String appendixIds = jsonObject.getString("appendixIds");
                    List<HrAppendix> hrAppendices = JSONArray.parseArray(appendixIds, HrAppendix.class);
                    //获取这个人的所有附件
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(hrAppendices)) {
                        hrAppendices.forEach(ls -> FileUtil.saveUrlAs(ls.getFileUrl(), signInPaperInfoPath + hrContentTemplateName + "_" + ls.getOriginName()));
                    }
                }

            }
        }
    }

    /**
     * 处理附件
     */
    private void dealAppendixList(String appendixIdStr, String filePath) {
        if (StringUtils.isNotBlank(appendixIdStr)) {
            List<String> appendixIds = Arrays.asList(appendixIdStr.split(","));
            List<HrAppendixDTO> hrAppendixDTOList = hrAppendixService.getHrAppendixListByIds(appendixIds);
            String appendixPath = filePath + "附件" + File.separator;
            cn.hutool.core.io.FileUtil.mkdir(appendixPath);
            hrAppendixDTOList.forEach(ls -> FileUtil.saveUrlAs(ls.getFileUrl(), appendixPath + ls.getOriginName()));
        }
    }


}
