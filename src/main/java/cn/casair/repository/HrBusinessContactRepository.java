package cn.casair.repository;

import cn.casair.domain.HrBusinessContact;
import cn.casair.dto.HrBusinessContactDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 业务联系人数据库操作类
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Repository
public interface HrBusinessContactRepository extends BaseMapper<HrBusinessContact> {


    /**
     * 获取最大排序值
     *
     * @return int
     * <AUTHOR>
     * @date 2022/3/1
     **/
    Integer getMaxSort();

    /**
     * 首页业务联系人
     *
     * @return java.util.List<cn.casair.dto.HrBusinessContactDTO>
     * <AUTHOR>
     * @date 2022/2/15
     **/
    List<HrBusinessContactDTO> getBusinessContactList();

    /**
     * 查询
     *
     * @param ids
     * @return java.util.List<cn.casair.domain.HrBusinessContact>
     * <AUTHOR>
     * @date 2022/2/14
     **/
    List<HrBusinessContact> selectByIds(@Param("ids") List<String> ids);

    /**
     * 分页查询业务联系人
     *
     * @param hrBusinessContactDTO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrBusinessContactDTO>
     * <AUTHOR>
     * @date 2022/2/14
     **/
    IPage<HrBusinessContactDTO> selectPageList(Page<HrBusinessContact> page, @Param("params") HrBusinessContactDTO hrBusinessContactDTO);

    /**
     * 根据序号查询
     * @param sort
     * <AUTHOR>
     * @return cn.casair.domain.HrBusinessContact
     * @date 2022/2/14
     **/
    List<HrBusinessContact> selectBySort(@Param("sort") Integer sort);
}
