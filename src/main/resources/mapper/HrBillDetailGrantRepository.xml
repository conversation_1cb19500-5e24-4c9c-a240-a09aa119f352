<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillDetailGrantRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, bill_detail_id, grant_state, grant_date, pay_year, pay_monthly, remark, is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillDetailGrant">
        <id column="id" property="id"/>
        <result column="bill_detail_id" property="billDetailId"/>
        <result column="grant_state" property="grantState"/>
        <result column="grant_date" property="grantDate"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <resultMap id="ResultMap" type="cn.casair.dto.HrBillDetailGrantDTO">
        <id column="id" property="id"/>
        <result column="bill_detail_id" property="billDetailId"/>
        <result column="grant_state" property="grantState"/>
        <result column="grant_date" property="grantDate"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
        <result column="taxPeriod" property="taxPeriod"/>
        <collection property="hrAppendixDTOList" ofType="cn.casair.dto.HrAppendixDTO" column="id" select="queryAppendixById"></collection>
    </resultMap>

    <select id="findListByBillDetailId" resultMap="ResultMap">
        select bdg.*,
               IF ( bdg.grant_state = 1, concat( bdg.pay_year, '-', IF ( bdg.pay_monthly > 9, bdg.pay_monthly, concat( '0', bdg.pay_monthly )) ), NULL ) taxPeriod
        from hr_bill_detail_grant bdg
        where bdg.is_delete = 0
          and bdg.bill_detail_id = #{billDetailId}
        order by bdg.created_date desc
    </select>

    <select id="queryAppendixById" resultType="cn.casair.dto.HrAppendixDTO">
        select ha.*
        from hr_appendix_union hau
                 left join hr_appendix ha on ha.id = hau.appendix_id
        where ha.is_delete = 0
          and hau.union_id = #{id}
        order by ha.created_date desc
    </select>
    <select id="findList" resultType="cn.casair.dto.HrBillDetailGrantDTO">
        select bdg.*,
               hbd.name,
               hbd.certificate_num,
               IF ( bdg.grant_state = 1, concat( bdg.pay_year, '-', IF ( bdg.pay_monthly > 9, bdg.pay_monthly, concat( '0', bdg.pay_monthly )) ), NULL ) taxPeriod
        from hr_bill_detail_grant bdg
            left join hr_bill_detail hbd on hbd.id = bdg.bill_detail_id
        where bdg.is_delete = 0
        <if test="params.billDetailId != null and params.billDetailId != ''">
            and bdg.bill_detail_id = #{params.billDetailId}
        </if>
        <if test="params.ids != null and params.ids.size() > 0">
            and bdg.id in
            <foreach collection="params.ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        order by bdg.created_date desc
    </select>

</mapper>
