package cn.casair.service;

import cn.casair.domain.HrNormalSalary;
import cn.casair.dto.HrNormalSalaryDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 正常薪金的导出状态服务类
 *
 * <AUTHOR>
 * @since 2022-12-01
 */
public interface HrNormalSalaryService extends IService<HrNormalSalary> {


    /**
     * 创建正常薪金的导出状态
     *
     * @param hrNormalSalaryDTO
     * @return
     */
    HrNormalSalaryDTO createHrNormalSalary(HrNormalSalaryDTO hrNormalSalaryDTO);

    /**
     * 修改正常薪金的导出状态
     *
     * @param hrNormalSalaryDTO
     * @return
     */
    Optional<HrNormalSalaryDTO> updateHrNormalSalary(HrNormalSalaryDTO hrNormalSalaryDTO);

    /**
     * 查询正常薪金的导出状态详情
     *
     * @param id
     * @return
     */
    HrNormalSalaryDTO getHrNormalSalary(String id);

    /**
     * 删除正常薪金的导出状态
     *
     * @param id
     */
    void deleteHrNormalSalary(String id);

    /**
     * 批量删除正常薪金的导出状态
     *
     * @param ids
     */
    void deleteHrNormalSalary(List<String> ids);

    /**
     * 分页查询正常薪金的导出状态
     *
     * @param hrNormalSalaryDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrNormalSalaryDTO hrNormalSalaryDTO, Long pageNumber, Long pageSize);

    /**
     * 保存正常薪金的导出状态
     *
     * @param idStr clientId-payYear-payMonthly
     * @return
     */
    void saveNormalSalary(String idStr);
}
