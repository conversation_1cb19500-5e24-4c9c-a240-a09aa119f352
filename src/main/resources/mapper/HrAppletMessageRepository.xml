<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrAppletMessageRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, staff_id, client_id, service_id, message_type, progress_description, see_state, show_date,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrAppletMessage">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="service_id" property="serviceId"/>
        <result column="message_type" property="messageType"/>
        <result column="progress_description" property="progressDescription"/>
        <result column="see_state" property="seeState"/>
        <result column="show_date" property="showDate"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

</mapper>
