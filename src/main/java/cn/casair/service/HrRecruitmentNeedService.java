package cn.casair.service;
import cn.casair.domain.HrRecruitmentNeed;
import cn.casair.dto.HrRecruitmentNeedDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

/**
 * 招聘需求表服务类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
public interface HrRecruitmentNeedService extends IService<HrRecruitmentNeed> {


    /**
     * 创建招聘需求表
     * @param hrRecruitmentNeedDTO
     * @return
     */
    HrRecruitmentNeedDTO createHrRecruitmentNeed(HrRecruitmentNeedDTO hrRecruitmentNeedDTO);

    /**
     * 修改招聘需求表
     * @param hrRecruitmentNeedDTO
     * @return
     */
    Optional<HrRecruitmentNeedDTO> updateHrRecruitmentNeed(HrRecruitmentNeedDTO hrRecruitmentNeedDTO);

    /**
     * 查询招聘需求表详情
     * @param id
     * @return
     */
    HrRecruitmentNeedDTO getHrRecruitmentNeed(String id);

    /**
     * 删除招聘需求表
     * @param id
     */
    void deleteHrRecruitmentNeed(String id);

    /**
     * 批量删除招聘需求表
     * @param ids
     */
    void deleteHrRecruitmentNeed(List<String> ids);

    /**
     * 分页查询招聘需求表
     * @param hrRecruitmentNeedDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRecruitmentNeedDTO hrRecruitmentNeedDTO,Long pageNumber,Long pageSize);

    /**
     * 查看招聘需求详情
     * @param hrRecruitmentNeedDTO
     * @return
     */
    HrRecruitmentNeedDTO getDetail(HrRecruitmentNeedDTO hrRecruitmentNeedDTO);

    /**
     * 批量确认
     * @param hrRecruitmentNeedDTOList
     * @return
     */
    ResponseEntity<?> updateStates(List<HrRecruitmentNeedDTO> hrRecruitmentNeedDTOList);

    /**
     * 获取招聘单位下面的招聘需求
     * @param clientId
     * @return
     */
    List<HrRecruitmentNeedDTO> unitRrecruitmentDemand(String clientId);

}
