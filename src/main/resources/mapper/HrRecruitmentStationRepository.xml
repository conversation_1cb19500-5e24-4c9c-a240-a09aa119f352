<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRecruitmentStationRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, service_id, recruitment_station_id, recruitment_station_name, recruitment_people_number,register_template_id,interview_paper_id, exam_format, client_id, paper_id, recruitment_term, is_need_pay, recruitment_fee, written_exam_start_time, written_exam_end_time, interview_exam_start_time, interview_exam_end_time,interview_location, promoted_ratio, investigation_ratio, is_equal_investigate, interview_pass_line, written_pass_line, interview_score_weight, written_score_weight, exam_notice, recruitment_num, pass_num, payment_num, is_publish_notice,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrRecruitmentStation">
        <id column="id" property="id"/>
        <result column="service_id" property="serviceId"/>
        <result column="recruitment_station_id" property="recruitmentStationId"/>
        <result column="recruitment_station_name" property="recruitmentStationName"/>
        <result column="recruitment_people_number" property="recruitmentPeopleNumber"/>
        <result column="exam_format" property="examFormat"/>
        <result column="client_id" property="clientId"/>
        <result column="paper_id" property="paperId"/>
        <result column="interview_paper_id" property="interviewPaperId"/>
        <result column="register_template_id" property="registerTemplateId"/>
        <result column="recruitment_term" property="recruitmentTerm"/>
        <result column="is_need_pay" property="isNeedPay"/>
        <result column="recruitment_fee" property="recruitmentFee"/>
        <result column="written_exam_start_time" property="writtenExamStartTime"/>
        <result column="written_exam_end_time" property="writtenExamEndTime"/>
        <result column="interview_exam_start_time" property="interviewExamStartTime"/>
        <result column="interview_exam_end_time" property="interviewExamEndTime"/>
        <result column="interview_location" property="interviewLocation"/>
        <result column="promoted_ratio" property="promotedRatio"/>
        <result column="investigation_ratio" property="investigationRatio"/>
        <result column="is_equal_investigate" property="isEqualInvestigate"/>
        <result column="interview_pass_line" property="interviewPassLine"/>
        <result column="written_pass_line" property="writtenPassLine"/>
        <result column="interview_score_weight" property="interviewScoreWeight"/>
        <result column="written_score_weight" property="writtenScoreWeight"/>
        <result column="exam_notice" property="examNotice"/>
        <result column="recruitment_num" property="recruitmentNum"/>
        <result column="pass_num" property="passNum"/>
        <result column="payment_num" property="paymentNum"/>
        <result column="is_publish_notice" property="isPublishNotice"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByServiceId" resultType="cn.casair.dto.HrRecruitmentStationDTO">
        SELECT * FROM hr_recruitment_station WHERE service_id = #{serviceId} AND is_delete = 0;
    </select>

    <select id="listByServiceId" resultType="cn.casair.dto.HrRecruitmentStationDTO">
            SELECT
                *
            FROM
                hr_recruitment_station rs
                    LEFT JOIN hr_station hs ON hs.id = rs.recruitment_station_id
                    AND hs.is_delete = 0
            where rs.is_delete = 0 and rs.service_id =  #{serviceId} ORDER BY rs.created_date
    </select>

    <select id="selsecthrRecruitmentStationRepository" resultType="cn.casair.dto.HrRecruitmentStationDTO">
        SELECT * from hr_recruitment_station where service_id =#{id} and is_delete=0
    </select>

    <select id="findList" resultType="cn.casair.dto.HrRecruitmentStationDTO">
        SELECT
            hrs.*,hs.*, ht.template_name
        FROM
            hr_recruitment_station hrs
        LEFT JOIN hr_template ht ON hrs.register_template_id = ht.id AND ht.is_delete = 0
        LEFT JOIN hr_station hs ON hs.id = hrs.recruitment_station_id AND hs.is_delete = 0
        WHERE hrs.is_delete = 0 AND hrs.service_id =  #{serviceId} ORDER BY hrs.created_date
    </select>

</mapper>
