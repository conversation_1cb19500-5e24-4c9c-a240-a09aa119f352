package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.RandomUtil;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrApplyDepartureStaffExport;
import cn.casair.mapper.HrAppendixMapper;
import cn.casair.mapper.HrApplyDepartureMapper;
import cn.casair.mapper.HrApplyDepartureStaffMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.asynchronous.EntryDepartureComponent;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 离职服务服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrApplyDepartureServiceImpl extends ServiceImpl<HrApplyDepartureRepository, HrApplyDeparture> implements HrApplyDepartureService {


    private final HrApplyDepartureRepository hrApplyDepartureRepository;
    private final HrApplyDepartureMapper hrApplyDepartureMapper;

    public HrApplyDepartureServiceImpl(HrApplyDepartureRepository hrApplyDepartureRepository, HrApplyDepartureMapper hrApplyDepartureMapper) {
        this.hrApplyDepartureRepository = hrApplyDepartureRepository;
        this.hrApplyDepartureMapper = hrApplyDepartureMapper;
    }
    @Value("${file.temp-path}")
    private String tempPath;
    @Value("${minio.excelPrefix}")
    private String excelPrefix;
    @Resource
    private RedisCache redisCache;
    @Resource
    private CodeTableService codeTableService;
    @Resource
    private HrApplyOpLogsService hrApplyOpLogsService;
    @Resource
    private HrApplyDepartureStaffService hrApplyDepartureStaffService;
    @Resource
    private HrApplyDepartureStaffMapper hrApplyDepartureStaffMapper;
    @Resource
    private HrAppendixService hrAppendixService;
    @Resource
    private HrAppendixMapper hrAppendixMapper;
    @Resource
    private HrApplyOpLogsRepository hrApplyOpLogsRepository;
    @Resource
    private HrTalentStaffRepository hrTalentStaffRepository;
    @Resource
    private HrApplyDepartureStaffRepository hrApplyDepartureStaffRepository;
    @Resource
    private HrClientService hrClientService;
    @Resource
    private HrNotificationUserService hrNotificationUserService;
    @Resource
    private HrAppletMessageService hrAppletMessageService;
    @Resource
    private HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;
    @Resource
    private SysOperLogService sysOperLogService;
    @Resource
    private EntryDepartureComponent entryDepartureComponent;
    @Resource
    private HrUpcomingService hrUpcomingService;
    @Resource
    private HrStaffSecondmentRepository hrStaffSecondmentRepository;

    /**
     * 创建离职服务
     *
     * @param hrApplyDepartureDTO
     * @return
     */
    @Override
    public HrApplyDepartureDTO createHrApplyDeparture(HrApplyDepartureDTO hrApplyDepartureDTO) {
        log.info("Create new HrApplyDeparture:{}", hrApplyDepartureDTO);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrApplyDepartureStaffDTO> hrApplyDepartureStaffDTOS = hrApplyDepartureDTO.getHrApplyDepartureStaffDTOS();
        hrApplyDepartureDTO.setStaffNum(hrApplyDepartureStaffDTOS.size()).setAppendixIds(String.join(",",hrApplyDepartureDTO.getAppendixIdList())).setLastModifiedDate(LocalDateTime.now());
        if (hrApplyDepartureDTO.getIsDefault().equals(StaffEnum.ApplyIsDefault.TEMPORARY_STORAGE.getKey())){
            hrApplyDepartureDTO.setApplyStatus(StaffEnum.ApplyStatusEnum.TEMPORARY_STORAGE.getKey());
        }else {
            hrApplyDepartureDTO.setApplyStatus(StaffEnum.ApplyStatusEnum.PENDING_REVIEW.getKey());
        }
        HrApplyDeparture hrApplyDeparture = this.hrApplyDepartureMapper.toEntity(hrApplyDepartureDTO);
        if (hrApplyDeparture.getId() != null){
            this.hrApplyDepartureRepository.updateById(hrApplyDeparture);
        }else {
            this.hrApplyDepartureRepository.insert(hrApplyDeparture);
        }
        HrApplyDepartureDTO departureDTO = this.hrApplyDepartureMapper.toDto(hrApplyDeparture);
        //查询附件
        String originName = "";
        if (CollectionUtils.isNotEmpty(hrApplyDepartureDTO.getAppendixIdList())){
            List<HrAppendixDTO> hrAppendixListByIds = hrAppendixService.getHrAppendixListByIds(hrApplyDepartureDTO.getAppendixIdList());
            originName = String.join(",",hrAppendixListByIds.stream().map(HrAppendixDTO::getOriginName).collect(Collectors.toList()));
        }
        //处理待离职员工
        List<String> nameList = new ArrayList<>();
        List<String> list = new ArrayList<>();
        boolean flag = hrApplyDepartureDTO.getIsDefault().equals(StaffEnum.ApplyIsDefault.SUBMIT.getKey());//true提交 false 暂存
        for (HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO : hrApplyDepartureStaffDTOS) {
            if (hrApplyDepartureStaffDTO.getIsDelete() != null && hrApplyDepartureStaffDTO.getIsDelete() && hrApplyDepartureStaffDTO.getId() != null){
                hrApplyDepartureRepository.deleteById(hrApplyDepartureStaffDTO.getId());
                continue;
            }
            //判断待离职员工数据中是否存在ID，如果存在ID说明该员工是从小程序端发起的离职申请
            if (list.contains(hrApplyDepartureStaffDTO.getStaffId())){
                throw new CommonException("添加的数据存在相同的员工！");
            }else {
                list.add(hrApplyDepartureStaffDTO.getStaffId());
            }
            hrApplyDepartureStaffDTO.setApplyDepartureId(departureDTO.getId());
            hrApplyDepartureStaffDTO.setWebsiteDepartureSchedule(DepartureServiceEnum.DepartureScheduleEnum.LAUNCH_RESIGNATION.getKey());
            if (flag){
                hrApplyDepartureStaffDTO.setDepartureStaffStatus(DepartureServiceEnum.STAY_MANAGER_REVIEW.getKey());
            }
            //修改缴费年月
            if (StringUtils.isNotBlank(hrApplyDepartureStaffDTO.getStopPaymentDate())){
                String[] split = hrApplyDepartureStaffDTO.getStopPaymentDate().split("-");
                hrApplyDepartureStaffDTO.setStopPayYear(Integer.parseInt(split[0])).setStopPayMonthly(Integer.parseInt(split[1]));
            }
            HrApplyDepartureStaff hrApplyDepartureStaff = hrApplyDepartureStaffMapper.toEntity(hrApplyDepartureStaffDTO);
            HrTalentStaff staff = hrTalentStaffRepository.selectById(hrApplyDepartureStaff.getStaffId());
            hrApplyDepartureStaff.setClientId(staff.getClientId());
            String message = "";
            if (hrApplyDepartureStaffDTO.getId() != null){//去修改
                if (!flag && hrApplyDepartureDTO.getIsLaunch() == 1){
                    hrApplyDepartureStaff.setDepartureStaffStatus(0);
                }
                if(!flag && hrApplyDepartureDTO.getIsLaunch() == 2){
                    hrApplyDepartureStaffDTO.setWebsiteDepartureSchedule(DepartureServiceEnum.DepartureScheduleEnum.CONFIRM_RESIGNATION.getKey());
                }
                hrApplyDepartureStaffService.updateById(hrApplyDepartureStaff);
                if (flag){
                    if (hrApplyDepartureDTO.getIsLaunch() == 2){
                        message = jwtUserDTO.getRealName()+"发起了"+staff.getName()+"的离职申请并上传了"+ originName;
                        hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),hrApplyDepartureStaff.getId(),hrApplyDepartureStaff.getStaffId(),message,false,ServiceCenterEnum.DEPARTURE_APPLICATIONS.getValue());
                        hrUpcomingService.updateUpcoming(hrApplyDepartureStaff.getId());
                    }else {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("departureStatus",DepartureServiceEnum.STAY_MANAGER_REVIEW.getKey());
                        jsonObject.put("message","公司已经发起您的离职申请");
                        message = jwtUserDTO.getRealName()+"新建了"+staff.getName()+"的离职服务并上传了"+originName+"####"+jsonObject.toJSONString();
                        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),hrApplyDepartureStaff.getId(),hrApplyDepartureStaff.getStaffId(),String.valueOf(jsonObject.get("message")),true,ServiceCenterEnum.DEPARTURE_APPLICATIONS.getValue());
                    }

                }
            }else {//去新增
                if (!flag){
                    hrApplyDepartureStaff.setDepartureStaffStatus(0);
                }
                hrApplyDepartureStaff.setDepartureApplicant(2);
                hrApplyDepartureStaff.setLastModifiedDate(LocalDateTime.now());
                hrApplyDepartureStaffService.save(hrApplyDepartureStaff);
                if (flag){
                    HrApplyDepartureStaffDTO departureStaffDTO = this.hrApplyDepartureStaffMapper.toDto(hrApplyDepartureStaff);
                    hrApplyDepartureStaff.setId(departureStaffDTO.getId());
                    //查询员工信息
                    staff.setDepartureStaffId(departureStaffDTO.getId());
                    hrTalentStaffRepository.updateById(staff);
                    nameList.add(staff.getName());
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("departureStatus",DepartureServiceEnum.STAY_MANAGER_REVIEW.getKey());
                    jsonObject.put("message","公司已经发起您的离职申请");
                    message = jwtUserDTO.getRealName()+"新建了"+staff.getName()+"的离职服务并上传了"+originName+"####"+jsonObject.toJSONString();
                    hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),hrApplyDepartureStaff.getId(),hrApplyDepartureStaff.getStaffId(),String.valueOf(jsonObject.get("message")),true,ServiceCenterEnum.DEPARTURE_APPLICATIONS.getValue());
                }
            }
            if (flag){
                this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(departureDTO.getId(),hrApplyDepartureStaff.getId(),jwtUserDTO.getId(),message,departureDTO.getAppendixIds(), ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
            }
        }
        HrClient hrClient = hrClientService.getById(departureDTO.getClientId());
        if (flag){
            hrUpcomingService.saveServiceUpcoming(departureDTO.getId(),departureDTO.getClientId(), "离职服务-审核" + hrClient.getClientName() + "的离职服务",LocalDate.now(),2);
            if (!nameList.isEmpty()){
                this.hrNotificationUserService.saveRemindContent(departureDTO.getClientId(),ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentEnum.CONTRACT_CONFIRMATION.getKey(),jwtUserDTO.getRealName()+"新建了"+String.join(",",nameList)+"的离职服务并上传了"+originName+"。等待经理审核",jwtUserDTO.getId());
            }
        }
        hrApplyDepartureDTO.setUnitNumber(hrClient.getUnitNumber());
        hrApplyDepartureDTO.setClientName(hrClient.getClientName());
        hrApplyDepartureDTO.setOriginNameList(originName);
        hrApplyDepartureDTO.setRecruits(String.join(",",hrApplyDepartureStaffDTOS.stream().map(HrApplyDepartureStaffDTO::getName).collect(Collectors.toList())));
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.RESIGNATION_APPLICATION.getValue(),
            hrApplyDepartureDTO.getId() == null ? BusinessTypeEnum.INSERT.getKey() : BusinessTypeEnum.UPDATE.getKey(),
            JSON.toJSONString(hrApplyDepartureDTO),
            HrApplyDepartureDTO.class,
            null,
            JSON.toJSONString(departureDTO)
        );
        return departureDTO;
    }

    /**
     * 修改离职服务
     *
     * @param hrApplyDepartureDTO
     * @return
     */
    @Override
    public Optional<HrApplyDepartureDTO> updateHrApplyDeparture(HrApplyDepartureDTO hrApplyDepartureDTO) {
        return Optional.ofNullable(this.hrApplyDepartureRepository.selectById(hrApplyDepartureDTO.getId()))
            .map(roleTemp -> {
                HrApplyDeparture hrApplyDeparture = this.hrApplyDepartureMapper.toEntity(hrApplyDepartureDTO);
                this.hrApplyDepartureRepository.updateById(hrApplyDeparture);
                log.info("Update HrApplyDeparture:{}", hrApplyDepartureDTO);
                return hrApplyDepartureDTO;
            });
    }

    /**
     * 查询离职服务详情
     *
     * @param id
     * @return
     */
    @Override
    public HrApplyDepartureDTO getHrApplyDeparture(String id) {
        log.info("Get HrApplyDeparture :{}", id);

        HrApplyDepartureDTO departureDTO = this.hrApplyDepartureRepository.findById(id);
        this.setDict(departureDTO);
        //查询待离职员工信息
        List<HrApplyDepartureStaffDTO> hrApplyDepartureStaffDTOS = hrApplyDepartureStaffService.viewResignationInFor(departureDTO.getId(),null,null);
        if (CollectionUtils.isNotEmpty(hrApplyDepartureStaffDTOS)){
            departureDTO.setHrApplyDepartureStaffDTOS(hrApplyDepartureStaffDTOS);
        }
        //返回附件信息
        List<HrAppendixDTO> hrAppendixDTOS = hrAppendixService.getHrAppendixListByIds(Arrays.asList(departureDTO.getAppendixIds().split(",")));
        if (CollectionUtils.isNotEmpty(hrAppendixDTOS)){
            departureDTO.setHrAppendixDTOS(hrAppendixDTOS);
        }
        //返回审核信息
        List<HrApplyOpLogsDTO> hrApplyOpLogsDTOS = hrApplyOpLogsService.findApplyOpLogsList(departureDTO.getId(),null);
        if (CollectionUtils.isNotEmpty(hrApplyOpLogsDTOS)){
            departureDTO.setHrApplyOpLogsDTOS(hrApplyOpLogsDTOS);
        }

        return departureDTO;
    }

    /**
     * 删除离职服务
     *
     * @param id
     */
    @Override
    public void deleteHrApplyDeparture(String id) {
        Optional.ofNullable(this.hrApplyDepartureRepository.selectById(id))
            .ifPresent(hrApplyDeparture -> {
                this.hrApplyDepartureRepository.deleteById(id);
                log.info("Delete HrApplyDeparture:{}", hrApplyDeparture);
                List<String> ids = new ArrayList<>();
                ids.add(id);
                this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.RESIGNATION_APPLICATION.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
            });
    }

    /**
     * 批量删除离职服务
     *
     * @param ids
     */
    @Override
    public void deleteHrApplyDeparture(List<String> ids) {
        log.info("Delete HrApplyDepartures:{}", ids);
        this.hrApplyDepartureRepository.deleteBatchIds(ids);
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.RESIGNATION_APPLICATION.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));

    }

    /**
     * 分页查询离职服务
     *
     * @param hrApplyDepartureDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrApplyDepartureDTO hrApplyDepartureDTO, Long pageNumber, Long pageSize) {
        Page<HrApplyDeparture> page = new Page<>(pageNumber, pageSize);
        if(CollectionUtils.isEmpty(hrApplyDepartureDTO.getClientIds())){
            List<String> clientIds = hrClientService.selectClientIdByUserId();
            if (CollectionUtils.isEmpty(clientIds)){
                clientIds.add("");
            }
            hrApplyDepartureDTO.setClientIds(clientIds);
        }
        IPage<HrApplyDepartureDTO> iPage = this.hrApplyDepartureRepository.findPage(page, hrApplyDepartureDTO);
        iPage.getRecords().forEach(this::setDict);
        return iPage;
    }

    /**
     * 赋值字典值
     * @param hrApplyDepartureDTO
     */
    private void setDict(HrApplyDepartureDTO hrApplyDepartureDTO) {
        if (hrApplyDepartureDTO.getApplyStatus()!=null){
            hrApplyDepartureDTO.setApplyStatusLabel(StaffEnum.ApplyStatusEnum.getValueByKey(hrApplyDepartureDTO.getApplyStatus()));
        }
    }

    /**
     * 经理审核离职服务
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity managerReviewDeparture(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();

        List<String> errorStatusList = new ArrayList<>();//员工状态不匹配集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String departureId : batchOptDTO.getApplyIdList()) {
            HrApplyDepartureDTO hrApplyDepartureDTO = hrApplyDepartureRepository.findById(departureId);

            if (!hrApplyDepartureDTO.getApplyStatus().equals(StaffEnum.ApplyStatusEnum.PENDING_REVIEW.getKey())
                && !hrApplyDepartureDTO.getApplyStatus().equals(StaffEnum.ApplyStatusEnum.PORTION__REVIEW.getKey())){
                //选择的数据中状态值不对
                errorStatusList.add(hrApplyDepartureDTO.getClientName());
                continue;
            }
            if (batchOptDTO.getOpt()){//通过
                hrApplyDepartureDTO.setApplyStatus(StaffEnum.ApplyStatusEnum.ALREADY_PASSED.getKey());
                hrUpcomingService.saveServiceUpcoming(hrApplyDepartureDTO.getId(),hrApplyDepartureDTO.getClientId(), "离职服务-通知员工办理离职",LocalDate.now(),0);
            }else {//拒绝
                hrApplyDepartureDTO.setApplyStatus(StaffEnum.ApplyStatusEnum.REJECTED.getKey());
                hrUpcomingService.updateUpcoming(hrApplyDepartureDTO.getId());
            }
            hrApplyDepartureRepository.updateById(hrApplyDepartureMapper.toEntity(hrApplyDepartureDTO));
            //生成审批的操作信息
            hrApplyOpLogsService.remove(new QueryWrapper<HrApplyOpLogs>().eq("apply_id",hrApplyDepartureDTO.getId()).isNull("apply_staff_id"));
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyDepartureDTO.getId(),null,jwtUserDTO.getId(),batchOptDTO.getCheckerReason(),null, ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
            this.hrNotificationUserService.saveRemindContent(hrApplyDepartureDTO.getClientId(),ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),ServiceCenterEnum.ReminderContentDepartureEnum.MANAGER_REVIEW.getKey(),jwtUserDTO.getRealName()+"审核了"+hrApplyDepartureDTO.getClientName()+"离职服务",jwtUserDTO.getId());
            successList.add(hrApplyDepartureDTO.getClientName());
            //操作待离职员工数据
            List<HrApplyDepartureStaffDTO> hrApplyDepartureStaffDTOS = hrApplyDepartureStaffService.viewResignationInFor(hrApplyDepartureDTO.getId(), StaffApplyStatusEnum.AuditResultEnum.STAY_AUDIT.getKey(), null);
            if (CollectionUtils.isNotEmpty(hrApplyDepartureStaffDTOS)){
                for (HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO : hrApplyDepartureStaffDTOS) {
                    //审核修改状态
                    this.updateDepartureStaffStatus(jwtUserDTO,hrApplyDepartureStaffDTO,batchOptDTO);
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success",successList);
        if (CollectionUtils.isNotEmpty(errorStatusList)){
            jsonObject.put("error_status","选择的数据中"+String.join(",",errorStatusList)+"的状态不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 离职服务--查看--审核员工信息
     * @param jwtUserDTO 操作人信息
     * @param hrApplyDepartureStaffDTO 待离职员工信息
     * @param batchOptDTO 操作参数
     */
    @Override
    public void updateDepartureStaffStatus(JWTUserDTO jwtUserDTO, HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO, BatchOptDTO batchOptDTO) {
        Integer applyStatus = null;
        Integer departureStaffStatus = null;
        Integer departureApplyStatus = null;
        Integer websiteDepartureSchedule = null;
        String message = "";
        JSONObject jsonObject = new JSONObject();
        if (batchOptDTO.getOpt()){
            //审核通过 审核结果更新为通过 状态更新为待通知员工办理离职 进度更新为经理审批
            applyStatus = StaffApplyStatusEnum.AuditResultEnum.AUDIT_PASS.getKey();
            departureStaffStatus = DepartureServiceEnum.TO_NOTIFIED_HANDLE_RESIGNATION.getKey();
            departureApplyStatus = StaffApplyStatusEnum.AuditResultEnum.AUDIT_PASS.getKey();
            websiteDepartureSchedule = DepartureServiceEnum.DepartureScheduleEnum.MANAGER_APPROVAL.getKey();
            jsonObject.put("departureStatus",DepartureServiceEnum.TO_NOTIFIED_HANDLE_RESIGNATION.getKey());
            jsonObject.put("message","经理已通过您的审核。您的离职日期是"+hrApplyDepartureStaffDTO.getDepartureDate());
            message = jwtUserDTO.getRealName()+"通过了"+hrApplyDepartureStaffDTO.getName()+"的离职申请。####"+jsonObject.toJSONString();
            hrTalentStaffRepository.updateStaffStatus(hrApplyDepartureStaffDTO.getStaffId(), StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey());
            hrUpcomingService.createServiceUpcoming(hrApplyDepartureStaffDTO.getId(),hrApplyDepartureStaffDTO.getStaffId(), "离职服务-通知员工-"+hrApplyDepartureStaffDTO.getName()+"办理离职",LocalDate.now(),0);
        }else {
            //审核拒绝 审核结果更新为拒绝 状态更新为经理审核拒绝 进度更新为离职结束
            applyStatus = StaffApplyStatusEnum.AuditResultEnum.AUDIT_REFUSE.getKey();
            departureStaffStatus = DepartureServiceEnum.MANAGER_REJECTED.getKey();
            departureApplyStatus = StaffApplyStatusEnum.AuditResultEnum.AUDIT_REFUSE.getKey();
            websiteDepartureSchedule = DepartureServiceEnum.DepartureScheduleEnum.END_RESIGNATION.getKey();
            jsonObject.put("departureStatus",DepartureServiceEnum.MANAGER_REJECTED.getKey());
            jsonObject.put("message","您的离职申请经理审核未通过，请重新发起申请。");
            jsonObject.put("checkerReason",batchOptDTO.getCheckerReason());
            message = jwtUserDTO.getRealName()+"拒绝了"+hrApplyDepartureStaffDTO.getName()+"的离职申请。####"+jsonObject.toJSONString();
            //查找员工是否借调信息，如果存在在判断是否正在借调
            Integer staffStatus = StaffEnum.StaffStatusEnum.ON_JOB.getKey();
            HrStaffSecondment hrStaffSecondment = hrStaffSecondmentRepository.selectOne(new QueryWrapper<HrStaffSecondment>().eq("is_delete", 0)
                .eq("staff_id", hrApplyDepartureStaffDTO.getStaffId()).lt("step", ServiceCenterEnum.SecondmentStepEnum.SECONDMENT_END.getKey())
                .orderByDesc("created_date").last("LIMIT 1"));
            if (hrStaffSecondment != null){
                staffStatus = StaffEnum.StaffStatusEnum.BORROWING.getKey();
            }
            //修改员工状态为在职
            hrTalentStaffRepository.updateStaffStatus(hrApplyDepartureStaffDTO.getStaffId(), staffStatus);
            hrUpcomingService.updateUpcoming(hrApplyDepartureStaffDTO.getId());
        }
        hrApplyDepartureStaffDTO.setDepartureApplyStatus(applyStatus).setDepartureStaffStatus(departureStaffStatus).setRejectionReason(batchOptDTO.getCheckerReason())
            .setWebsiteDepartureSchedule(websiteDepartureSchedule).setDepartureApplyStatus(departureApplyStatus);
        hrApplyDepartureStaffService.updateById(hrApplyDepartureStaffMapper.toEntity(hrApplyDepartureStaffDTO));
        //添加待离职员工的操作信息
        hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey(),hrApplyDepartureStaffDTO.getId(),hrApplyDepartureStaffDTO.getStaffId(),String.valueOf(jsonObject.get("message")),false,null);
        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyDepartureStaffDTO.getApplyDepartureId(),hrApplyDepartureStaffDTO.getId(),jwtUserDTO.getId(),message,null, ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
    }

    /**
     * 待离职员工信息导入模板
     * @param response
     */
    @Override
    public String importDepartureStaffTemplate(HttpServletResponse response) {
//        ExportParams params = new ExportParams(null, "待离职员工信息");
//        params.setStyle(ExcelStyleUtils.class);
//        params.setType(ExcelType.XSSF);
//        List<HrApplyDepartureStaffTemplate> list = new ArrayList<>();
//        Workbook workbook = ExcelExportUtil.exportExcel(params, HrApplyDepartureStaffTemplate.class, list);
//        Sheet sheet = workbook.getSheetAt(0);
//        CellStyle style= workbook.createCellStyle();
//        // 设置为文本格式
//        DataFormat format = workbook.createDataFormat();
//        style.setDataFormat(format.getFormat("@"));
//        //对单独某一列进行样式赋值，第一个参数为列数，第二个参数为样式
//        sheet.setDefaultColumnStyle(6, style);
//        ExcelUtils.downLoadExcel("待离职员工信息导入模板.xlsx", response, workbook);
        return excelPrefix + "待离职员工信息导入模板.xlsx";

    }

    /**
     * 待离职员工信息导入
     * @param file 文件
     * @param clientId 客户ID
     * @return
     */
    @Override
    public String importDepartureStaff(MultipartFile file, String clientId) {
        // 设置进度条
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String redisKey =  RedisKeyEnum.progressBar.HIRED_STAFF.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.entryDepartureComponent.dealDepartureStaffImport(inputStream, redisKey, jwtUserDTO,clientId);
        return redisKey;
    }

    /**
     * 导出失败员工列表
     * @param departureId 离职服务ID
     * @param response
     * @return
     */
    @Override
    public String exportDepartureStaff(String departureId, HttpServletResponse response) {
        List<HrApplyDepartureStaffExport> hrApplyDepartureStaffTemplates = hrApplyDepartureStaffRepository.getHrApplyDepartureStaffStaffList(departureId,null,StaffApplyStatusEnum.AuditResultEnum.AUDIT_REFUSE.getKey());
        if (hrApplyDepartureStaffTemplates.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        int listSize = hrApplyDepartureStaffTemplates.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(hrApplyDepartureStaffTemplates, "失败员工信息", HrApplyDepartureStaffExport.class);
        List<String> ids = new ArrayList<>();
        ids.add(departureId);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.RESIGNATION_APPLICATION.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }
}
