package cn.casair.repository;

import cn.casair.domain.HrPaperManagement;
import cn.casair.domain.HrPaperQuestion;
import cn.casair.dto.HrPaperManagementDTO;
import cn.casair.dto.HrPaperQuestionDTO;
import cn.casair.dto.HrQuestionDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 试卷管理数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
@Repository
public interface HrPaperManagementRepository extends BaseMapper<HrPaperManagement> {

    List<HrPaperManagementDTO> getHrPaperManagementClient();

    List<HrPaperManagementDTO> getHrPaperManagementStation();

    IPage<HrPaperManagementDTO> pageselect(Page<HrPaperManagement> page, @Param("param1")HrPaperManagementDTO hrPaperManagementDTO);

    List<HrPaperManagementDTO> getHrPaperManagementIndexClient();

    List<HrPaperManagementDTO> getHrPaperManagementIndexStation();

    HrPaperManagementDTO getCopyHrPaperManagement(String id);

    IPage<HrQuestionDTO> selectQuestionPage(Page<HrPaperQuestion> page, @Param("param1")HrQuestionDTO hrQuestionDTO);

    List<String> getHrPaperManagementIndexScore(@Param("param1")HrQuestionDTO hrQuestionDTO);

    String getClientName(String clientId);

    String getStationName(String stationId);

    IPage<HrQuestionDTO> findPageRandom( @Param("param1")HrQuestionDTO hrQuestionDTO, Page<HrPaperQuestion> page);

    List<HrPaperQuestionDTO> getSelectQuestion(String id);

    List<HrQuestionDTO> getHrPaperManagementScore(@Param("questionId")List<String> questionId);

    List<HrQuestionDTO> getHrQuestionDTOId(List<String> list);

    List<String> getQuestionId(@Param("param1")HrQuestionDTO hrQuestionDTOs);

    HrPaperManagementDTO selectmanId(String id);

    List<HrQuestionDTO> selectHrQuestion(String id);

    List<HrQuestionDTO> selectQuestionssSum(@Param("param1")HrQuestionDTO hrQuestionDTO);

    List<String> selectClientid(String id);

    /**
     * 根据条件查询试卷
     * @param paperManagementDTO
     * @return
     */
    List<HrPaperManagementDTO> findList(@Param("param1") HrPaperManagementDTO paperManagementDTO);

}
