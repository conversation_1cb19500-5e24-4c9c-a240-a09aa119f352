package cn.casair.repository;

import cn.casair.common.utils.excel.CellItem;
import cn.casair.domain.HrBill;
import cn.casair.domain.HrBillDetail;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrAnnualBonusExport;
import cn.casair.dto.excel.HrBankStatementExport;
import cn.casair.dto.excel.HrBillDetailExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 账单详情数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Repository
public interface HrBillDetailRepository extends BaseMapper<HrBillDetail> {

    /**
     * 获取账单明细列表
     *
     * @param billIds
     * @param billDetailIds
     * @return java.util.List<cn.casair.dto.HrBillDetailDTO>
     * <AUTHOR>
     * @date 2022/1/25
     **/
    List<HrBillDetailDTO> getBillDetailByIds(@Param("billIds") List<String> billIds, @Param("billDetailIds") List<String> billDetailIds);

    /**
     * 获取银行报账单
     *
     * @param billIds
     * @param billDetailIds
     * @return java.util.List<cn.casair.dto.excel.HrBankStatementExport>
     * <AUTHOR>
     * @date 2021/11/10
     **/
    List<HrBankStatementExport> exportBankStatement(@Param("billIds") List<String> billIds, @Param("billDetailIds") List<String> billDetailIds);

    /**
     * 根据billId获取账单明细
     *
     * @param billId
     * @return java.util.List<cn.casair.dto.HrBillDetailDTO>
     * <AUTHOR>
     * @date 2021/11/10
     **/
    List<HrBillDetailDTO> getByBillId(String billId);

    /**
     * 获取员工指定年月账单信息
     *
     * @param detailDTO
     * @return cn.casair.dto.HrBillDetailDTO
     * <AUTHOR>
     * @date 2021/11/7
     **/
    List<HrBillDetailDTO> getBillDetailByBill(@Param("params") HrBillDetailDTO detailDTO);

    /**
     * 根据账单ids获取正常薪金导出列表
     *
     * @param hrBillDTO
     * @param clientIds
     * @return
     */
    List<HrBillDetailExport> getNormalSalaryExportList( @Param("params") HrBillDTO hrBillDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 根据员工id获取薪资列表
     *
     * @param staffId
     * @return java.util.List<cn.casair.dto.HrStaffSalaryBillDTO>
     * <AUTHOR>
     * @date 2021/11/5
     **/
    List<HrStaffSalaryDTO> getStaffSalaryList(String staffId);

    /**
     * 获取客户公司中员工的计算相关的参数
     *
     * @param clientIds
     * @return
     */
    List<HrBillDetailDTO> getStaffParamsByClientId(@Param("clientIds") List<String> clientIds);

    /**
     * 获取客户公司中员工的计算相关的参数
     *
     * @param staffId
     * @return
     */
    HrBillDetailDTO getStaffParamsByStaffId(@Param("staffId") String staffId);

    /**
     * 获取某个账单关联的账单明细列表
     *
     * @param billId
     * @param isUsed 是否可用： 0不可用  1可用
     * @return
     */
    List<HrBillDetailDTO> getListByBillId(@Param("billId") String billId, @Param("isUsed") Integer isUsed);

    /**
     * 根据ids获取某个账单明细列表
     *
     * @param ids
     * @return
     */
    List<HrBillDetailDTO> getListByIds(@Param("ids") List<String> ids);

    /**
     * 获取员工薪资账单详情
     *
     * @param billDetailId
     * @return cn.casair.dto.HrStaffSalaryDetailDTO
     * <AUTHOR>
     * @date 2021/11/5
     **/
    HrStaffSalaryDetailDTO getStaffSalaryDetailById(String billDetailId);

    /**
     * 根据基础账单删除账单明细
     *
     * @param billId 基础账单id
     */
    @Update("update hr_bill_detail hd set hd.is_delete = 1 where hd.bill_id = #{billId}")
    void deleteByBillId(String billId);

    /**
     * 账单明细中查询指定列的数据
     *
     * @param cellItems 指定列的信息
     * @param billIds    账单id
     * @return
     */
    @MapKey("billId")
    List<Map<String, Object>> getListByDynamicFields(@Param("sqlFields") List<CellItem> cellItems, @Param("billIds") List<String> billIds);

    /**
     * 分页查询账单明细
     *
     * @param page
     * @param hrBillDetailDTO
     **/
    IPage<HrBillDetailDTO> findPage(Page<HrBillDetail> page, @Param("params") HrBillDetailDTO hrBillDetailDTO);

    /**
     * 获取月平均收入分布结果
     *
     * @param hrBillDetailDTO
     * @return
     */
    List<HrBillDetailDTO> getAvgSalaryMonthly(@Param("params") HrBillDetailDTO hrBillDetailDTO);

    /**
     * 根据ids,获取数据明细
     *
     * @param hrDataDetailDTO
     **/
    List<HrDataDetailDTO> getDataByIds(@Param("params") HrDataDetailDTO hrDataDetailDTO);

    /**
     * 批量新增账单明细数据
     *
     * @param detailList
     **/
    void batchSave(@Param("list") List<HrBillDetail> detailList);

    /**
     * 获取某个账单关联的账单明细列表
     *
     * @param billIds
     * @param isUsed 是否可用： 0不可用  1可用
     * @param staffIds 员工Id
     * @return
     */
    List<HrBillDetailDTO> getListByBillIdAndStaffId(@Param("billIds") List<String> billIds, @Param("staffIds") List<String> staffIds, @Param("isUsed") Integer isUsed);

    /**
     * 获取可用的保障账单
     * @param billDetail
     * @return
     */
    HrBillDetailDTO selectSecurityBill(@Param("params") HrBillDetailDTO billDetail);

    /**
     * 获取多个账单关联的账单明细列表
     * @param billIdList
     * @param isUsed
     * @return
     */
    List<HrBillDetailDTO> getListByBillIdBatch(@Param("billIds") List<String> billIdList, @Param("isUsed") Integer isUsed);

    /**
     * 删除草稿账单明细
     * @param billIds
     */
    void deleteByBillIdBatch(@Param("billIds")List<String> billIds);

    /**
     * 全年一次性奖金导出
     * @param hrBillDTO
     * @param clientIds
     * @return
     */
    List<HrAnnualBonusExport> getBonusExportList(@Param("params") HrBillDTO hrBillDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 查询员工账单基本信息
     * @param staffIds
     * @return
     */
    List<HrBillDetailDTO> findStaffInfo(@Param("staffIds") List<String> staffIds);

    /**
     * 查询结算单明细
     * @param id
     * @return
     */
    List<HrBillDetailDTO> selectSalaryDetail(@Param("id")String id);

    /**
     * 工资发放导出
     * @param billIdList
     * @return
     */
    List<HrBankStatementExport> exportSalaryPayment(@Param("billIdList") List<String> billIdList, @Param("billDetailIdList") List<String> billDetailIdList);

    /**
     * 获取指定时间内的工资收入
     * @param staffId
     * @param dateMin
     * @param dateMax
     * @return
     */
    List<HrBillDetailDTO> averageMonthlyIncome(@Param("staffId") String staffId, @Param("dateMin") String dateMin, @Param("dateMax") String dateMax);

    /**
     * 薪酬账单税前应发汇总
     * @param billList
     * @return
     */
    HrBillDetailDTO getPreTaxSalaryTotal(@Param("billList") List<String> billList);

    /**
     * 查询该员工同一年月的所有薪酬账单
     * @param billDetail
     * @return
     */
    List<HrBillDetailDTO> selectSalaryBill(@Param("params") HrBillDetailDTO billDetail);

    /**
     * 查询数据明细数据合计
     * @param hrBillDetailDTO
     * @return
     */
    HrBillDetailDTO getTotalData(@Param("params")HrBillDetailDTO hrBillDetailDTO);

    /**
     * 根据客户Id查询社保合计
     * @param clientIds
     * @param billType
     * @return
     */
    HrBillDetail getTotalByClientId(@Param("clientIds") List<String> clientIds, @Param("billType") Integer billType,
                                    @Param("payYear") Integer payYear, @Param("payMonth") Integer payMonth);

    /**
     * 根据年月查询明细
     * @param hrFeeReviewDTO
     * @param isUsed
     * @return
     */
    List<HrBillDetailDTO> getListBatch(@Param("params") HrFeeReviewDTO hrFeeReviewDTO, @Param("isUsed") Integer isUsed);

    /**
     * 根据身份证获取员工正常薪金账单列表
     * @param idNoList
     * @return
     */
    List<HrBillDetailDTO> getDetailNormalSalary(@Param("idNoList") List<String> idNoList);

    List<HrBillDetail> findByBillId(@Param("billType") Integer billType, @Param("billIds") List<String> billIds);

    /**
     * 维护工资发放状态关联
     *
     * @param billDetailId
     * @param billDetailGrantId
     */
    void updateGrantRelation(@Param("billDetailId") String billDetailId, @Param("billDetailGrantId") String billDetailGrantId);

    /**
     * 获取某个账单关联的账单明细列表
     *
     * @param billId
     * @param isUsed 是否可用： 0不可用  1可用
     * @return
     */
    List<HrBillDetailDTO> findListByBillId(@Param("billId") String billId, @Param("isUsed") Integer isUsed);

    /**
     * 员工工资发放列表
     *
     * @param hrBillDetailDTO
     * @return
     */
    List<HrBillDetailDTO> findSalaryPaymentList(@Param("params") HrBillDetailDTO hrBillDetailDTO);

    @Select("select * from hr_bill_detail bd left join hr_bill b on bd.bill_id = b.id and b.is_delete = 0 where bd.is_delete = 0 AND b.bill_no = #{billNo}")
    List<HrBillDetailDTO> getByBillNo(@Param("billNo")String billNo);

    /**
     * 获取中石化项目合同编号
     *
     * @return 合同编号
     */
    List<HrBillSinopecContractDTO> getProjectContractNo();

    /**
     * 修改中石化项目合同编号
     * @param contractNo 合同编号
     * @param projectName 项目名称
     * @param userName 修改人
     */
    void updateSinopecContractNo(@Param("contractNo") String contractNo, @Param("projectName") String projectName, @Param("userName") String userName);

    /**
     * 添加中石化项目合同编号
     * @param id ID标识
     * @param contractNo 合同编号
     * @param projectName 项目名称
     * @param userName 修改人
     */
    void insertSinopecContractNo(@Param("id") String id, @Param("contractNo") String contractNo, @Param("projectName") String projectName, @Param("userName") String userName);

    /**
     * 查询本月账单中未参与对账的员工信息
     *
     * @param idCardList 身份证信息
     * @param billIds 账单ID
     * @return
     */
    List<HrBillCompareResultDetailDTO> findNotAccordStaff(@Param("idCardList") List<String> idCardList, @Param("billIds") List<String> billIds);


    /**
     * 根据账单明细Id查询
     * @param billIdList
     * @param isUsed
     * @return
     */
    List<HrBillDetailDTO> findListBatch(@Param("billIdList") List<String> billIdList, @Param("isUsed") Integer isUsed);

    /**
     * 根据客户ID查询薪酬账单
     * @param payYear
     * @param payMonth
     * @param clientIdList
     * @return
     */
    List<HrBillDetailDTO> getSalaryBillBatchClientId(@Param("payYear") Integer payYear, @Param("payMonth") Integer payMonth,
                                                     @Param("clientIdList") List<String> clientIdList, @Param("idCardList") List<String> idCardList);

    /**
     * 获取全年一次性奖金总金额
     * @param billList
     * @return
     */
    HrBillDetailDTO getTotalBySalaryBill(@Param("billIdList") List<String> billList);

    /**
     * 统计薪酬账单的计税方式
     * @param billIds
     * @return
     */
    int countTaxCalculationMethod(@Param("billIdList") List<String> billIds);

    /**
     * 员工保障单信息
     *
     * @param staffId
     * @param dateMin
     * @param dateMax
     * @return
     */
    List<HrBillDetailDTO> averageMonthlySecurity(@Param("staffId") String staffId, @Param("dateMin") String dateMin, @Param("dateMax") String dateMax);
}
