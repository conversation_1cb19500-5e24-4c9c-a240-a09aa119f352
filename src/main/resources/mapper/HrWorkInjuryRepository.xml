<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrWorkInjuryRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, staff_id, client_id, injury_date, work_stoppage_start_date, work_stoppage_end_date, appraisal_status, status,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrWorkInjury">
                    <id column="id" property="id"/>
                    <result column="staff_id" property="staffId"/>
                    <result column="client_id" property="clientId"/>
                    <result column="injury_date" property="injuryDate"/>
                    <result column="work_stoppage_start_date" property="workStoppageStartDate"/>
                    <result column="work_stoppage_end_date" property="workStoppageEndDate"/>
                    <result column="appraisal_status" property="appraisalStatus"/>
                    <result column="status" property="status"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

        <select id="selectDetatil" resultType="cn.casair.dto.HrWorkInjuryDTO">
            SELECT
                hwi.id,
                hwi.staff_id,
                hwi.created_date,
                ht.client_id,
                hc.client_name,
                ht.`name`,
                ht.certificate_num,
                ht.sex,
                ht.phone,
                hs.profession_name,
                hs.id,
                ht.personnel_type,
                hwi.injury_date,
                hwi.work_stoppage_start_date,
                hwi.work_stoppage_end_date,
                hwi.`status`,
                hwi.appraisal_status,
                hwi.injury_description,
                su.real_name AS specialized,
                su.id,
                hse.basic_wage,
                hse.social_security_cardinal,
                hse.medical_insurance_cardinal,
                hse.accumulation_fund_cardinal,
                hce.contract_start_date,
                hce.contract_end_date
            FROM
                hr_work_injury hwi
                    LEFT JOIN hr_talent_staff ht ON hwi.staff_id = ht.id
                    AND ht.is_delete = 0
                    LEFT JOIN hr_staff_work_experience hsw ON hwi.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id
                    AND hsw.is_delete = 0
                    AND hsw.iz_default = 1
                    LEFT JOIN hr_station hs ON hsw.station_id = hs.id
                    AND hs.is_delete = 0
                    LEFT JOIN hr_client hc ON hc.id = hwi.client_id
                    AND hc.is_delete = 0
                    LEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id
                    AND huc.is_delete = 0
                    AND huc.is_specialized = 1
                    LEFT JOIN sys_user su ON huc.user_id = su.id
                    AND su.is_delete = 0
                    LEFT JOIN hr_staff_emolument hse ON hse.staff_id = ht.id
                    AND hse.is_delete = 0
                    LEFT JOIN (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
                               FROM (SELECT * FROM hr_contract WHERE is_delete = 0 AND state in (1,2) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) hce ON hce.staff_id = ht.id
                    AND hce.is_delete = 0
            WHERE
                hwi.is_delete = 0 AND ht.is_delete = 0 AND hwi.id = #{id}
    </select>
    <select id="exportWorkInjuries" resultType="cn.casair.dto.excel.HrWorkInjuryExport">
        SELECT
            hwi.id,
            ht.client_id,
            hc.client_name,
            ht.`name`,
            ht.staff_status,
            ht.certificate_num,
            ht.sex,
            ht.phone,
            hs.profession_name,
            hs.id,
            ht.personnel_type,
            hwi.injury_date,
            hwi.work_stoppage_start_date,
            hwi.work_stoppage_end_date,
            hwi.`status`,
            hwi.appraisal_status,
            hwi.injury_description,
            su.real_name AS specialized,
            su.id
        FROM
            hr_work_injury hwi
                LEFT JOIN hr_talent_staff ht ON hwi.staff_id = ht.id
                AND ht.is_delete = 0
                LEFT JOIN hr_staff_work_experience hsw ON hwi.staff_id = hsw.staff_id AND ht.client_id = hwi.client_id
                AND hsw.is_delete = 0
                AND hsw.iz_default = 1
                LEFT JOIN hr_station hs ON hsw.station_id = hs.id
                AND hs.is_delete = 0
                LEFT JOIN hr_client hc ON hc.id = hwi.client_id
                AND hc.is_delete = 0
                LEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id
                AND huc.is_delete = 0
                AND huc.is_specialized = 1
                LEFT JOIN sys_user su ON huc.user_id = su.id
                AND su.is_delete = 0
        ${ew.customSqlSegment}
    </select>
    <select id="selectByIds" resultType="cn.casair.dto.HrMaterialDTO">
        SELECT
            hm.id,
            hm.NAME,
            hm.type,
            ha.origin_name  fileName,
            ha.file_url fileUrl
        FROM
            hr_material hm
                LEFT JOIN hr_appendix ha ON hm.file_id = ha.id
        where hm.is_delete=0
        <if test="ids !=null and ids.size > 0">
            AND hm.id in
            <foreach collection="ids" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

    </select>

    <select id="workInjuryListByStaffId" resultType="cn.casair.dto.HrWorkInjuryDTO">
        SELECT
            hc.client_name,
            hwi.*
        FROM
            hr_work_injury hwi
        LEFT JOIN hr_talent_staff hts ON hwi.staff_id = hts.id
        LEFT JOIN hr_client hc ON hc.id = hwi.client_id
        WHERE
            hwi.is_delete = 0 AND hwi.staff_id = #{staffId}
        ORDER BY hwi.created_date
    </select>
</mapper>
