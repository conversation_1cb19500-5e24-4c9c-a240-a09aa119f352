package cn.casair.service;
import cn.casair.domain.NcCustomer;
import cn.casair.dto.NcCustomerDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * NC客户与系统客户的关联关系服务类
 *
 * <AUTHOR>
 * @since 2022-10-19
 */
public interface NcCustomerService extends IService<NcCustomer> {


    /**
     * 创建NC客户与系统客户的关联关系
     * @param ncCustomerDTO
     * @return
     */
    NcCustomerDTO createNcCustomer(NcCustomerDTO ncCustomerDTO);

    /**
     * 修改NC客户与系统客户的关联关系
     * @param ncCustomerDTO
     * @return
     */
    Optional<NcCustomerDTO> updateNcCustomer(NcCustomerDTO ncCustomerDTO);

    /**
     * 查询NC客户与系统客户的关联关系详情
     * @param id
     * @return
     */
    NcCustomerDTO getNcCustomer(String id);

    /**
     * 删除NC客户与系统客户的关联关系
     * @param id
     */
    void deleteNcCustomer(String id);

    /**
     * 批量删除NC客户与系统客户的关联关系
     * @param ids
     */
    void deleteNcCustomer(List<String> ids);

    /**
     * 分页查询NC客户与系统客户的关联关系
     * @param ncCustomerDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(NcCustomerDTO ncCustomerDTO,Long pageNumber,Long pageSize);
    }
