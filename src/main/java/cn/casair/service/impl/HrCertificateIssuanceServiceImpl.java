package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.EnumUtils;
import cn.casair.common.utils.PdfUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.formdata.InputLabel;
import cn.casair.mapper.HrAppendixMapper;
import cn.casair.mapper.HrCertificateIssuanceMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.ecloud.ECloudComponent;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 证明开具服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrCertificateIssuanceServiceImpl extends ServiceImpl<HrCertificateIssuanceRepository, HrCertificateIssuance> implements HrCertificateIssuanceService {


    private final HrCertificateIssuanceRepository hrCertificateIssuanceRepository;
    private final HrCertificateIssuanceMapper hrCertificateIssuanceMapper;

    public HrCertificateIssuanceServiceImpl(HrCertificateIssuanceRepository hrCertificateIssuanceRepository, HrCertificateIssuanceMapper hrCertificateIssuanceMapper) {
        this.hrCertificateIssuanceRepository = hrCertificateIssuanceRepository;
        this.hrCertificateIssuanceMapper = hrCertificateIssuanceMapper;
    }
    @Value("${file.temp-path}")
    private String tempPath;
    @Resource
    private HrApplyOpLogsService hrApplyOpLogsService;
    @Resource
    private HrAppendixRepository hrAppendixRepository;
    @Resource
    private HrAppendixService hrAppendixService;
    @Resource
    private ECloudComponent eCloudComponent;
    @Resource
    private UserRepository userRepository;
    @Resource
    private HrClientService hrClientService;
    @Resource
    private HrNotificationUserService hrNotificationUserService;
    @Resource
    private HrAppletMessageService hrAppletMessageService;
    @Resource
    private HrTalentStaffService hrTalentStaffService;
    @Resource
    private HrSmsTemplateService hrSmsTemplateService;
    @Resource
    private SysOperLogService sysOperLogService;
    @Resource
    private HrUpcomingService hrUpcomingService;
    @Resource
    private HrProofTemplateRepository hrProofTemplateRepository;
    @Resource
    private HrProofTemplateService hrProofTemplateService;
    @Resource
    private HrAppendixMapper hrAppendixMapper;
    @Resource
    private HrMessageListService hrMessageListService;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private HrMessageRoleRepository hrMessageRoleRepository;

    private static final String INCOME = "收入";

    private static final String ON_JOB = "在职";
    private static final String SENIORITY_CERTIFICATION = "工龄认定";

    private static final String RETIRE = "退休";
    private static final String INJURY = "工伤";
    private static final String BIRTH = "生育";
    private static final String DEATH = "死亡";
    private static final String TITLE = "职称";
    private static final String DISPATCH  = "派遣制";
    private static final String UNMARRIED = "未婚";
    private static final String INTRODUCTION = "介绍信";
    private static final String FIRST_MARRIAGE = "初婚未育";

    private static final String REGISTERED_RESIDENCE = "户口迁出";
    private static final String SETTLE = "落户";
    private static final String ARCHIVES = "档案调函";
    private static final String TEMPORARY = "暂住证";



    /**
     * 创建证明开具
     *
     * @param hrCertificateIssuanceDTO
     * @return
     */
    @Override
    public HrCertificateIssuanceDTO createHrCertificateIssuance(HrCertificateIssuanceDTO hrCertificateIssuanceDTO, Boolean requestFromWx) {
        log.info("Create new HrCertificateIssuance:{}", hrCertificateIssuanceDTO);
        String staffId; // 员工id
        if (requestFromWx) {
            JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
            staffId = jwtMiniDTO.getId();
        } else {
            staffId = hrCertificateIssuanceDTO.getStaffId();
        }
        if (StringUtils.isBlank(staffId)) {
            throw new CommonException("员工id不能为空");
        }
        HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(staffId);
        if (hrTalentStaff == null) {
            throw new CommonException("员工信息不存在");
        }
        if (hrTalentStaff.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())) {
            throw new CommonException("离职完成后不可在进行操作");
        }
        int type = 5;// 0 给专管员生成待办  1 给客户生成待办 2客服部经理生成待办 3档案管理员生成代办 4待遇合规专员 5招聘部部长 6薪酬专员 7入离职专员、待遇合规专员、招聘部部长、薪酬专员
        if (StringUtils.isNotBlank(hrCertificateIssuanceDTO.getProofTemplateId())){
            hrCertificateIssuanceDTO.setAppendixIds(hrCertificateIssuanceDTO.getProofTemplateId()).setIsDefault(1);
            HrProofTemplate hrProofTemplate = hrProofTemplateRepository.selectById(hrCertificateIssuanceDTO.getProofTemplateId());
            hrProofTemplate.setUseNum(hrProofTemplate.getUseNum() + 1);
            hrProofTemplateRepository.updateById(hrProofTemplate);
            if (hrProofTemplate.getTitle().contains(INCOME)){
                type = 6;
            }else if (hrProofTemplate.getTitle().contains(RETIRE)
                || hrProofTemplate.getTitle().contains(BIRTH)
                || hrProofTemplate.getTitle().contains(INJURY)
                || hrProofTemplate.getTitle().contains(DEATH)
                || hrProofTemplate.getTitle().contains(TITLE)
                || hrProofTemplate.getTitle().contains(DISPATCH)
                || hrProofTemplate.getTitle().contains(UNMARRIED)
                || hrProofTemplate.getTitle().contains(INTRODUCTION)
                || hrProofTemplate.getTitle().contains(FIRST_MARRIAGE)
            ){
                type = 4;
            }else if (hrProofTemplate.getTitle().contains(ON_JOB) || hrProofTemplate.getTitle().contains(SENIORITY_CERTIFICATION)){
                type = 0;
            }else if (hrProofTemplate.getTitle().contains(REGISTERED_RESIDENCE)
                || hrProofTemplate.getTitle().contains(SETTLE)
                || hrProofTemplate.getTitle().contains(ARCHIVES)
                || hrProofTemplate.getTitle().contains(TEMPORARY)
            ){
                type = 5;
            }
        }else {
            type = 7;
            if (hrCertificateIssuanceDTO.getAppendixIdList()!=null &&! hrCertificateIssuanceDTO.getAppendixIdList().isEmpty()){
                String join = String.join(",", hrCertificateIssuanceDTO.getAppendixIdList());
                if (StringUtils.isNotBlank(join) && !join.equals("null")){
                    hrCertificateIssuanceDTO.setAppendixIds(join);
                    List<String> collect = hrAppendixRepository.selectBatchIds(hrCertificateIssuanceDTO.getAppendixIdList()).stream().map(HrAppendix::getOriginName).collect(Collectors.toList());
                    hrCertificateIssuanceDTO.setOriginNameList(String.join(",", collect));
                }
            }
        }
        hrCertificateIssuanceDTO.setCertificateStatus(CertificateTypeEnum.CertificateStatus.TO_BE_MANAGER_REVIEWED.getKey())
            .setStaffId(staffId).setClientId(hrTalentStaff.getClientId()).setLastModifiedDate(LocalDateTime.now());
        HrCertificateIssuance hrCertificateIssuance = this.hrCertificateIssuanceMapper.toEntity(hrCertificateIssuanceDTO);
        this.hrCertificateIssuanceRepository.insert(hrCertificateIssuance);
        HrCertificateIssuanceDTO certificateIssuanceDTO = this.hrCertificateIssuanceMapper.toDto(hrCertificateIssuance);
        String opClientName = requestFromWx ? "小程序" : "网页端"; // 操作端
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("certificateStatus",CertificateTypeEnum.CertificateStatus.TO_BE_MANAGER_REVIEWED.getKey());
        jsonObject.put("message","您的证明开具申请已提交");
        String message = hrTalentStaff.getName()+"通过" + opClientName + "申请了"+certificateIssuanceDTO.getCertificateName()+"的开具证明申请。####"+jsonObject.toJSONString();
        // 保存操作日志
        if (requestFromWx) {
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(certificateIssuanceDTO.getId(),null,staffId,message,null,true,certificateIssuanceDTO.getAppendixIds(), ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey());
        } else {
            JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(certificateIssuanceDTO.getId(), null, jwtUserDTO.getId(), message, certificateIssuanceDTO.getAppendixIds(), ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey());
        }
        // 小程序发送通知
        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey(),certificateIssuanceDTO.getId(),certificateIssuanceDTO.getStaffId(),String.valueOf(jsonObject.get("message")),false,null);
        // 提醒内容
        this.hrNotificationUserService.saveRemindContent(certificateIssuanceDTO.getClientId(),ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey(),ServiceCenterEnum.CertificateIssuanceEnum.INITIATE_APPLICATION.getKey(),hrTalentStaff.getName()+"通过" + opClientName + "申请了"+certificateIssuanceDTO.getCertificateName()+"的开具证明申请。等待客户审核",staffId);
        // 创建待办
        hrUpcomingService.createServiceUpcoming(certificateIssuanceDTO.getId(),hrTalentStaff.getId(), "证明开具-审核" + hrTalentStaff.getName() + "的"+certificateIssuanceDTO.getCertificateName(),LocalDate.now(),type);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.CERTIFICATE_ISSUANCE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrCertificateIssuanceDTO),
            HrCertificateIssuanceDTO.class,
            null,
            JSON.toJSONString(hrCertificateIssuanceDTO)
        );
        return certificateIssuanceDTO;
    }

    /**
     * 修改证明开具
     *
     * @param hrCertificateIssuanceDTO
     * @return
     */
    @Override
    public Optional<HrCertificateIssuanceDTO> updateHrCertificateIssuance(HrCertificateIssuanceDTO hrCertificateIssuanceDTO) {
        return Optional.ofNullable(this.hrCertificateIssuanceRepository.selectById(hrCertificateIssuanceDTO.getId()))
            .map(roleTemp -> {
                HrCertificateIssuance hrCertificateIssuance = this.hrCertificateIssuanceMapper.toEntity(hrCertificateIssuanceDTO);
                this.hrCertificateIssuanceRepository.updateById(hrCertificateIssuance);
                log.info("Update HrCertificateIssuance:{}", hrCertificateIssuanceDTO);
                return hrCertificateIssuanceDTO;
            });
    }

    /**
     * 查询证明开具详情
     *
     * @param id
     * @return
     */
    @Override
    public HrCertificateIssuanceDTO getHrCertificateIssuance(String id) {
        log.info("Get HrCertificateIssuance :{}", id);
        HrCertificateIssuanceDTO hrCertificateIssuance = this.hrCertificateIssuanceRepository.findById(id);
        setDict(hrCertificateIssuance);
        //返回证明文件/模板附件
//        if (StringUtils.isNotBlank(hrCertificateIssuance.getAppendixIds())){
//            List<HrAppendix> hrAppendixList = hrAppendixRepository.selectBatchIds(Arrays.asList(hrCertificateIssuance.getAppendixIds().split(",")));
//            if (CollectionUtils.isNotEmpty(hrAppendixList)){
//                hrCertificateIssuance.setHrAppendixList(hrAppendixList);
//            }
//        }
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(id, null);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)){
            hrCertificateIssuance.setApplyOpLogsList(applyOpLogsList);
        }
        return hrCertificateIssuance;
    }

    /**
     * 删除证明开具
     *
     * @param id
     */
    @Override
    public void deleteHrCertificateIssuance(String id) {
        Optional.ofNullable(this.hrCertificateIssuanceRepository.selectById(id))
            .ifPresent(hrCertificateIssuance -> {
                this.hrCertificateIssuanceRepository.deleteById(id);
                log.info("Delete HrCertificateIssuance:{}", hrCertificateIssuance);
            });
    }

    /**
     * 批量删除证明开具
     *
     * @param ids
     */
    @Override
    public void deleteHrCertificateIssuance(List<String> ids) {
        log.info("Delete HrCertificateIssuances:{}", ids);
        List<HrCertificateIssuance> certificateIssuances = hrCertificateIssuanceRepository.selectBatchIds(ids);
        this.hrCertificateIssuanceRepository.deleteBatchIds(ids);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> collect = certificateIssuances.stream().map(HrCertificateIssuance::getCertificateName).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.CERTIFICATE_ISSUANCE.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除证明开具: "+JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(certificateIssuances),
            jwtUserDTO
        );
    }

    /**
     * 分页查询证明开具
     *
     * @param hrCertificateIssuanceDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrCertificateIssuanceDTO hrCertificateIssuanceDTO, Long pageNumber, Long pageSize) {
        Page<HrCertificateIssuance> page = new Page<>(pageNumber, pageSize);
        if(CollectionUtils.isEmpty(hrCertificateIssuanceDTO.getClientIds())) {
            List<String> clientIds = hrClientService.selectClientIdByUserId();
            if (CollectionUtils.isEmpty(clientIds)) {
                return new Page();
            }
            hrCertificateIssuanceDTO.setClientIds(clientIds);
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        this.handleRoleTemp(jwtUserDTO,hrCertificateIssuanceDTO);
        IPage<HrCertificateIssuanceDTO> iPage = this.hrCertificateIssuanceRepository.findPage(page, hrCertificateIssuanceDTO);
        iPage.getRecords().forEach(ls -> {
            this.setDict(ls);
            if (StringUtils.isNotBlank(ls.getAppendixIds())){
                List<String> appendixIds = Arrays.asList(ls.getAppendixIds().split(","));
                List<HrAppendix> hrAppendixList = hrAppendixService.list(
                    new QueryWrapper<HrAppendix>().in(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(appendixIds), "id", appendixIds).eq("is_delete",0));
                if (!hrAppendixList.isEmpty()){
                    ls.setHrAppendixList(hrAppendixList);
                }
            }
        });
        return iPage;
    }

    private void handleRoleTemp(JWTUserDTO jwtUserDTO, HrCertificateIssuanceDTO hrCertificateIssuanceDTO) {
        Integer roleKey = null;
        String title = null;
        UserRoleTypeEnum enumByKey = EnumUtils.getEnumByKey(UserRoleTypeEnum.class, jwtUserDTO.getCurrentRoleKey());
        switch (enumByKey){
            case GENERAL_MANAGER:
                roleKey = 1;title = "户口迁出|落户|档案调函|暂住证";
                break;
            case CUSTOMER_SERVICE_STAFF:
                roleKey = 2;title = "在职|工龄认定";
                break;
            case DAIYUHEGUIZHUANYUAN:
                roleKey = 3;title = "退休|生育|工伤|死亡|职称|派遣制|未婚|初婚未育|介绍信";
                break;
            case FINANCIAL_DIRECTOR:
            case XINCHOUZHUANYUANB:
                roleKey = 4;title = "收入";
                break;
            default: break;
        }
        hrCertificateIssuanceDTO.setRoleKey(roleKey);
        hrCertificateIssuanceDTO.setCertificateTitle(title);
    }

    /**
     * 赋值字典值
     * @param hrCertificateIssuanceDTO
     */
    private void setDict(HrCertificateIssuanceDTO hrCertificateIssuanceDTO) {
        if (hrCertificateIssuanceDTO.getPersonnelType()!=null){
            hrCertificateIssuanceDTO.setPersonnelTypeLabel(StaffEnum.PersonnelTypeEnum.getValueByKey(hrCertificateIssuanceDTO.getPersonnelType()));
        }
        if (hrCertificateIssuanceDTO.getCertificateStatus()!=null){
            hrCertificateIssuanceDTO.setCertificateStatusLabel(CertificateTypeEnum.CertificateStatus.getValueByKey(hrCertificateIssuanceDTO.getCertificateStatus()));

        }
    }

    /**
     * 开具完成，线下签章
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity issueCompleteCertificate(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        User user = userRepository.selectById(jwtUserDTO.getId());
        for (String certificateId : batchOptDTO.getApplyIdList()) {
            HrCertificateIssuanceDTO certificateIssuanceDTO = hrCertificateIssuanceRepository.findById(certificateId);
            certificateIssuanceDTO.setCertificateStatus(CertificateTypeEnum.CertificateStatus.CERTIFICATE_ISSUED_COMPLETE.getKey());
            hrCertificateIssuanceRepository.updateById(hrCertificateIssuanceMapper.toEntity(certificateIssuanceDTO));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("certificateStatus",CertificateTypeEnum.CertificateStatus.CERTIFICATE_ISSUED_COMPLETE.getKey());
            String address = user.getWorkAddress() == null ? "我们办公地点" : user.getWorkAddress() + "领取";
            jsonObject.put("message","您的证明已开具完成。请到"+address + "。请点击下载");
            String message = jwtUserDTO.getRealName()+"通过线下签章开具了"+certificateIssuanceDTO.getName()+"的"+certificateIssuanceDTO.getCertificateName()+"证明。" +"####"+jsonObject.toJSONString();
            String appendix = null;
            if (batchOptDTO.getAppendixIdList()!=null && !batchOptDTO.getAppendixIdList().isEmpty()){
                appendix = String.join(",",batchOptDTO.getAppendixIdList());
            }
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(certificateIssuanceDTO.getId(),null,jwtUserDTO.getId(),message,null,false,appendix, ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey());
            this.hrNotificationUserService.saveRemindContent(certificateIssuanceDTO.getClientId(),ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey(),ServiceCenterEnum.CertificateIssuanceEnum.ISSUE_CERTIFICATE.getKey(),jwtUserDTO.getRealName()+"向"+certificateIssuanceDTO.getName()+"申请的"+certificateIssuanceDTO.getCertificateName()+"开具了证明(线下盖章)",jwtUserDTO.getId());
            //专管员下载模板，修改内容盖章，通过系统通知员工到公司来取，并上传扫描件作为留存（非必传），员工也可在小程序查看与下载这个扫描件
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey(),certificateIssuanceDTO.getId(),certificateIssuanceDTO.getStaffId(),String.valueOf(jsonObject.get("message")),true,ServiceCenterEnum.CERTIFICATE_ISSUANCE.getValue());
            HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(certificateIssuanceDTO.getStaffId());
            HashMap<Integer, String> params = new HashMap<>();
            params.put(1, hrTalentStaff.getName());
            params.put(2, LocalDate.now().plusDays(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            params.put(3, user.getWorkAddress() == null ? "公司" : user.getWorkAddress());
            this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.KIND_INFORM_USE.getTemplateCode(), hrTalentStaff.getPhone());

        }
        return ResponseUtil.buildSuccess();
    }

    /**
     * 员工查看证明开具
     * @return
     */
    @Override
    public List<HrCertificateIssuanceDTO> seeCertificateIssuance() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        //根据员工查询所有开具证明
        List<HrCertificateIssuance> hrCertificateIssuanceList = hrCertificateIssuanceRepository.selectList(new QueryWrapper<HrCertificateIssuance>()
            .eq("staff_id", jwtMiniDTO.getId()).orderByDesc("created_date"));
        return hrCertificateIssuanceMapper.toDto(hrCertificateIssuanceList);
    }

    /**
     * 导出证明开具
     *
     * @param hrCertificateIssuanceDTO
     * @param response
     * @return
     */
    @Override
    public String exportCertificateIssuance(HrCertificateIssuanceDTO hrCertificateIssuanceDTO, HttpServletResponse response) {
        if(CollectionUtils.isEmpty(hrCertificateIssuanceDTO.getClientIds())) {
            List<String> clientIds = hrClientService.selectClientIdByUserId();
            if (CollectionUtils.isEmpty(clientIds)) {
                clientIds.add("");
            }
            hrCertificateIssuanceDTO.setClientIds(clientIds);
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        this.handleRoleTemp(jwtUserDTO,hrCertificateIssuanceDTO);
        List<HrCertificateIssuanceDTO> hrCertificateIssuanceDTOList = hrCertificateIssuanceRepository.getCertificateIssuanceList(hrCertificateIssuanceDTO);
        if (hrCertificateIssuanceDTOList.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        hrCertificateIssuanceDTOList.forEach(this::setDict);
        List<String> ids = hrCertificateIssuanceDTOList.stream().map(HrCertificateIssuanceDTO::getId).collect(Collectors.toList());
        int listSize = hrCertificateIssuanceDTOList.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(hrCertificateIssuanceDTOList, "开具证明信息", HrCertificateIssuanceDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.CERTIFICATE_ISSUANCE.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 批量操作
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity batchPassCertificate(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String currentRoleKey = jwtUserDTO.getCurrentRoleKey();
        if (currentRoleKey.equals(UserRoleTypeEnum.GENERAL_MANAGER.getKey())
            || currentRoleKey.equals(UserRoleTypeEnum.CUSTOMER_SERVICE_STAFF.getKey())
            || currentRoleKey.equals(UserRoleTypeEnum.DAIYUHEGUIZHUANYUAN.getKey())
            || currentRoleKey.equals(UserRoleTypeEnum.FINANCIAL_DIRECTOR.getKey())
            || currentRoleKey.equals(UserRoleTypeEnum.XINCHOUZHUANYUANB.getKey())
        ){
            throw new CommonException("不能进行批量审核！");
        }
        List<String> errorStatusCustomerList = new ArrayList<>();//状态值不是待客户审核的错误集合
        List<String> errorStatusManagerList = new ArrayList<>();//状态值不是待客户审核的错误集合
        List<String> errorStatusList = new ArrayList<>();//状态值错误集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String certificateId : batchOptDTO.getApplyIdList()) {
            String message = "";
            String roleName = "";
            Integer certificateStatus = null;
            Integer certificateIssuanceEnum = null;
            Integer agencyType = null;
            String title = "";
            String msg = "";
            HrCertificateIssuanceDTO certificateIssuanceDTO = hrCertificateIssuanceRepository.findById(certificateId);
            //判断角色
            switch (batchOptDTO.getRoleId()){
                case 1://客户
                    roleName = "客户";
                    agencyType = 2;
                    title = "证明开具-审核" + certificateIssuanceDTO.getName() + "的"+certificateIssuanceDTO.getCertificateName();
                    msg = "等待经理审核";
                    certificateIssuanceEnum = ServiceCenterEnum.CertificateIssuanceEnum.CUSTOMER_REVIEW.getKey();
                    if (certificateIssuanceDTO.getCertificateStatus() != CertificateTypeEnum.CertificateStatus.TO_BE_REVIEWED.getKey()){
                        errorStatusCustomerList.add(certificateIssuanceDTO.getCertificateName());
                        continue;
                    }else {
                        if (batchOptDTO.getOpt()){
                            certificateStatus = CertificateTypeEnum.CertificateStatus.MANAGER_REVIEW.getKey();
                        }else {
                            certificateStatus = CertificateTypeEnum.CertificateStatus.CUSTOMER_REJECTED.getKey();
                        }
                    }
                    break;
                case 2:
                    roleName = "经理";
                    agencyType = 0;
                    title = "证明开具-对" + certificateIssuanceDTO.getName() + "的"+certificateIssuanceDTO.getCertificateName()+"进行电子盖章或线下盖章并上传附件留存";
                    msg = "等待专管员开具证明";
                    certificateIssuanceEnum = ServiceCenterEnum.CertificateIssuanceEnum.MANAGER_REVIEW.getKey();
                    if (certificateIssuanceDTO.getCertificateStatus() != CertificateTypeEnum.CertificateStatus.MANAGER_REVIEW.getKey()){
                        errorStatusManagerList.add(certificateIssuanceDTO.getCertificateName());
                        continue;
                    }else {
                        if (batchOptDTO.getOpt()){
                            certificateStatus = CertificateTypeEnum.CertificateStatus.TO_BE_CERTIFICATE_ISSUED.getKey();
                        }else {
                            certificateStatus = CertificateTypeEnum.CertificateStatus.MANAGER_REJECTED.getKey();
                        }
                    }
                    break;
                default:
                    if (certificateIssuanceDTO.getCertificateStatus() == CertificateTypeEnum.CertificateStatus.TO_BE_REVIEWED.getKey()){
                        agencyType = 2;
                        title = "证明开具-审核" + certificateIssuanceDTO.getName() + "的"+certificateIssuanceDTO.getCertificateName();
                        msg = "等待经理审核";
                        certificateIssuanceEnum = ServiceCenterEnum.CertificateIssuanceEnum.CUSTOMER_REVIEW.getKey();
                        if (batchOptDTO.getOpt()){
                            certificateStatus = CertificateTypeEnum.CertificateStatus.MANAGER_REVIEW.getKey();
                        }else {
                            certificateStatus = CertificateTypeEnum.CertificateStatus.CUSTOMER_REJECTED.getKey();
                        }
                    }else if (certificateIssuanceDTO.getCertificateStatus() == CertificateTypeEnum.CertificateStatus.MANAGER_REVIEW.getKey()){
                        agencyType = 0;
                        title = "证明开具-对" + certificateIssuanceDTO.getName() + "的"+certificateIssuanceDTO.getCertificateName()+"进行电子盖章或线下盖章并上传附件留存";
                        msg = "等待专管员开具证明";
                        certificateIssuanceEnum = ServiceCenterEnum.CertificateIssuanceEnum.MANAGER_REVIEW.getKey();
                        if (batchOptDTO.getOpt()){
                            certificateStatus = CertificateTypeEnum.CertificateStatus.TO_BE_CERTIFICATE_ISSUED.getKey();
                        }else {
                            certificateStatus = CertificateTypeEnum.CertificateStatus.MANAGER_REJECTED.getKey();
                        }
                    }else{
                        errorStatusList.add(certificateIssuanceDTO.getCertificateName());
                        continue;
                    }
                    break;
            }
            certificateIssuanceDTO.setCertificateStatus(certificateStatus);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("certificateStatus",certificateStatus);
            if (batchOptDTO.getOpt()){//审批通过
                jsonObject.put("message","您的证明开具申请由"+jwtUserDTO.getRoleName()+"审核通过");
                message = jwtUserDTO.getRealName()+"同意了"+certificateIssuanceDTO.getName()+"的"+certificateIssuanceDTO.getCertificateName()+"证明开具申请。####"+jsonObject.toJSONString();
                hrUpcomingService.createServiceUpcoming(certificateIssuanceDTO.getId(),certificateIssuanceDTO.getStaffId(), title,LocalDate.now(),agencyType);
            }else {
                jsonObject.put("message","您的证明开具申请由"+jwtUserDTO.getRoleName()+"审核拒绝，拒绝原因："+batchOptDTO.getCheckerReason());
                message = jwtUserDTO.getRealName()+"拒绝了"+certificateIssuanceDTO.getName()+"的"+certificateIssuanceDTO.getCertificateName()+"证明开具申请。拒绝原因:"+batchOptDTO.getCheckerReason()+"####"+jsonObject.toJSONString();
                hrUpcomingService.updateUpcoming(certificateIssuanceDTO.getId());
            }
            hrCertificateIssuanceRepository.updateById(hrCertificateIssuanceMapper.toEntity(certificateIssuanceDTO));
            successList.add(certificateIssuanceDTO.getCertificateName());
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(certificateIssuanceDTO.getId(),null,jwtUserDTO.getId(),message,batchOptDTO.getRemark(),false,null, ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey());
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey(),certificateIssuanceDTO.getId(),certificateIssuanceDTO.getStaffId(),String.valueOf(jsonObject.get("message")),false,ServiceCenterEnum.CERTIFICATE_ISSUANCE.getValue());
            this.hrNotificationUserService.saveRemindContent(certificateIssuanceDTO.getClientId(),ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey(),certificateIssuanceEnum,jwtUserDTO.getRealName()+"审核了"+certificateIssuanceDTO.getCertificateName()+"的开具证明申请。"+msg,jwtUserDTO.getId());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success",successList);
        if (CollectionUtils.isNotEmpty(errorStatusList)){
            jsonObject.put("error_status","选择的数据中证明名称是"+String.join(",",errorStatusList)+"不是需要审核的流程");
        }
        if (CollectionUtils.isNotEmpty(errorStatusCustomerList)){
            jsonObject.put("error_status","选择的数据中证明名称是"+String.join(",",errorStatusCustomerList)+"不是需要客户审核的流程");
        }
        if (CollectionUtils.isNotEmpty(errorStatusManagerList)){
            jsonObject.put("error_status","选择的数据中证明名称是"+String.join(",",errorStatusManagerList)+"不是需要经理审核的流程");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 专管员审核
     * @param batchOptDTO
     * @return
     */
    @Override
    public HrCertificateIssuanceDTO reviewedSpecialSupervisor(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrCertificateIssuanceDTO hrCertificateIssuanceDTO = hrCertificateIssuanceRepository.findById(batchOptDTO.getApplyId());
        JSONObject jsonObject = new JSONObject();
        String message = "";
        Integer certificateStatus = null;
        if (batchOptDTO.getOpt()){
            Integer type = 0;
            if (batchOptDTO.getIsNeed().equals(ServiceCenterEnum.ContractViewNeedEnum.NEED.getKey())){
                type = 1;
                certificateStatus = CertificateTypeEnum.CertificateStatus.TO_BE_REVIEWED.getKey();
                Role role = roleRepository.selectOneForKey(UserRoleTypeEnum.CLIENT.getKey());
                HrMessageListDTO messageListDTO = new HrMessageListDTO()
                    .setContentType(2)
                    .setContent(PcMessageContentEnum.CERTIFICATE_ISSUANCE.getKey())
                    .setTitle("审核"+hrCertificateIssuanceDTO.getName()+"的证明开具申请")
                    .setCreatedById(hrCertificateIssuanceDTO.getUserId())
                    .setRecipientRoleIds(String.valueOf(role.getId()))
                    .setNoticeUserIds(Collections.singletonList(hrCertificateIssuanceDTO.getUserId()))
                    ;
                HrMessageListDTO hrMessageList = this.hrMessageListService.createHrMessageList(messageListDTO);
                HrMessageRole hrMessageRole = new HrMessageRole();
                hrMessageRole.setMessageId(hrMessageList.getId());
                hrMessageRole.setUserId(hrCertificateIssuanceDTO.getUserId());
                hrMessageRoleRepository.insert(hrMessageRole);
            }else {
                type = 2;
                certificateStatus = CertificateTypeEnum.CertificateStatus.MANAGER_REVIEW.getKey();
            }
            jsonObject.put("message","您的证明开具申请由"+jwtUserDTO.getRoleName()+"审核通过");
            message = jwtUserDTO.getRealName()+"同意了"+hrCertificateIssuanceDTO.getName()+"的"+hrCertificateIssuanceDTO.getCertificateName()+"证明开具申请。####"+jsonObject.toJSONString();
            hrUpcomingService.createServiceUpcoming(hrCertificateIssuanceDTO.getId(),hrCertificateIssuanceDTO.getStaffId(), "证明开具-审核" + hrCertificateIssuanceDTO.getName() + "的证明开具申请",LocalDate.now(),type);
        }else {
            certificateStatus = CertificateTypeEnum.CertificateStatus.TO_BE_MANAGER_REVIEWED_REJECTED.getKey();
            jsonObject.put("message","您的证明开具申请由"+jwtUserDTO.getRoleName()+"审核拒绝，拒绝原因："+batchOptDTO.getCheckerReason());
            message = jwtUserDTO.getRealName()+"拒绝了"+hrCertificateIssuanceDTO.getName()+"的"+hrCertificateIssuanceDTO.getCertificateName()+"证明开具申请。拒绝原因:"+batchOptDTO.getCheckerReason()+"####"+jsonObject.toJSONString();
            hrUpcomingService.updateUpcoming(hrCertificateIssuanceDTO.getId());
        }
        hrCertificateIssuanceRepository.updateCertificateStatus(certificateStatus,batchOptDTO.getIsNeed(),hrCertificateIssuanceDTO.getId());
        jsonObject.put("certificateStatus", certificateStatus);
        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrCertificateIssuanceDTO.getId(),null,jwtUserDTO.getId(),message,batchOptDTO.getRemark(),false,null, ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey());
        this.hrNotificationUserService.saveRemindContent(hrCertificateIssuanceDTO.getClientId(),ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey(), ServiceCenterEnum.CertificateIssuanceEnum.SPECIAL_SUPERVISOR_REVIEW.getKey(),jwtUserDTO.getRealName()+"审核了"+hrCertificateIssuanceDTO.getCertificateName()+"的开具证明申请。",jwtUserDTO.getId());
        return hrCertificateIssuanceDTO;
    }

    /**
     * 预处理证明模板文件
     * @param hrCertificateIssuanceDTO
     * @return/hr-selectclients
     */
    @Override
    public Map<String, Object> proofTemplatePretreatment(HrCertificateIssuanceDTO hrCertificateIssuanceDTO) {
        HrCertificateIssuance hrCertificateIssuance = hrCertificateIssuanceRepository.selectById(hrCertificateIssuanceDTO.getId());
        //获取证明模板附件
        HrProofTemplateDTO hrProofTemplateDTO = hrProofTemplateRepository.findById(hrCertificateIssuance.getAppendixIds());
        if (hrProofTemplateDTO == null){
            throw new CommonException("未查询到相关证明模板！");
        }
        hrProofTemplateDTO.setUseNum(hrProofTemplateDTO.getUseNum() + 1);

        hrProofTemplateRepository.updateUseNum(hrProofTemplateDTO.getUseNum(),hrProofTemplateDTO.getId());
        Map<String, Object> formFieldList = hrProofTemplateService.proofTemplatePretreatment(hrProofTemplateDTO,hrCertificateIssuance.getStaffId());
        HrAppendix hrAppendix = this.makeTemplate(formFieldList);
        hrCertificateIssuance.setFillInfo(JSON.toJSONString(formFieldList.get("inputLabel")));
        hrCertificateIssuance.setAppendixId(hrAppendix.getId());
        hrCertificateIssuance.setAppendixPath(hrAppendix.getFileUrl());
        hrCertificateIssuanceRepository.updateById(hrCertificateIssuance);
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("result", hrCertificateIssuanceRepository.selectById(hrCertificateIssuanceDTO.getId()));
        return result;
    }

    /**
     * 一键生成证明模板
     * @param params
     * @return
     */
    @Override
    public HrAppendix makeTemplate(Map<String, Object> params) {
        String templateId = String.valueOf(params.get("templateId"));
        if (StringUtils.isBlank(templateId)) {
            throw new CommonException("模板id不能为空！");
        }
        HrProofTemplateDTO hrProofTemplateDTO = hrProofTemplateRepository.findById(templateId);
        Map<String, Object> templateMap = new LinkedHashMap<>();
        // 判断表单域
        if (params.get("inputLabel") != null) {
            List<InputLabel> labels = new LinkedList<>();
            Map<String, Object> tempMap;
            // 获取表单信息(前台后台的入参不同，前台为map，后台为list)
            try {
                labels = JSON.parseArray(JSON.toJSONString(params.get("inputLabel")), InputLabel.class);
            } catch (Exception e) {
                // param中的参数为前台参数，需要将map处理为list
                tempMap = JSON.parseObject(JSON.toJSONString(params.get("inputLabel")), Map.class);
                for (Map.Entry<String, Object> entry : tempMap.entrySet()) {
                    InputLabel inputLabel = new InputLabel();
                    inputLabel.setName(entry.getKey());
                    inputLabel.setValue(entry.getValue());
                    labels.add(inputLabel);
                }
            }
            // 处理表单信息 添加时间等附属信息
            labels.forEach(ls -> {
                String name = ls.getName();
                String value = String.valueOf(ls.getValue());
                // 日期类表单处理
                switch (name) {
                    case "contractStartDate":
                        if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                            LocalDate date = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            templateMap.put("contractStartYears", date.getYear());
                            templateMap.put("contractStartMonth", date.getMonthValue());
                            templateMap.put("contractStartDay", date.getDayOfMonth());
                        }
                        break;
                    case "contractEndDate":
                        if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                            LocalDate date = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            templateMap.put("contractEndYears", date.getYear());
                            templateMap.put("contractEndMonth", date.getMonthValue());
                            templateMap.put("contractEndDay", date.getDayOfMonth());
                        }
                        break;
                    case "stampDate":
                        if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                            LocalDate date1 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            templateMap.put("stampYears", date1.getYear());
                            templateMap.put("stampMonth", date1.getMonthValue());
                            templateMap.put("stampDay", date1.getDayOfMonth());
                        }
                        break;
                    case "birthdayDate":
                        if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                            LocalDate date2 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            templateMap.put("birthdayYears", date2.getYear());
                            templateMap.put("birthdayMonth", date2.getMonthValue());
                            templateMap.put("birthdayDay", date2.getDayOfMonth());
                        }
                        break;
                    case "tenureDate":
                        if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                            LocalDate date3 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            templateMap.put("tenureYears", date3.getYear());
                            templateMap.put("tenureMonth", date3.getMonthValue());
                            templateMap.put("tenureDay", date3.getDayOfMonth());
                        }
                        break;
                    case "travelDate":
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(value) && !"null".equals(value)) {
                            LocalDate date4 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            templateMap.put("travelYears", date4.getYear());
                            templateMap.put("travelMonth", date4.getMonthValue());
                            templateMap.put("travelDay", date4.getDayOfMonth());
                        }
                        break;
                    case "marryDate":
                        if (StringUtils.isNotBlank(value) && !"null".equals(value)) {
                            LocalDate date5 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            templateMap.put("marryYears", date5.getYear());
                            templateMap.put("marryMonth", date5.getMonthValue());
                            templateMap.put("marryDay", date5.getDayOfMonth());
                        }
                        break;
                    case "resignationDate":
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(value) && !"null".equals(value)) {
                            LocalDate date6 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            templateMap.put("resignationYears", date6.getYear());
                            templateMap.put("resignationMonth", date6.getMonthValue());
                            templateMap.put("resignationDay", date6.getDayOfMonth());
                        }
                        break;
                    case "applyDate":
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(value) && !"null".equals(value)) {
                            LocalDate date7 = LocalDate.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            templateMap.put("applyYears", date7.getYear());
                            templateMap.put("applyMonth", date7.getMonthValue());
                            templateMap.put("applyDay", date7.getDayOfMonth());
                        }
                        break;
                    default:
                        templateMap.put(name, value);
                }
            });
        }
        // 填充内容到PDF
        String tempPdfPath = PdfUtils.fillTextOrImageToPdf(templateMap, hrProofTemplateDTO.getFileUrl(), tempPath, true);
        if (StringUtils.isBlank(tempPdfPath)) {
            throw new CommonException("PDF文件制作异常！");
        }
        // 上传到文件服务器
        return this.hrAppendixService.uploadTempPdf(tempPdfPath);
    }

    /**
     * 上传文档，电子盖章
     * @param params
     * @return
     */
    @Override
    public HrAppendix uploadDocumentFile(Map<String, Object> params) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String appendId = (String) params.get("appendId");
        String certificateId = (String) params.get("id");
        String fillInfoData = (String) params.get("fillInfo");
        HrCertificateIssuanceDTO certificateIssuanceDTO = hrCertificateIssuanceRepository.findById(certificateId);
        certificateIssuanceDTO.setCertificateStatus(CertificateTypeEnum.CertificateStatus.CERTIFICATE_ISSUED_COMPLETE.getKey());
        certificateIssuanceDTO.setFillInfo(fillInfoData);

        HrAppendixDTO hrAppendixDTO = hrAppendixService.getHrAppendix(appendId);
        // 检查并生成用户个人证书
        String contractNum = RandomUtil.generateContractNo();

        this.eCloudComponent.uploadContractUrl(contractNum, hrAppendixDTO.getOriginName(), hrAppendixDTO.getFileUrl());
        // 将甲方签章签署到合同文件
        Map<String, Object> fillInfo = JSONObject.parseObject(fillInfoData);
        String signId = String.valueOf(fillInfo.get("seal"));
        HrProofTemplateDTO hrProofTemplateDTO = hrProofTemplateRepository.findById(certificateIssuanceDTO.getAppendixIds());
        this.eCloudComponent.electronicSignature(contractNum, params,hrProofTemplateDTO.getFileUrl(),signId);
        HrAppendix hrAppendix = this.eCloudComponent.downloadContractFile(contractNum);
        certificateIssuanceDTO.setContractNum(contractNum);
        certificateIssuanceDTO.setAppendixId(hrAppendix.getId());
        certificateIssuanceDTO.setAppendixPath(hrAppendix.getFileUrl());
        hrCertificateIssuanceRepository.updateById(hrCertificateIssuanceMapper.toEntity(certificateIssuanceDTO));
        //添加操作信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("certificateStatus",CertificateTypeEnum.CertificateStatus.CERTIFICATE_ISSUED_COMPLETE.getKey());
        jsonObject.put("message","您的证明已开具完成。请点击下载");

        String message = jwtUserDTO.getRealName()+"通过线上签章开具了"+certificateIssuanceDTO.getName()+"的"+certificateIssuanceDTO.getCertificateName()+"证明。####"+jsonObject;
        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(certificateIssuanceDTO.getId(),null,jwtUserDTO.getId(),message,null,false,hrAppendix.getId(), ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey());
        this.hrNotificationUserService.saveRemindContent(certificateIssuanceDTO.getClientId(),ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey(),ServiceCenterEnum.CertificateIssuanceEnum.ISSUE_CERTIFICATE.getKey(),jwtUserDTO.getRealName()+"向"+certificateIssuanceDTO.getName()+"申请的"+certificateIssuanceDTO.getCertificateName()+"开具了证明(电子签章)",jwtUserDTO.getId());
        hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.CERTIFICATE_ISSUANCE.getKey(),certificateIssuanceDTO.getId(),certificateIssuanceDTO.getStaffId(),String.valueOf(jsonObject.get("message")),true,ServiceCenterEnum.CERTIFICATE_ISSUANCE.getValue());
        return hrAppendixMapper.toEntity(hrAppendixDTO);
    }
}
