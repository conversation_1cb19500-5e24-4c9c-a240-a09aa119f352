<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrSpecialDeductionRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , client_id, talent_staff_Id, start_date, end_date, children_education, continuing_education, housing_loan, housing_rent, support_elderly, other,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrSpecialDeduction">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="talent_staff_Id" property="talentStaffId"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="children_education" property="childrenEducation"/>
        <result column="continuing_education" property="continuingEducation"/>
        <result column="housing_loan" property="housingLoan"/>
        <result column="housing_rent" property="housingRent"/>
        <result column="support_elderly" property="supportElderly"/>
        <result column="other" property="other"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getSumCurrentIncome" resultType="java.math.BigDecimal">
        SELECT
            SUM( current_income )
        FROM
            hr_special_deduction
        WHERE
            is_delete = 0
          <if test="clientId!=null and clientId!=''">
              AND client_id = #{clientId}
          </if>
          AND start_date = #{startDate}
    </select>

    <select id="getByStaffIdAndStartDate" resultType="cn.casair.domain.HrSpecialDeduction">
        SELECT
            hsd.id,
            hsd.client_id,
            talent_staff_Id,
            start_date,
            end_date,
            children_education,
            continuing_education,
            housing_loan,
            housing_rent,
            support_elderly,
            other,
            job_number,
            income_item,
            current_income,
            cost,
            duty_free,
            pension,
            medical_treatment,
            unemployment,
            provident_fund,
            enterprise_occupational_annuity,
            business_health,
            tax_deferred_pension,
            other_deductions,
            cumulative_income,
            tax_free_income,
            subtract,
            special_deduction,
            allowable_donations,
            taxable_income,
            tax_rate,
            quick_deduction,
            payable,
            tax_deduction,
            tax_withholding,
            prepaid_tax,
            tax_should_be_paid_refunded,
            cumulative_infant_care
        FROM
            hr_special_deduction hsd
            LEFT JOIN hr_talent_staff hts ON hsd.talent_staff_Id = hts.id
        WHERE
            hsd.is_delete = 0
          AND hts.certificate_num = #{certificateNum}
          AND hsd.start_date = #{startDate}
          ORDER BY hsd.created_date DESC LIMIT 1
    </select>

    <sql id="Find_SQL">
        SELECT hsd.*,
        hc.client_name as clientName,
        hts.system_num as systemNum,
        hts.`name` as talentName,
        hts.staff_status,
        hts.certificate_type AS certificateTypeKey,
        hts.certificate_num AS certificateNum,
        hc.unit_number,
        hts.certificate_type
        FROM `hr_special_deduction` hsd
        LEFT JOIN hr_client hc on hc.id = hsd.client_id
        LEFT JOIN hr_talent_staff hts on hts.id = hsd.talent_staff_Id
        WHERE hc.is_delete=0 and hsd.is_delete=0 AND hts.is_delete=0
        <if test="param1.talentName!=null and param1.talentName!=''">
            and hts.`name` like concat('%',#{param1.talentName},'%')
        </if>
        <if test="param1.certificateNum!=null and param1.certificateNum!=''">
            and hts.certificate_num like concat('%',#{param1.certificateNum},'%')
        </if>
        <if test="param1.ids!=null and param1.ids.size() > 0">
            AND hsd.id IN
            <foreach collection="param1.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.clientIdList!=null and param1.clientIdList.size() > 0">
            AND hc.id IN
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.contractStartDateStart!=null ">
            AND  hsd.start_date <![CDATA[ >= ]]> #{param1.contractStartDateStart}
        </if>
        <if test="param1.contractStartDateEnd!=null ">
            AND hsd.start_date <![CDATA[ <= ]]> #{param1.contractStartDateEnd}
        </if>
        <if test="param1.contractEndDateStart!=null ">
            AND hsd.end_date <![CDATA[ >= ]]> #{param1.contractEndDateStart}
        </if>
        <if test="param1.contractEndDateEnd!=null ">
            AND hsd.end_date <![CDATA[ <= ]]>#{param1.contractEndDateEnd}
        </if>
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </sql>

    <select id="Page" resultType="cn.casair.dto.HrSpecialDeductionDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="selectClientId" resultType="cn.casair.dto.excel.HrSpecialDeductionExport">
        SELECT * FROM `hr_special_deduction` where id=#{id} and is_delete=0
    </select>

    <select id="getByBillIdAndStartDate" resultType="cn.casair.dto.HrSpecialDeductionDTO">
        SELECT
            d.*
            ,ts.certificate_num
        FROM hr_special_deduction d
                 LEFT JOIN hr_talent_staff ts ON d.talent_staff_Id = ts.id
        WHERE d.talent_staff_Id IN (
            SELECT
                hl.staff_id
            FROM hr_bill_detail hl
            WHERE hl.bill_id = #{billId}
              AND hl.is_delete = 0
        )
          AND d.start_date = #{startDate}
    </select>

    <select id="findList" resultType="cn.casair.dto.excel.HrSpecialDeductionExport">
        <include refid="Find_SQL"/>
    </select>

    <select id="getAllByStartDate" resultType="cn.casair.domain.HrSpecialDeduction">
         SELECT
            hts.certificate_num,
            hsd.id,
            hsd.client_id,
            talent_staff_Id,
            start_date,
            end_date,
            children_education,
            continuing_education,
            housing_loan,
            housing_rent,
            support_elderly,
            other,
            job_number,
            income_item,
            current_income,
            cost,
            duty_free,
            pension,
            medical_treatment,
            unemployment,
            provident_fund,
            enterprise_occupational_annuity,
            business_health,
            tax_deferred_pension,
            other_deductions,
            cumulative_income,
            tax_free_income,
            subtract,
            special_deduction,
            allowable_donations,
            taxable_income,
            tax_rate,
            quick_deduction,
            payable,
            tax_deduction,
            tax_withholding,
            prepaid_tax,
            tax_should_be_paid_refunded,
            cumulative_infant_care
        FROM
            hr_special_deduction hsd
        LEFT JOIN hr_talent_staff hts ON hsd.talent_staff_Id = hts.id
        LEFT JOIN hr_client hc on hc.id = hsd.client_id
        WHERE
            hsd.is_delete = 0 AND hts.is_delete = 0 AND hc.is_delete = 0
          AND hsd.start_date >= #{startDate} AND hsd.start_date <![CDATA[ <= ]]> #{endDate}
          ORDER BY hsd.created_date
    </select>

    <select id="getSumCurrentIncomeGroupClient" resultType="java.util.Map">
        SELECT
            client_id,
            SUM( current_income )
        FROM
            hr_special_deduction
        WHERE
            is_delete = 0
            AND client_id IS NOT NULL
            AND client_id != ''
            AND start_date = #{startDate}
        GROUP BY client_id
    </select>
</mapper>
