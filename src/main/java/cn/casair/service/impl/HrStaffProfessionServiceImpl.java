package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.domain.HrStaffProfession;
import cn.casair.dto.HrStaffProfessionDTO;
import cn.casair.mapper.HrStaffProfessionMapper;
import cn.casair.repository.HrStaffProfessionRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrStaffProfessionService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
/**
 * 专业技能服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrStaffProfessionServiceImpl extends ServiceImpl<HrStaffProfessionRepository, HrStaffProfession>implements HrStaffProfessionService {

    private static final Logger log=LoggerFactory.getLogger(HrStaffProfessionServiceImpl.class);

    private final HrStaffProfessionRepository hrStaffProfessionRepository;

    private final HrStaffProfessionMapper hrStaffProfessionMapper;

    private final CodeTableService codeTableService;

    private final SysOperLogService sysOperLogService;

    public HrStaffProfessionServiceImpl(HrStaffProfessionRepository hrStaffProfessionRepository, HrStaffProfessionMapper hrStaffProfessionMapper, CodeTableService codeTableService, SysOperLogService sysOperLogService){
        this.hrStaffProfessionRepository = hrStaffProfessionRepository;
        this.hrStaffProfessionMapper= hrStaffProfessionMapper;
        this.codeTableService= codeTableService;
        this.sysOperLogService = sysOperLogService;
    }

    /**
     * 创建专业技能
     * @param hrStaffProfessionDTO
     * @return
     */
    @Override
    public List<HrStaffProfessionDTO> createHrStaffProfession(HrStaffProfessionDTO hrStaffProfessionDTO){
        log.info("Create new HrStaffProfession:{}", hrStaffProfessionDTO);

        HrStaffProfession hrStaffProfession =this.hrStaffProfessionMapper.toEntity(hrStaffProfessionDTO);
        this.hrStaffProfessionRepository.insert(hrStaffProfession);
        // 操作日志
        setDict(hrStaffProfessionDTO);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_PROFESSION.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffProfessionDTO),
            HrStaffProfessionDTO.class,
            null,
            JSON.toJSONString(hrStaffProfession)
        );
        return this.findProfessionList(hrStaffProfessionDTO.getStaffId());
    }

    /**
     * 修改专业技能
     * @param hrStaffProfessionDTO
     * @return
     */
    @Override
    public Optional<List<HrStaffProfessionDTO>> updateHrStaffProfession(HrStaffProfessionDTO hrStaffProfessionDTO){
        return Optional.ofNullable(this.hrStaffProfessionRepository.selectById(hrStaffProfessionDTO.getId()))
        .map(roleTemp->{
            HrStaffProfession hrStaffProfession =this.hrStaffProfessionMapper.toEntity(hrStaffProfessionDTO);
            this.hrStaffProfessionRepository.updateById(hrStaffProfession);
            log.info("Update HrStaffProfession:{}", hrStaffProfessionDTO);
            // 操作日志
            setDict(hrStaffProfessionDTO);
            HrStaffProfessionDTO dto = hrStaffProfessionMapper.toDto(roleTemp);
            setDict(dto);
            this.sysOperLogService.insertSysOperLog(
                ModuleTypeEnum.STAFF_PROFESSION.getValue(),
                BusinessTypeEnum.UPDATE.getKey(),
                JSON.toJSONString(hrStaffProfessionDTO),
                HrStaffProfessionDTO.class,
                null,
                JSON.toJSONString(dto),
                JSON.toJSONString(hrStaffProfessionDTO),
                null,
                HrStaffProfessionDTO.class
            );
            return this.findProfessionList(hrStaffProfessionDTO.getStaffId());
        });
    }

    /**
     * 查询专业技能
     * @param staffId 员工ID
     * @return
     */
    @Override
    public List<HrStaffProfessionDTO> findProfessionList(String staffId) {
        List<HrStaffProfessionDTO> hrStaffProfessionDTOS = this.hrStaffProfessionMapper.toDto(
            hrStaffProfessionRepository.selectList(new QueryWrapper<HrStaffProfession>().eq("staff_id", staffId).eq("is_delete", 0)));
        hrStaffProfessionDTOS.forEach(this::setDict);
        return hrStaffProfessionDTOS;
    }

    /**
     * 赋值字典值
     * @param hrStaffProfessionDTO
     */
    public void setDict(HrStaffProfessionDTO hrStaffProfessionDTO) {
        if (hrStaffProfessionDTO.getSkillProficiency() != null){
            Map<Integer, String> proficiency = codeTableService.findCodeTableByInnerName("proficiency");//熟练程度
            hrStaffProfessionDTO.setSkillProficiencyLabel(proficiency.get(hrStaffProfessionDTO.getSkillProficiency()));
        }
    }

    /**
     * 查询专业技能详情
     * @param id
     * @return
     */
    @Override
    public HrStaffProfessionDTO getHrStaffProfession(String id){
        log.info("Get HrStaffProfession :{}",id);

        HrStaffProfession hrStaffProfession =this.hrStaffProfessionRepository.selectById(id);
        HrStaffProfessionDTO hrStaffProfessionDTO = this.hrStaffProfessionMapper.toDto(hrStaffProfession);
        this.setDict(hrStaffProfessionDTO);
        return hrStaffProfessionDTO;
    }

    /**
     * 删除专业技能
     * @param id
     */
    @Override
    public List<HrStaffProfessionDTO> deleteHrStaffProfession(String id){
        HrStaffProfession hrStaffProfession = this.hrStaffProfessionRepository.selectById(id);
        this.hrStaffProfessionRepository.deleteById(id);
        log.info("Delete HrStaffProfession:{}", hrStaffProfession);
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.STAFF_PROFESSION.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除" + ModuleTypeEnum.STAFF_PROFESSION.getValue()+": "+ hrStaffProfession.getSkillName(),
            null,
            null,
            null,
            JSON.toJSONString(hrStaffProfession),
            null
        );
        return this.findProfessionList(hrStaffProfession.getStaffId());
    }

}
