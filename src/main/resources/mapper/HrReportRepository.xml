<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrReportRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_id, report_name, report_type, appendix_id, client_id, `status`, denial_reason,
        is_delete ,created_by ,created_date ,last_modified_by ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrReport">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="report_name" property="reportName"/>
        <result column="report_type" property="reportType"/>
        <result column="appendix_id" property="appendixId"/>
        <result column="client_id" property="clientId"/>
        <result column="status" property="status"/>
        <result column="denial_reason" property="denialReason"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getClientInfoChange" resultType="cn.casair.dto.report.ClientInfoChangeDTO">
        WITH t1 AS (
            SELECT
            DATE_FORMAT( c.created_date, '%Y-%m' ) dateStr,
            COUNT( 1 ) num
            FROM
            hr_client c
            GROUP BY
            DATE_FORMAT( c.created_date, '%Y-%m' )),
        t3 AS (
            SELECT
            DATE_FORMAT( p.agreement_end_date, '%Y-%m' ) dateStr,
            COUNT( 1 ) reduceNum
            FROM
            hr_protocol p
            WHERE
            p.states = 2
            AND p.renew_type = 0
            GROUP BY
            DATE_FORMAT( p.agreement_end_date, '%Y-%m' )
        )
        SELECT
            <if test="params.exhibitionType==1">
                t1.dateStr,
            </if>
            <if test="params.exhibitionType==2">
                CONCAT( LEFT ( t1.dateStr, 4 ), '年第', FLOOR ( ( RIGHT ( t1.dateStr, 2 ) + 2 ) / 3 ), '季度' ) dateStr,
            </if>
            <if test="params.exhibitionType==3">
                CONCAT( LEFT ( t1.dateStr, 4 ), '年', IF ( RIGHT ( t1.dateStr, 2 ) &lt; 7, '上', '下' ), '半年度' ) dateStr,
            </if>
            <if test="params.exhibitionType==4">
                LEFT( t1.dateStr, 4 ) dateStr,
            </if>
            SUM( t2.num ) clientCount,
            t1.num increasedCount,
            t3.reduceNum reducedCount
        FROM t1
        LEFT JOIN t1 t2 ON t1.dateStr >= t2.dateStr
        LEFT JOIN t3 ON t1.dateStr = t3.dateStr
        <where>
            <if test="params.exhibitionDateStart!=null and params.exhibitionDateStart!=''">
                AND t1.dateStr &gt;= #{params.exhibitionDateStart}
            </if>
            <if test="params.exhibitionDateEnd!=null and params.exhibitionDateEnd!=''">
                AND t1.dateStr &lt;= #{params.exhibitionDateEnd}
            </if>
        </where>
        GROUP BY
            t1.dateStr
        ORDER BY
            t1.dateStr DESC
    </select>

    <sql id="Find_SQL">
        SELECT
        hr.id as id,
        hr.created_date as createdDate,
        hr.status as status,
        hr.appendix_id as appendixId,
        hr.report_name as reportName,
        hr.staff_id as staffId,
        hr.report_type as reportType,
        ht.`name` as `name`,
        ht.certificate_num as certificateNum,
        ht.phone AS phone,
        hr. denial_reason as denialReason,
        ht.staff_status as staffStatus,
        hc.client_name as clientName,
        ht.personnel_type as personnelType
        FROM
        `hr_report` hr
        LEFT JOIN hr_talent_staff ht on hr.staff_id=ht.id
        LEFT JOIN hr_client hc on hc.id=hr.client_id
        where
        hc.is_delete=0 AND ht.is_delete = 0
        <if test="param1.clientIdList!=null and param1. clientIdList.size() > 0">
            and hr.client_id in
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.ids!=null and param1. ids.size() > 0">
            and hr.id in
            <foreach collection="param1.ids" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.staffId!=null and param1.staffId !=''">
            and hr.staff_id=#{param1.staffId}
        </if>
        <if test="param1.name!=null and param1.name !=''">
            and ht.name like concat('%',#{param1.name},'%')
        </if>
        <if test="param1.certificateNum!=null and param1.certificateNum !=''">
            and ht.certificate_num like concat('%',#{param1.certificateNum},'%')
        </if>
        <if test="param1.phone!=null and param1.phone !=''">
            and ht.phone like concat('%',#{param1.phone},'%')
        </if>

        <if test="param1.staffStatusList!=null and param1. staffStatusList.size() > 0">
            and ht.staff_status in
            <foreach collection="param1.staffStatusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.personnelTypeList!=null and param1. personnelTypeList.size() > 0">
            and ht.personnel_type in
            <foreach collection="param1.personnelTypeList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>


        <if test="param1.reportName!=null and param1.reportName !=''">
            and hr.report_name like concat('%',#{param1.reportName},'%')
        </if>
        <if test="param1.reportTypeList!=null and param1.reportTypeList.size() > 0">
            and hr.report_type in
            <foreach collection="param1.reportTypeList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.contractStartDateStart!=null ">
            AND hr.created_date <![CDATA[ >= ]]> #{param1.contractStartDateStart}
        </if>
        <if test="param1.contractStartDateEnd!=null ">
            AND hr.created_date <![CDATA[ <= ]]> date_add(#{param1.contractStartDateEnd}, interval 1 day)
        </if>
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
        <if test="param1.field == null and param1.field == '' and param1.order == null and param1.order == ''">
            order by  hr.created_date
        </if>
    </sql>

    <select id="selectPages" resultType="cn.casair.dto.HrReportDTO">
       <include refid="Find_SQL"/>
    </select>

    <select id="findList" resultType="cn.casair.dto.excel.HrReportExport">
        <include refid="Find_SQL"/>
    </select>

    <sql id="businessData">
        <if test="param.exhibitionType==1">
            bf.pay_year, bf.pay_monthly
        </if>
        <if test="param.exhibitionType==2">
            bf.pay_year, payQuarter
        </if>
        <if test="param.exhibitionType==3">
            bf.pay_year, payHalfYear
        </if>
        <if test="param.exhibitionType==4">
            bf.pay_year
        </if>
    </sql>
    <select id="getBusinessData" resultType="cn.casair.dto.report.BusinessDataDTO">
        SELECT
            <if test="param.exhibitionType==1">
                bf.pay_year, bf.pay_monthly, 1 exhibitionType,
            </if>
            <if test="param.exhibitionType==2">
                bf.pay_year, FLOOR((bf.pay_monthly +2)/3) payQuarter, 2 exhibitionType,
            </if>
            <if test="param.exhibitionType==3">
                bf.pay_year, FLOOR((bf.pay_monthly +5)/6) payHalfYear, 3 exhibitionType,
            </if>
            <if test="param.exhibitionType==4">
                bf.pay_year, 4 exhibitionType,
            </if>
            c.id,
            c.`client_name`,
            c.`parent_id`,
            ANY_VALUE ( bt.`staff_num` ) onJobNum,
            CASE any_value ( pr.`agreement_type` )  WHEN 2 THEN '服务费' ELSE '代理费' END serviceFeeType,
            ANY_VALUE ( pr.`service_charge` ) serviceAmountPrice,
            ANY_VALUE ( pr.`agreement_end_date` ) agreementEndDate,
            IFNULL(ANY_VALUE( bi.incomeServiceAmount ), 0) incomeServiceAmount,
            IFNULL(ANY_VALUE( bi.taxAmount ), 0) taxAmount,
            IFNULL(ANY_VALUE( bi.noTaxIncomeServiceAmount ), 0) noTaxIncomeServiceAmount,
            IFNULL(ANY_VALUE( bi.incomeAmount ), 0) incomeAmount
        FROM
            hr_client c
        LEFT JOIN hr_protocol pr ON c.`id` = pr.`client_id` AND pr.`is_delete` = 0 AND pr.`use_status` = 1
        LEFT JOIN hr_fee_review bf ON bf.`client_id` = c.`id` AND bf.`status` = 1  AND bf.is_delete = 0
        LEFT JOIN hr_bill_total bt ON bt.bill_id = bf.id AND bt.is_delete = 0
        LEFT JOIN (
            SELECT
                bi.client_id client_id,
                IFNULL(SUM( CAST( bir.total_amount AS DECIMAL(7, 2))), 0) incomeAmount,
                IFNULL(SUM( CAST( bir.tax_amount AS DECIMAL(7, 2))), 0)   taxAmount,
                IFNULL(SUM( CAST( bir.no_tax_amount AS DECIMAL(7, 2))), 0)  noTaxIncomeServiceAmount,
                IFNULL(SUM( CAST( bir2.total_amount AS DECIMAL(7, 2))), 0)  incomeServiceAmount,
                <if test="param.exhibitionType==1">
                    IFNUll(bir.payment_date, DATE_FORMAT(bi.apply_date, '%Y-%m')) paymentDate,
                </if>
                <if test="param.exhibitionType==2">
                    FLOOR((IFNUll(bir.pay_monthly, DATE_FORMAT(bi.apply_date, '%m')) +2)/3) payQuarter,
                </if>
                <if test="param.exhibitionType==3">
                    FLOOR((IFNUll(bir.pay_monthly, DATE_FORMAT(bi.apply_date, '%m')) +5)/6) payHalfYear,
                </if>
                IFNULL(bir.pay_year, DATE_FORMAT(bi.apply_date, '%Y')) payYear
            FROM
                hr_bill_invoice bi
            LEFT JOIN hr_bill_invoice_record bir ON bir.invoice_id = bi.id  AND bir.`is_delete` = 0
            LEFT JOIN hr_bill_invoice_record bir2 ON bir2.invoice_id = bi.id AND bir.id = bir2.id AND bir2.content IN ( 1, 2, 3, 7, 8 ) AND bir2.`is_delete` = 0
            WHERE bi.is_delete = 0 AND bi.approve_status = 5
            <if test="param.clientIds != null and param.clientIds.size > 0">
                AND bi.client_id IN
                <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            GROUP BY client_id,
            <if test="param.exhibitionType==1">
                paymentDate
            </if>
            <if test="param.exhibitionType==2">
                payYear, payQuarter
            </if>
            <if test="param.exhibitionType==3">
                payYear, payHalfYear
            </if>
            <if test="param.exhibitionType==4">
                payYear
            </if>
        ) bi ON bi.client_id = c.id
            <if test="param.exhibitionType==1">
                AND FIND_IN_SET(CONCAT( bf.pay_year, '-', IF ( bf.pay_monthly &lt; 10, CONCAT( '0', bf.`pay_monthly` ), bf.`pay_monthly` )), bi.paymentDate)
            </if>
            <if test="param.exhibitionType==2">
                AND bf.pay_year = bi.payYear AND  FLOOR((bf.pay_monthly +2)/3) = bi.payQuarter
            </if>
            <if test="param.exhibitionType==3">
                AND bf.pay_year = bi.payYear AND FLOOR((bf.pay_monthly +5)/6) = bi.payHalfYear
            </if>
            <if test="param.exhibitionType==4">
                AND bf.pay_year = bi.payYear
            </if>
        WHERE 1 = 1 AND c.is_delete = 0
        <if test="param.clientIds != null and param.clientIds.size > 0">
            AND c.id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.serviceFeeType != null">
            <choose>
                <when test="param.serviceFeeType == 0">
                    AND pr.agreement_type = 2
                </when>
                <otherwise>
                    AND pr.agreement_type != 2
                </otherwise>
            </choose>
        </if>
        <if test="param.exhibitionDateStart != null and param.exhibitionDateStart !=''">
            AND CONCAT( bf.pay_year, '-', IF ( bf.pay_monthly &lt; 10, CONCAT( '0', bf.`pay_monthly` ), bf.`pay_monthly` )) &gt;= #{param.exhibitionDateStart}
        </if>
        <if test="param.exhibitionDateEnd != null and param.exhibitionDateEnd !=''">
            AND CONCAT( bf.pay_year, '-', IF ( bf.pay_monthly &lt; 10, CONCAT( '0', bf.`pay_monthly` ), bf.`pay_monthly` )) &lt;= #{param.exhibitionDateEnd}
        </if>
        GROUP BY c.id,
        <include refid="businessData"/>
        HAVING onJobNum > 0
        ORDER BY c.id,
        <include refid="businessData"/>
    </select>

    <sql id="workDataGroup">
        <if test="param.exhibitionType==1 or param.exhibitionType==4">
            dateStr,
        </if>
        <if test="param.exhibitionType==2">
            year, quarter,
        </if>
        <if test="param.exhibitionType==3">
            year,halfYear,
        </if>
    </sql>

    <sql id="recruitmentDataGroup">
        <if test="param.exhibitionType == 1">
            dateStr
        </if>
        <if test="param.exhibitionType == 2">
            `year`, quarter
        </if>
        <if test="param.exhibitionType == 3">
            `year`, halfYear
        </if>
        <if test="param.exhibitionType == 4">
            `year`
        </if>
    </sql>

    <select id="getWorkData" resultType="cn.casair.dto.report.WorkDataDTO">
        WITH t1 AS(
        SELECT l.`apply_staff_id`,l.`serve_type`,l.apply_id,
        DATE_FORMAT(l.created_date, '%Y-%m') ldate,
        TIME_TO_SEC(TIMEDIFF(MAX(l.`created_date`),MIN(l.`created_date`)))/3600 hours
        FROM `hr_apply_op_logs` l
        WHERE l.`serve_type` IN (0,1,3,4,6,22) AND l.`is_delete` = 0

        GROUP BY l.`apply_staff_id`,l.serve_type,ldate,l.apply_id)

        SELECT
        c.`id`,c.`client_name`,c.`parent_id`,
        <if test="param.exhibitionType==1">
            a.opDate dateStr, 1 exhibitionType,
        </if>
        <if test="param.exhibitionType==2">
            year(concat(a.opDate,'-01')) year, QUARTER(concat(a.opDate,'-01')) quarter, 2 exhibitionType,
        </if>
        <if test="param.exhibitionType==3">
            year(concat(a.opDate,'-01')) year,  floor((month(concat(a.opDate,'-01')) +5)/6) halfYear, 3 exhibitionType,
        </if>
        <if test="param.exhibitionType==4">
            YEAR(concat(a.opDate,'-01')) dateStr, 4 exhibitionType,
        </if>

        COUNT(a.serve_type = 0 OR NULL)boardNum, SUM(IF(a.serve_type=0,a.hours,0)) boardHours,
        COUNT(a.serve_type = 1 OR NULL)departureNum, SUM(IF(a.serve_type=1,a.hours,0)) departureHours,
        COUNT(a.serve_type = 3 OR NULL)txNum, SUM(IF(a.serve_type=3,a.hours,0)) txHours,
        COUNT(a.serve_type = 4 OR NULL)certificateNum, SUM(IF(a.serve_type=4,a.hours,0)) certificateHours,
        COUNT(a.serve_type = 6 OR NULL)gsNum, SUM(IF(a.serve_type=6,a.hours,0)) gsHours,
        COUNT(a.serve_type = 22 OR NULL)contractRenewalNum, SUM(IF(a.serve_type=22,a.hours,0)) contractRenewalHours,
        COUNT(hf.id OR NULL) syNum,
        COUNT(hp.id OR NULL) protocolNum
        FROM hr_client c
        LEFT JOIN (
            SELECT t1.*,aef.client_id, t1.ldate opDate  FROM hr_apply_entry_staff aef
            LEFT JOIN t1 ON t1.apply_staff_id=aef.id
            WHERE t1.serve_type = 0 AND aef.`entry_status` = 7  AND aef.is_delete = 0
            UNION
            SELECT t1.*,adf.client_id, DATE_FORMAT(adf.departure_date, '%Y-%m') opDate  FROM hr_apply_departure_staff adf
            LEFT JOIN t1 ON  t1.`apply_staff_id` = adf.`id`
            WHERE t1.serve_type = 1 AND adf.is_delete = 0 AND adf.`departure_staff_status` = 6
            UNION
            SELECT t1.*, re.client_id,  DATE_FORMAT(re.actual_retire_date, '%Y-%m') opDate FROM hr_retire re
            LEFT JOIN t1 ON t1.`apply_id` = re.id
            WHERE re.`status`=3 AND re.is_delete = 0
            UNION
            SELECT t1.*, wi.client_id, DATE_FORMAT(wi.work_stoppage_start_date, '%Y-%m') opDate FROM hr_work_injury wi
            LEFT JOIN t1 ON t1.`apply_id` = wi.id
            WHERE wi.`status`=5 AND wi.is_delete = 0
            UNION
            SELECT t1.*,  ts.client_id, t1.ldate opDate FROM`hr_talent_staff` ts
            LEFT JOIN t1 ON t1.`apply_id` = ts.renewal_service_id
            WHERE ts.renewal_service_id IS NOT NULL AND ts.is_delete = 0 and ts.renewal_process = 4
            UNION
            SELECT t1.*,ci.client_id, DATE_FORMAT(ci.created_date, '%Y-%m') opDate FROM hr_certificate_issuance ci
            LEFT JOIN t1 ON t1.`apply_id` = ci.id
            WHERE ci.`certificate_status`=5 AND ci.is_delete = 0
        ) a
        ON c.id = a.client_id
        LEFT JOIN hr_fertility hf ON c.id = hf.`client_id`
        LEFT JOIN hr_protocol hp ON hp.client_id = c.id AND hp.is_delete = 0 AND hp.type = 1 AND hp.types = 1
        WHERE c.is_delete = 0
        <if test="param.exhibitionDateStart != null">
            and a.opDate &gt;= #{param.exhibitionDateStart}
        </if>
        <if test="param.exhibitionDateEnd != null">
            AND a.opDate &lt;= #{param.exhibitionDateEnd}
        </if>
        <if test="param.clientIds != null and param.clientIds.size > 0">
            and c.id in
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>

        GROUP BY
        <include refid="workDataGroup"/>
        c.id, c.`client_name`
        order by
        <include refid="workDataGroup"/>
        c.id, c.`client_name`

    </select>
    <select id="getEmployeeChangeData" resultType="cn.casair.dto.report.EmployeeChangeDataDTO">
        WITH t1 AS (
            SELECT c.id,
                   c.parent_id,
                   c.client_name,
                   count(*) num
            FROM hr_staff_work_experience swe
                     LEFT JOIN hr_client c ON c.id = swe.client_id
            WHERE trim(swe.client_id) != ''
              AND swe.is_delete = 0
              AND c.is_delete = 0
                <if test="param.clientIds != null and param.clientIds.size() > 0">
                    AND c.id in
                    <foreach collection="param.clientIds" item="clientId" separator="," open="(" close=")">
                        #{clientId}
                    </foreach>
                </if>
              AND ((
                           !ISNULL(swe.departure_date)
                           AND (
                                   (DATE_FORMAT(swe.board_date, '%Y-%m') &gt;= #{param.time1Start} and
                                    DATE_FORMAT(swe.board_date, '%Y-%m') &lt;= #{param.time1End})
                                   OR (DATE_FORMAT(swe.board_date, '%Y-%m') &lt;= #{param.time1Start} and
                                       DATE_FORMAT(swe.departure_date, '%Y-%m') &gt;= #{param.time1End})
                                   OR (DATE_FORMAT(swe.departure_date, '%Y-%m') >= #{param.time1Start} and
                                       DATE_FORMAT(swe.departure_date, '%Y-%m') &lt;= #{param.time1End})
                                   OR (DATE_FORMAT(swe.board_date, '%Y-%m') >= #{param.time1Start} and
                                    DATE_FORMAT(swe.departure_date, '%Y-%m') &lt;= #{param.time1End})
                               )
                       ) or
                   (
                           ISNULL(swe.departure_date)
                           AND (
                                   (DATE_FORMAT(swe.board_date, '%Y-%m') &gt;= #{param.time1Start} and
                                    DATE_FORMAT(swe.board_date, '%Y-%m') &lt;= #{param.time1End})
                                   OR DATE_FORMAT(swe.board_date, '%Y-%m') &lt;= #{param.time1Start}
                               )
                       ))
            GROUP BY c.id
        ),
             t2 AS (
                 SELECT c.id,
                        c.parent_id,
                        c.client_name,
                        count(*) num
                 FROM hr_staff_work_experience swe
                          LEFT JOIN hr_client c ON c.id = swe.client_id
                 WHERE trim(swe.client_id) != ''
                   AND swe.is_delete = 0
                   AND c.is_delete = 0
                    <if test="param.clientIds != null and param.clientIds.size() > 0">
                        AND c.id in
                        <foreach collection="param.clientIds" item="clientId" separator="," open="(" close=")">
                            #{clientId}
                        </foreach>
                    </if>
                   AND ((
                                !ISNULL(swe.departure_date)
                                AND (
                                        (DATE_FORMAT(swe.board_date, '%Y-%m') &gt;= #{param.time2Start} and
                                         DATE_FORMAT(swe.board_date, '%Y-%m') &lt;= #{param.time2End})
                                        OR (DATE_FORMAT(swe.board_date, '%Y-%m') &lt;= #{param.time2Start} and
                                            DATE_FORMAT(swe.departure_date, '%Y-%m') &gt;= #{param.time2End})
                                        OR (DATE_FORMAT(swe.departure_date, '%Y-%m') >= #{param.time2Start} and
                                            DATE_FORMAT(swe.departure_date, '%Y-%m') &lt;= #{param.time2End})
                                        OR (DATE_FORMAT(swe.board_date, '%Y-%m') >= #{param.time2Start} and
                                            DATE_FORMAT(swe.departure_date, '%Y-%m') &lt;= #{param.time2End})
                                    )
                            ) or
                        (
                                ISNULL(swe.departure_date)
                                AND (
                                        (DATE_FORMAT(swe.board_date, '%Y-%m') &gt;= #{param.time2Start} and
                                         DATE_FORMAT(swe.board_date, '%Y-%m') &lt;= #{param.time2End})
                                        OR DATE_FORMAT(swe.board_date, '%Y-%m') &lt;= #{param.time2Start}
                                    )
                            ))
                 GROUP BY c.id
             )
        SELECT a.*,
               (num2 - num1) / num1  rate
        FROM (
                 SELECT t1.id             id,
                        t1.parent_id      parentId,
                        t1.client_name    clientName,
                        t1.num            num1,
                        IFNULL(t2.num, 0) num2
                 FROM t1
                          LEFT JOIN t2 ON t1.id = t2.id
                 UNION
                 SELECT t2.id             id,
                        t2.parent_id      parentId,
                        t2.client_name    clientName,
                        IFNULL(t1.num, 0) num1,
                        t2.num            num2
                 FROM t2
                          LEFT JOIN t1 ON t1.id = t2.id
             ) a
        ORDER BY rate DESC
    </select>
    <select id="getIncomeComparisonData" resultType="cn.casair.dto.report.IncomeComparisonDataDTO">
        WITH t1 AS (
            SELECT
                c.id,
                c.parent_id,
                c.`client_name`,
                IFNULL( SUM( bir.total_amount ), 0 ) serviceFee,
                IFNULL( SUM( bi.total_amount ), 0 ) totalAmount
            FROM
                hr_client c
                    LEFT JOIN hr_bill_invoice bi ON bi.client_id = c.id
                    LEFT JOIN hr_bill_invoice_record bir ON bir.invoice_id = bi.id
            WHERE
                c.is_delete = 0
              AND bi.`is_delete` = 0
              AND bi.invoice_state = 1
              AND bir.content IN ( 1, 2, 3, 7, 8 )
              AND bir.`is_delete` = 0
              AND trim( bir.payment_date ) != ''
              AND bir.payment_date &gt;= #{param.time1Start}
              AND bir.payment_date &lt;= #{param.time1End}
                <if test="param.clientIds != null and param.clientIds.size() > 0">
                    AND c.id in
                    <foreach collection="param.clientIds" item="clientId" separator="," open="(" close=")">
                        #{clientId}
                    </foreach>
                </if>
            GROUP BY
                c.id,
                c.`client_name`
        ),
             t2 AS (
                 SELECT
                     c.id,
                     c.parent_id,
                     c.`client_name`,
                     IFNULL( SUM( bir.total_amount ), 0 ) serviceFee,
                     IFNULL( SUM( bi.total_amount ), 0 ) totalAmount
                 FROM
                     hr_client c
                         LEFT JOIN hr_bill_invoice bi ON bi.client_id = c.id
                         LEFT JOIN hr_bill_invoice_record bir ON bir.invoice_id = bi.id
                 WHERE
                     c.is_delete = 0
                   AND bi.`is_delete` = 0
                   AND bi.invoice_state = 1
                   AND bir.content IN ( 1, 2, 3, 7, 8 )
                   AND bir.`is_delete` = 0
                   AND trim( bir.payment_date ) != ''
                   AND bir.payment_date &gt;= #{param.time2Start}
                   AND bir.payment_date &lt;= #{param.time2End}
                    <if test="param.clientIds != null and param.clientIds.size() > 0">
                        AND c.id in
                        <foreach collection="param.clientIds" item="clientId" separator="," open="(" close=")">
                            #{clientId}
                        </foreach>
                    </if>
                 GROUP BY
                     c.id,
                     c.`client_name`
             ) SELECT
                a.*,
                IF (a.bqServiceFee - a.tqServiceFee = 0 OR a.tqServiceFee = 0, 0, ( a.bqServiceFee - a.tqServiceFee )/
                a.tqServiceFee) serviceFeeRate,
                IF( a.bqTotalAmount - a.tqTotalAmount = 0 OR a.tqTotalAmount = 0, 0,( a.bqTotalAmount - a.tqTotalAmount)/
                a.tqTotalAmount ) incomeRate
        FROM
            (
                SELECT
                    t1.id,
                    t1.parent_id,
                    t1.client_name,
                    t1.serviceFee tqServiceFee,
                    t1.totalAmount tqTotalAmount,
                    IFNULL( t2.serviceFee, 0 ) bqServiceFee,
                    IFNULL( t2.totalAmount, 0 ) bqTotalAmount
                FROM
                    t1
                        LEFT JOIN t2 ON t1.id = t2.id UNION
                SELECT
                    t2.id,
                    t2.parent_id,
                    t2.client_name,
                    IFNULL( t1.serviceFee, 0 ) tqServiceFee,
                    IFNULL( t1.totalAmount, 0 ) tqTotalAmount,
                    t2.serviceFee bqServiceFee,
                    t2.totalAmount bqTotalAmount
                FROM
                    t2
                        LEFT JOIN t1 ON t1.id = t2.id
            ) a
        ORDER BY
            serviceFeeRate DESC,
            incomeRate DESC

    </select>
    <select id="getBudgetControlData" resultType="cn.casair.dto.report.BudgetControlDataDTO">
        SELECT
            <if test="param.exhibitionType==1">
                CONCAT( fr.pay_year, '-' ,IF ( fr.pay_monthly &gt; 9, fr.pay_monthly, CONCAT( '0', fr.pay_monthly ))) paymentDate,
            </if>
            <if test="param.exhibitionType==2">
                CONCAT( fr.pay_year, '年第', FLOOR( ( fr.pay_monthly + 2 ) / 3 ), '季度' ) paymentDate,
            </if>
            <if test="param.exhibitionType==3">
                CONCAT( fr.pay_year, '年', IF ( fr.pay_monthly &lt; 7, '上', '下'), '半年度') paymentDate,
            </if>
            <if test="param.exhibitionType==4">
                CONCAT( fr.pay_year, '年' )        paymentDate,
            </if>
            c.id,
            c.parent_id,
            c.`client_name`,
            IFNULL( SUM( bt.social_security_total ), 0 ) social_security_total,
            IFNULL( SUM( bt.accumulation_fund_total ), 0 ) accumulation_fund_total,
            IFNULL( SUM( bt.real_salary_total ), 0 ) real_salary_total,
            IFNULL( SUM( bt.personal_tax_total ), 0 ) personal_tax_total,
            IFNULL( SUM( bt.service_fee_total ), 0 ) service_fee_total
        FROM
            hr_client c
                LEFT JOIN hr_fee_review fr ON fr.`client_id` = c.`id`
                LEFT JOIN hr_bill_total bt ON fr.`id` = bt.`bill_id`
        WHERE
            fr.is_delete = 0
          AND fr.STATUS = 1
          AND bt.type = 4
          AND c.is_delete = 0
          AND fr.payment_date &gt;= #{param.time1Start}
          AND fr.payment_date &lt;= #{param.time1End}
            <if test="param.clientIds != null and param.clientIds.size() > 0">
                AND c.id in
                <foreach collection="param.clientIds" item="clientId" separator="," open="(" close=")">
                    #{clientId}
                </foreach>
            </if>
        GROUP BY
            <if test="param.exhibitionType==1">
                fr.pay_year,
                fr.pay_monthly,
            </if>
            <if test="param.exhibitionType==2 || param.exhibitionType==3">
                paymentDate,
            </if>
            <if test="param.exhibitionType==4">
                fr.pay_year,
            </if>
            c.id,
            c.`client_name`
        ORDER BY
            paymentDate desc,
            convert(c.`client_name` using gbk)
    </select>

    <select id="getRecruitmentData" resultType="cn.casair.dto.report.RecruitmentDataDTO">
        SELECT
            <if test="param.exhibitionType == 1">
                DATE_FORMAT( rb.created_date, '%Y-%m' ) dateStr, 1 exhibitionType,
            </if>
            <if test="param.exhibitionType == 2">
                YEAR(CONCAT(DATE_FORMAT( rb.created_date, '%Y-%m' ),'-01')) `year`,
                QUARTER(CONCAT(DATE_FORMAT( rb.created_date, '%Y-%m' ),'-01')) quarter,
                2 exhibitionType,
            </if>
            <if test="param.exhibitionType == 3">
                YEAR(CONCAT(DATE_FORMAT( rb.created_date, '%Y-%m' ),'-01')) `year`,
                FLOOR((MONTH(CONCAT(DATE_FORMAT( rb.created_date, '%Y-%m' ),'-01')) +5)/6) halfYear,
                3 exhibitionType,
            </if>
            <if test="param.exhibitionType == 4">
                YEAR(CONCAT(DATE_FORMAT( rb.created_date, '%Y-%m' ),'-01')) `year`, 4 exhibitionType,
            </if>
            COUNT( rb.id ) proSessions,
            SUM( t.signUpNum ) signUpNum,
            SUM( t.approveNum ) approvedNum,
            SUM( t.payNum ) payNum,
            SUM( t.payAmount ) payAmount,
            SUM( t.onDutyNum ) onDutyNum,
            ANY_VALUE(t1.service_fee) serviceFee,
            ANY_VALUE(t1.total_arrival_amount) backArrivalAmount
        FROM
            hr_recruitment_brochure rb
        LEFT JOIN (
            SELECT
                rs.service_id,
                COUNT( rd.id IS NOT NULL OR NULL ) signUpNum,
                COUNT( rd.`status` > 1 OR NULL ) approveNum,
                COUNT(( rd.`status` > 3 AND rs.is_need_pay = 1 ) OR NULL ) payNum,
                SUM(IF( rs.is_need_pay = 1, rs.recruitment_fee, 0 )) payAmount,
                COUNT( rd.`status` IN (18,19) OR NULL ) onDutyNum
            FROM
                hr_recruitment_station rs
            LEFT JOIN hr_registration_details rd ON rd.station_id = rs.id AND rd.is_delete = 0
            WHERE
                rs.is_delete = 0
            GROUP BY
                rs.service_id
        ) t ON t.service_id = rb.id
        LEFT JOIN (
            SELECT
                bi.id,
                ANY_VALUE( bi.client_id ) client_id,
                DATE_FORMAT(bi.apply_date, '%Y-%m') apply_date,
                SUM(br.total_amount) service_fee,
                ANY_VALUE(ar.total_arrival_amount) total_arrival_amount
            FROM
                hr_bill_invoice bi
            LEFT JOIN hr_bill_invoice_record br ON bi.id = br.invoice_id AND br.is_delete = 0 AND br.content = 3
            LEFT JOIN hr_arrival_record ar ON bi.id = ar.bill_invoice_id AND ar.is_delete = 0
            WHERE bi.is_delete = 0 AND bi.invoice_state = 1 AND bi.approve_status = 5
            GROUP BY bi.id HAVING COUNT( br.id) > 0 ORDER BY bi.created_date DESC
        )t1 ON t1.client_id = rb.client_id AND t1.apply_date = DATE_FORMAT( rb.created_date, '%Y-%m' )
        LEFT JOIN hr_arrival_record ar ON t1.id = ar.bill_invoice_id AND ar.is_delete = 0
        WHERE
            rb.is_delete = 0
        <if test="param.exhibitionDateStart != null">
            AND DATE_FORMAT(rb.created_date, '%Y-%m') &gt;= #{param.exhibitionDateStart}
        </if>
        <if test="param.exhibitionDateEnd != null">
            AND DATE_FORMAT(rb.created_date, '%Y-%m') &lt;= #{param.exhibitionDateEnd}
        </if>
        GROUP BY
        <include refid="recruitmentDataGroup"/>
        ORDER BY
        <include refid="recruitmentDataGroup"/>
        DESC
    </select>

    <select id="getRecruitmentInfoData" resultType="cn.casair.dto.report.RecruitmentInfoDataDTO">
        SELECT
            rb.client_id,
            ANY_VALUE( rb.id ) id,
            ANY_VALUE( hc.client_name ) client_name,
            ANY_VALUE( hc.parent_id ) parent_id,
            ANY_VALUE( rb.created_date ) needCreateDate,
            ANY_VALUE( hl.created_date ) brochureCreateDate,
            SUM( t.signUpNum ) signUpNum,
            SUM( t.approveNum ) approvedNum,
            SUM( t.payNum ) payNum,
            SUM( t.payAmount ) payAmount,
            SUM( t.onDutyNum ) onDutyNum,
            SUM( t.specialtyNum ) specialtyNum,
            SUM( t.undergraduateNum ) undergraduateNum,
            SUM( t.postgraduateNum ) postgraduateNum,
            ANY_VALUE ( t1.service_fee ) serviceFee,
            ANY_VALUE ( t1.total_arrival_amount ) backArrivalAmount,
            ANY_VALUE( t.written_exam_start_time) writtenExamTime,
            ANY_VALUE( t.interview_exam_start_time) interviewExamTime,
            ANY_VALUE( t.add_exam_time) addExamTime,
            ANY_VALUE( t.board_date) onDutyTime,
            IF(GROUP_CONCAT(t1.invoice_status) LIKE '%1%','是','否') invoice_status
        FROM
            hr_recruitment_brochure rb
        LEFT JOIN hr_client hc ON rb.client_id = hc.id AND hc.is_delete = 0
        LEFT JOIN hr_apply_op_logs hl ON hl.apply_id = rb.id AND hl.message LIKE '%发布%' AND hl.serve_type = 16
        LEFT JOIN (
            SELECT
                rs.service_id,
                ANY_VALUE( rs.written_exam_start_time) written_exam_start_time,
                ANY_VALUE( rs.interview_exam_start_time) interview_exam_start_time,
                ANY_VALUE( aol.created_date ) add_exam_time,
                ANY_VALUE( swe.board_date) board_date,
                COUNT( rd.id IS NOT NULL OR NULL ) signUpNum,
                COUNT( rd.`status` > 1 OR NULL ) approveNum,
                COUNT(( rd.`status` > 3 AND rs.is_need_pay = 1 ) OR NULL ) payNum,
                SUM(IF( rs.is_need_pay = 1, rs.recruitment_fee, 0 )) payAmount,
                COUNT( rd.`status` IN ( 18, 19 ) OR NULL ) onDutyNum,
                COUNT( ts.highest_education = 4 OR NULL ) specialtyNum,
                COUNT( ts.highest_education = 5 OR NULL ) undergraduateNum,
                COUNT( ts.highest_education = 6 OR NULL ) postgraduateNum
            FROM
                hr_recruitment_station rs
            LEFT JOIN hr_registration_details rd ON rd.station_id = rs.id AND rd.is_delete = 0
            LEFT JOIN hr_apply_op_logs aol ON aol.apply_id = rd.id AND aol.is_delete = 0 AND aol.serve_type = 15 AND aol.message LIKE '%加试成绩%'
            LEFT JOIN hr_talent_staff ts ON rd.staff_id = ts.id AND ts.is_delete = 0
            LEFT JOIN v_staff_work_experience swe ON swe.staff_id = ts.id AND (CASE ts.staff_status WHEN 5 THEN swe.iz_default = 0 AND swe.client_id IS NOT NULL AND ts.client_id = swe.client_id ELSE swe.iz_default = 1 END) AND swe.is_delete = 0
            WHERE rs.is_delete = 0
            GROUP BY rs.service_id
        ) t ON t.service_id = rb.id
        LEFT JOIN (
            SELECT
                bi.id,
                ANY_VALUE ( bi.invoice_status ) invoice_status,
                ANY_VALUE ( bi.client_id ) client_id,
                DATE_FORMAT( bi.apply_date, '%Y-%m' ) apply_date,
                SUM( br.total_amount ) service_fee,
                ANY_VALUE ( ar.total_arrival_amount ) total_arrival_amount
            FROM
                hr_bill_invoice bi
            LEFT JOIN hr_bill_invoice_record br ON bi.id = br.invoice_id AND br.is_delete = 0 AND br.content = 3
            LEFT JOIN hr_arrival_record ar ON bi.id = ar.bill_invoice_id AND ar.is_delete = 0
            WHERE bi.is_delete = 0 AND bi.invoice_state = 1 AND bi.approve_status = 5
            GROUP BY bi.id
            HAVING COUNT( br.id ) > 0
            ORDER BY bi.created_date DESC
        ) t1 ON t1.client_id = rb.client_id AND t1.apply_date = DATE_FORMAT( rb.created_date, '%Y-%m' )
        WHERE rb.is_delete = 0
        <if test="param.exhibitionDateStart != null">
            AND DATE_FORMAT(rb.created_date, '%Y-%m') &gt;= #{param.exhibitionDateStart}
        </if>
        <if test="param.exhibitionDateEnd != null">
            AND DATE_FORMAT(rb.created_date, '%Y-%m') &lt;= #{param.exhibitionDateEnd}
        </if>
        <if test="param.clientIds != null and param.clientIds.size() > 0">
            AND rb.client_id IN
            <foreach collection="param.clientIds" item="clientId" separator="," open="(" close=")">
                #{clientId}
            </foreach>
        </if>
        GROUP BY rb.client_id
        ORDER BY brochureCreateDate DESC
    </select>

    <select id="getRecruitmentPROData" resultType="cn.casair.dto.report.RecruitmentPRODataDTO">
        SELECT
            rb.id,
            ANY_VALUE( rb.created_date ) needCreateDate,
            ANY_VALUE( hl.created_date ) brochureCreateDate,
            SUM( t.signUpNum ) signUpNum,
            SUM( t.approveNum ) approvedNum,
            SUM( t.payNum ) payNum,
            SUM( t.payAmount ) payAmount,
            SUM( t.onDutyNum ) onDutyNum,
            SUM( t.specialtyNum ) specialtyNum,
            SUM( t.undergraduateNum ) undergraduateNum,
            SUM( t.postgraduateNum ) postgraduateNum,
            ANY_VALUE ( t1.service_fee ) serviceFee,
            ANY_VALUE ( t1.total_arrival_amount ) backArrivalAmount,
            ANY_VALUE( t.written_exam_start_time) writtenExamTime,
            ANY_VALUE( t.interview_exam_start_time) interviewExamTime,
            ANY_VALUE( t.add_exam_time) addExamTime,
            ANY_VALUE( t.board_date) onDutyTime,
            IF(GROUP_CONCAT(t1.invoice_status) LIKE '%1%','是','否') invoice_status
        FROM
            hr_recruitment_brochure rb
        LEFT JOIN hr_apply_op_logs hl ON hl.apply_id = rb.id AND hl.message LIKE '%发布%' AND hl.serve_type = 16
        LEFT JOIN (
            SELECT
                rs.service_id,
                ANY_VALUE( rs.written_exam_start_time) written_exam_start_time,
                ANY_VALUE( rs.interview_exam_start_time) interview_exam_start_time,
                ANY_VALUE( aol.created_date ) add_exam_time,
                ANY_VALUE( swe.board_date) board_date,
                COUNT( rd.id IS NOT NULL OR NULL ) signUpNum,
                COUNT( rd.`status` > 1 OR NULL ) approveNum,
                COUNT(( rd.`status` > 3 AND rs.is_need_pay = 1 ) OR NULL ) payNum,
                SUM(IF( rs.is_need_pay = 1, rs.recruitment_fee, 0 )) payAmount,
                COUNT( rd.`status` IN ( 18, 19 ) OR NULL ) onDutyNum,
                COUNT( ts.highest_education = 4 OR NULL ) specialtyNum,
                COUNT( ts.highest_education = 5 OR NULL ) undergraduateNum,
                COUNT( ts.highest_education = 6 OR NULL ) postgraduateNum
            FROM
                hr_recruitment_station rs
            LEFT JOIN hr_registration_details rd ON rd.station_id = rs.id AND rd.is_delete = 0
            LEFT JOIN hr_apply_op_logs aol ON aol.apply_id = rd.id AND aol.is_delete = 0 AND aol.serve_type = 15 AND aol.message LIKE '%加试成绩%'
            LEFT JOIN hr_talent_staff ts ON rd.staff_id = ts.id AND ts.is_delete = 0
            LEFT JOIN v_staff_work_experience swe ON swe.staff_id = ts.id AND (CASE ts.staff_status
            WHEN 5 THEN swe.iz_default = 0 AND swe.client_id IS NOT NULL AND ts.client_id = swe.client_id ELSE swe.iz_default = 1 END) AND swe.is_delete = 0
            WHERE rs.is_delete = 0
            GROUP BY rs.service_id
        ) t ON t.service_id = rb.id
        LEFT JOIN (
            SELECT
                bi.id,
                ANY_VALUE ( bi.invoice_status ) invoice_status,
                ANY_VALUE ( bi.client_id ) client_id,
                DATE_FORMAT( bi.apply_date, '%Y-%m' ) apply_date,
                SUM( br.total_amount ) service_fee,
                ANY_VALUE ( ar.total_arrival_amount ) total_arrival_amount
            FROM
                hr_bill_invoice bi
            LEFT JOIN hr_bill_invoice_record br ON bi.id = br.invoice_id AND br.is_delete = 0 AND br.content = 3
            LEFT JOIN hr_arrival_record ar ON bi.id = ar.bill_invoice_id AND ar.is_delete = 0
            WHERE bi.is_delete = 0 AND bi.invoice_state = 1 AND bi.approve_status = 5
            GROUP BY bi.id
            HAVING COUNT( br.id ) > 0
            ORDER BY bi.created_date DESC
        ) t1 ON t1.client_id = rb.client_id AND t1.apply_date = DATE_FORMAT( rb.created_date, '%Y-%m' )
        WHERE rb.is_delete = 0
        <if test="param.exhibitionDateStart != null">
            AND DATE_FORMAT(rb.created_date, '%Y-%m') &gt;= #{param.exhibitionDateStart}
        </if>
        <if test="param.exhibitionDateEnd != null">
            AND DATE_FORMAT(rb.created_date, '%Y-%m') &lt;= #{param.exhibitionDateEnd}
        </if>
        GROUP BY rb.id
        ORDER BY brochureCreateDate DESC
    </select>
</mapper>
