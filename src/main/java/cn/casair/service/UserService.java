package cn.casair.service;

import cn.casair.common.enums.UserRoleTypeEnum;
import cn.casair.domain.User;
import cn.casair.dto.UserDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2020-05-08
 */
public interface UserService extends IService<User> {

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    UserDTO getUser(String id);

    /**
     * 删除
     *
     * @param id
     */
    void deleteUser(String id);

    /**
     * 批量删除
     *
     * @param ids
     */
    void deleteUser(List<String> ids);

    UserDTO getByLoginName(String loginName);

    void authenticate(String password, String password1);

    /**
     * 查询所有赛区负责人包含角色
     *
     * @param
     * @return java.util.List<cn.casair.dto.UserDTO>
     * <AUTHOR> yanglei 2020/5/30 14:34
     */
    List<UserDTO> getUserWithRole();

    /**
     * 创建(带角色的)用户
     *
     * @param userDTO
     * @param roleTypeEnum
     * <AUTHOR> Lyric.Lin 2020/6/12 10:21
     */
    UserDTO createUserWithRole(UserDTO userDTO, UserRoleTypeEnum roleTypeEnum);

    /**
     * 编辑用户
     *
     * @param userDTO
     * <AUTHOR> Lyric.Lin 2020/6/12 10:56
     */
    Optional<UserDTO> updateSimpleUser(UserDTO userDTO);

    /**
     * 分页获取用户列表
     *
     * @param userDTO
     * @param pageNumber
     * @param pageSize
     * <AUTHOR> Lyric.Lin 2020/6/12 12:00
     */
    IPage findUserPages(UserDTO userDTO, Long pageNumber, Long pageSize);

    List<UserDTO> getByRole(String... roleKeys);

    UserDTO distribute(UserDTO userDTO);

    List<UserDTO> getSpecialized();

    User getAuditorInfo(String id);

    /**
     * 导入
     * @param file
     * @return
     */
    String importUser(MultipartFile file);

    /**
     * 用户入模板
     * @param response
     */
    String imporTemplate(HttpServletResponse response);

}


