<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrFertilityRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, staff_id, client_id, maternity_leave_start_date, maternity_leave_end_date, status,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrFertility">
                    <id column="id" property="id"/>
                    <result column="staff_id" property="staffId"/>
                    <result column="client_id" property="clientId"/>
                    <result column="maternity_leave_start_date" property="maternityLeaveStartDate"/>
                    <result column="maternity_leave_end_date" property="maternityLeaveEndDate"/>
                    <result column="status" property="status"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

        <select id="selectDetatil" resultType="cn.casair.dto.HrFertilityDTO">
            SELECT
                hf.id,
                hf.staff_id,
                hf.created_date,
                hf.client_id,
                hc.client_name,
                ht.`name`,
                ht.certificate_num,
                ht.sex,
                ht.phone,
                hs.profession_name,
                ht.personnel_type,
                hf.maternity_leave_start_date,
                hf.maternity_leave_end_date,
                hf.`status`,
                su.real_name AS specialized,
                hse.basic_wage,
                hse.social_security_cardinal,
                hse.medical_insurance_cardinal,
                hse.accumulation_fund_cardinal,
                hce.contract_start_date,
                hce.contract_end_date
            FROM
                hr_fertility hf
                    LEFT JOIN hr_talent_staff ht ON hf.staff_id = ht.id
                    AND ht.is_delete = 0
                    LEFT JOIN hr_staff_work_experience hsw ON hf.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id
                    AND hsw.is_delete = 0
                    AND hsw.iz_default = 1
                    LEFT JOIN hr_station hs ON hsw.station_id = hs.id
                    AND hs.is_delete = 0
                    LEFT JOIN hr_client hc ON hc.id = hf.client_id
                    AND hc.is_delete = 0
                    LEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id
                    AND huc.is_delete = 0
                    AND huc.is_specialized = 1
                    LEFT JOIN sys_user su ON huc.user_id = su.id
                    AND su.is_delete = 0
                    LEFT JOIN hr_staff_emolument hse ON hse.staff_id = ht.id
                    AND hse.is_delete = 0
                    LEFT JOIN (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
                               FROM (SELECT * FROM hr_contract WHERE is_delete = 0 AND state in (1,2) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) hce ON hce.staff_id = ht.id AND hce.is_delete = 0
            WHERE hf.is_delete = 0 AND ht.is_delete = 0 AND hf.id = #{id}
    </select>

        <select id="exportFertility" resultType="cn.casair.dto.excel.HrFertilityExport">
            SELECT
                hf.id,
                ht.client_id,
                hc.client_name,
                ht.`name`,
                ht.staff_status,
                ht.certificate_num,
                ht.sex,
                ht.phone,
                hs.profession_name,
                ht.personnel_type,
                hf.maternity_leave_start_date,
                hf.maternity_leave_end_date,
                hf.`status`,
                su.real_name AS specialized
            FROM
                hr_fertility hf
                    LEFT JOIN hr_talent_staff ht ON hf.staff_id = ht.id
                    AND ht.is_delete = 0
                    LEFT JOIN hr_staff_work_experience hsw ON hf.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id
                    AND hsw.is_delete = 0
                    AND hsw.iz_default = 1
                    LEFT JOIN hr_station hs ON hsw.station_id = hs.id
                    AND hs.is_delete = 0
                    LEFT JOIN hr_client hc ON hc.id = hf.client_id
                    AND hc.is_delete = 0
                    LEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id
                    AND huc.is_delete = 0
                    AND huc.is_specialized = 1
                    LEFT JOIN sys_user su ON huc.user_id = su.id
                    AND su.is_delete = 0
             ${ew.customSqlSegment}
        </select>

    <select id="fertilityListByStaffId" resultType="cn.casair.dto.HrFertilityDTO">
        SELECT
            hc.client_name,
            hf.*
        FROM
            hr_fertility hf
        LEFT JOIN hr_talent_staff hts ON hf.staff_id = hts.id
        LEFT JOIN hr_client hc ON hc.id = hf.client_id
        WHERE
            hf.is_delete = 0 AND hf.staff_id = #{staffId}
        ORDER BY hf.maternity_leave_start_date
    </select>
</mapper>
