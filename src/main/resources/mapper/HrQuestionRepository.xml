<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrQuestionRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , title, question_type, question_pro, score, difficult, correct, applicable_post, answer_analysis, question_cont, status,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrQuestion">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="question_type" property="questionType"/>
        <result column="question_pro" property="questionPro"/>
        <result column="score" property="score"/>
        <result column="difficult" property="difficult"/>
        <result column="correct" property="correct"/>
        <result column="applicable_post" property="applicablePost"/>
        <result column="answer_analysis" property="answerAnalysis"/>
        <!--        <result column="question_cont" property="questionCont"/>-->
        <result column="status" property="status"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>
    <delete id="deleteList">
        delete from hr_question_station
        <where>
            <if test="applicablePostList != null and applicablePostList.size() > 0">
                question_id IN
                <foreach collection="applicablePostList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </delete>

    <delete id="deleteId">
        delete
        from hr_question_station
        where question_id = #{id}
    </delete>

    <select id="selectQuesionid" resultType="cn.casair.dto.HrQuestionDTO">
        select * from hr_question where is_delete=0
        <if test="qid != null and qid.size() > 0">
            and id IN
            <foreach collection="qid" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        ORDER BY  question_type DESC
        LIMIT #{totalscoreSum}
    </select>

    <select id="getlistsQuestion" resultType="java.lang.String">
        SELECT
        hq. id
        FROM
        hr_question hq
        LEFT JOIN hr_question_station hs on hq.id=hs.question_id
        WHERE

        <if test="param1.applicablePostList!=null and param1.applicablePostList.size() > 0">
            hs.station_id in
            <foreach collection="param1.applicablePostList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.scoredList!=null and param1.scoredList.size() > 0">
            and hq.score in
            <foreach collection="param1.scoredList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.questionType!=null and param1.questionType!=''">
            AND hq.question_type = #{param1.questionType}
        </if>
        AND hs.is_delete = 0 ORDER BY hs.created_date DESC
        LIMIT #{param1.scoreSum}
    </select>
    <select id="selectPages" resultType="cn.casair.dto.HrQuestionDTO">
        SELECT DISTINCT hq. *
        from hr_question hq
                 LEFT JOIN hr_question_station hqs on hq.id = hqs.question_id and hqs.is_delete = 0
        WHERE hq.is_delete = 0
        and hqs.station_id in
        <foreach collection="param1.stationIdList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
          AND hq.question_type = #{param1.questionType}
          AND hq.score = #{param1.score}
          AND hq.question_pro = #{param1.questionPro}
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </select>
</mapper>
