package cn.casair.service;

import cn.casair.domain.HrTemplate;
import cn.casair.dto.HrTemplateDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

/**
 * 模板管理服务类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
public interface HrTemplateService extends IService<HrTemplate> {


    /**
     * 创建模板管理
     * @param hrTemplateDTO
     * @return
     */
    HrTemplateDTO createHrTemplate(HrTemplateDTO hrTemplateDTO);

    /**
     * 修改模板管理
     * @param hrTemplateDTO
     * @return
     */
    Optional<HrTemplateDTO> updateHrTemplate(HrTemplateDTO hrTemplateDTO);

    /**
     * 查询模板管理详情
     * @param id
     * @return
     */
    HrTemplateDTO getHrTemplate(String id);

    /**
     * 删除模板管理
     * @param id
     */
    void deleteHrTemplate(String id);

    /**
     * 批量删除模板管理
     * @param ids
     * @return
     */
    ResponseEntity<?> deleteHrTemplate(List<String> ids);

    /**
     * 分页查询模板管理
     * @param hrTemplateDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrTemplateDTO hrTemplateDTO,Long pageNumber,Long pageSize);

    List<String> getHrTemplateFrequency();

    void getHrTemplateCopy(String id);

    List<HrTemplateDTO> HrTemplatesDownload(HrTemplateDTO hrTemplateDTO);
}
