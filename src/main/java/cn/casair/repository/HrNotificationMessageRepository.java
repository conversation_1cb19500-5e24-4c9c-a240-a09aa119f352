package cn.casair.repository;

import cn.casair.domain.HrNotificationMessage;
import cn.casair.domain.HrNotificationUser;
import cn.casair.dto.HrNotificationMessageDTO;
import cn.casair.dto.HrNotificationUserDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 通知消息数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Repository
public interface HrNotificationMessageRepository extends BaseMapper<HrNotificationMessage> {

    IPage<HrNotificationMessageDTO> Page(Page<HrNotificationMessage> page, @Param("param1")HrNotificationMessageDTO hrNotificationMessageDTO);

    List<HrNotificationMessageDTO> selectUserId(String id);

    void updeteHrNotificationMessage(@Param("param1")HrNotificationMessageDTO hrNotificationMessageDTO);

    HrNotificationMessageDTO selectHrNotificationUser(@Param("param1")HrNotificationMessageDTO hrNotificationMessageDTOs);

    HrNotificationMessageDTO selectNotificationMessageContent(String id);

    List<HrNotificationMessageDTO> getHrNotificationMessageSelectContent(String id);

    Optional<HrNotificationUserDTO> updateNotificationUser(@Param("param1")HrNotificationUser hrNotificationUser);


    IPage<HrNotificationMessageDTO> PageReminderMethod(Page<HrNotificationMessage> page, @Param("param1")HrNotificationMessageDTO hrNotificationMessageDTO);

    String getNotificationMessageId(@Param("reminderValue") Integer reminderValue, @Param("notificationMessageId") String notificationMessageId);

    IPage<HrNotificationMessageDTO> getPageReminderMethod(Page<HrNotificationMessage> page, @Param("param1")HrNotificationMessageDTO hrNotificationMessageDTO);
}
