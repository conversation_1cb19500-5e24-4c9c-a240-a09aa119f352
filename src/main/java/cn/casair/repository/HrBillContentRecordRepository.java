package cn.casair.repository;

import cn.casair.domain.HrBillContentRecord;
import cn.casair.dto.HrBillContentRecordDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Repository
public interface HrBillContentRecordRepository extends BaseMapper<HrBillContentRecord> {

    /**
     * 分页查询账单
     *
     * @param page
     * @param hrBillContentRecordDTO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrBillDTO>
     **/
    IPage<HrBillContentRecordDTO> findPage(Page<HrBillContentRecord> page, @Param("params") HrBillContentRecordDTO hrBillContentRecordDTO);

    /**
     * 分页查询账单日志明细
     *
     * @param page
     * @param hrBillContentRecordDTO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrBillDTO>
     **/
    IPage<HrBillContentRecordDTO> findRecordPage(Page<HrBillContentRecord> page, @Param("params") HrBillContentRecordDTO hrBillContentRecordDTO);

    /**
     * 分页查询账单日志明细
     *
     * @param hrBillContentRecordDTO
     **/
    List<HrBillContentRecordDTO> findList(@Param("params") HrBillContentRecordDTO hrBillContentRecordDTO);

    /**
     * 不分页查询账单
     * @param hrBillContentRecordDTO
     * @return
     */
    List<HrBillContentRecordDTO> selectListBatch(@Param("params") HrBillContentRecordDTO hrBillContentRecordDTO);

}
