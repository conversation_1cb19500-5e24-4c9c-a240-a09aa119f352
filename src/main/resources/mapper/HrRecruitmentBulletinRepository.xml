<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRecruitmentBulletinRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, recruitment_bulletin_name, recruitment_brochure_id, recruitment_station_name, notice_type, notice_content, appendix_ids,achievement_type,investigation_start_time，investigation_end_time，investigation_place，
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrRecruitmentBulletin">
        <id column="id" property="id"/>
        <id column="recruitment_bulletin_name" property="recruitmentBulletinName"/>
        <result column="recruitment_brochure_id" property="recruitmentBrochureId"/>
        <result column="recruitment_station_name" property="recruitmentStationName"/>
        <result column="notice_type" property="noticeType"/>
        <result column="notice_content" property="noticeContent"/>
        <result column="achievement_type" property="achievementType"/>
        <result column="investigation_start_time" property="investigationStartTime"/>
        <result column="investigation_end_time" property="investigationEndTime"/>
        <result column="investigation_place" property="investigationPlace"/>
        <result column="appendix_ids" property="appendixIds"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findById" resultType="cn.casair.dto.HrRecruitmentBulletinDTO">
        SELECT
            hrbc.recruit_brochure_name,hc.client_name,hrbt.*
        FROM
            hr_recruitment_bulletin hrbt
        LEFT JOIN hr_recruitment_brochure hrbc ON hrbt.recruitment_brochure_id = hrbc.id
        LEFT JOIN hr_client hc ON hrbc.client_id = hc.id WHERE hrbt.id = #{id}
    </select>

    <select id="selectByRecruitmentBrochureId" resultType="cn.casair.domain.HrRecruitmentBulletin">
        SELECT * FROM hr_recruitment_bulletin WHERE recruitment_brochure_id = #{recruitmentBrochureId} AND is_delete = 0
    </select>
</mapper>
