package cn.casair.service;

import cn.casair.domain.HrAppendix;
import cn.casair.domain.HrRecruitmentBulletin;
import cn.casair.dto.HrRecruitmentBrochureDTO;
import cn.casair.dto.HrRecruitmentBulletinDTO;
import cn.casair.dto.HrRegistrationDetailsDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

/**
 * 发布公告服务类
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
public interface HrRecruitmentBulletinService extends IService<HrRecruitmentBulletin> {


    /**
     * 创建发布公告
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    HrRecruitmentBulletinDTO createHrRecruitmentBulletin(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO);


    /**
     * 创建其他公告
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    HrRecruitmentBulletinDTO saveHrRecruitmentBulletin(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO);

    /**
     * 修改发布公告
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    Optional<HrRecruitmentBulletinDTO> updateHrRecruitmentBulletin(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO);

    /**
     * 查询发布公告详情
     *
     * @param id
     * @return
     */
    HrRecruitmentBulletinDTO getHrRecruitmentBulletin(String id);

    /**
     * 分页查询发布公告
     *
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    List<HrRecruitmentBulletinDTO> findPage(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO);

    /**
     * 公告发布--查询对应的岗位
     *
     * @param recruitmentBrochureId 招聘简章ID
     * @param noticeType 公告类型
     * @return
     */
    HrRecruitmentBrochureDTO matchingRecruitmentStations(String recruitmentBrochureId, Integer noticeType);

    /**
     * 考场名称是否重复
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    ResponseEntity examRoomQueryData(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO);

    /**
     * 返回相应的成绩单附件
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    List<HrAppendix> returnResultsAppendix(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO);

    /**
     * 发布成绩公告
     * @param hrRecruitmentBulletinDTO
     */
    ResponseEntity<?>  publishResultsAnnouncement(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO);

    /**
     * 发布简章到官网
     * @param ids 招聘简章IDS
     * @return
     */
    ResponseEntity releaseOfficialWebsite(List<String> ids);

    List<HrRegistrationDetailsDTO> mergerList(List<HrRegistrationDetailsDTO> qualifiedList, Integer achievementType);

    /**
     * 是否可以发布成绩公告
     * @param hrRecruitmentBulletinDTO
     * @return
     */
    Boolean publishAchievementAnnouncement(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO);

}
