package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.RandomUtil;
import cn.casair.common.utils.excel.ExcelStyleUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.HrSpecialDeduction;
import cn.casair.dto.HrSpecialDeductionDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrSpecialDeductionExport;
import cn.casair.dto.excel.HrSpecialDeductionTemplate;
import cn.casair.mapper.HrSpecialDeductionMapper;
import cn.casair.repository.HrClientRepository;
import cn.casair.repository.HrSpecialDeductionRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrSpecialDeductionService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.component.asynchronous.ClientComponent;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-28
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrSpecialDeductionServiceImpl extends ServiceImpl<HrSpecialDeductionRepository, HrSpecialDeduction> implements HrSpecialDeductionService {
    @Value("${file.temp-path}")
    private String fileTempPath;
    private static final Logger log = LoggerFactory.getLogger(HrSpecialDeductionServiceImpl.class);
    private final HrClientRepository hrClientRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrSpecialDeductionRepository hrSpecialDeductionRepository;
    private final RedisCache redisCache;
    private final HrSpecialDeductionMapper hrSpecialDeductionMapper;
    private final CodeTableService codeTableService;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final ClientComponent clientComponent;

    public HrSpecialDeductionServiceImpl(HrClientRepository hrClientRepository, HrTalentStaffRepository hrTalentStaffRepository, HrSpecialDeductionRepository hrSpecialDeductionRepository, RedisCache redisCache, HrSpecialDeductionMapper hrSpecialDeductionMapper, CodeTableService codeTableService, HrAppendixService hrAppendixService, SysOperLogService sysOperLogService, ClientComponent clientComponent) {
        this.hrClientRepository = hrClientRepository;
        this.hrTalentStaffRepository = hrTalentStaffRepository;
        this.hrSpecialDeductionRepository = hrSpecialDeductionRepository;
        this.redisCache = redisCache;
        this.hrSpecialDeductionMapper = hrSpecialDeductionMapper;
        this.codeTableService = codeTableService;
        this.hrAppendixService = hrAppendixService;
        this.sysOperLogService = sysOperLogService;
        this.clientComponent = clientComponent;
    }

    /**
     * 创建
     *
     * @param hrSpecialDeductionDTO
     * @return
     */
    @Override
    public HrSpecialDeductionDTO createHrSpecialDeduction(HrSpecialDeductionDTO hrSpecialDeductionDTO) {
        log.info("Create new HrSpecialDeduction:{}", hrSpecialDeductionDTO);

        HrSpecialDeduction hrSpecialDeduction = this.hrSpecialDeductionMapper.toEntity(hrSpecialDeductionDTO);
        this.hrSpecialDeductionRepository.insert(hrSpecialDeduction);
        return this.hrSpecialDeductionMapper.toDto(hrSpecialDeduction);
    }

    /**
     * 修改
     *
     * @param hrSpecialDeductionDTO
     * @return
     */
    @Override
    public Optional<HrSpecialDeductionDTO> updateHrSpecialDeduction(HrSpecialDeductionDTO hrSpecialDeductionDTO) {
        return Optional.ofNullable(this.hrSpecialDeductionRepository.selectById(hrSpecialDeductionDTO.getId()))
            .map(roleTemp -> {
                HrSpecialDeduction hrSpecialDeduction = this.hrSpecialDeductionMapper.toEntity(hrSpecialDeductionDTO);
                this.hrSpecialDeductionRepository.updateById(hrSpecialDeduction);
                log.info("Update HrSpecialDeduction:{}", hrSpecialDeductionDTO);
                return hrSpecialDeductionDTO;
            });
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Override
    public HrSpecialDeductionDTO getHrSpecialDeduction(String id) {
        log.info("Get HrSpecialDeduction :{}", id);

        HrSpecialDeduction hrSpecialDeduction = this.hrSpecialDeductionRepository.selectById(id);
        return this.hrSpecialDeductionMapper.toDto(hrSpecialDeduction);
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleteHrSpecialDeduction(String id) {
        Optional.ofNullable(this.hrSpecialDeductionRepository.selectById(id))
            .ifPresent(hrSpecialDeduction -> {
                this.hrSpecialDeductionRepository.deleteById(id);
                log.info("Delete HrSpecialDeduction:{}", hrSpecialDeduction);
            });
    }

    /**
     * 批量删除
     *
     * @param ids
     */
    @Override
    public void deleteHrSpecialDeduction(List<String> ids) {
        log.info("Delete HrSpecialDeductions:{}", ids);
        this.hrSpecialDeductionRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询
     *
     * @param hrSpecialDeductionDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrSpecialDeductionDTO hrSpecialDeductionDTO, Long pageNumber, Long pageSize) {
        if(hrSpecialDeductionDTO.getField()!=null){
            if(hrSpecialDeductionDTO.getField().equals("talent_name")){
                hrSpecialDeductionDTO.setField("talentName");
            }
        }
        Page<HrSpecialDeduction> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrSpecialDeduction> qw = new QueryWrapper<>();
        qw.orderByDesc("id");
        IPage<HrSpecialDeductionDTO> iPage = this.hrSpecialDeductionRepository.Page(page, hrSpecialDeductionDTO);
        Map<Integer, String> businessMap = codeTableService.findCodeTableByInnerName("certificateType");
        for (HrSpecialDeductionDTO record : iPage.getRecords()) {
            record.setCertificateType(businessMap.get( record.getCertificateTypeKey()));
         }
        return iPage;
    }

    /**
     * 专项扣除导出
     *
     * @param
     * @param
     * @param
     * @param hrSpecialDeductionDTO
     * @return
     */
    @Override
    public String exportHrStaff(HrSpecialDeductionDTO hrSpecialDeductionDTO, HttpServletResponse response) {
        List<HrSpecialDeductionExport> hrSpecialDeductionExport = this.hrSpecialDeductionRepository.findList(hrSpecialDeductionDTO);
        Map<Integer, String> certificateType = codeTableService.findCodeTableByInnerName("certificateType");
        for (HrSpecialDeductionExport specialDeductionExport : hrSpecialDeductionExport) {
            specialDeductionExport.setCertificate(certificateType.get(specialDeductionExport.getCertificateType()));
        }
        int listSize = hrSpecialDeductionExport.size();
        List<String> ids = hrSpecialDeductionExport.stream().map(HrSpecialDeductionExport::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(hrSpecialDeductionExport, "专项扣除附加项导出", HrSpecialDeductionExport.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.SPECIAL_DEDUCTION.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 客户信息导入
     *
     * @param file
     * @param
     * @return
     */
    @Override
    public String importHrStaff(MultipartFile file ) {

        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        // 设置进度条
        String redisKey = RedisKeyEnum.progressBar.SPECIAL_DEDUCTION.getValue()+ RandomUtil.generateId()  ;
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);

        this.clientComponent.saveSpecialDeduction(file, redisKey, jwtUserDTO);
        return redisKey;
    }

    /**
     * 专项扣除信息导入模板
     *
     * @param
     * @param
     * @return
     */
    @Override
    public void importHrClientsTemplate(HttpServletResponse response) {
        ExportParams params = new ExportParams(null, "综合所得申报税款计算");
        params.setStyle(ExcelStyleUtils.class);
        params.setType(ExcelType.XSSF);
        List<HrSpecialDeductionTemplate> list = new ArrayList<>();
        Workbook workbook = ExcelExportUtil.exportExcel(params, HrSpecialDeductionTemplate.class, list);
        ExcelUtils.downLoadExcel("专项扣除导入模板.xlsx", response, workbook);
    }



}
