package cn.casair.service;

import cn.casair.domain.HrQuestion;
import cn.casair.dto.HrQuestionDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 题库表服务类
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
public interface HrQuestionService extends IService<HrQuestion> {


    /**
     * 创建题库表
     *
     * @param hrQuestionDTO
     * @return
     */
    HrQuestionDTO createHrQuestion(HrQuestionDTO hrQuestionDTO);

    /**
     * 修改题库表
     *
     * @param hrQuestionDTO
     * @return
     */
    Optional<HrQuestionDTO> updateHrQuestion(HrQuestionDTO hrQuestionDTO);

    /**
     * 查询题库表详情
     *
     * @param id
     * @return
     */
    HrQuestionDTO getHrQuestion(String id);

    /**
     * 删除题库表
     *
     * @param id
     */
    void deleteHrQuestion(String id);

    /**
     * 批量删除题库表
     *
     * @param ids
     */
    void deleteHrQuestion(List<String> ids);

    /**
     * 分页查询题库表
     *
     * @param hrQuestionDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrQuestionDTO hrQuestionDTO, Long pageNumber, Long pageSize);

    /**
     *
     * @param file
     * @return
     */
    String importQuestions(MultipartFile file);

    /**
     * 导入模板
     * @param response
     */
    String importQuestionsTemplate(HttpServletResponse response);

    /**
     * 导出
     * @param hrQuestionDTO
     * @param httpServletResponse
     * @return
     */
    String exportQuestions(HrQuestionDTO hrQuestionDTO, HttpServletResponse httpServletResponse);

    /**
     * 获取所有试题属性
     * @return
     */
    List<HrQuestionDTO> selectProList();

}
