package cn.casair.service;
import cn.casair.common.enums.SpecialBillClient;
import cn.casair.domain.HrBillDetailItems;
import cn.casair.domain.HrQuickDeduction;
import cn.casair.domain.HrSpecialDeduction;
import cn.casair.dto.HrBillDTO;
import cn.casair.dto.HrBillDetailDTO;
import cn.casair.dto.HrBillDetailItemsDTO;
import cn.casair.dto.HrBillDynamicFieldsDTO;
import cn.casair.dto.billl.BillExcelDataDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 薪酬账单动态费用详情服务类
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface HrBillDetailItemsService extends IService<HrBillDetailItems> {

    /**
     * 重新计算员工个税
     *
     * @param hrBillDetailList
     * @return cn.casair.dto.HrBillDetailDTO
     * <AUTHOR>
     * @date 2021/11/10
     **/
    List<HrBillDetailDTO> calculatePersonalTax(List<HrBillDetailDTO> hrBillDetailList);

    /**
     * 重新计算员工个税--全年一次性奖金计税
     * @param hrBillDetailList
     * @return
     */
    List<HrBillDetailDTO> recalculateBonusTax(List<HrBillDetailDTO> hrBillDetailList);

    /**
     * 修改薪酬账单总金额
     * @param hrBillDTO
     * @return
     */
    List<HrBillDetailDTO> modifySalaryBill(HrBillDTO hrBillDTO);

    /**
     * 创建薪酬账单动态费用详情
     *
     * @param hrBillDetailItemsDTO
     * @return
     */
    HrBillDetailItemsDTO createHrBillDetailItems(HrBillDetailItemsDTO hrBillDetailItemsDTO);

    /**
     * 修改薪酬账单动态费用详情
     * @param hrBillDetailItemsDTO
     * @return
     */
    Optional<HrBillDetailItemsDTO> updateHrBillDetailItems(HrBillDetailItemsDTO hrBillDetailItemsDTO);

    /**
     * 查询薪酬账单动态费用详情详情
     * @param id
     * @return
     */
    HrBillDetailItemsDTO getHrBillDetailItems(String id);

    /**
     * 删除薪酬账单动态费用详情
     * @param id
     */
    void deleteHrBillDetailItems(String id);

    /**
     * 批量删除薪酬账单动态费用详情
     * @param ids
     */
    void deleteHrBillDetailItems(List<String> ids);

    /**
     * 分页查询薪酬账单动态费用详情
     * @param hrBillDetailItemsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillDetailItemsDTO hrBillDetailItemsDTO,Long pageNumber,Long pageSize);

    /**
     *  创建薪酬账单明细
     * @param params 账单id
     */
    void createBillDetails(Map<String,Object> params);

    /**
     *  获取薪酬账单详情
     * @param billId 基础账单id
     * @return
     */
    HrBillDTO getBillDynamicDetailInfo(String billId);

    /**
     * 计算个税
     * <p>
     * 以10月为例，个税计算规则如下：
     * A. 10月累计收入额 = 9月累计收入额 + 10月应发工资。
     * B. 10月累计减除费用 = 9月累计减除费用 + (9月累计减除费用 - 8月累计减除费用)「PS：若上月累计减除费用是60000，则直接按60000计算；若小于60000，则正常计算，1月按照5000计算」。
     * C. 10月累计专项扣除 = 9月累计专项扣除 + 10月个人社保小计 + 10月个人公积金小计。
     * D. 10月累计子女教育 = 9月累计子女教育 + (9月累计子女教育 - 8月累计子女教育)。
     * E. 10月累计继续教育 = 9月累计继续教育 + (9月累计继续教育 - 8月累计继续教育)。
     * F. 10月累计住房贷款利息 =   9月累计住房贷款利息 + (9月累计住房贷款利息 - 8月累计住房贷款利息)。
     * G. 10月累计住房租金 = 9月累计住房租金 + (9月累计住房贷款利息 - 8月累计住房租金)。
     * H. 10月累计赡养老人 = 9月累计赡养老人 + (9月累计赡养老人 - 8月累计赡养老人)。
     * I. 10月累计其他扣除 = 9月累计其他扣除 + (9月累计其他扣除 - 8月累计其他扣除)。
     * J. 9月累计免税收入：由个税导盘获得。
     * K. 10月累计应纳税所得额 = A - B - C - D - E - F - G - H - I - J 。
     * L. 10月累计应纳税额 = K x 税率 - 速算扣除数。[税率等级由速算扣除规则决定]
     * M. 10月累计应扣缴税额 = L - 9月累计减免税额。
     * N. 10月个税 = M - 9月累计应扣缴税额。
     * </p>
     *
     * @param detailDTO
     * @return void
     * <AUTHOR>
     * @date 2021/11/7
     */
    void calculatePersonalIncomeTax(HrBillDetailDTO detailDTO,Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions);

    /**
     * 全年一次性奖金计算个税
     * a. 全年一次性奖金 / 12
     * b. 使用 a 匹配速算扣除数
     * c. 个税 = 全年一次性奖金 * 适用税率 - 速算扣除数
     * @param detailDTO
     */
    void annualLumpSumBonusTax(HrBillDetailDTO detailDTO);

    /**
     * 添加中石化账单费用项
     * @param hrBillDetailItemsDTO
     * @return
     */
    List<HrBillDetailItems> createBillDetailItems(HrBillDetailItemsDTO hrBillDetailItemsDTO);

    /**
     * 删除中石化账单费用项
     * @param hrBillDetailItemsDTO
     */
    void deleteBillDetailItems(HrBillDetailItemsDTO hrBillDetailItemsDTO);

    /**
     * 处理特殊处理客户薪酬账单组装表头
     * @param specialBillClient 特殊客户
     */
    void dealSpecialBillClient(SpecialBillClient specialBillClient, List<String> clientIdList, Map<String, BillExcelDataDTO> specialBillExcelDataMap, int lastPayYear, int lastPayMonthly, HrBillDynamicFieldsDTO dynamicFieldsDTO);

    /**
     * 获取一个值为0的动态费用项列表
     * @param excelDataMap
     * @return
     */
    List<HrBillDetailItemsDTO> getInitDynamicFee(Map<String, BillExcelDataDTO> excelDataMap);

    /**
     * 判断账单特殊处理客户
     * @param clientIds
     * @return
     */
    SpecialBillClient judgeSpecialBillClient(List<String> clientIds);

}
