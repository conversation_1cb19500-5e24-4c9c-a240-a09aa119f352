<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrArchivesDetailRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, archives_id, `name`, state, type,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrArchivesDetail">
        <id column="id" property="id"/>
        <result column="archives_id" property="archivesId"/>
        <result column="name" property="name"/>
        <result column="state" property="state"/>
        <result column="type" property="type"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <resultMap id="ArchivesDetailList" type="cn.casair.dto.HrArchivesDetailDTO">
        <id column="id" property="id"/>
        <result column="archives_id" property="archivesId"/>
        <result column="name" property="name"/>
        <result column="state" property="state"/>
        <result column="type" property="type"/>
        <collection property="appendixList" javaType="java.util.ArrayList" ofType="cn.casair.dto.HrAppendixDTO">
            <result column="appendixId" property="id"/>
            <result column="appendixName" property="name"/>
            <result column="origin_name" property="originName"/>
            <result column="file_type" property="fileType"/>
            <result column="file_url" property="fileUrl"/>
        </collection>
    </resultMap>

    <select id="getArchivesDetailByArchivesId" resultType="cn.casair.dto.HrArchivesDetailDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_archives_detail
        WHERE is_delete = 0
        AND archives_id = #{archivesId}
        AND state = 1
        ORDER BY created_date DESC
    </select>

    <select id="getDetailByArchivesId" resultMap="ArchivesDetailList">
        SELECT had.id,
               had.archives_id,
               had.`name`,
               had.state,
               had.type,
               ha.id appendixId,
               ha.`name` appendixName,
               ha.origin_name,
               ha.file_type,
               ha.file_url
        FROM hr_archives_detail had
        LEFT JOIN hr_appendix_union hau ON hau.union_id = had.id
        AND had.is_delete = 0
        LEFT JOIN hr_appendix ha ON ha.id = hau.appendix_id
        AND ha.is_delete = 0
        WHERE had.is_delete = 0
          AND had.archives_id = #{archivesId}
        ORDER BY had.created_date desc
    </select>

    <update id="changeDetailState">
        UPDATE hr_archives_detail
        SET state = #{state}
        WHERE id = #{id}
    </update>

    <update id="updateDetailStateByArchivesId">
        UPDATE hr_archives_detail
        SET state = #{state}
        WHERE id = #{archivesId}
    </update>

    <select id="getDetailByDetailIds" resultMap="ArchivesDetailList">
        SELECT had.id,
               had.archives_id,
               had.`name`,
               had.state,
               had.type,
               ha.id appendixId,
               ha.`name` appendixName,
               ha.origin_name,
               ha.file_type,
               ha.file_url
        FROM hr_archives_detail had
                 LEFT JOIN hr_appendix_union hau ON hau.union_id = had.id
            AND had.is_delete = 0
                 LEFT JOIN hr_appendix ha ON ha.id = hau.appendix_id
            AND ha.is_delete = 0
        WHERE had.is_delete = 0
        AND  INSTR(#{idsStr},had.id)
        ORDER BY had.created_date desc
    </select>

    <select id="getArchivesDetailByBringId" resultType="cn.casair.dto.HrArchivesDetailDTO">
        SELECT
            had.*
        FROM
            hr_archives_bring_detail abd
                LEFT JOIN hr_archives_detail had ON had.id = abd.detail_id
                AND had.is_delete = 0
        WHERE
            abd.bring_id = #{bringId}
    </select>

    <select id="getAllArchivesDetailByArchivesId" resultType="cn.casair.dto.HrArchivesDetailDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM hr_archives_detail
        WHERE is_delete = 0
            AND archives_id = #{archivesId}
        ORDER BY created_date DESC
    </select>

    <select id="getDetailByStaffId" resultMap="ArchivesDetailList">
        SELECT
            had.id,
            had.archives_id,
            had.`name`,
            had.state,
            had.type,
            ha.id appendixId,
            ha.`name` appendixName,
            ha.origin_name,
            ha.file_type,
            ha.file_url
        FROM hr_archives_detail had
        LEFT JOIN hr_archives_manage ham ON had.archives_id = ham.id
        LEFT JOIN hr_talent_staff hts ON ham.staff_id = hts.id
        LEFT JOIN hr_appendix_union hau ON hau.union_id = had.id AND had.is_delete = 0
        LEFT JOIN hr_appendix ha ON ha.id = hau.appendix_id AND ha.is_delete = 0
        WHERE had.is_delete = 0 AND ham.archives_type = 0
        AND ham.staff_id = #{staffId} AND hts.client_id = #{clientId}
        ORDER BY had.created_date desc
    </select>

    <select id="getDealByStaffId" resultType="cn.casair.domain.HrArchivesDetail">
        SELECT
            had.*
        FROM
            hr_archives_detail had
        LEFT JOIN hr_archives_manage ham ON had.archives_id = ham.id
        WHERE
            had.is_delete = 0
            AND ham.is_delete = 0
            AND ham.staff_id = #{staffId}
    </select>

    <update id="deleteBatchByArchivesId">
        UPDATE hr_archives_detail
        SET is_delete = 1
        WHERE archives_id IN
        <foreach collection="archivesIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

</mapper>
