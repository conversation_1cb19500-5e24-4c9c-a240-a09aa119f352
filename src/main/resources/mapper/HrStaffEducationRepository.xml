<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffEducationRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, staff_id, education, education_end_date, education_start_date, specialty, highest_degree, degree, study_modality, year_num, certificate,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffEducation">
                    <id column="id" property="id"/>
                    <result column="staff_id" property="staffId"/>
                    <result column="education" property="education"/>
                    <result column="education_end_date" property="educationEndDate"/>
                    <result column="education_start_date" property="educationStartDate"/>
                    <result column="specialty" property="specialty"/>
                    <result column="highest_degree" property="highestDegree"/>
                    <result column="degree" property="degree"/>
                    <result column="study_modality" property="studyModality"/>
                    <result column="year_num" property="yearNum"/>
                    <result column="certificate" property="certificate"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

</mapper>
