package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.domain.HrStaffTechnique;
import cn.casair.dto.HrStaffTechniqueDTO;
import cn.casair.mapper.HrStaffTechniqueMapper;
import cn.casair.repository.HrStaffTechniqueRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrStaffTechniqueService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 职业技术能力服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrStaffTechniqueServiceImpl extends ServiceImpl<HrStaffTechniqueRepository, HrStaffTechnique> implements HrStaffTechniqueService {

    private static final Logger log = LoggerFactory.getLogger(HrStaffTechniqueServiceImpl.class);

    private final HrStaffTechniqueRepository hrStaffTechniqueRepository;

    private final HrStaffTechniqueMapper hrStaffTechniqueMapper;

    private final CodeTableService codeTableService;

    private final SysOperLogService sysOperLogService;

    public HrStaffTechniqueServiceImpl(HrStaffTechniqueRepository hrStaffTechniqueRepository, HrStaffTechniqueMapper hrStaffTechniqueMapper, CodeTableService codeTableService, SysOperLogService sysOperLogService) {
        this.hrStaffTechniqueRepository = hrStaffTechniqueRepository;
        this.hrStaffTechniqueMapper = hrStaffTechniqueMapper;
        this.codeTableService = codeTableService;
        this.sysOperLogService = sysOperLogService;
    }

    /**
     * 创建职业技术能力
     *
     * @param hrStaffTechniqueDTO
     * @return
     */
    @Override
    public List<HrStaffTechniqueDTO> createHrStaffTechnique(HrStaffTechniqueDTO hrStaffTechniqueDTO) {
        log.info("Create new HrStaffTechnique:{}", hrStaffTechniqueDTO);

        HrStaffTechnique hrStaffTechnique = this.hrStaffTechniqueMapper.toEntity(hrStaffTechniqueDTO);
        this.hrStaffTechniqueRepository.insert(hrStaffTechnique);
        // 操作日志
        setDict(hrStaffTechniqueDTO);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_TECHNIQUE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffTechniqueDTO),
            HrStaffTechniqueDTO.class,
            null,
            JSON.toJSONString(hrStaffTechniqueDTO)
        );
        return this.findTechniqueList(hrStaffTechniqueDTO.getStaffId());
    }

    /**
     * 修改职业技术能力
     *
     * @param hrStaffTechniqueDTO
     * @return
     */
    @Override
    public Optional<List<HrStaffTechniqueDTO>> updateHrStaffTechnique(HrStaffTechniqueDTO hrStaffTechniqueDTO) {
        return Optional.ofNullable(this.hrStaffTechniqueRepository.selectById(hrStaffTechniqueDTO.getId()))
            .map(roleTemp -> {
                HrStaffTechnique hrStaffTechnique = this.hrStaffTechniqueMapper.toEntity(hrStaffTechniqueDTO);
                this.hrStaffTechniqueRepository.updateById(hrStaffTechnique);
                log.info("Update HrStaffTechnique:{}", hrStaffTechniqueDTO);
                // 操作日志
                setDict(hrStaffTechniqueDTO);
                HrStaffTechniqueDTO dto = hrStaffTechniqueMapper.toDto(roleTemp);
                setDict(dto);
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.STAFF_TECHNIQUE.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrStaffTechniqueDTO),
                    HrStaffTechniqueDTO.class,
                    null,
                    JSON.toJSONString(dto),
                    JSON.toJSONString(hrStaffTechniqueDTO),
                    null,
                    HrStaffTechniqueDTO.class
                );
                return this.findTechniqueList(hrStaffTechniqueDTO.getStaffId());
            });
    }

    /**
     * 查询职业技术能力详情
     *
     * @param id
     * @return
     */
    @Override
    public HrStaffTechniqueDTO getHrStaffTechnique(String id) {
        log.info("Get HrStaffTechnique :{}", id);

        HrStaffTechnique hrStaffTechnique = this.hrStaffTechniqueRepository.selectById(id);
        HrStaffTechniqueDTO hrStaffTechniqueDTO = this.hrStaffTechniqueMapper.toDto(hrStaffTechnique);
        this.setDict(hrStaffTechniqueDTO);
        return hrStaffTechniqueDTO;
    }

    /**
     * 删除职业技术能力
     *
     * @param id
     */
    @Override
    public List<HrStaffTechniqueDTO> deleteHrStaffTechnique(String id) {
        HrStaffTechnique hrStaffTechnique = this.hrStaffTechniqueRepository.selectById(id);
        this.hrStaffTechniqueRepository.deleteById(id);
        log.info("Delete HrStaffTechnique:{}", hrStaffTechnique);
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.STAFF_TECHNIQUE.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除" + ModuleTypeEnum.STAFF_TECHNIQUE.getValue()+": "+ hrStaffTechnique.getTechniqueName(),
            null,
            null,
            null,
            JSON.toJSONString(hrStaffTechnique),
            null
        );
        return this.findTechniqueList(hrStaffTechnique.getStaffId());
    }

    /**
     * 查询职业技术能力
     * @param staffId 员工ID
     * @return
     */
    @Override
    public List<HrStaffTechniqueDTO> findTechniqueList(String staffId) {
        List<HrStaffTechniqueDTO> hrStaffTechniqueDTOS = this.hrStaffTechniqueMapper.toDto(
            hrStaffTechniqueRepository.selectList(new QueryWrapper<HrStaffTechnique>().eq("staff_id", staffId).eq("is_delete", 0)));
        hrStaffTechniqueDTOS.forEach(this::setDict);
        return hrStaffTechniqueDTOS;
    }

    /**
     * 赋值字典值
     * @param hrStaffTechniqueDTO
     */
    public void setDict(HrStaffTechniqueDTO hrStaffTechniqueDTO) {
        if (hrStaffTechniqueDTO.getTechniqueGrade()!=null){
            Map<Integer, String> gradeType = codeTableService.findCodeTableByInnerName("gradeType");//等级
            hrStaffTechniqueDTO.setTechniqueGradeLabel(gradeType.get(hrStaffTechniqueDTO.getTechniqueGrade()));
        }
    }
}
