package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.domain.HrRecruitmentStation;
import cn.casair.dto.HrRecruitmentNeedDTO;
import cn.casair.dto.HrRecruitmentStationDTO;
import cn.casair.mapper.HrRecruitmentStationMapper;
import cn.casair.repository.HrRecruitmentStationRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrRecruitmentStationService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 招聘岗位表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrRecruitmentStationServiceImpl extends ServiceImpl<HrRecruitmentStationRepository, HrRecruitmentStation> implements HrRecruitmentStationService {


    private final HrRecruitmentStationRepository hrRecruitmentStationRepository;
    private final HrRecruitmentStationMapper hrRecruitmentStationMapper;
    private final CodeTableService codeTableService;
    private final SysOperLogService sysOperLogService;

    public HrRecruitmentStationServiceImpl(HrRecruitmentStationRepository hrRecruitmentStationRepository, HrRecruitmentStationMapper hrRecruitmentStationMapper, CodeTableService codeTableService, SysOperLogService sysOperLogService) {
        this.hrRecruitmentStationRepository = hrRecruitmentStationRepository;
        this.hrRecruitmentStationMapper = hrRecruitmentStationMapper;
        this.codeTableService = codeTableService;
        this.sysOperLogService = sysOperLogService;
    }

    /**
     * 创建招聘岗位表
     *
     * @param hrRecruitmentStationDTO
     * @return
     */
    @Override
    public HrRecruitmentStationDTO createHrRecruitmentStation(HrRecruitmentStationDTO hrRecruitmentStationDTO) {
        log.info("Create new HrRecruitmentStation:{}", hrRecruitmentStationDTO);

        HrRecruitmentStation hrRecruitmentStation = this.hrRecruitmentStationMapper.toEntity(hrRecruitmentStationDTO);
        this.hrRecruitmentStationRepository.insert(hrRecruitmentStation);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.RECRUITMENT_STATION.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrRecruitmentStationDTO),
            HrRecruitmentStationDTO.class,
            null,
            JSON.toJSONString(hrRecruitmentStationDTO)
        );
        return this.hrRecruitmentStationMapper.toDto(hrRecruitmentStation);
    }

    /**
     * 查询招聘岗位表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrRecruitmentStationDTO getHrRecruitmentStation(String id) {
        log.info("Get HrRecruitmentStation :{}", id);

        HrRecruitmentStation hrRecruitmentStation = this.hrRecruitmentStationRepository.selectById(id);
        return this.hrRecruitmentStationMapper.toDto(hrRecruitmentStation);
    }

    /**
     * 删除招聘岗位表
     *
     * @param id
     */
    @Override
    public void deleteHrRecruitmentStation(String id) {
        Optional.ofNullable(this.hrRecruitmentStationRepository.selectById(id))
            .ifPresent(hrRecruitmentStation -> {
                this.hrRecruitmentStationRepository.deleteById(id);
                log.info("Delete HrRecruitmentStation:{}", hrRecruitmentStation);
            });
    }

    /**
     * 批量删除招聘岗位表
     *
     * @param ids
     */
    @Override
    public void deleteHrRecruitmentStation(List<String> ids) {
        log.info("Delete HrRecruitmentStations:{}", ids);
        this.hrRecruitmentStationRepository.deleteBatchIds(ids);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.RECRUITMENT_STATION.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
    }

    /**
     * 分页查询招聘岗位表
     *
     * @param hrRecruitmentStationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrRecruitmentStationDTO hrRecruitmentStationDTO, Long pageNumber, Long pageSize) {
        Page<HrRecruitmentStation> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrRecruitmentStation> qw = new QueryWrapper<>(this.hrRecruitmentStationMapper.toEntity(hrRecruitmentStationDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrRecruitmentStationRepository.selectPage(page, qw);
        iPage.setRecords(this.hrRecruitmentStationMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    @Override
    public List<HrRecruitmentStationDTO> listByServiceId(String serviceId) {
        List<HrRecruitmentStationDTO> list = hrRecruitmentStationRepository.listByServiceId(serviceId);
        Map<Integer, String> examFormat = codeTableService.findCodeTableByInnerName("examFormat");
        for (HrRecruitmentStationDTO hrRecruitmentStationDTO : list) {
            hrRecruitmentStationDTO.setExamFormatName(examFormat.get(hrRecruitmentStationDTO.getExamFormat()));
        }
        return list;
    }
}
