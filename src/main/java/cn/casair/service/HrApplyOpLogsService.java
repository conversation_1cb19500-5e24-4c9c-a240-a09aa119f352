package cn.casair.service;

import cn.casair.domain.HrApplyOpLogs;
import cn.casair.dto.HrApplyOpLogsDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 申请操作日志服务类
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface HrApplyOpLogsService extends IService<HrApplyOpLogs> {

    /**
     * 保存申请操作日志
     *
     * @param applyId     申请id
     * @param checkerId   操作人id
     * @param checkerType 操作人类型(false:客户PC ture:小程序)
     * @param message     操作信息
     */
    void saveHrApplyOpLogs(String applyId, String checkerId, boolean checkerType, String message);

    /**
     * 保存操作日志---第二期PC端
     * @param applyId 申请id
     * @param applyStaffId 申请入职员工ID
     * @param checkerId 操作人id
     * @param message 操作信息
     * @param appendixIds 附件
     * @param serveType 服务类型 (0:入职申请 1:离职申请 .....)--ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey()
     */
    void saveHrApplyOpLogsPhaseTWO(String applyId,String applyStaffId, String checkerId, String message, String appendixIds, Integer serveType);

    /**
     * 保存操作日志---第二期小程序端
     * @param applyId 申请id
     * @param applyStaffId 申请员工ID ---待入职员工ID/待离职员工ID
     * @param checkerId 操作人id
     * @param message 操作信息
     * @param checkerType 操作人类型(0:客户PC 1:小程序)
     * @param serveType 服务类型 (0:入职申请 1:离职申请 .....)
     */
    void saveHrApplyOpLogsMiniPhaseTWO(String applyId, String applyStaffId, String checkerId, String message, Boolean checkerType, Integer serveType);

    /**
     * 保存操作日志---第二期小程序端
     * @param applyId 申请id
     * @param applyStaffId 申请员工ID ---待入职员工ID/待离职员工ID
     * @param checkerId 操作人id
     * @param message 操作信息
     * @param remark 备注
     * @param checkerType 操作人类型(0:客户PC 1:小程序)
     * @param serveType 服务类型 (0:入职申请 1:离职申请 .....)
     * @param appendixId 小程序上传附件信息
     */
    void saveHrApplyOpLogsEnclosure(String applyId, String applyStaffId, String checkerId, String message,String remark, Boolean checkerType, String appendixId, Integer serveType);

    /**
     * 创建申请操作日志
     *
     * @param hrApplyOpLogsDTO
     * @return
     */
    HrApplyOpLogsDTO createHrApplyOpLogs(HrApplyOpLogsDTO hrApplyOpLogsDTO);

    /**
     * 修改申请操作日志
     *
     * @param hrApplyOpLogsDTO
     * @return
     */
    Optional<HrApplyOpLogsDTO> updateHrApplyOpLogs(HrApplyOpLogsDTO hrApplyOpLogsDTO);

    /**
     * 查询申请操作日志详情
     *
     * @param id
     * @return
     */
    HrApplyOpLogsDTO getHrApplyOpLogs(String id);

    /**
     * 删除申请操作日志
     *
     * @param id
     */
    void deleteHrApplyOpLogs(String id);

    /**
     * 批量删除申请操作日志
     *
     * @param ids
     */
    void deleteHrApplyOpLogs(List<String> ids);

    /**
     * 分页查询申请操作日志
     *
     * @param hrApplyOpLogsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrApplyOpLogsDTO hrApplyOpLogsDTO, Long pageNumber, Long pageSize);

    /**
     * 查询操作信息
     * @param applyId
     * @param applyStaffId
     * @return
     */
    List<HrApplyOpLogsDTO> findApplyOpLogsList(String applyId, String applyStaffId);

    /**
     * 根据业务id查询审批记录
     * @param id
     * @return
     */
    List<HrApplyOpLogsDTO> getByApplyId(String id);
}
