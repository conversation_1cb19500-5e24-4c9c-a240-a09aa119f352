package cn.casair.service.impl;

import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.domain.HrArrivalRecordDetail;
import cn.casair.dto.HrArrivalRecordDetailDTO;
import cn.casair.mapper.HrArrivalRecordDetailMapper;
import cn.casair.repository.HrArrivalRecordDetailRepository;
import cn.casair.repository.HrArrivalRecordRepository;
import cn.casair.service.HrArrivalRecordDetailService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 到账记录明细服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrArrivalRecordDetailServiceImpl extends ServiceImpl<HrArrivalRecordDetailRepository, HrArrivalRecordDetail> implements HrArrivalRecordDetailService {

    private final HrArrivalRecordRepository hrArrivalRecordRepository;
    private final HrArrivalRecordDetailRepository hrArrivalRecordDetailRepository;
    private final HrArrivalRecordDetailMapper hrArrivalRecordDetailMapper;


    /**
     * 创建到账记录明细
     *
     * @param hrArrivalRecordDetailDTO
     * @return
     */
    @Override
    public HrArrivalRecordDetailDTO createHrArrivalRecordDetail(HrArrivalRecordDetailDTO hrArrivalRecordDetailDTO) {
        log.info("Create new HrArrivalRecordDetail:{}", hrArrivalRecordDetailDTO);

        HrArrivalRecordDetail hrArrivalRecordDetail = this.hrArrivalRecordDetailMapper.toEntity(hrArrivalRecordDetailDTO);
        this.hrArrivalRecordDetailRepository.insert(hrArrivalRecordDetail);
        // 计算到账记录汇总
        this.hrArrivalRecordRepository.subtractionTotalArrivalAmount(hrArrivalRecordDetailDTO.getArrivalId(), hrArrivalRecordDetailDTO.getArrivalAmount());
        return this.hrArrivalRecordDetailMapper.toDto(hrArrivalRecordDetail);
    }

    /**
     * 修改到账记录明细
     *
     * @param hrArrivalRecordDetailDTO
     * @return
     */
    @Override
    public Optional<HrArrivalRecordDetailDTO> updateHrArrivalRecordDetail(HrArrivalRecordDetailDTO hrArrivalRecordDetailDTO) {
        return Optional.ofNullable(this.hrArrivalRecordDetailRepository.selectById(hrArrivalRecordDetailDTO.getId()))
                .map(roleTemp -> {
                    HrArrivalRecordDetail hrArrivalRecordDetail = this.hrArrivalRecordDetailMapper.toEntity(hrArrivalRecordDetailDTO);
                    this.hrArrivalRecordDetailRepository.updateById(hrArrivalRecordDetail);
                    log.info("Update HrArrivalRecordDetail:{}", hrArrivalRecordDetailDTO);
                    return hrArrivalRecordDetailDTO;
                });
    }

    /**
     * 查询到账记录明细详情
     *
     * @param id
     * @return
     */
    @Override
    public HrArrivalRecordDetailDTO getHrArrivalRecordDetail(String id) {
        log.info("Get HrArrivalRecordDetail :{}", id);

        HrArrivalRecordDetail hrArrivalRecordDetail = this.hrArrivalRecordDetailRepository.selectById(id);
        return this.hrArrivalRecordDetailMapper.toDto(hrArrivalRecordDetail);
    }

    /**
     * 删除到账记录明细
     *
     * @param id
     */
    @Override
    public void deleteHrArrivalRecordDetail(String id) {
        Optional.ofNullable(this.hrArrivalRecordDetailRepository.selectById(id))
                .ifPresent(hrArrivalRecordDetail -> {
                    this.hrArrivalRecordDetailRepository.deleteById(id);
                    log.info("Delete HrArrivalRecordDetail:{}", hrArrivalRecordDetail);
                });
    }

    /**
     * 批量删除到账记录明细
     *
     * @param ids
     */
    @Override
    public void deleteHrArrivalRecordDetail(List<String> ids) {
        log.info("Delete HrArrivalRecordDetails:{}", ids);
        // 计算到账记录汇总

        List<HrArrivalRecordDetail> hrArrivalRecordDetailList = hrArrivalRecordDetailRepository.selectByIds(ids);
        String arrivalId = hrArrivalRecordDetailList.get(0).getArrivalId();
        BigDecimal arrivalAmount = BigDecimal.ZERO;
        for (HrArrivalRecordDetail temp : hrArrivalRecordDetailList) {
            arrivalAmount = CalculateUtils.decimalAddition(arrivalAmount, temp.getArrivalAmount());
        }
        this.hrArrivalRecordRepository.additionTotalArrivalAmount(arrivalId, arrivalAmount);
        this.hrArrivalRecordDetailRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询到账记录明细
     *
     * @param hrArrivalRecordDetailDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrArrivalRecordDetailDTO hrArrivalRecordDetailDTO, Long pageNumber, Long pageSize) {
        Page<HrArrivalRecordDetail> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrArrivalRecordDetail> qw = new QueryWrapper<>(this.hrArrivalRecordDetailMapper.toEntity(hrArrivalRecordDetailDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrArrivalRecordDetailRepository.selectPage(page, qw);
        iPage.setRecords(this.hrArrivalRecordDetailMapper.toDto(iPage.getRecords()));
        return iPage;
    }
}
