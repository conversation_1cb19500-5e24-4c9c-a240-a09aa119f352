package cn.casair.service;
import cn.casair.domain.HrBillInvoiceSubject;
import cn.casair.dto.HrBillInvoiceSubjectDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 发票内容-科目关系表服务类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
public interface HrBillInvoiceSubjectService extends IService<HrBillInvoiceSubject> {


    /**
     * 创建发票内容-科目关系表
     * @param hrBillInvoiceSubjectDTO
     * @return
     */
    HrBillInvoiceSubjectDTO createHrBillInvoiceSubject(HrBillInvoiceSubjectDTO hrBillInvoiceSubjectDTO);

    /**
     * 修改发票内容-科目关系表
     * @param hrBillInvoiceSubjectDTO
     * @return
     */
    Optional<HrBillInvoiceSubjectDTO> updateHrBillInvoiceSubject(HrBillInvoiceSubjectDTO hrBillInvoiceSubjectDTO);

    /**
     * 查询发票内容-科目关系表详情
     * @param id
     * @return
     */
    HrBillInvoiceSubjectDTO getHrBillInvoiceSubject(String id);

    /**
     * 删除发票内容-科目关系表
     * @param id
     */
    void deleteHrBillInvoiceSubject(String id);

    /**
     * 批量删除发票内容-科目关系表
     * @param ids
     */
    void deleteHrBillInvoiceSubject(List<String> ids);

    /**
     * 分页查询发票内容-科目关系表
     * @param hrBillInvoiceSubjectDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillInvoiceSubjectDTO hrBillInvoiceSubjectDTO,Long pageNumber,Long pageSize);
    }
