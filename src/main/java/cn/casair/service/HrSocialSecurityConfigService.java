package cn.casair.service;

import cn.casair.domain.HrSocialSecurityConfig;
import cn.casair.dto.HrSocialSecurityConfigDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 社保类型模板服务类
 *
 * <AUTHOR>
 * @since 2022-11-19
 */
public interface HrSocialSecurityConfigService extends IService<HrSocialSecurityConfig> {


    /**
     * 创建社保类型模板
     *
     * @param hrSocialSecurityConfigDTO
     * @return
     */
    HrSocialSecurityConfigDTO createHrSocialSecurityConfig(HrSocialSecurityConfigDTO hrSocialSecurityConfigDTO);

    /**
     * 修改社保类型模板
     *
     * @param hrSocialSecurityConfigDTO
     * @return
     */
    Optional<HrSocialSecurityConfigDTO> updateHrSocialSecurityConfig(HrSocialSecurityConfigDTO hrSocialSecurityConfigDTO);

    /**
     * 查询社保类型模板详情
     *
     * @param id
     * @return
     */
    HrSocialSecurityConfigDTO getHrSocialSecurityConfig(String id);

    /**
     * 删除社保类型模板
     *
     * @param id
     */
    void deleteHrSocialSecurityConfig(String id);

    /**
     * 批量删除社保类型模板
     *
     * @param ids
     */
    void deleteHrSocialSecurityConfig(List<String> ids);

    /**
     * 分页查询社保类型模板
     *
     * @param hrSocialSecurityConfigDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrSocialSecurityConfigDTO hrSocialSecurityConfigDTO, Long pageNumber, Long pageSize);

    /**
     * 根据社保类型ID查询
     * @param socialSecurityId 社保类型ID
     * @return 模板配置信息
     */
    HrSocialSecurityConfigDTO getBySocialSecurityId(String socialSecurityId);
}
