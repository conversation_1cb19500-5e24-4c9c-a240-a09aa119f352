<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.NcCustomerRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, client_id, short_name, nc_code, nc_name, custsuptype,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.NcCustomer">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="short_name" property="shortName"/>
        <result column="nc_code" property="ncCode"/>
        <result column="nc_name" property="ncName"/>
        <result column="custsuptype" property="custsuptype"/>
        <result column="nc_taxpayerid" property="ncTaxpayerid"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findPage" resultType="cn.casair.dto.NcCustomerDTO">
        select nc.*,
               hc.client_name
        from nc_customer nc
        left join hr_client hc on hc.id = nc.client_id
        where hc.is_delete = 0
        and nc.is_delete = 0
        <if test="param.clientId != null and param.clientId != '' ">
            and nc.client_id = #{param.clientId}
        </if>
        order by nc.created_date desc
    </select>
    <select id="findByClientId" resultType="cn.casair.dto.NcCustomerDTO">
        select nc.*,
        hc.client_name
        from nc_customer nc
        left join hr_client hc on hc.id = nc.client_id
        where hc.is_delete = 0
        and nc.is_delete = 0
        and nc.client_id = #{clientId}
    </select>
    <select id="findAll" resultType="cn.casair.dto.NcCustomerDTO">
        select nc.*,
               hc.client_name
        from nc_customer nc
                 left join hr_client hc on hc.id = nc.client_id
        where hc.is_delete = 0
          and nc.is_delete = 0
    </select>

</mapper>
