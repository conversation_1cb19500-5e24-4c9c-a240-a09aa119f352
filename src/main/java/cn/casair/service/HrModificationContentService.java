package cn.casair.service;

import cn.casair.domain.HrModificationContent;
import cn.casair.dto.HrModificationContentDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 申请修改内容服务类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
public interface HrModificationContentService extends IService<HrModificationContent> {


    /**
     * 创建申请修改内容
     *
     * @param hrModificationContentDTO
     * @return
     */
    HrModificationContentDTO createHrModificationContent(HrModificationContentDTO hrModificationContentDTO);

    /**
     * 修改申请修改内容
     *
     * @param hrModificationContentDTO
     * @return
     */
    Optional<HrModificationContentDTO> updateHrModificationContent(HrModificationContentDTO hrModificationContentDTO);

    /**
     * 查询申请修改内容详情
     *
     * @param id
     * @return
     */
    HrModificationContentDTO getHrModificationContent(String id);

    /**
     * 删除申请修改内容
     *
     * @param id
     */
    void deleteHrModificationContent(String id);

    /**
     * 批量删除申请修改内容
     *
     * @param ids
     */
    void deleteHrModificationContent(List<String> ids);

    /**
     * 分页查询申请修改内容
     *
     * @param hrModificationContentDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrModificationContentDTO hrModificationContentDTO, Long pageNumber, Long pageSize);
}
