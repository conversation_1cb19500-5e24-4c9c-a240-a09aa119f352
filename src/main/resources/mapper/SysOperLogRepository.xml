<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.SysOperLogRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,title,operator_type,business_type,oper_detail,oper_account,oper_name,oper_time,file_url,method,request_method,oper_url,oper_ip,oper_param,
        json_result,`status`,error_msg,is_delete,created_by,last_modified_by,created_date,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.SysOperLog">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="operator_type" property="operatorType"/>
        <result column="business_type" property="businessType"/>
        <result column="oper_detail" property="operDetail"/>
        <result column="oper_account" property="operAccount"/>
        <result column="oper_name" property="operName"/>
        <result column="oper_time" property="operTime"/>
        <result column="file_url" property="fileUrl"/>
        <result column="method" property="method"/>
        <result column="request_method" property="requestMethod"/>
        <result column="oper_url" property="operUrl"/>
        <result column="oper_ip" property="operIp"/>
        <result column="oper_param" property="operParam"/>
        <result column="json_result" property="jsonResult"/>
        <result column="status" property="status"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectForPage" resultType="cn.casair.dto.SysOperLogDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_oper_log
        WHERE is_delete = 0 AND `status` = 0
        <if test="param.title!=null and param.title!=''">
            AND title LIKE CONCAT('%', #{param.title}, '%')
        </if>
        <if test="param.titleList!=null and param.titleList.size()>0">
            AND title IN
            <foreach collection="param.titleList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.operatorTypes!=null and param.operatorTypes.size()>0">
            AND operator_type IN
            <foreach collection="param.operatorTypes" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.businessTypes!=null and param.businessTypes.size()>0">
            AND business_type IN
            <foreach collection="param.businessTypes" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.operDetail!=null and param.operDetail!=''">
            AND oper_detail LIKE CONCAT('%', #{param.operDetail}, '%')
        </if>
        <if test="param.operAccount!=null and param.operAccount!=''">
            AND oper_account LIKE CONCAT('%', #{param.operAccount}, '%')
        </if>
        <if test="param.operName!=null and param.operName!=''">
            AND oper_name LIKE CONCAT('%', #{param.operName}, '%')
        </if>
        <if test="param.operTimeStart!=null">
            AND oper_time &gt;= #{param.operTimeStart}
        </if>
        <if test="param.operTimeEnd!=null">
            AND oper_time &lt;= #{param.operTimeEnd}
        </if>
        <choose>
            <when test="param.field!=null and param.field!=''">
                <choose>
                    <when test="param.field=='business_type_str'">
                        ORDER BY business_type ${param.order}
                    </when>
                    <otherwise>
                        ORDER BY ${param.field} ${param.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY created_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectByIds" resultType="cn.casair.dto.SysOperLogDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_oper_log
        WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

</mapper>
