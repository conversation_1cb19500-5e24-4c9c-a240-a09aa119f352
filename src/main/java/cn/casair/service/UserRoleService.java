package cn.casair.service;

import cn.casair.domain.UserRole;
import cn.casair.dto.UserRoleDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 用户角色关联服务类
 *
 * <AUTHOR>
 * @since 2020-05-12
 */
public interface UserRoleService extends IService<UserRole> {

    /**
     * 创建用户角色关联
     *
     * @param userRoleDTO
     * @return
     */
    UserRoleDTO createUserRole(UserRoleDTO userRoleDTO);

    /**
     * 修改用户角色关联
     *
     * @param userRoleDTO
     * @return
     */
    Optional<UserRoleDTO> updateUserRole(UserRoleDTO userRoleDTO);

    /**
     * 查询用户角色关联详情
     *
     * @param id
     * @return
     */
    UserRoleDTO getUserRole(Integer id);

    /**
     * 删除用户角色关联
     *
     * @param id
     */
    void deleteUserRole(Integer id);

    /**
     * 批量删除用户角色关联
     *
     * @param ids
     */
    void deleteUserRole(List<Integer> ids);

    /**
     * 批量删除用户角色关联
     *
     * @param userIds
     * <AUTHOR> Lyric.Lin 2020/6/12 11:32
     */
    void deleteUserRoleByUserIds(List<String> userIds);

    /**
     * 分页查询用户角色关联
     *
     * @param userRoleDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(UserRoleDTO userRoleDTO, Long pageNumber, Long pageSize);

    void delete(QueryWrapper<UserRole> qw);
}
