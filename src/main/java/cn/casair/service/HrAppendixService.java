package cn.casair.service;

import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.domain.HrAppendix;
import cn.casair.dto.HrAppendixDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

/**
 * 附件表服务类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface HrAppendixService extends IService<HrAppendix> {


    /**
     * 旋转并上传图片
     *
     * @param file
     * @return
     */
    HrAppendixDTO rotateAndUploadImage(MultipartFile file);

    /**
     * 文件压缩并上传到minio
     *
     * @param fileList    文件列表
     * @param outFileName 输入文件名
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/1/13
     **/
    String zipAndUploadFile(List<File> fileList, String outFileName);

    /**
     * 上传导出Excel
     *
     * @param list
     * @param name
     * @param pojoClass
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/30
     **/
    String uploadExportFile(List<?> list, String name, Class<?> pojoClass);

    /**
     * 创建桶并上传文件
     * <p>
     * 其他方法禁止使用此方法
     * </p>
     *
     * @param bucketName
     * @param file
     * @return cn.casair.domain.HrAppendix
     * <AUTHOR>
     * @date 2021/12/28
     **/
    String createBucketAndUploadFile(String bucketName, MultipartFile file);

    /**
     * 判断是否需要上传错误导入文件
     *
     * @param importResult
     * @return void
     * <AUTHOR>
     * @date 2021/12/24
     **/
    void uploadErrorImportFile(ImportResultDTO importResult);

    /**
     * 保存附件与业务表id关联信息
     *
     * @param appendixId
     * @param unionId
     * @return int
     * <AUTHOR>
     * @date 2021/10/11
     **/
    int saveAppendixUnion(String appendixId, String unionId);

    /**
     * 根据id列表获取附件信息
     *
     * @param ids id列表
     * @return java.util.List<cn.casair.dto.HrAppendixDTO>
     * <AUTHOR>
     * @date 2021/9/16
     **/
    List<HrAppendixDTO> getHrAppendixListByIds(List<String> ids);

    /**
     * 上传导入错误文件
     *
     * @param failureFileUrl 错误文件地址
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/9/13
     **/
    String uploadErrorImportFile(String failureFileUrl);

    /**
     * 上传转换PDF文件
     * @param filePath
     * @return
     */
    String uploadPictureTransferPDF(String filePath);

    /**
     * 上传pdf合同文件到临时文件服务器目录
     *
     * @param tempPdfPath
     * @return
     */
    HrAppendix uploadTempPdf(String tempPdfPath);

    /**
     * 单文件上传
     *
     * @param file 文件
     * @return cn.casair.dto.HrAppendixDTO
     * <AUTHOR>
     * @date 2021/9/8
     **/
    HrAppendixDTO uploadSingleFile(MultipartFile file);

    /**
     * 本地文件上传
     *
     * @param filePath
     * @return cn.casair.domain.HrAppendix
     * <AUTHOR>
     * @date 2022/1/25
     **/
    String uploadLocalFile(String filePath);

    /**
     * 档案单文件上传
     *
     * @param file
     * @return cn.casair.dto.HrAppendixDTO
     * @date 2021/9/8
     */
    HrAppendixDTO uploadArchivesManage(MultipartFile file);

    /**
     * 合同文件上传
     *
     * @param filePath
     * @return cn.casair.domain.HrAppendix
     * <AUTHOR>
     * @date 2021/9/29
     **/
    HrAppendix uploadContractFile(String filePath);

    /**
     * 多文件上传
     *
     * @param files 文件列表
     * @return cn.casair.dto.HrAppendixDTO
     * <AUTHOR>
     * @date 2021/9/8
     **/
    List<HrAppendixDTO> uploadMultipleFile(List<MultipartFile> files);

    /**
     * 查询附件表详情
     *
     * @param id
     * @return
     */
    HrAppendixDTO getHrAppendix(String id);

    /**
     * 根据文件url删除文件
     *
     * @param fileUrl
     * @return void
     * <AUTHOR>
     * @date 2022/1/17
     **/
    void deleteHrAppendixByUrl(String fileUrl);

    /**
     * 删除附件表
     *
     * @param id
     */
    void deleteHrAppendix(String id);

    /**
     * 批量删除附件表
     *
     * @param ids
     */
    void deleteHrAppendix(List<String> ids);

    /**
     * 分页查询附件表
     *
     * @param hrAppendixDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrAppendixDTO hrAppendixDTO, Long pageNumber, Long pageSize);

    /**
     * 根据业务id删除
     * @param id
     */
    void deleteByUnionId(String id);

    /**
     * 根据业务id查询关联的附件列表
     * @param id
     * @return
     */
    List<HrAppendixDTO> getByUnionId(String id);

    /**
     * 生成word文件
     * @param content 内容
     * @param targetFileName 文件名
     * @param suffix 文件格式
     * @return 路径
     */
    String generateWordFile(String content, String targetFileName, String suffix);

    /**
     * 导入文件
     *
     * @param file
     * @return
     */
    HrAppendix uploadImportFile(String file);

    /**
     * 创建桶
     *
     * @param bucketName
     * @param files
     * @return cn.casair.dto.HrAppendixDTO
     * <AUTHOR>
     * @date 2021/12/16
     **/
    List<String> createBucket(String bucketName, List<MultipartFile> files);
}
