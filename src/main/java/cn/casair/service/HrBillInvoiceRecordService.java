package cn.casair.service;
import cn.casair.domain.HrBillInvoiceRecord;
import cn.casair.dto.HrBillInvoiceRecordDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 开票明细服务类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface HrBillInvoiceRecordService extends IService<HrBillInvoiceRecord> {


    /**
     * 创建开票明细
     * @param hrBillInvoiceRecordDTO
     * @return
     */
    HrBillInvoiceRecordDTO createHrBillInvoiceRecord(HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO);

    /**
     * 修改开票明细
     * @param hrBillInvoiceRecordDTO
     * @return
     */
    Optional<HrBillInvoiceRecordDTO> updateHrBillInvoiceRecord(HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO);

    /**
     * 查询开票明细详情
     * @param id
     * @return
     */
    HrBillInvoiceRecordDTO getHrBillInvoiceRecord(String id);

    /**
     * 删除开票明细
     * @param id
     */
    void deleteHrBillInvoiceRecord(String id);

    /**
     * 批量删除开票明细
     * @param ids
     */
    void deleteHrBillInvoiceRecord(List<String> ids);

    /**
     * 分页查询开票明细
     * @param hrBillInvoiceRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO,Long pageNumber,Long pageSize);

    /**
     * 根据发票id获取发票明细
     * @param id
     * @return
     */
    List<HrBillInvoiceRecordDTO> getByInvoiceId(String id);

    /**
     * 根据申请发票id删除发票明细
     * @param id
     */
    void deleteByInvoiceId(String id);

    /**
     * 修改开票明细状态
     * @param ids 明细id
     * @param state 锁定状态
     */
    void updateState(List<String> ids, Integer state);

    /**
     * 根据开票ID修改开票明细状态
     * @param invoiceIds 开票IDS
     * @param invoiceTypes 发票类型
     * @param state 锁定状态
     */
    int updateStateByInvoiceId(List<String> invoiceIds, List<HrBillInvoiceRecordDTO> invoiceTypes, Integer state);

    /**
     * 根据明细ID查询
     * @param invoiceRecordIds
     * @return
     */
    List<HrBillInvoiceRecordDTO> getByIdBatch(List<String> invoiceRecordIds);

}
