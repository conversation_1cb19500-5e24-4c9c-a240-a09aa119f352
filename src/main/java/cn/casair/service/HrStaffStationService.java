package cn.casair.service;

import cn.casair.domain.HrStaffStation;
import cn.casair.dto.HrStaffStationDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 员工岗位信息服务类
 *
 * <AUTHOR>
 * @since 2021-09-04
 */
public interface HrStaffStationService extends IService<HrStaffStation> {


    /**
     * 创建员工岗位信息
     *
     * @param hrStaffStationDTO
     * @return
     */
    HrStaffStationDTO createHrStaffStation(HrStaffStationDTO hrStaffStationDTO);

    /**
     * 修改员工岗位信息
     *
     * @param hrStaffStationDTO
     * @return
     */
    Optional<HrStaffStationDTO> updateHrStaffStation(HrStaffStationDTO hrStaffStationDTO);

    /**
     * 查询员工岗位信息详情
     *
     * @param id
     * @return
     */
    HrStaffStationDTO getHrStaffStation(String id);

    /**
     * 删除员工岗位信息
     *
     * @param id
     */
    void deleteHrStaffStation(String id);

    /**
     * 批量删除员工岗位信息
     *
     * @param ids
     */
    void deleteHrStaffStation(List<String> ids);

    /**
     * 分页查询员工岗位信息
     *
     * @param hrStaffStationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrStaffStationDTO hrStaffStationDTO, Long pageNumber, Long pageSize);
}
