<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrContractViewRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, client_id, staff_id, contract_id, apply_state, apply_description, states, is_need, is_again_apply, audit_date,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrContractView">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="staff_id" property="staffId"/>
        <result column="contract_id" property="contractId"/>
        <result column="apply_state" property="applyState"/>
        <result column="apply_description" property="applyDescription"/>
        <result column="states" property="states"/>
        <result column="is_need" property="isNeed"/>
        <result column="is_again_apply" property="isAgainApply"/>
        <result column="audit_date" property="auditDate"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Select_Sql">
        SELECT
        hc.client_name,
        hts.`name`,
        hts.staff_status,
        hts.certificate_num,
        hts.sex,
        IF(hts.sex = 1, '男', '女') AS sexLabel,
        hts.phone,
        hswe.staff_id,
        hs.profession_name,
        hts.personnel_type,
        hcv.*,
        su.real_name AS specialized
        FROM
        hr_contract_view hcv
        LEFT JOIN hr_talent_staff hts ON hcv.staff_id = hts.id AND hts.iz_default = 0 AND hts.is_delete = 0
        LEFT JOIN hr_staff_work_experience hswe ON hswe.staff_id = hts.id AND hts.client_id = hswe.client_id AND hswe.iz_default = 1 AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hswe.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN hr_client hc ON hcv.client_id = hc.id AND hc.is_delete = 0
        LEFT JOIN sys_user su ON hc.specialized_id = su.id AND su.is_delete = 0
        WHERE hcv.is_delete = 0
        <if test="permissionClient!=null and permissionClient.size>0">
            AND hcv.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.ids!=null and param.ids.size>0">
            AND hcv.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size()>0">
            AND hcv.client_id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.name != null and param.name != ''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.sex != null">
            AND hts.sex = #{param.sex}
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND hts.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.professionIdList!=null and param.professionIdList.size() > 0">
            AND hswe.station_id IN
            <foreach collection="param.professionIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.personnelTypeList!=null and param.personnelTypeList.size() > 0">
            AND hts.personnel_type IN
            <foreach collection="param.personnelTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.staffStatusList!=null and param.staffStatusList.size() > 0">
            AND hts.staff_status IN
            <foreach collection="param.staffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.createdDateStart != null">
            AND hcv.created_date >= #{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd != null">
            AND hcv.created_date &lt; date_add(#{param.createdDateEnd}, interval 1 day)
        </if>
        <if test="param.applyState != null">
            AND hcv.apply_state = #{param.applyState}
        </if>
        <if test="param.statesList!=null and param.statesList.size() > 0">
            AND hcv.states IN
            <foreach collection="param.statesList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.userIdList!=null and param.userIdList.size() > 0">
            AND su.id IN
            <foreach collection="param.userIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <choose>
            <when test="param.field != null and param.field!= '' and param.order != null and param.order != ''">
                ORDER BY ${param.field} ${param.order}
            </when>
            <otherwise>
                ORDER BY hcv.created_date DESC
            </otherwise>
        </choose>
    </sql>
    <select id="findPage" resultType="cn.casair.dto.HrContractViewDTO">
        <include refid="Select_Sql"></include>
    </select>
    <select id="findList" resultType="cn.casair.dto.HrContractViewDTO">
        <include refid="Select_Sql"></include>
    </select>

    <select id="findInfoById" resultType="cn.casair.dto.HrContractViewDTO">
        SELECT
            hc.client_name,
            hc.user_id,
            hts.`name`,
            hts.certificate_num,
            hts.sex,
            IF(hts.sex = 1, '男', '女') AS sexLabel,
            hts.phone,
            hswe.staff_id,
            hs.profession_name,
            hts.personnel_type,
            hct.contract_start_date,
            hct.contract_end_date,
            hcv.*
        FROM
            hr_contract_view hcv
        LEFT JOIN hr_talent_staff hts ON hcv.staff_id = hts.id AND hts.iz_default = 0
        LEFT JOIN hr_staff_work_experience hswe ON hswe.staff_id = hts.id AND hts.client_id = hswe.client_id AND hswe.iz_default = 1 AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hswe.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN hr_client hc ON hcv.client_id = hc.id
        LEFT JOIN hr_contract hct ON hcv.contract_id = hct.id
        WHERE hcv.is_delete = 0 AND hcv.id = #{id}
    </select>

    <select id="findBatchIds" resultType="cn.casair.dto.HrContractViewDTO">
        SELECT
        hc.client_name,
        hts.`name`,
        hts.certificate_num,
        hts.sex,
        IF(hts.sex = 1, '男', '女') AS sexLabel,
        hts.phone,
        hswe.staff_id,
        hs.profession_name,
        hts.personnel_type,
        hct.contract_start_date,
        hct.contract_end_date,
        hcv.*
        FROM
        hr_contract_view hcv
        LEFT JOIN hr_talent_staff hts ON hcv.staff_id = hts.id AND hts.iz_default = 0
        LEFT JOIN hr_staff_work_experience hswe ON hswe.staff_id = hts.id AND hts.client_id = hswe.client_id AND hswe.iz_default = 1 AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hswe.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN hr_client hc ON hcv.client_id = hc.id
        LEFT JOIN hr_contract hct ON hcv.contract_id = hct.id
        WHERE hcv.is_delete = 0 AND hcv.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <update id="updateContractView">
        UPDATE hr_contract_view
        <set>
            <if test="param.states != null">states = #{param.states},</if>
            <if test="param.auditDate != null">audit_date = #{param.auditDate},</if>
            <if test="param.isAgainApply != null">is_again_apply = #{param.isAgainApply},</if>
            <if test="param.auditDate != null">audit_date = #{param.auditDate},</if>
            <if test="param.isNeed != null">is_need = #{param.isNeed}</if>
        </set>
        WHERE id = #{param.id}
    </update>

</mapper>
