package cn.casair.service;

import cn.casair.domain.HrSeals;
import cn.casair.dto.HrSealsDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
public interface HrSealsService extends IService<HrSeals> {


    /**
     * 获取公章列表
     *
     * @return
     */
    List<HrSealsDTO> getSealsList();

    /**
     * 创建
     *
     * @param hrSealsDTO
     * @return
     */
    HrSealsDTO createHrSeals(HrSealsDTO hrSealsDTO);

    /**
     * 修改
     *
     * @param hrSealsDTO
     * @return
     */
    Optional<HrSealsDTO> updateHrSeals(HrSealsDTO hrSealsDTO);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    HrSealsDTO getHrSeals(String id);

    /**
     * 删除
     *
     * @param id
     */
    void deleteHrSeals(String id);

    /**
     * 批量删除
     *
     * @param ids
     */
    void deleteHrSeals(List<String> ids);

    /**
     * 分页查询
     *
     * @param hrSealsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrSealsDTO hrSealsDTO, Long pageNumber, Long pageSize);

    List<HrSealsDTO> getSealses();

    ResponseEntity delectHrSeals(HrSealsDTO hrSealsDTO);
}
