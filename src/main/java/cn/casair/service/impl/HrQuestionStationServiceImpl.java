package cn.casair.service.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrQuestionStation;
import cn.casair.dto.HrQuestionStationDTO;
import cn.casair.repository.HrQuestionStationRepository;
import cn.casair.mapper.HrQuestionStationMapper;
import cn.casair.service.HrQuestionStationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;
/**
 * 岗位题库关联表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrQuestionStationServiceImpl extends ServiceImpl<HrQuestionStationRepository, HrQuestionStation>implements HrQuestionStationService {

    private static final Logger log=LoggerFactory.getLogger(HrQuestionStationServiceImpl.class);

    private final HrQuestionStationRepository hrQuestionStationRepository;

    private final HrQuestionStationMapper hrQuestionStationMapper;

    public HrQuestionStationServiceImpl(HrQuestionStationRepository hrQuestionStationRepository, HrQuestionStationMapper hrQuestionStationMapper){
    this.hrQuestionStationRepository = hrQuestionStationRepository;
    this.hrQuestionStationMapper= hrQuestionStationMapper;
    }

    /**
     * 创建岗位题库关联表
     * @param hrQuestionStationDTO
     * @return
     */
    @Override
    public HrQuestionStationDTO createHrQuestionStation(HrQuestionStationDTO hrQuestionStationDTO){
    log.info("Create new HrQuestionStation:{}", hrQuestionStationDTO);

    HrQuestionStation hrQuestionStation =this.hrQuestionStationMapper.toEntity(hrQuestionStationDTO);
    this.hrQuestionStationRepository.insert(hrQuestionStation);
    return this.hrQuestionStationMapper.toDto(hrQuestionStation);
    }

    /**
     * 修改岗位题库关联表
     * @param hrQuestionStationDTO
     * @return
     */
    @Override
    public Optional<HrQuestionStationDTO>updateHrQuestionStation(HrQuestionStationDTO hrQuestionStationDTO){
    return Optional.ofNullable(this.hrQuestionStationRepository.selectById(hrQuestionStationDTO.getId()))
    .map(roleTemp->{
    HrQuestionStation hrQuestionStation =this.hrQuestionStationMapper.toEntity(hrQuestionStationDTO);
    this.hrQuestionStationRepository.updateById(hrQuestionStation);
    log.info("Update HrQuestionStation:{}", hrQuestionStationDTO);
    return hrQuestionStationDTO;
    });
    }

    /**
     * 查询岗位题库关联表详情
     * @param id
     * @return
     */
    @Override
    public HrQuestionStationDTO getHrQuestionStation(String id){
    log.info("Get HrQuestionStation :{}",id);

    HrQuestionStation hrQuestionStation =this.hrQuestionStationRepository.selectById(id);
    return this.hrQuestionStationMapper.toDto(hrQuestionStation);
    }

    /**
     * 删除岗位题库关联表
     * @param id
     */
    @Override
    public void deleteHrQuestionStation(String id){
    Optional.ofNullable(this.hrQuestionStationRepository.selectById(id))
    .ifPresent(hrQuestionStation ->{
    this.hrQuestionStationRepository.deleteById(id);
    log.info("Delete HrQuestionStation:{}", hrQuestionStation);
    });
    }

    /**
     * 批量删除岗位题库关联表
     * @param ids
     */
    @Override
    public void deleteHrQuestionStation(List<String>ids){
    log.info("Delete HrQuestionStations:{}",ids);
    this.hrQuestionStationRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询岗位题库关联表
     * @param hrQuestionStationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrQuestionStationDTO hrQuestionStationDTO,Long pageNumber,Long pageSize){
    Page<HrQuestionStation>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrQuestionStation>qw=new QueryWrapper<>(this.hrQuestionStationMapper.toEntity(hrQuestionStationDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrQuestionStationRepository.selectPage(page,qw);
    iPage.setRecords(this.hrQuestionStationMapper.toDto(iPage.getRecords()));
    return iPage;
    }
}
