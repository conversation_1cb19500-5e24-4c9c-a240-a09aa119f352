package cn.casair.service;

import cn.casair.domain.HrRegistrationInfo;
import cn.casair.dto.HrRegistrationDetailsDTO;
import cn.casair.dto.HrRegistrationInfoDTO;
import cn.casair.dto.HrTalentStaffDTO;
import cn.casair.dto.WebLoginDTO;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

/**
 * 报名信息表服务类
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
public interface HrRegistrationInfoService extends IService<HrRegistrationInfo> {


    /**
     * 创建报名信息表
     * @param hrRegistrationInfoDTO
     * @return
     */
    HrRegistrationInfoDTO createHrRegistrationInfo(HrRegistrationInfoDTO hrRegistrationInfoDTO);

    /**
     * 修改报名信息表
     * @param hrRegistrationInfoDTO
     * @return
     */
    Optional<HrRegistrationInfoDTO> updateHrRegistrationInfo(HrRegistrationInfoDTO hrRegistrationInfoDTO);

    /**
     * 查询报名信息表详情
     * @param id
     * @return
     */
    HrRegistrationInfoDTO getHrRegistrationInfo(String id);

    /**
     * 删除报名信息表
     * @param id
     */
    void deleteHrRegistrationInfo(String id);

    /**
     * 批量删除报名信息表
     * @param ids
     */
    void deleteHrRegistrationInfo(List<String> ids);

    /**
     * 分页查询报名信息表
     * @param hrRegistrationInfoDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRegistrationInfoDTO hrRegistrationInfoDTO,Long pageNumber,Long pageSize);

    Object getRegistrationopenId(String id);

    String getRegistratiupdateopenId(String id);

    HrRegistrationInfoDTO getRegistratiselectOpendId(HrRegistrationInfoDTO hrRegistrationInfoDTO);

    ResponseEntity<?> getHrRegistrationInfoPhone(HrTalentStaffDTO hrTalentStaff);

    JSONObject getHrRegistrationInfoPhoneEffectiveness(WebLoginDTO webLoginDTO);

    String sendAppCodes(WebLoginDTO appLoginDTO);

    HrRegistrationDetailsDTO getRegistratiinformation(String serviceId);
}
