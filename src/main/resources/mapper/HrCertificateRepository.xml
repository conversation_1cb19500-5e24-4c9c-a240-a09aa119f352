<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrCertificateRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,certificate_name,certificate_type,certificate_attribute,certificate_quantity,certificate_limited_quantity,certificate_scope_start_quantity,
        certificate_scope_end_quantity,certificate_state,is_delete,created_by,last_modified_by,created_date,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrCertificate">
        <id column="id" property="id"/>
        <result column="certificate_name" property="certificateName"/>
        <result column="certificate_type" property="certificateType"/>
        <result column="certificate_attribute" property="certificateAttribute"/>
        <result column="certificate_quantity" property="certificateQuantity"/>
        <result column="certificate_limited_quantity" property="certificateLimitedQuantity"/>
        <result column="certificate_scope_start_quantity" property="certificateScopeStartQuantity"/>
        <result column="certificate_scope_end_quantity" property="certificateScopeEndQuantity"/>
        <result column="certificate_state" property="certificateState"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getById" resultType="cn.casair.dto.HrCertificateDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_certificate
        WHERE id = #{id}
        AND is_delete = 0
    </select>
    <select id="selectCertificateName" resultType="cn.casair.dto.HrCertificateDTO">
        SELECT * from hr_certificate where certificate_name =#{certificateName} and is_delete=0
    </select>
</mapper>
