package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.enums.SpecialBillClient;
import cn.casair.common.enums.StaffEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.RandomUtil;
import cn.casair.common.utils.StringUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrClientExport;
import cn.casair.mapper.HrClientMapper;
import cn.casair.mapper.HrProtocolMapper;
import cn.casair.ncc.NccService;
import cn.casair.ncc.dto.NccCustomerCreateResultDTO;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.asynchronous.ClientComponent;
import cn.casair.service.util.SecurityUtils;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 客户组织架构表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrClientServiceImpl extends ServiceImpl<HrClientRepository, HrClient> implements HrClientService {

    @Value("${file.temp-path}")
    private String fileTempPath;
    private final CodeTableService codeTableService;
    private final HrClientRepository hrClientRepository;
    private final HrClientMapper hrClientMapper;
    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;
    private final HrProtocolMapper hrProtocolMapper;
    private final HrProtocolRepository hrProtocolRepository;
    private final HrUserClientRepository hrUserClientRepository;
    private final RedisCache redisCache;
    private final RoleService roleService;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final HrFeeReviewRepository hrFeeReviewRepository;
    private final HrUpcomingRepository hrUpcomingRepository;
    private final HrPaperManagementRepository hrPaperManagementRepository;
    private final HrPaperClientRepository hrPaperClientRepository;
    private final ClientComponent clientComponent;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final NccService nccService;
    private final NcCustomerService ncCustomerService;
    @Value("${minio.excelPrefix}")
    private String excelPrefix;


    @Override
    public List<EnumDTO> specialClient() {
        List<EnumDTO> result = new ArrayList<>();
        for (SpecialBillClient specialBillClient : SpecialBillClient.values()) {
            result.add(new EnumDTO().setKey(specialBillClient.getKey()).setValue(String.valueOf(specialBillClient.getLevel())).setName(specialBillClient.getName()));
        }
        return result;
    }

    /**
     * 创建客户组织架构表
     *
     * @param hrClientDTO
     * @return
     */
    @Override
    public HrClientDTO createHrClient(HrClientDTO hrClientDTO) {
        log.info("Create new HrClient:{}", hrClientDTO);
        int i = this.hrClientRepository.getunitNumber(hrClientDTO.getUnitNumber());
        if (i > 0) {
            throw new CommonException("单位编码重复，请重新进行填写!");
        }
        //客户用户名效验
        int getuser = this.hrClientRepository.getclientUser(hrClientDTO.getUserName());
        if (getuser > 0) {
            throw new CommonException("用户名重复，请重新进行填写!");
        }
        //客户名称效验
        int getclientnames = this.hrClientRepository.getclientName(hrClientDTO.getClientName());
        if (getclientnames > 0) {
            throw new CommonException("客户名称重复，请重新进行填写!");
        }

        Integer roleid = this.hrClientRepository.getroleId("client");
        hrClientDTO.setRoleIdList(roleid);
        if (hrClientDTO.getUserName() != null && hrClientDTO.getPassword() != null) {
            // 创建用户
            User user = new User();
            user.setUserName(hrClientDTO.getUserName());
            user.setRealName(hrClientDTO.getClientName());
            user.setPassword(passwordEncoder.encode("c123456"));
            log.info("insert user: {}", user);
            this.userRepository.insert(user);
            // 创建角色关联
            Integer roleIdList = hrClientDTO.getRoleIdList();
            if (roleIdList != null) {
                UserRole userRole = new UserRole();
                userRole.setUserId(user.getId());
                userRole.setRoleId(roleIdList);
                this.hrClientRepository.insertuserrole(userRole);
            }

            //创建客户
            HrClient hrClient = this.hrClientMapper.toEntity(hrClientDTO);
            hrClient.setUserId(user.getId());

            this.hrClientRepository.insert(hrClient);
            //创建试卷
            QueryWrapper<HrPaperManagement> ew = new QueryWrapper<>();
            ew.eq("is_preset", 1);
            HrPaperManagement hrPaperManagement = this.hrPaperManagementRepository.selectOne(ew);
            HrPaperClient hrPaperClient = new HrPaperClient();
            hrPaperClient.setClientId(hrClient.getId());
            hrPaperClient.setPaperId(hrPaperManagement.getId());
            this.hrPaperClientRepository.insert(hrPaperClient);


            //专管员进行关联
            HrUserClient hrUserClient = new HrUserClient();
            if (hrClientDTO.getSpecializedId() != null) {
                hrUserClient.setUserId(hrClientDTO.getSpecializedId());
                hrUserClient.setClientId(hrClient.getId());
                hrUserClient.setIsSpecialized(true);
                this.hrUserClientRepository.insert(hrUserClient);
            }
            //添加多协议
            if (hrClientDTO.getAgreementId() != null) {
                HrProtocol protocolList = this.hrProtocolRepository.selectById(hrClientDTO.getAgreementId());
                protocolList.setClientId(hrClient.getId());
                protocolList.setId(null);
                protocolList.setUseStatus(1);
                protocolList.setType(0);
                this.hrProtocolRepository.insert(protocolList);
            }
            //获取数据字典业务类型
            Map<Integer, String> businessType = codeTableService.findCodeTableByInnerName("businessType");
            hrClientDTO.setBusinessTypekey(businessType.get(hrClient.getBusinessType()));
            if (StringUtils.isNotEmpty(hrClient.getParentId())) {
                HrClient hrClientas = this.hrClientRepository.selectById(hrClient.getParentId());
                hrClientDTO.setSuperiorUnit(hrClientas.getClientName());
            }
            //获取数据字典企业性质
            Map<Integer, String> enterprise = codeTableService.findCodeTableByInnerName("enterprise");
            hrClientDTO.setEnterpriseNaturekey(enterprise.get(hrClient.getEnterpriseNature()));


            // 操作日志
            this.sysOperLogService.insertSysOperLog(
                ModuleTypeEnum.CLIENT.getValue(),
                BusinessTypeEnum.INSERT.getKey(),
                JSON.toJSONString(hrClientDTO),
                HrClientDTO.class,
                null,
                JSON.toJSONString(hrClient)
            );

            HrClientDTO clientDTO = this.hrClientMapper.toDto(hrClient);
            // TODO Jigsaw ！ - 2025/8/22 ：客户同步ncc
            try {
                NccCustomerCreateResultDTO nccCustomerCreateResultDTO = nccService.createCustomer(hrClient.getClientName(), hrClientDTO.getNccCustomerType());
                NcCustomer ncCustomer = new NcCustomer();
                ncCustomer.setClientId(hrClient.getId());
//                ncCustomer.setNcCode(nccCustomerCreateResultDTO.getClientCode());
//                ncCustomer.setNcName(nccCustomerCreateResultDTO.getClientName());
//                ncCustomer.setCustsuptype(nccCustomerCreateResultDTO.getClientType());
                this.ncCustomerService.save(ncCustomer);
            } catch (Exception e) {
                clientDTO.setNccMessage("ncc客户创建失败，" + e.getMessage());
                log.error("创建客商失败:{}", e.getMessage());
            }

            return clientDTO;
        }
        throw new CommonException("账号密码不能为空");
    }

    /**
     * 修改客户组织架构表
     *
     * @param hrClientDTO
     * @return
     */
    @Override
    public HrClientDTO updateHrClient(HrClientDTO hrClientDTO) {
        if (hrClientDTO.getFlag() == null) {
            if (StringUtils.isEmpty(hrClientDTO.getParentId())) {
                hrClientDTO.setParentId("0");
            }
        } else {
            HrClient hrClient = hrClientRepository.selectById(hrClientDTO.getId());
            hrClientDTO.setParentId(hrClient.getParentId());
            hrClientDTO.setSpecializedId(hrClient.getSpecializedId());
        }
        //专管员进行关联
        HrUpcomingDTO hrUpcomingDTO = hrUpcomingRepository.selectUserIdByClientId(hrClientDTO.getId());
        if (hrClientDTO.getSpecializedId() != null) {
            if (!hrUpcomingDTO.getClientUserId().equals(hrClientDTO.getSpecializedId())) {
                hrUserClientRepository.delete(new QueryWrapper<HrUserClient>().eq("client_id", hrClientDTO.getId()).eq("is_specialized", 1));
                HrUserClient hrUserClient = new HrUserClient();
                hrUserClient.setUserId(hrClientDTO.getSpecializedId());
                hrUserClient.setClientId(hrClientDTO.getId());
                hrUserClient.setIsSpecialized(true);
                this.hrUserClientRepository.insert(hrUserClient);
            }
        } else {
            hrClientDTO.setSpecializedId(null);
            hrUserClientRepository.delete(new QueryWrapper<HrUserClient>().eq("client_id", hrClientDTO.getId()).eq("is_specialized", 1));
        }
        HrClient hrClient = this.hrClientMapper.toEntity(hrClientDTO);
        this.hrClientRepository.updateById(hrClient);
        //获取数据字典业务类型
        Map<Integer, String> businessType = codeTableService.findCodeTableByInnerName("businessType");
        hrClientDTO.setBusinessTypekey(businessType.get(hrClient.getBusinessType()));
        if (!hrClientDTO.getParentId().equals("0")) {
            if (StringUtils.isNotEmpty(hrClientDTO.getParentId())) {
                HrClient hrClientas = this.hrClientRepository.selectById(hrClientDTO.getParentId());
                hrClientDTO.setSuperiorUnit(hrClientas.getClientName());
            }
        }
        log.info("Update HrClient:{}", hrClientDTO);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.CLIENT.getValue(),
            BusinessTypeEnum.UPDATE.getKey(),
            JSON.toJSONString(hrClientDTO),
            HrClientDTO.class,
            null,
            JSON.toJSONString(hrClientDTO)
        );
        return hrClientDTO;

    }

    /**
     * 查询客户组织架构表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrsocialDTO getHrClient(String id) {
        log.info("Get HrClient :{}", id);
        HrsocialDTO hrsocialDTO = new HrsocialDTO();
        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String newdate = format.format(date);
        List<HrClientDTO> hrClient = this.hrClientRepository.getClientId(id);
        hrsocialDTO.setHrClientDTO(hrClient);
        HrProtocolDTO hrProtocolDTO = this.hrClientRepository.getprotocoendtime(id);
        if (hrProtocolDTO != null) {
            if (hrProtocolDTO.getAgreementEndDate() != null && hrProtocolDTO.getAgreementStartDate() != null) {
                hrsocialDTO.getHrClientDTO().get(0).setAgreementEndDate(hrProtocolDTO.getAgreementEndDate());
                hrsocialDTO.getHrClientDTO().get(0).setAgreementStartDate(hrProtocolDTO.getAgreementStartDate());
            }
        }
        if (hrsocialDTO.getHrClientDTO() != null) {
            if (hrsocialDTO.getHrClientDTO().size() != 0) {
                //逻辑获取客户类型
                if (hrsocialDTO.getHrClientDTO().get(0).getAgreementEndDate() != null) {
                    String endtime = String.valueOf(hrsocialDTO.getHrClientDTO().get(0).getAgreementEndDate());
                    String starttime = String.valueOf(hrsocialDTO.getHrClientDTO().get(0).getAgreementStartDate());

                    if (endtime.compareTo(newdate) < 0) {
                        //过期客户
                        hrsocialDTO.getHrClientDTO().get(0).setCustomerType("0");
                    }
                    if (endtime.compareTo(newdate) > 0 && hrsocialDTO.getHrClientDTO().get(0).getStatus().equals(3)) {
                        //异常客户
                        hrsocialDTO.getHrClientDTO().get(0).setCustomerType("1");
                    }
                    if (endtime != null && starttime != null && endtime.compareTo(newdate) >= 0 && starttime.compareTo(newdate) <= 0) {
                        //正常客户
                        hrsocialDTO.getHrClientDTO().get(0).setCustomerType("2");
                    }
                    if (starttime.compareTo(newdate) > 0) {
                        //待期客户
                        hrsocialDTO.getHrClientDTO().get(0).setCustomerType("4");
                    }

                } else {
                    //潜在客户
                    hrsocialDTO.getHrClientDTO().get(0).setCustomerType("3");
                }

                //获取数据字典企业性质
                Map<Integer, String> enterprisemap = codeTableService.findCodeTableByInnerName("enterprise");
                hrsocialDTO.getHrClientDTO().get(0).setEnterpriseNaturekey(enterprisemap.get(hrClient.get(0).getEnterpriseNature()));
                hrsocialDTO.getHrClientDTO().get(0).setEnterpriseNature(hrClient.get(0).getEnterpriseNature());
                //获取数据字典业务类型
                Map<Integer, String> businessmap = codeTableService.findCodeTableByInnerName("agreementType");
                hrsocialDTO.getHrClientDTO().get(0).setAgreementTypekey(businessmap.get(hrsocialDTO.getHrClientDTO().get(0).getBusinessType()));
                hrsocialDTO.getHrClientDTO().get(0).setAgreementType(hrClient.get(0).getBusinessType());
                //获取数据字典行业类别
                Map<Integer, String> industrymap = codeTableService.findCodeTableByInnerName("industry");
                hrsocialDTO.getHrClientDTO().get(0).setIndustrykey(industrymap.get(hrsocialDTO.getHrClientDTO().get(0).getIndustry()));
                hrsocialDTO.getHrClientDTO().get(0).setIndustry(hrClient.get(0).getIndustry());
                UserDTO user = new UserDTO();
                //获取客户的用户名以及获取专管员电话
                if (id != null) {
                    user = this.hrClientRepository.getusername(id);
                }

                if (user != null) {
                    hrsocialDTO.getHrClientDTO().get(0).setUserName(user.getUserName());
                }
                //获取专管员电话
                String specialized = this.hrClientRepository.getspecializedphone(hrsocialDTO.getHrClientDTO().get(0).getSpecializedId());
                if (specialized != null) {
                    hrsocialDTO.getHrClientDTO().get(0).setSpecializedphone(specialized);
                }

            }
        }
        //获取客户协议
        hrsocialDTO.setHrProtocolDTO(this.hrClientRepository.getprotoco(id));
        //获取数据字典结算方式
        if (hrsocialDTO.getHrProtocolDTO().size() != 0) {
            Map<Integer, String> businessmap = codeTableService.findCodeTableByInnerName("settlementMethod");
            hrsocialDTO.getHrProtocolDTO().get(0).setSettlementMethodkey(businessmap.get(hrsocialDTO.getHrProtocolDTO().get(0).getSettlementMethod()));
            hrsocialDTO.getHrProtocolDTO().get(0).setSettlementMethod(hrsocialDTO.getHrProtocolDTO().get(0).getSettlementMethod());

            if (hrsocialDTO.getHrProtocolDTO().size() != 0) {
                //获取协议的对接人
                String protocolId = hrsocialDTO.getHrProtocolDTO().get(0).getId();
                if (protocolId != null) {
                    hrsocialDTO.setHrDockingDTO(this.hrClientRepository.getHrDocking(protocolId));
                }
            }
        }
        return hrsocialDTO;
    }

    /**
     * 删除客户组织架构表
     *
     * @param id
     */
    @Override
    public void deleteHrClient(String id) {
        Optional.ofNullable(this.hrClientRepository.selectById(id))
            .ifPresent(hrClient -> {
                this.hrClientRepository.deleteById(id);
                log.info("Delete HrClient:{}", hrClient);
            });
    }


    /**
     * 批量删除客户组织架构表
     *
     * @param ids
     */
    @Override
    public void deleteHrClient(List<String> ids) {
        log.info("Delete HrClients:{}", ids);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrClient> hrClientList = this.hrClientRepository.selectBatchIds(ids);
        //删除客户
        for (String id : ids) {
            List<String> clentId = this.hrClientRepository.selectclientId(id);
            this.hrClientRepository.deleteById(id);
            for (String s : clentId) {
                this.hrClientRepository.deleteById(s);
            }
        }


        for (String id : ids) {
            //删除协议
            this.hrProtocolRepository.deleteClient(id);
            //删除用户

            String cleanUserid = this.hrClientRepository.selectByIdUser(id);
            this.hrClientRepository.deleteUser(cleanUserid);
            //删除员工
            this.hrClientRepository.deleteTalent(id);
            // 操作日志

        }

        List<String> collect = hrClientList.stream().map(HrClient::getClientName).collect(Collectors.toList());

        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.CLIENT.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除客户: " + JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(hrClientList),
            jwtUserDTO
        );

    }


    /**
     * 分页查询客户组织架构表
     *
     * @param hrClientDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrClientDTO hrClientDTO, Long pageNumber, Long pageSize) {
        Page<HrClient> page = new Page<>(pageNumber, pageSize);
        IPage<HrClientDTO> iPage = new Page<>();
        if (hrClientDTO.getContractStartDateQuery() != null) {
            for (int i = 0; i < hrClientDTO.getContractStartDateQuery().size(); i++) {
                hrClientDTO.setAgreementStartDate(hrClientDTO.getContractStartDateQuery().get(0));
                hrClientDTO.setAgreementStartDateend(hrClientDTO.getContractStartDateQuery().get(1));
            }
        }
        if (hrClientDTO.getContractEndDateQuery() != null) {
            for (int i = 0; i < hrClientDTO.getContractEndDateQuery().size(); i++) {
                hrClientDTO.setAgreementEndDate(hrClientDTO.getContractEndDateQuery().get(0));
                hrClientDTO.setAgreementEndDateend(hrClientDTO.getContractEndDateQuery().get(1));
            }
        }
        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String newdate = format.format(date);
        if (hrClientDTO.getIdList() == null && hrClientDTO.getFlag() == null) {
            List<String> clientId = this.selectClientIdByUserId();
            if (clientId != null) {
                hrClientDTO.setIdList(clientId);
            }
        }

        if (hrClientDTO.getFlag() != null && hrClientDTO.getFlag()) {
            hrClientDTO.setIdList(null);
        }

        if (hrClientDTO.getCustomerType() != null) {
            HrProtocolDTO hrProtocolDTO = new HrProtocolDTO();
            return this.getcustomerType(hrProtocolDTO, hrClientDTO, newdate, pageNumber, pageSize);

        } else {
            List<HrClient> structure = this.getStructure();
            if (hrClientDTO.getField() != null) {
                if (hrClientDTO.getField().equals("agreement_number")) {
                    hrClientDTO.setField("a.agreementNumber");
                }
                if (hrClientDTO.getField().equals("agreement_typekey")) {
                    hrClientDTO.setField("a.agreementType");
                }
            }
            if (CollectionUtils.isNotEmpty(hrClientDTO.getLevelList())) {
                if (CollectionUtils.isNotEmpty(structure)) {
                    List<String> ids = structure.stream().filter(ls -> hrClientDTO.getLevelList().contains(ls.getLevel())).map(HrClient::getId).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(ids)) {
                        return new Page();
                    } else {
                        hrClientDTO.setIds(ids);
                    }
                }
            }
            iPage = this.hrClientRepository.page(page, hrClientDTO);
            //获取数据字典企业性质
            Map<Integer, String> enterprisemap = codeTableService.findCodeTableByInnerName("enterprise");
            //获取数据字典业务类型
            Map<Integer, String> businessmap = codeTableService.findCodeTableByInnerName("businessType");
            //获取数据字典业务类型
            Map<Integer, String> agreementType = codeTableService.findCodeTableByInnerName("agreementType");
            for (HrClientDTO record : iPage.getRecords()) {
                if (!record.getParentId().equals("0")) {
                    List<String> clientIdList = this.findClientIdList(record.getId());
                    clientIdList.remove(record.getId());
                    List<HrClient> hrClientList = structure.stream().filter(lst -> clientIdList.contains(lst.getId())).collect(Collectors.toList());
                    record.setHrClientList(hrClientList);
                }
                hrClientDTO.setId(record.getId());
                HrProtocolDTO hrProtocolDTO = this.hrClientRepository.selectprotocol(hrClientDTO);
                //存储数据字典的key,value

                record.setEnterpriseNaturekey(enterprisemap.get(record.getEnterpriseNature()));
                record.setEnterpriseNature(record.getEnterpriseNature());
                record.setAgreementType(record.getAgreementType());
                record.setAgreementTypekey(agreementType.get(record.getAgreementType()));
                record.setBusinessType(record.getBusinessType());
                record.setBusinessTypekey(businessmap.get(record.getBusinessType()));
                //获取员工人数

                List<String> clientIdLista = new ArrayList<>();
                List<String> clientIdList = this.hrFeeReviewRepository.selectChlientId(record.getId());
                clientIdLista.addAll(clientIdList);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdList)) {
                    for (String s : clientIdList) {
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdList)) {
                            List<String> clientIdLists = this.hrFeeReviewRepository.selectChlientId(s);
                            clientIdLista.addAll(clientIdLists);
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdLists)) {
                                for (String idList : clientIdLists) {
                                    List<String> clientIdListss = this.hrFeeReviewRepository.selectChlientId(idList);
                                    clientIdLista.addAll(clientIdListss);
                                }
                            }

                        }
                    }
                }
                clientIdLista.add(record.getId());
                if (CollectionUtils.isNotEmpty(clientIdLista)) {
                    int psum = this.hrClientRepository.selectclientsums(clientIdLista);
                    List<String> listNew = new ArrayList<String>(new TreeSet<String>(clientIdLista));
                    record.setClientIdList(listNew);
                    record.setPeoplesum(psum);
                }

                if (hrProtocolDTO != null) {
                    record.setProtocolId(hrProtocolDTO.getId());
                    if (hrProtocolDTO.getAgreementStartDate() != null) {
                        String endTime = String.valueOf(hrProtocolDTO.getAgreementEndDate());
                        String startTime = String.valueOf(hrProtocolDTO.getAgreementStartDate());
                        record.setAgreementStartDate(hrProtocolDTO.getAgreementStartDate());
                        if (hrProtocolDTO.getStates() == 3) {
                            record.setAgreementEndDate(hrProtocolDTO.getAgreementTerminationDate());
                        } else {
                            record.setAgreementEndDate(hrProtocolDTO.getAgreementEndDate());
                        }
                        if (endTime.compareTo(newdate) < 0) {
                            //过期客户
                            record.setCustomerType("0");
                        }
                        if (endTime.compareTo(newdate) > 0 && hrProtocolDTO.getStates() == 3) {
                            //异常客户
                            record.setCustomerType("1");
                            record.setAgreementStartDate(null);
                            record.setAgreementEndDate(null);
                        }
                        if (endTime != null && startTime != null && endTime.compareTo(newdate) >= 0 && startTime.compareTo(newdate) <= 0 && hrProtocolDTO.getStates() < 2) {
                            //正常客户
                            record.setCustomerType("2");
                        }
                        if (startTime.compareTo(newdate) > 0) {
                            //待期客户
                            record.setCustomerType("4");

                        }
                        List<HrProtocolDTO> poId = this.hrClientRepository.selectprotocolId(hrClientDTO.getId());
                        List<HrProtocolDTO> HrProtocols = poId.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getAgreementNumber()))), ArrayList::new));

                        if (HrProtocols.size() > 1) {
                            //多协议客户
                            record.setCustomerType("5");

                        }
                    }
                } else {
                    //潜在客户
                    record.setCustomerType("3");
                }
                //获取专管员名字
                String specialized = this.hrClientRepository.selectspecialized(record.getSpecializedId());
                record.setSpecialized(specialized);
                //获取用户名
                String username = this.hrClientRepository.getClientUsername(record.getId());
                record.setUserName(username);
            }
            return iPage;

        }


    }

    //根据客户类型进行筛选条件分页
    private IPage getcustomerType(HrProtocolDTO hrProtocolDTO, HrClientDTO hrClientDTO, String newdate, Long pageNumber, Long pageSize) {
        IPage iPage = new Page();
        if (hrClientDTO.getIdList() != null) {
            String customerType = hrClientDTO.getCustomerType();
            Page<HrClient> page = new Page<>(pageNumber, pageSize);
            QueryWrapper<HrClient> qws = new QueryWrapper<>(this.hrClientMapper.toEntity(hrClientDTO));
            QueryWrapper<HrProtocol> qw = new QueryWrapper<>(this.hrProtocolMapper.toEntity(hrProtocolDTO));
            String AgreementEndDateend = String.valueOf(hrClientDTO.getAgreementEndDateend());
            String AgreementEndDate = String.valueOf(hrClientDTO.getAgreementEndDate());
            String AgreementStartDate = String.valueOf(hrClientDTO.getAgreementStartDate());
            String AgreementStartDateend = String.valueOf(hrClientDTO.getAgreementStartDateend());
            //获取数据字典企业性质
            Map<Integer, String> enterprisemap = codeTableService.findCodeTableByInnerName("enterprise");
            //获取数据字典协议类型
            Map<Integer, String> agreementType = codeTableService.findCodeTableByInnerName("agreementType");
            ArrayList<String> arrayList = new ArrayList();
            if (customerType != null) {
                if (customerType.equals("0")) {
                    qw.lt("agreement_end_date", newdate);
                    qw.eq("states", "2");
                    qw.eq("use_status", 1);

                }
                if (customerType.equals("1")) {
                    qw.eq("use_status", 1);
                    qw.eq("states", "3");
                }
                if (customerType.equals("2")) {
                    qw.gt("agreement_end_date", newdate);
                    qw.le("agreement_start_date", newdate);
                    qw.lt("states", "2");
                    qw.eq("use_status", 1);
                }
                if (customerType.equals("3")) {
                    qw.select("client_id");

                }
                if (customerType.equals("4")) {
                    qw.gt("agreement_start_date", newdate);
                    qw.eq("use_status", 1);
                }
                if (customerType.equals("5")) {
                    List<String> protocolDTOS = this.hrClientRepository.selectprotocolDTOS();
                    qw.in(CollectionUtils.isNotEmpty(protocolDTOS), "id", protocolDTOS);
                    qw.eq("use_status", 1);

                }
                if (CollectionUtils.isNotEmpty(hrClientDTO.getAgreementTypeList())) {
                    qw.in("agreement_type", hrClientDTO.getAgreementTypeList());
                }
                if (!AgreementStartDate.equals("null") && AgreementStartDate != "") {
                    qw.ge("agreement_start_date", AgreementStartDate);
                }
                if (!AgreementStartDateend.equals("null") && AgreementStartDateend != "") {
                    qw.le("agreement_start_date", AgreementStartDateend);
                }
                if (!AgreementEndDate.equals("null") && AgreementEndDate != "") {
                    qw.ge("agreement_end_date", AgreementEndDate);
                }
                if (!AgreementEndDateend.equals("null") && AgreementEndDateend != "") {
                    qw.le("agreement_end_date", AgreementEndDateend);
                }
                if (hrClientDTO.getField() != null) {
                    if (hrClientDTO.getField().equals("agreement_typekey")) {
                        hrClientDTO.setField("agreement_type");
                    }
                    if (hrClientDTO.getField().equals("agreement_start_date")) {
                        if (hrClientDTO.getField().equals("agreement_end_date")) {
                            //排序
                            if (cn.casair.common.utils.StringUtils.isNotBlank(hrClientDTO.getOrder())) {
                                if (hrClientDTO.getOrder().equals("DESC")) {
                                    qw.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrClientDTO.getField()), false, hrClientDTO.getField());
                                } else {
                                    qw.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrClientDTO.getField()), true, hrClientDTO.getField());
                                }
                            }
                        }
                    }
                }
                qw.select("any_value(id) id",
                    "any_value ( agreement_number ) agreement_number",
                    "any_value ( agreement_title ) agreement_title",
                    "any_value ( agreement_end_date ) agreement_end_date",
                    " any_value ( settlement_method ) settlement_method",
                    " any_value ( agreement_owned_customer ) agreement_owned_customer",
                    " any_value ( service_charge_type ) service_charge_type",
                    " any_value ( agreement_start_date ) agreement_start_date",
                    " any_value ( states ) states",
                    " any_value ( last_modified_date ) last_modified_date",
                    "any_value ( client_id ) client_id",
                    "any_value ( reason_termination_agreement ) reason_termination_agreement",
                    "any_value ( appendix_id ) appendix_id",
                    "any_value ( remark ) remark",
                    "any_value ( created_date ) created_date",
                    "any_value ( agreement_type ) agreementType",
                    "any_value ( agreement_termination_date ) agreement_termination_date");
                if (!customerType.equals("3")) {
                    qw.in(CollectionUtils.isNotEmpty(hrClientDTO.getIdList()), "client_id", hrClientDTO.getIdList());
                }
                qw.orderByDesc("agreement_start_date", "1");
                qw.groupBy("agreement_number");
                List<HrProtocol> hrProtocols = this.hrProtocolRepository.selectList(qw);
                LinkedList<HrProtocol> linkList = new LinkedList<>();
                linkList.addAll(hrProtocols);
                if (linkList.size() != 0) {
                    for (HrProtocol hrProtocol : linkList) {
                        arrayList.add(hrProtocol.getClientId());
                    }
                    if (customerType.equals("3")) {
                        List<HrProtocol> hrProtocolLists = this.hrProtocolRepository.selectList(null);
                        List<String> pid = hrProtocolLists.stream().map(HrProtocol::getClientId).distinct().collect(Collectors.toList());
                        qws.notIn("hc.id", pid);
                        qws.eq("hc.is_delete", 0);
                        if (CollectionUtils.isNotEmpty(hrClientDTO.getIdList())) {
                            qws.in("hc.id", hrClientDTO.getIdList());
                        }
                    } else {
                        if (!customerType.equals("5")) {
                            List<String> hrClientDTOList = new ArrayList<>();
                            for (String s : arrayList) {
                                if (StringUtils.isNotEmpty(s)) {
                                    QueryWrapper<HrProtocol> ww = new QueryWrapper<>();
                                    ww.eq("client_id", s);
                                    List<HrProtocol> hrProtocolLists = this.hrProtocolRepository.selectList(ww);
                                    if (CollectionUtils.isNotEmpty(hrProtocolLists)) {
                                        ArrayList<HrProtocol> hrBillDetailDTOList = hrProtocolLists.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getAgreementNumber()))), ArrayList::new));
                                        if (hrBillDetailDTOList.size() == 1) {
                                            hrClientDTOList.add(s);
                                        }
                                    }
                                }

                            }
                            qws.in(CollectionUtils.isNotEmpty(hrClientDTOList),"hc.id", hrClientDTOList);
                        } else {
                            List<String> hrClientDTOList = new ArrayList<>();
                            for (String s : arrayList) {
                                if (StringUtils.isNotEmpty(s)) {
                                    QueryWrapper<HrProtocol> ww = new QueryWrapper<>();
                                    ww.eq("client_id", s);
                                    ww.eq("type", 1);
                                    List<HrProtocol> hrProtocolLists = this.hrProtocolRepository.selectList(ww);
                                    if (CollectionUtils.isNotEmpty(hrProtocolLists)) {
                                        ArrayList<HrProtocol> hrBillDetailDTOList = hrProtocolLists.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getAgreementNumber()))), ArrayList::new));
                                        if (hrBillDetailDTOList.size() > 1) {
                                            hrClientDTOList.add(s);
                                        }
                                    }
                                }

                            }
                            qws.in(CollectionUtils.isNotEmpty(hrClientDTOList),"hc.id", hrClientDTOList);
                        }
                    }
                    if (hrClientDTO.getUnitNumber() != null && hrClientDTO.getUnitNumber() != "") {
                        qws.like("hc.unit_number", hrClientDTO.getUnitNumber());
                    }
                    if (hrClientDTO.getClientName() != null && hrClientDTO.getClientName() != "") {
                        qws.eq("hc.client_name", hrClientDTO.getClientName());
                    }
                    if (hrClientDTO.getBusinessType() != null) {
                        qws.eq("hc.business_type", hrClientDTO.getBusinessType());
                    }
                    if (hrClientDTO.getEnterpriseNature() != null) {
                        qws.eq("hc.enterprise_nature", hrClientDTO.getEnterpriseNature());
                    }
                    if (hrClientDTO.getSpecializedId() != null && hrClientDTO.getSpecializedId() != "") {
                        qws.eq("hc.specialized_id", hrClientDTO.getSpecializedId());
                    }
                    if (hrClientDTO.getProvidentFundAccountId() != null && hrClientDTO.getProvidentFundAccountId() != "") {
                        qws.eq("hc.provident_fund_account_id", hrClientDTO.getProvidentFundAccountId());
                    }
                    if (hrClientDTO.getSocialSecurityTypeId() != null && hrClientDTO.getSocialSecurityTypeId() != "") {
                        qws.eq("hc.social_security_type_id", hrClientDTO.getSocialSecurityTypeId());
                    }
                    if (hrClientDTO.getProvidentFundTypeId() != null && hrClientDTO.getProvidentFundTypeId() != "") {
                        qws.eq("hc.provident_fund_type_id", hrClientDTO.getProvidentFundTypeId());
                    }


                    if (CollectionUtils.isNotEmpty(hrClientDTO.getSpecializedIdList())) {
                        qws.in("hc.specialized_id", hrClientDTO.getSpecializedIdList());
                    }
                    if (CollectionUtils.isNotEmpty(hrClientDTO.getClientIdList())) {
                        qws.in("hc.id", hrClientDTO.getClientIdList());
                    }
                    qws.in(CollectionUtils.isNotEmpty(hrClientDTO.getClientIds()), "hc.id", hrClientDTO.getClientIds());

                } else {
                    return null;
                }

            }
            List<HrClient> structure = this.getStructure();
            if (CollectionUtils.isNotEmpty(hrClientDTO.getLevelList())) {
                if (CollectionUtils.isNotEmpty(structure)) {
                    List<String> ids = structure.stream().filter(ls -> hrClientDTO.getLevelList().contains(ls.getLevel())).map(HrClient::getId).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(ids)) {
                        return new Page();
                    } else {
                        qws.in("hc.id", ids);
                    }
                }
            }
            if (hrClientDTO.getField() != null) {
                if (!hrClientDTO.getField().equals("agreement_start_date")) {
                    if (!hrClientDTO.getField().equals("agreement_end_date")) {
                        if (hrClientDTO.getField().equals("agreement_typekey")) {
                            hrClientDTO.setField(null);
                            hrClientDTO.setOrder(null);
                        }
                        if (hrClientDTO.getField().equals("agreement_type")) {
                            hrClientDTO.setField(null);
                            hrClientDTO.setOrder(null);
                        }

                        if (hrClientDTO.getField() != null) {
                            if (hrClientDTO.getField().equals("agreement_number")) {
                                hrClientDTO.setField(null);
                                hrClientDTO.setOrder(null);
                            }
                        }

                        //排序
                        if (cn.casair.common.utils.StringUtils.isNotBlank(hrClientDTO.getOrder())) {
                            if (hrClientDTO.getField().equals("user_name")) {
                                hrClientDTO.setField("su.user_name");
                            }
                            if (hrClientDTO.getOrder().equals("DESC")) {
                                qws.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrClientDTO.getField()), false, hrClientDTO.getField());
                            } else {
                                qws.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrClientDTO.getField()), true, hrClientDTO.getField());
                            }

                        }
                    }
                }
            }


            iPage = this.hrClientRepository.selectPages(page, qws);
            List<HrClientDTO> hrClientDTOS = iPage.getRecords();

            for (HrClientDTO record : hrClientDTOS) {
                if (!record.getParentId().equals("0")) {
                    List<String> clientIdList = this.findClientIdList(record.getId());
                    clientIdList.remove(record.getId());
                    List<HrClient> hrClientList = structure.stream().filter(ls -> clientIdList.contains(ls.getId())).collect(Collectors.toList());
                    record.setHrClientList(hrClientList);
                }
                if (customerType != null) {
                    List<String> cuost = new ArrayList<>();
                    //过期客户
                    if (customerType.equals("0")) {

                        record.setCustomerType("0");
                        cuost.add("2");

                    }
                    //异常客户
                    if (customerType.equals("1")) {
                        record.setCustomerType("1");
                        cuost.add("3");
                    }
                    //正常客户
                    if (customerType.equals("2")) {
                        record.setCustomerType("2");
                        cuost.add("1");
                        cuost.add("0");
                    }
                    //待期客户
                    if (customerType.equals("4")) {
                        record.setCustomerType("4");
                        cuost.add("4");
                    }
                    //多协议客户
                    if (customerType.equals("5")) {
                        record.setCustomerType("5");
                    }
                    //潜在客户
                    if (customerType.equals("3")) {
                        record.setCustomerType("3");
                    }
                    record.setCustomerTypeList(cuost);
                }

                if (!record.getCustomerType().equals("3")) {


                    HrProtocolDTO hrProtocolDTOs = this.hrClientRepository.selectprotocol(record);

                    if (record != null) {
                        if (hrProtocolDTOs != null) {
                            if (hrProtocolDTOs.getAgreementEndDate() != null) {
                                if (record.getCustomerType().equals("5")) {
                                    record.setAgreementEndDate(hrProtocolDTOs.getAgreementEndDate());
                                    record.setAgreementStartDate(hrProtocolDTOs.getAgreementStartDate());
                                } else {
                                    record.setAgreementStartDate(hrProtocolDTOs.getAgreementStartDate());
                                    if (hrProtocolDTOs.getStates().equals("3")) {
                                        record.setAgreementEndDate(hrProtocolDTOs.getAgreementTerminationDate());
                                    } else {
                                        record.setAgreementEndDate(hrProtocolDTOs.getAgreementEndDate());
                                    }
                                }
                                if (record.getCustomerType().equals("0")) {
                                    record.setAgreementStartDate(hrProtocolDTOs.getAgreementStartDate());
                                    record.setAgreementEndDate(hrProtocolDTOs.getAgreementEndDate());
                                }
                                if (record.getCustomerType().equals("4")) {
                                    record.setAgreementStartDate(hrProtocolDTOs.getAgreementStartDate());
                                    record.setAgreementEndDate(hrProtocolDTOs.getAgreementEndDate());
                                }
                                if (record.getCustomerType().equals("1")) {

                                    record.setAgreementEndDate(hrProtocolDTOs.getAgreementTerminationDate());
                                }
                            }
                        }
                   /* //获取员工人数

                    List<String> clientIdLista = new ArrayList<>();
                    List<String> clientIdList = this.hrFeeReviewRepository.selectChlientId(record.getId());
                    clientIdLista.addAll(clientIdList);
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdList)) {
                        for (String s : clientIdList) {
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdList)) {
                                List<String> clientIdLists = this.hrFeeReviewRepository.selectChlientId(s);
                                clientIdLista.addAll(clientIdLists);
                                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdLists)) {
                                    for (String idList : clientIdLists) {
                                        List<String> clientIdListss = this.hrFeeReviewRepository.selectChlientId(idList);
                                        clientIdLista.addAll(clientIdListss);
                                    }
                                }

                            }
                        }
                    }
                    clientIdLista.add(record.getId());
                    if (CollectionUtils.isNotEmpty(clientIdLista)) {
                        int psum = this.hrClientRepository.selectclientsums(clientIdLista);
                        List<String> listNew = new ArrayList<String>(new TreeSet<String>(clientIdLista));
                        record.setClientIdList(listNew);
                        record.setPeoplesum(psum);
                    }*/
                        if (hrProtocolDTOs != null) {
                            if (org.apache.commons.lang3.StringUtils.isNotBlank(hrProtocolDTOs.getAgreementNumber())) {
                                record.setAgreementNumber(hrProtocolDTOs.getAgreementNumber());
                            }
                        }
                        if (hrProtocolDTOs != null) {
                            if (hrProtocolDTOs.getAgreementType() != null) {
                                record.setAgreementType(hrProtocolDTOs.getAgreementType());
                                record.setAgreementTypekey(agreementType.get(hrProtocolDTOs.getAgreementType()));
                            }
                        }

                  /*  //获取专管员名字
                    String specialized = this.hrClientRepository.selectspecialized(record.getSpecializedId());
                    record.setSpecialized(specialized);
                    //获取用户名
                    String username = this.hrClientRepository.getClientUsername(record.getId());
                    record.setUserName(username);
                    //存储数据字典的key,value
                    record.setEnterpriseNaturekey(enterprisemap.get(record.getEnterpriseNature()));
                    record.setEnterpriseNature(record.getEnterpriseNature());*/
                    }
                }

                //获取员工人数

                List<String> clientIdLista = new ArrayList<>();
                List<String> clientIdList = this.hrFeeReviewRepository.selectChlientId(record.getId());
                clientIdLista.addAll(clientIdList);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdList)) {
                    for (String s : clientIdList) {
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdList)) {
                            List<String> clientIdLists = this.hrFeeReviewRepository.selectChlientId(s);
                            clientIdLista.addAll(clientIdLists);
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdLists)) {
                                for (String idList : clientIdLists) {
                                    List<String> clientIdListss = this.hrFeeReviewRepository.selectChlientId(idList);
                                    clientIdLista.addAll(clientIdListss);
                                }
                            }

                        }
                    }
                }
                clientIdLista.add(record.getId());
                if (CollectionUtils.isNotEmpty(clientIdLista)) {
                    int psum = this.hrClientRepository.selectclientsums(clientIdLista);
                    List<String> listNew = new ArrayList<String>(new TreeSet<String>(clientIdLista));
                    record.setClientIdList(listNew);
                    record.setPeoplesum(psum);
                }
                //获取专管员名字
                String specialized = this.hrClientRepository.selectspecialized(record.getSpecializedId());
                record.setSpecialized(specialized);
                //获取用户名
                String username = this.hrClientRepository.getClientUsername(record.getId());
                record.setUserName(username);
                //存储数据字典的key,value

                record.setEnterpriseNaturekey(enterprisemap.get(record.getEnterpriseNature()));
                record.setEnterpriseNature(record.getEnterpriseNature());

            }
            iPage.setRecords(hrClientDTOS);

            return iPage;
        } else {
            return iPage;
        }
    }

    /**
     * 返回层级结构树
     */
    public List<HrClient> getStructure() {
        //查询所有数据
        List<HrClient> allList = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("is_delete", 0));
        //查询顶级数据
        List<HrClient> topList = allList.stream().filter(ls -> ls.getParentId().equals("0")).collect(Collectors.toList());
        //为一级数据设置子数据，getChild是递归调用的
        if (!CollectionUtils.isEmpty(topList)) { //不为空进入
            for (HrClient entity : topList) {
                //需在实体创建存放结构树字段进行set
                entity.setLevel(1);
                getChild(entity.getId(), allList, 1);
            }
        }
        return allList;
    }

    /**
     * 调用递归
     */
    private List<HrClient> getChild(String id, List<HrClient> allList, Integer level) {
        // 子级数据
        ++level;//level计算当前处于第几级
        List<HrClient> childList = Lists.newArrayList();
        for (HrClient entity : allList) {
            // 遍历所有节点，将父菜单id与传过来的id比较
            if (org.apache.commons.lang3.StringUtils.isNotBlank(entity.getParentId())) {
                if (entity.getParentId().equals(id)) {
                    entity.setLevel(level);
                    childList.add(entity);
                }
            }
        }
        // 把子级数据的子级再循环一遍
        for (HrClient entity : childList) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(entity.getParentId())) {
                // 递归
                entity.setLevel(level);
                getChild(entity.getId(), allList, level);
            }
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }

    /**
     * 查询所有的上级客户
     *
     * @param hrClientDTO
     * @return
     */
    @Override
    public HrClientDTO getParentClient(HrClientDTO hrClientDTO, List<HrClient> allList) {
        List<String> clientIds = queryParentIds(hrClientDTO.getId(), allList);
        List<HrClient> hrClientList = allList.stream().filter(ls -> clientIds.contains(ls.getId())).collect(Collectors.toList());
        hrClientDTO.setHrClientList(hrClientList);
        return hrClientDTO;
    }

    /**
     * 递归获取父级ids
     *
     * @param id
     * @param taxCompanyList
     * @return
     */
    public List<String> queryParentIds(String id, List<HrClient> taxCompanyList) {
        //递归获取父级ids
        List<String> parentIds = new ArrayList<>();
        this.getParentTaxCompanyIds(taxCompanyList, id, parentIds);
        return parentIds;
    }

    /**
     * 递归获取父级ids
     *
     * @param clientList
     * @param id
     */
    private void getParentTaxCompanyIds(List<HrClient> clientList, String id, List<String> ids) {
        for (HrClient client : clientList) {
            if (StringUtils.isEmpty(client.getParentId())) {
                continue;
            }
            //判断是否有父节点
            if (id.equals(client.getId())) {
                ids.add(client.getId());
                getParentTaxCompanyIds(clientList, client.getParentId(), ids);
            }
        }
    }

    /**
     * GET /hr-clients/:id
     * <p>
     * 查询客户组织架构表
     *
     * @param
     * @param clientName
     * @return
     */
    @Override
    public List<HrClientDTO> getselectHrClient(String clientName) {
        List<String> ids = this.selectClientIdByUserId();
        List<HrClientDTO> list = new ArrayList<>();
        if (ids != null) {
            List<HrClientDTO> lista = this.hrClientRepository.getselectHrClient(ids, clientName);
            list.addAll(lista);
        }

        return list;
    }

    /**
     * 查询客户组织架构--无数据权限
     *
     * @return
     */
    @Override
    public List<HrClientDTO> notDataPermissionHrClient() {
        return this.hrClientRepository.getselectHrClient(new ArrayList<>(), null);
    }

    /**
     * GET /hr-social-securities/:id
     * <p>
     * 创建客户的时候查询社保类型
     *
     * @param
     * @return
     */
    @Override
    public List<HrSocialSecurityDTO> getHrSocialSelectSecurity() {
        List<HrSocialSecurityDTO> list = this.hrClientRepository.getHrSocialSelectSecurity();
        return list;
    }

    /**
     * GET /hr-social-securities/:id
     * <p>
     * 创建客户的时候查询公积金类型
     *
     * @param
     * @return
     */
    @Override
    public List<HrAccumulationFundDTO> getHrAccumulationSelectSecurity() {
        List<HrAccumulationFundDTO> list = this.hrClientRepository.getgetHrAccumulationSelectSecurity();
        return list;
    }


    /**
     * GET /hr-social-securities/:id
     * <p>
     * 创建客户的时候查询工资发放账户
     *
     * @param
     * @return
     */
    @Override
    public List<HrPlatformAccountDTO> getHrPlatformAccount() {
        List<HrPlatformAccountDTO> list = this.hrClientRepository.getHrPlatformAccount();
        return list;
    }

    //客户查询专管员
    @Override
    public List<UserDTO> getclientsspecialized() {
        List<UserDTO> list = this.hrClientRepository.getclientsspecialized();
        return list;
    }

    // 查询客户员工详情
    @Override
    public List<HrTalentStaffDTO> getTalentStaff(String id) {
        List<HrTalentStaffDTO> list = this.hrClientRepository.getTalentStaff(id);
        return list;
    }

    @Override
    public List<HrProtocolDTO> getselectProtocol() {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String userid = jwtUserDTO.getId();
        String clieanId = this.hrClientRepository.getselectclieanId(userid);
        List<HrProtocolDTO> list = this.hrClientRepository.getselectProtocol(clieanId);
        return list;
    }

    /**
     * 所属客户
     *
     * @param id
     * @return
     */
    @Override
    public List<HrClient> getOwnedCustomerList(String id) {
        /**
         * 判断clientID是否为空
         * a.为空：展示自身
         * b.不为空：根据clientId查询对应的子客户
         */
        List<HrClient> clientList = new ArrayList<>();
        if (StringUtils.isEmpty(id)) {
            JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
            //超级管理员看所有客户
            if (jwtUserDTO != null) {
                if ("super_admin".equals(jwtUserDTO.getCurrentRoleKey()) || "developer".equals(jwtUserDTO.getCurrentRoleKey())) {
                    //查询所有客户
                    List<HrClient> hrClientList = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("parent_id", 0).eq("status", 0));
                    this.findNextLevel(hrClientList);
                    return hrClientList;
                } else {
                    HrClient hrClient = hrClientRepository.selectOne(new QueryWrapper<HrClient>()
                        .eq("user_id", jwtUserDTO.getId())
                        .eq("is_delete", 0)
                        .eq("status", 0));
                    clientList.add(hrClient);
                    this.findNextLevel(clientList);

                }
            }
        } else {
            List<HrClient> hrClientList = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("parent_id", id).eq("status", 0));
            if (CollectionUtils.isNotEmpty(hrClientList)) {
                this.findNextLevel(hrClientList);
                clientList.addAll(hrClientList);
            }
        }
        return clientList;
    }

    /**
     * 查询是否有下一级客户
     *
     * @param hrClientList
     */
    private void findNextLevel(List<HrClient> hrClientList) {
        for (HrClient hrClient : hrClientList) {
            Integer count = hrClientRepository.selectCount(new QueryWrapper<HrClient>().eq("parent_id", hrClient.getId()).eq("status", 0));
            if (count > 0) {
                hrClient.setIsLeaf(true);
            } else {
                hrClient.setIsLeaf(false);
            }
        }
    }

    /**
     * 客户信息导出
     *
     * @param
     * @param
     * @return
     */
    @Override
    public String exportHrStaff(HrClientDTO clientDTO, HttpServletResponse response) {
        //获取数据字典企业性质
        Map<Integer, String> enterpriseMap = codeTableService.findCodeTableByInnerName("enterprise");
        //获取数据字典行业类别
        Map<Integer, String> industryMap = codeTableService.findCodeTableByInnerName("industry");

        //获取当前时间
        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String newdate = format.format(date);
        //查询要导出的数据
        List<HrClientDTO> hrClientDTOList = this.findPage(clientDTO, (long) 1, (long) 999999).getRecords();
        if (hrClientDTOList.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        List<HrClientExport> getlist = new ArrayList<>();
        // 表头
        List<ExcelExportEntity> excelHeader = new ArrayList<>();
        // 数据
        List<Map<String, Object>> paramsList = new ArrayList<>();
        List<HrClient> structure = this.getStructure();
        for (HrClientDTO hrClientDTO : hrClientDTOList) {
            if (!hrClientDTO.getParentId().equals("0")) {
                List<String> clientIdList = this.findClientIdList(hrClientDTO.getId());
                clientIdList.remove(hrClientDTO.getId());
                List<HrClient> hrClientList = structure.stream().filter(lst -> clientIdList.contains(lst.getId())).collect(Collectors.toList());
                hrClientDTO.setHrClientList(hrClientList);
            }
            HrClientExport hrClientExport = new HrClientExport();
            HrProtocolDTO hrProtocolDTO = new HrProtocolDTO();
            if (hrClientDTO != null) {
                if (!hrClientDTO.getParentId().equals("0")) {
                    HrClientDTO hrClientDTOs = this.hrClientRepository.getIdNumber(hrClientDTO.getParentId());
                    if (hrClientDTOs != null) {
                        hrClientDTO.setSuperiorUnitNumber(hrClientDTOs.getSuperiorUnitNumber());
                        hrClientDTO.setSuperiorUnit(hrClientDTOs.getSuperiorUnit());
                    }
                }
            }

            hrProtocolDTO = this.hrClientRepository.getprotocoendtime(hrClientDTO.getClientId());
            if (hrProtocolDTO != null) {
                if (hrProtocolDTO.getAgreementEndDate() != null && hrProtocolDTO.getAgreementStartDate() != null) {
                    if (hrClientDTO != null) {
                        hrClientDTO.setAgreementStartDate(hrProtocolDTO.getAgreementStartDate());
                        hrClientDTO.setAgreementEndDate(hrProtocolDTO.getAgreementEndDate());
                    }

                }
            }


            //逻辑获取客户类型
            if (hrProtocolDTO != null) {
                if (hrProtocolDTO.getAgreementEndDate() != null) {
                    String endTime = String.valueOf(hrProtocolDTO.getAgreementEndDate());
                    String startTime = String.valueOf(hrProtocolDTO.getAgreementStartDate());
                    if (endTime.compareTo(newdate) < 0) {
                        //过期客户
                        hrClientDTO.setCustomerType("过期客户");
                    }
                    if (endTime.compareTo(newdate) > 0 && hrProtocolDTO.getStates().equals(3)) {
                        //异常客户
                        hrClientDTO.setCustomerType("异常客户");
                    }
                    if (endTime != null && startTime != null && endTime.compareTo(newdate) >= 0 && startTime.compareTo(newdate) <= 0) {
                        //正常客户
                        hrClientDTO.setCustomerType("正常客户");
                    }
                    if (startTime.compareTo(newdate) > 0) {
                        //待期客户
                        hrClientDTO.setCustomerType("3");
                    }

                }
            } else {
                //潜在客户
                hrClientDTO.setCustomerType("潜在客户");
            }

            //存储数据字典的key,value
            //企业性质
            hrClientDTO.setEnterpriseNaturekey(enterpriseMap.get(hrClientDTO.getEnterpriseNature()));
            //行业类别
            hrClientDTO.setIndustrykey(industryMap.get(hrClientDTO.getIndustry()));
            //获取员工人数
            int psum = this.hrClientRepository.selectclientsum(hrClientDTO.getClientId());
            hrClientDTO.setPeoplesum(psum);
            BeanUtils.copyProperties(hrClientDTO, hrClientExport);
            getlist.add(hrClientExport);
            this.exportHeader(hrClientDTO, excelHeader, paramsList);
        }
        List<ExcelExportEntity> collect = excelHeader.stream().distinct().collect(Collectors.toList());
        String filePath = ExcelUtils.dynamicColumnExportLocal("客户列表", null, collect, paramsList, fileTempPath);
        String fileUrl = this.hrAppendixService.uploadLocalFile(filePath);

//        int listSize = getlist.size();
//        List<String> ids = hrClientDTOList.stream().map(HrClientDTO::getId).collect(Collectors.toList());
//        String fileUrl = this.hrAppendixService.uploadExportFile(getlist, "客户列表", HrClientExport.class);
//        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.CLIENT.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
//
        return fileUrl;
    }

    private void exportHeader(HrClientDTO hrClientDTO, List<ExcelExportEntity> excelHeader, List<Map<String, Object>> paramsList) {
        Map<String, Object> param = new HashMap<>();
        excelHeader.add(new ExcelExportEntity("上级单位编号", "superiorUnitNumber", 20));
        param.put("superiorUnitNumber", hrClientDTO.getSuperiorUnitNumber());
        excelHeader.add(new ExcelExportEntity("上级单位", "superiorUnit", 20));
        param.put("superiorUnit", hrClientDTO.getSuperiorUnit());
        excelHeader.add(new ExcelExportEntity("单位编号", "unitNumber", 20));
        param.put("unitNumber", hrClientDTO.getUnitNumber());
        excelHeader.add(new ExcelExportEntity("用户名", "userName", 20));
        param.put("userName", hrClientDTO.getUserName());
        List<HrClient> hrClientList = hrClientDTO.getHrClientList();
        if (CollectionUtils.isNotEmpty(hrClientList)) {
            Integer integer = hrClientList.stream().map(HrClient::getLevel).max(Integer::compareTo).get();
            for (int i = 1; i <= integer; i++) {
                int finalI = i;
                HrClient hrClient = hrClientList.stream().filter(ls -> ls.getLevel() == finalI).findFirst().orElse(null);
                if (hrClient != null){
                    String digit = Convert.numberToChinese(i, false);
                    excelHeader.add(new ExcelExportEntity(digit + "级客户", digit, 20));
                    param.put(digit, hrClient.getClientName());
                }
            }
        }
        excelHeader.add(new ExcelExportEntity("客户名称", "clientName", 20));
        param.put("clientName", hrClientDTO.getClientName());
        excelHeader.add(new ExcelExportEntity("客户类型", "customerType", 20));
        param.put("customerType", hrClientDTO.getCustomerType());
        excelHeader.add(new ExcelExportEntity("企业性质", "enterpriseNatureKey", 20));
        param.put("enterpriseNatureKey", hrClientDTO.getEnterpriseNaturekey());
        excelHeader.add(new ExcelExportEntity("注册资本", "registeredCapital", 20));
        param.put("registeredCapital", hrClientDTO.getRegisteredCapital());
        excelHeader.add(new ExcelExportEntity("成立时间", "establishedDate", 20));
        param.put("establishedDate", hrClientDTO.getEstablishedDate());
        excelHeader.add(new ExcelExportEntity("行业类别", "industryKey", 20));
        param.put("industryKey", hrClientDTO.getIndustrykey());
        excelHeader.add(new ExcelExportEntity("员工人数", "peopleSum", 20));
        param.put("peopleSum", hrClientDTO.getPeoplesum());
        excelHeader.add(new ExcelExportEntity("专管员", "specialized", 20));
        param.put("specialized", hrClientDTO.getSpecialized());
        excelHeader.add(new ExcelExportEntity("办公地址", "address", 20));
        param.put("address", hrClientDTO.getAddress());
        excelHeader.add(new ExcelExportEntity("工资发放日", "payDate", 20));
        param.put("payDate", hrClientDTO.getPayDate());
        excelHeader.add(new ExcelExportEntity("最新协议开始日期", "agreementStartDate", 20));
        param.put("agreementStartDate", hrClientDTO.getAgreementStartDate());
        excelHeader.add(new ExcelExportEntity("最新协议结束日期", "agreementEndDate", 20));
        param.put("agreementEndDate", hrClientDTO.getAgreementEndDate());
        excelHeader.add(new ExcelExportEntity("备注", "remarks", 20));
        param.put("remarks", hrClientDTO.getRemarks());
        paramsList.add(param);
    }


    /**
     * 客户信息导入
     *
     * @param file
     * @param
     * @return
     */
    @Override
    public String importHrStaff(MultipartFile file) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        // 设置进度条
        String redisKey = RedisKeyEnum.progressBar.CLIENT.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.clientComponent.saveClientData(inputStream, redisKey, jwtUserDTO);
        return redisKey;
     /*   ExcelImportResult<HrClientImport> result = ExcelUtils.importExcel(file, true, HrClientImport.class);
        //进度条
        String key = jwtUserDTO.getId() + RedisKeyEnum.progressBar.TALENT.getValue();
        redisCache.setCacheObject(key, 0, 10, TimeUnit.MINUTES);
        try {
            //导入客户信息以及校验
            this.saveStaffData(result, key);
        } catch (Exception e) {
            redisCache.deleteObject(key);
            throw new CommonException(e.getMessage());
        }
        ImportResultDTO resultDTO;
        try {
            resultDTO = ImportResultUtils.writeErrorFile("客户信息" + System.currentTimeMillis(), HrClientImport.class, result, fileTempPath);
            // 判断是否需要上传错误文件
            if (resultDTO.getFailureFileUrl() != null) {
                String fileUrl = this.hrAppendixService.uploadErrorImportFile(resultDTO.getFailureFileUrl());
                resultDTO.setFailureFileUrl(fileUrl);
            }
        } catch (IOException e) {
            log.error("人才信息检测导入异常:{}", e.getMessage());
            return ResponseUtil.buildError(e.getMessage());
        } finally {
            redisCache.deleteObject(key);
        }
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CLIENT.getValue(), BusinessTypeEnum.IMPORT.getKey(), file, JSON.toJSONString(resultDTO), ImportResultDTO.class);

        return ResponseUtil.buildSuccess(resultDTO);*/
    }

    /*   *//**
     * 客户信息导入效验
     *
     * @param
     * @param
     * @return
     *//*
    private void saveStaffData(ExcelImportResult<HrClientImport> result, String key) {
        long l = System.currentTimeMillis();
        int listSize = result.getList().size();
        Map<String, Integer> map = new HashMap<>(result.getList().size());
        int scale = 0;
        List<HrClientImport> hrClientImport = new ArrayList<>();
        for (HrClientImport hrClientImports : result.getList()) {
            try {

                //效验客户名是否为空
                if (org.apache.commons.lang3.StringUtils.isBlank(hrClientImports.getClientName())) {
                    throw new CommonException("客户名称不能为空");
                }
                log.info("效验客户名是否为空");
                //客户名称效验
                int getclientnames = this.hrClientRepository.getclientName(hrClientImports.getClientName());
                if (getclientnames > 0) {
                    throw new CommonException("客户名称重复，请重新进行填写!");
                }
                log.info("客户名称效验");
                //生成客户编号
                String unitSum = "";
                HrClient hrClient = new HrClient();
                if (org.apache.commons.lang3.StringUtils.isBlank(hrClientImports.getUnitNumber())) {
                    long timeNew = System.currentTimeMillis();
                    int radom = (int) (Math.random() * 10 + 1);
                    unitSum = "KH" + System.currentTimeMillis();
                }
                log.info("生成客户编号");
                //效验用户名是否重复
                if (org.apache.commons.lang3.StringUtils.isNotBlank(hrClientImports.getUserName())) {
                    QueryWrapper<User> ew = new QueryWrapper<>();
                    ew.eq("user_name", hrClientImports.getUserName());
                    List<User> userList = this.userRepository.selectList(ew);
                    if (CollectionUtils.isNotEmpty(userList)) {
                        throw new CommonException("用户名已重复，请修改");
                    }
                }
                log.info("效验用户名是否重复");
                //根据证件号码查询是否已经存在
                HrClient hrClients = this.hrClientRepository.selectOne(new QueryWrapper<HrClient>().eq("unit_number", hrClient.getUnitNumber()).eq("status", 0));
                if (hrClients != null) {
                    throw new CommonException("已存在!");
                }
                hrClientImport.add(hrClientImports);
                if (CollectionUtils.isNotEmpty(hrClientImport)) {
                    log.info("是否为空");
                    BeanUtils.copyProperties(hrClientImports, hrClient);

                    //效验客户层级是否存在上级客户编号
                    if (hrClientImports.getCustomerLevel() != null) {
                        log.info("效验客户层级是否存在上级客户编号");
                        if (hrClientImports.getCustomerLevel().equals("2")) {
                            String parid = hrClientImports.getParentcustomerLevel();
                            HrClientDTO hrClientDTO = new HrClientDTO();
                            if (parid != null) {
                                hrClientDTO = this.hrClientRepository.selectCountparid(parid);
                                if (hrClientDTO == null) {
                                    throw new CommonException("上级客户编号不存在,请重新填写!");
                                } else {
                                    hrClient.setParentId(hrClientDTO.getId());
                                }
                            }
                        }
                    }
                    if (hrClientImports.getEstablishedDate() != null) {
                        hrClient.setEstablishedDate(hrClientImports.getEstablishedDate());
                    }
                    log.info("效验客户成立时间");

                    //效验社保账号
                    if (hrClientImports.getSocialSecurityAccount() != null) {
                        HrPlatformAccountDTO hrPlatformAccountDTO = new HrPlatformAccountDTO();
                        hrPlatformAccountDTO = this.hrClientRepository.getSocialSecuritysum(hrClientImports.getSocialSecurityAccount());
                        if (hrPlatformAccountDTO == null) {
                            throw new CommonException("社保账号不存在,请重新填写!");
                        } else {
                            hrClient.setSocialSecurityAccountId(hrPlatformAccountDTO.getId());
                        }
                    }
                    log.info("效验社保账号");
                    //效验医保账号
                    if (hrClientImports.getMedicalInsuranceAccount() != null) {
                        HrPlatformAccountDTO hrPlatformAccountDTO = new HrPlatformAccountDTO();
                        hrPlatformAccountDTO = this.hrClientRepository.getMedicalInsurance(hrClientImports.getMedicalInsuranceAccount());
                        if (hrPlatformAccountDTO == null) {
                            throw new CommonException("医保账号不存在,请重新填写!");
                        } else {
                            hrClient.setMedicalInsuranceAccountId(hrPlatformAccountDTO.getId());
                        }
                    }
                    log.info("效验医保账号");
                    //效验公积金账号
                    if (hrClientImports.getProvidentFundAccount() != null) {
                        HrPlatformAccountDTO hrPlatformAccountDTO = new HrPlatformAccountDTO();
                        hrPlatformAccountDTO = this.hrClientRepository.getProvidentFund(hrClientImports.getProvidentFundAccount());
                        if (hrPlatformAccountDTO == null) {
                            throw new CommonException("公积金账号不存在,请重新填写!");
                        } else {
                            hrClient.setProvidentFundAccountId(hrPlatformAccountDTO.getId());
                        }
                    }
                    log.info("效验公积金账号");
                    //效验工资账号
                    if (hrClientImports.getPayrollAccount() != null) {
                        HrPlatformAccountDTO hrPlatformAccountDTO = new HrPlatformAccountDTO();
                        hrPlatformAccountDTO = this.hrClientRepository.getPayroll(hrClientImports.getPayrollAccount());
                        if (hrPlatformAccountDTO == null) {
                            throw new CommonException("工资账号不存在,请重新填写!");
                        } else {
                            hrClient.setPayrollAccountId(hrPlatformAccountDTO.getId());
                        }
                    }
                    log.info("效验工资账号");
                    //效验社保类型
                    if (hrClientImports.getSocialSecurityType() != null) {
                        HrSocialSecurityDTO hrSocialSecurityDTO = new HrSocialSecurityDTO();
                        hrSocialSecurityDTO = this.hrClientRepository.getSocialSecurity(hrClientImports.getSocialSecurityType());
                        if (hrSocialSecurityDTO == null) {
                            throw new CommonException("社保类型不存在,请重新填写!");
                        } else {
                            hrClient.setSocialSecurityTypeId(hrSocialSecurityDTO.getId());
                        }
                    }
                    log.info("效验社保类型");
                    //效验公积金类型
                    if (hrClientImports.getProvidentFundType() != null) {
                        HrAccumulationFundDTO hrAccumulationFundDTO = new HrAccumulationFundDTO();
                        hrAccumulationFundDTO = this.hrClientRepository.getProvidentFundType(hrClientImports.getProvidentFundType());
                        if (hrAccumulationFundDTO == null) {
                            throw new CommonException("公积金类型不存在,请重新填写!");
                        } else {
                            hrClient.setProvidentFundTypeId(hrAccumulationFundDTO.getId());
                        }
                    }
                    log.info("效验公积金类型");
                    //效验专管员
                    if (hrClientImports.getSpecialized() != null) {

                        String specializedId = this.hrClientRepository.getSpecialized(hrClientImports.getSpecialized());
                        if (specializedId == null || specializedId.equals("")) {
                            throw new CommonException("专管员不存在,请重新填写!");
                        } else {
                            hrClient.setSpecializedId(specializedId);
                        }
                    }
                    log.info("效验专管员");
                    //效验是否发放工资
                    if (hrClientImports.getClientPay() != null) {
                        hrClient.setClientPay(Boolean.valueOf(hrClientImports.getClientPay()));
                        if (hrClientImports.getClientPay().equals("1")) {
                            if (hrClientImports.getPayDate() != null) {
                                hrClient.setPayDate(hrClientImports.getPayDate());
                            } else {
                                throw new CommonException("是否发放工资日期为必填项，请重新填写");
                            }
                        }
                    } else {
                        hrClient.setClientPay(false);
                    }
                    log.info("效验是否发放工资");
                    //查询数据字典中的key
                    //企业性质
                    if (hrClientImports.getEnterpriseNature() != null) {
                        hrClient.setEnterpriseNature(hrClientImports.getEnterpriseNature());
                    }
                    log.info("企业性质");
                    //行业类别
                    if (hrClientImports.getIndustry() != null) {
                        hrClient.setIndustry(hrClientImports.getIndustry());
                    }
                    log.info("行业类别");
                    //业务类别
                    if (StringUtils.isNotEmpty(hrClientImports.getBusinessType()) && !hrClientImports.getBusinessType().equals("null")) {
                        hrClient.setBusinessType(Integer.valueOf(hrClientImports.getBusinessType()));
                    }
                    log.info("业务类别");

                    // 创建客户登录角色信息
                    if (hrClientImports.getUserName() != null) {
                        //客户用户名效验
                        log.info("客户用户名效验");
                        int getuser = this.hrClientRepository.getclientUser(hrClientImports.getUserName());
                        if (getuser > 0) {
                            throw new CommonException("用户名重复，请重新进行填写!");
                        }
                        log.info("客户user");
                        User user = new User();
                        user.setUserName(hrClientImports.getUserName());
                        user.setRealName(hrClientImports.getClientName());
                        user.setPassword(passwordEncoder.encode("c123456"));
                        log.info("insert user: {}", user);
                        this.userRepository.insert(user);
                        // 创建角色关联

                        Integer roleid = this.hrClientRepository.getroleId("client");
                        if (roleid != null) {
                            UserRole userRole = new UserRole();
                            userRole.setUserId(user.getId());
                            userRole.setRoleId(roleid);

                            this.hrClientRepository.insertuserrole(userRole);
                        }
                        //录入客户基本信息
                        if (hrClientImports.getClientPay() != null) {
                            if (hrClientImports.getClientPay().equals("1")) {
                                hrClient.setClientPay(true);
                            } else {
                                hrClient.setClientPay(false);
                            }
                        }

                        hrClient.setUserId(user.getId());
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(unitSum)) {
                            hrClient.setUnitNumber(unitSum);
                        }

                        this.hrClientRepository.insert(hrClient);
                        //创建试卷
                        QueryWrapper<HrPaperManagement> ew = new QueryWrapper<>();
                        ew.eq("is_preset", 1);
                        HrPaperManagement hrPaperManagement = this.hrPaperManagementRepository.selectOne(ew);
                        HrPaperClient hrPaperClient = new HrPaperClient();
                        hrPaperClient.setClientId(hrClient.getId());
                        hrPaperClient.setPaperId(hrPaperManagement.getId());
                        this.hrPaperClientRepository.insert(hrPaperClient);

                        //专管员进行关联
                        HrUserClient hrUserClient = new HrUserClient();
                        if (hrClient.getSpecializedId() != null) {
                            hrUserClient.setUserId(hrClient.getSpecializedId());
                            hrUserClient.setClientId(hrClient.getId());
                            hrUserClient.setIsSpecialized(true);
                            this.hrUserClientRepository.insert(hrUserClient);
                        }

                    }


                }

            } catch (Exception e) {

                log.error("保存客户信息异常:{}", e.getMessage());
                if (e instanceof MyBatisSystemException) {
                    hrClientImports.setErrorMsg("该信息查询出多条记录!");
                } else if (e.getMessage().contains("已存在")) {
                    hrClientImports.setDuplicate(true);
                    hrClientImports.setErrorMsg(e.getMessage());
                } else {
                    hrClientImports.setErrorMsg(e.getMessage());
                }
            } finally {
                scale++;
                int i = CalculateUtils.calculationProgress(scale, listSize);
                redisCache.setCacheObject(key, i, 10, TimeUnit.MINUTES);
            }
        }
        long timeMillis = System.currentTimeMillis();
        log.info("逻辑结束时间：{}", timeMillis);
        log.info("逻辑耗时：{} ms", timeMillis - l);

        //导入数据

    }
*/

    /**
     * 客户导入模板
     *
     * @param response
     */
    @Override
    public String importHrClientsTemplate(HttpServletResponse response) {
        /* ExportParams params = new ExportParams(null, "客户信息");
       // params.setStyle(ExcelStyleUtils.class);
        params.setType(ExcelType.XSSF);
        List<HrClientTemplate> list = new ArrayList<>();
        HrClientTemplate hrClientTemplate = new HrClientTemplate();
        HrClientTemplate hrClientTemplates = new HrClientTemplate();
        hrClientTemplate.setUnitNumber("***************");
        hrClientTemplate.setUserName("kh001");
        hrClientTemplate.setClientName("xxx公司");
        hrClientTemplate.setEnterpriseNature("国有企业");
        hrClientTemplate.setCustomerLevel("一级");
        hrClientTemplate.setParentcustomerLevel(null);
        hrClientTemplate.setRegisteredCapital("2000万");
        hrClientTemplate.setEstablishedDate(LocalDate.parse("2021-01-01"));
        hrClientTemplate.setIndustry("建筑业");
        hrClientTemplate.setAddress("xxx");
        hrClientTemplate.setSocialSecurityAccount("623xxx");
        hrClientTemplate.setMedicalInsuranceAccount("623xxx");
        hrClientTemplate.setProvidentFundAccount("623xxx");
        hrClientTemplate.setSocialSecurityType("xx类型");
        hrClientTemplate.setProvidentFundType("xx类型");
        hrClientTemplate.setPayrollAccount("623xxx");
        hrClientTemplate.setClientPay("是");
        hrClientTemplate.setPayDate(Integer.valueOf("12"));
        hrClientTemplate.setSpecialized("张三");
        hrClientTemplate.setRemarks(null);
        hrClientTemplates.setUnitNumber("***************");
        hrClientTemplates.setUserName("kh001");
        hrClientTemplates.setClientName("xxx公司");
        hrClientTemplates.setEnterpriseNature("国有企业");
        hrClientTemplates.setCustomerLevel("多级");
        hrClientTemplates.setParentcustomerLevel("kh1631013429892");
        hrClientTemplates.setRegisteredCapital("2000万");
        hrClientTemplates.setEstablishedDate(LocalDate.parse("2021-01-01"));
        hrClientTemplates.setIndustry("建筑业");
        hrClientTemplates.setBusinessType("劳务派遣");
        hrClientTemplates.setAddress("xxx");
        hrClientTemplates.setSocialSecurityAccount("623xxx");
        hrClientTemplates.setMedicalInsuranceAccount("623xxx");
        hrClientTemplates.setProvidentFundAccount("623xxx");
        hrClientTemplates.setSocialSecurityType("xx类型");
        hrClientTemplates.setProvidentFundType("xx类型");
        hrClientTemplates.setPayrollAccount("623xxx");
        hrClientTemplates.setClientPay("否");
        hrClientTemplates.setPayDate(Integer.valueOf("12"));
        hrClientTemplates.setSpecialized("张三");
        hrClientTemplates.setRemarks(null);
        list.add(hrClientTemplate);
        list.add(hrClientTemplates);
        ZipSecureFile.setMinInflateRatio(-1.0d);
        Workbook workbook = ExcelExportUtil.exportExcel(params, HrClientTemplate.class, list);
        ExcelUtils.downLoadExcel("客户信息导入模板.xlsx", response, workbook);*/
        return excelPrefix + "客户信息导入模板.xlsx";

    }

    //  /**
//     * GET /hr-clients/
//     * <p>
//     * 查询上级客户协议
//     * 传客户id
//     *
//     * @param
//     * @return
//     */
    @Override
    public List<HrProtocolDTO> selectProtocolId(String id) {
        List<HrProtocolDTO> list = new ArrayList<>();
        if (!id.equals("") && id != null) {
            List<HrClient> clientIdList = this.hrProtocolRepository.selectAllParentClientById(id);
            List<String> clientIdLista = new ArrayList<>();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdList)) {
                for (HrClient client : clientIdList) {
                    clientIdLista.add(client.getId());
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdList)) {
                        List<HrClient> clientIdLists = this.hrProtocolRepository.selectAllParentClientById(client.getParentId());
                        for (HrClient idList : clientIdLists) {
                            clientIdLista.add(idList.getId());
                        }
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdLists)) {
                            for (HrClient idLisst : clientIdLists) {
                                List<HrClient> clientIdListss = this.hrProtocolRepository.selectAllParentClientById(idLisst.getParentId());
                                for (HrClient idListss : clientIdListss) {
                                    clientIdLista.add(idListss.getId());

                                }
                            }
                        }

                    }
                }
            }
            clientIdLista.add(id);

            list = this.hrProtocolRepository.selectListClentId(clientIdLista);
            for (HrProtocolDTO hrProtocolDTO : list) {
                //获取数据字典协议类型
                Map<Integer, String> businessmap = codeTableService.findCodeTableByInnerName("agreementType");
                hrProtocolDTO.setAgreementTypekey(businessmap.get(hrProtocolDTO.getAgreementType()));
                hrProtocolDTO.setAgreementType(hrProtocolDTO.getAgreementType());

                //获取数据字典结算方式
                Map<Integer, String> settlementMethodmap = codeTableService.findCodeTableByInnerName("settlementMethod");
                hrProtocolDTO.setSettlementMethod(hrProtocolDTO.getSettlementMethod());
                hrProtocolDTO.setSettlementMethodkey(settlementMethodmap.get(hrProtocolDTO.getSettlementMethod()));
            }

        }
        return list;
    }


    /**
     * 数据权限控制接口
     */
    @Override
    public List<String> selectClientIdByUserId() {
        try {
            //获取当前用户
            JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
            //根据角色key来决定数据控制
            String currentRoleKey = jwtUserDTO.getCurrentRoleKey();
            List<String> clientIdList = this.noticeAuthority(currentRoleKey, jwtUserDTO.getId());
            return clientIdList;
        } catch (Exception e) {
            throw new CommonException("请重新登录！");
        }
    }

    /**
     * 角色权限控制接口
     */
    @Override
    public List<String> noticeAuthority(String currentRoleKey, String userId) {
        if (org.apache.commons.lang3.StringUtils.isBlank(currentRoleKey)) {
            throw new CommonException("该用户没有角色key,无法分配客户权限");
        }
        List<String> clientIdList = new ArrayList<>();
        //超级管理员看所有客户//或者开发者角色
        if ("super_admin".equals(currentRoleKey) || "developer".equals(currentRoleKey)) {
            //查询所有客户
            QueryWrapper<HrClient> qw = new QueryWrapper<>();
            qw.eq("is_delete", 0);
            List<HrClient> clientList = hrClientRepository.selectList(qw);
            for (HrClient hrClient : clientList) {
                clientIdList.add(hrClient.getId());
            }
            return clientIdList;
        }
        //客户角色获取该客户下所有子客户
        if ("client".equals(currentRoleKey)) {
            //获取该用户的客户id
            QueryWrapper<HrClient> qw = new QueryWrapper<>();
            qw.eq("user_id", userId);
            qw.eq("is_delete", 0);
            /*  qw.eq("status", 0);*/
            HrClient hrClient = hrClientRepository.selectOne(qw);
            //判空
            if (hrClient == null) {
                throw new CommonException("该客户不存在");
            }
            //查询该客户的子集
            QueryWrapper<HrClient> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("parent_id", hrClient.getId());
            List<HrClient> hrUserClients = hrClientRepository.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(hrUserClients)) {
                clientIdList.add(hrClient.getId());
                return clientIdList;
            }
            for (HrClient hrUserClient : hrUserClients) {
                clientIdList.add(hrUserClient.getId());
            }
            List<String> allClientIdList = getClientIdList(clientIdList, clientIdList);
            allClientIdList.add(hrClient.getId());
            return allClientIdList;
        }
        //其他角色
        else {
            //查询数据是受限
            QueryWrapper<Role> roleQueryWrapper = new QueryWrapper<>();
            roleQueryWrapper.eq("role_key", currentRoleKey);
            List<Role> list = roleService.list(roleQueryWrapper);
            if (CollectionUtils.isEmpty(list)) {
                throw new CommonException("该角色不存在");
            }
            Boolean isRestrictions = list.get(0).getIsRestrictions();
            //如果受限
            if (isRestrictions) {
                QueryWrapper<HrUserClient> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("user_id", userId);
                queryWrapper.eq("is_delete", 0);
                List<HrUserClient> hrUserClients = hrUserClientRepository.selectList(queryWrapper);
                for (HrUserClient hrUserClient : hrUserClients) {
                    clientIdList.add(hrUserClient.getClientId());
                }
                return clientIdList;
            } else {
                //查询所有客户
                QueryWrapper<HrClient> qw = new QueryWrapper<>();
                qw.eq("is_delete", 0);
                List<HrClient> clientList = hrClientRepository.selectList(qw);
                for (HrClient hrClient : clientList) {
                    clientIdList.add(hrClient.getId());
                }
                return clientIdList;
            }
        }
    }

    /**
     * 查询客户下面的所有子客户
     *
     * @param clientId 客户ID
     * @return 子客户ID集合
     */
    @Override
    public List<String> querySubordinateClient(String clientId) {
        List<String> clientIds = new ArrayList<>();
        List<HrClient> hrUserClients = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("parent_id", clientId).eq("is_delete", 0));
        if (CollectionUtils.isEmpty(hrUserClients)) {
            clientIds.add(clientId);
            return clientIds;
        }
        clientIds.addAll(hrUserClients.stream().map(HrClient::getId).collect(Collectors.toList()));
        List<String> allClientIdList = getClientIdList(clientIds, clientIds);
        allClientIdList.add(clientId);
        return allClientIdList;
    }

    /**
     * 不分页查询客户列表
     *
     * @param hrClientDTO
     * @return
     */
    @Override
    public List<HrClientDTO> nonFindPage(HrClientDTO hrClientDTO) {
        List<HrClientDTO> hrClientDTOList = hrClientRepository.nonFindPage(hrClientDTO);
        //获取数据字典业务类型
        Map<Integer, String> agreementType = codeTableService.findCodeTableByInnerName("agreementType");
        hrClientDTOList.forEach(ls -> {
            ls.setAgreementTypekey(agreementType.get(ls.getAgreementType()));
        });
        return hrClientDTOList;
    }

    @Override
    public List<String> findClientIdList(String clientId) {
        List<HrClientDTO> clientDTOS = hrClientRepository.getParentTree(Collections.singletonList(clientId));
        List<String> list = clientDTOS.stream().map(HrClientDTO::getId).collect(Collectors.toList());
        /*List<String> list = new ArrayList<>();
        List<HrClient> clientIdList = this.hrProtocolRepository.selectClientIdList(clientId);
        if (CollectionUtils.isNotEmpty(clientIdList)) {
            for (HrClient client : clientIdList) {
                list.add(client.getId());
                if (CollectionUtils.isNotEmpty(clientIdList)) {
                    List<HrClient> clientIdLists = this.hrProtocolRepository.selectClientIdList(client.getParentId());
                    for (HrClient idList : clientIdLists) {
                        list.add(idList.getId());
                    }
                    if (CollectionUtils.isNotEmpty(clientIdLists)) {
                        for (HrClient hrClient : clientIdLists) {
                            List<HrClient> selectClientIdList = this.hrProtocolRepository.selectClientIdList(hrClient.getParentId());
                            for (HrClient entry : selectClientIdList) {
                                list.add(entry.getId());

                            }
                        }
                    }
                }
            }
        }*/
        return list;
    }

    private List<String> getClientIdList(List<String> hrClientId, List<String> allClientIdList) {
        //查询该客户所有子客户
        ArrayList<String> clientIdList = new ArrayList<>();
        QueryWrapper<HrClient> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("parent_id", hrClientId);
        queryWrapper.eq("is_delete", 0);
        /*   queryWrapper.eq("status", 0);*/
        List<HrClient> hrUserClients = hrClientRepository.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(hrUserClients)) {
            for (HrClient hrUserClient : hrUserClients) {
                clientIdList.add(hrUserClient.getId());
            }
            allClientIdList.addAll(clientIdList);
            getClientIdList(clientIdList, allClientIdList);

        }
        return allClientIdList;
    }

    @Override
    public List<HrProtocolDTO> getOwnedCustomerListselectclientse(String id) {
        List<HrProtocolDTO> hrProtocolDTOlIST = this.hrClientRepository.getOwnedCustomerListselectclientse(id);
        return hrProtocolDTOlIST;
    }

    @Override
    public List<HrPlatformAccountDTO> getHrPlatformAccountAccount(HrPlatformAccountDTO hrPlatformAccountDTO) {
        List<HrPlatformAccountDTO> list = this.hrClientRepository.getHrPlatformAccountAccount(hrPlatformAccountDTO);
        return list;

    }


    /**
     * 创建客户协议恢复员工禁用状态
     *
     * @param hrProtocol
     */
    @Override
    public void handleStaffStatus(HrProtocol hrProtocol) {
        log.info("创建客户协议恢复员工禁用状态");
        List<HrTalentStaff> hrTalentStaffList = hrTalentStaffRepository.findResignationStaff(new HrTalentStaffDTO().setClientId(hrProtocol.getClientId()));
        LocalDate localDate = LocalDate.now();
        for (HrTalentStaff hrTalentStaff : hrTalentStaffList) {
            if (hrTalentStaff.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey()) && hrTalentStaff.getResignationDate() != null) {
                LocalDate plusDays = hrTalentStaff.getResignationDate().plusDays(45);
                if (plusDays.isBefore(localDate)) {//已离职超过45天的员工保留禁用状态
                    continue;
                }
            }
            hrTalentStaff.setStatus(false).setProtocolId(hrProtocol.getId());
            hrTalentStaffRepository.updateById(hrTalentStaff);
        }
    }
}
