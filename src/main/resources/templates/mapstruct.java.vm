package ${cfg.extPackage.Mapper};
import org.springframework.stereotype.Service;
import org.mapstruct.Mapper;


import ${package.Entity}.${entity};
import ${cfg.extPackage.Dto}.${entity}DTO;

@Service
@Mapper(componentModel = "spring")
public interface ${entity}Mapper extends ${cfg.superMapStructClassName}<${entity}DTO, ${entity}> {

@Override
${entity} toEntity(${entity}DTO dto);

@Override
${entity}DTO toDto(${entity} entity);
#foreach($field in ${table.fields})
    #if(${field.keyFlag})
        #set($keyPropertyType=${field.propertyType})
    #end
#end
default ${entity} fromId($!{keyPropertyType} id){
    if(id==null){
    return null;
    }
    ${entity} ${cfg.entityProperty} =new ${entity}();
    ${cfg.entityProperty}.setId(id);
    return ${cfg.entityProperty};
    }

    }
