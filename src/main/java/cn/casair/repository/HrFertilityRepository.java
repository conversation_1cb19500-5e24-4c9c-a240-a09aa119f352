package cn.casair.repository;

import cn.casair.domain.HrFertility;
import cn.casair.dto.HrFertilityDTO;
import cn.casair.dto.excel.HrFertilityExport;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 生育表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
@Repository
public interface HrFertilityRepository extends BaseMapper<HrFertility> {

    @Select("SELECT hf.id, hf.staff_id, hf.client_id, hc.client_name, ht.`name`, ht.certificate_num, ht.sex, ht.phone, hs.profession_name, hs.id, ht.personnel_type, ht.staff_status, hf.maternity_leave_start_date, hf.maternity_leave_end_date, hf.`status`, su.real_name AS specialized ,su.id  FROM hr_fertility hf LEFT JOIN hr_talent_staff ht ON hf.staff_id = ht.id  LEFT JOIN hr_staff_work_experience hsw ON hf.staff_id = hsw.staff_id  AND ht.client_id = hsw.client_id AND hsw.is_delete = 0  AND hsw.iz_default = 1 LEFT JOIN hr_station hs ON hsw.station_id = hs.id  AND hs.is_delete = 0 LEFT JOIN hr_client hc ON hc.id = hf.client_id  AND hc.is_delete = 0 LEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id  AND huc.is_delete = 0  AND huc.is_specialized = 1 LEFT JOIN sys_user su ON huc.user_id = su.id  AND su.is_delete = 0 ${ew.customSqlSegment}")
    IPage<HrFertilityDTO> page(Page page, @Param(Constants.WRAPPER)QueryWrapper<HrFertility> qw);


    HrFertilityDTO selectDetatil(String id);

    List<HrFertilityExport> exportFertility(@Param(Constants.WRAPPER)QueryWrapper<HrFertility> qw);

    List<HrFertilityDTO> fertilityListByStaffId(@Param("staffId") String id);
}
