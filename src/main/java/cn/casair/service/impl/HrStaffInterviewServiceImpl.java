package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.RecruitmentBrochure;
import cn.casair.domain.*;
import cn.casair.dto.HrExamResultDTO;
import cn.casair.dto.HrRecruitmentStationDTO;
import cn.casair.dto.HrScheduleDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.mapper.HrExamResultMapper;
import cn.casair.mapper.HrStaffInterviewMapper;
import cn.casair.repository.*;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrStaffInterviewService;
import cn.casair.service.HrStationService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 应试经历服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrStaffInterviewServiceImpl extends ServiceImpl<HrStaffInterviewRepository, HrStaffInterview>implements HrStaffInterviewService {

    private static final Logger log=LoggerFactory.getLogger(HrStaffInterviewServiceImpl.class);

    private final HrStaffInterviewRepository hrStaffInterviewRepository;
    private final HrRecruitmentBrochureRepository hrRecruitmentBrochureRepository;
    private final HrRegistrationDetailsRepository hrRegistrationDetailsRepository;
    private final HrRecruitmentBulletinRepository hrRecruitmentBulletinRepository;

    private final HrExamResultRepository hrExamResultRepository;
    private final HrRegistrationDetailsEvaluationRepository hrRegistrationDetailsEvaluationRepository;
    private final HrRecruitmentStationRepository hrRecruitmentStationRepository;
    private final HrExamDetailsRepository hrExamDetailsRepository;
    private final SysOperLogService sysOperLogService;

    @Autowired
    //private  HrExamResultService hrExamResultService;

    private final HrStaffInterviewMapper hrStaffInterviewMapper;
    private final HrExamResultMapper hrExamResultMapper;

    private final CodeTableService codeTableService;

    private final HrStationService hrStationService;

    public HrStaffInterviewServiceImpl(HrStaffInterviewRepository hrStaffInterviewRepository, HrRecruitmentBrochureRepository hrRecruitmentBrochureRepository, HrRegistrationDetailsRepository hrRegistrationDetailsRepository, HrRecruitmentBulletinRepository hrRecruitmentBulletinRepository, HrExamResultRepository hrExamResultRepository, HrRegistrationDetailsEvaluationRepository hrRegistrationDetailsEvaluationRepository, HrRecruitmentStationRepository hrRecruitmentStationRepository, HrExamDetailsRepository hrExamDetailsRepository, SysOperLogService sysOperLogService, HrStaffInterviewMapper hrStaffInterviewMapper, HrExamResultMapper hrExamResultMapper, CodeTableService codeTableService, HrStationService hrStationService){
        this.hrStaffInterviewRepository = hrStaffInterviewRepository;
        this.hrRecruitmentBrochureRepository = hrRecruitmentBrochureRepository;
        this.hrRegistrationDetailsRepository = hrRegistrationDetailsRepository;
        this.hrRecruitmentBulletinRepository = hrRecruitmentBulletinRepository;
        this.hrExamResultRepository = hrExamResultRepository;
        this.hrRegistrationDetailsEvaluationRepository = hrRegistrationDetailsEvaluationRepository;
        this.hrRecruitmentStationRepository = hrRecruitmentStationRepository;
        this.hrExamDetailsRepository = hrExamDetailsRepository;
        this.sysOperLogService = sysOperLogService;
        this.hrStaffInterviewMapper= hrStaffInterviewMapper;
        this.hrExamResultMapper = hrExamResultMapper;
        this.codeTableService= codeTableService;
        this.hrStationService = hrStationService;
    }

    /**
     * 创建应试经历
     * @param hrExamResultDTO
     * @return
     */
    @Override
//    public List<HrStaffInterviewDTO> createHrStaffInterview(HrStaffInterviewDTO hrStaffInterviewDTO){
//    log.info("Create new HrStaffInterview:{}", hrStaffInterviewDTO);
//
//        HrStaffInterview hrStaffInterview =this.hrStaffInterviewMapper.toEntity(hrStaffInterviewDTO);
//        this.hrStaffInterviewRepository.insert(hrStaffInterview);
//        return this.findInterviewList(hrStaffInterviewDTO.getStaffId());
//    }
    public List<HrExamResultDTO> createHrStaffInterview(HrExamResultDTO hrExamResultDTO){
        log.info("Create new HrStaffInterview:{}", hrExamResultDTO);

        HrExamResult hrExamResult = this.hrExamResultMapper.toEntity(hrExamResultDTO);
        this.hrExamResultRepository.insert(hrExamResult);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_INTERVIEW.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrExamResultDTO),
            HrExamResultDTO.class,
            null,
            JSON.toJSONString(hrExamResultDTO)
        );
        return this.findInterviewList(hrExamResultDTO.getStaffId());
    }

    /**
     * 修改应试经历
     * @param hrExamResultDTO
     * @return
     */
    @Override
    public Optional<List<HrExamResultDTO>> updateHrStaffInterview(HrExamResultDTO hrExamResultDTO){
        return Optional.ofNullable(this.hrExamResultRepository.selectById(hrExamResultDTO.getId()))
            .map(roleTemp->{
                HrExamResult hrExamResult = this.hrExamResultMapper.toEntity(hrExamResultDTO);
                this.hrExamResultRepository.updateById(hrExamResult);
                log.info("Update HrStaffInterview:{}", hrExamResultDTO);
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.STAFF_INTERVIEW.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrExamResultDTO),
                    HrExamResultDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrExamResultDTO),
                    null,
                    HrExamResultDTO.class
                );
                return this.findInterviewList(hrExamResultDTO.getStaffId());
            });
    }

    /**
     * 赋值字典值
     * @param hrExamResultDTO
     */
//    public void setDict(HrStaffInterviewDTO hrStaffInterviewDTO) {
//        if (hrStaffInterviewDTO.getInterviewLink() != null){
//            Map<Integer, String> interviewLink = codeTableService.findCodeTableByInnerName("interviewLink");//应试环节
//            hrStaffInterviewDTO.setInterviewLinkLabel(interviewLink.get(hrStaffInterviewDTO.getInterviewLink()));
//        }
//        if (hrStaffInterviewDTO.getInterviewResult() != null){
//            Map<Integer, String> interviewResult = codeTableService.findCodeTableByInnerName("interviewResult");//应试结果
//            hrStaffInterviewDTO.setInterviewResultLabel(interviewResult.get(hrStaffInterviewDTO.getInterviewResult()));
//        }
//        if (hrStaffInterviewDTO.getInterviewStationId() != null){
//            HrStation hrStation = hrStationService.getById(hrStaffInterviewDTO.getInterviewStationId());
//            hrStaffInterviewDTO.setProfessionName(hrStation.getProfessionName());
//        }
//    }

    public void setDict(HrExamResultDTO hrExamResultDTO) {
        if (hrExamResultDTO.getInterviewLink() != null){
            Map<Integer, String> interviewLink = codeTableService.findCodeTableByInnerName("interviewLink");//应试环节
            hrExamResultDTO.setInterviewLinkLabel(interviewLink.get(hrExamResultDTO.getInterviewLink()));
        }
        if (hrExamResultDTO.getInterviewResult() != null){
            Map<Integer, String> interviewResult = codeTableService.findCodeTableByInnerName("interviewResult");//应试结果
            hrExamResultDTO.setInterviewResultLabel(interviewResult.get(hrExamResultDTO.getInterviewResult()));
        }
//        if (hrExamResultDTO.getStaffId() != null){
//            HrStation hrStation = hrStationService.getById(hrExamResultDTO.getStationId());
//            hrExamResultDTO.setProfessionName(hrStation.getProfessionName());
//        }
    }

    /**
     * 查询应试经历详情
     * @param id
     * @return
     */
    @Override
    public HrExamResultDTO getHrStaffInterview(String id){
        log.info("Get HrStaffInterview :{}",id);

        HrExamResult hrExamResult = this.hrExamResultRepository.selectById(id);
        HrExamResultDTO hrExamResultDTO = hrExamResultMapper.toDto(hrExamResult);
        this.setDict(hrExamResultDTO);
        return hrExamResultDTO;
    }

    /**
     * 删除应试经历
     * @param id
     */
    @Override
    public List<HrExamResultDTO> deleteHrStaffInterview(String id){
        HrExamResult hrExamResult = this.hrExamResultRepository.selectById(id);
        this.hrExamResultRepository.deleteById(id);
        log.info("Delete HrStaffInterview:{}", hrExamResult);
        // 操作日志
//        List<String> list = new ArrayList<>();
//        list.add(id);
//        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.STAFF_INTERVIEW.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(list));
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        //List<String> collect = hrSocialSecurityList.stream().map(HrSocialSecurity::getSocialSecurityName).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.STAFF_INTERVIEW.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除附加信息-应试经历,应试岗位: "+JSON.toJSONString(hrExamResult.getProfessionName()),
            null,
            null,
            null,
            JSON.toJSONString(hrExamResult),
            jwtUserDTO
        );
        return this.findInterviewList(hrExamResult.getStaffId());
    }

    /**
     * 查询全部应试经历
     * @param staffId
     * @return
     */
    @Override
//    public List<HrStaffInterviewDTO> findInterviewList(String staffId) {
//        List<HrStaffInterviewDTO> list = hrStaffInterviewRepository.findInterviewByStaffId(staffId);
//        list.forEach(this::setDict);
//        return list;
//    }
    public List<HrExamResultDTO> findInterviewList(String staffId) {
        QueryWrapper<HrExamResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("staff_id",staffId);
        queryWrapper.eq("is_synchronize",1);
        List<HrExamResult> list = hrExamResultRepository.selectList(queryWrapper);
        List<HrExamResultDTO> hrExamResultDTOS = hrExamResultMapper.toDto(list);
        hrExamResultDTOS.forEach(this::setDict);
        ArrayList<HrExamResultDTO> resultExamResult = new ArrayList<>();
        for (HrExamResultDTO hrExamResultDTO : hrExamResultDTOS) {
            String paperId = hrExamResultDTO.getPaperId();
            String examName = hrExamResultDTO.getExamName();
            String professionName = hrExamResultDTO.getProfessionName();
            if (StringUtils.isBlank(paperId)&&StringUtils.isBlank(examName)){
                resultExamResult.add(hrExamResultDTO);
            }
            else {
                QueryWrapper<HrRecruitmentBrochure> brochureQueryWrapper = new QueryWrapper<>();
                brochureQueryWrapper.eq("recruit_brochure_name",hrExamResultDTO.getExamName());
                List<HrRecruitmentBrochure> hrRecruitmentBrochures = hrRecruitmentBrochureRepository.selectList(brochureQueryWrapper);
                if (CollectionUtils.isEmpty(hrRecruitmentBrochures)){
                    //正常考试
                    normalExam(resultExamResult, hrExamResultDTO);
                }
                else {
                    //简章考试
                    //笔试分数
                    BigDecimal score = hrExamResultDTO.getScore();
                    //面试分数
                    BigDecimal interviewScoreResult = hrExamResultDTO.getInterviewScoreResult();
                    //加试分数
                    BigDecimal addResult = hrExamResultDTO.getAddResult();
                    //考察结果
                    Integer examResult = hrExamResultDTO.getExamResult();
                    //体检结果
                    Integer physicalExaminationResult = hrExamResultDTO.getPhysicalExaminationResult();
                    registrationExam(resultExamResult, hrExamResultDTO, hrRecruitmentBrochures, score, 1);
                    registrationExam(resultExamResult, hrExamResultDTO, hrRecruitmentBrochures, interviewScoreResult, 2);
                    registrationExam(resultExamResult, hrExamResultDTO, hrRecruitmentBrochures, addResult, 6);
                    if (examResult!=null){
                        registrationExam(resultExamResult, hrExamResultDTO, hrRecruitmentBrochures, interviewScoreResult, 7);
                    }
                    if (physicalExaminationResult!=null){
                        registrationExam(resultExamResult, hrExamResultDTO, hrRecruitmentBrochures, interviewScoreResult, 8);
                    }
                }

            }
        }
        return resultExamResult;
    }

    private void registrationExam(ArrayList<HrExamResultDTO> resultExamResult, HrExamResultDTO hrExamResultDTO, List<HrRecruitmentBrochure> hrRecruitmentBrochures, BigDecimal score, int interviewLink) {
        if (score !=null){
            HrExamResultDTO resultExamResultDTO = new HrExamResultDTO();
            BeanUtils.copyProperties(hrExamResultDTO,resultExamResultDTO);
            //赋值分数
            resultExamResultDTO.setScore(score);
            //赋值环节
            resultExamResultDTO.setInterviewLink(interviewLink);
            //查询报名表
            QueryWrapper<HrRegistrationDetails> hrRegistrationDetailsQueryWrapper = new QueryWrapper<>();
            hrRegistrationDetailsQueryWrapper.eq("brochure_id", hrRecruitmentBrochures.get(0).getId());
            hrRegistrationDetailsQueryWrapper.eq("station_name", hrExamResultDTO.getProfessionName());
            hrRegistrationDetailsQueryWrapper.eq("staff_id", hrExamResultDTO.getStaffId());
            List<HrRegistrationDetails> hrRegistrationDetailsList = hrRegistrationDetailsRepository.selectList(hrRegistrationDetailsQueryWrapper);
            if (CollectionUtils.isEmpty(hrRegistrationDetailsList)){
                return;
            }
            //赋值时间
            if (interviewLink ==1){
                //获取笔试时间
                HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRegistrationDetailsList.get(0).getStationId());
                if (hrRecruitmentStation==null){
                    return;
                }
                //笔试地点
                resultExamResultDTO.setExamDate(hrRecruitmentStation.getWrittenExamStartTime());
                String admissionTicketInfo = hrRegistrationDetailsList.get(0).getAdmissionTicketInfo();
                JSONObject jsonObject = JSONObject.parseObject(admissionTicketInfo);
                if (jsonObject!=null){
                    resultExamResultDTO.setInterviewPlace(jsonObject.getString("examRoomPlace"));
                }
                 if (hrRecruitmentStation.getWrittenPassLine()!=null){
                     //应试结果
                     if (score.intValue() >= hrRecruitmentStation.getWrittenPassLine().intValue()) {
                         resultExamResultDTO.setInterviewResult(1);
                     } else {
                         resultExamResultDTO.setInterviewResult(2);
                     }
                 }
            }
            if (interviewLink ==2){
                //获取面试时间
                HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRegistrationDetailsList.get(0).getStationId());
                if (hrRecruitmentStation==null){
                    return;
                }
                resultExamResultDTO.setExamDate(hrRecruitmentStation.getInterviewExamStartTime());
                resultExamResultDTO.setInterviewPlace(hrRecruitmentStation.getInterviewLocation());
                if (hrRecruitmentStation.getInterviewPassLine()!=null){
                    if (score.intValue() >= hrRecruitmentStation.getInterviewPassLine().intValue()) {
                        resultExamResultDTO.setInterviewResult(1);
                    } else {
                        resultExamResultDTO.setInterviewResult(2);
                    }
                }

            }
            if (interviewLink ==6){

            }
            if (interviewLink ==7){
//                //获取考察时间
//                QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
//                hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochures.get(0).getId());
//                hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultDTO.getProfessionName());
//                hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.INVESTIGATION_RESULTS.getKey());
//                List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
//                if (CollectionUtils.isEmpty(hrRecruitmentBulletins)){
//                    return;
//                }
                LocalDateTime examDate = null;
                String investigationPlace = "";
                HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationRepository.selectById(hrRegistrationDetailsList.get(0).getStationId());
                Integer examFormat = hrRecruitmentStation.getExamFormat();
                if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
                    QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
                    hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochures.get(0).getId());
                    hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name",  hrExamResultDTO.getProfessionName());
                    hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.WRITTEN_ACHIEVEMENT.getKey());
                    hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
                    List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
                    if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins)){
                        examDate = hrRecruitmentBulletins.get(0).getInvestigationStartTime();
                        investigationPlace = hrRecruitmentBulletins.get(0).getInvestigationPlace();
                    }
                } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
                    QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
                    hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochures.get(0).getId());
                    hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name",  hrExamResultDTO.getProfessionName());
                    hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.INTERVIEW_ACHIEVEMENT.getKey());
                    hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
                    List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
                    if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins)){
                        examDate = hrRecruitmentBulletins.get(0).getInvestigationStartTime();
                        investigationPlace = hrRecruitmentBulletins.get(0).getInvestigationPlace();
                    }
                } else {
                    QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
                    hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochures.get(0).getId());
                    hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name",  hrExamResultDTO.getProfessionName());
                    hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.FINAL_ACHIEVEMENT.getKey());
                    hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
                    List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
                    if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins)){
                        examDate = hrRecruitmentBulletins.get(0).getInvestigationStartTime();
                        investigationPlace = hrRecruitmentBulletins.get(0).getInvestigationPlace();
                    }
                }
                resultExamResultDTO.setExamDate(examDate);
                resultExamResultDTO.setInterviewPlace(investigationPlace);
                resultExamResultDTO.setScore(null);
                if (hrExamResultDTO.getExamResult()==1) {
                    resultExamResultDTO.setInterviewResult(1);
                } else {
                    resultExamResultDTO.setInterviewResult(2);
                }
            }
            if (interviewLink ==8){
                //体检
                LocalDate checkupDate = hrRegistrationDetailsList.get(0).getCheckupDate();
                if (checkupDate!=null){
                    resultExamResultDTO.setExamDate(hrRegistrationDetailsList.get(0).getCheckupDate().atStartOfDay());
                }
                resultExamResultDTO.setInterviewPlace(hrRegistrationDetailsList.get(0).getCheckupPlace());
                resultExamResultDTO.setScore(null);
                if (hrExamResultDTO.getExamResult()==1) {
                    resultExamResultDTO.setInterviewResult(1);
                } else {
                    resultExamResultDTO.setInterviewResult(2);
                }
            }

            //获取评价
            QueryWrapper<HrRegistrationDetailsEvaluation> evaluationQueryWrapper = new QueryWrapper<>();
            evaluationQueryWrapper.eq("details_id", hrRegistrationDetailsList.get(0).getId());
            evaluationQueryWrapper.eq("evaluation_status",interviewLink);
            List<HrRegistrationDetailsEvaluation> hrRegistrationDetailsEvaluations = hrRegistrationDetailsEvaluationRepository.selectList(evaluationQueryWrapper);
            if (CollectionUtils.isNotEmpty(hrRegistrationDetailsEvaluations)){
                resultExamResultDTO.setEvaluation(hrRegistrationDetailsEvaluations.get(0).getEvaluation());
            }
            this.setDict(resultExamResultDTO);
            resultExamResult.add(resultExamResultDTO);
        }
    }


    /**
     * 正常的考试拆分应试应试经历
     * @param resultExamResult
     * @param hrExamResultDTO
     */
    private void normalExam(ArrayList<HrExamResultDTO> resultExamResult, HrExamResultDTO hrExamResultDTO) {
        QueryWrapper<HrExamDetails> hrExamDetailsQueryWrapper = new QueryWrapper<>();
        hrExamDetailsQueryWrapper.eq("exam_result_id",hrExamResultDTO.getId());
        hrExamDetailsQueryWrapper.orderByDesc("exam_time");
        List<HrExamDetails> hrExamDetailsList = hrExamDetailsRepository.selectList(hrExamDetailsQueryWrapper);
        for (HrExamDetails hrExamDetails : hrExamDetailsList) {
            HrExamResultDTO resultExamResultDTO = new HrExamResultDTO();
            BeanUtils.copyProperties(hrExamResultDTO, resultExamResultDTO);
            //赋值分数
            resultExamResultDTO.setScore(hrExamDetails.getScore());
            //赋值环节
            resultExamResultDTO.setInterviewLink(hrExamDetails.getExamType());
            //赋值时间
            resultExamResultDTO.setExamDate(hrExamDetails.getExamTime());
            //赋值地点
            resultExamResultDTO.setInterviewPlace(hrExamDetails.getExamPlace());
            //应试结果
            if (hrExamDetails.getIsPassed()==1) {
                resultExamResultDTO.setInterviewResult(1);
            } else {
                resultExamResultDTO.setInterviewResult(2);
            }
            //获取评价
            List<HrRegistrationDetailsEvaluation> hrRegistrationDetailsEvaluations = getHrRegistrationDetailsEvaluations(hrExamResultDTO, hrExamDetails.getExamType());
            if (CollectionUtils.isNotEmpty(hrRegistrationDetailsEvaluations)) {
                resultExamResultDTO.setEvaluation(hrRegistrationDetailsEvaluations.get(0).getEvaluation());
            }
            this.setDict(resultExamResultDTO);
            resultExamResult.add(resultExamResultDTO);
        }
//        //正常考试(笔试)
//        BigDecimal score = hrExamResultDTO.getScore();
//        int interviewLink= 1;
//        if (score!=null){
//            HrExamResultDTO resultExamResultDTO = new HrExamResultDTO();
//            BeanUtils.copyProperties(hrExamResultDTO,resultExamResultDTO);
//            //赋值分数
//            resultExamResultDTO.setScore(score);
//           //赋值环节
//            resultExamResultDTO.setInterviewLink(1);
//            //赋值时间
//            if (hrExamResultDTO.getWrittenExamStartTime()!=null){
//                resultExamResultDTO.setExamDate(hrExamResultDTO.getWrittenExamStartTime());
//            }
//            //赋值地点
//            if (StringUtils.isNotBlank(hrExamResultDTO.getWrittenLocation())){
//                resultExamResultDTO.setInterviewPlace(hrExamResultDTO.getWrittenLocation());
//            }
//            //获取评价
//            List<HrRegistrationDetailsEvaluation> hrRegistrationDetailsEvaluations = getHrRegistrationDetailsEvaluations(hrExamResultDTO,1);
//            if (CollectionUtils.isNotEmpty(hrRegistrationDetailsEvaluations)){
//                resultExamResultDTO.setEvaluation(hrRegistrationDetailsEvaluations.get(0).getEvaluation());
//            }
//            this.setDict(resultExamResultDTO);
//            resultExamResult.add(resultExamResultDTO);
//
//        }
//        //正常考试(面试）
//        if (hrExamResultDTO.getInterviewScoreResult()!=null){
//            HrExamResultDTO resultExamResultDTO = new HrExamResultDTO();
//            BeanUtils.copyProperties(hrExamResultDTO,resultExamResultDTO);
//            //赋值分数
//            resultExamResultDTO.setScore(hrExamResultDTO.getInterviewScoreResult());
//            //赋值环节
//            resultExamResultDTO.setInterviewLink(2);
//            resultExamResultDTO.setInterviewResultLabel(null);
//            resultExamResultDTO.setInterviewResult(null);
//            //赋值时间
//            if (hrExamResultDTO.getInterviewExamStartTime()!=null){
//                resultExamResultDTO.setExamDate(hrExamResultDTO.getInterviewExamStartTime());
//            }
//            //赋值地点
//            if (StringUtils.isNotBlank(hrExamResultDTO.getInterviewLocation())){
//                resultExamResultDTO.setInterviewPlace(hrExamResultDTO.getInterviewLocation());
//            }
//            //获取评价
//            List<HrRegistrationDetailsEvaluation> hrRegistrationDetailsEvaluations = getHrRegistrationDetailsEvaluations(hrExamResultDTO,2);
//            if (CollectionUtils.isNotEmpty(hrRegistrationDetailsEvaluations)){
//                resultExamResultDTO.setEvaluation(hrRegistrationDetailsEvaluations.get(0).getEvaluation());
//            }
//            this.setDict(resultExamResultDTO);
//            resultExamResult.add(resultExamResultDTO);
//        }
    }

    /**
     * 获取评价
     * @param hrExamResultDTO
     * @return
     */
    private List<HrRegistrationDetailsEvaluation> getHrRegistrationDetailsEvaluations(HrExamResultDTO hrExamResultDTO,Integer type) {
        //获取评价
        QueryWrapper<HrRegistrationDetailsEvaluation> evaluationQueryWrapper = new QueryWrapper<>();
        evaluationQueryWrapper.eq("details_id", hrExamResultDTO.getId());
        evaluationQueryWrapper.eq("evaluation_status",type);
        List<HrRegistrationDetailsEvaluation> hrRegistrationDetailsEvaluations = hrRegistrationDetailsEvaluationRepository.selectList(evaluationQueryWrapper);
        return hrRegistrationDetailsEvaluations;
    }

}
