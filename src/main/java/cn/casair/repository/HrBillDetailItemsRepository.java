package cn.casair.repository;

import cn.casair.domain.HrBillDetail;
import cn.casair.domain.HrBillDetailItems;
import cn.casair.dto.HrBillDetailItemsDTO;
import cn.casair.dto.HrBillDynamicFieldsDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * 薪酬账单动态费用详情数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Repository
public interface HrBillDetailItemsRepository extends BaseMapper<HrBillDetailItems> {


    /**
     * 根据账单明细id获取工资增减项
     *
     * @param billDetailIds
     * @return cn.casair.domain.HrBillDetailItems
     * <AUTHOR>
     * @date 2021/11/5
     **/
    List<HrBillDetailItemsDTO> getByBillDetailId(@Param("billDetailIds") List<String> billDetailIds);

    void deleteByBillId(String billId);

    /**
     *  获取动态费用项数据
     * @param billId 基础账单
     * @param dynamicFees 动态费用项字段
     * @return
     */
    List<HashMap<String, Object>> getBillDynamicItemsData(@Param("billId") String billId, @Param("dynamicFees") List<String> dynamicFees);

    /**
     * 获取该账单关联的任意一个账单明细对应的动态费用项数据
     *
     * @param billId 账单id
     * @return
     */
    @Select("select * from hr_bill_detail_items where bill_detail_id = (select id from hr_bill_detail where bill_id = #{billId} and is_delete = 0 limit 1)")
    List<HrBillDetailItemsDTO> getBillDynamicItemsByAnyDetail(String billId);

    /**
     * 获取账单明细表头
     * @param billDetailIds 账单详情ID
     * @return
     */
    List<String> getBillDynamicItemsByBillDetailId(@Param("billDetailIds") List<String> billDetailIds);

    void deleteByBillIdBatch(@Param("billIdList") List<String> billIdList);

    /**
     * 删除中石化账单动态添加的费用项
     * @param billDetailIds
     * @param expenseType
     * @param expenseName
     */
    void deleteBatch(@Param("billDetailIds") List<String> billDetailIds, @Param("expenseType") String expenseType, @Param("expenseName") String expenseName);

    /**
     * 批量添加
     * @param insertHrBillDetailItems
     */
    void batchSave(@Param("list") List<HrBillDetailItemsDTO> insertHrBillDetailItems);

}
