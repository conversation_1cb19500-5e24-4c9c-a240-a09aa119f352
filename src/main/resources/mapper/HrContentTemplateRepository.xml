<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrContentTemplateRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, label, name, required, defaults, type, optionsName, width, ruleType, groupName,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>


    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrContentTemplate">
                    <id column="id" property="id"/>
                    <result column="label" property="label"/>
                    <result column="name" property="name"/>
                    <result column="required" property="required"/>
                    <result column="defaults" property="defaults"/>
                    <result column="type" property="type"/>
                    <result column="optionsName" property="optionsname"/>
                    <result column="width" property="width"/>
                    <result column="ruleType" property="ruletype"/>
                    <result column="groupName" property="groupname"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>
    <update id="updatelabel">
        UPDATE hr_content_template set label=#{certificateName}   where name=#{id} and is_delete=0
    </update>
</mapper>
