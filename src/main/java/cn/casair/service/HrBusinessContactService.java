package cn.casair.service;

import cn.casair.domain.HrBusinessContact;
import cn.casair.dto.HrBusinessContactDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 业务联系人服务类
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
public interface HrBusinessContactService extends IService<HrBusinessContact> {


    /**
     * 获取最大排序值
     *
     * @return int
     * <AUTHOR>
     * @date 2022/3/1
     **/
    int getMaxSort();

    /**
     * 首页业务联系人
     *
     * @return java.util.List<cn.casair.dto.HrBusinessContactDTO>
     * <AUTHOR>
     * @date 2022/2/15
     **/
    List<HrBusinessContactDTO> homePageBusinessContact();

    /**
     * 创建业务联系人
     *
     * @param hrBusinessContactDTO
     * @return
     */
    HrBusinessContactDTO createHrBusinessContact(HrBusinessContactDTO hrBusinessContactDTO);

    /**
     * 修改业务联系人
     *
     * @param hrBusinessContactDTO
     * @return
     */
    Optional<HrBusinessContactDTO> updateHrBusinessContact(HrBusinessContactDTO hrBusinessContactDTO);

    /**
     * 查询业务联系人详情
     *
     * @param id
     * @return
     */
    HrBusinessContactDTO getHrBusinessContact(String id);

    /**
     * 批量删除业务联系人
     *
     * @param ids
     */
    void deleteHrBusinessContact(List<String> ids);

    /**
     * 分页查询业务联系人
     *
     * @param hrBusinessContactDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrBusinessContactDTO> findPage(HrBusinessContactDTO hrBusinessContactDTO, Long pageNumber, Long pageSize);
}
