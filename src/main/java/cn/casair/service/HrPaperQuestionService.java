package cn.casair.service;

import cn.casair.domain.HrPaperQuestion;
import cn.casair.dto.HrPaperQuestionDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 试卷题库关联表服务类
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
public interface HrPaperQuestionService extends IService<HrPaperQuestion> {


    /**
     * 创建试卷题库关联表
     * @param hrPaperQuestionDTO
     * @return
     */
    HrPaperQuestionDTO createHrPaperQuestion(HrPaperQuestionDTO hrPaperQuestionDTO);

    /**
     * 修改试卷题库关联表
     * @param hrPaperQuestionDTO
     * @return
     */
    Optional<HrPaperQuestionDTO> updateHrPaperQuestion(HrPaperQuestionDTO hrPaperQuestionDTO);

    /**
     * 查询试卷题库关联表详情
     * @param id
     * @return
     */
    HrPaperQuestionDTO getHrPaperQuestion(String id);

    /**
     * 删除试卷题库关联表
     * @param id
     */
    void deleteHrPaperQuestion(String id);

    /**
     * 批量删除试卷题库关联表
     * @param ids
     */
    void deleteHrPaperQuestion(List<String> ids);



 }
