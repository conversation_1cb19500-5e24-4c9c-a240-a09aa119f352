package cn.casair.ncc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * NCC会计科目及辅助信息传输对象（DTO）
 * 用于接收 NCC 会计科目查询接口返回的数据
 */
@Data
public class NccAccountAuxiliaryDTO {

    /**
     * 科目名称
     * 示例值：250101\长期借款\借款
     */
    @JsonProperty("name")
    private String name;

    /**
     * 科目编码
     * 示例值：250101
     */
    @JsonProperty("code")
    private String code;

    /**
     * 科目主键
     * 示例值：10011A10000000002GZS
     */
    @JsonProperty("pk_accasoa")
    private String pkAccasoa;

    /**
     * 辅助信息数组
     * 包含辅助核算项的编码和名称
     */
    @JsonProperty("auxiliary")
    private List<Auxiliary> auxiliary;

    /**
     * 辅助信息 DTO（内部类）
     */
    @Data
    public static class Auxiliary {

        /**
         * 辅助编码
         * 示例值：0052
         */
        @JsonProperty("access_code")
        private String accessCode;

        /**
         * 辅助名称
         * 示例值：贷款银行档案
         */
        @JsonProperty("access_name")
        private String accessName;
    }
}
