package cn.casair.repository;

import cn.casair.domain.HrReport;
import cn.casair.dto.HrReportDTO;
import cn.casair.dto.excel.HrReportExport;
import cn.casair.dto.report.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 报告管理数据库操作类
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Repository
public interface HrReportRepository extends BaseMapper<HrReport> {

    /**
     * 获取客户信息变动统计列表
     *
     * @param queryParam
     * @return
     */
    List<ClientInfoChangeDTO> getClientInfoChange(@Param("params") ReportQueryParam queryParam);

    IPage<HrReportDTO> selectPages(Page<HrReport> page, @Param("param1") HrReportDTO hrReportDTO);

    List<HrReportExport> findList(@Param("param1") HrReportDTO reportDTO);

    IPage<BusinessDataDTO> getBusinessData(Page<BusinessDataDTO> page, @Param("param") ReportQueryParam queryParam);

    List<BusinessDataDTO> getBusinessData(@Param("param") ReportQueryParam queryParam);

    IPage<WorkDataDTO> getWorkData(Page<WorkDataDTO> page, @Param("param") ReportQueryParam queryParam);

    List<WorkDataDTO> getWorkData(@Param("param") ReportQueryParam queryParam);


    /**
     * 分页查询 人员增减变化数据
     *
     * @param page
     * @param queryParam
     * @return
     */
    IPage<EmployeeChangeDataDTO> getEmployeeChangeData(Page<EmployeeChangeDataDTO> page, @Param("param") ReportQueryParam queryParam);

    /**
     * 查询 人员增减变化数据
     *
     * @param queryParam
     * @return
     */
    List<EmployeeChangeDataDTO> getEmployeeChangeData(@Param("param") ReportQueryParam queryParam);

    /**
     * 分页查询 人员增减变化数据
     *
     * @param page
     * @param queryParam
     * @return
     */
    IPage<IncomeComparisonDataDTO> getIncomeComparisonData(Page<IncomeComparisonDataDTO> page, @Param("param") ReportQueryParam queryParam);

    /**
     * 查询 人员增减变化数据
     *
     * @param queryParam
     * @return
     */
    List<IncomeComparisonDataDTO> getIncomeComparisonData(@Param("param") ReportQueryParam queryParam);



    /**
     * 分页查询  预算执行控制数据
     *
     * @param page
     * @param queryParam
     * @return
     */
    IPage<BudgetControlDataDTO> getBudgetControlData(Page<BudgetControlDataDTO> page, @Param("param") ReportQueryParam queryParam);

    /**
     * 查询  预算执行控制数据
     *
     * @param queryParam
     * @return
     */
    List<BudgetControlDataDTO> getBudgetControlData(@Param("param") ReportQueryParam queryParam);

    /**
     * 分页查询招聘数据统计
     *
     * @param page
     * @param queryParam
     * @return
     */
    IPage<RecruitmentDataDTO> getRecruitmentData(Page<RecruitmentDataDTO> page, @Param("param") ReportQueryParam queryParam);

    /**
     * 查询招聘数据统计
     *
     * @param queryParam
     * @return
     */
    List<RecruitmentDataDTO> getRecruitmentData(@Param("param") ReportQueryParam queryParam);

    /**
     * 分页招聘信息汇总表
     *
     * @param page
     * @param queryParam
     * @return
     */
    IPage<RecruitmentInfoDataDTO> getRecruitmentInfoData(Page<RecruitmentInfoDataDTO> page, @Param("param") ReportQueryParam queryParam);

    /**
     * 招聘信息汇总表
     *
     * @param queryParam
     * @return
     */
    List<RecruitmentInfoDataDTO> getRecruitmentInfoData(@Param("param") ReportQueryParam queryParam);

    /**
     * 分页PRO业务样表
     *
     * @param page
     * @param queryParam
     * @return
     */
    IPage<RecruitmentPRODataDTO> getRecruitmentPROData(Page<RecruitmentPRODataDTO> page, @Param("param") ReportQueryParam queryParam);

    /**
     * 分页PRO业务样表
     *
     * @param queryParam
     * @return
     */
    List<RecruitmentPRODataDTO> getRecruitmentPROData(@Param("param") ReportQueryParam queryParam);

}
