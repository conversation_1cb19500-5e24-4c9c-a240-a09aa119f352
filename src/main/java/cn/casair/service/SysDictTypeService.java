package cn.casair.service;
import cn.casair.domain.SysDictType;
import cn.casair.dto.SysDictTypeDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 字典类型表服务类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
public interface SysDictTypeService extends IService<SysDictType> {

    /**
     * 创建数据字典类型
     * @param sysDictTypeDTO
     * @return
     */
    SysDictTypeDTO createSysDictType(SysDictTypeDTO sysDictTypeDTO);

    /**
     * 修改数据字典类型
     * @param sysDictTypeDTO
     * @return
     */
    Optional<SysDictTypeDTO> updateSysDictType(SysDictTypeDTO sysDictTypeDTO);

    /**
     * 查询数据字典类型详情
     * @param id
     * @return
     */
    SysDictTypeDTO getSysDictType(Long id);

    /**
     * 删除数据字典类型
     * @param id
     */
    void deleteSysDictType(Long id);

    /**
     * 批量删除数据字典类型
     * @param ids
     */
    void deleteSysDictType(List<Long> ids);

    /**
     * 分页查询数据字典类型
     * @param sysDictTypeDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(SysDictTypeDTO sysDictTypeDTO,Long pageNumber,Long pageSize);
}
