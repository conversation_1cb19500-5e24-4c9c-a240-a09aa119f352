package cn.casair.service;

import cn.casair.domain.HrRealNameAuth;
import cn.casair.dto.HrRealNameAuthDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 实名认证服务类
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
public interface HrRealNameAuthService extends IService<HrRealNameAuth> {

    /**
     * 使命认证
     *
     * @param hrRealNameAuthDTO
     */
    void authNameCertificateNum(HrRealNameAuthDTO hrRealNameAuthDTO);
}
