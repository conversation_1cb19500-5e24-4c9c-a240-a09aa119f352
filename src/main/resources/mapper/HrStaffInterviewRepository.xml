<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffInterviewRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, staff_id, interview_unit, interview_station_id, interview_link, interview_time, interview_place, interview_examiner, interview_grade, interview_result, interview_evaluate, interview_remark,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffInterview">
                    <id column="id" property="id"/>
                    <result column="staff_id" property="staffId"/>
                    <result column="interview_unit" property="interviewUnit"/>
                    <result column="interview_station_id" property="interviewStationId"/>
                    <result column="interview_link" property="interviewLink"/>
                    <result column="interview_time" property="interviewTime"/>
                    <result column="interview_place" property="interviewPlace"/>
                    <result column="interview_examiner" property="interviewExaminer"/>
                    <result column="interview_grade" property="interviewGrade"/>
                    <result column="interview_result" property="interviewResult"/>
                    <result column="interview_evaluate" property="interviewEvaluate"/>
                    <result column="interview_remark" property="interviewRemark"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

    <sql id="findSql">
        SELECT a.*, b.profession_name FROM hr_staff_interview a
        LEFT JOIN hr_station b ON a.staff_id = b.id
    </sql>

    <select id="findInterviewByStaffId" resultType="cn.casair.dto.HrStaffInterviewDTO">
        <include refid="findSql"/>
        WHERE a.is_delete = 0
        <if test="staffId != null and staffId != ''">
            and a.staff_id = #{staffId}
        </if>
    </select>

    <select id="selectInterviewById" resultType="cn.casair.dto.HrStaffInterviewDTO">
        <include refid="findSql"/>
        WHERE a.is_delete = 0 AND a.id = #{id}
    </select>
</mapper>
