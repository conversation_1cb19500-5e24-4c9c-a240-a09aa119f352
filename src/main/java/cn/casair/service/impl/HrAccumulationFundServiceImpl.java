package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.BigDecimalCompare;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrAccumulationFundImport;
import cn.casair.dto.excel.HrAccumulationFundTemplate;
import cn.casair.mapper.HrAccumulationFundMapper;
import cn.casair.mapper.HrClientMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.asynchronous.AccumulationFoundComponent;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 公积金类型管理服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrAccumulationFundServiceImpl extends ServiceImpl<HrAccumulationFundRepository, HrAccumulationFund> implements HrAccumulationFundService {

    @Value("${file.temp-path}")
    private String fileTempPath;

    private final HrStaffWelfareRecordRepository hrStaffWelfareRecordRepository;
    private final HrFeeReviewRepository hrFeeReviewRepository;
    private final HrStaffWelfareRecordService hrStaffWelfareRecordService;
    private final HrWelfareCompensationRecordService hrWelfareCompensationRecordService;
    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final HrStaffEmolumentRepository hrStaffEmolumentRepository;
    private final HrAccumulationFundRepository hrAccumulationFundRepository;
    private final HrAccumulationFundMapper hrAccumulationFundMapper;
    private final CodeTableService codeTableService;
    private final HrClientService hrClientService;
    private final HrClientRepository hrClientRepository;
    private final HrClientMapper hrClientMapper;
    private final RedisCache redisCache;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;

    @Autowired
    private  AccumulationFoundComponent accumulationFoundComponent;

    @Value("${minio.excelPrefix}")
    private String excelPrefix;

    @Override
    public List<HrAccumulationFundDTO> getAccumulationFundTypeList() {
        return this.hrAccumulationFundRepository.getAccumulationFundTypeList();
    }

    /**
     * 创建公积金类型管理
     *
     * @param hrAccumulationFundDTO
     * @return
     */
    @Override
    public HrAccumulationFundDTO createHrAccumulationFund(HrAccumulationFundDTO hrAccumulationFundDTO) {
        log.info("Create new HrAccumulationFund:{}", hrAccumulationFundDTO);

        HrAccumulationFund hrAccumulationFund = this.hrAccumulationFundMapper.toEntity(hrAccumulationFundDTO);
        BigDecimal personageScale = extracted(1, hrAccumulationFund.getPersonageScale());
        hrAccumulationFund.setPersonageScale(personageScale);
        BigDecimal unitScale = extracted(1, hrAccumulationFund.getUnitScale());
        hrAccumulationFund.setUnitScale(unitScale);
        //公积金名称不可以重复
        List<HrAccumulationFund> list = getHrAccumulationFunds(hrAccumulationFundDTO.getTypeName());
        if (CollectionUtils.isNotEmpty(list)){
            throw new CommonException("公积金类型名称不可以重复！");
        }
        this.hrAccumulationFundRepository.insert(hrAccumulationFund);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.ACCUMULATION_FUND_TYPE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrAccumulationFundDTO),
            HrAccumulationFundDTO.class,
            null,
            JSON.toJSONString(hrAccumulationFund)
        );
        return this.hrAccumulationFundMapper.toDto(hrAccumulationFund);

    }

    /**
     * 修改公积金类型管理
     *
     * @param hrAccumulationFundDTO
     * @return
     */
    @Override
    public Optional<List<String>> updateHrAccumulationFund(HrAccumulationFundDTO hrAccumulationFundDTO) {
        return Optional.ofNullable(this.hrAccumulationFundRepository.selectById(hrAccumulationFundDTO.getId()))
            .map(roleTemp -> {
                if (!roleTemp.getTypeName().equals(hrAccumulationFundDTO.getTypeName())){
                    List<HrAccumulationFund> list = getHrAccumulationFunds(hrAccumulationFundDTO.getTypeName());
                    if (CollectionUtils.isNotEmpty(list)){
                        throw new CommonException("公积金类型名称不可以重复！");
                    }
                }
                HrAccumulationFund hrAccumulationFund = this.hrAccumulationFundMapper.toEntity(hrAccumulationFundDTO);
                if (hrAccumulationFund.getPersonageScale() != null) {
                    BigDecimal personageScale = extracted(1, hrAccumulationFund.getPersonageScale());
                    hrAccumulationFund.setPersonageScale(personageScale);
                }

                if (hrAccumulationFund.getUnitScale() != null) {
                    BigDecimal unitScale = extracted(1, hrAccumulationFund.getUnitScale());
                    hrAccumulationFund.setUnitScale(unitScale);
                }

                List<String> ids = new ArrayList<>();
                // 检查缴费年月
                if (StringUtils.isNotBlank(hrAccumulationFundDTO.getPaymentDate())) {
                    String[] split = hrAccumulationFundDTO.getPaymentDate().split("-");
                    int payYear = Integer.parseInt(split[0]);
                    int payMonthly = Integer.parseInt(split[1]);

                    if (payYear != LocalDate.now().getYear()) {
                        throw new CommonException("缴费年月只能选择当前年！");
                    }
                    if (payMonthly > LocalDate.now().getMonthValue()) {
                        throw new CommonException("缴费年月不能大于本月！");
                    }

                    // 检查公积金比例是否发生变化
                    HrWelfareCompensationRecord hrWelfareCompensationRecord = this.checkScaleIsChange(roleTemp, hrAccumulationFund);
                    if (hrWelfareCompensationRecord != null) {
                        // 获取使用此公积金比例的公司列表
                        List<HrClient> clientList = this.hrAccumulationFundRepository.getClientByProvidentFundTypeId(hrAccumulationFund.getId());
                        if (!clientList.isEmpty()) {
                            clientList.forEach(client -> {
                                int loopEndMonth = 0;
                                // 获取该员工公司的最近审核通过账单
                                HrFeeReview hrFeeReview = this.hrFeeReviewRepository.selectNewestRecordByClientId(client.getId());
                                if (hrFeeReview == null) {
                                    loopEndMonth = LocalDate.now().getMonthValue() - 1;
                                } else {
                                    // 判断出账单缴费年月是否跨年
                                    if (hrFeeReview.getPayYear() == LocalDate.now().getYear()) {
                                        loopEndMonth = hrFeeReview.getPayMonthly();
                                    }
                                }
                                List<HrEmployeeWelfareDTO> hrEmployeeWelfareList = this.hrStaffEmolumentRepository.selectByClientId(client.getId());
                                int finalLoopEndMonth = loopEndMonth;
                                hrEmployeeWelfareList.forEach(employeeWelfareOld -> {
                                    if (employeeWelfareOld.getPayMonthly() != null && finalLoopEndMonth != 0 && this.checkWelfareIsComplete(employeeWelfareOld)) {
                                        for (int i = payMonthly; i <= employeeWelfareOld.getPayMonthly(); i++) {
                                            HrWelfareCompensation hrWelfareCompensation = this.calculateCompensation(employeeWelfareOld, hrAccumulationFund, payYear, i, hrWelfareCompensationRecord.getId());
                                            this.hrWelfareCompensationRepository.insert(hrWelfareCompensation);

                                            String redisKey = RedisKeyEnum.currencyKey.PAYMENT_DATE.getValue() + employeeWelfareOld.getId();
                                            this.redisCache.setCacheObject(redisKey, JSON.toJSONString(employeeWelfareOld), 24, TimeUnit.HOURS);

                                            // 更新员工缴费年月
                                            this.hrStaffEmolumentRepository.updateStaffPaymentDate(employeeWelfareOld.getId(), payYear, payMonthly);
                                            ids.add(hrWelfareCompensation.getId());
                                        }
                                    }
                                });
                            });
                        }
                    }
                }

                String redisKey = RedisKeyEnum.currencyKey.ACCUMULATION_FUND.getValue() + roleTemp.getId();
                this.redisCache.setCacheObject(redisKey, JSON.toJSONString(roleTemp), 24, TimeUnit.HOURS);

                this.hrAccumulationFundRepository.updateById(hrAccumulationFund);
                log.info("Update HrAccumulationFund:{}", hrAccumulationFundDTO);
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.ACCUMULATION_FUND_TYPE.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrAccumulationFundDTO),
                    HrAccumulationFundDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrAccumulationFundDTO),
                    null,
                    HrAccumulationFundDTO.class
                );
                return ids;
            });
    }

    /**
     * 根据公积金名称查询
     * @param typeName
     * @return
     */
    private List<HrAccumulationFund> getHrAccumulationFunds(String typeName) {
        //公积金名称不可以重复
        QueryWrapper<HrAccumulationFund> hrAccumulationFundQueryWrapper = new QueryWrapper<>();
        hrAccumulationFundQueryWrapper.eq("type_name", typeName);
        return list(hrAccumulationFundQueryWrapper);
    }

    /**
     * 判断员工薪酬配置是否完整
     *
     * @param employeeWelfareOld
     * @return boolean
     * <AUTHOR>
     * @date 2021/11/1
     **/
    private boolean  checkWelfareIsComplete(HrEmployeeWelfareDTO employeeWelfareOld) {
        return employeeWelfareOld.getAccumulationFundCardinal() != null;
    }

    /**
     * 计算公积金基数福利补差
     *
     * @param employeeWelfareOld
     * @param hrAccumulationFund
     * @param payYear
     * @param payMonthly
     * @param recordId
     * @return cn.casair.domain.HrWelfareCompensation
     * <AUTHOR>
     * @date 2021/11/1
     **/
    private HrWelfareCompensation calculateCompensation(HrEmployeeWelfareDTO employeeWelfareOld, HrAccumulationFund hrAccumulationFund, int payYear, int payMonthly, String recordId) {
        // 查询缴费年月员工福利记录
        Map<String, Object> query = new HashMap<>();
        query.put("staffId", employeeWelfareOld.getId());
        query.put("payYear", payYear);
        query.put("payMonthly", 0);
        HrStaffWelfareRecord benchmarkRecord = this.hrStaffWelfareRecordService.selectByObject(query);
        // 检查基准数据
        if (benchmarkRecord == null) {
            benchmarkRecord = new HrStaffWelfareRecord()
                .setStaffId(employeeWelfareOld.getId())
                .setPayYear(payYear)
                .setPayMonthly(0)
//                .setSocialSecurityCardinalBase(employeeWelfareOld.getSocialSecurityCardinal())
                .setUnitPensionCardinalBase(employeeWelfareOld.getUnitPensionCardinal())
                .setUnitUnemploymentCardinalBase(employeeWelfareOld.getUnitUnemploymentCardinal())
                .setWorkInjuryCardinalBase(employeeWelfareOld.getWorkInjuryCardinal())
                .setUnitMaternityCardinalBase(employeeWelfareOld.getUnitMaternityCardinal())
                .setMedicalInsuranceCardinalBase(employeeWelfareOld.getMedicalInsuranceCardinal())
                .setAccumulationFundCardinalBase(employeeWelfareOld.getAccumulationFundCardinal())
                .setPersonalPensionCardinalBase(employeeWelfareOld.getPersonalPensionCardinal())
                .setPersonalUnemploymentCardinalBase(employeeWelfareOld.getPersonalUnemploymentCardinal())
                .setMedicalInsuranceCardinalBasePersonal(employeeWelfareOld.getMedicalInsuranceCardinalPersonal())
                .setUnitLargeMedicalExpenseBase(employeeWelfareOld.getUnitLargeMedicalExpense())
                .setReplenishWorkInjuryExpenseBase(employeeWelfareOld.getReplenishWorkInjuryExpense())
                .setPersonalLargeMedicalExpenseBase(employeeWelfareOld.getPersonalLargeMedicalExpense())
                .setPersonalMaternityCardinalBase(employeeWelfareOld.getPersonalMaternityCardinal())
                .setPersonalMaternityScale(employeeWelfareOld.getPersonalMaternity())
                .setUnitPensionScale(employeeWelfareOld.getUnitPension())
                .setUnitMedicalScale(employeeWelfareOld.getUnitMedical())
                .setWorkInjuryScale(employeeWelfareOld.getWorkInjury())
                .setUnitUnemploymentScale(employeeWelfareOld.getUnitUnemployment())
                .setUnitMaternityScale(employeeWelfareOld.getUnitMaternity())
                .setPersonalPensionScale(employeeWelfareOld.getPersonalPension())
                .setPersonalMedicalScale(employeeWelfareOld.getPersonalMedical())
                .setPersonalUnemploymentScale(employeeWelfareOld.getPersonalUnemployment())
                .setUnitAccumulationFundScale(employeeWelfareOld.getUnitScale())
                .setPersonalAccumulationFundScale(employeeWelfareOld.getPersonageScale());
            this.hrStaffWelfareRecordRepository.insert(benchmarkRecord);
        }
        query.put("payMonthly", payMonthly);
        HrStaffWelfareRecord hrStaffWelfareRecord = this.hrStaffWelfareRecordService.selectByObject(query);

        BigDecimal unitAccumulationFundOld;
        BigDecimal unitAccumulationFundNew;
        BigDecimal personageAccumulationFundOld;
        BigDecimal personageAccumulationFundNew;

        if (hrStaffWelfareRecord == null) {
            // 单位补差
            unitAccumulationFundOld = CalculateUtils.decimalMultiply(benchmarkRecord.getAccumulationFundCardinalBase(), benchmarkRecord.getUnitAccumulationFundScale(), 2);
            unitAccumulationFundNew = CalculateUtils.decimalMultiply(benchmarkRecord.getAccumulationFundCardinalBase(), hrAccumulationFund.getUnitScale(), 2);
            // 个人补差
            personageAccumulationFundOld = CalculateUtils.decimalMultiply(benchmarkRecord.getAccumulationFundCardinalBase(), benchmarkRecord.getPersonalAccumulationFundScale(), 2);
            personageAccumulationFundNew = CalculateUtils.decimalMultiply(benchmarkRecord.getAccumulationFundCardinalBase(), hrAccumulationFund.getPersonageScale(), 2);
        } else {
            // 单位补差
            unitAccumulationFundOld = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getAccumulationFundCardinalBase(), hrStaffWelfareRecord.getUnitAccumulationFundScale(), 2);
            unitAccumulationFundNew = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getAccumulationFundCardinalBase(), hrAccumulationFund.getUnitScale(), 2);
            // 个人补差
            personageAccumulationFundOld = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getAccumulationFundCardinalBase(), hrStaffWelfareRecord.getPersonalAccumulationFundScale(), 2);
            personageAccumulationFundNew = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getAccumulationFundCardinalBase(), hrAccumulationFund.getPersonageScale(), 2);
        }

        // 更新员工每月福利配置
        // this.hrStaffWelfareRecordService.insertOrUpdateStaffWelfareRecord(employeeWelfareOld, hrStaffWelfareRecord, hrAccumulationFund, payYear, payMonthly);

        HrWelfareCompensation hrWelfareCompensation = new HrWelfareCompensation();
        hrWelfareCompensation.setUnitAccumulationFund(CalculateUtils.decimalSubtraction(unitAccumulationFundNew, unitAccumulationFundOld));
        hrWelfareCompensation.setPersonalAccumulationFund(CalculateUtils.decimalSubtraction(personageAccumulationFundNew, personageAccumulationFundOld));
        hrWelfareCompensation.setAccumulationFundTotal(CalculateUtils.decimalAddition(hrWelfareCompensation.getUnitAccumulationFund(), hrWelfareCompensation.getPersonalAccumulationFund()));

        // 基数比例记录
        hrWelfareCompensation.setIdNo(employeeWelfareOld.getCertificateNum());
        hrWelfareCompensation.setStaffId(employeeWelfareOld.getId());
        hrWelfareCompensation.setClientId(employeeWelfareOld.getClientId());
        hrWelfareCompensation.setPayYear(payYear);
        hrWelfareCompensation.setPayMonthly(payMonthly);

        hrWelfareCompensation.setIsUsed(-1);
        hrWelfareCompensation.setType(0);
        hrWelfareCompensation.setRecordId(recordId);

        return hrWelfareCompensation;
    }

    /**
     * 检查公积金比例是否发生改变
     *
     * @param oldDate 旧数据
     * @param newDate 新数据
     * @return boolean
     * <AUTHOR>
     * @date 2021/10/31
     **/
    private HrWelfareCompensationRecord checkScaleIsChange(HrAccumulationFund oldDate, HrAccumulationFund newDate) {
        StringBuilder sb = new StringBuilder();
        boolean result = false;
        if (!BigDecimalCompare.of(oldDate.getUnitScale()).eq(newDate.getUnitScale())) {
            sb.append("单位公积金比例：修改前").append(oldDate.getUnitScale()).append("，").append("修改后").append(newDate.getUnitScale()).append("。");
            result = true;
        }
        if (!BigDecimalCompare.of(oldDate.getPersonageScale()).eq(newDate.getPersonageScale())) {
            result = true;
        }

        if (result) {
            return this.hrWelfareCompensationRecordService.addLog(oldDate, newDate, sb.toString());
        }
        return null;
    }

    /**
     * 查询公积金类型管理详情
     *
     * @param id
     * @return
     */
    @Override
    public HrAccumulationFundDTO getHrAccumulationFund(String id) {
        log.info("Get HrAccumulationFund :{}", id);

        HrAccumulationFund hrAccumulationFund = this.hrAccumulationFundRepository.selectById(id);
        return this.hrAccumulationFundMapper.toDto(hrAccumulationFund);
    }

    /**
     * 批量删除公积金类型管理
     *
     * @param ids
     */
    @Override
    public void deleteHrAccumulationFund(List<String> ids) {
        log.info("Delete HrAccumulationFunds:{}", ids);
        // 操作日志
        //this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.ACCUMULATION_FUND_TYPE.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
        List<HrAccumulationFund> hrAccumulationFunds = hrAccumulationFundRepository.selectBatchIds(ids);
        this.hrAccumulationFundRepository.deleteBatchIds(ids);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> collect = hrAccumulationFunds.stream().map(HrAccumulationFund::getTypeName).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.ACCUMULATION_FUND_TYPE.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除公积金: "+JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(hrAccumulationFunds),
            jwtUserDTO
        );
    }

    /**
     * 分页查询公积金类型管理
     *
     * @param hrAccumulationFundDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrAccumulationFundDTO hrAccumulationFundDTO, Long pageNumber, Long pageSize) {
        Page<HrAccumulationFund> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrAccumulationFund> qw = new QueryWrapper<>();
        qw.like(StringUtils.isNotBlank(hrAccumulationFundDTO.getTypeName()), "type_name", hrAccumulationFundDTO.getTypeName());
        //排序
        if (StringUtils.isNotBlank(hrAccumulationFundDTO.getOrder()))
        {
            if (hrAccumulationFundDTO.getOrder().equals("DESC"))
            {
                qw.orderBy(StringUtils.isNotBlank(hrAccumulationFundDTO.getField()),false,hrAccumulationFundDTO.getField());
            }
            else {
                qw.orderBy(StringUtils.isNotBlank(hrAccumulationFundDTO.getField()),true,hrAccumulationFundDTO.getField());
            }

        }

        IPage iPage = this.hrAccumulationFundRepository.selectPage(page, qw);
        List<HrAccumulationFundDTO> list = this.hrAccumulationFundMapper.toDto(iPage.getRecords());
        //查询该公积金类型下所有客户
        for (HrAccumulationFundDTO accumulationFundDTO : list) {
            QueryWrapper<HrClient> hrClientQueryWrapper = new QueryWrapper<>();
            hrClientQueryWrapper.eq("provident_fund_type_id", accumulationFundDTO.getId());
            List<HrClient> clientList = hrClientService.list(hrClientQueryWrapper);
            accumulationFundDTO.setClientNumber(clientList.size());
            accumulationFundDTO.setHrClientList(clientList);
            if (accumulationFundDTO.getPersonageScale() != null) {
                BigDecimal personageScale = extracted(0, accumulationFundDTO.getPersonageScale());
                accumulationFundDTO.setPersonageScale(personageScale);
            }

            if (accumulationFundDTO.getUnitScale() != null) {
                BigDecimal unitScale = extracted(0, accumulationFundDTO.getUnitScale());
                accumulationFundDTO.setUnitScale(unitScale);
            }
        }
        iPage.setRecords(list);
        return iPage;
    }

    @Override
    public IPage findClientPage(HrClientDTO hrClientDTO, Long pageNumber, Long pageSize) {
        Page<HrClient> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrClient> qw = new QueryWrapper<>(this.hrClientMapper.toEntity(hrClientDTO));
//        qw.eq("hc.is_delete","0");
//        qw.eq("hc.status",0);
        //IPage<HrClientDTO> iPage = this.hrAccumulationFundRepository.selectPageClient(page, qw);
        Page iPage = this.hrClientService.page(page, qw);
        List<HrClientDTO> hrClientDTOList = hrClientMapper.toDto(iPage.getRecords());
        //获取数据字典企业性质
        Map<Integer, String> enterprisemap = codeTableService.findCodeTableByInnerName("enterprise");
        //获取数据字典业务类型
        Map<Integer, String> businessmap = codeTableService.findCodeTableByInnerName("businessType");
        //获取当前时间
        Date date = new Date();
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String newdate = format.format(date);
        for (HrClientDTO record : hrClientDTOList) {
            //赋值属性
            record.setEnterpriseNaturekey(enterprisemap.get(record.getEnterpriseNature()));
            record.setEnterpriseNature(record.getEnterpriseNature());
            record.setBusinessType(record.getBusinessType());
            record.setBusinessTypekey(businessmap.get(record.getBusinessType()));
            //查询客户数量
            int psum = this.hrClientRepository.selectclientsum(record.getId());
            record.setPeoplesum(psum);
            //创建查询条件
            HrClientDTO selectHrClientDTO = new HrClientDTO();
            selectHrClientDTO.setId(record.getId());
            //查询最新协议
            HrProtocolDTO hrProtocolDTO = this.hrClientRepository.selectprotocol(selectHrClientDTO);
            if (hrProtocolDTO != null) {
                String endTime = String.valueOf(hrProtocolDTO.getAgreementEndDate());
                String startTime = String.valueOf(hrProtocolDTO.getAgreementStartDate());

                if (endTime.compareTo(newdate) < 0) {
                    //过期客户
                    record.setCustomerType("过期客户");
                }
                if (endTime.compareTo(newdate) > 0 && hrProtocolDTO.getStates().equals(3)) {
                    //异常客户
                    record.setCustomerType("异常客户");
                }
                if (endTime != null && startTime != null && endTime.compareTo(newdate) >= 0 && startTime.compareTo(newdate) <= 0) {
                    //正常客户
                    record.setCustomerType("正常客户");
                }
                if (startTime.compareTo(newdate) > 0) {
                    //待期客户
                    record.setCustomerType("待期客户");
                }
                List<HrProtocolDTO> poId = this.hrClientRepository.selectprotocolId(record.getId());
                List<HrProtocolDTO>HrProtocols= poId.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getAgreementNumber()))), ArrayList::new));
                if (HrProtocols.size() > 1) {
                    //多协议客户
                    record.setCustomerType("多协议客户");
                }
            } else {
                //潜在客户
                record.setCustomerType("潜在客户");
            }
        }
        iPage.setRecords(hrClientDTOList);
        return iPage;
    }

    /**
     * 导出excel
     * @param accumulationFundDTO
     * @return
     */
    @Override
    public String exportAccumulation(HrAccumulationFundDTO accumulationFundDTO) {
        QueryWrapper<HrAccumulationFund> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(accumulationFundDTO.getTypeName()), "type_name", accumulationFundDTO.getTypeName());
        wrapper.in(CollectionUtils.isNotEmpty(accumulationFundDTO.getIds()),"id",accumulationFundDTO.getIds());
        wrapper.eq("is_delete",0);
        List<HrAccumulationFund> list = this.hrAccumulationFundRepository.selectList(wrapper);
        //重新给模板赋值
        ArrayList<HrAccumulationFundTemplate> exportList = new ArrayList<>();
        for (HrAccumulationFund hrAccumulationFund : list) {
            HrAccumulationFundTemplate hrAccumulationFundTemplate = new HrAccumulationFundTemplate();
            BeanUtils.copyProperties(hrAccumulationFund,hrAccumulationFundTemplate);
            exportList.add(hrAccumulationFundTemplate);
        }
        if (list.isEmpty()) {
            throw new CommonException("未查到数据");
        }
         //List<HrAccumulationFundDTO> hrAccumulationFundDTOS = hrAccumulationFundMapper.toDto(list);
        //重新赋值小数
        for (HrAccumulationFundTemplate hrAccumulationFundDTO : exportList) {
            String personageScaleExcel = extracted(0, hrAccumulationFundDTO.getPersonageScale()).toString();
            hrAccumulationFundDTO.setPersonageScaleExcel(personageScaleExcel + "%");
            String UnitScaleExcel = extracted(0, hrAccumulationFundDTO.getUnitScale()).toString();
            hrAccumulationFundDTO.setUnitScaleExcel(UnitScaleExcel + "%");
        }
        int listSize = list.size();
        List<String> ids = list.stream().map(HrAccumulationFund::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(exportList, "公积金类型管理", HrAccumulationFundTemplate.class);
        // 操作日志
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.ACCUMULATION_FUND_TYPE.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 处理小数
     *
     * @param type 0 乘法 1除法
     * @param personageScaleExcel
     * @return
     */
    private BigDecimal extracted(int type, BigDecimal personageScaleExcel) {
        BigDecimal bignum = new BigDecimal("100");
        BigDecimal multiply = null;
        if (type == 0) {
            multiply = personageScaleExcel.multiply(bignum).setScale(2, BigDecimal.ROUND_DOWN);
        } else {
            multiply = personageScaleExcel.divide(bignum, 4, BigDecimal.ROUND_DOWN);
        }
        return multiply;
    }

    /**
     * 导入excel
     * @param file
     * @return
     */
    @Override
    public String importAccumulation(MultipartFile file) {
        // 设置进度条
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.ACCUMULATION_FOUND.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.accumulationFoundComponent.accumulationImport(inputStream, redisKey, jwtUserDTO);
        return redisKey;

//        ExcelImportResult<HrAccumulationFundImport> result =
//            ExcelUtils.importExcel(file, 0, 1, true, HrAccumulationFundImport.class);
////        int size = result.getList().size();
////        if (size == 0 || size > 1000) {
////            throw new CommonException("最少导入一条数据，最多导入1000条数据");
////        }
//        //进度条
//        String key = jwtUserDTO.getId() + "Talent" + redisKey;
//        redisCache.setCacheObject(key, 0, 10, TimeUnit.MINUTES);
//        try {
//            //导入数据
//            this.saveData(result, key);
//        } catch (Exception e) {
//            redisCache.deleteObject(key);
//            throw new CommonException(e.getMessage());
//        }
//        ImportResultDTO resultDTO;
//        try {
//            resultDTO = ImportResultUtils.writeErrorFile("公积金类型管理" + System.currentTimeMillis(), HrAccumulationFundImport.class, result, fileTempPath);
//            // 判断是否需要上传错误文件
//            if (resultDTO.getFailureFileUrl() != null) {
//                String fileUrl = this.hrAppendixService.uploadErrorImportFile(resultDTO.getFailureFileUrl());
//                resultDTO.setFailureFileUrl(fileUrl);
//            }
//        } catch (IOException e) {
//            log.error("公积金类型管理导入异常:{}", e.getMessage());
//            return ResponseUtil.buildError(e.getMessage());
//        } finally {
//            redisCache.deleteObject(key);
//        }
//        // 操作日志
//        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.ACCUMULATION_FUND_TYPE.getValue(), BusinessTypeEnum.IMPORT.getKey(), file, JSON.toJSONString(resultDTO), ImportResultDTO.class);
//        return ResponseUtil.buildSuccess(resultDTO);
    }

    /**
     * 下载模板
     * @param response
     */
    @Override
    public String importAccumulationTemplate(HttpServletResponse response) {
//        ExportParams params = new ExportParams(null, "公积金类型");
//        //params.setStyle(ExcelStyleUtils.class);
//        params.setType(ExcelType.XSSF);
//        List<HrAccumulationFundTemplate> list = new ArrayList<>();
//        HrAccumulationFundTemplate hrAccumulationFundTemplate = new HrAccumulationFundTemplate();
//        hrAccumulationFundTemplate.setArea("青岛");
//        hrAccumulationFundTemplate.setPayeeAccount("623xxx");
//        hrAccumulationFundTemplate.setPayeeBank("中国建设银行");
//        hrAccumulationFundTemplate.setPayeeName("xx单位");
//        hrAccumulationFundTemplate.setTypeName("xx类型");
//        hrAccumulationFundTemplate.setPersonageScaleExcel("12%");
//        hrAccumulationFundTemplate.setUnitScaleExcel("12%");
//        list.add(hrAccumulationFundTemplate);
//        ZipSecureFile.setMinInflateRatio(-1.0d);
//        Workbook workbook = ExcelExportUtil.exportExcel(params, HrAccumulationFundTemplate.class, list);
////        String[] type={"公积金缴纳账户","社保缴纳账户","工资发放账户","医保缴纳账户","其他"};
////        ExcelUtils.selectList(workbook,0,0,type,"隐藏",1,true);
//        //中国建设银行、青岛银行、中国农业银行、青岛农商银行、中国工商银行、招商银行、交通银行、中国银行、中国民生银行、浦发银行、中国光大银行、中国邮政储蓄银行、中信银行、广发银行、平安银行、华夏银行、渤海银行、恒丰银行、兴业银行、浙商银行、其他
//        String[] bank = {"中国建设银行", "青岛银行", "中国农业银行", "招商银行", "青岛农商银行", "中国工商银行", "招商银行", "交通银行", "中国银行", "中国民生银行", "浦发银行", "中国邮政储蓄银行", "中信银行", "广发银行", "平安银行", "华夏银行", "渤海银行", "恒丰银行", "兴业银行", "浙商银行", "其他"};
//        ExcelUtils.selectList(workbook, 4, 4, bank, "隐藏", 1, true);
//
//        ExcelUtils.downLoadExcel("公积金类型导入模板.xlsx", response, workbook);
        return excelPrefix + "公积金类型导入模板.xlsx";
    }

    private void saveData(ExcelImportResult<HrAccumulationFundImport> result, String key) {
        //long l = System.currentTimeMillis();
        int listSize = result.getList().size();
        //Map<String, Integer> map = new HashMap<>(result.getList().size());
        int scale = 0;
        //List<HrAccumulationFundDTO> list = result.getList();

        for (HrAccumulationFundImport hrAccumulationFundDTO : result.getList()) {
            //单独处理百分号
            HrAccumulationFund hrAccumulationFund = new HrAccumulationFund();
            try {
                List<HrAccumulationFund> list = getHrAccumulationFunds(hrAccumulationFundDTO.getTypeName());
                if (CollectionUtils.isNotEmpty(list)){
                    throw new CommonException("公积金类型名称不可以重复！");
                }
                Map<String, Integer> ownedBank = codeTableService.findCodeTableByInnerValue("ownedBank");
                if (StringUtils.isNotBlank(hrAccumulationFundDTO.getPayeeBank())){
                    Integer ownedBankValue = ownedBank.get(hrAccumulationFundDTO.getPayeeBank());
                    if (ownedBankValue==null){
                        throw new CommonException("银行在系统中不存在!");
                    }
                }
                hrAccumulationFund = getHrAccumulationFund(hrAccumulationFundDTO);
                save(hrAccumulationFund);
            } catch (Exception e) {
                log.error("保存公积金类型异常:{}", e.getMessage());
                hrAccumulationFundDTO.setErrorMsg(e.getMessage());
            } finally {
                scale++;
                int i = CalculateUtils.calculationProgress(scale, listSize);
                redisCache.setCacheObject(key, i, 10, TimeUnit.MINUTES);
            }

        }

    }

    /**
     * 将百分号转化成小数或验证小数
     *
     * @param hrAccumulationFundDTO
     * @return
     */
    private HrAccumulationFund getHrAccumulationFund(HrAccumulationFundImport hrAccumulationFundDTO) {
        String personageScaleExcel = hrAccumulationFundDTO.getPersonageScaleExcel();

        BigDecimal personageScale =null;
        //如果包含% 并去掉 在转化成小数
        if (personageScaleExcel.contains("%")) {
            personageScaleExcel = personageScaleExcel.replace("%", "").replaceAll(" ", "");
            BigDecimal bigDecimal = null;
            try {
                bigDecimal = new BigDecimal(personageScaleExcel);
            } catch (Exception e) {
                throw new CommonException("个人比例数据格式不正确");
            }
             personageScale = extracted(1, bigDecimal);
        }
        //如果不包含%判断是否小于1
        else {
            try {
                personageScale  = new BigDecimal(personageScaleExcel);
            } catch (Exception e) {
                throw new CommonException("个人比例数据格式不正确");
            }
            BigDecimal bigDecimal = new BigDecimal("1.00");
            if (personageScale.compareTo(bigDecimal) == 1) {
                throw new CommonException("个人比例数据格式不正确");
            }
        }

        BigDecimal unitScale = null;
        String unitScaleExcel = hrAccumulationFundDTO.getUnitScaleExcel();
        if (unitScaleExcel.contains("%"))
        {
            unitScaleExcel = unitScaleExcel.replace("%", "").replaceAll(" ", "");

            try {
                unitScale = new BigDecimal(unitScaleExcel);
            } catch (Exception e) {
                throw new CommonException("单位比例数据格式不正确");
            }
            unitScale = extracted(1, unitScale);
        }
        else {
            try {
                unitScale  = new BigDecimal(unitScaleExcel);
            } catch (Exception e) {
                throw new CommonException("单位比例数据格式不正确");
            }
            BigDecimal bigDecimal = new BigDecimal("1.00");
            if (unitScale.compareTo(bigDecimal) == 1) {
                throw new CommonException("单位比例数据格式不正确");
            }
        }

        //HrAccumulationFund hrAccumulationFund = hrAccumulationFundMapper.toEntity(hrAccumulationFundDTO);
        HrAccumulationFund hrAccumulationFund = new HrAccumulationFund();
        BeanUtils.copyProperties(hrAccumulationFundDTO,hrAccumulationFund);
        hrAccumulationFund.setPersonageScale(personageScale);
        hrAccumulationFund.setUnitScale(unitScale);
        return hrAccumulationFund;
    }
}
