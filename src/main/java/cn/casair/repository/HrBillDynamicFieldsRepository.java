package cn.casair.repository;

import cn.casair.domain.HrBillDynamicFields;
import cn.casair.dto.HrBillDynamicFieldsDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 薪酬账单动态字段数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Repository
public interface HrBillDynamicFieldsRepository extends BaseMapper<HrBillDynamicFields> {

    @Select("select * from hr_bill_dynamic_fields bf where bf.bill_id = #{billId} and bf.is_delete = 0 order by created_date desc limit 1")
    HrBillDynamicFieldsDTO getByBillId(String billId);

    @Update("update hr_bill_dynamic_fields bf set bf.is_delete = 1 where bf.bill_id = #{billId}")
    void deleteByBillId(String billId);

    List<HrBillDynamicFieldsDTO> getBatchBillId(@Param("billIds") List<String> billIds);


}
