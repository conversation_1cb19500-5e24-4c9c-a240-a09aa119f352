package cn.casair.service.component.ecloud;

import cn.casair.cache.RedisCache;
import cn.casair.common.enums.ContractEnum;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.PdfUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.domain.HrAppendix;
import cn.casair.dto.HrContractAppendixDTO;
import cn.casair.dto.HrContractDTO;
import cn.casair.dto.formdata.InputLabel;
import cn.casair.dto.formdata.SignRegionDTO;
import cn.casair.mapper.HrContractAppendixMapper;
import cn.casair.repository.HrContractAppendixRepository;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 电签处理相关
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 10:20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ElectronicSignComponent {


    private final RedisCache redisCache;
    private final ECloudComponent eCloudComponent;
    private final HrContractAppendixMapper hrContractAppendixMapper;
    private final HrContractAppendixRepository hrContractAppendixRepository;


    /**
     * 合同审核-通过
     *
     * @param hrContractAppendixDTOList
     */
    public void handleContractAuditPass(List<HrContractAppendixDTO> hrContractAppendixDTOList) {
        hrContractAppendixDTOList.forEach(this::dealPassContractAppendix);
    }

    /**
     * 合同审核-作废
     *
     * @param hrContractDTO
     * @param hrContractAppendixDTOList
     */
    public void handleContractAuditToVoid(HrContractDTO hrContractDTO, List<HrContractAppendixDTO> hrContractAppendixDTOList) {
        if (StringUtils.isBlank(hrContractDTO.getCheckMsg())) {
            throw new CommonException("请填写审核信息！");
        }
        // 合同作废
        hrContractAppendixDTOList.forEach(ls -> {
            try {
                if (StringUtils.isNotBlank(ls.getContractNum())) {
                    this.eCloudComponent.contractCancel(ls.getContractNum());
                }
            } catch (Exception e) {
                log.error("合同作废异常：{}", e.getMessage());
            }
            this.hrContractAppendixRepository.deleteById(ls.getId());
        });
    }

    /**
     * 合同审核-通知员工修改
     *
     * @param hrContractDTO
     * @param hrContractAppendixDTOList
     */
    public void handleContractAuditFix(HrContractDTO hrContractDTO, List<HrContractAppendixDTO> hrContractAppendixDTOList) {
        if (StringUtils.isBlank(hrContractDTO.getCheckMsg())) {
            throw new CommonException("请填写审核信息！");
        }
        hrContractAppendixDTOList.forEach(ls -> {
            // 获取未通过合同附件 重新生成电签合同
            if (ContractEnum.ExamineState.FAIL.getKey().equals(ls.getState())) {
                if (ContractEnum.AppendixType.TEMPLATE_CONTRACT.getKey().equals(ls.getType())) {
                    // 作废合同
                    try {
                        if (StringUtils.isNotBlank(ls.getContractNum())) {
                            this.eCloudComponent.contractCancel(ls.getContractNum());
                        }
                    } catch (Exception exception) {
                        log.info("作废合同异常：{}", exception.getMessage());
                    }
                    // 重新制作合同信息
                    String contractNo = RandomUtil.generateContractNo();
                    ls.setContractNum(contractNo);
                    ls.setAppendixId(null);
                    ls.setAppendixPath(ls.getOriginalPath());
                    List<InputLabel> inputLabelList = JSON.parseArray(ls.getFirstPartInfo(), InputLabel.class);
                    for (InputLabel inputLabel : inputLabelList) {
                        if (ContractEnum.FormField.contractNo.getKey().equals(inputLabel.getName())) {
                            inputLabel.setValue(contractNo);
                            break;
                        }
                    }
                    ls.setFirstPartInfo(JSON.toJSONString(inputLabelList));
                }
                // 将状态改为重签
                ls.setState(ContractEnum.ExamineState.COUNTERSIGN.getKey());
                this.hrContractAppendixRepository.updateById(this.hrContractAppendixMapper.toEntity(ls));
            } else {
                // 通过附件为劳动合同时 需要放置甲方签章
                this.dealPassContractAppendix(ls);
            }
        });
    }

    /**
     * 处理审核通过合同附件
     *
     * @param hrContractAppendixDTO
     */
    private void dealPassContractAppendix(HrContractAppendixDTO hrContractAppendixDTO) {
        if (ContractEnum.AppendixType.TEMPLATE_CONTRACT.getKey().equals(hrContractAppendixDTO.getType()) && ContractEnum.TemplateType.LABOR_CONTRACT.getKey().equals(hrContractAppendixDTO.getTemplateType())) {
            if (hrContractAppendixDTO.getSealSignId() == null) {
                throw new CommonException("请选择甲方签章！");
            }
            this.setFistPartSign(hrContractAppendixDTO);
        }
        hrContractAppendixDTO.setState(ContractEnum.ExamineState.PASS.getKey());
        this.hrContractAppendixRepository.updateById(this.hrContractAppendixMapper.toEntity(hrContractAppendixDTO));
    }

    /**
     * 签署甲方签名
     *
     * @param hrContractAppendixDTO
     */
    private void setFistPartSign(HrContractAppendixDTO hrContractAppendixDTO) {
        SignRegionDTO signRegionDTO;
        String redisKey = RedisKeyEnum.currencyKey.PDF_SIGN_REGION.getValue() + hrContractAppendixDTO.getTemplateId() + ":" + ContractEnum.FormField.firstPartSign.getKey();
        if (this.redisCache.hasKey(redisKey)) {
            signRegionDTO = JSONObject.parseObject(this.redisCache.getCacheObject(redisKey), SignRegionDTO.class);
        } else {
            signRegionDTO = PdfUtils.getPdfSignRegion(hrContractAppendixDTO.getOriginalPath(), ContractEnum.FormField.firstPartSign.getKey());
            this.redisCache.setCacheObject(redisKey, JSON.toJSONString(signRegionDTO));
        }
        if (signRegionDTO == null) {
            throw new CommonException("未检测到合同模板甲方签名区域!");
        }
        // 合同是否已经完成下载(上一次审核通知修改时, 此合同已通过无需再次处理)
        boolean isFinish = false;
        try {
            this.eCloudComponent.firstPartSealToContract(hrContractAppendixDTO.getContractNum(), hrContractAppendixDTO.getSealSignId(), signRegionDTO);
        } catch (Exception e) {
            log.error("甲方签章到合同异常:{}", e.getMessage());
            if (!"易云章错误信息：合同已完成,不允许签署".equals(e.getMessage())) {
                throw new CommonException("甲方签章到合同异常" + e.getMessage());
            }
            isFinish = true;
        }
        if (!isFinish) {
            HrAppendix contract = this.eCloudComponent.downloadContractFile(hrContractAppendixDTO.getContractNum());
            hrContractAppendixDTO.setAppendixId(contract.getId());
            hrContractAppendixDTO.setAppendixPath(contract.getFileUrl());
        }
    }
}
