package cn.casair.repository;

import cn.casair.domain.HrOriginalSalary;
import cn.casair.dto.HrOriginalSalaryDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 薪酬原单（客户）数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Repository
public interface HrOriginalSalaryRepository extends BaseMapper<HrOriginalSalary> {


    IPage<HrOriginalSalaryDTO> selectFiPage(Page<HrOriginalSalary> page, @Param("param1") HrOriginalSalaryDTO hrOriginalSalaryDTO);


    List<String> selectid(String id);

    /**
     * 不分页查询薪酬原单
     * @param hrOriginalSalaryDTO
     * @return
     */
    List<HrOriginalSalaryDTO> findList(@Param("param1") HrOriginalSalaryDTO hrOriginalSalaryDTO);

}
