package cn.casair.service;
import cn.casair.domain.HrBillReimbursementApplyDetail;
import cn.casair.domain.HrClient;
import cn.casair.dto.HrBillReimbursementApplyDTO;
import cn.casair.dto.HrBillReimbursementApplyDetailDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 报销申请费用明细服务类
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
public interface HrBillReimbursementApplyDetailService extends IService<HrBillReimbursementApplyDetail> {


    /**
     * 创建报销申请费用明细
     * @param hrBillReimbursementApplyDetailDTO
     * @return
     */
    HrBillReimbursementApplyDetailDTO createHrBillReimbursementApplyDetail(HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO);

    /**
     * 修改报销申请费用明细
     * @param hrBillReimbursementApplyDetailDTO
     * @return
     */
    Optional<HrBillReimbursementApplyDetailDTO> updateHrBillReimbursementApplyDetail(HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO);

    /**
     * 查询报销申请费用明细详情
     * @param id
     * @return
     */
    HrBillReimbursementApplyDetailDTO getHrBillReimbursementApplyDetail(String id);

    /**
     * 删除报销申请费用明细
     * @param id
     */
    void deleteHrBillReimbursementApplyDetail(String id);

    /**
     * 批量删除报销申请费用明细
     * @param ids
     */
    void deleteHrBillReimbursementApplyDetail(List<String> ids);

    /**
     * 分页查询报销申请费用明细
     * @param hrBillReimbursementApplyDetailDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO,Long pageNumber,Long pageSize);

    /**
     * 异步处理审核时上级报销数据
     * @param applyDTO
     * @param type 审核类型
     */
    void handleParentInvoiceData(HrBillReimbursementApplyDTO applyDTO, Integer type);

    /**
     * 特殊客户处理明细账单对应锁定状态
     * @param detailId 报销明细ID
     * @param invoiceType 报销项类型
     * @param lockState 锁定状态
     * @param rootParentClient 顶级客户信息
     */
    void updateDetailClientLock(String detailId, Integer invoiceType, Integer lockState, HrClient rootParentClient);

}
