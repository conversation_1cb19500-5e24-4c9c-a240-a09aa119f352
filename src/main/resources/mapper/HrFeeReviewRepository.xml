<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrFeeReviewRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , client_id, bill_id, status, pay_year, pay_monthly, title, appendix_id, remark,cancel_reason,cancel_remark,is_draw_bill,payment_date,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrFeeReview">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="bill_id" property="billId"/>
        <result column="status" property="status"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="title" property="title"/>
        <result column="appendix_id" property="appendixId"/>
        <result column="remark" property="remark"/>
        <result column="cancel_reason" property="cancelReason"/>
        <result column="cancel_remark" property="cancelRemark"/>
        <result column="is_draw_bill" property="isDrawBill"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByClientIdAndBillId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM hr_fee_review
        WHERE is_delete = 0
          AND client_id = #{clientId}
          AND FIND_IN_SET(#{billId}, bill_id)
    </select>

    <select id="selectChlientId" resultType="java.lang.String">
        SELECT t3.id
        FROM (
                 SELECT t1.*,
                        t2.*,
                        IF(FIND_IN_SET(parent_id, @pids) > 0, @pids := CONCAT(@pids, ',', id), '0') AS isChild
                 FROM (
                          SELECT *
                          FROM hr_client
                          WHERE is_delete = 0
                          ORDER BY parent_id, id
                          ) AS t1,
                      (SELECT @pids := #{id}) AS t2
                 ) t3
        WHERE t3.isChild != '0'
    </select>

    <select id="selectNewestRecordByClientId" resultType="cn.casair.domain.HrFeeReview">
        SELECT
         *
        FROM hr_fee_review
        WHERE
        is_delete = 0
        AND client_id = #{clientId}
        AND `status` = 1
        ORDER BY
        pay_year DESC,
        pay_monthly DESC
        LIMIT 1
    </select>

    <select id="selectClientname" resultType="cn.casair.dto.HrFeeReviewDTO">
        select unit_number as unitNumber, client_name as clientName, hc.id as clientId
        from hr_client hc
        where hc.id = #{id}
          and hc.is_delete = 0
    </select>

    <select id="selectBillDetail" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
        any_value ( hbd.id ) id,
        any_value ( hbd.system_num )systemNum,
        any_value ( hbd.name ) name,
        any_value ( hbd.certificate_num )certificateNum,
        any_value ( hbd.phone )phone,
        any_value ( hbd.bill_id )billId,
        any_value ( hbd.staff_id )staffId,
        any_value ( hbd.pay_year )payYear,
        any_value ( hbd.pay_monthly )payMonthly,
        any_value ( hbd.social_security_area )socialSecurityArea,
        any_value ( hbd.staff_status )staffStatus,
        any_value ( hbd.personnel_type )personnelType,
        any_value ( hbd.iz_insured )izInsured,
        any_value ( hbd.social_security_num )socialSecurityNum,
        any_value ( hbd.social_security_cardinal )socialSecurityCardinal,
        any_value ( hbd.medical_insurance_num )medicalInsuranceNum,
        any_value ( hbd.medical_insurance_cardinal )medicalInsuranceCardinal,
        any_value ( hbd.accumulation_fund_num )accumulationFundNum,
        any_value ( hbd.accumulation_fund_cardinal )accumulationFundCardinal,
        any_value ( hbd.unit_pension )unitPension,
        any_value ( hbd.unit_unemployment )unitUnemployment,
        any_value ( hbd.unit_medical )unitMedical,
        any_value ( hbd.work_injury )workInjury,
        any_value ( hbd.unit_social_security_make_up )unitSocialSecurityMake_up,
        any_value ( hbd.unit_subtotal )unitSubtotal,
        any_value ( hbd.personal_pension )personalPension,
        any_value ( hbd.personal_unemployment )personalUnemployment,
        any_value ( hbd.personal_medical )personalMedical,
        any_value ( hbd.personal_social_security_make_up )personalSocialSecurityMakeUp,
        any_value ( hbd.personal_subtotal )personalSubtotal,
        any_value ( hbd.social_security_total )socialSecurityTotal,
        any_value ( hbd.unit_accumulation_fund_make_up )unitAccumulationFundMakeUp,
        any_value ( hbd.unit_accumulation_fund )unitAccumulationFund,
        any_value ( hbd.personal_accumulation_fund )personalAccumulationFund,
        any_value ( hbd.personal_accumulation_fund_make_up )personalAccumulationFundMakeUp,
        any_value ( hbd.accumulation_fund_total )accumulationFundTotal,
        any_value ( hbd.service_fee )serviceFee,
        any_value ( hbd.total )total,
        any_value ( hbd.bill_used )billUsed,
        any_value ( hbd.reason )reason,
        any_value ( hbd.is_used )isUsed,
        any_value ( hbd.salary )salary,
        any_value ( hbd.pre_tax_salary )preTaxSalary,
        any_value ( hbd.personal_tax )personalTax,
        any_value ( hbd.real_salary )realSalary,
        any_value ( hbd.other_salary )otherSalary,
        hb.client_id
        FROM
        hr_bill_detail hbd
        LEFT JOIN `hr_bill` hb on hb.id=hbd.bill_id
        LEFT JOIN hr_bill_total hbt on hb.id=hbt.bill_id

        where
        <if test="param1.clientIdList!=null and  param1.clientIdList.size() > 0">
            hb.client_id in
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.payYear!=null">
            and hbd.pay_year=#{param1.payYear}
        </if>
        <if test="param1.payMonthly!=null">
            and hbd.pay_monthly=#{param1.payMonthly}
        </if>

        and hb.is_delete=0 AND hbd.is_delete=0 and is_used=1 and hb.is_official=1 GROUP BY hbd.id
        ORDER BY
        hbd.sort_value, hbd.created_date DESC
    </select>

    <select id="selectBillTotal" resultType="cn.casair.dto.HrBillTotalDTO">
        SELECT

        sum(hbt.staff_num) AS staffNum,
        sum(hbt. social_security_cardinal ) AS socialSecurityCardinal,
        sum(hbt. medical_insurance_cardinal ) AS medicalInsuranceCardinal,
        sum(hbt. accumulation_fund_cardinal ) AS accumulationFundCardinal,
        sum(hbt. unit_pension_total ) AS unitPensionTotal,
        sum(hbt. unit_medical_total ) AS unitMedicalTotal,
        sum(hbt. unit_unemployment_total ) AS unitUnemploymentTotal,
        sum(hbt. work_injury_total ) AS workInjuryTotal,
        sum(hbt. unit_subtotal ) AS unitSubtotal,
        sum(hbt. personal_pension_total ) AS personalPensionTotal,
        sum(hbt. personal_medical_total ) AS personalMedicalTotal,
        sum(hbt. personal_unemployment_total ) AS personalUnemploymentTotal,
        sum(hbt. personal_subtotal ) AS personalSubtotal,
        sum(hbt. unit_social_security_make_up_total ) AS unitSocialSecurityMake_upTotal,
        sum(hbt. personal_social_security_make_up_total ) AS personalSocialSecurityMakeUpTotal ,
        sum(hbt. social_security_total ) AS socialSecurityTotal,
        sum(hbt. unit_accumulation_fund_total ) AS unitAccumulationFundTotal,
        sum(hbt. personal_accumulation_fund_total ) AS personalAccumulationFundTotal,
        sum(hbt. unit_accumulation_fund_make_up_total ) AS unitAccumulationFundMakeUpTotal,
        sum(hbt. personal_accumulation_fund_make_up_total ) AS personalAccumulationFundMakeUpTotal,
        sum(hbt. accumulation_fund_total ) AS accumulationFundTotal,
        sum(hbt. service_fee_total ) AS serviceFeeTotal,
        sum(hbt. accumulation_fund_total ) AS accumulationFundTotal,
        sum(hbt. last_month_make_up ) AS lastMonthMakeUp,
        sum(hbt. total) total,
        sum(hbt. real_salary_total ) AS realSalaryTotal,
        sum(hbt. personal_tax_total ) AS personalTaxTotal,
        sum(hbt. other_fee_total ) AS otherFeeTotal
        FROM hr_bill hb
        LEFT JOIN hr_bill_total hbt on hb.id = hbt.bill_id
        where
        <if test="param1.clientIdList!=null and param1.clientIdList.size() > 0">
            hb.client_id in
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.payYear!=null">
            and hbt.pay_year=#{param1.payYear}
        </if>
        <if test="param1.payMonthly!=null">
            and hbt.pay_monthly=#{param1.payMonthly}
        </if>
        and hb.is_delete = 0 and hbt.is_delete=0 and hb.is_official=1
    </select>

    <select id="selectBill" resultType="cn.casair.dto.HrBillDTO">
        SELECT * FROM hr_bill where
        <if test="param1.clientIdList!=null and  param1.clientIdList.size() > 0">
            client_id in
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.payYear!=null">
            and pay_year=#{param1.payYear}
        </if>
        <if test="param1.payMonthly!=null">
            and pay_monthly=#{param1.payMonthly}
        </if>
        and is_delete=0 and is_official=1
    </select>

    <update id="updateHrBill">
        UPDATE hr_bill
        set bill_state=#{type}
        where id = #{id}
          and is_delete = 0
    </update>

    <select id="selectClient" resultType="cn.casair.dto.HrFeeReviewDTO">
        SELECT hc.unit_number as unitNumber,
               hc.client_name AS clientName,
               hf.*
        FROM hr_fee_review hf
                 LEFT JOIN hr_client hc on hf.client_id = hc.id
        WHERE hf.id = #{id}
          and hf.is_delete = 0
    </select>

    <select id="selectBillId" resultType="cn.casair.dto.HrBillDTO">
        SELECT *
        FROM hr_bill
        where
        <if test="id!=null and id.size() > 0">
            id in
            <foreach collection=" id" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        and is_delete = 0
        and is_official = 1
    </select>

    <select id="selectPages" resultType="cn.casair.dto.HrFeeReviewDTO">
        SELECT
            any_value ( hfr.id )id,
            any_value ( hfr.client_id )clientId,
            any_value ( hfr.bill_id )billId,
            any_value ( hfr.`status` )status,
            any_value ( hfr.pay_year )payYear,
            any_value ( hfr.pay_monthly )payMonthly,
            any_value ( hfr.title )title,
            any_value ( hfr.appendix_id )appendixId,
            any_value ( hfr.detail_appendix_id )detailAppendixId,
            any_value ( hfr.detail_pdf_appendix_id )detailPdfAppendixId,
            any_value ( hfr.summary_appendix_id )summaryAppendixId,
            any_value ( hfr.refuse_remark )refuseRemark,
            any_value ( hfr.created_date )createdDate,
            any_value ( hfr.client_review_date )clientReviewDate,
            any_value ( hfr.remark )remark,
            any_value ( hfr.payment_date ) paymentDate,
            any_value (hbt.total) total,
            any_value ( hc.client_name ) AS clientName,
            any_value ( su.real_name) AS realName,
            any_value ( su.id) AS realNameId
        FROM
            hr_fee_review hfr
        LEFT JOIN hr_bill_total hbt ON hfr.id = hbt.bill_id
        LEFT JOIN hr_client hc ON hfr.client_id = hc.id
        LEFT JOIN sys_user su ON hc.specialized_id = su.id
        WHERE hfr.is_delete = 0
        <if test="param1.clientIdList!=null and param1. clientIdList.size() > 0">
            and hfr.client_id in
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.payYear!=null">
            and hfr.pay_year=#{param1.payYear}
        </if>
        <if test="param1.payMonthly!=null">
            and hfr.pay_monthly=#{param1.payMonthly}
        </if>
<!--        <if test="param1.feeReviewDate != null and param1.feeReviewDate != ''">-->
<!--            and  FIND_IN_SET(#{param1.feeReviewDate},payment_date)-->
<!--        </if>-->
        <if test="param1.statusList!=null and param1. statusList.size() > 0">
            and hfr.status in
            <foreach collection="param1.statusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.realNameIdList!=null and param1. realNameIdList.size() > 0">
            and su.id in
            <foreach collection="param1.realNameIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY hfr.id
        <if test="param1.field == null  and param1.order == null ">
            ORDER BY hfr.created_date DESC
        </if>
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </select>

    <select id="selectChlient" resultType="cn.casair.dto.HrClientsDTO">
        SELECT id, client_name ,parent_id,unit_number from hr_client where is_delete=0
        <if test="clientIdList!=null and  clientIdList.size() > 0">
            and id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>

    <select id="selectBillTotals" resultType="cn.casair.dto.HrBillTotalDTO">
        SELECT
        any_value(hbt.staff_num) staffNum,
        sum(hbt. social_security_cardinal ) AS socialSecurityCardinal,
        sum(hbt. medical_insurance_cardinal ) AS medicalInsuranceCardinal,
        sum(hbt. accumulation_fund_cardinal ) AS accumulationFundCardinal,
        sum(hbt. unit_pension_total ) AS unitPensionTotal,
        sum(hbt. unit_medical_total ) AS unitMedicalTotal,
        sum(hbt. unit_unemployment_total ) AS unitUnemploymentTotal,
        sum(hbt. work_injury_total ) AS workInjuryTotal,
        sum(hbt. unit_subtotal ) AS unitSubtotal,
        sum(hbt. personal_pension_total ) AS personalPensionTotal,
        sum(hbt. personal_medical_total ) AS personalMedicalTotal,
        sum(hbt. personal_unemployment_total ) AS personalUnemploymentTotal,
        sum(hbt. personal_subtotal ) AS personalSubtotal,
        sum(hbt. unit_social_security_make_up_total ) AS unitSocialSecurityMake_upTotal,
        sum(hbt. personal_social_security_make_up_total ) AS personalSocialSecurityMakeUpTotal ,
        sum(hbt. social_security_total ) AS socialSecurityTotal,
        sum(hbt. unit_accumulation_fund_total ) AS unitAccumulationFundTotal,
        sum(hbt. personal_accumulation_fund_total ) AS personalAccumulationFundTotal,
        sum(hbt. unit_accumulation_fund_make_up_total ) AS unitAccumulationFundMakeUpTotal,
        sum(hbt. personal_accumulation_fund_make_up_total ) AS personalAccumulationFundMakeUpTotal,
        sum(hbt. accumulation_fund_total ) AS accumulationFundTotal,
        sum(hbt. service_fee_total ) AS serviceFeeTotal,
        sum(hbt. accumulation_fund_total ) AS accumulationFundTotal,
        sum(hbt. last_month_make_up ) AS lastMonthMakeUp,
        any_value(hbt. total) total,
        sum(hbt. real_salary_total ) AS realSalaryTotal,
        sum(hbt. personal_tax_total ) AS personalTaxTotal,
        sum(hbt. other_fee_total ) AS otherFeeTotal
        FROM hr_bill hb
        LEFT JOIN hr_bill_total hbt on hb.id = hbt.bill_id
        LEFT JOIN hr_fee_review hfr on FIND_IN_SET(hb.id,hfr.bill_id)
        where
        <if test="id!=null and  id.size() > 0">
            hb.id in
            <foreach collection="id" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        and hb.is_delete = 0

        and hbt.is_delete = 0
        and hb.is_official=1
    </select>
    <select id="selectBillDet" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT *
        from hr_bill_detail where
        <if test="id!=null and  id.size() > 0">
            bill_id in
            <foreach collection="id" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        and is_delete = 0
        and is_used = 1
    </select>


    <update id="updateStatus">
        UPDATE hr_bill
        set bill_state=0
        where
        <if test="id!=null and  id.size() > 0">
            id in
            <foreach collection="id" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        and is_delete = 0
    </update>

    <select id="selectHrBillDetailItems" resultType="cn.casair.dto.HrBillDetailItemsDTO">
        SELECT *
        from hr_bill_detail_items
        where bill_detail_id = #{id}
          and is_delete = 0
    </select>
    <select id="selecttotal" resultType="java.lang.String">
        SELECT
        SUM( total )
        FROM
        hr_bill_total
        WHERE
        <if test="billId!=null and  billId.size() > 0">
            bill_id IN
            <foreach collection="billId" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        AND is_delete =0
    </select>

    <select id="selectChlientIds" resultType="java.lang.String">
        SELECT t3.id
        FROM (
                 SELECT t1.*,
                        t2.*,
                        IF(FIND_IN_SET(parent_id, @pids) > 0, @pids := CONCAT(@pids, ',', id), '0') AS isChild
                 FROM (
                          SELECT *
                          FROM hr_client
                          WHERE is_delete = 0
                          ORDER BY parent_id, id
                          ) AS t1,
                      (SELECT @pids := #{id}) AS t2
                 ) t3
        WHERE t3.isChild != '0'
    </select>

    <select id="selectTile" resultType="cn.casair.dto.HrFeeReviewDTO">
        SELECT *
        FROM hr_fee_review hf
                 LEFT JOIN hr_client hc on hc.id = hf.client_id
        WHERE hf.id = #{id}
          and hf.is_delete = 0
          and hc.is_delete = 0
    </select>
    <select id="selectBillTotalas" resultType="cn.casair.dto.HrBillTotalDTO">
        SELECT

        sum(hbt.staff_num) AS staffNum,
        sum(hbt. social_security_cardinal ) AS socialSecurityCardinal,
        sum(hbt. medical_insurance_cardinal ) AS medicalInsuranceCardinal,
        sum(hbt. accumulation_fund_cardinal ) AS accumulationFundCardinal,
        sum(hbt. unit_pension_total ) AS unitPensionTotal,
        sum(hbt. unit_medical_total ) AS unitMedicalTotal,
        sum(hbt. unit_unemployment_total ) AS unitUnemploymentTotal,
        sum(hbt. work_injury_total ) AS workInjuryTotal,
        sum(hbt. unit_subtotal ) AS unitSubtotal,
        sum(hbt. personal_pension_total ) AS personalPensionTotal,
        sum(hbt. personal_medical_total ) AS personalMedicalTotal,
        sum(hbt. personal_unemployment_total ) AS personalUnemploymentTotal,
        sum(hbt. personal_subtotal ) AS personalSubtotal,
        sum(hbt. unit_social_security_make_up_total ) AS unitSocialSecurityMake_upTotal,
        sum(hbt. personal_social_security_make_up_total ) AS personalSocialSecurityMakeUpTotal ,
        sum(hbt. social_security_total ) AS socialSecurityTotal,
        sum(hbt. unit_accumulation_fund_total ) AS unitAccumulationFundTotal,
        sum(hbt. personal_accumulation_fund_total ) AS personalAccumulationFundTotal,
        sum(hbt. unit_accumulation_fund_make_up_total ) AS unitAccumulationFundMakeUpTotal,
        sum(hbt. personal_accumulation_fund_make_up_total ) AS personalAccumulationFundMakeUpTotal,
        sum(hbt. accumulation_fund_total ) AS accumulationFundTotal,
        sum(hbt. service_fee_total ) AS serviceFeeTotal,
        sum(hbt. accumulation_fund_total ) AS accumulationFundTotal,
        sum(hbt. last_month_make_up ) AS lastMonthMakeUp,
        sum(hbt. total) total,
        sum(hbt. real_salary_total ) AS realSalaryTotal,
        sum(hbt. personal_tax_total ) AS personalTaxTotal,
        sum(hbt. other_fee_total ) AS otherFeeTotal
        FROM hr_bill hb
        LEFT JOIN hr_bill_total hbt on hb.id = hbt.bill_id
        LEFT JOIN hr_fee_review hfr ON
        hfr.pay_year = hb.pay_year
        AND hfr.pay_monthly = hb.pay_monthly
        AND hfr.is_delete = 0
        where
        <if test="param1.clientIdList!=null and  param1.clientIdList.size() > 0">
            hb.client_id in
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        and hfr.id=#{param1.id}
        <if test="param1.payYear!=null">
            and hbt.pay_year=#{param1.payYear}
        </if>
        <if test="param1.payMonthly!=null">
            and hbt.pay_monthly=#{param1.payMonthly}
        </if>
        and hb.is_delete = 0 and hbt.is_delete=0 and hb.is_official=1
    </select>
    <select id="selectBillDetails" resultType="cn.casair.dto.HrBillDetailDTO">
        SELECT
        hbd.*,
        hbdi.id itemId,
        hbdi.expense_name,
        hbdi.expense_type,
        hbdi.amount,
        hb.client_id
        FROM hr_bill_detail hbd
        LEFT JOIN hr_bill_detail_items hbdi ON hbdi.bill_detail_id = hbd.id AND hbdi.is_delete = 0
        LEFT JOIN hr_bill hb on hb.id=hbd.bill_id and hb.is_delete=0

        WHERE
        hbd.is_delete = 0
        <if test="billId!=null and  billId.size() > 0">
            AND hbd.bill_id in
            <foreach collection="billId" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        AND hbd.is_used = 1

        ORDER BY
        hbd.sort_value, hbd.created_date DESC
    </select>

    <select id="findHrFeeReviewById" resultType="cn.casair.dto.HrSettleAccountDTO">
        SELECT
            hc.client_name,
            hc.unit_number,
            hc2.client_name superiorUnit,
            hfr.*
        FROM
            hr_fee_review hfr
        LEFT JOIN hr_client hc ON hfr.client_id = hc.id
        LEFT JOIN hr_client hc2 ON hc.parent_id = hc2.id
        WHERE
            hfr.is_delete = 0 AND hfr.id = #{id}
    </select>

    <select id="getHrBillDetailItems" resultType="cn.casair.dto.HrBillDetailItemsDTO">
        SELECT
            hbdi.id,hbdi.bill_detail_id,hbdi.expense_name,hbdi.expense_type,hbdi.amount
        FROM
            hr_bill_detail_items hbdi
        LEFT JOIN hr_bill_detail hbd ON hbdi.bill_detail_id = hbd.id
        LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
        WHERE hbdi.is_delete = 0 AND hbd.is_delete = 0
        AND hbd.is_used = 1 AND hb.id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

    <select id="selectFeeReview" resultType="cn.casair.dto.HrFeeReviewDTO">
        SELECT
            hc.client_name,
            hfr.*
        FROM
            hr_fee_review hfr
        LEFT JOIN hr_client hc ON hfr.client_id = hc.id
        WHERE hfr.is_delete = 0 AND hfr.`status` = 1
        AND FIND_IN_SET(#{paymentDate},payment_date)
        <if test="ids != null and ids.size() > 0">
            AND hfr.id NOT IN
            <foreach collection="ids" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>

    <select id="selectFeeReviewByClientId" resultType="cn.casair.dto.HrFeeReviewDTO">
        SELECT
            hfr.*
        FROM
            hr_fee_review hfr
        WHERE hfr.is_delete = 0 AND hfr.`status` = 1 AND hfr.client_id = #{clientId}
        ORDER BY hfr.pay_year,hfr.pay_monthly DESC
    </select>

    <select id="findFeeReviewByInvoiceId" resultType="cn.casair.domain.HrFeeReview">
        SELECT
            hfr.*
        FROM
            hr_fee_review hfr
        LEFT JOIN hr_bill_invoice_review hbir ON hfr.id = hbir.fee_review_id
        WHERE
            hfr.is_delete = 0
            AND hbir.is_delete = 0
            AND hbir.invoice_id = #{invoiceId}
    </select>

    <select id="selectReviewConfigByClientId" resultType="cn.casair.domain.HrFeeReviewConfig">
        SELECT
            id,
            client_id,
            fee_review_id,
            dynamic_header,
            is_delete,
            created_by,
            created_date,
            last_modified_by,
            last_modified_date
        FROM
            hr_fee_review_config
        WHERE is_delete = 0
        <if test="clientId != null and clientId != ''">
            AND client_id = #{clientId}
        </if>
        <if test="feeReviewId != null and feeReviewId != ''">
            AND fee_review_id = #{feeReviewId}
        </if>
        ORDER BY created_date DESC
        LIMIT 1
    </select>

</mapper>
