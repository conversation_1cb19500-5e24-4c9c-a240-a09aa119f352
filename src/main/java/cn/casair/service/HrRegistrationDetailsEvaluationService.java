package cn.casair.service;

import cn.casair.domain.HrRegistrationDetailsEvaluation;
import cn.casair.dto.HrRegistrationDetailsEvaluationDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
public interface HrRegistrationDetailsEvaluationService extends IService<HrRegistrationDetailsEvaluation> {


    /**
     * 创建
     * @param hrRegistrationDetailsEvaluationDTO
     * @return
     */
    HrRegistrationDetailsEvaluationDTO createHrRegistrationDetailsEvaluation(HrRegistrationDetailsEvaluationDTO hrRegistrationDetailsEvaluationDTO);

    /**
     * 修改
     * @param hrRegistrationDetailsEvaluationDTO
     * @return
     */
    Optional<HrRegistrationDetailsEvaluationDTO> updateHrRegistrationDetailsEvaluation(HrRegistrationDetailsEvaluationDTO hrRegistrationDetailsEvaluationDTO);

    /**
     * 查询详情
     * @param id
     * @return
     */
    HrRegistrationDetailsEvaluationDTO getHrRegistrationDetailsEvaluation(String id);

    /**
     * 删除
     * @param id
     */
    void deleteHrRegistrationDetailsEvaluation(String id);

    /**
     * 批量删除
     * @param ids
     */
    void deleteHrRegistrationDetailsEvaluation(List<String> ids);

    /**
     * 分页查询
     * @param hrRegistrationDetailsEvaluationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRegistrationDetailsEvaluationDTO hrRegistrationDetailsEvaluationDTO,Long pageNumber,Long pageSize);
    }
