package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.StringUtils;
import cn.casair.domain.HrRemoteMedicalRecord;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.HrApplyOpLogsDTO;
import cn.casair.dto.HrRemoteMedicalRecordDTO;
import cn.casair.dto.JWTMiniDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.mapper.HrRemoteMedicalRecordMapper;
import cn.casair.repository.HrRemoteMedicalRecordRepository;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 异地医疗备案记录服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrRemoteMedicalRecordServiceImpl extends ServiceImpl<HrRemoteMedicalRecordRepository, HrRemoteMedicalRecord> implements HrRemoteMedicalRecordService {

    private final HrRemoteMedicalRecordRepository hrRemoteMedicalRecordRepository;
    private final HrRemoteMedicalRecordMapper hrRemoteMedicalRecordMapper;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final SysOperLogService sysOperLogService;
    private final HrClientService hrClientService;
    private final HrAppendixService hrAppendixService;
    private final HrTalentStaffService hrTalentStaffService;

    /**
     * 创建异地医疗备案记录
     *
     * @param hrRemoteMedicalRecordDTO
     * @param requestFromWx            是否请求自微信小程序
     * @return
     */
    @Override
    public HrRemoteMedicalRecordDTO createHrRemoteMedicalRecord(HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO, Boolean requestFromWx) {
        log.info("Create new HrRemoteMedicalRecord:{}", hrRemoteMedicalRecordDTO);
        String staffId = hrRemoteMedicalRecordDTO.getStaffId();
        if (requestFromWx) {
            JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
            staffId = jwtMiniDTO.getId();
        }
        if (StringUtils.isBlank(staffId)) {
            throw new CommonException("员工id不能为空");
        }
        HrTalentStaff hrTalentStaff = this.hrTalentStaffService.getById(staffId);
        // 新增记录
        hrRemoteMedicalRecordDTO.setStaffId(staffId)
            .setLastModifiedDate(LocalDateTime.now());
        HrRemoteMedicalRecord hrRemoteMedicalRecord = this.hrRemoteMedicalRecordMapper.toEntity(hrRemoteMedicalRecordDTO);
        this.hrRemoteMedicalRecordRepository.insert(hrRemoteMedicalRecord);
        HrRemoteMedicalRecordDTO toDto = this.hrRemoteMedicalRecordMapper.toDto(hrRemoteMedicalRecord);
        // 添加日志
        if (requestFromWx) {
            String message = hrTalentStaff.getName() + "新增了异地医疗备案记录";
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(toDto.getId(), null, staffId, message, null, true, null, ServiceCenterEnum.REMOTE_MEDICAL_RECORD.getKey());
        } else {
            JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
            String message = jwtUserDTO.getRealName() + "从管理端为" + hrTalentStaff.getName() + "新增了异地医疗备案记录";
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(toDto.getId(), null, jwtUserDTO.getId(), message, null, ServiceCenterEnum.REMOTE_MEDICAL_RECORD.getKey());
        }
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.REMOTE_MEDICAL_RECORD.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrRemoteMedicalRecordDTO),
            HrRemoteMedicalRecordDTO.class,
            null,
            JSON.toJSONString(toDto)
        );
        return toDto;
    }

    /**
     * 修改异地医疗备案记录
     *
     * @param hrRemoteMedicalRecordDTO
     * @return
     */
    @Override
    public Optional<HrRemoteMedicalRecordDTO> updateHrRemoteMedicalRecord(HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();

        return Optional.ofNullable(this.hrRemoteMedicalRecordRepository.selectById(hrRemoteMedicalRecordDTO.getId()))
            .map(roleTemp -> {
                roleTemp.setRemark(hrRemoteMedicalRecordDTO.getRemark()).setLastModifiedDate(LocalDateTime.now());
                this.hrRemoteMedicalRecordRepository.updateById(roleTemp);
                String message = jwtUserDTO.getRealName() + "填写了备注。内容为：" + hrRemoteMedicalRecordDTO.getRemark();
                hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(roleTemp.getId(), null, jwtUserDTO.getId(), message, hrRemoteMedicalRecordDTO.getRemark(),
                    false, String.join(",", hrRemoteMedicalRecordDTO.getAppendixIds()), ServiceCenterEnum.REMOTE_MEDICAL_RECORD.getKey());
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.REMOTE_MEDICAL_RECORD.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrRemoteMedicalRecordDTO),
                    HrRemoteMedicalRecordDTO.class,
                    null,
                    JSON.toJSONString(roleTemp)
                );
                log.info("Update HrRemoteMedicalRecord:{}", hrRemoteMedicalRecordDTO);
                return hrRemoteMedicalRecordDTO;
            });
    }

    /**
     * 查询异地医疗备案记录详情
     *
     * @param id
     * @return
     */
    @Override
    public HrRemoteMedicalRecordDTO getHrRemoteMedicalRecord(String id) {
        log.info("Get HrRemoteMedicalRecord :{}", id);

        HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO = this.hrRemoteMedicalRecordRepository.findById(id);
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(id, null);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)) {
            hrRemoteMedicalRecordDTO.setApplyOpLogsList(applyOpLogsList);
        }
        return hrRemoteMedicalRecordDTO;
    }

    /**
     * 删除异地医疗备案记录
     *
     * @param id
     */
    @Override
    public void deleteHrRemoteMedicalRecord(String id) {
        Optional.ofNullable(this.hrRemoteMedicalRecordRepository.selectById(id))
            .ifPresent(hrRemoteMedicalRecord -> {
                this.hrRemoteMedicalRecordRepository.deleteById(id);
                log.info("Delete HrRemoteMedicalRecord:{}", hrRemoteMedicalRecord);
            });
    }

    /**
     * 批量删除异地医疗备案记录
     *
     * @param ids
     */
    @Override
    public void deleteHrRemoteMedicalRecord(List<String> ids) {
        log.info("Delete HrRemoteMedicalRecords:{}", ids);
        this.hrRemoteMedicalRecordRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询异地医疗备案记录
     *
     * @param hrRemoteMedicalRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO, Long pageNumber, Long pageSize) {
        Page<HrRemoteMedicalRecord> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        IPage<HrRemoteMedicalRecordDTO> iPage = this.hrRemoteMedicalRecordRepository.findPage(page, hrRemoteMedicalRecordDTO, clientIds);
        return iPage;
    }

    @Override
    public String export(HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrRemoteMedicalRecordDTO> list = hrRemoteMedicalRecordRepository.findListByDTO(hrRemoteMedicalRecordDTO, clientIds);
        List<String> ids = list.stream().map(HrRemoteMedicalRecordDTO::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(list, ModuleTypeEnum.REMOTE_MEDICAL_RECORD.getValue(), HrRemoteMedicalRecordDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.REMOTE_MEDICAL_RECORD.getValue(), BusinessTypeEnum.EXPORT.getKey(),
            JSON.toJSONString(ids), ids.size(), fileUrl);
        return fileUrl;
    }

    /**
     * 不分页查询异地医疗备案记录
     *
     * @return
     */
    @Override
    public List<HrRemoteMedicalRecordDTO> findList() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        List<HrRemoteMedicalRecordDTO> list = this.hrRemoteMedicalRecordRepository.findList(jwtMiniDTO.getId());
        return list;
    }
}
