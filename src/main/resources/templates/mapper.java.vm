package ${package.Mapper};

import ${package.Entity}.${entity};
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

/**
 * $!{table.comment}数据库操作类
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${kotlin})
interface ${table.mapperName} : ${superMapperClass}<${entity}>
#else
@Repository
public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {

}
#end
