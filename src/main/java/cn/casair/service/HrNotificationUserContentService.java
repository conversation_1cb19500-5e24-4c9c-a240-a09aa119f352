package cn.casair.service;
import cn.casair.domain.HrNotificationUserContent;
import cn.casair.dto.HrNotificationUserContentDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 通知消息内容表服务类
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
public interface HrNotificationUserContentService extends IService<HrNotificationUserContent> {


    /**
     * 创建通知消息内容表
     * @param hrNotificationUserContentDTO
     * @return
     */
    HrNotificationUserContentDTO createHrNotificationUserContent(HrNotificationUserContentDTO hrNotificationUserContentDTO);

    /**
     * 修改通知消息内容表
     * @param hrNotificationUserContentDTO
     * @return
     */
    Optional<HrNotificationUserContentDTO> updateHrNotificationUserContent(HrNotificationUserContentDTO hrNotificationUserContentDTO);

    /**
     * 查询通知消息内容表详情
     * @param id
     * @return
     */
    HrNotificationUserContentDTO getHrNotificationUserContent(String id);

    /**
     * 删除通知消息内容表
     * @param id
     */
    void deleteHrNotificationUserContent(String id);

    /**
     * 批量删除通知消息内容表
     * @param ids
     */
    void deleteHrNotificationUserContent(List<String> ids);

    /**
     * 分页查询通知消息内容表
     * @param hrNotificationUserContentDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrNotificationUserContentDTO hrNotificationUserContentDTO,Long pageNumber,Long pageSize);
    }
