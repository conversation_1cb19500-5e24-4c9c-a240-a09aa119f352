package cn.casair.repository;

import cn.casair.domain.HrBillDetailGrant;
import cn.casair.dto.HrBillDetailGrantDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 工资发放状态记录数据库操作类
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
@Repository
public interface HrBillDetailGrantRepository extends BaseMapper<HrBillDetailGrant> {

    /**
     * 查询工资发放状态记录
     *
     * @param billDetailId
     * @return
     */
    List<HrBillDetailGrantDTO> findListByBillDetailId(@Param("billDetailId") String billDetailId);

    /**
     * 查询工资发放状态记录
     *
     * @param billDetailGrantDTO
     * @return
     */
    List<HrBillDetailGrantDTO> findList(@Param("params") HrBillDetailGrantDTO billDetailGrantDTO);
}
