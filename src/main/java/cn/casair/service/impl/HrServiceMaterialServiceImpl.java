package cn.casair.service.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrServiceMaterial;
import cn.casair.dto.HrServiceMaterialDTO;
import cn.casair.repository.HrServiceMaterialRepository;
import cn.casair.mapper.HrServiceMaterialMapper;
import cn.casair.service.HrServiceMaterialService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;
import java.util.List;
/**
 * 服务与材料的关联表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrServiceMaterialServiceImpl extends ServiceImpl<HrServiceMaterialRepository, HrServiceMaterial>implements HrServiceMaterialService {


    private final HrServiceMaterialRepository hrServiceMaterialRepository;
    private final HrServiceMaterialMapper hrServiceMaterialMapper;

    public HrServiceMaterialServiceImpl(HrServiceMaterialRepository hrServiceMaterialRepository, HrServiceMaterialMapper hrServiceMaterialMapper){
    this.hrServiceMaterialRepository = hrServiceMaterialRepository;
    this.hrServiceMaterialMapper= hrServiceMaterialMapper;
    }

    /**
     * 创建服务与材料的关联表
     * @param hrServiceMaterialDTO
     * @return
     */
    @Override
    public HrServiceMaterialDTO createHrServiceMaterial(HrServiceMaterialDTO hrServiceMaterialDTO){
    log.info("Create new HrServiceMaterial:{}", hrServiceMaterialDTO);

    HrServiceMaterial hrServiceMaterial =this.hrServiceMaterialMapper.toEntity(hrServiceMaterialDTO);
    this.hrServiceMaterialRepository.insert(hrServiceMaterial);
    return this.hrServiceMaterialMapper.toDto(hrServiceMaterial);
    }

    /**
     * 修改服务与材料的关联表
     * @param hrServiceMaterialDTO
     * @return
     */
    @Override
    public Optional<HrServiceMaterialDTO>updateHrServiceMaterial(HrServiceMaterialDTO hrServiceMaterialDTO){
    return Optional.ofNullable(this.hrServiceMaterialRepository.selectById(hrServiceMaterialDTO.getId()))
    .map(roleTemp->{
    HrServiceMaterial hrServiceMaterial =this.hrServiceMaterialMapper.toEntity(hrServiceMaterialDTO);
    this.hrServiceMaterialRepository.updateById(hrServiceMaterial);
    log.info("Update HrServiceMaterial:{}", hrServiceMaterialDTO);
    return hrServiceMaterialDTO;
    });
    }

    /**
     * 查询服务与材料的关联表详情
     * @param id
     * @return
     */
    @Override
    public HrServiceMaterialDTO getHrServiceMaterial(String id){
    log.info("Get HrServiceMaterial :{}",id);

    HrServiceMaterial hrServiceMaterial =this.hrServiceMaterialRepository.selectById(id);
    return this.hrServiceMaterialMapper.toDto(hrServiceMaterial);
    }

    /**
     * 删除服务与材料的关联表
     * @param id
     */
    @Override
    public void deleteHrServiceMaterial(String id){
    Optional.ofNullable(this.hrServiceMaterialRepository.selectById(id))
    .ifPresent(hrServiceMaterial ->{
    this.hrServiceMaterialRepository.deleteById(id);
    log.info("Delete HrServiceMaterial:{}", hrServiceMaterial);
    });
    }

    /**
     * 批量删除服务与材料的关联表
     * @param ids
     */
    @Override
    public void deleteHrServiceMaterial(List<String>ids){
    log.info("Delete HrServiceMaterials:{}",ids);
    this.hrServiceMaterialRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询服务与材料的关联表
     * @param hrServiceMaterialDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrServiceMaterialDTO hrServiceMaterialDTO,Long pageNumber,Long pageSize){
    Page<HrServiceMaterial>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrServiceMaterial>qw=new QueryWrapper<>(this.hrServiceMaterialMapper.toEntity(hrServiceMaterialDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrServiceMaterialRepository.selectPage(page,qw);
    iPage.setRecords(this.hrServiceMaterialMapper.toDto(iPage.getRecords()));
    return iPage;
    }

    @Override
    public void addList(ArrayList<String> materialList, String serviceId) {
        try {
            ArrayList<HrServiceMaterial> hrServiceMaterialArrayList = new ArrayList<>();
            for (String materialId : materialList) {
                HrServiceMaterial hrServiceMaterial = new HrServiceMaterial();
                hrServiceMaterial.setMaterialId(materialId);
                hrServiceMaterial.setServiceId(serviceId);
                hrServiceMaterialArrayList.add(hrServiceMaterial);
            }
            saveBatch(hrServiceMaterialArrayList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     *
     * @param serviceId
     * @return
     */
    @Override
    public List<HrServiceMaterial> selectByServiceId(String serviceId) {
        QueryWrapper<HrServiceMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("service_id",serviceId);
        List<HrServiceMaterial> list = list(queryWrapper);
        return list;
    }
}
