package cn.casair.service;

import cn.casair.domain.HrArrivalRecordDetail;
import cn.casair.dto.HrArrivalRecordDetailDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 到账记录明细服务类
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
public interface HrArrivalRecordDetailService extends IService<HrArrivalRecordDetail> {


    /**
     * 创建到账记录明细
     *
     * @param hrArrivalRecordDetailDTO
     * @return
     */
    HrArrivalRecordDetailDTO createHrArrivalRecordDetail(HrArrivalRecordDetailDTO hrArrivalRecordDetailDTO);

    /**
     * 修改到账记录明细
     *
     * @param hrArrivalRecordDetailDTO
     * @return
     */
    Optional<HrArrivalRecordDetailDTO> updateHrArrivalRecordDetail(HrArrivalRecordDetailDTO hrArrivalRecordDetailDTO);

    /**
     * 查询到账记录明细详情
     *
     * @param id
     * @return
     */
    HrArrivalRecordDetailDTO getHrArrivalRecordDetail(String id);

    /**
     * 删除到账记录明细
     *
     * @param id
     */
    void deleteHrArrivalRecordDetail(String id);

    /**
     * 批量删除到账记录明细
     *
     * @param ids
     */
    void deleteHrArrivalRecordDetail(List<String> ids);

    /**
     * 分页查询到账记录明细
     *
     * @param hrArrivalRecordDetailDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrArrivalRecordDetailDTO hrArrivalRecordDetailDTO, Long pageNumber, Long pageSize);
}
