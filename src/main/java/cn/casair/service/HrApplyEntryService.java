package cn.casair.service;

import cn.casair.domain.HrApplyEntry;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.DynamicHeadersDTO;
import cn.casair.dto.HrApplyEntryDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 客户入职申请服务类
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
public interface HrApplyEntryService extends IService<HrApplyEntry> {


    /**
     * 创建客户入职申请
     *
     * @param hrApplyEntryDTO
     * @return
     */
    HrApplyEntryDTO createHrApplyEntry(HrApplyEntryDTO hrApplyEntryDTO);

    /**
     * 修改客户入职申请
     *
     * @param hrApplyEntryDTO
     * @return
     */
    Optional<HrApplyEntryDTO> updateHrApplyEntry(HrApplyEntryDTO hrApplyEntryDTO);

    /**
     * 查询客户入职申请详情
     *
     * @param id
     * @return
     */
    HrApplyEntryDTO getHrApplyEntry(String id);

    /**
     * 删除客户入职申请
     *
     * @param id
     */
    void deleteHrApplyEntry(String id);

    /**
     * 批量删除客户入职申请
     *
     * @param ids
     */
    void deleteHrApplyEntry(List<String> ids);

    /**
     * 分页查询客户入职申请
     *
     * @param hrApplyEntryDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrApplyEntryDTO hrApplyEntryDTO, Long pageNumber, Long pageSize);

    /**
     * 待入职员工信息导入
     *
     *
     * @param clientId
     * @param file
     * @return
     */
    String importHiredStaff(String clientId, MultipartFile file);

    /**
     * 待入职员工信息导入模板
     *
     * @param response
     */
    String importHiredStaffTemplate(HttpServletResponse response);

    /**
     * 经理审批拒绝
     *
     * @param batchOptDTO
     * @return
     */
    ResponseEntity approvalRejectApplyEntry(BatchOptDTO batchOptDTO);

    /**
     * 经理审批通过
     *
     * @param batchOptDTO
     * @return
     */
    ResponseEntity approvalPassApplyEntry(BatchOptDTO batchOptDTO);

    /**
     * 员工基数动态显示
     *
     * @param params
     * @return
     */
    List<DynamicHeadersDTO> staffCardinalDynamicHeader(Map<String, String> params);

}
