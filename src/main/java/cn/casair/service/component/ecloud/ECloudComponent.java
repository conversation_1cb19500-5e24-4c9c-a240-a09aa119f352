package cn.casair.service.component.ecloud;

import cn.casair.cache.RedisCache;
import cn.casair.common.Constants;
import cn.casair.common.enums.CompanyInfoEnum;
import cn.casair.common.enums.ContractEnum;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.Base64Util;
import cn.casair.common.utils.FileUtil;
import cn.casair.common.utils.PdfUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.domain.HrAppendix;
import cn.casair.domain.HrApplyDepartureStaff;
import cn.casair.domain.HrContract;
import cn.casair.domain.HrContractAppendix;
import cn.casair.dto.HrSealsDTO;
import cn.casair.dto.HrStaffSignDTO;
import cn.casair.dto.JWTMiniDTO;
import cn.casair.dto.formdata.SignRegionDTO;
import cn.casair.dto.verify.VerifyUserInfo;
import cn.casair.repository.HrApplyDepartureStaffRepository;
import cn.casair.repository.HrContractAppendixRepository;
import cn.casair.repository.HrContractRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.util.SecurityUtils;
import com.agile.ecloud.sdk.bean.ECloudDomain;
import com.agile.ecloud.sdk.bean.EcloudPublicKey;
import com.agile.ecloud.sdk.common.ContractInfoSignApiParam;
import com.agile.ecloud.sdk.http.EcloudClient;
import com.agile.ecloud.sdk.util.AESUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfReader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 易云章服务操作组件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/09/25 21:16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ECloudComponent {

    @Value("${file.temp-path}")
    private String tempPath;

    @Value("${ecloud.aesKey}")
    private String aesKey;

    @Value("${ecloud.h5url}")
    private String h5Url;

    @Value("${ecloud.smsCode}")
    private boolean smsCode;

    private final RedisCache redisCache;
    private final HrAppendixService hrAppendixService;
    private final HrContractAppendixRepository hrContractAppendixRepository;
    private final HrContractRepository hrContractRepository;
    private final HrApplyDepartureStaffRepository hrApplyDepartureStaffRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;

    /**
     * 判断易云章返回参数
     *
     * @param returnData 易云章返回数据
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/9/25
     **/
    private JSONObject judgeReturnData(ECloudDomain returnData) {
        if (Constants.SUCCESS_CODE.equals(returnData.getCode())) {
            return JSONObject.parseObject(JSONObject.toJSONString(returnData.getData()));
        } else {
            throw new CommonException("易云章错误信息：" + returnData.getMessage());
        }
    }

    /**
     * 获取易云章签名板URL
     */
    public String getWritingBoard() {
        JWTMiniDTO user = SecurityUtils.getCurrentMini().get();
        ECloudDomain returnData = EcloudClient.handWriting(user.getPhone(), "https://www.baidu.com/");
        log.info("获取易云章签名板URL:{}", JSON.toJSONString(returnData));
        JSONObject jsonObject = this.judgeReturnData(returnData);
        return jsonObject.getString("handWritingUrl");
    }

    /**
     * 下载合同文件并上传到文件服务器
     *
     * @param contractNum
     * @return
     */
    public HrAppendix downloadContractFile(String contractNum) {
        ECloudDomain returnData = EcloudClient.downloadContractBase64(contractNum);
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(returnData.getData()));
        String bytes = jsonObject.getString("bytes");
        try {
            String targetPath = tempPath + RandomUtil.generateId() + ".pdf";
            Base64Util.decoderBase64File(bytes, targetPath);
            return this.hrAppendixService.uploadContractFile(targetPath);
        } catch (Exception e) {
            log.error("易云章下载合同异常:{}", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 员工添加签名
     *
     * @param hrStaffSignDTO
     * @return
     */
    public JSONObject addStaffSign(HrStaffSignDTO hrStaffSignDTO) {
        String base64String = FileUtil.imgUrlToBase64(hrStaffSignDTO.getSignImg());
        ECloudDomain returnData = EcloudClient.addSign(hrStaffSignDTO.getPhone(), "1", base64String);
        log.info("员工添加签名:{}", JSON.toJSONString(returnData));
        return judgeReturnData(returnData);
    }

    /**
     * 获取员工签名列表
     *
     * @return
     */
    public JSONObject getSignList() {
        JWTMiniDTO user = SecurityUtils.getCurrentMini().get();
        ECloudDomain returnData = EcloudClient.getsignList(user.getPhone(), "1");
        log.info("获取员工签名列表:{}", JSON.toJSONString(returnData));
        return judgeReturnData(returnData);
    }

    /**
     * 保存人脸识别上送身份中间信息
     *
     * @param params
     * @return void
     * <AUTHOR>
     * @date 2022/1/11
     **/
    public void saveFaceVerifyInfo(Map<String, Object> params) {
        if (params.get("platformUserId") == null) {
            throw new CommonException("platformUserId不能为空！");
        }
        if (params.get("orderNo") == null) {
            throw new CommonException("orderNo不能为空！");
        }
        String platformUserId = String.valueOf(params.get("platformUserId"));
        String orderNo = String.valueOf(params.get("orderNo"));
        log.info("保存人脸识别上送身份中间信息:{},{}", platformUserId, orderNo);

        String redisKey = RedisKeyEnum.personalVerification.VERIFY_INFO.getValue() + platformUserId;
        redisCache.setCacheObject(redisKey, orderNo, 24, TimeUnit.HOURS);
    }

    /**
     * 个人身份证三要素认证
     *
     * @param verifyUserInfo
     * @return
     */
    public ECloudDomain threeElementsIdentification(VerifyUserInfo verifyUserInfo) {
        ECloudDomain returnData = EcloudClient.threeElementsIdentification(verifyUserInfo.getRealName(), verifyUserInfo.getPhone(), verifyUserInfo.getIdCardNumber());
        log.info("获取易云章个人身份证三要素认证:{}", JSON.toJSONString(returnData));
        return returnData;
    }

    /**
     * 用户信息三要素校验
     *
     * @param idNo
     * @param phone
     * @param realName
     */
    public void internalThreeElementsCheck(String idNo, String phone, String realName) {
        // 身份三要素校验
        ECloudDomain eCloudDomain = this.threeElementsIdentification(new VerifyUserInfo().setIdCardNumber(idNo).setRealName(realName).setPhone(phone));
        if (eCloudDomain == null) {
            throw new CommonException("用户身份证三要素认证异常！");
        }
        if (eCloudDomain.getCode().equals(Constants.SUCCESS_CODE)) {
            if ("不一致".equals(eCloudDomain.getMessage())) {
                throw new CommonException("用户：" + realName + " 姓名、身份证号码、手机号码信息一致性校验校验未通过!");
            }
        } else {
            throw new CommonException("个人身份证三要素认证异常:" + eCloudDomain.getCode() + " " + eCloudDomain.getMessage());
        }
    }

    /**
     * 个人银行卡认证
     *
     * @param verifyUserInfo
     * @return
     */
    public ECloudDomain bankVerify(VerifyUserInfo verifyUserInfo) {
        ECloudDomain returnData = EcloudClient.bankVerify(verifyUserInfo.getRealName(), null, verifyUserInfo.getBankNumber(), verifyUserInfo.getIdCardNumber(), 3);
        log.info("获取易云章个人银行卡认证:{}", JSON.toJSONString(returnData));
        return returnData;
    }

    /**
     * 获取易云章用户信息
     *
     * @param type
     * @param cardType
     * @param idCardNum
     * @return
     */
    public ECloudDomain getCertInfo(String type, String cardType, String idCardNum) {
        ECloudDomain returnData = EcloudClient.getCertInfo(type, cardType, idCardNum);
        log.info("获取易云章用户信息:{}", JSON.toJSONString(returnData));
        return returnData;
    }

    /**
     * 合同作废
     *
     * @param contractNum
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/12/2
     **/
    public JSONObject contractCancel(String contractNum) {
        ECloudDomain returnData = EcloudClient.contractCancel(contractNum, CompanyInfoEnum.FIRST_PART_PHONE.getValue());
        log.info("易云章合同作废：{}", JSON.toJSONString(returnData));
        return judgeReturnData(returnData);
    }

    /**
     * 实名认证：个人身份证二要素
     *
     * @param name
     * @param certificateNum
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/12/2
     **/
    public JSONObject realNameAuth(String name, String certificateNum) {
        ECloudDomain returnData = EcloudClient.identityAuthUrl(name, certificateNum);
        log.info("易云章实名认证:{}", JSON.toJSONString(returnData));
        return judgeReturnData(returnData);
    }

    /**
     * 添加公司新印章到易云章
     *
     * @param hrSealsDTO 印章实体
     * @return void
     * <AUTHOR>
     * @date 2021/9/24
     **/
    public void addNewSignToeCloud(HrSealsDTO hrSealsDTO) {
        log.info("时间：{}",LocalDateTime.now());
        String base64String = FileUtil.imgUrlToBase64(hrSealsDTO.getSealUrl());
        // 使用公司联系方式创建印章
        ECloudDomain returnData = EcloudClient.addSign(CompanyInfoEnum.FIRST_PART_PHONE.getValue(), "2", base64String);
        log.info("添加公司新印章到易云章:{}", JSON.toJSONString(returnData));
        JSONObject jsonObject = this.judgeReturnData(returnData);
        hrSealsDTO.setSignId(jsonObject.getString("signId"));
    }

    /**
     * 删除公司印章
     *
     * @param signId 印章id
     * @return void
     * <AUTHOR>
     * @date 2021/9/25
     **/
    public void delSign(String signId) {
        ECloudDomain returnData = EcloudClient.delSign(CompanyInfoEnum.FIRST_PART_PHONE.getValue(), signId);
        log.info("删除公司印章:{}", JSON.toJSONString(returnData));
        this.judgeReturnData(returnData);
    }

    /**
     * 易云章OCR识别
     *
     * @param fileUrl     文件Url
     * @param cardType idc:身份证, xsz-new:行驶证,jsz-new:驾驶证,银行卡:bank-new
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/9/26
     **/
    public ECloudDomain allOcrVerify(String fileUrl, String cardType) {
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            InputStream inputStream = conn.getInputStream();
            byte[] data = IOUtils.toByteArray(inputStream);
            String imgContent = Base64.encodeBase64String(data);
            if (StringUtils.isBlank(imgContent)) {
                throw new CommonException("识别图片信息不能为空！");
            }
            ECloudDomain returnData = EcloudClient.allOcrVerify(cardType, imgContent);
            log.info("易云章OCR识别结果：{}", JSON.toJSONString(returnData));
            return returnData;
        } catch (IOException e) {
            log.error("证件OCR识别异常：{}", e.getMessage());
            e.printStackTrace();
            throw new CommonException("证件OCR识别异常：" + e.getMessage());
        }
    }

    /**
     * 吊销用户
     *
     * @param idNo
     * @param userName
     * @param phone
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/12/27
     **/
    public JSONObject revokeUser(String idNo, String userName, String phone) {
        JSONObject param = new JSONObject();
        param.put("type", "1");
        param.put("cardType", "0");
        param.put("idCardNum", idNo);
        param.put("name", userName);
        param.put("mobilePhone", phone);
        ECloudDomain returnData = EcloudClient.unwrap(JSONObject.toJSONString(param));
        log.info("用户吊销结果：{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData);
    }

    /**
     * 员工证书申请
     *
     * @param idNo
     * @param phone
     * @param staffName
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/9/26
     **/
    public JSONObject applyCert(String idNo, String phone, String staffName) {
        JSONObject param = new JSONObject();
        param.put("cardType", "0");
        param.put("idCardNum", idNo);
        param.put("mobilePhone", phone);
        param.put("name", staffName);
        param.put("type", "1");
        ECloudDomain returnData = EcloudClient.applyCert(JSONObject.toJSONString(param));
        log.info("员工证书申请：{}", JSON.toJSONString(returnData));
        if (Constants.SUCCESS_CODE.equals(returnData.getCode())) {
            return JSONObject.parseObject(JSONObject.toJSONString(returnData.getData()));
        } else if (Constants.CERT_EXISTS_CODE.equals(returnData.getCode())) {
            log.info("员工证书申请：{}", "证书已存在！");
            return this.judgeReturnData(EcloudClient.getCertInfo(JSONObject.toJSONString(param)));
        } else {
            throw new CommonException("易云章错误信息：" + returnData.getMessage());
        }
    }

    /**
     * 更新员工证书主体姓名
     *
     * @param idNo
     * @param name
     * @param phone
     * @return
     */
    public JSONObject updateCertUserName(String idNo, String name, String phone) {
        ECloudDomain returnData = EcloudClient.updateCert("1", "0", idNo, name, phone);
        log.info("更新员工易云章证书主体名称：{}", JSON.toJSONString(returnData));
        if (Constants.SUCCESS_CODE.equals(returnData.getCode())) {
            return JSONObject.parseObject(JSONObject.toJSONString(returnData.getData()));
        } else if (returnData.getCode().equals(9093)) {
            log.info("证件名称一致，无需重新申请证书");
            return null;
        } else {
            throw new CommonException(returnData.getMessage());
        }
    }

    /**
     * 更新员工证书主体手机号
     *
     * @param idNo
     * @param name
     * @param phone
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/9/28
     **/
    public JSONObject updateCertUserPhone(String idNo, String name, String phone) {
        ECloudDomain returnData = EcloudClient.changeMobile("1", "0", idNo, name, phone);
        log.info("更新员工易云章证书主体手机号：{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData);
    }

    /**
     * 上传合同文件到易云章
     *
     * @param contractNum
     * @param fileName
     * @param appendixPath
     * @return String
     * <AUTHOR>
     * @date 2021/9/26
     **/
    public String uploadContractUrl(String contractNum, String fileName, String appendixPath) {
        Map<String, Object> param = new HashMap<>();
        param.put("mobilePhone", CompanyInfoEnum.FIRST_PART_PHONE.getValue());
        if (StringUtils.isNotBlank(contractNum)) {
            param.put("fileId", contractNum);
        }
        param.put("fileType", "pdf");
        param.put("fileName", fileName);
        param.put("fileUrl", appendixPath);
        log.info("上传合同文件到易云章请求参数：{}", JSON.toJSONString(param));
        ECloudDomain returnData = EcloudClient.uploadContractUrl(JSON.toJSONString(param));
        log.info("上传合同文件到易云章{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData).getString("contractNum");
    }

    /**
     * 上传辞职申请书到易云章
     *
     * @param fileName
     * @param appendixPath
     * @return String
     * <AUTHOR>
     * @date 2021/9/26
     **/
    public String uploadResignationUrl(String fileName, String appendixPath, String phone) {
        Map<String, Object> param = new HashMap<>();
        param.put("mobilePhone", phone);
        param.put("fileType", "pdf");
        param.put("fileName", fileName + ".pdf");
        param.put("fileUrl", appendixPath);
        log.info("上传辞职申请书到易云章请求参数：{}", JSON.toJSONString(param));
        ECloudDomain returnData = EcloudClient.uploadContractUrl(JSON.toJSONString(param));
        log.info("上传辞职申请书文件到易云章{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData).getString("contractNum");
    }

    /**
     * 乙方签名到合同
     *
     * @param contractNum
     * @param idNo
     * @param realName
     * @param phone
     * @param signId
     * @return
     */
    public JSONObject secondPartSealToContract(String contractNum, String idNo, String realName, String phone, String signId, SignRegionDTO signRegionDTO, int isFinish) {
        JSONObject signerInfo = new JSONObject();
        signerInfo.put("type", 1);
        signerInfo.put("cardType", 0);
        signerInfo.put("idCardNum", idNo);
        signerInfo.put("name", realName);
        signerInfo.put("mobilePhone", phone);

        JSONObject position = new JSONObject();
        position.put("page", signRegionDTO.getPageNo());
        position.put("x", signRegionDTO.getX());
        position.put("y", signRegionDTO.getY());
        position.put("signId", signId);
        JSONArray arr = new JSONArray();
        arr.add(position);

        ECloudDomain returnData = EcloudClient.autoSignByPoistion(JSONObject.toJSONString(signerInfo), contractNum, isFinish, JSONObject.toJSONString(arr));
        log.info("乙方签章到合同:{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData);
    }

    /**
     * 甲方签名到合同
     *
     * @param contractNum
     * @param signId
     * @return
     */
    public JSONObject firstPartSealToContract(String contractNum, String signId, SignRegionDTO signRegionDTO) {
        JSONObject signerInfo = new JSONObject();
        signerInfo.put("type", 2);
        signerInfo.put("cardType", 4);
        signerInfo.put("idCardNum", CompanyInfoEnum.BUSINESS_NUM.getValue());
        signerInfo.put("name", CompanyInfoEnum.FIRST_PART_NAME.getValue());
        signerInfo.put("mobilePhone", CompanyInfoEnum.FIRST_PART_PHONE.getValue());

        JSONObject position = new JSONObject();
        position.put("page", signRegionDTO.getPageNo());
        position.put("x", signRegionDTO.getX());
        position.put("y", signRegionDTO.getY());
        position.put("signId", signId);
        JSONArray arr = new JSONArray();
        arr.add(position);

        ECloudDomain returnData = EcloudClient.autoSignByPoistion(JSONObject.toJSONString(signerInfo), contractNum, 0, JSONObject.toJSONString(arr));
        log.info("甲方签章到合同:{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData);
    }


    /**
     * 甲方签章到合同
     *
     * @param contractNum 合同编号
     * @param fileUrl     文件地址
     * @param signId      易云章签章id
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/9/27
     **/
    public JSONObject sealToContract(String contractNum, String fileUrl, String signId) {
        JSONObject signerInfo = new JSONObject();
        signerInfo.put("type", 2);
        signerInfo.put("cardType", 4);
        signerInfo.put("idCardNum", CompanyInfoEnum.BUSINESS_NUM.getValue());
        signerInfo.put("name", CompanyInfoEnum.FIRST_PART_NAME.getValue());
        signerInfo.put("mobilePhone", CompanyInfoEnum.FIRST_PART_PHONE.getValue());
        log.info("signerInfo:{}", signerInfo);

        SignRegionDTO pdfSignRegion = PdfUtils.getPdfSignRegion(fileUrl, ContractEnum.FormField.firstPartSign.getKey());
        JSONObject position = new JSONObject();
        position.put("page", pdfSignRegion.getPageNo());
        position.put("x", pdfSignRegion.getX());
        position.put("y", pdfSignRegion.getY());
        position.put("signId", signId);
        position.put("positionName", "xxx");

        JSONObject position1 = new JSONObject();
        position1.put("page", pdfSignRegion.getPageNo());
        position1.put("x", pdfSignRegion.getX());
        position1.put("y", pdfSignRegion.getY());
        position1.put("type", "1");
        position1.put("positionName", "yyy");

        JSONArray arr = new JSONArray();
        arr.add(position);
        arr.add(position1);
        log.info("arr:{}", arr);
        ECloudDomain returnData = EcloudClient.autoSignByPoistionForDateImage(JSON.toJSONString(signerInfo), contractNum, 1, JSON.toJSONString(arr));
        // ECloudDomain returnData = EcloudClient.autoSignByPoistionForDateImage(JSON.toJSONString(signerInfo), contractNum, 1, JSON.toJSONString(arr));
        log.info("甲方签章到合同:{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData);
    }

    /**
     * 开具证明盖章
     * @param contractNum
     * @param pdfSignRegion
     * @return
     */
    public JSONObject electronicSignature(String contractNum, Map<String, Object> pdfSignRegion,String fileUrl,String signId) {
        JSONObject signerInfo = new JSONObject();
        signerInfo.put("type", 2);
        signerInfo.put("cardType", 4);
        signerInfo.put("idCardNum", CompanyInfoEnum.BUSINESS_NUM.getValue());
        signerInfo.put("name", CompanyInfoEnum.FIRST_PART_NAME.getValue());
        signerInfo.put("mobilePhone", CompanyInfoEnum.FIRST_PART_PHONE.getValue());
        log.info("signerInfo:{}", signerInfo);
        int pageNo = (int)pdfSignRegion.get("pageNo");
        double x = Double.parseDouble(String.valueOf(pdfSignRegion.get("x")));
        double y = Double.parseDouble(String.valueOf(pdfSignRegion.get("y")));
        Rectangle rectangle = PdfUtils.widthAndHeight(fileUrl, pageNo);
        float width = rectangle.getWidth();
        float height = rectangle.getHeight();
        JSONObject position = new JSONObject();
        position.put("page", pageNo);
        position.put("x", Math.round(x * width - 83) > 0 ? Math.round(x * width - 83) : 0);
        position.put("y", Math.round(y * height - 83) > 0 ? Math.round(y * height - 83) : 0);
        position.put("signId", signId);
        JSONArray arr = new JSONArray();
        arr.add(position);
        log.info("arr:{}", arr);
        ECloudDomain returnData = EcloudClient.autoSignByPoistion(JSON.toJSONString(signerInfo), contractNum, 0, JSON.toJSONString(arr));
        log.info("甲方签章到合同:{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData);
    }

    public JSONObject electronicBillSignature(String contractNum,String fileUrl,String signId,int x, int y) {
        JSONObject signerInfo = new JSONObject();
        signerInfo.put("type", 2);
        signerInfo.put("cardType", 4);
        signerInfo.put("idCardNum", CompanyInfoEnum.BUSINESS_NUM.getValue());
        signerInfo.put("name", CompanyInfoEnum.FIRST_PART_NAME.getValue());
        signerInfo.put("mobilePhone", CompanyInfoEnum.FIRST_PART_PHONE.getValue());
        log.info("signerInfo:{}", signerInfo);
        JSONArray arr = new JSONArray();
        try {
            PdfReader reader = new PdfReader(fileUrl);
            int numberOfPages = reader.getNumberOfPages();
            for (int i = 1; i <= numberOfPages; i++) {
                int pageNo = i;
                JSONObject position = new JSONObject();
                position.put("page", pageNo);
                position.put("x", x);
                position.put("y", y);
                position.put("signId", signId);
                arr.add(position);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.info("arr:{}", arr);
        ECloudDomain returnData = EcloudClient.autoSignByPoistion(JSON.toJSONString(signerInfo), contractNum, 0, JSON.toJSONString(arr));
        log.info("结算单明细盖财务章:{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData);
    }

    /**
     * 获取H5人脸核身页面跳转地址
     *
     * @param params
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/9/27
     **/
    public JSONObject getFaceNucleusUrl(Map<String, Object> params) {
        JWTMiniDTO user = SecurityUtils.getCurrentMini().get();
        String url = String.valueOf(params.get("url"));
        if (StringUtils.isBlank(url)) {
            throw new CommonException("url不能为空！");
        }
        url = url + "?platformUserId=" + user.getId();
        ECloudDomain returnData = EcloudClient.tencentFace(user.getName(), user.getCertificateNum(), "browser", url);
        log.info("获取H5人脸核身页面跳转地址:{},{},{}", user.getName(), user.getCertificateNum(), JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData);
    }

    /**
     * 获取人脸核身结果
     *
     * @param userId
     * @param orderNo
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/9/27
     **/
    public ECloudDomain getFaceNucleusResult(String userId, String orderNo) {
        ECloudDomain returnData = EcloudClient.getTencentFace(orderNo, userId);
        log.info("获取人脸核身结果:{}, {}", returnData.getCode(), returnData.getMessage());
        return returnData;
    }

    /**
     * 生成h5签署页面
     *
     * @param params
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2021/9/27
     **/
    public Map<String, Object> makeH5SignPage(Map<String, Object> params) throws Exception {
        String id = String.valueOf(params.get("id"));
        String wxCallback = String.valueOf(params.get("wxCallback"));
        HrContractAppendix contractAppendix = this.hrContractAppendixRepository.selectById(id);
        HrContract contract = this.hrContractRepository.selectById(contractAppendix.getContractId());

        /*Map<String, String> map = new LinkedHashMap<>();
        map.put("secret", EcloudPublicKey.instance.getSecret());
        map.put("v", "1.0");
        map.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("telephone", contract.getPhone());
        map.put("contractNum", contractAppendix.getContractNum());
        map.put("cardType", "0");
        map.put("idCardNum", contract.getIdNo());
        map.put("name", contract.getStaffName());
        map.put("isFinish", "0");
        map.put("signPosition", "");

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> en : map.entrySet()) {
            if (sb.length() == 0) {
                sb.append(String.format("%s=%s", en.getKey(), AESUtils.aesEncrypt(en.getValue(), aesKey)));
            } else {
                sb.append(String.format("&%s=%s", en.getKey(), AESUtils.aesEncrypt(en.getValue(), aesKey)));
            }
        }
        //无需aes加密的参数
        sb.append(String.format("&%s=%s", "appKey", EcloudPublicKey.instance.getApp_key()));
        sb.append(String.format("&%s=%s", "wxCallBack", wxCallback));
        sb.append(String.format("&%s=%s", "smscode", smsCode));
        String url = h5Url + "?" + sb.toString();
        Map<String, Object> result = new HashMap<>();
        result.put("h5url", url);*/

        EcloudPublicKey.init("yyz4x9swf5azly66l9", "ddcd51cab72dd3acd664b0fb7a02e8ab", "1.0", "https://testapi.ecloudsign.cn");
        String telephone = contract.getPhone();
        String cardType ="1";
        String idCardNum = contract.getIdNo();
        String callBack = "";
        String wxCallBack = "";
        String type= "1";
        ArrayList<ContractInfoSignApiParam> cons = new ArrayList<>();
        cons.add(new ContractInfoSignApiParam(contractAppendix.getContractNum(),"签字",1,100,100,0));
        ECloudDomain out = EcloudClient.getBatchSignUrl(telephone,cardType,idCardNum,callBack,wxCallBack,type,cons);
        System.out.println(JSONObject.toJSONString(out));
        return null;
    }

    /**
     * 生成h5签署页面----辞职电子签
     *
     * @param params
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2021/9/27
     **/
    public Map<String, Object> makeH5SignPageQuit(Map<String, Object> params) throws Exception {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        String departureStaffId = hrTalentStaffRepository.selectById(jwtMiniDTO.getId()).getDepartureStaffId();
        HrApplyDepartureStaff hrApplyDepartureStaff = hrApplyDepartureStaffRepository.selectById(departureStaffId);
        String wxCallback = String.valueOf(params.get("wxCallback"));
        Map<String, String> map = new LinkedHashMap<>();
        map.put("secret", EcloudPublicKey.instance.getSecret());
        map.put("v", "1.0");
        map.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("telephone", jwtMiniDTO.getPhone());
        map.put("contractNum", hrApplyDepartureStaff.getContractNum());
        map.put("cardType", "0");
        map.put("idCardNum", jwtMiniDTO.getCertificateNum());
        map.put("name", jwtMiniDTO.getName());
        map.put("isFinish", "0");
        map.put("signPosition", "");

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> en : map.entrySet()) {
            if (sb.length() == 0) {
                sb.append(String.format("%s=%s", en.getKey(), AESUtils.aesEncrypt(en.getValue(), aesKey)));
            } else {
                sb.append(String.format("&%s=%s", en.getKey(), AESUtils.aesEncrypt(en.getValue(), aesKey)));
            }
        }
        //无需aes加密的参数
        sb.append(String.format("&%s=%s", "appKey", EcloudPublicKey.instance.getApp_key()));
        sb.append(String.format("&%s=%s", "wxCallBack", wxCallback));
        sb.append(String.format("&%s=%s", "smscode", smsCode));
        String url = h5Url + "?" + sb.toString();
        Map<String, Object> result = new HashMap<>();
        result.put("h5url", url);
        return result;
    }

    /**
     * 获取易云章合同详情
     *
     * @param phone
     * @param contractNum
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021/9/29
     **/
    public JSONObject getContractDetail(String phone, String contractNum) {
        ECloudDomain returnData = EcloudClient.getContractDetail(phone, contractNum);
        // log.info("获取易云章合同详情:{}", JSON.toJSONString(returnData));
        return judgeReturnData(returnData);
    }

    /**
     * 根据合同编号获取合同存证下载
     *
     * @param phone
     * @param contractNum
     * @return String
     * <AUTHOR>
     * @date 2021/9/29
     **/
    public JSONObject getEvidenceData(String phone, String contractNum) {
        ECloudDomain returnData = EcloudClient.getDownloadEvidenceUrl(contractNum, phone);
        log.info("根据合同编号获取合同存证下载:{}", JSON.toJSONString(returnData));
        return this.judgeReturnData(returnData);
        // String targetPath = tempPath + UUID.randomUUID().toString().replace("-", "") + ".pdf";
        // cn.casair.ecloudsign.util.FileUtil.ecloudToFile(returnData, targetPath);
        // HrAppendix hrAppendix = this.hrAppendixService.uploadContractFile(targetPath);
        // hrAppendix.setFilePath(jsonObject.getString("ossUrl"));
        // return jsonObject;
    }

    /**
     * 获取并下载合同文件
     *
     * @param contractNum
     * @return cn.casair.domain.HrAppendix
     * <AUTHOR>
     * @date 2021/10/11
     **/
    public HrAppendix downloadContract(String contractNum, String phone) {
        ECloudDomain returnData = EcloudClient.downloadCont(contractNum, phone);
//        log.info("获取并下载合同文件:{}", JSON.toJSONString(returnData));
        this.judgeReturnData(returnData);
        String targetPath = tempPath + UUID.randomUUID().toString().replace("-", "") + ".pdf";
        ecloudToFile(returnData, targetPath);
        return this.hrAppendixService.uploadContractFile(targetPath);
    }

    private void ecloudToFile(ECloudDomain eCloudDomain, String targetPath) {
        JSONObject o2 = JSONObject.parseObject(eCloudDomain.getData().toString());
        JSONArray arr = (JSONArray) o2.get("bytes");
        byte[] b1 = JSON.parseObject(arr.toString(), byte[].class);
        FileUtil.writeFile(targetPath, b1);
    }
}
