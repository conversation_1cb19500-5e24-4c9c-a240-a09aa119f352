package cn.casair.service;

import cn.casair.domain.DistinguishOcr;
import cn.casair.dto.DistinguishOcrDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.verify.VerifyUserInfo;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 证件ocr识别记录服务类
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
public interface DistinguishOcrService extends IService<DistinguishOcr> {

    /**
     * 员工身份证三要素认证
     *
     * @param realName
     * @param idCardNumber
     * @param phone
     * @return void
     * <AUTHOR>
     * @date 2022/1/4
     **/
    // void threeElementsIdentification(String realName, String idCardNumber, String phone, JWTUserDTO jwtUserDTO);

    /**
     * 个人身份证三要素认证
     *
     * @param verifyUserInfo
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2021/12/29
     **/
    // Map<String, Object> threeElementsIdentification(VerifyUserInfo verifyUserInfo, Object user);

    /**
     * 个人银行卡认证
     *
     * @param verifyUserInfo
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/28
     **/
    Map<String, Object> bankVerify(VerifyUserInfo verifyUserInfo);

    /**
     * ocr识别
     *
     * @param fileUrl
     * @param cardType
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2021/12/28
     **/
    Map<String, Object> ocrVerify(String fileUrl, String cardType);

    /**
     * 创建证件ocr识别记录
     *
     * @param distinguishOcrDTO
     * @return
     */
    DistinguishOcrDTO createDistinguishOcr(DistinguishOcrDTO distinguishOcrDTO);

    /**
     * 修改证件ocr识别记录
     *
     * @param distinguishOcrDTO
     * @return
     */
    Optional<DistinguishOcrDTO> updateDistinguishOcr(DistinguishOcrDTO distinguishOcrDTO);

    /**
     * 查询证件ocr识别记录详情
     *
     * @param id
     * @return
     */
    DistinguishOcrDTO getDistinguishOcr(String id);

    /**
     * 删除证件ocr识别记录
     *
     * @param id
     */
    void deleteDistinguishOcr(String id);

    /**
     * 批量删除证件ocr识别记录
     *
     * @param ids
     */
    void deleteDistinguishOcr(List<String> ids);

    /**
     * 分页查询证件ocr识别记录
     *
     * @param distinguishOcrDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(DistinguishOcrDTO distinguishOcrDTO, Long pageNumber, Long pageSize);

    /**
     * 获取人脸核身结果
     *
     * @param params
     * @return
     */
    JSONObject getFaceNucleusResult(Map<String, Object> params);
}
