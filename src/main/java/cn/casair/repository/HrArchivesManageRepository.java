package cn.casair.repository;

import cn.casair.domain.HrArchivesManage;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.HrArchivesManageDTO;
import cn.casair.dto.HrClientDTO;
import cn.casair.dto.HrTalentStaffDTO;
import cn.casair.dto.excel.HrArchivesManageExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 档案管理数据库操作类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Repository
public interface HrArchivesManageRepository extends BaseMapper<HrArchivesManage> {

    /**
     * 根据档案编号获取数据数量
     *
     * @param archivesNum
     * @return int
     * <AUTHOR>
     * @date 2022/2/11
     **/
    int selectCountByArchivesNum(String archivesNum);

    /**
     * 获取所有有效档案信息列表
     *
     * @return java.util.List<cn.casair.domain.HrArchivesManage>
     * <AUTHOR>
     * @date 2022/2/11
     **/
    List<String> selectExistingList();

    /**
     * 批量修改档案状态
     *
     * @param ids
     * @param state
     * @return int
     * <AUTHOR>
     * @date 2022/2/9
     **/
    int updateStateByIds(@Param("ids") List<String> ids, @Param("state") Integer state);

    /**
     * 根据ids查询档案列表
     *
     * @param ids
     * @return java.util.List<cn.casair.domain.HrArchivesManage>
     * <AUTHOR>
     * @date 2022/2/9
     **/
    List<HrArchivesManage> selectListByIds(@Param("ids") List<String> ids);

    /**
     * 获取档案详情
     *
     * @param id
     * @return cn.casair.dto.HrArchivesManageDTO
     * <AUTHOR>
     * @date 2021/11/29
     **/
    HrArchivesManageDTO getHrArchivesById(@Param("id") String id);

    /**
     * 根据员工id获取档案（不区分is_delete）
     *
     * @param staffId 员工id
     * @return cn.casair.domain.HrArchivesManage
     * <AUTHOR>
     * @date 2021/10/18
     **/
    HrArchivesManage getArchivesByStaffId(@Param("staffId") String staffId);

    /**
     * 档案编号唯一性校验
     *
     * @param id
     * @param archivesNum
     * @return int
     * <AUTHOR>
     * @date 2021/10/12
     **/
    int getCountByArchivesNum(@Param("id") String id, @Param("archivesNum") String archivesNum);

    /**
     * 分页查询档案详情
     *
     * @param page
     * @param hrArchivesManageDTO
     * @param clientIds
     * @return
     */
    IPage<HrArchivesManageDTO> selectArchivesManagePage(Page<HrArchivesManage> page, @Param("param") HrArchivesManageDTO hrArchivesManageDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 分页查询未建档的员工
     *
     * @param page
     * @param hrTalentStaffDTO
     * @return
     */
    IPage<HrTalentStaffDTO> findEmployeePage(Page<HrTalentStaff> page, @Param("param") HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 分页查询未建档的客户
     *
     * @param page
     * @param hrClientDTO
     * @return
     */
    IPage<HrClientDTO> findClientPage(Page<HrClientDTO> page, @Param("param") HrClientDTO hrClientDTO);

    /**
     * 获取导出数据列表
     *
     * @param hrArchivesManageDTO
     * @param clientIds
     * @return
     */
    List<HrArchivesManageExport> selectExportListByIds(@Param("param") HrArchivesManageDTO hrArchivesManageDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 根据档案类型和员工或者单位id获取档案信息
     *
     * @param archivesType
     * @param unionId
     * @return cn.casair.dto.HrArchivesManageDTO
     * <AUTHOR>
     * @date 2021/10/13
     **/
    HrArchivesManageDTO getArchivesByTypeAndUnionId(@Param("archivesType") Integer archivesType, @Param("unionId") String unionId);

    /**
     * 根据客户id员工id获取员工档案信息
     *
     * @param staffId
     * @return cn.casair.dto.HrArchivesManageDTO
     * <AUTHOR>
     * @date 2021/10/27
     **/
    HrArchivesManageDTO getArchivesByClientIdAndStaffId(@Param("staffId") String staffId);
}
