package cn.casair.service;

import cn.casair.domain.HrArchivesDetail;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.*;
import cn.casair.dto.formdata.TemplateDealParamsDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 员工管理服务类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
public interface HrTalentStaffService extends IService<HrTalentStaff> {

    /**
     * 批量更新员工计算补缴状态
     *
     * @param hrTalentStaffDTO
     */
    void updateStaffSupplementaryPayment(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 手动处理合同续签旧数据
     *
     * @param hrContractDTO
     */
    void confirmationTelegramForRenew(HrContractDTO hrContractDTO);

    /**
     * @return void
     * <AUTHOR>
     * @date 2022/1/17
     **/
    void updateStaffFirstLoginSign();

    /**
     * 验证员工身份信息是否有效
     *
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2021/12/30
     **/
    Map<String, Object> checkStaffIdentityInfo();

    /**
     * 导入员工福利
     *
     * @param file
     * @return cn.casair.common.utils.excel.ImportResultDTO
     * <AUTHOR>
     * @date 2021/10/20
     **/
    String importEmployeeWelfare(MultipartFile file);

    /**
     * 下载员工福利导入模板
     *
     * @return void
     * <AUTHOR>
     * @date 2021/10/20
     **/
    String downloadImportTemplate();

    /**
     * 员工福利导出
     *
     * @param hrEmployeeWelfareDTO
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2021/10/20
     **/
    String exportEmployeeWelfare(HrEmployeeWelfareDTO hrEmployeeWelfareDTO, HttpServletResponse response);

    /**
     * 分页查询员工福利列表
     *
     * @param hrEmployeeWelfareDTO
     * @param pageNumber
     * @param pageSize
     * @return org.springframework.http.ResponseEntity<?>
     * <AUTHOR>
     * @date 2021/10/15
     **/
    IPage<HrEmployeeWelfareDTO> findEmployeeWelfarePage(HrEmployeeWelfareDTO hrEmployeeWelfareDTO, Long pageNumber, Long pageSize);

    /**
     * 获取员工户口所在地地址
     *
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021/9/16
     **/
    List<String> domicilePlaceList();

    /**
     * 创建员工管理
     *
     * @param hrTalentStaffDTO
     * @return
     */
    HrTalentStaffDTO createHrTalentStaff(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 修改员工管理
     *
     * @param hrTalentStaffDTO
     * @return
     */
    Optional<HrTalentStaffDTO> updateHrTalentStaff(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 查询员工管理详情
     *
     * @param id
     * @return
     */
    HrTalentStaffDTO getHrTalentStaff(String id);

    /**
     * 批量删除员工管理
     *
     * @param ids
     */
    void deleteHrTalentStaff(List<String> ids);

    /**
     * 分页查询员工管理
     *
     * @param hrTalentStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrTalentStaffDTO hrTalentStaffDTO, Long pageNumber, Long pageSize);

    /**
     * 验证证件号码有效性
     *
     * @param certificateNum 证件号码
     * @return
     */
    HrTalentStaff getStaffByIdNumber(String certificateNum);

    /**
     * 验证手机号码有效性
     *
     * @param phone 手机号码
     * @return
     */
    ResponseEntity getStaffByPhone(String phone);

    /**
     * 员工附加信息详情
     *
     * @param id 员工ID
     * @return
     */
    ExtrasDTO getExtrasById(String id);

    /**
     * 查看报名详情
     *
     * @param hrRegistrationDetailsDTO
     * @return
     */
    ExtrasDTO getRegistrationDetails(HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    /**
     * 人才附件上传
     *
     * @param staffId 人才ID
     * @param file    附件
     * @return
     */
    HrAppendixDTO uploadSingleFile(String staffId, MultipartFile file);

    /**
     * 人才附件删除
     *
     * @param staffId    人才ID
     * @param appendixId 附件ID
     */
    void deleteMultipleFile(String staffId, String appendixId);

    /**
     * 附件详情
     *
     * @param id 人才ID
     * @return
     */
    HrTalentStaffDTO getAppendixById(String id);

    /**
     * 员工导出信息
     *
     * @param hrTalentStaffDTO
     * @param response
     * @return
     */
    String exportHrStaff(HrTalentStaffDTO hrTalentStaffDTO, HttpServletResponse response);

    /**
     * 人才导出信息
     *
     * @param hrTalentStaffDTO
     * @param response
     * @return
     */
    String exportHrTalent(HrTalentStaffDTO hrTalentStaffDTO, HttpServletResponse response);

    /**
     * 人才导入信息
     *
     * @param file
     * @return
     */
    String importHrTalent(MultipartFile file);

    /**
     * 员工信息导入
     *
     * @param file
     * @return
     */
    String importHrStaff(MultipartFile file);

    /**
     * 人才导入模板
     *
     * @param response
     * @return
     */
    String importHrTalentTemplate(HttpServletResponse response);

    /**
     * 员工导入模板
     *
     * @param response
     */
    String importHrStaffTemplate(HttpServletResponse response);

    /**
     * 人才删除
     *
     * @param ids
     */
    void deleteTalentedPerson(List<String> ids);

    /**
     * 入职资料-基本信息
     *
     * @param id
     * @return
     */
    HrTalentStaffDTO getStaffBasicInfo(String id);

    /**
     * 入职资料-保存基本信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    Optional<HrTalentStaffDTO> fillStaffBasicInfo(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 员工入职资料填写状态
     *
     * @param id 员工状态
     * @return
     */
    Map<String, Object> getEntryInfoStatusById(String id);

    /**
     * 员工列表查询入职进度
     *
     * @param id 员工ID
     * @return
     */
    HrApplyEntryStaffDTO getSecondaryInfo(String id);

    /**
     * 查询员工入离职信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    Map<String, List> searchEntryHrStaff(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 导出员工入离职信息
     *
     * @param hrTalentStaffDTO 员工ID集合
     * @param response
     */
    String exportEntryHrStaff(HrTalentStaffDTO hrTalentStaffDTO, HttpServletResponse response);

    List<HrQuestionDTO> getHrPaperManagementSelect();

    /**
     * 服务中心--根据名称以及身份证查询数据
     *
     * @param hrTalentStaffDTO 名称/身份证
     * @return
     */
    List<HrTalentStaffDTO> searchDataHrStaff(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 检查并更改离职员工数据
     */
    void updateStaffStatus();

    /**
     * 单人续签-合同模板预处理
     *
     * @param params
     * @return
     */
    TemplateDealParamsDTO renewalGenerateContract(TemplateDealParamsDTO params);

    /**
     * 医疗备案页面数据
     *
     * @param staffId 员工ID
     * @return
     */
    HrTalentStaffDTO obtainMedicalRecordInfo(String staffId);

    /**
     * 医疗备案日期更新
     *
     * @param params
     * @return
     */
    void modifyMedicalRecordInfo(Map<String, Object> params);

    /**
     * 查询员工对应的档案明细
     *
     * @return
     */
    List<HrArchivesDetail> fileDetailsStaff();

    /**
     * 单人续签--确认发送
     *
     * @param electricSignDTO
     */
    void renewalSendConfirm(ElectricSignDTO electricSignDTO);

    /**
     * 员工签署合同
     * @param contractId 合同ID
     */
    void renewalSignContract(String contractId);

    /**
     * 单人续签--合同模板项制作确认
     *
     * @param hrContractAppendixDTO
     */
    void renewalContractConfirmation(HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 待签订续签劳动合同列表
     *
     * @param contractId
     * @return
     */
    Map<String, Object> getRenewalContractList(String contractId);

    /**
     * 确认电签--续签
     *
     * @param hrContractDTO
     */
    void renewalConfirmationTelegram(HrContractDTO hrContractDTO);

    /**
     * 我的合同
     *
     * @return
     */
    List<HrContractDTO> myContract();

    void setDict(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 员工信息--关联信息
     *
     * @param id 员工ID
     * @return
     */
    ExtrasDTO staffRelatedInformation(String id);

    /**
     * 不分页查询员工信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    List<HrTalentStaffDTO> findNotPageHrTalentStaff(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 批量续签--合同模板预处理
     *
     * @param params
     * @return
     */
    TemplateDealParamsDTO batchRenewalGenerateContract(TemplateDealParamsDTO params);

    /**
     * 批量续签-制作合同确定
     *
     * @param params
     **/
    void batchRenewalConfirmation(TemplateDealParamsDTO params);

    /**
     * 批量续签-确认发送
     *
     * @param electricSignDTO
     */
    void renewalSendConfirmBatch(ElectricSignDTO electricSignDTO);

    /**
     * 批量续签--批量确认通过
     *
     * @param electricSignDTO
     */
    void batchRenewalConfirm(ElectricSignDTO electricSignDTO);

    /**
     * 续签服务列表查询
     *
     * @param hrTalentStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrTalentStaffDTO> findPageRenewal(HrTalentStaffDTO hrTalentStaffDTO, Long pageNumber, Long pageSize);

    /**
     * 导出
     *
     * @param hrTalentStaffDTO
     * @return
     */
    String export(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 续签服务详情
     *
     * @param id 员工ID
     * @return
     */
    HrTalentStaffDTO getRenewalInfo(String id);

    /**
     * 导出续签附件信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    List<ContractAppendixDownloadDTO> exportRenewal(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 每天0点检测员工续签状态
     */
    void updateStaffRenewalContract();

    /**
     * 获取当前员工客户发放银行
     *
     * @return
     */
    HrClientDTO findClientPlatformAccount();

    /**
     * 批量处理之前合同
     *
     * @param serviceType 1入职合同 2续签合同
     * @param state       1通过 3不通过
     * @param idNoList
     * @return
     */
    void batchHandleContract(Integer serviceType, Integer state, List<String> idNoList);

    /**
     * 修改员工微信openid
     *
     * @param staffIds 员工Id
     * @param openId
     */
    void updateOpenIdByStaffId(List<String> staffIds, String openId);

    /**
     * 手动修改员工入职状态
     *
     * @param staffIds
     * @param process
     */
    void manualUpdateStaffInductionProcess(List<String> staffIds, Integer process);

    /**
     * 小程序识别跳过
     * @param distinguishDTO
     */
    void distinguishCloud(HrDistinguishDTO distinguishDTO);

    /**
     * 小程序员工跳过认证标识
     * @return false 正常认证 true跳过认证
     */
    Boolean findDistinguishVerify();

    /**
     * 每天0点处理员工在职信息
     */
    void handleStaffWorkExperience();
}
