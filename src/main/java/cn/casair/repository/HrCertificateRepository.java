package cn.casair.repository;
import cn.casair.domain.HrCertificate;
import cn.casair.dto.HrCertificateDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 证件管理数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Repository
public interface HrCertificateRepository extends BaseMapper<HrCertificate> {

    /**
     * 根基id获取
     *
     * @param id
     * @return cn.casair.dto.HrCertificateDTO
     * <AUTHOR>
     * @date 2021/9/21
     **/
    HrCertificateDTO getById(@Param("id") String id);

    HrCertificateDTO selectCertificateName(String certificateName);
}
