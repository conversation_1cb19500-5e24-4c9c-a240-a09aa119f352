<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrDockingRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, protocol_id, docking, docking_phone, department,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrDocking">
                    <id column="id" property="id"/>
                    <result column="protocol_id" property="protocolId"/>
                    <result column="docking" property="docking"/>
                    <result column="docking_phone" property="dockingPhone"/>
                    <result column="department" property="department"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

</mapper>
