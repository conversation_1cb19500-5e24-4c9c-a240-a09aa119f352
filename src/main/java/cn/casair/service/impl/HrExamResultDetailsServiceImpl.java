package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.dto.HrAccumulationFundDTO;
import cn.casair.dto.HrExamDetailsDTO;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrExamResultDetails;
import cn.casair.dto.HrExamResultDetailsDTO;
import cn.casair.repository.HrExamResultDetailsRepository;
import cn.casair.mapper.HrExamResultDetailsMapper;
import cn.casair.service.HrExamResultDetailsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Optional;
import java.util.List;

/**
 * 考生答案表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrExamResultDetailsServiceImpl extends ServiceImpl<HrExamResultDetailsRepository, HrExamResultDetails> implements HrExamResultDetailsService {

    private static final Logger log = LoggerFactory.getLogger(HrExamResultDetailsServiceImpl.class);

    private final HrExamResultDetailsRepository hrExamResultDetailsRepository;

    private final HrExamResultDetailsMapper hrExamResultDetailsMapper;

    private final SysOperLogService sysOperLogService;

    public HrExamResultDetailsServiceImpl(HrExamResultDetailsRepository hrExamResultDetailsRepository, HrExamResultDetailsMapper hrExamResultDetailsMapper, SysOperLogService sysOperLogService) {
        this.hrExamResultDetailsRepository = hrExamResultDetailsRepository;
        this.hrExamResultDetailsMapper = hrExamResultDetailsMapper;
        this.sysOperLogService = sysOperLogService;
    }

    /**
     * 创建考生答案表
     *
     * @param hrExamResultDetailsDTO
     * @return
     */
    @Override
    public HrExamResultDetailsDTO createHrExamResultDetails(HrExamResultDetailsDTO hrExamResultDetailsDTO) {
        log.info("Create new HrExamResultDetails:{}", hrExamResultDetailsDTO);

        HrExamResultDetails hrExamResultDetails = this.hrExamResultDetailsMapper.toEntity(hrExamResultDetailsDTO);
        this.hrExamResultDetailsRepository.insert(hrExamResultDetails);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.EXAM_RESULT_DETAILS.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrExamResultDetailsDTO),
            HrExamResultDetailsDTO.class,
            null,
            JSON.toJSONString(hrExamResultDetailsDTO)
        );
        return this.hrExamResultDetailsMapper.toDto(hrExamResultDetails);
    }

    /**
     * 修改考生答案表
     *
     * @param hrExamResultDetailsDTO
     * @return
     */
    @Override
    public Optional<HrExamResultDetailsDTO> updateHrExamResultDetails(HrExamResultDetailsDTO hrExamResultDetailsDTO) {
        return Optional.ofNullable(this.hrExamResultDetailsRepository.selectById(hrExamResultDetailsDTO.getId()))
            .map(roleTemp -> {
                HrExamResultDetails hrExamResultDetails = this.hrExamResultDetailsMapper.toEntity(hrExamResultDetailsDTO);
                this.hrExamResultDetailsRepository.updateById(hrExamResultDetails);
                log.info("Update HrExamResultDetails:{}", hrExamResultDetailsDTO);
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.EXAM_RESULT_DETAILS.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrExamResultDetailsDTO),
                    HrExamResultDetailsDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrExamResultDetailsDTO),
                    null,
                    HrExamResultDetailsDTO.class
                );
                return hrExamResultDetailsDTO;
            });
    }

    /**
     * 查询考生答案表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrExamResultDetailsDTO getHrExamResultDetails(String id) {
        log.info("Get HrExamResultDetails :{}", id);

        HrExamResultDetails hrExamResultDetails = this.hrExamResultDetailsRepository.selectById(id);
        return this.hrExamResultDetailsMapper.toDto(hrExamResultDetails);
    }

    /**
     * 删除考生答案表
     *
     * @param id
     */
    @Override
    public void deleteHrExamResultDetails(String id) {
        Optional.ofNullable(this.hrExamResultDetailsRepository.selectById(id))
            .ifPresent(hrExamResultDetails -> {
                this.hrExamResultDetailsRepository.deleteById(id);
                log.info("Delete HrExamResultDetails:{}", hrExamResultDetails);
            });
    }

    /**
     * 批量删除考生答案表
     *
     * @param ids
     */
    @Override
    public void deleteHrExamResultDetails(List<String> ids) {
        log.info("Delete HrExamResultDetailss:{}", ids);
        this.hrExamResultDetailsRepository.deleteBatchIds(ids);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.EXAM_RESULT_DETAILS.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
    }

    /**
     * 分页查询考生答案表
     *
     * @param hrExamResultDetailsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrExamResultDetailsDTO hrExamResultDetailsDTO, Long pageNumber, Long pageSize) {
        Page<HrExamResultDetails> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrExamResultDetails> qw = new QueryWrapper<>(this.hrExamResultDetailsMapper.toEntity(hrExamResultDetailsDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrExamResultDetailsRepository.selectPage(page, qw);
        iPage.setRecords(this.hrExamResultDetailsMapper.toDto(iPage.getRecords()));
        return iPage;
    }
}
