package cn.casair.repository;

import cn.casair.domain.HrStaffSignCert;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 入职员工签名证书（易云章）数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Repository
public interface HrStaffSignCertRepository extends BaseMapper<HrStaffSignCert> {

    /**
     * 获取员工易云章证书
     *
     * @param query
     * @return cn.casair.domain.HrStaffSignCert
     * <AUTHOR>
     * @date 2021/9/22
     **/
    HrStaffSignCert selectByObject(Map<String, Object> query);

    /**
     * 根据员工id获取员工易云章证书信息
     *
     * @param staffId
     * @return cn.casair.domain.HrStaffSignCert
     * <AUTHOR>
     * @date 2021/9/28
     **/
    HrStaffSignCert getByStaffId(@Param("staffId") String staffId);
}
