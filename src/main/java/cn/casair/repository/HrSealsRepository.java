package cn.casair.repository;
import cn.casair.domain.HrSeals;
import cn.casair.dto.HrSealsDTO;
import cn.casair.dto.formdata.SelectionOption;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@Repository
public interface HrSealsRepository extends BaseMapper<HrSeals> {

    /**
     * 获取有效的印章列表
     *
     * @return
     */
    List<HrSealsDTO> getEffectiveSealses();

    /**
     * 获取企业公章
     *
     * @return cn.casair.domain.HrSeals
     * <AUTHOR>
     * @date 2021/9/29
     **/
    HrSeals getEnterpriseOfficialSeal();

    /**
     * 获取企业公章
     *
     * @return cn.casair.domain.HrSeals
     * <AUTHOR>
     * @date 2021/9/17
     **/
    List<SelectionOption> getOfficialSeal();

    List<HrSealsDTO> getSealses();

    void delectSeals(HrSealsDTO hrSealsDTO);

    int selectSeals(String sealName);

    List<SelectionOption> getOfficialSealCondition(@Param("list") List<String> stringList);

}
