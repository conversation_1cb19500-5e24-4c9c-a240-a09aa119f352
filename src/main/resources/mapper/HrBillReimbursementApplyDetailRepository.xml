<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillReimbursementApplyDetailRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        apply_id,
        invoice_type,
        content,
        amount,
        is_delete ,
        created_by ,
        last_modified_by ,
        created_date ,
        last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillReimbursementApplyDetail">
        <id column="id" property="id"/>
        <result column="apply_id" property="applyId"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="content" property="content"/>
        <result column="amount" property="amount"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getByApplyId" resultType="cn.casair.dto.HrBillReimbursementApplyDetailDTO">
        WITH t1 AS (SELECT * FROM code_table ct WHERE ct.is_delete = 0 AND ct.parent_id = (SELECT id FROM code_table c WHERE c.inner_name = 'REIMBURSEMENT_CONTENT'))
        SELECT dl.*, ct.item_name AS invoiceTypeName FROM hr_bill_reimbursement_apply_detail dl LEFT JOIN t1 ct ON ct.item_value = dl.invoice_type
        WHERE dl.is_delete = 0 AND dl.apply_id = #{applyId}
    </select>

    <select id="getByApplyIdList" resultType="cn.casair.dto.HrBillReimbursementApplyDetailDTO">
        WITH t1 AS (
            SELECT
            *
            FROM
            code_table ct
            WHERE
            ct.is_delete = 0
            AND ct.parent_id = ( SELECT id FROM code_table c WHERE c.inner_name = 'REIMBURSEMENT_CONTENT' )) SELECT
            hc.client_name,hbr.title,
            dl.*,
            ct.item_name AS invoiceTypeName
        FROM
            hr_bill_reimbursement_apply_detail dl
        LEFT JOIN hr_bill_reimbursement_apply hbr ON hbr.id = dl.apply_id
        LEFT JOIN hr_client hc ON hc.id = hbr.client_id
        LEFT JOIN t1 ct ON ct.item_value = dl.invoice_type
        WHERE
        dl.is_delete = 0
        AND dl.apply_id IN
        <foreach collection="idList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

    <select id="findListByApplyId" resultType="cn.casair.dto.HrBillReimbursementClientDTO">
         WITH t1 AS (
            SELECT
            *
            FROM
            code_table ct
            WHERE
            ct.is_delete = 0
            AND ct.parent_id = ( SELECT id FROM code_table c WHERE c.inner_name = "REIMBURSEMENT_CONTENT" )
        ) SELECT
            hc.client_name,
            hbr.title,
            hbr.account_type,
            dl.*,
            ct.item_name AS invoiceTypeName,
            hp.account_number
        FROM
            hr_bill_reimbursement_client dl
        LEFT JOIN hr_client hc ON dl.client_id = hc.id
        LEFT JOIN hr_platform_account hp ON dl.account_id = hp.id
        LEFT JOIN hr_bill_reimbursement_apply hbr ON hbr.id = dl.apply_id
        LEFT JOIN t1 ct ON ct.item_value = hbr.account_type
        WHERE dl.is_delete = 0 AND dl.apply_id IN
        <foreach collection="idList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
        ORDER BY hp.account_number
    </select>

    <select id="getDetailClientById" resultType="cn.casair.dto.HrBillReimbursementClientDTO">
        SELECT
            brdc.detail_id,
            brdc.client_bill_id,
            brc.*
        FROM
            hr_bill_reimbursement_detail_client brdc
        LEFT JOIN hr_bill_reimbursement_client brc ON brdc.client_bill_id = brc.id
        WHERE brdc.is_delete = 0
        <if test="detailIds != null and detailIds.size() > 0">
            AND brdc.detail_id IN
            <foreach collection="detailIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="clientBillIds != null and clientBillIds.size() > 0">
            AND brdc.client_bill_id IN
            <foreach collection="clientBillIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
    </select>

    <select id="getDetailInfo" resultType="cn.casair.dto.HrBillReimbursementApplyDetailDTO">
        SELECT
            hbrd.*
        FROM
            hr_bill_reimbursement_apply_detail hbrd
            LEFT JOIN hr_bill_reimbursement_apply hbra ON hbrd.apply_id = hbra.id
        WHERE hbrd.is_delete = 0
        <if test="approveStatus != null">
            AND hbra.approve_status = #{approveStatus}
        </if>
        <if test="detailIds != null and detailIds.size() > 0">
            AND hbrd.id IN
            <foreach collection="detailIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="parentId != null and parentId != ''">
            AND hbrd.parent_id = #{parentId}
        </if>
        <if test="invoiceType != null">
            AND hbrd.invoice_type = #{invoiceType}
        </if>
    </select>

    <select id="getDetailStatusByApplyId" resultType="cn.casair.dto.HrBillReimbursementApplyDetailDTO">
        SELECT
            d1.*,
            bra.approve_status
        FROM
            hr_bill_reimbursement_apply_detail d1
            LEFT JOIN hr_bill_reimbursement_apply_detail d2 ON d1.level_id = d2.id AND d2.is_delete = 0
            LEFT JOIN hr_bill_reimbursement_apply bra ON d2.apply_id = bra.id
        WHERE
            d1.is_delete = 0
            AND d1.apply_id = #{applyId}
    </select>

    <update id="updateState">
        UPDATE hr_bill_reimbursement_apply_detail SET state = #{state} WHERE is_delete = 0 AND id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>


    <insert id="insertDetailClient">
        INSERT INTO `hr_bill_reimbursement_detail_client` (
            `id`,
            `detail_id`,
            `client_bill_id`,
            `created_by`,
            `created_date`
        )
        VALUES
        (#{param.id},
        #{param.detailId},
        #{param.clientBillId},
        #{param.createdBy},
        #{param.createdDate})
    </insert>

    <update id="deleteDetailClient">
        update hr_bill_reimbursement_detail_client dl set dl.is_delete = 1 where dl.detail_id in
        <foreach collection="detailIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>
</mapper>
