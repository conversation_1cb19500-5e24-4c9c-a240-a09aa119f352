package cn.casair.service.impl;

import cn.casair.domain.HrOriginalSalary;
import cn.casair.dto.HrOriginalSalaryDTO;
import cn.casair.mapper.HrOriginalSalaryMapper;
import cn.casair.repository.HrClientRepository;
import cn.casair.repository.HrOriginalSalaryRepository;
import cn.casair.service.HrClientService;
import cn.casair.service.HrOriginalSalaryService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 薪酬原单（客户）服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrOriginalSalaryServiceImpl extends ServiceImpl<HrOriginalSalaryRepository, HrOriginalSalary> implements HrOriginalSalaryService {

    private static final Logger log = LoggerFactory.getLogger(HrOriginalSalaryServiceImpl.class);
    private final HrClientService hrClientService;

    private final HrOriginalSalaryRepository hrOriginalSalaryRepository;
    private final HrClientRepository hrClientRepository;

    private final HrOriginalSalaryMapper hrOriginalSalaryMapper;

    public HrOriginalSalaryServiceImpl(HrClientService hrClientService, HrOriginalSalaryRepository hrOriginalSalaryRepository, HrClientRepository hrClientRepository, HrOriginalSalaryMapper hrOriginalSalaryMapper) {
        this.hrClientService = hrClientService;
        this.hrOriginalSalaryRepository = hrOriginalSalaryRepository;
        this.hrClientRepository = hrClientRepository;
        this.hrOriginalSalaryMapper = hrOriginalSalaryMapper;
    }

    /**
     * 创建薪酬原单（客户）
     *
     * @param hrOriginalSalaryDTO
     * @return
     */
    @Override
    public HrOriginalSalaryDTO createHrOriginalSalary(HrOriginalSalaryDTO hrOriginalSalaryDTO) {
        log.info("Create new HrOriginalSalary:{}", hrOriginalSalaryDTO);

        HrOriginalSalary hrOriginalSalary = this.hrOriginalSalaryMapper.toEntity(hrOriginalSalaryDTO);
        this.hrOriginalSalaryRepository.insert(hrOriginalSalary);
        return this.hrOriginalSalaryMapper.toDto(hrOriginalSalary);
    }

    /**
     * 修改薪酬原单（客户）
     *
     * @param hrOriginalSalaryDTO
     * @return
     */
    @Override
    public Optional<HrOriginalSalaryDTO> updateHrOriginalSalary(HrOriginalSalaryDTO hrOriginalSalaryDTO) {
        return Optional.ofNullable(this.hrOriginalSalaryRepository.selectById(hrOriginalSalaryDTO.getId()))
            .map(roleTemp -> {
                HrOriginalSalary hrOriginalSalary = this.hrOriginalSalaryMapper.toEntity(hrOriginalSalaryDTO);
                this.hrOriginalSalaryRepository.updateById(hrOriginalSalary);
                log.info("Update HrOriginalSalary:{}", hrOriginalSalaryDTO);
                return hrOriginalSalaryDTO;
            });
    }

    /**
     * 查询薪酬原单（客户）详情
     *
     * @param id
     * @return
     */
    @Override
    public HrOriginalSalaryDTO getHrOriginalSalary(String id) {
        log.info("Get HrOriginalSalary :{}", id);

        HrOriginalSalary hrOriginalSalary = this.hrOriginalSalaryRepository.selectById(id);
        return this.hrOriginalSalaryMapper.toDto(hrOriginalSalary);
    }

    /**
     * 删除薪酬原单（客户）
     *
     * @param id
     */
    @Override
    public void deleteHrOriginalSalary(String id) {
        Optional.ofNullable(this.hrOriginalSalaryRepository.selectById(id))
            .ifPresent(hrOriginalSalary -> {
                this.hrOriginalSalaryRepository.deleteById(id);
                log.info("Delete HrOriginalSalary:{}", hrOriginalSalary);
            });
    }

    /**
     * 批量删除薪酬原单（客户）
     *
     * @param ids
     */
    @Override
    public void deleteHrOriginalSalary(List<String> ids) {
        log.info("Delete HrOriginalSalarys:{}", ids);
        this.hrOriginalSalaryRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询薪酬原单（客户）
     *
     * @param hrOriginalSalaryDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrOriginalSalaryDTO hrOriginalSalaryDTO, Long pageNumber, Long pageSize) {
        Page<HrOriginalSalary> page = new Page<>(pageNumber, pageSize);
        if (CollectionUtils.isEmpty(hrOriginalSalaryDTO.getClientIdList())) {
            List<String> clientId = this.hrClientService.selectClientIdByUserId();
            hrOriginalSalaryDTO.setClientIdList(clientId);
        }
        IPage<HrOriginalSalaryDTO> iPage = this.hrOriginalSalaryRepository.selectFiPage(page, hrOriginalSalaryDTO);
        for (HrOriginalSalaryDTO record : iPage.getRecords()) {
            String costDate = record.getPayYear() + "-" + record.getPayMonthly();
            record.setCostDate(costDate);
        }

        return iPage;
    }


    @Override
    public List<HrOriginalSalary> selectHrOriginalSalary(List<String> ids) {
        List<HrOriginalSalary> list = this.hrOriginalSalaryRepository.selectBatchIds(ids);
        return list;
    }

    @Override
    public List<HrOriginalSalary> getHrOriginalSalarySelect(HrOriginalSalaryDTO hrOriginalSalaryDTO) {
        QueryWrapper<HrOriginalSalary> qw = new QueryWrapper<>();
        qw.eq("client_id", hrOriginalSalaryDTO.getClientId());
        qw.eq("pay_year", hrOriginalSalaryDTO.getPayYear());
        qw.eq("pay_monthly", hrOriginalSalaryDTO.getPayMonthly());
        qw.eq("is_delete", "0");
        return this.hrOriginalSalaryRepository.selectList(qw);
    }

    @Override
    public List<HrOriginalSalaryDTO> findList(HrOriginalSalaryDTO hrOriginalSalaryDTO) {
        if (CollectionUtils.isEmpty(hrOriginalSalaryDTO.getClientIdList())) {
            List<String> clientId = this.hrClientService.selectClientIdByUserId();
            hrOriginalSalaryDTO.setClientIdList(clientId);
        }
        List<HrOriginalSalaryDTO> hrOriginalSalaryDTOS = hrOriginalSalaryRepository.findList(hrOriginalSalaryDTO);
        return hrOriginalSalaryDTOS;
    }
}
