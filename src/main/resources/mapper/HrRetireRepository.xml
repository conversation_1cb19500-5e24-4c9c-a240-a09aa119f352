<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRetireRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, staff_id, original_retire _date, actual_retire _date, operate_cont, remark, status,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrRetire">
                    <id column="id" property="id"/>
                    <result column="staff_id" property="staffId"/>
                    <result column="original_retire_date" property="originalRetireDate"/>
                    <result column="actual_retire_date" property="actualRetireDate"/>
                    <result column="operate_cont" property="operateCont"/>
                    <result column="remark" property="remark"/>
                    <result column="status" property="status"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

        <select id="selectDetatil" resultType="cn.casair.dto.HrRetireDTO">
            SELECT
                hr.id,
                hr.apply_retire_date,
                hr.apply_description,
                hr.staff_id,
                ht.client_id,
                hc.client_name,
                ht.`name`,
                ht.certificate_num,
                ht.sex,
                ht.phone,
                hs.profession_name,
                hs.id,
                ht.personnel_type,
                hr.`status`,
                su.real_name AS specialized,
                su.id,
                hr.`actual_retire_date`,
                hr.`original_retire_date`,
                hr.operate_cont,
                hr.remark,
                hse.basic_wage,
                hse.social_security_cardinal,
                hse.medical_insurance_cardinal,
                hse.accumulation_fund_cardinal,
                hce.contract_start_date,
                hce.contract_end_date
            FROM
                hr_retire hr
                    LEFT JOIN hr_talent_staff ht ON hr.staff_id = ht.id
                    AND ht.is_delete = 0
                    LEFT JOIN hr_staff_work_experience hsw ON hr.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id
                    AND hsw.is_delete = 0
                    AND hsw.iz_default = 1
                    LEFT JOIN hr_station hs ON hsw.station_id = hs.id
                    AND hs.is_delete = 0
                    LEFT JOIN hr_client hc ON hc.id = hr.client_id
                    AND hc.is_delete = 0
                    LEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id
                    AND huc.is_delete = 0
                    AND huc.is_specialized = 1
                    LEFT JOIN sys_user su ON huc.user_id = su.id
                    AND su.is_delete = 0
                    LEFT JOIN hr_staff_emolument hse ON hse.staff_id = ht.id
                    AND hse.is_delete = 0
                    LEFT JOIN (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
                               FROM (SELECT * FROM hr_contract WHERE is_delete = 0 AND state in (1,2) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) hce ON hce.staff_id = ht.id
                    AND hce.is_delete = 0
            WHERE
                hr.is_delete = 0 AND ht.is_delete = 0 AND hr.id = #{id}
    </select>

        <select id="exportRetire" resultType="cn.casair.dto.excel.HrRetireExport">
            SELECT
                hr.id,
                hr.apply_retire_date,
                hr.apply_description,
                ht.client_id,
                hc.client_name,
                ht.`name`,
                ht.staff_status,
                ht.certificate_num,
                ht.sex,
                IF (ht.sex = 1, '男', '女') sexName,
                ht.phone,
                hs.profession_name,
                hs.id,
                ht.personnel_type,
                hr.`status`,
                su.real_name AS specialized,
                su.id,
                hr.`actual_retire_date`,
                hr.`original_retire_date`,
                hr.operate_cont,
                hr.remark
            FROM
                hr_retire hr
                    LEFT JOIN hr_talent_staff ht ON hr.staff_id = ht.id
                    AND ht.is_delete = 0
                    LEFT JOIN hr_staff_work_experience hsw ON hr.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id
                    AND hsw.is_delete = 0
                    AND hsw.iz_default = 1
                    LEFT JOIN hr_station hs ON hsw.station_id = hs.id
                    AND hs.is_delete = 0
                    LEFT JOIN hr_client hc ON hc.id = hr.client_id
                    AND hc.is_delete = 0
                    LEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id
                    AND huc.is_delete = 0
                    AND huc.is_specialized = 1
                    LEFT JOIN sys_user su ON huc.user_id = su.id
                    AND su.is_delete = 0
            ${ew.customSqlSegment}
    </select>
</mapper>
