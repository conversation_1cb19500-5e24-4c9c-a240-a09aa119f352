package cn.casair.service.impl;

import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.domain.*;
import cn.casair.dto.HrNotificationUserDTO;
import cn.casair.dto.UserDTO;
import cn.casair.mapper.HrNotificationUserMapper;
import cn.casair.repository.*;
import cn.casair.service.HrClientService;
import cn.casair.service.HrNotificationUserService;
import cn.casair.service.UserRoleService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrNotificationUserServiceImpl extends ServiceImpl<HrNotificationUserRepository, HrNotificationUser>implements HrNotificationUserService {

    private static final Logger log=LoggerFactory.getLogger(HrNotificationUserServiceImpl.class);

    private final HrNotificationUserRepository hrNotificationUserRepository;

    private final HrNotificationUserMapper hrNotificationUserMapper;

    public HrNotificationUserServiceImpl(HrNotificationUserRepository hrNotificationUserRepository, HrNotificationUserMapper hrNotificationUserMapper){
    this.hrNotificationUserRepository = hrNotificationUserRepository;
    this.hrNotificationUserMapper= hrNotificationUserMapper;
    }
    @Resource
    private HrNotificationMessageRepository hrNotificationMessageRepository;
    @Resource
    private HrNotificationUserContentRepository hrNotificationUserContentRepository;
    @Resource
    private HrMessageListRepository hrMessageListRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private HrClientService hrClientService;
    @Resource
    private UserRoleService userRoleService;
    @Resource
    private HrMessageRoleRepository hrMessageRoleRepository;

    /**
     * 创建
     * @param hrNotificationUserDTO
     * @return
     */
    @Override
    public HrNotificationUserDTO createHrNotificationUser(HrNotificationUserDTO hrNotificationUserDTO){
    log.info("Create new HrNotificationUser:{}", hrNotificationUserDTO);
    HrNotificationUser hrNotificationUser =this.hrNotificationUserMapper.toEntity(hrNotificationUserDTO);
    this.hrNotificationUserRepository.insert(hrNotificationUser);
    return this.hrNotificationUserMapper.toDto(hrNotificationUser);
    }

    /**
     * 修改
     * @param hrNotificationUserDTO
     * @return
     */
    @Override
    public Optional<HrNotificationUserDTO>updateHrNotificationUser(HrNotificationUserDTO hrNotificationUserDTO){
    return Optional.ofNullable(this.hrNotificationUserRepository.selectById(hrNotificationUserDTO.getId()))
    .map(roleTemp->{
    HrNotificationUser hrNotificationUser =this.hrNotificationUserMapper.toEntity(hrNotificationUserDTO);
    this.hrNotificationUserRepository.updateById(hrNotificationUser);
    log.info("Update HrNotificationUser:{}", hrNotificationUserDTO);
    return hrNotificationUserDTO;
    });
    }

    /**
     * 查询详情
     * @param id
     * @return
     */
    @Override
    public HrNotificationUserDTO getHrNotificationUser(String id){
    log.info("Get HrNotificationUser :{}",id);

    HrNotificationUser hrNotificationUser =this.hrNotificationUserRepository.selectById(id);
    return this.hrNotificationUserMapper.toDto(hrNotificationUser);
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void deleteHrNotificationUser(String id){
    Optional.ofNullable(this.hrNotificationUserRepository.selectById(id))
    .ifPresent(hrNotificationUser ->{
    this.hrNotificationUserRepository.deleteById(id);
    log.info("Delete HrNotificationUser:{}", hrNotificationUser);
    });
    }

    /**
     * 批量删除
     * @param ids
     */
    @Override
    public void deleteHrNotificationUser(List<String>ids){
    log.info("Delete HrNotificationUsers:{}",ids);
    this.hrNotificationUserRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询
     * @param hrNotificationUserDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrNotificationUserDTO hrNotificationUserDTO,Long pageNumber,Long pageSize){
    Page<HrNotificationUser>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrNotificationUser>qw=new QueryWrapper<>(this.hrNotificationUserMapper.toEntity(hrNotificationUserDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrNotificationUserRepository.selectPage(page,qw);
    iPage.setRecords(this.hrNotificationUserMapper.toDto(iPage.getRecords()));
    return iPage;
    }

    /**
     * 保存提醒内容信息
     * @param clientId 申请人所属客户
     * @param applyType 申请类型
     * @param launchType 提醒类型
     * @param content 提醒内容
     * @param loginId 创建人
     */
    @Override
    public void saveRemindContent(String clientId, Integer applyType, Integer launchType,String content, String loginId) {
        //查询启用的入职申请的通知配置
        String notificationId = this.hrNotificationMessageRepository.selectOne(new QueryWrapper<HrNotificationMessage>().eq("notification_value", applyType)).getId();
        String notificationContentId = this.hrNotificationMessageRepository.getNotificationMessageId(launchType,notificationId);
        List<UserRole> userRoles = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        if (StringUtils.isNotBlank(notificationId) && StringUtils.isNotBlank(notificationId)){
            List<HrNotificationUser> hrNotificationUserList = this.hrNotificationUserRepository.selectList(
                new QueryWrapper<HrNotificationUser>().eq("states", "0").eq("notification_id", notificationId).eq("notification_content_id", notificationContentId));
            for (HrNotificationUser hrNotificationUser : hrNotificationUserList) {
                //判断提醒方式是否需要提醒
                if (StringUtils.isNotBlank(hrNotificationUser.getReminderMethod())){
                    //获取每个角色所绑定的客户
                    String userId = hrNotificationUser.getUserId();
                    UserDTO user = userRepository.getUserInFor(userId);
                    if (user!=null){
                        List<String> clientIds = hrClientService.noticeAuthority(user.getRoleKey(), userId);//这个用户所绑定的客户
                        if (clientIds.contains(clientId)){
                            HrNotificationUserContent hrNotificationUserContent = new HrNotificationUserContent();
                            hrNotificationUserContent.setNotificationUserId(hrNotificationUser.getId()).setNotificationContent(content);
                            switch (hrNotificationUser.getReminderMethod()){
                                case "1"://1.是提醒框
                                    hrNotificationUserContentRepository.insert(hrNotificationUserContent);
                                    break;
                                case "2":
                                    //查询用户所对应的角色
                                    userIds.add(user.getId());
                                    List<UserRole> userRoleList = this.userRoleService.list(new QueryWrapper<UserRole>().eq("user_id", user.getId()));
                                    if (!userRoleList.isEmpty()){
                                        userRoles.addAll(userRoleList);
                                    }
                                    break;
                                default:
                                    //全部
                                    hrNotificationUserContentRepository.insert(hrNotificationUserContent);
                                    //查询用户所对应的角色
                                    userIds.add(user.getId());
                                    List<UserRole> userRoleListS = this.userRoleService.list(new QueryWrapper<UserRole>().eq("user_id", user.getId()));
                                    if (!userRoleListS.isEmpty()){
                                        userRoles.addAll(userRoleListS);
                                    }
                            }
                        }
                    }
                }
            }
            if (!userIds.isEmpty()){
                List<Integer> roleList = userRoles.stream().map(UserRole::getRoleId).distinct().collect(Collectors.toList());
                HrMessageList hrMessageList = new HrMessageList();
                hrMessageList.setTitle(ServiceCenterEnum.getValueByKey(applyType));
                hrMessageList.setType("通知");
                hrMessageList.setCreatedById(loginId);
                hrMessageList.setRecipientRoleIds(Joiner.on(",").join(roleList));
                hrMessageList.setContent(content);
                hrMessageListRepository.insert(hrMessageList);
                for (String userId : userIds) {
                    HrMessageRole hrMessageRole = new HrMessageRole();
                    hrMessageRole.setMessageId(hrMessageList.getId());
                    hrMessageRole.setUserId(userId);
                    hrMessageRoleRepository.insert(hrMessageRole);
                }
            }
        }
    }
}
