FROM registry.cn-hangzhou.aliyuncs.com/jeecgdocker/alpine-java:8_server-jre_unlimited
VOLUME /tmp
#RUN mkdir /myapp
RUN mkdir -p /myapp/config
WORKDIR /myapp
ADD target/*.jar /myapp/hr-server.jar
#EXPOSE 10020
ENV LANG C.UTF-8
COPY simsun.ttc /usr/share/fonts/
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom","-Dfile.encoding=utf-8","-jar","hr-server.jar"]
CMD ["--spring.profiles.active=test"]
