version: '3'
services:
  casairwebbase-prometheus:
    image: prom/prometheus:v2.9.2
    volumes:
      - ./prometheus/:/etc/prometheus/
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
    ports:
      - 9090:9090
    # On MacOS, remove next line and replace localhost by host.docker.internal in prometheus/prometheus.yml and
    # grafana/provisioning/datasources/datasource.yml
    network_mode: 'host' # to test locally running service
  casairwebbase-grafana:
    image: grafana/grafana:6.1.4
    volumes:
      - ./grafana/provisioning/:/etc/grafana/provisioning/
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    ports:
      - 3000:3000
    # On MacOS, remove next line and replace localhost by host.docker.internal in prometheus/prometheus.yml and
    # grafana/provisioning/datasources/datasource.yml
    network_mode: 'host' # to test locally running service
