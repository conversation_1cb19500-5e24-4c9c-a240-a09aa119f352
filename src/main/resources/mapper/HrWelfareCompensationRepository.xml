<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrWelfareCompensationRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_id, client_id, pay_year, pay_monthly, unit_pension, unit_unemployment, unit_medical, unit_injury, unit_maternity, unit_subtotal, personal_pension, personal_unemployment,
        personal_medical, personal_subtotal, social_security_total, unit_accumulation_fund, personal_accumulation_fund, accumulation_fund_total, personal_tax, type, is_used, bill_result_id, record_id,
        unit_large_medical,replenish_work_injury,personal_large_medical,personal_maternity,unit_late_fee,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrWelfareCompensation">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="unit_pension" property="unitPension"/>
        <result column="unit_unemployment" property="unitUnemployment"/>
        <result column="unit_medical" property="unitMedical"/>
        <result column="unit_injury" property="unitInjury"/>
        <result column="unit_maternity" property="unitMaternity"/>
        <result column="unit_subtotal" property="unitSubtotal"/>
        <result column="personal_pension" property="personalPension"/>
        <result column="personal_unemployment" property="personalUnemployment"/>
        <result column="personal_medical" property="personalMedical"/>
        <result column="personal_subtotal" property="personalSubtotal"/>
        <result column="social_security_total" property="socialSecurityTotal"/>
        <result column="unit_accumulation_fund" property="unitAccumulationFund"/>
        <result column="personal_accumulation_fund" property="personalAccumulationFund"/>
        <result column="accumulation_fund_total" property="accumulationFundTotal"/>
        <result column="personal_tax" property="personalTax"/>
        <result column="type" property="type"/>
        <result column="is_used" property="isUsed"/>
        <result column="bill_result_id" property="billResultId"/>
        <result column="record_id" property="recordId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <update id="delByObject">
        UPDATE hr_welfare_compensation
        SET is_delete = 1
        WHERE staff_id = #{params.staffId}
          AND client_id = #{params.clientId}
          AND pay_year = #{params.payYear}
          AND pay_monthly = #{params.payMonthly}
          AND type = #{params.type}
          AND is_used = #{params.isUsed}
    </update>

    <update id="deleteBatch">
        <foreach collection="list" item="params" index = "index" open="" close = "" separator=";">
            UPDATE hr_welfare_compensation
            SET is_delete = 1
            WHERE staff_id = #{params.staffId}
                AND client_id = #{params.clientId}
                AND pay_year = #{params.payYear}
                AND pay_monthly = #{params.payMonthly}
                AND type = #{params.type}
                AND is_used = #{params.isUsed}
        </foreach>
    </update>

    <update id="updateConfirmByIds">
        UPDATE hr_welfare_compensation
        SET is_used = 0
        WHERE is_delete = 0
        AND is_used = -1
        AND id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateIsUsed">
        UPDATE hr_welfare_compensation
        SET is_used = #{isUsed}
        WHERE is_delete = 0
          AND id IN (
          SELECT make_up_id FROM hr_make_up_use_record WHERE is_delete = 0 AND bill_id IN
          <foreach collection="billList" open="(" separator="," close=")" item="i">
              #{i}
          </foreach>
        )
    </update>

    <select id="selectTaxSummaryStaff" resultType="cn.casair.dto.report.TaxSummaryStaffDTO">
        SELECT
            <if test="params.exhibitionType==1">
                CONCAT( hwc.pay_year, '-' ,IF ( hwc.pay_monthly &gt; 9, hwc.pay_monthly, CONCAT( '0', hwc.pay_monthly ))) dateStr,
            </if>
            <if test="params.exhibitionType==2">
                CONCAT( hwc.pay_year, '年第', FLOOR( ( hwc.pay_monthly + 2 ) / 3 ), '季度' ) dateStr,
            </if>
            <if test="params.exhibitionType==3">
                CONCAT( hwc.pay_year, '年', IF ( hwc.pay_monthly &lt; 7, '上', '下'), '半年度') dateStr,
            </if>
            <if test="params.exhibitionType==4">
                CONCAT( hwc.pay_year, '年' )        dateStr,
            </if>
            hwc.pay_year,
            hwc.pay_monthly,
            hwc.client_id,
            hc.client_name,
            hc.parent_id,
            hwc.staff_id,
            hts.`name` staffName,
            hts.certificate_num,
            hwcr.personal_tax_source,
            hwcr.personal_tax_system,
            hwc.personal_tax,
            hwc.is_used
        FROM
            hr_welfare_compensation hwc
            LEFT JOIN hr_welfare_compensation_record hwcr ON hwcr.id = hwc.record_id
            LEFT JOIN hr_client hc ON hc.id = hwc.client_id
            LEFT JOIN hr_talent_staff hts ON hts.id = hwc.staff_id
        WHERE
            hwc.is_delete = 0
            AND hwc.type = 4
            AND hwc.staff_id != 'null'
            <if test="params.exhibitionDateStart!=null">
                AND CONCAT( hwc.pay_year, '-' ,IF ( hwc.pay_monthly > 9, hwc.pay_monthly, CONCAT( '0', hwc.pay_monthly ))) &gt;= #{params.exhibitionDateStart}
            </if>
            <if test="params.exhibitionDateEnd!=null">
                AND CONCAT( hwc.pay_year, '-' ,IF ( hwc.pay_monthly > 9, hwc.pay_monthly, CONCAT( '0', hwc.pay_monthly ))) &lt;= #{params.exhibitionDateEnd}
            </if>
            <if test="params.clientIds!=null and params.clientIds.size() > 0">
                AND hwc.client_id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.staffName!=null and params.staffName!=''">
                AND hts.`name` LIKE CONCAT( '%', #{params.staffName}, '%' )
            </if>
            <if test="params.certificateNum!=null and params.certificateNum!=''">
                AND hts.certificate_num LIKE CONCAT( '%', #{params.certificateNum}, '%' )
            </if>
            <if test="params.isUsed!=null">
                AND hwc.is_used = #{params.isUsed}
            </if>
        ORDER BY
            <if test="params.exhibitionType==1">
                hwc.pay_year DESC,
                hwc.pay_monthly DESC,
            </if>
            <if test="params.exhibitionType==2">
                dateStr,
            </if>
            <if test="params.exhibitionType==3">
                dateStr,
            </if>
            <if test="params.exhibitionType==4">
                hwc.pay_year DESC,
            </if>
            hwc.client_id,
            hwc.staff_id
    </select>

    <select id="findPage" resultType="cn.casair.dto.HrWelfareCompensationDTO">
        SELECT
            hwc.id,
            hwc.staff_id,
            hwc.client_id,
            hc.unit_number,
            hc.client_name,
            hts.`name` staffName,
            hts.certificate_num,
            hwc.pay_year,
            hwc.pay_monthly,
            hse.social_security_num,
            hse.medical_insurance_num,
            hwc.unit_pension,
            hwc.unit_unemployment,
            hwc.unit_medical,
            hwc.unit_injury,
            hwc.unit_maternity,
            hwc.unit_subtotal,
            hwc.personal_pension,
            hwc.personal_unemployment,
            hwc.personal_medical,
            hwc.personal_subtotal,
            hwc.social_security_total,
            hse.accumulation_fund_num,
            hwc.unit_accumulation_fund,
            hwc.personal_accumulation_fund,
            hwc.accumulation_fund_total,
            hwc.unit_large_medical,
            hwc.replenish_work_injury,
            hwc.personal_large_medical,
            hwc.personal_maternity
        FROM
            hr_welfare_compensation hwc
                LEFT JOIN hr_client hc ON hc.id = hwc.client_id
                AND hc.is_delete = 0
                LEFT JOIN hr_talent_staff hts ON hts.id = hwc.staff_id
                AND hts.is_delete = 0
                LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hwc.staff_id
                AND hse.is_delete = 0
        WHERE
            hwc.is_delete = 0 AND hts.is_delete = 0
            AND hwc.is_used = -1
            <if test="params.ids!=null and params.ids.size>0">
                AND hwc.id IN
                <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.unitNumber!=null and params.unitNumber!=''">
                AND hc.unit_number LIKE CONCAT('%', #{params.unitNumber}, '%')
            </if>
            <if test="params.clientName!=null and params.clientName!=''">
                AND hc.client_name LIKE CONCAT('%', #{params.clientName}, '%')
            </if>
            <if test="params.staffName!=null and params.staffName!=''">
                AND hts.`name` LIKE CONCAT('%', #{params.staffName}, '%')
            </if>
            <if test="params.certificateNum!=null and params.certificateNum!=''">
                AND hts.certificate_num LIKE CONCAT('%', #{params.certificateNum}, '%')
            </if>
            <if test="params.paymentYearQuery!=null">
                AND hwc.pay_year = #{params.paymentYearQuery}
            </if>
            <if test="params.paymentMonthlyQuery!=null">
                AND hwc.pay_monthly = #{params.paymentMonthlyQuery}
            </if>
            <choose>
                <when test="params.field!=null and params.field!=''">
                    <choose>
                        <when test="params.field=='staff_name'">
                            ORDER BY staffName ${params.order}
                        </when>
                        <when test="params.field=='payment_date'">
                            ORDER BY hwc.pay_year ${params.order}, hwc.pay_monthly ${params.order}
                        </when>
                        <otherwise>
                            ORDER BY ${params.field} ${params.order}
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    ORDER BY
                        hwc.pay_year DESC,
                        hwc.pay_monthly DESC
                </otherwise>
            </choose>
    </select>

    <select id="selectListByObject" resultType="cn.casair.domain.HrWelfareCompensation">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_welfare_compensation
        WHERE is_delete = 0
          AND staff_id = #{staffId}
          AND client_id = #{clientId}
          AND pay_year = #{payYear}
          AND pay_monthly = #{payMonthly}
          ORDER BY created_date DESC
    </select>

    <select id="getStaffWelfareCompensationByPaymentDate" resultType="cn.casair.domain.HrWelfareCompensation">
        SELECT hwc.*
        FROM hr_welfare_compensation hwc
        WHERE hwc.is_delete = 0
          AND hwc.id_no = #{idNo}
          AND hwc.is_used = 0
          <if test="billType == 0">
              AND hwc.type = 4
          </if>
          <if test="billType == 1">
              AND hwc.type != 4
          </if>
          AND hwc.id NOT IN (
            SELECT make_up_id FROM hr_make_up_use_record humr LEFT JOIN hr_bill hb ON humr.bill_id = hb.id
            WHERE humr.is_delete = 0 AND hb.is_delete = 0 AND hb.is_official = 1 AND humr.staff_id = #{staffId}  AND hb.bill_type = #{billType}
            )
        ORDER BY hwc.created_date
    </select>

    <select id="getStaffMakeUpStatistics" resultType="cn.casair.dto.HrWelfareCompensationStatistics">
        SELECT sum(unit_subtotal)              unitSocialSecurityMakeUp,
               sum(personal_subtotal)          personalSocialSecurityMakeUp,
               sum(unit_accumulation_fund)     unitAccumulationFundMakeUp,
               sum(personal_accumulation_fund) personalAccumulationFund
        FROM hr_welfare_compensation
        WHERE is_delete = 0
          AND staff_id = #{staffId}
          AND client_id = #{clientId}
          AND is_used = 0
    </select>

    <select id="findSalaryTaxPage" resultType="cn.casair.dto.HrWelfareCompensationDTO">
        SELECT
            hwc.id,
            hc.client_name,
            hts.`name` staffName,
            hts.certificate_num,
            hwc.pay_year,
            hwc.pay_monthly,
            hwc.social_security_total,
            hwc.accumulation_fund_total,
            hwc.personal_tax,
            hwc.is_used
        FROM
            hr_welfare_compensation hwc
            LEFT JOIN hr_client hc ON hc.id = hwc.client_id
            LEFT JOIN hr_talent_staff hts ON hts.id = hwc.staff_id
        WHERE
            hwc.is_delete = 0 AND
            hts.is_delete = 0
            AND hwc.type != 0
            <if test="permissionClient!=null and permissionClient.size()>0">
                AND hc.id IN
                <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.clientIds!=null and params.clientIds.size()>0">
                AND hwc.client_id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.staffName!=null and params.staffName!=''">
                AND hts.name LIKE CONCAT('%', #{params.staffName}, '%')
            </if>
            <if test="params.certificateNum!=null and params.certificateNum!=''">
                AND hts.certificate_num LIKE CONCAT('%', #{params.certificateNum}, '%')
            </if>
            <if test="params.paymentYearQuery!=null">
                AND hwc.pay_year = #{params.paymentYearQuery}
            </if>
            <if test="params.paymentMonthlyQuery!=null">
                AND hwc.pay_monthly = #{params.paymentMonthlyQuery}
            </if>
            <if test="params.isUsed!=null">
                AND hwc.is_used = #{params.isUsed}
            </if>
            <choose>
                <when test="params.field!=null and params.field!=''">
                    <choose>
                        <when test="params.field=='payment_date'">
                            ORDER BY hwc.pay_year ${params.order}, hwc.pay_monthly ${params.order}
                        </when>
                        <otherwise>
                            ORDER BY ${params.field} ${params.order}
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    ORDER BY
                    hwc.created_date DESC
                </otherwise>
            </choose>
    </select>

    <select id="getSalaryTaxDetail" resultType="cn.casair.dto.HrWelfareAdditionDTO">
        SELECT hwc.id,
               hwc.staff_id,
               hts.`name`         staffName,
               hts.sex,
               TIMESTAMPDIFF( YEAR, hts.birthday, CURDATE()) age,
               hts.certificate_num,
               hts.phone,
               hwc.client_id,
               hc.unit_number,
               hc.client_name,
               hwc.pay_year,
               hwc.pay_monthly,
               hwc.unit_pension,
               hwc.unit_unemployment,
               hwc.unit_medical,
               hwc.unit_injury,
               hwc.unit_maternity,
               hwc.unit_late_fee,
               hwc.unit_subtotal,
               hwc.personal_pension,
               hwc.personal_unemployment,
               hwc.personal_medical,
               hwc.personal_subtotal,
               hwc.social_security_total,
               hwc.unit_accumulation_fund,
               hwc.personal_accumulation_fund,
               hwc.accumulation_fund_total,
               hwc.personal_tax,
               hwc.type,
               hwc.is_used,
               hwc.record_id,
               hwc.unit_large_medical,
               hwc.replenish_work_injury,
               hwc.personal_large_medical,
               hwc.personal_maternity
        FROM hr_welfare_compensation hwc
                 LEFT JOIN hr_client hc ON hc.id = hwc.client_id
                 LEFT JOIN hr_talent_staff hts ON hts.id = hwc.staff_id
        WHERE hwc.id = #{id}
    </select>

    <select id="selectSalaryTaxList" resultType="cn.casair.dto.HrWelfareCompensationDTO">
        SELECT
            hwc.id,
            hc.client_name,
            hts.`name` staffName,
            hts.staff_status,
            hts.certificate_num,
            hwc.pay_year,
            hwc.pay_monthly,
            hwc.social_security_total,
            hwc.accumulation_fund_total,
            hwc.personal_tax,
            hwc.is_used
        FROM
            hr_welfare_compensation hwc
                LEFT JOIN hr_client hc ON hc.id = hwc.client_id
                AND hc.is_delete = 0
                LEFT JOIN hr_talent_staff hts ON hts.id = hwc.staff_id
                AND hts.is_delete = 0
        WHERE
            hwc.is_delete = 0
            AND hts.is_delete = 0
            AND hwc.type != 0
        <if test="params.ids!=null and params.ids.size()>0">
            AND hwc.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="permissionClient!=null and permissionClient.size()>0">
            AND hc.id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientIds!=null and params.clientIds.size()>0">
            AND hwc.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.staffName!=null and params.staffName!=''">
            AND hts.name LIKE CONCAT('%', #{params.staffName}, '%')
        </if>
        <if test="params.certificateNum!=null and params.certificateNum!=''">
            AND hts.certificate_num LIKE CONCAT('%', #{params.certificateNum}, '%')
        </if>
        <if test="params.paymentYearQuery!=null">
            AND hwc.pay_year = #{params.paymentYearQuery}
        </if>
        <if test="params.paymentMonthlyQuery!=null">
            AND hwc.pay_monthly = #{params.paymentMonthlyQuery}
        </if>
        <if test="params.isUsed!=null">
            AND hwc.is_used = #{params.isUsed}
        </if>
        ORDER BY
            hwc.pay_year DESC,
            hwc.pay_monthly DESC
    </select>

    <select id="getByBillIdAndIsUsed" resultType="cn.casair.dto.HrWelfareCompensationDTO">
        SELECT * FROM hr_welfare_compensation hc
        LEFT JOIN hr_bill_compare_result r ON hc.bill_result_id = r.id
        LEFT JOIN hr_bill_compare_config c ON r.bill_compare_config_id = c.id
        WHERE hc.is_delete = 0
        <if test="billId != null and billId!=''">
            AND c.bill_id = #{billId}
        </if>
        AND c.`type` = #{type}
        and hc.is_used in
              <foreach collection="useStatus" open="(" separator="," close=")" item="val">
                    #{val}
              </foreach>
    </select>

    <select id="selectByIds" resultType="cn.casair.domain.HrWelfareCompensation">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_welfare_compensation
        WHERE
            is_delete = 0
            AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>

    <select id="selectStaffIdListByIds" resultType="java.lang.String">
        SELECT
            staff_id
        FROM
            hr_welfare_compensation
        WHERE
            id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        GROUP BY
            staff_id
    </select>

    <select id="selectAccumulationFundIdListByIds" resultType="java.lang.String">
        SELECT
            hc.provident_fund_type_id
        FROM
            hr_welfare_compensation hwc
            LEFT JOIN hr_client hc ON hwc.client_id = hc.id
        WHERE
            hwc.id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        GROUP BY
            provident_fund_type_id
    </select>

    <select id="selectSocialSecurityIdListByIds" resultType="java.lang.String">
        SELECT
            hc.social_security_type_id
        FROM
            hr_welfare_compensation hwc
            LEFT JOIN hr_client hc on hwc.client_id = hc.id
        WHERE
            hwc.id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        GROUP BY
            social_security_type_id
    </select>

    <select id="getStaffWelfareCompensation" resultType="cn.casair.dto.HrWelfareCompensationDTO">
        SELECT
            ANY_VALUE ( staff_id ) staff_id,
            ANY_VALUE ( client_id ) client_id,
            pay_year,
            pay_monthly,
            SUM( unit_pension ) unit_pension,
            SUM( unit_unemployment ) unit_unemployment,
            SUM( unit_medical ) unit_medical,
            SUM( unit_injury ) unit_injury,
            SUM( unit_maternity ) unit_maternity,
            SUM( unit_subtotal ) unit_subtotal,
            SUM( personal_pension ) personal_pension,
            SUM( personal_unemployment ) personal_unemployment,
            SUM( personal_medical ) personal_medical,
            SUM( personal_subtotal ) personal_subtotal,
            SUM( social_security_total ) social_security_total,
            SUM( unit_accumulation_fund ) unit_accumulation_fund,
            SUM( personal_accumulation_fund ) personal_accumulation_fund,
            SUM( accumulation_fund_total ) accumulation_fund_total,
            SUM( personal_tax ) personal_tax,
            SUM( unit_large_medical ) unit_large_medical,
            SUM( replenish_work_injury ) replenish_work_injury,
            SUM( personal_large_medical ) personal_large_medical,
            SUM( personal_maternity ) personal_maternity,
            ANY_VALUE ( is_used ) is_used
        FROM
            hr_welfare_compensation
        WHERE
            is_delete = 0
            AND staff_id = #{params.staffId}
            AND is_used = 0
            <if test="params.isUsed!=null">
                AND is_used = #{params.isUsed}
            </if>
        GROUP BY
            pay_year,
            pay_monthly
        ORDER BY
            pay_year DESC,
            pay_monthly DESC
    </select>

    <select id="exportWelfareCompensation" resultType="cn.casair.dto.excel.HrWelfareCompensationExport">
        SELECT
            CONCAT( pay_year, '-', pay_monthly ) paymentDate,
            SUM( unit_pension ) unit_pension,
            SUM( unit_unemployment ) unit_unemployment,
            SUM( unit_medical ) unit_medical,
            SUM( unit_injury ) unit_injury,
            SUM( unit_maternity ) unit_maternity,
            SUM( unit_subtotal ) unit_subtotal,
            SUM( personal_pension ) personal_pension,
            SUM( personal_unemployment ) personal_unemployment,
            SUM( personal_medical ) personal_medical,
            SUM( personal_subtotal ) personal_subtotal,
            SUM( social_security_total ) social_security_total,
            SUM( unit_accumulation_fund ) unit_accumulation_fund,
            SUM( personal_accumulation_fund ) personal_accumulation_fund,
            SUM( accumulation_fund_total ) accumulation_fund_total,
            SUM( unit_large_medical ) unit_large_medical,
            SUM( replenish_work_injury ) replenish_work_injury,
            SUM( personal_large_medical ) personal_large_medical,
            SUM( personal_maternity ) personal_maternity,
            ANY_VALUE ( is_used ) is_used
        FROM
            hr_welfare_compensation
        WHERE
            is_delete = 0
            AND staff_id = #{staffId}
            AND is_used = 0
            GROUP BY
            pay_year,
            pay_monthly
        ORDER BY
            pay_year DESC,
            pay_monthly DESC
    </select>

    <select id="getUnUsedWelfareCompensation" resultType="cn.casair.domain.HrWelfareCompensation">
        SELECT
            hwc.*
        FROM
            hr_welfare_compensation hwc
        WHERE hwc.is_delete = 0 AND hwc.is_used = 0
        <if test="billType == 0">
            AND hwc.type = 4
        </if>
        <if test="billType == 1">
            AND hwc.type != 4
        </if>
        AND CONCAT( hwc.pay_year, '-', IF ( hwc.pay_monthly > 9, hwc.`pay_monthly`, CONCAT( '0', hwc.`pay_monthly` ))) &lt;=  #{paymentDate}
        AND hwc.id NOT IN (
            SELECT make_up_id FROM hr_make_up_use_record humr LEFT JOIN hr_bill hb ON humr.bill_id = hb.id
            WHERE humr.is_delete = 0 AND hb.is_delete = 0 AND hb.is_official = 1 AND hb.bill_type = #{billType}
        )
        ORDER BY hwc.created_date DESC
    </select>

    <select id="getUsedMakeUpByResultId" resultType="cn.casair.dto.HrWelfareCompensationDTO">
        SELECT
            hb.title,
            hwc.*
        FROM
            hr_welfare_compensation hwc
        LEFT JOIN hr_bill_compare_result hbcr ON hwc.bill_result_id = hbcr.id
        LEFT JOIN hr_bill_compare_config hbcc ON hbcr.bill_compare_config_id = hbcc.id
        LEFT JOIN hr_bill hb ON hbcc.bill_id = hb.id
        WHERE
            hwc.is_delete = 0
            AND hbcr.is_delete = 0
            AND hwc.type != 0
            AND hbcc.type = #{type}
            AND hwc.is_used = #{isUsed}
            AND hwc.bill_result_id IN
            <foreach collection="billResultIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>

    <update id="delByBillIdAndIsUsed">
        UPDATE hr_welfare_compensation c
            INNER JOIN hr_bill_compare_result r ON c.bill_result_id = r.id
            INNER JOIN hr_bill_compare_config cf ON r.bill_compare_config_id = cf.id
        SET c.is_delete = #{isDelete}
        WHERE c.is_used = 0 AND cf.`type` = #{type}
        <if test="billId != null and billId != ''">
            AND cf.bill_id = #{billId}
        </if>
        <if test="billConfigIds != null and billConfigIds.size()>0">
            AND cf.id IN
            <foreach collection="billConfigIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
    </update>

    <update id="delByResultId">
        UPDATE hr_welfare_compensation SET is_delete = 1,last_modified_by = #{lastModifiedBy},last_modified_date = now() WHERE `type` != 0 AND is_used != 1 AND bill_result_id IN
        <foreach collection="billResultIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

</mapper>
