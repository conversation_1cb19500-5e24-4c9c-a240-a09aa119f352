package cn.casair.repository;

import cn.casair.domain.HrContractTemplate;
import cn.casair.dto.HrContractTemplateDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 合同模板数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@Repository
public interface HrContractTemplateRepository extends BaseMapper<HrContractTemplate> {

    /**
     * 分页查询合同模板
     *
     * @param page
     * @param hrContractTemplateDTO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrContractTemplateDTO>
     * <AUTHOR>
     * @date 2021/9/28
     **/
    IPage<HrContractTemplateDTO> selectPageByObjet(Page<HrContractTemplateDTO> page, @Param("param") HrContractTemplateDTO hrContractTemplateDTO);

    /**
     * 合同模板使用次数+1
     *
     * @param templateId
     * @return int
     * <AUTHOR>
     * @date 2021/9/28
     **/
    int usageCountPlus(@Param("templateId") String templateId);

    /**
     * 获取合同模板列表 不分页
     *
     * @return java.util.List<cn.casair.dto.HrContractTemplateDTO>
     * <AUTHOR>
     * @date 2021/9/28
     **/
    List<HrContractTemplateDTO> getContractTemplateList();

    /**
     * 根据搜索条件获取合同模板 不分页
     * @param hrContractTemplateDTO
     * @return
     */
    List<HrContractTemplateDTO> findList(@Param("param")HrContractTemplateDTO hrContractTemplateDTO);
}
