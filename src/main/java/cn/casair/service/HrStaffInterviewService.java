package cn.casair.service;

import cn.casair.domain.HrStaffInterview;
import cn.casair.dto.HrExamResultDTO;
import cn.casair.dto.HrStaffInterviewDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 应试经历服务类
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface HrStaffInterviewService extends IService<HrStaffInterview> {


    /**
     * 创建应试经历
     * @param hrExamResultDTO
     * @return
     */
    //List<HrStaffInterviewDTO> createHrStaffInterview(HrStaffInterviewDTO hrStaffInterviewDTO);
    List<HrExamResultDTO> createHrStaffInterview(HrExamResultDTO hrExamResultDTO);

    /**
     * 修改应试经历
     * @param hrExamResultDTO
     * @return
     */
    Optional<List<HrExamResultDTO>> updateHrStaffInterview(HrExamResultDTO hrExamResultDTO);

    /**
     * 查询应试经历详情
     * @param id
     * @return
     */
    HrExamResultDTO getHrStaffInterview(String id);

    /**
     * 删除应试经历
     * @param id
     */
    List<HrExamResultDTO> deleteHrStaffInterview(String id);

    /**
     * 查询应试经历
     * @param staffId 员工ID
     * @return
     */
    List<HrExamResultDTO> findInterviewList(String staffId);

    void setDict(HrExamResultDTO dto);
}
