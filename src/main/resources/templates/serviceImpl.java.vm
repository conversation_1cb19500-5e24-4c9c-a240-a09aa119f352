package ${package.ServiceImpl};
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import ${package.Entity}.${entity};
import ${cfg.extPackage.Dto}.${entity}DTO;
import ${package.Mapper}.${table.mapperName};
import ${cfg.extPackage.Mapper}.${entity}Mapper;
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;
/**
 * $!{table.comment}服务实现类
 *
 * <AUTHOR>
 * @since ${date}
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
#if(${kotlin})
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

    }
#else
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}>implements ${table.serviceName} {

    #foreach($field in ${table.fields})
        #if(${field.keyFlag})
            #set($keyPropertyType=${field.propertyType})
        #end
    #end
    ## private static final Logger log=LoggerFactory.getLogger(${table.serviceImplName}.class);
    private final ${table.mapperName} ${cfg.repositoryProperty};
    private final ${entity}Mapper ${cfg.entityProperty}Mapper;

    #*public ${table.serviceName}Impl(${table.mapperName} ${cfg.repositoryProperty}, ${entity}Mapper ${cfg.entityProperty}Mapper){
    this.${cfg.repositoryProperty} = ${cfg.repositoryProperty};
    this.${cfg.entityProperty}Mapper= ${cfg.entityProperty}Mapper;
    }*#

    /**
     * 创建$!{table.comment}
     * @param ${cfg.entityProperty}DTO
     * @return
     */
    @Override
    public ${entity}DTO create${entity}(${entity}DTO ${cfg.entityProperty}DTO){
    log.info("Create new ${entity}:{}", ${cfg.entityProperty}DTO);

    ${entity} ${cfg.entityProperty} =this.${cfg.entityProperty}Mapper.toEntity(${cfg.entityProperty}DTO);
    this.${cfg.repositoryProperty}.insert(${cfg.entityProperty});
    return this.${cfg.entityProperty}Mapper.toDto(${cfg.entityProperty});
    }

    /**
     * 修改$!{table.comment}
     * @param ${cfg.entityProperty}DTO
     * @return
     */
    @Override
    public Optional<${entity}DTO>update${entity}(${entity}DTO ${cfg.entityProperty}DTO){
    return Optional.ofNullable(this.${cfg.repositoryProperty}.selectById(${cfg.entityProperty}DTO.getId()))
    .map(roleTemp->{
    ${entity} ${cfg.entityProperty} =this.${cfg.entityProperty}Mapper.toEntity(${cfg.entityProperty}DTO);
    this.${cfg.repositoryProperty}.updateById(${cfg.entityProperty});
    log.info("Update ${entity}:{}", ${cfg.entityProperty}DTO);
    return ${cfg.entityProperty}DTO;
    });
    }

    /**
     * 查询$!{table.comment}详情
     * @param id
     * @return
     */
    @Override
    public ${entity}DTO get${entity}($!{keyPropertyType} id){
    log.info("Get ${entity} :{}",id);

    ${entity} ${cfg.entityProperty} =this.${cfg.repositoryProperty}.selectById(id);
    return this.${cfg.entityProperty}Mapper.toDto(${cfg.entityProperty});
    }

    /**
     * 删除$!{table.comment}
     * @param id
     */
    @Override
    public void delete${entity}($!{keyPropertyType} id){
    Optional.ofNullable(this.${cfg.repositoryProperty}.selectById(id))
    .ifPresent(${cfg.entityProperty} ->{
    this.${cfg.repositoryProperty}.deleteById(id);
    log.info("Delete ${entity}:{}", ${cfg.entityProperty});
    });
    }

    /**
     * 批量删除$!{table.comment}
     * @param ids
     */
    @Override
    public void delete${entity}(List<$!{keyPropertyType}>ids){
    log.info("Delete ${entity}s:{}",ids);
    this.${cfg.repositoryProperty}.deleteBatchIds(ids);
    }

    /**
     * 分页查询$!{table.comment}
     * @param ${cfg.entityProperty}DTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(${entity}DTO ${cfg.entityProperty}DTO,Long pageNumber,Long pageSize){
    Page<${entity}>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<${entity}>qw=new QueryWrapper<>(this.${cfg.entityProperty}Mapper.toEntity(${cfg.entityProperty}DTO));
    qw.orderByDesc("id");

    IPage iPage=this.${cfg.repositoryProperty}.selectPage(page,qw);
    iPage.setRecords(this.${cfg.entityProperty}Mapper.toDto(iPage.getRecords()));
    return iPage;
    }
}
#end
