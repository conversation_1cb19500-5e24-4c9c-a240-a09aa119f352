<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffTurnPositiveRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , staff_id, states, salary, refuse, client_user_id, client_date, client_appendix_id, client_remark, enterprise_remark, enterprise_appendix_id, enterprise_date, enterprise_user_id,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffTurnPositive">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="states" property="states"/>
        <result column="salary" property="salary"/>
        <result column="refuse" property="refuse"/>
        <result column="client_user_id" property="clientUserId"/>
        <result column="client_date" property="clientDate"/>
        <result column="client_appendix_id" property="clientAppendixId"/>
        <result column="client_remark" property="clientRemark"/>
        <result column="enterprise_remark" property="enterpriseRemark"/>
        <result column="enterprise_appendix_id" property="enterpriseAppendixId"/>
        <result column="enterprise_date" property="enterpriseDate"/>
        <result column="enterprise_user_id" property="enterpriseUserId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Select_Sql">
        SELECT
        any_value(hs.id) id,
        any_value(htf.id) staffId,
        any_value(htf.system_num) systemNum,
        any_value(htf.`name`) name ,
        any_value(htf.certificate_num) certificateNum,
        any_value(htf.phone) phone,
        any_value ( htf.staff_status ) staffStatus,
        any_value(haes.staff_type) staffType,
        any_value(haes.contract_start_date) contractStartDate,
        any_value(haes.internship_date) internshipDate,
        any_value(hc.client_name) clientName,
        any_value(hc.id) clientId,
        any_value(su.real_name) specializedName,
        any_value(hs.enterprise_states) enterpriseStates,
        any_value(hs.states) states,
        any_value(hs.application_description) applicationDescription,
        any_value(he.board_date) boarDate
        FROM
        hr_apply_entry_staff haes
        LEFT JOIN hr_talent_staff htf ON haes.staff_id = htf.id
        LEFT JOIN hr_client hc ON haes.client_id = hc.id
        LEFT JOIN sys_user su ON hc.specialized_id = su.id
        LEFT JOIN hr_staff_turn_positive hs ON haes.staff_id = hs.staff_id
        LEFT JOIN hr_staff_work_experience he on haes.staff_id=he.staff_id
        WHERE
        haes.is_delete = 0
        AND htf.is_delete = 0


        <if test="param1.staffIdList!=null and  param1.staffIdList.size() > 0">
            and haes.staff_id in
            <foreach collection="param1.staffIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.systemNum!=null and param1.systemNum!=''">
            and htf.system_num like concat('%',#{param1.systemNum   },'%')
        </if>

        <if test="param1.name!=null and param1.name!=''">
            and htf.name like concat('%',#{param1.name},'%')
        </if>


        <if test="param1.enterpriseStates!=null and param1.enterpriseStates!=''">
            and hs.enterprise_states=#{param1.enterpriseStates}
        </if>
        <if test="param1.certificateNum!=null and param1.certificateNum!=''">
            and htf.certificate_num like concat('%',#{param1.certificateNum},'%')
        </if>
        <if test="param1.phone!=null and param1.phone!=''">
            and htf.phone like concat('%',#{param1.phone},'%')
        </if>

        <if test="param1.staffTypeList!=null and  param1.staffTypeList.size() > 0">
            and htf.staff_status in
            <foreach collection="param1.staffTypeList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.contractStartDateStart!=null ">
            AND haes.contract_start_date <![CDATA[ >= ]]> #{param1.contractStartDateStart}
        </if>
        <if test="param1.contractStartDateEnd!=null ">
            AND haes.contract_start_date <![CDATA[ <= ]]> #{param1.contractStartDateEnd}
        </if>
        <if test="param1.internshipDateStartDateStart!=null ">
            AND haes.internship_date <![CDATA[ >= ]]> #{param1.internshipDateStartDateStart}
        </if>
        <if test="param1.internshipDateStartDateEnd!=null ">
            AND haes.internship_date <![CDATA[ <= ]]> #{param1.internshipDateStartDateEnd}
        </if>

        GROUP BY htf.id
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </sql>
    <select id="selectStaff" resultType="cn.casair.dto.HrStaffTurnPositiveDTO">
        <include refid="Select_Sql"></include>
    </select>
    <select id="findList" resultType="cn.casair.dto.HrStaffTurnPositiveDTO">
        <include refid="Select_Sql"></include>
    </select>

    <update id="updateHrStaffTurnPositive">
        UPDATE hr_staff_turn_positive
        SET enterprise_appendix_id =#{enterpriseAppendixId},
            enterprise_remark      =#{enterpriseRemark},
            enterprise_user_id     =#{enterpriseUserId},
            enterprise_date        =#{enterpriseDate},
            enterprise_states=#{enterpriseStates}
        WHERE id = #{id}
          and is_delete = 0
          and states = 1
    </update>
    <update id="updateSalary">
        UPDATE  hr_staff_emolument set salary=#{salary} where staff_id=#{staffId} and is_delete=0
    </update>

    <select id="selectTurn" resultType="cn.casair.dto.HrStaffTurnPositiveDTO">
        SELECT any_value(hs.id)                    id,
               any_value(htf.id)                   staffId,
               any_value(hc.unit_number)           unitNumber,
               any_value(hc.client_name)           clientName,
               any_value(hc.id)                    clientId,
               any_value(htf.system_num)           systemNum,
               any_value(htf.`name`)               name,
               any_value(htf.certificate_num)      certificateNum,
               any_value(htf.sex)                  sex,
               any_value(htf.phone)                phone,
               any_value(hsn.profession_name)      professionName,
               any_value(htf.personnel_type)       personnelType,
               any_value(htf.staff_status)         staffStatus,
               any_value(he.board_date)            boarDate,
               any_value(haes.internship_date)     internshipDate,
               any_value(haes.contract_start_date) contractStartDate,
               any_value(haes.contract_end_date)   contractEndDate,
               any_value(su.real_name)             specializedName,
               any_value(hae.created_date)         applyCreatedDate,
               any_value(hs.refuse)                refuse,
               any_value(hs.enterprise_refuse)     enterpriseRefuse,
               any_value(hs.states)                states,
               any_value(hs.enterprise_states)     enterpriseStates,
               any_value(hs.salary)                salary,
               any_value(hs.client_appendix_id)    clientAppendixId,
               any_value(hs.client_remark)         clientRemark
        FROM hr_apply_entry_staff haes
                 LEFT JOIN hr_talent_staff htf ON haes.staff_id = htf.id
                 LEFT JOIN hr_station hsn ON hsn.id = haes.station_id
                 LEFT JOIN hr_client hc ON haes.client_id = hc.id
                 LEFT JOIN sys_user su ON hc.specialized_id = su.id
                 LEFT JOIN hr_staff_turn_positive hs ON haes.staff_id = hs.staff_id
                 LEFT JOIN hr_staff_work_experience he ON haes.staff_id = he.staff_id
                 LEFT JOIN hr_apply_entry hae ON hae.id = haes.apply_id
        WHERE haes.is_delete = 0

          AND htf.is_delete = 0
          AND htf.id = #{id}
        GROUP BY htf.id
    </select>
    <select id="selectAppendixId" resultType="cn.casair.domain.HrAppendix">
        SELECT * from hr_appendix where
        <if test="param1!=null and  param1.size() > 0">
            id in
            <foreach collection="param1" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>

    <select id="selecttime" resultType="java.lang.Integer">
        SELECT rule_day
        FROM `hr_remind_conf`
        where remind_key = #{staff_turn}
          and is_delete = 0
    </select>
    <select id="selectClientId" resultType="java.lang.String">
        SELECT id
        from hr_client
        where user_id = #{clientUserId}
          and is_delete = 0
    </select>
    <select id="selectStaffName" resultType="java.lang.String">
        SELECT name
        from hr_talent_staff
        where id = #{staffId}
          and is_delete = 0
    </select>
    <select id="selectGetClientId" resultType="java.lang.String">
        select client_id
        from hr_talent_staff
        where id = #{staffId}
          and is_delete = 0
    </select>
</mapper>
