package cn.casair.service;

import cn.casair.domain.HrStaffEducation;
import cn.casair.dto.HrStaffEducationDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 教育经历服务类
 *
 * <AUTHOR>
 * @since 2021-09-01
 */
public interface HrStaffEducationService extends IService<HrStaffEducation> {


    /**
     * 创建教育经历
     * @param hrStaffEducationDTO
     * @return
     */
    List<HrStaffEducationDTO> createHrStaffEducation(HrStaffEducationDTO hrStaffEducationDTO);

    /**
     * 修改教育经历
     * @param hrStaffEducationDTO
     * @return
     */
    Optional<List<HrStaffEducationDTO>> updateHrStaffEducation(HrStaffEducationDTO hrStaffEducationDTO);

    /**
     * 查询教育经历详情
     * @param id
     * @return
     */
    HrStaffEducationDTO getHrStaffEducation(String id);

    /**
     * 删除教育经历
     * @param id
     */
    List<HrStaffEducationDTO> deleteHrStaffEducation(String id);

    /**
     * 查询教育经历
     * @param staffId 员工ID
     * @return
     */
    List<HrStaffEducationDTO> findEducationList(String staffId);

    void setDict(HrStaffEducationDTO dto);
}
