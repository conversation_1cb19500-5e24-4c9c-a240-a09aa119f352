package cn.casair.repository;

import cn.casair.domain.HrWorkInjury;
import cn.casair.dto.HrMaterialDTO;
import cn.casair.dto.HrWorkInjuryDTO;
import cn.casair.dto.excel.HrWorkInjuryExport;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 工伤服务表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Repository
public interface HrWorkInjuryRepository extends BaseMapper<HrWorkInjury> {

    @Select("SELECT\n" +
        "\thwi.id,\n" +
        "\thwi.staff_id,\n" +
        "\thwi.declare_date,\n" +
        "\tht.client_id,\n" +
        "\thc.client_name,\n" +
        "\tht.`name`,\n" +
        "\tht.certificate_num,\n" +
        "\tht.sex,\n" +
        "\tht.phone,\n" +
        "\tht.staff_status,\n" +
        "\ths.profession_name,\n" +
        "\ths.id,\n" +
        "\tht.personnel_type,\n" +
        "\thwi.injury_date,\n" +
        "\thwi.work_stoppage_start_date,\n" +
        "\thwi.work_stoppage_end_date,\n" +
        "\thwi.`status`,\n" +
        "\thwi.appraisal_status,\n" +
        "\thwi.injury_description,\n" +
        "\tsu.real_name AS specialized,\n" +
        "\tsu.id \n" +
        "FROM\n" +
        "\thr_work_injury hwi\n" +
        "\tLEFT JOIN hr_talent_staff ht ON hwi.staff_id = ht.id \n" +
        "\tAND ht.is_delete = 0\n" +
        "\tLEFT JOIN hr_staff_work_experience hsw ON hwi.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id\n" +
        "\tAND hsw.is_delete = 0 \n" +
        "\tAND hsw.iz_default = 1\n" +
        "\tLEFT JOIN hr_station hs ON hsw.station_id = hs.id \n" +
        "\tAND hs.is_delete = 0\n" +
        "\tLEFT JOIN hr_client hc ON hc.id = hwi.client_id \n" +
        "\tAND hc.is_delete = 0\n" +
        "\tLEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id \n" +
        "\tAND huc.is_delete = 0 \n" +
        "\tAND huc.is_specialized = 1\n" +
        "\tLEFT JOIN sys_user su ON huc.user_id = su.id \n" +
        "\tAND su.is_delete = 0 ${ew.customSqlSegment}")
    IPage<HrWorkInjuryDTO> page(Page page, @Param(Constants.WRAPPER)QueryWrapper<HrWorkInjury> qw);

    HrWorkInjuryDTO selectDetatil(String id);

    List<HrWorkInjuryExport> exportWorkInjuries(@Param(Constants.WRAPPER)QueryWrapper<HrWorkInjury> qw);

    List<HrMaterialDTO> selectByIds(@Param("ids")ArrayList<String> ids);

    /**
     * 查询员工所有的工伤
     * @param staffId 员工ID
     * @return
     */
    List<HrWorkInjuryDTO> workInjuryListByStaffId(@Param("staffId") String staffId);

}
