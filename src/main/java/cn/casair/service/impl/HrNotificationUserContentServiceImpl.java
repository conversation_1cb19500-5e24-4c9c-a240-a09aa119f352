package cn.casair.service.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrNotificationUserContent;
import cn.casair.dto.HrNotificationUserContentDTO;
import cn.casair.repository.HrNotificationUserContentRepository;
import cn.casair.mapper.HrNotificationUserContentMapper;
import cn.casair.service.HrNotificationUserContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;
/**
 * 通知消息内容表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrNotificationUserContentServiceImpl extends ServiceImpl<HrNotificationUserContentRepository, HrNotificationUserContent>implements HrNotificationUserContentService {

        private final HrNotificationUserContentRepository hrNotificationUserContentRepository;
    private final HrNotificationUserContentMapper hrNotificationUserContentMapper;



    /**
     * 创建通知消息内容表
     * @param hrNotificationUserContentDTO
     * @return
     */
    @Override
    public HrNotificationUserContentDTO createHrNotificationUserContent(HrNotificationUserContentDTO hrNotificationUserContentDTO){
    log.info("Create new HrNotificationUserContent:{}", hrNotificationUserContentDTO);

    HrNotificationUserContent hrNotificationUserContent =this.hrNotificationUserContentMapper.toEntity(hrNotificationUserContentDTO);
    this.hrNotificationUserContentRepository.insert(hrNotificationUserContent);
    return this.hrNotificationUserContentMapper.toDto(hrNotificationUserContent);
    }

    /**
     * 修改通知消息内容表
     * @param hrNotificationUserContentDTO
     * @return
     */
    @Override
    public Optional<HrNotificationUserContentDTO>updateHrNotificationUserContent(HrNotificationUserContentDTO hrNotificationUserContentDTO){
    return Optional.ofNullable(this.hrNotificationUserContentRepository.selectById(hrNotificationUserContentDTO.getId()))
    .map(roleTemp->{
    HrNotificationUserContent hrNotificationUserContent =this.hrNotificationUserContentMapper.toEntity(hrNotificationUserContentDTO);
    this.hrNotificationUserContentRepository.updateById(hrNotificationUserContent);
    log.info("Update HrNotificationUserContent:{}", hrNotificationUserContentDTO);
    return hrNotificationUserContentDTO;
    });
    }

    /**
     * 查询通知消息内容表详情
     * @param id
     * @return
     */
    @Override
    public HrNotificationUserContentDTO getHrNotificationUserContent(String id){
    log.info("Get HrNotificationUserContent :{}",id);

    HrNotificationUserContent hrNotificationUserContent =this.hrNotificationUserContentRepository.selectById(id);
    return this.hrNotificationUserContentMapper.toDto(hrNotificationUserContent);
    }

    /**
     * 删除通知消息内容表
     * @param id
     */
    @Override
    public void deleteHrNotificationUserContent(String id){
    Optional.ofNullable(this.hrNotificationUserContentRepository.selectById(id))
    .ifPresent(hrNotificationUserContent ->{
    this.hrNotificationUserContentRepository.deleteById(id);
    log.info("Delete HrNotificationUserContent:{}", hrNotificationUserContent);
    });
    }

    /**
     * 批量删除通知消息内容表
     * @param ids
     */
    @Override
    public void deleteHrNotificationUserContent(List<String>ids){
    log.info("Delete HrNotificationUserContents:{}",ids);
    this.hrNotificationUserContentRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询通知消息内容表
     * @param hrNotificationUserContentDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrNotificationUserContentDTO hrNotificationUserContentDTO,Long pageNumber,Long pageSize){
    Page<HrNotificationUserContent>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrNotificationUserContent>qw=new QueryWrapper<>(this.hrNotificationUserContentMapper.toEntity(hrNotificationUserContentDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrNotificationUserContentRepository.selectPage(page,qw);
    iPage.setRecords(this.hrNotificationUserContentMapper.toDto(iPage.getRecords()));
    return iPage;
    }
}
