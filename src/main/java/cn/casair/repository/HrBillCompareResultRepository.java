package cn.casair.repository;

import cn.casair.domain.HrBillCompareResult;
import cn.casair.dto.HrBillCompareConfigDTO;
import cn.casair.dto.HrBillCompareResultDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 对账结果数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Repository
public interface HrBillCompareResultRepository extends BaseMapper<HrBillCompareResult> {

    /**
     * 根据对账配置id获取对账结果
     *
     * @param billCompareConfigIds
     * @return
     */
    List<HrBillCompareResult> getBilCompareResultByBillConfigIds(@Param("billCompareConfigIds") List<String> billCompareConfigIds);

    @Select("SELECT * FROM hr_bill_compare_result hr WHERE hr.is_delete = 0 AND hr.bill_compare_config_id = #{billConfigId}")
    HrBillCompareResultDTO getByBillConfigId(String billConfigId);

    IPage<HrBillCompareResultDTO> findPage(Page<HrBillCompareResult> page, @Param("params") HrBillCompareResultDTO hrBillCompareResultDTO);

    void delByBillIdAndType(@Param("billId") String billId, @Param("type") Integer type, @Param("isDelete") Integer isDelete, @Param("billConfigIds") List<String> billConfigIds);

    @Update("UPDATE hr_bill_compare_result ht SET ht.is_delete = 1 WHERE ht.bill_compare_config_id = #{billCompareConfigId}")
    void delByBillCompareConfigId(String id);

    /**
     * 不分页查询对账结果
     * @param hrBillCompareResultDTO
     * @return
     */
    List<HrBillCompareResultDTO> findList(@Param("params") HrBillCompareResultDTO hrBillCompareResultDTO);

    /**
     * 根据ID删除
     * @param ids
     * @param lastModifiedBy
     */
    void delBatchIds(@Param("ids") List<String> ids, @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 修改对账锁定状态
     * @param lockStatus 锁定状态
     * @param configIds 配置IDs
     */
    void updateConfigLockStatus(@Param("lockStatus") Integer lockStatus, @Param("configIds") List<String> configIds);

    /**
     * 获取同月份以及上个月已保存补差的对账数据
     * @param payYear
     * @param payMonthly
     * @param lastPayYear
     * @param lastPayMonthly
     * @param typeList
     * @return
     */
    List<HrBillCompareResultDTO> getBillCompareResult(@Param("payYear") Integer payYear, @Param("payMonthly") Integer payMonthly,
                                                      @Param("lastPayYear") Integer lastPayYear, @Param("lastPayMonthly") Integer lastPayMonthly,
                                                      @Param("typeList") List<Integer> typeList);

    /**
     * 查询已经保存补差或者已经锁定的账单
     * @param payYear
     * @param payMonthly
     * @param billType
     * @return
     */
    List<HrBillCompareConfigDTO> getIsUsedBillId(@Param("payYear") Integer payYear, @Param("payMonthly") Integer payMonthly, @Param("billType") Integer billType);

    /**
     * 修改对账结果状态
     * @param resultDTO
     */
    void updateByObject(@Param("param") HrBillCompareResultDTO resultDTO);

}
