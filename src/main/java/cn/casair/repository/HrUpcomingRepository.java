package cn.casair.repository;
import cn.casair.domain.HrUpcoming;
import cn.casair.dto.HrUpcomingDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalTime;
import java.util.List;

/**
 * 待办表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Repository
public interface HrUpcomingRepository extends BaseMapper<HrUpcoming> {


    List<HrUpcomingDTO> selectWarn(@Param("userId")String userId, @Param("nowTime")LocalTime nowTime, @Param("localTime")LocalTime localTime);

    /**
     * 根据员工id获取对应专管员的用户id
     * @param staffId
     * @return
     */
    HrUpcomingDTO selectUserId(String staffId);

    /**
     * 根据客户id获取对应专管员的用户id
     * @param clientId 客户Id
     * @return
     */
    HrUpcomingDTO selectUserIdByClientId(String clientId);
}
