package cn.casair.repository;
import cn.casair.domain.HrStaffInterview;
import cn.casair.dto.HrStaffInterviewDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 应试经历数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Repository
public interface HrStaffInterviewRepository extends BaseMapper<HrStaffInterview> {

    /**
     * 查询应试经历
     * @param staffId 员工ID
     * @return
     */
    List<HrStaffInterviewDTO> findInterviewByStaffId(@Param("staffId") String staffId);

    /**
     * 查询应试经历详情
     * @param id
     * @return
     */
    HrStaffInterviewDTO selectInterviewById(String id);
}
