package cn.casair.service;

import cn.casair.domain.HrBill;
import cn.casair.domain.HrBillInvoiceRecord;
import cn.casair.domain.HrBillReimbursementApply;
import cn.casair.domain.HrClient;
import cn.casair.dto.*;
import cn.casair.dto.billl.BillReimApplyUrgeDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 报销申请服务类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
public interface HrBillReimbursementApplyService extends IService<HrBillReimbursementApply> {


    /**
     * 创建报销申请
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    HrBillReimbursementApplyDTO createHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO);

    /**
     * 可申请报销发起申请
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    HrBillReimbursementApplyDTO saveHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO);

    /**
     * 修改报销申请
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    Optional<HrBillReimbursementApplyDTO> updateHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO);

    /**
     * 查询报销申请详情
     *
     * @param id
     * @return
     */
    HrBillReimbursementApplyDTO getHrBillReimbursementApply(String id);

    /**
     * 删除报销申请
     *
     * @param id
     */
    void deleteHrBillReimbursementApply(String id);

    /**
     * 批量删除报销申请
     *
     * @param ids
     */
    void deleteHrBillReimbursementApply(List<String> ids);

    /**
     * 分页查询报销申请
     *
     * @param hrBillReimbursementApplyDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO, Long pageNumber, Long pageSize);

    /**
     * 审核申请
     *
     * @param param
     */
    void auditApply(HrBillReimbursementApplyDTO param);

    /**
     * 获取可催办的审批人
     *
     * @param applyId 申请id
     * @return
     */
    List<UserDTO> getUrgeUsers(String applyId);

    /**
     * 报销申请催办
     *
     * @param param
     */
    void reimbursementApplyUrge(BillReimApplyUrgeDTO param);

    /**
     * 报销申请催办--批量操作
     *
     * @param param
     */
    Map<String, Object> batchApplyUrge(BillReimApplyUrgeDTO param);

    /**
     * 获取可通知的角色列表
     *
     * @return
     */
    List<RoleDTO> getNoticeRoles();

    /**
     * 报销申请:获取基础费用信息
     *
     * @param param
     * @return
     */
    HrBillReimbursementApplyDTO getBillTotalInfo(HrBillTotalDTO param);

    /**
     * 报销申请:获取基础费用信息
     *
     * @param billTotalDTO
     * @param result
     * @param integer           0结算单审核 1开票单审核
     * @param invoiceRecordList
     * @return
     */
    List<HrBillReimbursementApplyDetailDTO> assignmentDetailDTOList(HrBillTotalDTO billTotalDTO, HrBillReimbursementApplyDTO result, List<HrBill> hrBills, Integer integer,
                                                                    List<HrBillInvoiceRecord> invoiceRecordList, HrClient rootParentClient);

    /**
     * 报销申请下载报销单以及附件
     *
     * @param ids
     * @return
     */
    String downloadReimbursementForm(List<String> ids);

    /**
     * 生成报销信息
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    HrBillReimbursementApplyDTO generateHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO);

    /**
     * 批量发起报销申请
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    HrBillReimbursementApplyDTO insertHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO);

    /**
     * 从对账结果生成报销申请
     * 社保/医保/公积金
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    HrBillReimbursementApplyDTO insertHrBillReimbursementApplyFromCompareResult(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO);

    /**
     * 代发工资符合账单查询
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    List<HrFeeReviewDTO> findSalaryBill(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO);

    /**
     * 海尔客户查询可用账单汇总
     *
     * @param applyId 报销单ID
     * @return 账单汇总
     */
    List<HrBillTotalDTO> findHaierBill(String applyId);


    /**
     * 生成凭证
     *
     * @param hrBillReimbursementApplyDTO
     */
    @Deprecated
    void createVoucher(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO);

    /**
     * 生成ncc凭证
     *
     * @param hrBillReimbursementApplyDTO
     */
    void createVoucherNcc(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO);

    /**
     * 删除凭证
     *
     * @param hrBillReimbursementApplyId
     */
    void deleteVoucher(String hrBillReimbursementApplyId);

    /**
     * 海尔报销:获取代发工资明细
     *
     * @param billTotalDTO 结算单汇总
     * @param result       报销单
     * @return
     */
    List<HrBillReimbursementApplyDetailDTO> assignmentSalaryDetailList(HrBillTotalDTO billTotalDTO, HrBillReimbursementApplyDTO result);

    /**
     * 根据对账结果删除，只删除未发起的申请
     *
     * @param billCompareResultIds
     */
    void deleteByBillCompareResultId(List<String> billCompareResultIds);

    /**
     * 根据申请id查询银行存款记录
     *
     * @param applyId
     * @return
     */
    List<BankDepositDTO> getBankDepositList(String applyId);
}
