package cn.casair.service;

import cn.casair.domain.HrApplyChecker;
import cn.casair.dto.HrApplyCheckerDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 审核明细服务类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface HrApplyCheckerService extends IService<HrApplyChecker> {

    /**
     * 审核信息
     * @param applyId 服务申请ID
     * @param checkerId 审核人ID
     * @param checkerName 审核人名称
     * @param checkerResult 审核结果(0:未审核 1:审核通过 2:审核拒绝 3:待定)-----AuditInForEnum.CheckerResult
     * @param checkerReason 拒绝理由
     * @param checkerRemark 备注
     */
     void saveHrApplyChecker(String applyId, String checkerId, String checkerName, Integer checkerResult, String checkerReason, String checkerRemark);

    /**
     * 创建审核明细
     *
     * @param hrApplyCheckerDTO
     * @return
     */
    HrApplyCheckerDTO createHrApplyChecker(HrApplyCheckerDTO hrApplyCheckerDTO);

    /**
     * 修改审核明细
     *
     * @param hrApplyCheckerDTO
     * @return
     */
    Optional<HrApplyCheckerDTO> updateHrApplyChecker(HrApplyCheckerDTO hrApplyCheckerDTO);

    /**
     * 查询审核明细详情
     *
     * @param id
     * @return
     */
    HrApplyCheckerDTO getHrApplyChecker(String id);

    /**
     * 删除审核明细
     *
     * @param id
     */
    void deleteHrApplyChecker(String id);

    /**
     * 批量删除审核明细
     *
     * @param ids
     */
    void deleteHrApplyChecker(List<String> ids);

    /**
     * 分页查询审核明细
     *
     * @param hrApplyCheckerDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrApplyCheckerDTO hrApplyCheckerDTO, Long pageNumber, Long pageSize);

    /**
     * 操作信息
     * @param applyId 申请Id
     * @return
     */
    List<HrApplyCheckerDTO> findApplyChecker(String applyId);

}
