<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrSocialSecurityRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , social_security_name, area, name_of_beneficiary, receiving_account, account_bank, unit_pension, work_injury, unit_unemployment, personal_pension, personal_unemployment, unit_medical, personal_medical,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrSocialSecurity">
        <id column="id" property="id"/>
        <result column="social_security_name" property="socialSecurityName"/>
        <result column="area" property="area"/>
        <result column="name_of_beneficiary" property="nameOfBeneficiary"/>
        <result column="receiving_account" property="receivingAccount"/>
        <result column="account_bank" property="accountBank"/>
        <result column="unit_pension" property="unitPension"/>
        <result column="work_injury" property="workInjury"/>
        <result column="unit_unemployment" property="unitUnemployment"/>
        <result column="personal_pension" property="personalPension"/>
        <result column="personal_unemployment" property="personalUnemployment"/>
        <result column="unit_medical" property="unitMedical"/>
        <result column="personal_medical" property="personalMedical"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getClientSocialSecurity" resultType="cn.casair.dto.HrSocialSecurityDTO">
        SELECT
            DISTINCT hss.*,
            hsc.special_field,
            hsc.alone_cardinal,
            hsc.merge_cardinal
        FROM
            hr_social_security hss
        LEFT JOIN hr_client hc ON hc.social_security_type_id = hss.id AND hc.is_delete = 0
        LEFT JOIN hr_social_security_config hsc ON hsc.social_security_id = hss.id
        WHERE hss.is_delete = 0 AND hsc.is_delete = 0
        AND hc.id IN
        <foreach collection="clientIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="getClientBySocialSecurityTypeId" resultType="cn.casair.domain.HrClient">
        SELECT hc.*
        FROM hr_client hc
                 LEFT JOIN hr_social_security hss ON hss.id = hc.social_security_type_id
            AND hss.is_delete = 0
        WHERE hc.is_delete = 0
          AND hss.id = #{id}
    </select>

    <select id="getSocialSecurityTypeList" resultType="cn.casair.dto.HrSocialSecurityDTO">
        SELECT
            id,
            social_security_name
        FROM
            hr_social_security
        WHERE
            is_delete = 0
        ORDER BY
            created_date DESC
    </select>

    <select id="getSelectHrplatform" resultType="cn.casair.dto.HrPlatformAccountDTO">
        SELECT account_number AS accountNumber,
               `password`,id,
               platform_type  AS platformType,
               Issuing_bank   AS IssuingBank
        FROM `hr_platform_account`
        WHERE is_delete = 0
          AND `status` = 0;
    </select>

    <select id="getSocialSecurity" resultType="cn.casair.dto.HrSocialSecurityDTO">
        SELECT * from hr_social_security WHERE is_delete=0
    </select>

    <select id="getAccumulationFund" resultType="cn.casair.dto.HrAccumulationFundDTO">
        SELECT * FROM `hr_accumulation_fund` WHERE is_delete=0;
    </select>

    <select id="socialSecurityListByStaffId" resultType="cn.casair.dto.HrSocialSecurityDTO">
        SELECT
            hse.accumulation_fund_cardinal,
            hse.unit_pension_cardinal,
            hse.unit_unemployment_cardinal,
            hse.unit_maternity_cardinal,
            hse.work_injury_cardinal,
            hse.medical_insurance_cardinal,
            hse.personal_pension_cardinal,
            hse.personal_unemployment_cardinal,
            hse.medical_insurance_cardinal_personal,
            hse.personal_maternity_cardinal,
            hse.unit_large_medical_expense,
            hse.replenish_work_injury_expense,
            hse.personal_large_medical_expense,
            hse.unit_enterprise_annuity,
            hse.personal_enterprise_annuity,
            hse.commercial_insurance,
            ROUND(haf.unit_scale * 100 , 2) unit_scale,
            ROUND(haf.personage_scale * 100 , 2) personage_scale,
            ROUND(hss.unit_pension * 100 , 2) unit_pension,
            ROUND(hss.work_injury * 100 , 2) work_injury,
            ROUND(hss.unit_unemployment * 100 , 2) unit_unemployment,
            ROUND(hss.personal_pension * 100 , 2) personal_pension,
            ROUND(hss.personal_unemployment * 100 , 2) personal_unemployment,
            ROUND(hss.unit_medical * 100 , 2) unit_medical,
            ROUND(hss.personal_medical * 100 , 2) personal_medical
        FROM
            hr_talent_staff hts
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        LEFT JOIN hr_staff_emolument hse ON hts.id = hse.staff_id AND hse.is_delete = 0
        LEFT JOIN hr_accumulation_fund haf ON hc.provident_fund_type_id = haf.id AND haf.is_delete = 0
        LEFT JOIN hr_social_security hss ON hc.social_security_type_id = hss.id AND hss.is_delete = 0
        WHERE hts.is_delete = 0 AND hts.id = #{staffId}
        LIMIT 1
    </select>

    <update id="updateHrSocialSecurity">
        UPDATE `hr_social_security`
            SET `social_security_name`              = #{param.socialSecurityName},
            `area`                                  = #{param.area},
            `name_of_beneficiary`                   = #{param.nameOfBeneficiary},
            `receiving_account`                     = #{param.receivingAccount},
            `account_bank`                          = #{param.accountBank},
            `unit_pension`                          = #{param.unitPension},
            `work_injury`                           = #{param.workInjury},
            `unit_unemployment`                     = #{param.unitUnemployment},
            `personal_pension`                      = #{param.personalPension},
            `personal_unemployment`                 = #{param.personalUnemployment},
            `unit_medical`                          = #{param.unitMedical},
            `personal_medical`                      = #{param.personalMedical},
            `unit_maternity`                        = #{param.unitMaternity},
            `personal_maternity`                    = #{param.personalMaternity},
            `unit_pension_upper_limit`              = #{param.unitPensionUpperLimit},
            `work_injury_upper_limit`               = #{param.workInjuryUpperLimit},
            `unit_unemployment_upper_limit`         = #{param.unitUnemploymentUpperLimit},
            `personal_pension_upper_limit`          = #{param.personalPensionUpperLimit},
            `personal_unemployment_upper_limit`     = #{param.personalUnemploymentUpperLimit},
            `unit_medical_upper_limit`              = #{param.unitMedicalUpperLimit},
            `personal_medical_upper_limit`          = #{param.personalMedicalUpperLimit},
            `unit_maternity_upper_limit`            = #{param.unitMaternityUpperLimit},
            `personal_maternity_upper_limit`        = #{param.personalMaternityUpperLimit},
            `unit_pension_lower_limit`              = #{param.unitPensionLowerLimit},
            `work_injury_lower_limit`               = #{param.workInjuryLowerLimit},
            `unit_unemployment_lower_limit`         = #{param.unitUnemploymentLowerLimit},
            `personal_pension_lower_limit`          = #{param.personalPensionLowerLimit},
            `personal_unemployment_lower_limit`     = #{param.personalUnemploymentLowerLimit},
            `unit_medical_lower_limit`              = #{param.unitMedicalLowerLimit},
            `personal_medical_lower_limit`          = #{param.personalMedicalLowerLimit},
            `unit_maternity_lower_limit`            = #{param.unitMaternityLowerLimit},
            `personal_maternity_lower_limit`        = #{param.personalMaternityLowerLimit},
            `last_modified_date`                    = now()
        WHERE `id` = #{param.id};
    </update>
</mapper>
