package cn.casair.service;

import cn.casair.domain.HrAppendix;
import cn.casair.domain.HrContractAppendix;
import cn.casair.dto.HrContractAppendixDTO;
import cn.casair.dto.JWTMiniDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 员工合同-电签附件服务类
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
public interface HrContractAppendixService extends IService<HrContractAppendix> {

    /**
     * 检查员工工资卡信息
     *
     * @param user
     * @param bankName
     * @param bankNo
     */
    void checkStaffBankInfo(JWTMiniDTO user, String bankName, String bankNo);

    /**
     * 员工确认签名
     *
     * @param hrContractAppendixDTO
     * @return
     */
    HrContractAppendixDTO staffConfirmSign(HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 员工确认合同信息
     *
     * @param hrContractAppendixDTO
     * @return
     */
    HrContractAppendixDTO staffConfirmInformation(HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 检查合同项签订状态
     *
     * @param params
     * @return void
     * <AUTHOR>
     * @date 2021/9/29
     **/
    Map<String, Object> checkContractItemState(Map<String, Object> params);

    /**
     * 员工确认合同 上传合同
     *
     * @return void
     * <AUTHOR>
     * @date 2021/9/16
     * @param contractId
     **/
    void staffUploadContract(String contractId);

    /**
     * 合同文件签名
     *
     * @param hrContractAppendixDTO
     * @return void
     * <AUTHOR>
     * @date 2021/9/16
     **/
    HrAppendix contractSign(HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 合同附件上传
     * 自定义附件上传
     *
     * @param hrContractAppendixDTO
     * @return void
     * <AUTHOR>
     * @date 2021/9/15
     **/
    void contractAppendixUpload(HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 创建员工合同-电签附件
     *
     * @param hrContractAppendixDTO
     * @return
     */
    HrContractAppendixDTO createHrContractAppendix(HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 修改员工合同-电签附件
     *
     * @param hrContractAppendixDTO
     * @return
     */
    Optional<HrContractAppendixDTO> updateHrContractAppendix(HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 查询员工合同-电签附件详情
     *
     * @param id
     * @return
     */
    HrContractAppendixDTO getHrContractAppendix(String id);

    /**
     * 删除员工合同-电签附件
     *
     * @param id
     */
    void deleteHrContractAppendix(String id);

    /**
     * 批量删除员工合同-电签附件
     *
     * @param ids
     */
    void deleteHrContractAppendix(List<String> ids);

    /**
     * 分页查询员工合同-电签附件
     *
     * @param hrContractAppendixDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrContractAppendixDTO hrContractAppendixDTO, Long pageNumber, Long pageSize);
}
