package cn.casair.repository;

import cn.casair.domain.HrStaffSign;
import cn.casair.dto.HrStaffSignDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 员工签名数据库操作类
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Repository
public interface HrStaffSignRepository extends BaseMapper<HrStaffSign> {

    /**
     * 获取员工签名列表
     *
     * @param staffId
     * @return
     */
    List<HrStaffSignDTO> selectListByStaffId(@Param("staffId") String staffId);
}
