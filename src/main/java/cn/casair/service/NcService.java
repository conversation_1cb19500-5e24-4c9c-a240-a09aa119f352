package cn.casair.service;

import cn.casair.ncc.dto.NccVoucherDTO;
import cn.casair.dto.nc.param.VoucherDetailDTO;
import cn.casair.dto.nc.param.VoucherHeadDTO;
import cn.casair.dto.nc.result.NcResultDTO;
import cn.casair.dto.nc.result.NccCustomerDTO;

import java.util.List;

/**
 * NC系统相关
 */
public interface NcService {


    /**
     * 查询会计科目及辅助
     *
     * @return
     */
    Object qryAccountAuxiliary(String code, String name);

    /**
     * 查询客户档案
     */
    List<NccCustomerDTO> qryCustomer(String code, String name);



    /**
     * 生成凭证
     */
    NcResultDTO<?> createVoucher(VoucherHeadDTO voucherHead, List<VoucherDetailDTO> voucherDetailList);

    /**
     * 生成ncc凭证
     *
     * @param nccVoucherDTO
     * @return
     */
    NcResultDTO<NccVoucherDTO> createNccVoucher(NccVoucherDTO nccVoucherDTO);
}
