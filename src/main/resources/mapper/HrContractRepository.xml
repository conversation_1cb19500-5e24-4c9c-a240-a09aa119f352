<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrContractRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , client_id, unit_number, client_name, staff_id, system_num, staff_name, id_no, phone, state, contract_start_date, contract_end_date,
        contract_init_date, contract_sign_date, check_result, check_msg,contract_type,contract_view_id,
        is_delete, created_by, created_date, last_modified_by,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrContract">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="unit_number" property="unitNumber"/>
        <result column="client_name" property="clientName"/>
        <result column="staff_id" property="staffId"/>
        <result column="system_num" property="systemNum"/>
        <result column="staff_name" property="staffName"/>
        <result column="id_no" property="idNo"/>
        <result column="phone" property="phone"/>
        <result column="state" property="state"/>
        <result column="contract_start_date" property="contractStartDate"/>
        <result column="contract_end_date" property="contractEndDate"/>
        <result column="contract_init_date" property="contractInitDate"/>
        <result column="contract_sign_date" property="contractSignDate"/>
        <result column="check_result" property="checkResult"/>
        <result column="check_msg" property="checkMsg"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_view_id" property="contractViewId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Select_SQL">
        <if test="params.ids!=null and params.ids.size()>0">
            AND hc.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="permissionClient!=null and permissionClient.size>0">
            AND hc.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.unitNumber!=null and params.unitNumber!=''">
            AND hc.unit_number LIKE CONCAT( '%', #{params.unitNumber}, '%' )
        </if>
        <if test="params.clientIds!=null and params.clientIds.size()>0">
            AND hc.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.systemNum!=null and params.systemNum!=''">
            AND hc.system_num LIKE CONCAT( '%', #{params.systemNum}, '%' )
        </if>
        <if test="params.staffName!=null and params.staffName!=''">
            AND hc.staff_name LIKE CONCAT( '%', #{params.staffName}, '%' )
        </if>
        <if test="params.idNo!=null and params.idNo!=''">
            AND hc.id_no LIKE CONCAT( '%', #{params.idNo}, '%' )
        </if>
        <if test="params.phone!=null and params.phone!=''">
            AND hc.phone LIKE CONCAT( '%', #{params.phone}, '%' )
        </if>
        <if test="params.states!=null and params.states.size()>0">
            AND hc.state IN
            <foreach collection="params.states" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.contractStartDateStart!=null">
            AND hc.contract_start_date &gt;= #{params.contractStartDateStart}
        </if>
        <if test="params.contractStartDateEnd!=null">
            AND hc.contract_start_date &lt;= #{params.contractStartDateEnd}
        </if>
        <if test="params.contractEndDateStart!=null">
            AND hc.contract_end_date &gt;= #{params.contractEndDateStart}
        </if>
        <if test="params.contractEndDateEnd!=null">
            AND hc.contract_end_date &lt;= #{params.contractEndDateEnd}
        </if>
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='state_str'">
                        ORDER BY
                        hc.state ${params.order}
                    </when>
                    <when test="params.field=='remainder_day'">
                        ORDER BY
                        remainderDay ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY
                        hc.${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY
                hc.created_date DESC
            </otherwise>
        </choose>
    </sql>

    <insert id="batchInsert">
        INSERT INTO hr_contract
        (id, client_id, unit_number, client_name, staff_id, system_num, staff_name, id_no, phone, contract_type, state,
         contract_start_date, contract_end_date, contract_init_date, contract_sign_date,
         created_by, created_date)
        VALUES
        <foreach collection="list" open="(" close=")" separator="," item="i">
            #{i.id},
            #{i.clientId},
            #{i.unitNumber},
            #{i.clientName},
            #{i.staffId},
            #{i.systemNum},
            #{i.staffName},
            #{i.idNo},
            #{i.phone},
            #{i.contractType},
            #{i.state},
            #{i.contractStartDate},
            #{i.contractEndDate},
            #{i.contractInitDate},
            #{i.contractSignDate},
            #{i.createdBy},
            #{i.createdDate}
        </foreach>
    </insert>

    <select id="getNotEffectiveContractList" resultType="cn.casair.domain.HrContract">
        SELECT
            *
        FROM
            hr_contract
        WHERE
            is_delete = 0
            AND staff_id = #{staffId}
            AND `state` = 0
        ORDER BY
            created_date DESC
    </select>

    <select id="getNotActiveContract" resultType="cn.casair.domain.HrContract">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        hr_contract
        WHERE
        is_delete = 0
        AND staff_id = #{staffId}
        AND state = 0
        ORDER BY created_date DESC LIMIT 1
    </select>

    <select id="selectNewestRecord" resultType="cn.casair.domain.HrContract">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        hr_contract
        WHERE
        is_delete = 0
        AND staff_id = #{staffId}
        AND state IN (0,1, 2, 3)
        ORDER BY contract_start_date DESC LIMIT 1
    </select>

    <select id="findPage" resultType="cn.casair.dto.HrContractDTO">
        SELECT
        hc.id,
        hc.client_id,
        hc.unit_number,
        hc.client_name,
        hc.staff_id,
        hc.system_num,
        hc.staff_name,
        hc.id_no,
        hc.phone,
        hc.state,
        hc.contract_start_date,
        hc.contract_end_date,
        hc.contract_init_date,
        hc.contract_sign_date,
        hc.contract_type,
        IF ( hc.state IN ( 1, 2 ), datediff( contract_end_date, now() ), 0 ) remainderDay,
        ham.id archivesId
        FROM
        hr_contract hc
        LEFT JOIN hr_archives_manage ham ON ham.staff_id = hc.staff_id
        AND ham.is_delete = 0
        WHERE hc.is_delete = 0
        <include refid="Select_SQL"/>
    </select>

    <select id="findList" resultType="cn.casair.dto.HrContractDTO">
        SELECT
            hc.id,
            hc.client_id,
            hc.unit_number,
            hc.client_name,
            hc.staff_id,
            hc.system_num,
            hc.staff_name,
            hts.staff_status,
            hc.id_no,
            hc.phone,
            hc.state,
            hc.contract_start_date,
            hc.contract_end_date,
            hc.contract_init_date,
            hc.contract_sign_date,
            hc.contract_type,
            IF( hc.state IN ( 1, 2 ), datediff( hc.contract_end_date, now() ), 0 ) remainderDay,
            hswe.departure_date,
            IF(hads.departure_reason IS NOT NULL,hads.departure_reason,hads.return_reason) reason
        FROM
            hr_contract hc
        LEFT JOIN hr_talent_staff hts ON hc.staff_id = hts.id
        LEFT JOIN hr_apply_departure_staff hads ON hads.staff_id = hts.id AND hads.is_delete = 0
        LEFT JOIN v_staff_work_experience hswe ON hswe.staff_id = hts.id AND (CASE hts.staff_status
        WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END) AND hswe.is_delete = 0
        WHERE hc.is_delete = 0
        <include refid="Select_SQL"/>
    </select>

    <select id="getSecheduleTaskContractList" resultType="cn.casair.dto.HrContractDTO">
        SELECT
            hc.* ,
            hts.staff_status,
            hts.renewal_process
        FROM
            hr_contract hc
            LEFT JOIN hr_talent_staff hts ON hc.staff_id = hts.id
        WHERE
            hc.is_delete = 0
            AND hc.state IN (0 ,1, 2)
#       AND contract_sign_date IS NOT NULL
    </select>

    <select id="historyContract" resultType="cn.casair.dto.HrContractDTO">
        WITH a AS (
            SELECT contract_id, ANY_VALUE ( created_date ) created_date
            FROM hr_contract_appendix
            WHERE is_delete = 0
            GROUP BY contract_id
            ORDER BY created_date DESC
            )
        SELECT
        hc.id,
        hc.client_id,
        hc.client_name,
        hc.contract_init_date,
        hc.staff_id,
        hc.staff_name,
        hs.profession_name postName
        FROM
             a
        LEFT JOIN hr_contract hc ON a.contract_id = hc.id
        LEFT JOIN hr_staff_work_experience swe ON swe.staff_id = hc.staff_id AND hc.client_id = swe.client_id
        AND swe.is_delete = 0
        AND swe.iz_default = 1
        LEFT JOIN hr_station hs ON hs.id = swe.station_id
        WHERE hc.is_delete = 0
        <if test="params.clientId!=null and params.clientId!=''">
            AND hc.client_id = #{params.clientId}
        </if>
        <if test="permissionClient!=null and permissionClient.size>0">
            AND hc.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        ORDER BY hc.contract_init_date DESC
        LIMIT 10
    </select>

    <select id="getContractlistByStaffId" resultType="cn.casair.domain.HrContract">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_contract
        WHERE is_delete = 0
        AND staff_id = #{staffId}
    </select>

    <select id="getContractByStaffIdAndClientId" resultType="cn.casair.domain.HrContract">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        hr_contract
        WHERE
        is_delete = 0
        AND staff_id = #{staffId}
        ORDER BY
        created_date DESC
        LIMIT 1
    </select>

    <select id="getNewestStaffContract" resultType="cn.casair.domain.HrContract">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        hr_contract
        WHERE
        is_delete = 0
        AND staff_id = #{staffId}
        ORDER BY
        created_date DESC
        LIMIT 1
    </select>
    <select id="selectStatus" resultType="cn.casair.domain.HrContract">
        SELECT
        hc.staff_id,
        any_value (hc.id) id,
        any_value (hc.state) state,
        any_value (hc.contract_start_date) contract_start_date,
        any_value (hc.contract_end_date) contract_end_date,
        any_value (hc.is_delete) is_delete
        FROM
        (
        SELECT
        *
        FROM
        hr_contract
        WHERE
        is_delete = 0 AND state in (1,2,3)
        ORDER BY
        contract_start_date DESC
        LIMIT 999999999
        ) AS hc
        where hc.is_delete=0
        <if test="clientIdList!=null and clientIdList.size>0">
            AND hc.client_id IN
            <foreach collection="clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        GROUP BY
        hc.staff_id
    </select>

    <select id="selectContract" resultType="cn.casair.dto.HrContractDTO">
        SELECT
            hc.*,
            hcv.apply_state,
            hcv.states AS contractViewStates,
            hcv.is_again_apply,
            hcv.audit_date
        FROM
            hr_contract hc
        LEFT JOIN hr_contract_view hcv ON hc.contract_view_id = hcv.id AND hcv.is_delete = 0
        ${ew.customSqlSegment}
    </select>
    <select id="selectNewestStaffContract" resultType="cn.casair.dto.HrContractDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_contract
        WHERE
            is_delete = 0
            AND state IN ( 1, 2, 3, 5)
            AND staff_id = #{staffId}
        ORDER BY created_date DESC
        LIMIT 1
    </select>

    <select id="selectLatestContractInfo" resultType="cn.casair.domain.HrContract">
        SELECT
            hc.staff_id,
            any_value (hc.staff_name) staff_name,
            any_value ( hc.contract_start_date ) contract_start_date,
            any_value ( hc.contract_end_date ) contract_end_date
        FROM
            (
            SELECT
                staff_name,
                staff_id,
                contract_start_date,
                contract_end_date
            FROM
                hr_contract
            WHERE is_delete = 0 AND staff_id IN
                <foreach collection="staffIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            ORDER BY contract_start_date ${order} LIMIT 999999999
            ) AS hc
        GROUP BY hc.staff_id
    </select>

    <select id="getNewRenewalContract" resultType="cn.casair.dto.HrContractDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_contract
        WHERE is_delete = 0 AND contract_type = 1
            AND staff_id = #{staffId}
        ORDER BY created_date DESC LIMIT 1
    </select>

    <select id="getStaffNewestContract" resultType="cn.casair.dto.HrContractDTO">
        SELECT
            id,
            staff_id
        FROM
            hr_contract
        WHERE
            id_no = #{idNo}
	        AND is_delete = 0
	    ORDER BY
	        created_date DESC
	        LIMIT 1
    </select>

    <update id="updateContractSignDate">
        UPDATE hr_contract
        SET contract_sign_date = NOW()
        WHERE id = #{contractId}
    </update>

    <update id="updateContractViewId">
        UPDATE hr_contract SET contract_view_id = #{contractViewId} WHERE id = #{contractId}
    </update>

    <update id="updateContractViewNull">
        UPDATE hr_contract SET contract_view_id = NULL WHERE contract_view_id IN
        <foreach collection="contractViewIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="manualUpdateStaffContract">
        UPDATE hr_contract
        SET
        <if test="params.state==0">
            check_result = null,
            check_msg    = null,
        </if>
            state        = #{params.state}
        WHERE
            is_delete = 0
            AND staff_id = #{params.staffId}
            AND contract_type = #{params.contractType}
            <if test="params.contractType == 1">
                ORDER BY contract_end_date DESC LIMIT 1
            </if>
    </update>

    <update id="updateStaffNewestContract">
        UPDATE hr_contract
        SET state = #{state},
        contract_end_date = #{contractEndDate}
        WHERE
            id = #{id}
    </update>
</mapper>
