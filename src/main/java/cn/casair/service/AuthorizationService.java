package cn.casair.service;

import cn.casair.domain.Authorization;
import cn.casair.dto.AuthorizationDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 授权表服务类
 *
 * <AUTHOR>
 * @since 2020-05-12
 */
public interface AuthorizationService extends IService<Authorization> {


    /**
     * 创建授权表
     *
     * @param authorizationDTO
     * @return
     */
    AuthorizationDTO createAuthorization(AuthorizationDTO authorizationDTO);

    /**
     * 修改授权表
     *
     * @param authorizationDTO
     * @return
     */
    Optional<AuthorizationDTO> updateAuthorization(AuthorizationDTO authorizationDTO);

    /**
     * 查询授权表详情
     *
     * @param id
     * @return
     */
    AuthorizationDTO getAuthorization(Integer id);

    /**
     * 删除授权表
     *
     * @param id
     */
    void deleteAuthorization(Integer id);

    /**
     * 批量删除授权表
     *
     * @param ids
     */
    void deleteAuthorization(List<Integer> ids);

    /**
     * 分页查询授权表
     *
     * @param authorizationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(AuthorizationDTO authorizationDTO, Long pageNumber, Long pageSize);
}
