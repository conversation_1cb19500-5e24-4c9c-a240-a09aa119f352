package cn.casair.service;
import cn.casair.domain.HrRemindConf;
import cn.casair.dto.HrRemindConfDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 提醒配置表服务类
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
public interface HrRemindConfService extends IService<HrRemindConf> {


    /**
     * 创建提醒配置表
     * @param hrRemindConfDTO
     * @return
     */
    HrRemindConfDTO createHrRemindConf(HrRemindConfDTO hrRemindConfDTO);

    /**
     * 修改提醒配置表
     * @param hrRemindConfDTO
     * @return
     */
    Optional<HrRemindConfDTO> updateHrRemindConf(HrRemindConfDTO hrRemindConfDTO);

    /**
     * 查询提醒配置表详情
     * @param id
     * @return
     */
    HrRemindConfDTO getHrRemindConf(String id);

    /**
     * 删除提醒配置表
     * @param id
     */
    void deleteHrRemindConf(String id);

    /**
     * 批量删除提醒配置表
     * @param ids
     */
    void deleteHrRemindConf(List<String> ids);

    /**
     * 分页查询提醒配置表
     * @param hrRemindConfDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRemindConfDTO hrRemindConfDTO,Long pageNumber,Long pageSize);

    /**
     * 批量更新状态
     * @param ids
     * @param stateBoolean
     */
    void updateHrRemindConfStates(List<String> ids, Boolean stateBoolean);
}
