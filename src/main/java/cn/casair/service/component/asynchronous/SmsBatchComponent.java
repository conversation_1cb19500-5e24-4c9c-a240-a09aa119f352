package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.HrAccumulationFund;
import cn.casair.domain.HrQuickDeduction;
import cn.casair.domain.User;
import cn.casair.domain.UserRole;
import cn.casair.dto.ApplyStaffImportResultDTO;
import cn.casair.dto.HrTalentStaffDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrAccumulationFundImport;
import cn.casair.dto.excel.HrSmsBatchImport;
import cn.casair.dto.excel.HrUserImport;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrAppendixService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.casair.common.utils.ValidateUtil.isCellPhoneNo;

@Slf4j
@Component
@RequiredArgsConstructor
public class SmsBatchComponent {
    @Value("${file.temp-path}")
    private String tempPath;

    private final SysOperLogService sysOperLogService;

    private final HrAppendixService hrAppendixService;

    private final RedisCache redisCache;

    private final HrTalentStaffRepository hrTalentStaffRepository;

    private final CodeTableService codeTableService;

    @Async
    public void importBatch(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        ExcelImportResult<HrSmsBatchImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrSmsBatchImport.class);

        ImportResultDTO importResult;
        ApplyStaffImportResultDTO resultDTO = new ApplyStaffImportResultDTO();
        ArrayList<String> successPhoneList = new ArrayList<>();
        Map<Integer, String> staffType = codeTableService.findCodeTableByInnerName("staffType");
        Map<Integer, String> staffStates = codeTableService.findCodeTableByInnerName("staffStates");
        try {
            int scale = 0;
            int listSize = result.getList().size();
            ArrayList<HrSmsBatchImport> successResult = new ArrayList<>();
            for (HrSmsBatchImport hrSmsBatchImport : result.getList()) {
                try {
                    if (!isCellPhoneNo(hrSmsBatchImport.getPhone())){
                        throw new CommonException("手机号格式不正确");
                    }

                    if (successPhoneList.contains(hrSmsBatchImport.getPhone())){
                        throw new CommonException("手机号已存在");
                    }
                    HrTalentStaffDTO hrTalentStaffDTO = hrTalentStaffRepository.selectByPhone(hrSmsBatchImport.getPhone());
                    if (hrTalentStaffDTO!=null){
                        hrSmsBatchImport.setStaffStatusLabel(staffStates.get(hrTalentStaffDTO.getStaffStatus()));
                        hrSmsBatchImport.setPersonnelTypeLabel(staffType.get(hrTalentStaffDTO.getPersonnelType()));
                        hrSmsBatchImport.setClientName(hrTalentStaffDTO.getClientName());
                        hrSmsBatchImport.setCertificateNum(hrTalentStaffDTO.getCertificateNum());
                    }
                    successResult.add(hrSmsBatchImport);
                    successPhoneList.add(hrSmsBatchImport.getPhone());
                } catch (Exception e) {
                    log.error("批量短信异常:{}", e.getMessage());
                    if (e.getMessage().contains("已存在")) {
                        hrSmsBatchImport.setDuplicate(true);
                        hrSmsBatchImport.setErrorMsg(e.getMessage());
                    } else {
                        hrSmsBatchImport.setErrorMsg(e.getMessage());
                    }
                } finally {
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }

            }
           //List<HrTalentStaffDTO> hrTalentStaffDTOList = hrTalentStaffRepository.selectByPhone(successPhoneList);

//            for (HrTalentStaffDTO hrTalentStaffDTO : hrTalentStaffDTOList) {
//                hrTalentStaffDTO.setStaffStatusLabel(staffStates.get(hrTalentStaffDTO.getStaffStatus()));
//                hrTalentStaffDTO.setPersonnelTypeLabel(staffType.get(hrTalentStaffDTO.getPersonnelType()));
//            }
            resultDTO.setHrTalentStaffDTOList(successResult);
            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("批量发送短信导入", HrSmsBatchImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);
            BeanUtils.copyProperties(importResult,resultDTO);
            redisCache.setCacheObject(redisKey, JSON.toJSONString(resultDTO), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.SMS_TEMPLATE.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("批量发送短信异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "批量发送短信导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }
}
