<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrApplyOpLogsRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, apply_id,apply_staff_id, checker_id, message,checker_type,appendix_ids,serve_type,remark
            is_delete ,created_by ,created_date ,last_modified_by ,last_modified_date
        </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrApplyOpLogs">
        <id column="id" property="id"/>
        <result column="apply_id" property="applyId"/>
        <result column="apply_staff_id" property="applyStaffId"/>
        <result column="checker_id" property="checkerId"/>
        <result column="message" property="message"/>
        <result column="checker_type" property="checkerType"/>
        <result column="appendix_ids" property="appendixIds"/>
        <result column="serve_type" property="serveType"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findApplyOpLogsList" resultType="cn.casair.dto.HrApplyOpLogsDTO">
        SELECT
            haol.*, CASE haol.checker_type
        WHEN 0 THEN
            concat(concat('(',sr.role_name,') '),su.real_name)
        ELSE
            CASE haol.serve_type WHEN 0 THEN  concat('(待入职员工) ',hts.`name`) ELSE concat('(员工) ',hts.`name`) END
        END AS realName
        FROM
            hr_apply_op_logs haol
        LEFT JOIN sys_user su ON haol.checker_id = su.id
        LEFT JOIN sys_user_role sur ON su.id = sur.user_id AND sur.is_delete = 0
        LEFT JOIN sys_role sr ON sur.role_id = sr.id AND sr.is_delete = 0
        LEFT JOIN hr_talent_staff hts ON haol.checker_id = hts.id
        WHERE
        haol.is_delete = 0
        <if test="applyId != null">
            AND haol.apply_id = #{applyId}
        </if>
        <if test="applyStaffId != null">
            AND haol.apply_staff_id = #{applyStaffId}
        </if>
        <if test="applyStaffId == null">
            AND haol.apply_staff_id IS NULL
        </if>
        ORDER BY
            haol.created_date ASC
    </select>

    <select id="getByApplyId" resultType="cn.casair.dto.HrApplyOpLogsDTO">
        SELECT haop.*, u.`real_name`, mu.avatar_url, sr.`role_name` FROM `hr_apply_op_logs` haop
          LEFT JOIN sys_user u ON haop.`checker_id` = u.`id` and u.is_delete = 0
            left join hr_mini_user mu on haop.checker_id = mu.user_id
          LEFT JOIN sys_user_role sur ON u.`id` = sur.`user_id` AND sur.`is_delete` = 0
          LEFT JOIN sys_role sr ON sr.`id` = sur.`role_id` AND sr.`is_delete` = 0
        where haop.apply_id = #{applyId} and haop.is_delete = 0
        order by haop.created_date asc
    </select>

    <select id="reviewerInfo" resultType="cn.casair.dto.HrApplyOpLogsDTO">
        SELECT
            su.real_name,
            hal.created_date,
            hal.*
        FROM
            hr_apply_op_logs hal
            LEFT JOIN sys_user su ON hal.checker_id = su.id
            LEFT JOIN sys_user_role sur ON su.id = sur.user_id
            LEFT JOIN sys_role sr ON sur.role_id = sr.id
        WHERE hal.is_delete = 0 AND su.is_delete = 0
            AND hal.message LIKE concat('%', #{message}, '%')
            AND sr.role_key = #{roleKey}
            AND hal.apply_id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>
</mapper>
