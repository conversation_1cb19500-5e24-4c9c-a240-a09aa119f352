<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRemindConfRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, remind_key, title, dept, rule_cont, rule_day, role_key, status,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>
    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrRemindConf">
                    <id column="id" property="id"/>
                    <result column="remind_key" property="remindKey"/>
                    <result column="title" property="title"/>
                    <result column="dept" property="dept"/>
                    <result column="rule_cont" property="ruleCont"/>
                    <result column="rule_day" property="ruleDay"/>
                    <result column="role_key" property="roleKey"/>
                    <result column="status" property="status"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

    <select id="selectByRemindKey" resultType="cn.casair.domain.HrRemindConf">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_remind_conf
        WHERE is_delete = 0
        AND remind_key = #{remindKey}
    </select>

    <select id="getRemindConfByRoleAndKey" resultType="cn.casair.dto.HrRemindConfDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_remind_conf
        WHERE
            is_delete = 0
            AND remind_key = #{remindKey}
            AND FIND_IN_SET(#{currentRoleId}, role_key)
    </select>

</mapper>
