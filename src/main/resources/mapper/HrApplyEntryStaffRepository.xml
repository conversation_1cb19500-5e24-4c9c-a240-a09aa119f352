<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrApplyEntryStaffRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, client_id, protocol_id,staff_id, `name`, certificate_num, sex, phone, station_id, personnel_type, contract_start_date, contract_end_date, basic_wage,
        apply_id, household_registration, seniority_wage_base, pay_year, pay_monthly,seniority_pay, staff_type, internship_duration,internship_date, staff_remark,
        social_security_cardinal, medical_insurance_cardinal, accumulation_fund_cardinal,social_security_cardinal_personal,medical_insurance_cardinal_personal,
        apply_status, apply_step, apply_checker_id, iz_default,entry_status,iz_show,
        is_delete,created_by,created_date,last_modified_by,last_modified_date
    </sql>

    <sql id="findSql">
        SELECT hc.unit_number,
               hc.client_name,
               hp.agreement_number,
               hp.agreement_title,
               hp.agreement_type,
               hc.specialized_id,
               haes.*,
               hs.profession_name,
               hs.profession_name AS stationIdLabel,
               su.real_name AS specialized,
               IF(haes.sex = 1, '男', '女') AS sexLabel
        FROM hr_apply_entry_staff haes
                 LEFT JOIN hr_client hc ON haes.client_id = hc.id AND hc.is_delete = 0
                 LEFT JOIN hr_protocol hp ON haes.protocol_id = hp.id AND hp.is_delete = 0
                 LEFT JOIN hr_station hs ON haes.station_id = hs.id AND hs.is_delete = 0
                 LEFT JOIN sys_user su ON hc.specialized_id = su.id AND su.is_delete = 0
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrApplyEntryStaff">
        <id column="id" property="id"/>
        <result column="apply_id" property="applyId"/>
        <result column="client_id" property="clientId"/>
        <result column="protocol_id" property="protocolId"/>
        <result column="staff_id" property="staffId"/>
        <result column="name" property="name"/>
        <result column="certificate_num" property="certificateNum"/>
        <result column="sex" property="sex"/>
        <result column="phone" property="phone"/>
        <result column="station_id" property="stationId"/>
        <result column="personnel_type" property="personnelType"/>
        <result column="contract_start_date" property="contractStartDate"/>
        <result column="contract_end_date" property="contractEndDate"/>
        <result column="basic_wage" property="basicWage"/>
        <result column="social_security_cardinal" property="socialSecurityCardinal"/>
        <result column="medical_insurance_cardinal" property="medicalInsuranceCardinal"/>
        <result column="accumulation_fund_cardinal" property="accumulationFundCardinal"/>
        <result column="social_security_cardinal_personal" property="socialSecurityCardinalPersonal"/>
        <result column="medical_insurance_cardinal_personal" property="medicalInsuranceCardinalPersonal"/>
        <result column="household_registration" property="householdRegistration"/>
        <result column="seniority_wage_base" property="seniorityWageBase"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="seniority_pay" property="seniorityPay"/>
        <result column="staff_type" property="staffType"/>
        <result column="internship_duration" property="internshipDuration"/>
        <result column="internship_date" property="internshipDate"/>
        <result column="staff_remark" property="staffRemark"/>
        <result column="apply_status" property="applyStatus"/>
        <result column="apply_step" property="applyStep"/>
        <result column="apply_checker_id" property="applyCheckerId"/>
        <result column="iz_default" property="izDefault"/>
        <result column="entry_status" property="entryStatus"/>
        <result column="iz_show" property="izShow"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <update id="updateStaffApplyEntryState">
        UPDATE hr_apply_entry_staff
        SET entry_status = #{newStateKey}
        WHERE is_delete = 0
          AND client_id = #{clientId}
          AND staff_id = #{staffId}
          AND entry_status = #{oldStateKey}
    </update>

    <update id="updateStaffApplyEntryApplyState">
        UPDATE hr_apply_entry_staff
        SET apply_status = #{applyState},
            apply_step   = #{applyStep}
        WHERE is_delete = 0
          AND client_id = #{clientId}
          AND staff_id = #{staffId}
    </update>


    <select id="getApplyEntryStaffInfo" resultType="cn.casair.domain.HrApplyEntryStaff">
        SELECT id,
               apply_id,
               client_id,
               protocol_id,
               staff_id,
               name,
               certificate_num,
               sex,
               phone,
               station_id,
               personnel_type,
               contract_start_date,
               contract_end_date,
               basic_wage,
               social_security_cardinal,
               medical_insurance_cardinal,
               accumulation_fund_cardinal,
               apply_status,
               apply_step,
               apply_checker_id,
               iz_default,
               entry_status,
               seniority_wage_base,
               pay_monthly,
               seniority_pay
        FROM hr_apply_entry_staff
        WHERE is_delete = 0 AND entry_status <![CDATA[ < ]]> 7 AND client_id = #{clientId} AND staff_id = #{staffId}
    </select>

    <select id="findPage" resultType="cn.casair.dto.HrApplyEntryStaffDTO">
        SELECT
        b.unit_number,
        b.client_name,
        b.specialized_id,
        a.*, c.profession_name,
        d.real_name AS specialized,
        e.iz_start_end,
        IF(a.sex = 1, '男', '女') AS sexLabel
        FROM
        hr_apply_entry_staff a
        LEFT JOIN hr_client b ON a.client_id = b.id AND b.is_delete = 0
        LEFT JOIN hr_station c ON a.station_id = c.id AND c.is_delete = 0
        LEFT JOIN sys_user d ON b.specialized_id = d.id AND d.is_delete = 0 AND d.user_status = 1
        LEFT JOIN hr_talent_staff e ON a.staff_id = e.id AND e.is_delete = 0
        WHERE a.is_delete = 0 AND e.is_delete = 0 AND a.iz_show = 0 AND a.entry_status > 0
        <if test="param.unitNumber != null and param.unitNumber != ''">
            AND b.unit_number LIKE concat('%', #{param.unitNumber}, '%')
        </if>
        <if test="param.clientId != null">
            AND b.id = #{param.clientId}
        </if>
        <if test="param.name != null and param.name != ''">
            AND a.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND a.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND a.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.stationId != null">
            AND a.station_id = #{param.stationId}
        </if>
        <if test="param.professionName != null and param.professionName != ''">
            AND c.profession_name LIKE concat('%', #{param.professionName}, '%')
        </if>
        <if test="param.createdDateStart!=null">
            AND a.created_date &gt;= #{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd!=null">
            AND a.created_date &lt; date_add(#{param.createdDateEnd}, interval 1 day)
        </if>
        <if test="param.sexList!=null and param.sexList.size() > 0">
            AND a.sex IN
            <foreach collection="param.sexList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.stationIdList!=null and param.stationIdList.size() > 0">
            AND a.station_id IN
            <foreach collection="param.stationIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.entryStatusList!=null and param.entryStatusList.size() > 0">
            AND a.entry_status IN
            <foreach collection="param.entryStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.personnelTypeList!=null and param.personnelTypeList.size() > 0">
            AND a.personnel_type IN
            <foreach collection="param.personnelTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND b.id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.userIdList!=null and param.userIdList.size() > 0">
            AND d.id IN
            <foreach collection="param.userIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.contractStartDateStart!=null">
            AND a.contract_start_date &gt;= #{param.contractStartDateStart}
        </if>
        <if test="param.contractStartDateEnd!=null">
            AND a.contract_start_date &lt; date_add(#{param.contractStartDateEnd}, interval 1 day)
        </if>
        <if test="param.contractEndDateStart!=null">
            AND a.contract_end_date &gt;= #{param.contractEndDateStart}
        </if>
        <if test="param.contractEndDateEnd!=null">
            AND a.contract_end_date &lt; date_add(#{param.contractEndDateEnd}, interval 1 day)
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            order by ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY a.created_date DESC
        </if>
    </select>

    <select id="getApplyEntryStaffById" resultType="cn.casair.dto.HrApplyEntryStaffDTO">
        SELECT b.unit_number,
               b.client_name,
               a.*,
               c.profession_name,
               d.agreement_number,
               d.agreement_title,
               b.business_type,
               IF(a.sex = 1, '男', '女') AS sexLabel
        FROM hr_apply_entry_staff a
                 LEFT JOIN hr_client b ON a.client_id = b.id
                 LEFT JOIN hr_station c ON a.station_id = c.id
                 LEFT JOIN hr_protocol d ON a.protocol_id = d.id

        WHERE a.is_delete = 0
          AND a.id = #{id}
    </select>

    <select id="entryCountByYear" resultType="java.util.Map">
        SELECT m.id                months,
               IFNULL(countNum, 0) countNum
        FROM (
                 select 1 as id
                 union
                 select 2
                 union
                 select 3
                 union
                 select 4
                 union
                 select 5
                 union
                 select 6
                 union
                 select 7
                 union
                 select 8
                 union
                 select 9
                 union
                 select 10
                 union
                 select 11
                 union
                 select 12) m
                 LEFT JOIN (
            SELECT DATE_FORMAT(hswe.board_date, '%c') months,
                   COUNT(hts.id)                      countNum
            FROM hr_talent_staff hts
                     LEFT JOIN hr_staff_work_experience hswe ON hts.id = hswe.staff_id
                AND hswe.is_delete = 0 AND hswe.iz_default = 1  AND hts.client_id = hswe.client_id
            WHERE hts.is_delete = 0
                      AND hts.iz_default = 0
                      AND YEAR (hswe.board_date) = #{particularYear}
        GROUP BY
            months
            ) n
        ON m.id = n.months
        ORDER BY m.id ASC
    </select>

    <select id="selectHrApplyEntryStaffList" resultType="cn.casair.dto.HrApplyEntryStaffDTO">
        <include refid="findSql"/>
        WHERE haes.is_delete = 0
        <if test="applyId !=null">
            AND haes.apply_id = #{applyId}
        </if>
        <if test="applyStatus !=null">
            AND haes.apply_status = #{applyStatus}
        </if>
        <if test="applyStaffIds!=null and applyStaffIds.size() > 0">
            AND haes.id IN
            <foreach collection="applyStaffIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        ORDER BY haes.created_date
    </select>

    <select id="getHrApplyEntryStaffList" resultType="cn.casair.dto.excel.HrApplyEntryStaffFailExport">
        <include refid="findSql"/>
        WHERE haes.is_delete = 0 AND haes.apply_status = 2
        <if test="applyId !=null">
            AND haes.apply_id = #{applyId}
        </if>
    </select>

    <select id="exportToHiredStaff" resultType="cn.casair.dto.excel.HrApplyEntryStaffFailExport">
        SELECT
        b.unit_number,
        b.client_name,
        e.staff_status,
        b.specialized_id,
        a.*, c.profession_name,
        d.real_name AS specialized,e.iz_start_end
        FROM
        hr_apply_entry_staff a
        LEFT JOIN hr_client b ON a.client_id = b.id AND b.is_delete = 0
        LEFT JOIN hr_station c ON a.station_id = c.id AND c.is_delete = 0
        LEFT JOIN hr_protocol hp ON a.protocol_id = hp.id AND hp.is_delete = 0
        LEFT JOIN sys_user d ON b.specialized_id = d.id AND d.is_delete = 0 AND d.user_status = 1
        LEFT JOIN hr_talent_staff e ON a.staff_id = e.id AND e.is_delete = 0
        WHERE a.is_delete = 0 AND a.iz_show = 0 AND a.entry_status > 0
        <if test="permissionClient!=null and permissionClient.size()>0">
            AND b.id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.ids!=null and param.ids.size() > 0">
            AND a.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.unitNumber != null and param.unitNumber != ''">
            AND b.unit_number LIKE concat('%', #{param.unitNumber}, '%')
        </if>
        <if test="param.clientId != null">
            AND b.id = #{param.clientId}
        </if>
        <if test="param.name != null and param.name != ''">
            AND a.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND a.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND a.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.stationId != null">
            AND a.station_id = #{param.stationId}
        </if>
        <if test="param.professionName != null and param.professionName != ''">
            AND c.profession_name LIKE concat('%', #{param.professionName}, '%')
        </if>
        <if test="param.createdDateStart!=null">
            AND a.created_date &gt;= #{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd!=null">
            AND a.created_date &lt; date_add(#{param.createdDateEnd}, interval 1 day)
        </if>
        <if test="param.sexList!=null and param.sexList.size() > 0">
            AND a.sex IN
            <foreach collection="param.sexList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.stationIdList!=null and param.stationIdList.size() > 0">
            AND a.station_id IN
            <foreach collection="param.stationIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.entryStatusList!=null and param.entryStatusList.size() > 0">
            AND a.entry_status IN
            <foreach collection="param.entryStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.personnelTypeList!=null and param.personnelTypeList.size() > 0">
            AND a.personnel_type IN
            <foreach collection="param.personnelTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND b.id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.userIdList!=null and param.userIdList.size() > 0">
            AND d.id IN
            <foreach collection="param.userIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.contractStartDateStart!=null">
            AND a.contract_start_date &gt;= #{param.contractStartDateStart}
        </if>
        <if test="param.contractStartDateEnd!=null">
            AND a.contract_start_date &lt; date_add(#{param.contractStartDateEnd}, interval 1 day)
        </if>
        <if test="param.contractEndDateStart!=null">
            AND a.contract_end_date &gt;= #{param.contractEndDateStart}
        </if>
        <if test="param.contractEndDateEnd!=null">
            AND a.contract_end_date &lt; date_add(#{param.contractEndDateEnd}, interval 1 day)
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            order by ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY a.created_date DESC
        </if>
    </select>

    <select id="getApplyEntryStaffByObject" resultType="cn.casair.domain.HrApplyEntryStaff">
        SELECT
            id,
            client_id,
            protocol_id,
            staff_id,
            name,
            certificate_num,
            sex,
            phone,
            station_id,
            personnel_type,
            contract_start_date,
            contract_end_date,
            basic_wage,
            social_security_cardinal,
            medical_insurance_cardinal,
            accumulation_fund_cardinal,
            apply_status,
            apply_step,
            apply_checker_id,
            iz_default,
            entry_status,
            seniority_wage_base,
            pay_monthly,
            seniority_pay
        FROM
            hr_apply_entry_staff
        WHERE
            iz_default = 0
          AND client_id = #{clientId}
          AND staff_id = #{staffId}
        ORDER BY created_date DESC LIMIT 1
    </select>

    <select id="selectClientId" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
        ht.*,
        ha.internship_date
        FROM
        `hr_talent_staff` ht
        LEFT JOIN hr_apply_entry_staff ha ON ha.id = ht.apply_staff_id
        WHERE
        ht. is_delete = "0" AND ht.iz_default = 0

        <if test="staffStatusList!=null and  staffStatusList.size() > 0">
            and ht.staff_status in
            <foreach collection="staffStatusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="clientList!=null and  clientList.size() > 0">
            and ht.client_id in
            <foreach collection="clientList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>

    <select id="getByStaffId" resultType="cn.casair.domain.HrApplyEntryStaff">
        SELECT * FROM
        hr_apply_entry_staff
        WHERE
        is_delete = 0
        ANd staff_id = #{staffId}
    </select>

    <update id="updateStaffApplyEntryStateById">
        UPDATE hr_apply_entry_staff
        SET entry_status = #{entryState}
        WHERE id = #{id}
    </update>

    <update id="updaterStaffTurn">
        UPDATE hr_staff_turn_positive
        SET enterprise_states =#{enterpriseStates},
            enterprise_refuse =#{enterpriseRefuse},
            enterprise_user_id =#{enterpriseUserId},
            enterprise_date =#{enterpriseDate}

            WHERE
            id = #{id} and is_delete=0
    </update>

    <update id="updaterStaff">
        UPDATE hr_talent_staff set staff_status=4 where id=#{s} and is_delete=0
    </update>

    <update id="updaterStaffStatus">
        UPDATE hr_talent_staff set staff_status=10 where id=#{s} and is_delete=0
    </update>

    <update id="manualUpdateStaffEntryState">
        UPDATE hr_apply_entry_staff
        SET
            apply_step   = #{params.applyStep},
            entry_status = #{params.entryStatus}
        WHERE
            staff_id = #{params.staffId};
    </update>
</mapper>
