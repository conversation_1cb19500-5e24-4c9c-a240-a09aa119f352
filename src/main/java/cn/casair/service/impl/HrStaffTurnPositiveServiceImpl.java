package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.enums.StaffEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.StringUtils;
import cn.casair.domain.HrAppendix;
import cn.casair.domain.HrApplyDepartureStaff;
import cn.casair.domain.HrApplyEntryStaff;
import cn.casair.domain.HrApplyOpLogs;
import cn.casair.domain.HrClient;
import cn.casair.domain.HrStaffTurnPositive;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.HrStaffTurnPositiveDTO;
import cn.casair.dto.HrTalentStaffDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.mapper.HrStaffTurnPositiveMapper;
import cn.casair.repository.HrAppendixRepository;
import cn.casair.repository.HrApplyDepartureStaffRepository;
import cn.casair.repository.HrApplyEntryStaffRepository;
import cn.casair.repository.HrApplyOpLogsRepository;
import cn.casair.repository.HrClientRepository;
import cn.casair.repository.HrStaffTurnPositiveRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.repository.UserRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrAppletMessageService;
import cn.casair.service.HrApplyOpLogsService;
import cn.casair.service.HrClientService;
import cn.casair.service.HrNotificationUserService;
import cn.casair.service.HrStaffTurnPositiveService;
import cn.casair.service.HrUpcomingService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 员工转正（客户pc）服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrStaffTurnPositiveServiceImpl extends ServiceImpl<HrStaffTurnPositiveRepository, HrStaffTurnPositive> implements HrStaffTurnPositiveService {

    private final HrClientService hrClientService;
    private final HrStaffTurnPositiveRepository hrStaffTurnPositiveRepository;
    private final HrStaffTurnPositiveMapper hrStaffTurnPositiveMapper;
    private final HrApplyEntryStaffRepository hrApplyEntryStaffRepository;
    private final CodeTableService codeTableService;
    private final HrAppendixRepository hrAppendixRepository;
    private final UserRepository userRepository;
    private final HrClientRepository hrClientRepository;
    private final HrApplyDepartureStaffRepository hrApplyDepartureStaffRepository;
    @Resource
    private HrNotificationUserService hrNotificationUserService;
    private final HrApplyOpLogsRepository hrApplyOpLogsRepository;
    private final HrUpcomingService hrUpcomingService;

    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final SysOperLogService sysOperLogService;

    @Resource
    private HrAppletMessageService hrAppletMessageService;
    private final HrApplyOpLogsService hrApplyOpLogsService;

    private final HrAppendixService hrAppendixService;

    public HrStaffTurnPositiveServiceImpl(HrClientService hrClientService, HrStaffTurnPositiveRepository hrStaffTurnPositiveRepository,
                                          HrStaffTurnPositiveMapper hrStaffTurnPositiveMapper, HrApplyEntryStaffRepository hrApplyEntryStaffRepository,
                                          CodeTableService codeTableService, HrAppendixRepository hrAppendixRepository, HrClientRepository hrClientRepository,
                                          UserRepository userRepository, HrClientRepository hrClientRepository1, HrApplyDepartureStaffRepository hrApplyDepartureStaffRepository,
                                          HrApplyOpLogsRepository hrApplyOpLogsRepository, HrUpcomingService hrUpcomingService, HrTalentStaffRepository hrTalentStaffRepository,
                                          SysOperLogService sysOperLogService, HrApplyOpLogsService hrApplyOpLogsService, HrAppendixService hrAppendixService) {
        this.hrClientService = hrClientService;
        this.hrStaffTurnPositiveRepository = hrStaffTurnPositiveRepository;
        this.hrStaffTurnPositiveMapper = hrStaffTurnPositiveMapper;
        this.hrApplyEntryStaffRepository = hrApplyEntryStaffRepository;
        this.codeTableService = codeTableService;
        this.hrAppendixRepository = hrAppendixRepository;
        this.userRepository = userRepository;
        this.hrClientRepository = hrClientRepository1;

        this.hrApplyDepartureStaffRepository = hrApplyDepartureStaffRepository;
        this.hrApplyOpLogsRepository = hrApplyOpLogsRepository;
        this.hrUpcomingService = hrUpcomingService;
        this.hrTalentStaffRepository = hrTalentStaffRepository;
        this.sysOperLogService = sysOperLogService;
        this.hrApplyOpLogsService = hrApplyOpLogsService;
        this.hrAppendixService = hrAppendixService;
    }

    /**
     * 创建员工转正（客户pc）
     *
     * @param hrStaffTurnPositiveDTO
     * @return
     */
    @Override
    public HrStaffTurnPositiveDTO createHrStaffTurnPositive(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO) throws InterruptedException {
        log.info("Create new HrStaffTurnPositive:{}", hrStaffTurnPositiveDTO);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        //获取当前日期
        DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDateTime newdate = LocalDateTime.now();
        if (!jwtUserDTO.getCurrentRoleKey().equals("client")) {
            HrStaffTurnPositiveDTO hrStaffTurnPositiveDTOs = new HrStaffTurnPositiveDTO();
            hrStaffTurnPositiveDTOs.setEnterpriseAppendixId(hrStaffTurnPositiveDTO.getClientAppendixId());
            hrStaffTurnPositiveDTOs.setEnterpriseRemark(hrStaffTurnPositiveDTO.getClientRemark());
            hrStaffTurnPositiveDTOs.setStaffId(hrStaffTurnPositiveDTO.getStaffId());
            hrStaffTurnPositiveDTOs.setId(hrStaffTurnPositiveDTO.getId());
            hrStaffTurnPositiveDTOs.setEnterpriseUserId(jwtUserDTO.getId());
            hrStaffTurnPositiveDTOs.setEnterpriseDate(newdate);
            hrStaffTurnPositiveDTOs.setEnterpriseStates(hrStaffTurnPositiveDTO.getStates());
            String ciientId = this.hrStaffTurnPositiveRepository.selectGetClientId(hrStaffTurnPositiveDTO.getStaffId());
            this.hrStaffTurnPositiveRepository.updateHrStaffTurnPositive(hrStaffTurnPositiveDTOs);

            this.hrApplyEntryStaffRepository.updaterStaff(hrStaffTurnPositiveDTO.getStaffId());
            HrStaffTurnPositive hrStaffTurnPositive = this.hrStaffTurnPositiveMapper.toEntity(hrStaffTurnPositiveDTOs);
            String staffName = this.hrStaffTurnPositiveRepository.selectStaffName(hrStaffTurnPositive.getStaffId());
            String miz = staffName + "，根据您在实习/试用期的工作表现，经公司领导批准，现同意您的转正申请，恭喜您成为公司的正式员工";
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrStaffTurnPositive.getId(), hrStaffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), jwtUserDTO.getUserName() + "专管员确认了" + staffName + "转正申请", null, ServiceCenterEnum.GET_APPLICATIONS.getKey());
            hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.GET_APPLICATIONS.getKey(), hrStaffTurnPositiveDTO.getId(), hrStaffTurnPositiveDTO.getStaffId(), miz, false, null);


            //添加操作信息--小程序
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("certificateStatus", "2");
            jsonObject.put("message", "您的转正申请专管员已确认");
            String message = "专管员确认了转正申请。####" + jsonObject.toJSONString();
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrStaffTurnPositiveDTO.getId(), hrStaffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), message, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());

            Thread.sleep(1000);
            //添加操作信息--小程序
            JSONObject jsonObjects = new JSONObject();
            jsonObjects.put("certificateStatus", "3");
            jsonObjects.put("message", "您的转正申请已完成，恭喜您将于试用/实习期结束后成为正式员工");
            String messages = "您的转正申请已完成，恭喜您将于试用/实习期结束后成为正式员工。####" + jsonObjects.toJSONString();
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrStaffTurnPositiveDTO.getId(), hrStaffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), messages, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());
            //代办变成已办
            this.hrUpcomingService.updateUpcoming(hrStaffTurnPositiveDTO.getId());

            return this.hrStaffTurnPositiveMapper.toDto(hrStaffTurnPositive);
        } else {
            hrStaffTurnPositiveDTO.setClientUserId(jwtUserDTO.getId());
            hrStaffTurnPositiveDTO.setClientDate(newdate);
            HrStaffTurnPositive hrStaffTurnPositive = this.hrStaffTurnPositiveMapper.toEntity(hrStaffTurnPositiveDTO);
            hrStaffTurnPositive.setLastModifiedDate(newdate);
            this.hrStaffTurnPositiveRepository.updateSalary(hrStaffTurnPositive.getStaffId(), hrStaffTurnPositive.getSalary());
            if (StringUtils.isNotEmpty(hrStaffTurnPositive.getId())) {
                this.hrStaffTurnPositiveRepository.updateById(hrStaffTurnPositive);
            } else {
                this.hrStaffTurnPositiveRepository.insert(hrStaffTurnPositive);
            }

            if (hrStaffTurnPositiveDTO.getStates() == 1) {
                //创建通知配置
                String clientId = this.hrStaffTurnPositiveRepository.selectClientId(hrStaffTurnPositiveDTO.getClientUserId());
                String staffName = this.hrStaffTurnPositiveRepository.selectStaffName(hrStaffTurnPositive.getStaffId());
                this.hrNotificationUserService.saveRemindContent(clientId, ServiceCenterEnum.GET_APPLICATIONS.getKey(), 1, staffName + "转正申请客户已审核通过。" + staffName + "转正申请待专管员审核", jwtUserDTO.getId());
                //添加操作信息--小程序
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("certificateStatus", "1");
                jsonObject.put("message", "您的转正申请客户已确认");
                String message = "客户通过了您的转正申请。####" + jsonObject.toJSONString();
                hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrStaffTurnPositiveDTO.getId(), hrStaffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), message, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());
                /**
                 * 服务生成待办
                 * @param ServiceId 服务id
                 * @param staffId 员工id
                 * @param title 待办标题
                 * @param date 待办日期
                 * @param type 0 给专管员生成待办  1 给客户生成待办
                 */
                HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrStaffTurnPositive.getStaffId());
                hrUpcomingService.createServiceUpcoming(hrStaffTurnPositive.getId(), hrStaffTurnPositive.getStaffId(), hrTalentStaff.getName() + "交了转正申请", LocalDate.now(), 0);


            }
            if (hrStaffTurnPositiveDTO.getStates() == 0) {
                //拒绝添加到待离职表
                //获取当前日期
                HrApplyDepartureStaff hrApplyDepartureStaff = new HrApplyDepartureStaff();
                LocalDate newdates = LocalDate.now();

                hrApplyDepartureStaff.setDepartureDate(newdates);
                hrApplyDepartureStaff.setLastModifiedDate(LocalDateTime.now());
                hrApplyDepartureStaff.setClientId(hrStaffTurnPositiveDTO.getClientId());
                hrApplyDepartureStaff.setStaffId(hrStaffTurnPositiveDTO.getStaffId());
                if (hrStaffTurnPositiveDTO.getRefuse() != null) {
                    hrApplyDepartureStaff.setDepartureReason(hrStaffTurnPositiveDTO.getRefuse());
                }
                hrApplyDepartureStaff.setDepartureApplicant(3);
                this.hrApplyDepartureStaffRepository.insert(hrApplyDepartureStaff);

                //添加操作信息--小程序
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("certificateStatus", "4");
                jsonObject.put("message", "您的转正申请公司未通过");
                String message = "客户拒绝了您的转正申请。####" + jsonObject.toJSONString();
                hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrStaffTurnPositiveDTO.getId(), hrStaffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), message, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());
                //修改员工状态
                this.hrApplyEntryStaffRepository.updaterStaffStatus(hrStaffTurnPositiveDTO.getStaffId());

            }
            String staffName = this.hrStaffTurnPositiveRepository.selectStaffName(hrStaffTurnPositive.getStaffId());

            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrStaffTurnPositive.getId(), hrStaffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), jwtUserDTO.getUserName() + "客户确认了" + staffName + "转正申请", null, ServiceCenterEnum.GET_APPLICATIONS.getKey());

            return this.hrStaffTurnPositiveMapper.toDto(hrStaffTurnPositive);
        }


    }

    /**
     * 修改员工转正（客户pc）
     *
     * @param hrStaffTurnPositiveDTO
     * @return
     */
    @Override
    public Optional<HrStaffTurnPositiveDTO> updateHrStaffTurnPositive(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO) {
        return Optional.ofNullable(this.hrStaffTurnPositiveRepository.selectById(hrStaffTurnPositiveDTO.getId()))
            .map(roleTemp -> {
                HrStaffTurnPositive hrStaffTurnPositive = this.hrStaffTurnPositiveMapper.toEntity(hrStaffTurnPositiveDTO);
                this.hrStaffTurnPositiveRepository.updateById(hrStaffTurnPositive);
                log.info("Update HrStaffTurnPositive:{}", hrStaffTurnPositiveDTO);
                return hrStaffTurnPositiveDTO;
            });
    }

    /**
     * 查询员工转正（客户pc）详情
     *
     * @param
     * @return
     */
    @Override
    public HrStaffTurnPositiveDTO getHrStaffTurnPositive(String staffId) {
        log.info("Get HrStaffTurnPositive :{}", staffId);
        HrStaffTurnPositiveDTO hrStaffTurnPositive = this.hrStaffTurnPositiveRepository.selectTurn(staffId);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();

        if (hrStaffTurnPositive.getStaffStatus() != null) {//员工状态
            Map<Integer, String> staffStates = codeTableService.findCodeTableByInnerName("staffStates");
            hrStaffTurnPositive.setStaffStatusLabel(staffStates.get(Integer.parseInt(hrStaffTurnPositive.getStaffStatus())));
        }
        if (hrStaffTurnPositive.getPersonnelType() != null) {//人员类型
            Map<Integer, String> staffType = codeTableService.findCodeTableByInnerName("staffType");
            hrStaffTurnPositive.setPersonnelTypeLabel(staffType.get(Integer.parseInt(String.valueOf(hrStaffTurnPositive.getPersonnelType()))));
        }


        List<HrAppendix> AppendixId = new ArrayList<>();
        if (!jwtUserDTO.getCurrentRoleKey().equals("client")) {
            if (hrStaffTurnPositive.getClientAppendixId() != null) {
                List<String> AppendixIds = Arrays.asList(hrStaffTurnPositive.getClientAppendixId().split(","));
                AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(AppendixIds);
            }
        } else {
            if (hrStaffTurnPositive.getClientAppendixId() != null && !hrStaffTurnPositive.getClientAppendixId().equals("")) {
                List<String> AppendixIds = Arrays.asList(hrStaffTurnPositive.getClientAppendixId().split(","));
                AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(AppendixIds);
            }
        }
        hrStaffTurnPositive.setAppendixIdList(AppendixId);

        return hrStaffTurnPositive;
    }

    /**
     * 删除员工转正（客户pc）
     *
     * @param id
     */
    @Override
    public void deleteHrStaffTurnPositive(String id) {
        Optional.ofNullable(this.hrStaffTurnPositiveRepository.selectById(id))
            .ifPresent(hrStaffTurnPositive -> {
                this.hrStaffTurnPositiveRepository.deleteById(id);
                log.info("Delete HrStaffTurnPositive:{}", hrStaffTurnPositive);
            });
    }

    /**
     * 批量删除员工转正（客户pc）
     *
     * @param ids
     */
    @Override
    public void deleteHrStaffTurnPositive(List<String> ids) {
        log.info("Delete HrStaffTurnPositives:{}", ids);
        this.hrStaffTurnPositiveRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询员工转正（客户pc）
     *
     * @param hrStaffTurnPositiveDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO, Long pageNumber, Long pageSize) {
        IPage<HrStaffTurnPositiveDTO> iPage = new Page();
        if (fillQueryDTO(hrStaffTurnPositiveDTO)) return null;
        Page<HrStaffTurnPositive> page = new Page<>(pageNumber, pageSize);
        iPage = this.hrStaffTurnPositiveRepository.selectStaff(page, hrStaffTurnPositiveDTO);
        fillResult(iPage.getRecords());
        return iPage;
    }

    @Override
    public String export(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO) {
        List<HrStaffTurnPositiveDTO> list = fillQueryDTO(hrStaffTurnPositiveDTO) ? Collections.emptyList() : this.hrStaffTurnPositiveRepository.findList(hrStaffTurnPositiveDTO);
        List<String> ids = list.stream().map(HrStaffTurnPositiveDTO::getId).collect(Collectors.toList());
        fillResult(list);

        String fileUrl = this.hrAppendixService.uploadExportFile(list, ModuleTypeEnum.POSITIVE.getValue(), HrStaffTurnPositiveDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.POSITIVE.getValue(), BusinessTypeEnum.EXPORT.getKey(),
            JSON.toJSONString(ids), ids.size(), fileUrl);
        return fileUrl;
    }

    private void fillResult(List<HrStaffTurnPositiveDTO> list) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        for (HrStaffTurnPositiveDTO record : list) {
            if (record.getStaffStatus() != null) {//员工状态
                Map<Integer, String> staffStates = codeTableService.findCodeTableByInnerName("staffStates");
                int staffsta = Integer.parseInt(record.getStaffStatus());
                String StaffStatu = staffStates.get(staffsta);
                record.setStaffStatusLabel(StaffStatu);
                this.hrNotificationUserService.saveRemindContent(record.getClientId(), ServiceCenterEnum.GET_APPLICATIONS.getKey(), null, record.getClientName() + "提交了转正申请。" + record.getClientName() + "转正申请待客户审核", jwtUserDTO.getId());
                if (record.getId() == null) {
                    record.setId(record.getStaffId());
                }
            }
        }
    }

    private boolean fillQueryDTO(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO) {
        if (hrStaffTurnPositiveDTO.getField() != null) {
            if (hrStaffTurnPositiveDTO.getField().equals("client_name")) {
                hrStaffTurnPositiveDTO.setField("clientName");
            }
        }
        if (hrStaffTurnPositiveDTO.getField() != null) {
            if (hrStaffTurnPositiveDTO.getField().equals("application_description")) {
                hrStaffTurnPositiveDTO.setField("applicationDescription");
            }
        }
        if (hrStaffTurnPositiveDTO.getField() != null) {
            if (hrStaffTurnPositiveDTO.getField().equals("certificate_num")) {
                hrStaffTurnPositiveDTO.setField("htf.certificate_num");
            }
        }
        if (hrStaffTurnPositiveDTO.getField() != null) {
            if (hrStaffTurnPositiveDTO.getField().equals("boar_date")) {
                hrStaffTurnPositiveDTO.setField("boarDate");
            }
        }
        if (hrStaffTurnPositiveDTO.getField() != null) {
            if (hrStaffTurnPositiveDTO.getField().equals("internship_date")) {
                hrStaffTurnPositiveDTO.setField("internshipDate");
            }
        }
        if (hrStaffTurnPositiveDTO.getField() != null) {
            if (hrStaffTurnPositiveDTO.getField().equals("staff_type")) {
                hrStaffTurnPositiveDTO.setField("staffType");
            }
        }

        if (hrStaffTurnPositiveDTO.getField() != null) {
            if (hrStaffTurnPositiveDTO.getField().equals("specialized_name")) {
                hrStaffTurnPositiveDTO.setField("specializedName");
            }
        }


        //判断前台是否传了客户id
        //没有传获取数据权限的客户
        if (CollectionUtils.isEmpty(hrStaffTurnPositiveDTO.getClientList())) {
            List<String> clientId = this.hrClientService.selectClientIdByUserId();
            if (clientId != null) {
                hrStaffTurnPositiveDTO.setClientList(clientId);
            }
        }

        //获取当前日期
        DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd");
        LocalDate newdate = LocalDate.now();

        Integer time = this.hrStaffTurnPositiveRepository.selecttime("staff_turn");
        if (time == null) {
            time = 0;
        }
        //协议终止时间提前60天
        Calendar calendar = Calendar.getInstance();

        calendar.add(calendar.DATE, time);
        LocalDate terminationtime = LocalDate.parse(newtime.format(calendar.getTime()));
        List<String> staffId = new ArrayList<>();
        //查询出客户id的所有员工
        List<String> stature = new ArrayList<>();
        stature.add("3");
        stature.add("9");
        hrStaffTurnPositiveDTO.setStaffStatusList(stature);
        List<HrTalentStaffDTO> hrTalentStaffDTO = this.hrApplyEntryStaffRepository.selectClientId(hrStaffTurnPositiveDTO);
        List<String> staffIds = hrTalentStaffDTO.stream().map(HrTalentStaffDTO::getId).distinct().collect(Collectors.toList());
        List<String> staffidLists = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(staffIds)) {
            //获取主动提交的
            QueryWrapper<HrStaffTurnPositive> dw = new QueryWrapper<>();
            dw.eq("type", 1);
            dw.in("staff_id", staffIds);
            List<HrStaffTurnPositive> hrStaffTurnPositives = this.hrStaffTurnPositiveRepository.selectList(dw);
            if (CollectionUtils.isNotEmpty(hrStaffTurnPositives)) {
                List<String> staffIda = hrStaffTurnPositives.stream().map(HrStaffTurnPositive::getStaffId).distinct().collect(Collectors.toList());
                staffidLists.addAll(staffIda);
            }
        }

        //遍历获取距离员工转正还有15天的员工id
        for (HrTalentStaffDTO talentStaffDTO : hrTalentStaffDTO) {
            //获取时间差
            if (talentStaffDTO.getInternshipDate() != null) {
                long date = newdate.until(talentStaffDTO.getInternshipDate(), ChronoUnit.DAYS);
                if (date <= time) {
                    if (talentStaffDTO.getId() != null) {
                        staffId.add(talentStaffDTO.getId());
                    } else {
                        staffId.add("");
                    }
                }
            }
            staffidLists.addAll(staffId);
        }
        hrStaffTurnPositiveDTO.setStaffIdList(staffidLists);
        if (CollectionUtils.isEmpty(hrStaffTurnPositiveDTO.getStaffIdList())) {
            return true;
        }
        return false;
    }


    /**
     * POST /hr-staff-turn-positives/deletes
     * <p>
     * 批量员工同意（客户pc）
     *
     * @param
     * @return
     */
    @Override
    public ResponseEntity<?> uodateHrStaffTurnBatchConsent(List<HrStaffTurnPositiveDTO> hrStaffTurnPositiveDTO, Integer status) throws InterruptedException {
        if (CollectionUtils.isEmpty(hrStaffTurnPositiveDTO)) {
            throw new CommonException("请先选择员工");
        }
        List<String> successList = new ArrayList<>();//成功集合
        List<String> errorStatusList = new ArrayList<>();//失败集合
        //获取当前日期
        DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDateTime newdate = LocalDateTime.now();
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        for (HrStaffTurnPositiveDTO staffTurnPositiveDTO : hrStaffTurnPositiveDTO) {
            QueryWrapper<HrApplyEntryStaff> qw = new QueryWrapper<>();
            HrStaffTurnPositiveDTO hrStaffTurnPositiveDTOs = new HrStaffTurnPositiveDTO();
            HrApplyDepartureStaff hrApplyDepartureStaff = new HrApplyDepartureStaff();
            qw.eq("staff_id", staffTurnPositiveDTO.getStaffId());
            qw.eq("is_delete", 0);
            //获取基本薪资
            HrApplyEntryStaff hrApplyEntryStaff = this.hrApplyEntryStaffRepository.selectOne(qw);
            //判断角色
            //专管员角色
            if (!jwtUserDTO.getCurrentRoleKey().equals("client")) {
                if (status == 1) {//专管员同意
                    if (staffTurnPositiveDTO.getStates() == 1) {
                        hrStaffTurnPositiveDTOs.setId(staffTurnPositiveDTO.getId());
                        hrStaffTurnPositiveDTOs.setEnterpriseStates(status);
                        hrStaffTurnPositiveDTOs.setEnterpriseUserId(jwtUserDTO.getId());
                        hrStaffTurnPositiveDTOs.setEnterpriseDate(newdate);
                        hrStaffTurnPositiveDTOs.setStaffId(staffTurnPositiveDTO.getStaffId());
                        if (staffTurnPositiveDTO.getRefuse() != null) {
                            hrStaffTurnPositiveDTOs.setEnterpriseRefuse(staffTurnPositiveDTO.getRefuse());
                        }

                        this.hrApplyEntryStaffRepository.updaterStaffTurn(hrStaffTurnPositiveDTOs);
                        this.hrApplyEntryStaffRepository.updaterStaff(staffTurnPositiveDTO.getStaffId());
                        //操作日志
                        String staffName = this.hrStaffTurnPositiveRepository.selectStaffName(staffTurnPositiveDTO.getStaffId());
                        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrStaffTurnPositiveDTOs.getId(), staffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), jwtUserDTO.getUserName() + "专管员确认了" + staffName + "转正申请", null, ServiceCenterEnum.GET_APPLICATIONS.getKey());

                        String ciientId = this.hrStaffTurnPositiveRepository.selectGetClientId(staffTurnPositiveDTO.getId());
                        String miz = staffName + "，根据您在实习/试用期的工作表现，经公司领导批准，现同意您的转正申请，恭喜您成为公司的正式员工";
                        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.GET_APPLICATIONS.getKey(), staffTurnPositiveDTO.getId(), staffTurnPositiveDTO.getStaffId(), miz, false, null);


                        //添加操作信息--小程序
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("certificateStatus", "2");
                        jsonObject.put("message", "您的转正申请专管员已确认");
                        String message = "专管员确认了转正申请。####" + jsonObject.toJSONString();
                        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(staffTurnPositiveDTO.getId(), staffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), message, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());

                        Thread.sleep(1000);
                        //添加操作信息--小程序
                        JSONObject jsonObjects = new JSONObject();
                        jsonObjects.put("certificateStatus", "3");
                        jsonObjects.put("message", "您的转正申请已完成，恭喜您将于试用/实习期结束后成为正式员工");
                        String messages = "您的转正申请已完成，恭喜您将于试用/实习期结束后成为正式员工。####" + jsonObjects.toJSONString();
                        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(staffTurnPositiveDTO.getId(), staffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), messages, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());

                        this.hrUpcomingService.updateUpcoming(staffTurnPositiveDTO.getId());
                        successList.add(staffTurnPositiveDTO.getName());
                    } else {
                        errorStatusList.add(staffTurnPositiveDTO.getName());
                    }
                } else if (status == 2) {//专管员拒绝
                    if (staffTurnPositiveDTO.getStates() == 1) {
                        hrStaffTurnPositiveDTOs.setId(staffTurnPositiveDTO.getId());
                        hrStaffTurnPositiveDTOs.setEnterpriseStates(status);
                        hrStaffTurnPositiveDTOs.setEnterpriseUserId(jwtUserDTO.getId());
                        hrStaffTurnPositiveDTOs.setEnterpriseDate(newdate);
                        hrStaffTurnPositiveDTOs.setStaffId(staffTurnPositiveDTO.getStaffId());
                        if (staffTurnPositiveDTO.getRefuse() != null) {
                            hrStaffTurnPositiveDTOs.setEnterpriseRefuse(staffTurnPositiveDTO.getRefuse());
                        }
                        this.hrApplyEntryStaffRepository.updaterStaffTurn(hrStaffTurnPositiveDTOs);
                        //拒绝添加到待离职表
                        //获取当前日期
                        LocalDate newdates = LocalDate.now();
                        LocalDateTime newdatess = LocalDateTime.now();
                        hrApplyDepartureStaff.setDepartureDate(newdates);
                        hrApplyDepartureStaff.setLastModifiedDate(newdatess);
                        hrApplyDepartureStaff.setClientId(staffTurnPositiveDTO.getClientId());
                        hrApplyDepartureStaff.setStaffId(staffTurnPositiveDTO.getStaffId());
                        if (staffTurnPositiveDTO.getRefuse() != null) {
                            hrApplyDepartureStaff.setDepartureReason(staffTurnPositiveDTO.getRefuse());
                        }
                        hrApplyDepartureStaff.setDepartureApplicant(3);
                        hrApplyDepartureStaff.setId(null);
                        this.hrApplyDepartureStaffRepository.insert(hrApplyDepartureStaff);
                        this.hrApplyEntryStaffRepository.updaterStaffStatus(staffTurnPositiveDTO.getStaffId());

                        //添加操作信息--小程序
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("certificateStatus", "4");
                        jsonObject.put("message", "您的转正申请专管员已拒绝");
                        String message = "专管员拒绝了您的转正申请。####" + jsonObject.toJSONString();
                        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(staffTurnPositiveDTO.getId(), staffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), message, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());


                        //操作日志
                        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrStaffTurnPositiveDTOs.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getUserName() + "专管员拒绝了" + staffTurnPositiveDTO.getName() + "转正申请", null, ServiceCenterEnum.GET_APPLICATIONS.getKey());
                        successList.add(staffTurnPositiveDTO.getName());
                    } else {
                        errorStatusList.add(staffTurnPositiveDTO.getName());
                    }
                }
            } else {
                //客户角色
                if (status == 1) {//客户同意
                    if (staffTurnPositiveDTO.getStates() == null || staffTurnPositiveDTO.getStates() == 0) {
                        hrStaffTurnPositiveDTOs.setStates(status);
                        hrStaffTurnPositiveDTOs.setStaffId(staffTurnPositiveDTO.getStaffId());
                        hrStaffTurnPositiveDTOs.setSalary(hrApplyEntryStaff.getBasicWage());
                        hrStaffTurnPositiveDTOs.setClientUserId(jwtUserDTO.getId());
                        hrStaffTurnPositiveDTOs.setClientDate(newdate);
                        hrStaffTurnPositiveDTOs.setLastModifiedDate(newdate);
                        if (staffTurnPositiveDTO.getRefuse() != null) {
                            hrStaffTurnPositiveDTOs.setRefuse(staffTurnPositiveDTO.getRefuse());
                        }
                        HrStaffTurnPositive hrStaffTurnPositive = this.hrStaffTurnPositiveMapper.toEntity(hrStaffTurnPositiveDTOs);
                        this.hrStaffTurnPositiveRepository.updateSalary(hrStaffTurnPositive.getStaffId(), hrStaffTurnPositive.getSalary());

                        if (StringUtils.isNotEmpty(staffTurnPositiveDTO.getId())) {
                            hrStaffTurnPositive.setId(staffTurnPositiveDTO.getId());
                            this.hrStaffTurnPositiveRepository.updateById(hrStaffTurnPositive);
                        } else {
                            this.hrStaffTurnPositiveRepository.insert(hrStaffTurnPositive);
                        }

                        //添加操作信息--小程序
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("certificateStatus", "1");
                        jsonObject.put("message", "您的转正申请客户已通过");
                        String message = "客户通过了您的转正申请。####" + jsonObject.toJSONString();
                        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(staffTurnPositiveDTO.getId(), staffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), message, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());


                        /**
                         * 服务生成待办
                         * @param ServiceId 服务id
                         * @param staffId 员工id
                         * @param title 待办标题
                         * @param date 待办日期
                         * @param type 0 给专管员生成待办  1 给客户生成待办
                         */
                        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrStaffTurnPositive.getStaffId());
                        hrUpcomingService.createServiceUpcoming(hrStaffTurnPositive.getId(), hrStaffTurnPositive.getStaffId(), hrTalentStaff.getName() + "交了转正申请", LocalDate.now(), 0);


                        //操作日志
                        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrStaffTurnPositive.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getUserName() + "客户通过了" + staffTurnPositiveDTO.getName() + "转正申请", null, ServiceCenterEnum.GET_APPLICATIONS.getKey());

                        successList.add(staffTurnPositiveDTO.getName());
                    } else {
                        errorStatusList.add(staffTurnPositiveDTO.getName());
                    }
                } else if (status == 0) {//客户拒绝
                    if (staffTurnPositiveDTO.getStates() == null || staffTurnPositiveDTO.getStates() == 0) {
                        hrStaffTurnPositiveDTOs.setStates(status);
                        hrStaffTurnPositiveDTOs.setStaffId(staffTurnPositiveDTO.getStaffId());
                        hrStaffTurnPositiveDTOs.setSalary(hrApplyEntryStaff.getBasicWage());
                        hrApplyDepartureStaff.setClientId(staffTurnPositiveDTO.getClientId());
                        hrStaffTurnPositiveDTOs.setClientDate(newdate);
                        if (staffTurnPositiveDTO.getRefuse() != null) {
                            hrStaffTurnPositiveDTOs.setRefuse(staffTurnPositiveDTO.getRefuse());
                        }
                        HrStaffTurnPositive hrStaffTurnPositive = this.hrStaffTurnPositiveMapper.toEntity(hrStaffTurnPositiveDTOs);
                        if (StringUtils.isNotEmpty(staffTurnPositiveDTO.getId())) {
                            hrStaffTurnPositive.setId(staffTurnPositiveDTO.getId());
                            this.hrStaffTurnPositiveRepository.updateById(hrStaffTurnPositive);
                        } else {
                            this.hrStaffTurnPositiveRepository.insert(hrStaffTurnPositive);
                        }

                        //添加操作信息--小程序
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("certificateStatus", "4");
                        jsonObject.put("message", "您的转正申请公司未通过");
                        String message = "客户拒绝了您的转正申请。####" + jsonObject.toJSONString();
                        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(staffTurnPositiveDTO.getId(), staffTurnPositiveDTO.getStaffId(), jwtUserDTO.getId(), message, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());


                        //拒绝添加到待离职表
                        //获取当前日期
                        LocalDate newdates = LocalDate.now();
                        LocalDateTime newdatess = LocalDateTime.now();
                        hrApplyDepartureStaff.setDepartureDate(newdates);
                        hrApplyDepartureStaff.setLastModifiedDate(newdatess);
                        hrApplyDepartureStaff.setClientId(staffTurnPositiveDTO.getClientId());
                        hrApplyDepartureStaff.setStaffId(staffTurnPositiveDTO.getStaffId());
                        if (staffTurnPositiveDTO.getRefuse() != null) {
                            hrApplyDepartureStaff.setDepartureReason(staffTurnPositiveDTO.getRefuse());
                        }
                        hrApplyDepartureStaff.setDepartureApplicant(3);
                        this.hrApplyDepartureStaffRepository.insert(hrApplyDepartureStaff);
                        this.hrApplyEntryStaffRepository.updaterStaffStatus(staffTurnPositiveDTO.getStaffId());

                        //操作日志
                        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrStaffTurnPositive.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getUserName() + "客户拒绝了" + staffTurnPositiveDTO.getName() + "转正申请", null, ServiceCenterEnum.GET_APPLICATIONS.getKey());

                        successList.add(staffTurnPositiveDTO.getName());
                    } else {
                        errorStatusList.add(staffTurnPositiveDTO.getName());
                    }
                }

            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态不支持该操作");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    //小程序员工申请转正
    @Override
    public HrStaffTurnPositiveDTO staffTurnPositives(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO) {
        HrStaffTurnPositive hrStaffTurnPositive = this.hrStaffTurnPositiveMapper.toEntity(hrStaffTurnPositiveDTO);
        hrStaffTurnPositive.setType(1);
        this.hrStaffTurnPositiveRepository.insert(hrStaffTurnPositive);
        // 操作日志
        HrTalentStaff hrTalentStaffs = this.hrTalentStaffRepository.selectById(hrStaffTurnPositiveDTO.getStaffId());
        hrStaffTurnPositiveDTO.setName(hrTalentStaffs.getName());
        hrStaffTurnPositiveDTO.setSystemNum(hrTalentStaffs.getSystemNum());
        hrStaffTurnPositiveDTO.setCertificateNum(hrTalentStaffs.getCertificateNum());
        hrStaffTurnPositiveDTO.setPhone(hrTalentStaffs.getPhone());
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.POSITIVE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffTurnPositiveDTO),
            HrStaffTurnPositiveDTO.class,
            null,
            JSON.toJSONString(hrStaffTurnPositiveDTO)
        );
        //添加操作信息--小程序
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("certificateStatus", "0");
        jsonObject.put("message", "您的转正申请已提交");
        String message = " 您的转正申请。####" + jsonObject.toJSONString();
        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrStaffTurnPositive.getId(), hrStaffTurnPositiveDTO.getStaffId(), hrStaffTurnPositiveDTO.getStaffId(), message, null, true, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());
        //创建待办

        /**
         * 服务生成待办
         * @param ServiceId 服务id
         * @param staffId 员工id
         * @param title 待办标题
         * @param date 待办日期
         * @param type 0 给专管员生成待办  1 给客户生成待办
         */
        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrStaffTurnPositive.getStaffId());
        hrUpcomingService.createServiceUpcoming(hrStaffTurnPositive.getId(), hrStaffTurnPositive.getStaffId(), hrTalentStaff.getName() + "交了转正申请", LocalDate.now(), 1);


        return this.hrStaffTurnPositiveMapper.toDto(hrStaffTurnPositive);

    }

    /**
     * (PC)创建员工转正申请
     *
     * @param hrStaffTurnPositiveDTO
     * <AUTHOR> Lyric.Lin 2022/12/30 12:42
     */
    @Override
    public HrStaffTurnPositiveDTO createStaffTurnPositiveApply(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO) {
        String staffId = hrStaffTurnPositiveDTO.getStaffId();
        if (StringUtils.isBlank(staffId)) {
            throw new CommonException("员工id不能为空");
        }
        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(staffId);
        if (hrTalentStaff == null) {
            throw new CommonException("员工不存在");
        }
        if (hrTalentStaff.getStaffStatus() != StaffEnum.StaffStatusEnum.TO_INTERNSHIP.getKey() && hrTalentStaff.getStaffStatus() != StaffEnum.StaffStatusEnum.ON_PROBATION.getKey()) {
            throw new CommonException("非试用期/实习中的员工不能申请转正");
        }
        // 判断是否已有处理中的转正申请
        QueryWrapper qw = new QueryWrapper();
        qw.eq("staff_id", staffId);
        qw.eq("is_delete", 0);
        List<HrStaffTurnPositive> dataList = this.hrStaffTurnPositiveRepository.selectList(qw);
        boolean ifHasApplyRecord = false; // 是否有审核中的申请
        if (dataList != null && !dataList.isEmpty()) {
            for (int i = 0; i < dataList.size(); i++) {
                HrStaffTurnPositive hrStaffTurnPositive = dataList.get(i);
                if (hrStaffTurnPositive.getStates() == null || hrStaffTurnPositive.getEnterpriseStates() == null) { // 有审核中的记录
                    ifHasApplyRecord = true;
                    break;
                }
            }
        }
        if (ifHasApplyRecord) {
            throw new CommonException("已有审核中的申请，不能重复申请");
        }
        // 新增一条转正申请
        HrStaffTurnPositive hrStaffTurnPositive = this.hrStaffTurnPositiveMapper.toEntity(hrStaffTurnPositiveDTO);
        hrStaffTurnPositive.setType(1);
        this.hrStaffTurnPositiveRepository.insert(hrStaffTurnPositive);
        // 添加日志
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String logInfo = jwtUserDTO.getRealName() + "从管理端为" + hrTalentStaff.getName()+"新增了转正申请";
        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrStaffTurnPositive.getId(), null, jwtUserDTO.getId(), logInfo, null, ServiceCenterEnum.GET_APPLICATIONS.getKey());
        //创建待办
        hrUpcomingService.createServiceUpcoming(hrStaffTurnPositive.getId(), hrStaffTurnPositive.getStaffId(), hrTalentStaff.getName() + "提交了转正申请", LocalDate.now(), 1);
        // 操作日志
        hrStaffTurnPositiveDTO.setName(hrTalentStaff.getName());
        hrStaffTurnPositiveDTO.setSystemNum(hrTalentStaff.getSystemNum());
        hrStaffTurnPositiveDTO.setCertificateNum(hrTalentStaff.getCertificateNum());
        hrStaffTurnPositiveDTO.setPhone(hrTalentStaff.getPhone());
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.POSITIVE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffTurnPositiveDTO),
            HrStaffTurnPositiveDTO.class,
            null,
            JSON.toJSONString(hrStaffTurnPositiveDTO)
        );
        return this.hrStaffTurnPositiveMapper.toDto(hrStaffTurnPositive);
    }

    //小程序员工申请转正首页
    @Override
    public List<HrStaffTurnPositiveDTO> getStaffTurnPositives(String staffId) {
        QueryWrapper<HrStaffTurnPositive> qw = new QueryWrapper<>();
        qw.eq("staff_id", staffId);
        List<HrStaffTurnPositive> hrStaffTurnPositives = this.hrStaffTurnPositiveRepository.selectList(qw);
        List<HrStaffTurnPositiveDTO> hrStaffTurnPositiveDTOS = this.hrStaffTurnPositiveMapper.toDto(hrStaffTurnPositives);
        for (HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO : hrStaffTurnPositiveDTOS) {
            HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(hrStaffTurnPositiveDTO.getStaffId());

            if (hrTalentStaff != null) {
                if (StringUtils.isNotEmpty(hrTalentStaff.getClientId())) {
                    HrClient hrClient = this.hrClientRepository.selectById(hrTalentStaff.getClientId());
                    hrStaffTurnPositiveDTO.setClientName(hrClient.getClientName());
                }
                if (StringUtils.isNotEmpty(hrTalentStaff.getApplyStaffId())) {
                    HrApplyEntryStaff hrApplyEntryStaff = this.hrApplyEntryStaffRepository.selectById(hrTalentStaff.getApplyStaffId());
                    hrStaffTurnPositiveDTO.setInternshipDate(hrApplyEntryStaff.getInternshipDate());
                }
            }
        }

        return hrStaffTurnPositiveDTOS;
    }

    //查詢流程
    @Override
    public HrStaffTurnPositiveDTO getStaffTurnPositivesProcess(String staffId, String serviceId) {
        QueryWrapper<HrStaffTurnPositive> qsw = new QueryWrapper();
        qsw.eq("staff_id", staffId);
        HrStaffTurnPositive hrStaffTurnPositive = this.hrStaffTurnPositiveRepository.selectOne(qsw);
        HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO = this.hrStaffTurnPositiveMapper.toDto(hrStaffTurnPositive);
        QueryWrapper<HrApplyOpLogs> qw = new QueryWrapper<>();
        qw.eq(StringUtils.isNotEmpty("serviceId"), "apply_id", serviceId);
        qw.eq("apply_staff_id", staffId);
        qw.eq("checker_type", 1);
        qw.orderByAsc("created_date");
        List<HrApplyOpLogs> hrApplyOpLogsList = this.hrApplyOpLogsRepository.selectList(qw);

        if (CollectionUtils.isNotEmpty(hrApplyOpLogsList)) {
            hrStaffTurnPositiveDTO.setHrApplyOpLogs(hrApplyOpLogsList);
        }
        if (StringUtils.isNotEmpty(hrStaffTurnPositive.getClientAppendixId())) {
            List<HrAppendix> AppendixId = new ArrayList<>();
            List<String> AppendixIds = Arrays.asList(hrStaffTurnPositive.getClientAppendixId().split(","));
            AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(AppendixIds);
            hrStaffTurnPositiveDTO.setClientAppendixIdLists(AppendixId);
        }
        if (StringUtils.isNotEmpty(hrStaffTurnPositive.getEnterpriseAppendixId())) {
            List<HrAppendix> AppendixId = new ArrayList<>();
            List<String> AppendixIds = Arrays.asList(hrStaffTurnPositive.getEnterpriseAppendixId().split(","));
            AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(AppendixIds);
            hrStaffTurnPositiveDTO.setEnterpriseAppendixIdLists(AppendixId);
        }

        return hrStaffTurnPositiveDTO;
    }


    /**
     * 员工转正查询转正时间
     *
     * @return
     */
    @Override
    public HrStaffTurnPositiveDTO staffTurnPositivestime(String staffId) {
        HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO = new HrStaffTurnPositiveDTO();
        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(staffId);
        HrApplyEntryStaff hrApplyEntryStaff = this.hrApplyEntryStaffRepository.selectById(hrTalentStaff.getApplyStaffId());
        QueryWrapper<HrStaffTurnPositive> qsw = new QueryWrapper();
        qsw.eq("staff_id", staffId);
        HrStaffTurnPositive hrStaffTurnPositive = this.hrStaffTurnPositiveRepository.selectOne(qsw);
        if (hrStaffTurnPositive != null) {
            hrStaffTurnPositiveDTO = this.hrStaffTurnPositiveMapper.toDto(hrStaffTurnPositive);
        }

        if (hrApplyEntryStaff != null) {
            hrStaffTurnPositiveDTO.setInternshipDate(hrApplyEntryStaff.getInternshipDate());
        }

        return hrStaffTurnPositiveDTO;
    }
}
