package cn.casair.repository;
import cn.casair.domain.HrExam;
import cn.casair.dto.HrExamDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-17
 */
@Repository
public interface HrExamRepository extends BaseMapper<HrExam> {

    IPage<HrExamDTO> pageSelect(Page<HrExam> page, @Param("param1")HrExamDTO hrExamDTO);

    List<HrExamDTO> HrExams(@Param("ids")List<String> ids);
}
