package cn.casair.service;
import cn.casair.domain.HrInformation;
import cn.casair.dto.HrInformationDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;

/**
 * 资讯模块服务类
 *
 * <AUTHOR>
 * @since 2021-11-12
 */
public interface HrInformationService extends IService<HrInformation> {


    /**
     * 创建资讯模块
     * @param hrInformationDTO
     * @return
     */
    HrInformationDTO createHrInformation(HrInformationDTO hrInformationDTO);

    /**
     * 修改资讯模块
     * @param hrInformationDTO
     * @return
     */
    Optional<HrInformationDTO> updateHrInformation(HrInformationDTO hrInformationDTO);

    /**
     * 查询资讯模块详情
     * @param id
     * @return
     */
    HrInformationDTO getHrInformation(String id);

    /**
     * 删除资讯模块
     * @param id
     */
    void deleteHrInformation(String id);

    /**
     * 批量删除资讯模块
     * @param ids
     */
    void deleteHrInformation(List<String> ids);

    /**
     * 分页查询资讯模块
     * @param hrInformationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrInformationDTO hrInformationDTO,Long pageNumber,Long pageSize);

    /**
     * 资讯批量导出
     * @param hrInformationDTO
     * @param httpResponse
     */
    String exportArchives(HrInformationDTO hrInformationDTO, HttpServletResponse httpResponse);

    /**
     * 查询所有发布的资讯
     * @return
     */
    List<HrInformationDTO> getHrInformationRelease();

}
