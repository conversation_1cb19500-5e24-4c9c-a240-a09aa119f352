package cn.casair.service;

import cn.casair.domain.HrStaffCertificate;
import cn.casair.dto.HrStaffCertificateDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 证书服务类
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
public interface HrStaffCertificateService extends IService<HrStaffCertificate> {


    /**
     * 创建证书
     *
     * @param hrStaffCertificateDTO
     * @return
     */
    List<HrStaffCertificateDTO> createHrStaffCertificate(HrStaffCertificateDTO hrStaffCertificateDTO);

    /**
     * 修改证书
     *
     * @param hrStaffCertificateDTO
     * @return
     */
    Optional<List<HrStaffCertificateDTO>> updateHrStaffCertificate(HrStaffCertificateDTO hrStaffCertificateDTO);

    /**
     * 查询证书详情
     *
     * @param id
     * @return
     */
    HrStaffCertificateDTO getHrStaffCertificate(String id);

    /**
     * 删除证书
     *
     * @param id
     */
    List<HrStaffCertificateDTO> deleteHrStaffCertificate(String id);

    /**
     * 查询证书
     * @param staffId 员工ID
     * @return
     */
    List<HrStaffCertificateDTO> findCertificateList(String staffId);
}
