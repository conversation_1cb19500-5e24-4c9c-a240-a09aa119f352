package cn.casair.repository;

import cn.casair.domain.Role;
import cn.casair.dto.RoleDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色表
 * roleid 改为id
 * 添加 founder, choose, is_delete
 * 添加4个通用字段数据库操作类
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Repository
public interface RoleRepository extends BaseMapper<Role> {

    @Select("SELECT * FROM sys_role WHERE role_key = #{roleKey} AND is_delete = 0 LIMIT 1")
    Role selectOneForKey(@Param("roleKey") String roleKey);

    /**
     * 根据用户id获取用户角色
     *
     * @param userId 用户id
     * @return java.util.List<cn.casair.domain.Role>
     * <AUTHOR>
     * @date 2021/8/27
     **/
    List<Role> getByUserId(@Param("userId") String userId);

    /**
     * 根据角色ids查询角色名称
     * @param ids
     * @return
     */
    String getRolesByIds(@Param("ids") List<String> ids);

    /**
     *  根据角色key列表获取角色列表
     * @param roleKeys
     * @return
     */
    List<RoleDTO> getByRoleKeys(List<String> roleKeys);
}
