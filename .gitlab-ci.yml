variables:
    API_NAME: "hr-server"
    API_VERSION: "3.5.0"
    IMAGE_NAME: "************:8082/hr/$API_NAME:$API_VERSION"
    CONTAINER_NAME: $API_NAME-$API_VERSION
    ENV_INTERGRATION_IP: ************
    SSHPASS: 'asdf.1234'

stages:
    - deploy

deploy:
    stage: deploy
    only:
        - dev-3.5.0---
    when: manual
    tags:
        - java8
    script:
        - java -version
        - mvn clean package -DskipTests=true -U
        - export DOCKER_HOST=$CASAIR_DOCKER_HOST
        - mvn dockerfile:build dockerfile:push -Ddockerfile.tag=$API_VERSION
        - sshpass -e ssh -o "StrictHostKeyChecking no" root@$ENV_INTERGRATION_IP
            "cd /opt/hr/deploy/demo && docker-compose pull && docker-compose up -d "
