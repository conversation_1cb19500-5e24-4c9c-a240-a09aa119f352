<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.RecruitmentStationRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, service_id, recruitment_station_id, recruitment_people_number, exam_format, recruitment_term,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.RecruitmentStation">
                    <id column="id" property="id"/>
                    <result column="service_id" property="serviceId"/>
                    <result column="recruitment_station_id" property="recruitmentStationId"/>
                    <result column="recruitment_people_number" property="recruitmentPeopleNumber"/>
                    <result column="exam_format" property="examFormat"/>
                    <result column="recruitment_term" property="recruitmentTerm"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

</mapper>
