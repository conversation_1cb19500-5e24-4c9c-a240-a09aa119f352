package cn.casair.repository;

import cn.casair.domain.HrBillReimbursementApplyDetail;
import cn.casair.dto.HrBillReimbursementApplyDetailDTO;
import cn.casair.dto.HrBillReimbursementClientDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 报销申请费用明细数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Repository
public interface HrBillReimbursementApplyDetailRepository extends BaseMapper<HrBillReimbursementApplyDetail> {

    List<HrBillReimbursementApplyDetailDTO> getByApplyId(String applyId);

    @Update("update hr_bill_reimbursement_apply_detail dl set dl.is_delete = 1 where dl.apply_id = #{applyId}")
    void delByApplyId(String id);

    List<HrBillReimbursementApplyDetailDTO> getByApplyIdList(@Param("idList") List<String> idList);

    void updateState(@Param("ids") List<String> ids, @Param("state") Integer state);

    List<HrBillReimbursementClientDTO> findListByApplyId(@Param("idList")List<String> ids);

    void insertDetailClient(@Param("param") HrBillReimbursementClientDTO list);

    List<HrBillReimbursementClientDTO> getDetailClientById(@Param("detailIds") List<String> detailIds, @Param("clientBillIds") List<String> clientBillIds);

    void deleteDetailClient(@Param("detailIds") List<String> detailIds);

    List<HrBillReimbursementApplyDetailDTO> getDetailInfo(@Param("detailIds") List<String> detailIds, @Param("approveStatus") Integer approveStatus,
                                                          @Param("parentId") String parentId, @Param("invoiceType") Integer invoiceType);

    List<HrBillReimbursementApplyDetailDTO> getDetailStatusByApplyId(@Param("applyId") String applyId);

}
