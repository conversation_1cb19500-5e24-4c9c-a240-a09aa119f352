package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.enums.StaffEnum;
import cn.casair.common.enums.UserRoleTypeEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.domain.HrStaffSecondment;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.*;
import cn.casair.mapper.HrStaffSecondmentMapper;
import cn.casair.mapper.HrTalentStaffMapper;
import cn.casair.repository.HrContractRepository;
import cn.casair.repository.HrStaffSecondmentRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工借调服务服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrStaffSecondmentServiceImpl extends ServiceImpl<HrStaffSecondmentRepository, HrStaffSecondment> implements HrStaffSecondmentService {

    private final HrStaffSecondmentRepository hrStaffSecondmentRepository;
    private final HrStaffSecondmentMapper hrStaffSecondmentMapper;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrTalentStaffMapper hrTalentStaffMapper;
    private final HrContractRepository hrContractRepository;
    private final HrClientService hrClientService;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrNotificationUserService hrNotificationUserService;
    private final HrUpcomingService hrUpcomingService;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;


    /**
     * 创建员工借调服务
     *
     * @param hrStaffSecondmentDTO
     * @return
     */
    @Override
    public HrStaffSecondmentDTO createHrStaffSecondment(HrStaffSecondmentDTO hrStaffSecondmentDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();

        log.info("Create new HrStaffSecondment:{}", hrStaffSecondmentDTO);
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(hrStaffSecondmentDTO.getStaffId());
        if (hrStaffSecondmentDTO.getNewClientId().equals(hrTalentStaff.getClientId())) {
            throw new CommonException("借调至单位与原单位不能一致！");
        }
        hrStaffSecondmentDTO.setOldClientId(hrTalentStaff.getClientId());
        hrStaffSecondmentDTO.setStaffStatus(hrTalentStaff.getStaffStatus());
        hrStaffSecondmentDTO.setLastModifiedDate(LocalDateTime.now());
        HrStaffSecondment hrStaffSecondment = this.hrStaffSecondmentMapper.toEntity(hrStaffSecondmentDTO);
        this.hrStaffSecondmentRepository.insert(hrStaffSecondment);
        HrStaffSecondmentDTO staffSecondmentDTO = this.hrStaffSecondmentMapper.toDto(hrStaffSecondment);
        hrTalentStaff.setSecondmentId(staffSecondmentDTO.getId());
        hrTalentStaffRepository.updateById(hrTalentStaff);
        String message = jwtUserDTO.getRealName() + "发起了借调申请";
        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(staffSecondmentDTO.getId(), null, jwtUserDTO.getId(), message, null, false, null, ServiceCenterEnum.STAFF_SECONDMENT.getKey());
        this.hrNotificationUserService.saveRemindContent(hrTalentStaff.getClientId(), ServiceCenterEnum.STAFF_SECONDMENT.getKey(), ServiceCenterEnum.SecondmentApplyEnum.OLD_COMPANY_INITIATED.getKey(), message, jwtUserDTO.getId());
        hrUpcomingService.saveServiceUpcoming(staffSecondmentDTO.getId(), hrStaffSecondmentDTO.getNewClientId(), "员工借调申请-确认" + hrTalentStaff.getName() + "的借调申请", LocalDate.now(), 1);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_SECONDMENT.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffSecondmentDTO),
            HrStaffSecondmentDTO.class,
            null,
            JSON.toJSONString(staffSecondmentDTO)
        );
        return staffSecondmentDTO;
    }

    /**
     * 结束员工借调服务
     *
     * @param hrStaffSecondmentDTO
     * @return
     */
    @Override
    public Optional<HrStaffSecondmentDTO> updateHrStaffSecondment(HrStaffSecondmentDTO hrStaffSecondmentDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        return Optional.ofNullable(this.hrStaffSecondmentRepository.findBatch(Arrays.asList(hrStaffSecondmentDTO.getId())).get(0))
            .map(roleTemp -> {
                if (roleTemp.getOldStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION.getKey())
                    || roleTemp.getOldStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey())
                    || roleTemp.getOldStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())) {
                    throw new CommonException(roleTemp.getName() + "已是" + StaffEnum.StaffStatusEnum.getValueByKey(roleTemp.getOldStaffStatus()) + "不可继续借调流程");
                }
                hrStaffSecondmentDTO.setIsDefault(1);//结束借调
                hrStaffSecondmentDTO.setStep(ServiceCenterEnum.SecondmentStepEnum.NEW_COMPANY_INITIATED.getKey());
                hrStaffSecondmentDTO.setStates(ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_ON.getKey());
                HrStaffSecondment hrStaffSecondment = this.hrStaffSecondmentMapper.toEntity(hrStaffSecondmentDTO);
                this.hrStaffSecondmentRepository.updateById(hrStaffSecondment);
                String message = jwtUserDTO.getRealName() + "发起了结束借调申请";
                hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(roleTemp.getId(), null, jwtUserDTO.getId(), message, null, false, null, ServiceCenterEnum.STAFF_SECONDMENT.getKey());
                this.hrNotificationUserService.saveRemindContent(roleTemp.getNewClientId(), ServiceCenterEnum.STAFF_SECONDMENT.getKey(), ServiceCenterEnum.SecondmentApplyEnum.NEW_COMPANY_INITIATED.getKey(), message, jwtUserDTO.getId());
                hrUpcomingService.saveServiceUpcoming(roleTemp.getId(), roleTemp.getOldClientId(), "员工借调申请-确认" + roleTemp.getName() + "的借调申请", LocalDate.now(), 1);
                log.info("Update HrStaffSecondment:{}", hrStaffSecondmentDTO);
                return hrStaffSecondmentDTO;
            });
    }

    /**
     * 查询员工借调服务详情
     *
     * @param id
     * @return
     */
    @Override
    public HrStaffSecondmentDTO getHrStaffSecondment(String id) {
        log.info("Get HrStaffSecondment :{}", id);
        List<HrStaffSecondmentDTO> list = this.hrStaffSecondmentRepository.findBatch(Collections.singletonList(id));
        if (list == null || list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        HrStaffSecondmentDTO hrStaffSecondmentDTO = list.get(0);
        HrContractDTO hrContractDTO = hrContractRepository.selectNewestStaffContract(hrStaffSecondmentDTO.getStaffId());
        if (hrContractDTO != null) {
            hrStaffSecondmentDTO.setContractStartDate(hrContractDTO.getContractStartDate()).setContractEndDate(hrContractDTO.getContractEndDate());
        }
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(id, null);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)) {
            hrStaffSecondmentDTO.setApplyOpLogsList(applyOpLogsList);
        }
        return hrStaffSecondmentDTO;
    }

    /**
     * 删除员工借调服务
     *
     * @param id
     */
    @Override
    public void deleteHrStaffSecondment(String id) {
        Optional.ofNullable(this.hrStaffSecondmentRepository.selectById(id))
            .ifPresent(hrStaffSecondment -> {
                this.hrStaffSecondmentRepository.deleteById(id);
                log.info("Delete HrStaffSecondment:{}", hrStaffSecondment);
            });
    }

    /**
     * 批量删除员工借调服务
     *
     * @param ids
     */
    @Override
    public void deleteHrStaffSecondment(List<String> ids) {
        log.info("Delete HrStaffSecondments:{}", ids);
        this.hrStaffSecondmentRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询员工借调服务
     *
     * @param hrStaffSecondmentDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrStaffSecondmentDTO> findPage(HrStaffSecondmentDTO hrStaffSecondmentDTO, Long pageNumber, Long pageSize) {
        Page<HrStaffSecondment> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();

        IPage<HrStaffSecondmentDTO> iPage = this.hrStaffSecondmentRepository.findPage(page, hrStaffSecondmentDTO, clientIds);
        return iPage;
    }

    /**
     * 导出员工借调服务
     *
     * @param hrStaffSecondmentDTO
     * @return
     */
    @Override
    public String exportHrStaffSecondment(HrStaffSecondmentDTO hrStaffSecondmentDTO) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrStaffSecondmentDTO> hrStaffSecondmentDTOList = hrStaffSecondmentRepository.findList(hrStaffSecondmentDTO, clientIds);
        if (hrStaffSecondmentDTOList.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }

        int listSize = hrStaffSecondmentDTOList.size();
        List<String> nameList = hrStaffSecondmentDTOList.stream().map(HrStaffSecondmentDTO::getName).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(hrStaffSecondmentDTOList, "员工借调信息", HrStaffSecondmentDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.STAFF_SECONDMENT.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(nameList), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 员工借调-->批量确认
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public Map<String, Object> confirmationBatch(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrStaffSecondmentDTO> hrStaffSecondmentDTOList = this.hrStaffSecondmentRepository.findBatch(batchOptDTO.getApplyIdList());
        List<String> error = new ArrayList<>();//离职中、离职员工集合
        List<String> errorList = new ArrayList<>();//状态值错误集合
        List<String> fillNewList = new ArrayList<>();//客户审核错误集合
        List<String> fillOldList = new ArrayList<>();//客户审核错误集合
        for (HrStaffSecondmentDTO hrStaffSecondmentDTO : hrStaffSecondmentDTOList) {
            if (hrStaffSecondmentDTO.getOldStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION.getKey())
                || hrStaffSecondmentDTO.getOldStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey())
                || hrStaffSecondmentDTO.getOldStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())) {
                error.add(hrStaffSecondmentDTO.getName());
                continue;
            }
            Integer launchType = null;
            Integer step = ServiceCenterEnum.SecondmentStepEnum.SECONDMENT_REFUSE.getKey();
            if (hrStaffSecondmentDTO.getStep().equals(ServiceCenterEnum.SecondmentStepEnum.OLD_COMPANY_INITIATED.getKey())) {
                if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())) {
                    if (hrStaffSecondmentDTO.getNewUserId().equals(jwtUserDTO.getId())) {
                        if (batchOptDTO.getOpt()) {
                            step = ServiceCenterEnum.SecondmentStepEnum.NEW_COMPANY_CONFIRMED.getKey();
                        }
                        launchType = ServiceCenterEnum.SecondmentApplyEnum.NEW_COMPANY_CONFIRMED.getKey();
                    } else {
                        fillOldList.add(hrStaffSecondmentDTO.getName());
                        continue;
                    }
                } else {
                    if (batchOptDTO.getOpt()) {
                        step = ServiceCenterEnum.SecondmentStepEnum.NEW_COMPANY_CONFIRMED.getKey();
                    }
                    launchType = ServiceCenterEnum.SecondmentApplyEnum.NEW_COMPANY_CONFIRMED.getKey();
                }
            } else if (hrStaffSecondmentDTO.getStep().equals(ServiceCenterEnum.SecondmentStepEnum.NEW_COMPANY_INITIATED.getKey())) {
                if (jwtUserDTO.getCurrentRoleKey().equals(UserRoleTypeEnum.CLIENT.getKey())) {
                    if (hrStaffSecondmentDTO.getOldUserId().equals(jwtUserDTO.getId())) {
                        if (batchOptDTO.getOpt()) {
                            step = ServiceCenterEnum.SecondmentStepEnum.OLD_COMPANY_CONFIRMED.getKey();
                        }
                        launchType = ServiceCenterEnum.SecondmentApplyEnum.OLD_COMPANY_CONFIRMED.getKey();
                    } else {
                        fillNewList.add(hrStaffSecondmentDTO.getName());
                        continue;
                    }
                } else {
                    if (batchOptDTO.getOpt()) {
                        step = ServiceCenterEnum.SecondmentStepEnum.OLD_COMPANY_CONFIRMED.getKey();
                    }
                    launchType = ServiceCenterEnum.SecondmentApplyEnum.OLD_COMPANY_CONFIRMED.getKey();
                }
            } else {
                errorList.add(hrStaffSecondmentDTO.getName());
                continue;
            }

            String message = "";
            if (batchOptDTO.getOpt()) {
                message = jwtUserDTO.getRealName() + "确认了借调申请";
                hrUpcomingService.saveServiceUpcoming(hrStaffSecondmentDTO.getId(), hrStaffSecondmentDTO.getOldClientId(), "员工借调申请-审核" + hrStaffSecondmentDTO.getName() + "的借调申请", LocalDate.now(), 0);
            } else {
                message = jwtUserDTO.getRealName() + "拒绝了借调申请。拒绝原因：" + batchOptDTO.getCheckerReason();
                hrUpcomingService.updateUpcoming(hrStaffSecondmentDTO.getId());
                hrStaffSecondmentDTO.setStates(ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_REFUSE.getKey());
                HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(hrStaffSecondmentDTO.getStaffId());
                //审核拒绝更改员工状态为之前状态
                hrTalentStaff.setClientId(hrStaffSecondmentDTO.getOldClientId())
                    .setStaffStatus(hrStaffSecondmentDTO.getStaffStatus())
                    .setSecondmentId(null);
                hrTalentStaffRepository.updateHrTalentStaff(hrTalentStaff);
            }
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrStaffSecondmentDTO.getId(), null, jwtUserDTO.getId(), message, batchOptDTO.getRemark(), false, null, ServiceCenterEnum.STAFF_SECONDMENT.getKey());
            this.hrNotificationUserService.saveRemindContent(hrStaffSecondmentDTO.getOldClientId(), ServiceCenterEnum.STAFF_SECONDMENT.getKey(), launchType, message, jwtUserDTO.getId());
            hrStaffSecondmentDTO.setStep(step);
            hrStaffSecondmentRepository.updateStepAndStates(hrStaffSecondmentDTO);
        }
        if (CollectionUtils.isNotEmpty(error)) {
            throw new CommonException(String.join(",", error) + "已是离职中或是离职不可继续借调流程");
        }
        Map<String, Object> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(errorList) || CollectionUtils.isNotEmpty(fillOldList) || CollectionUtils.isNotEmpty(fillNewList)) {
            String checkMsg = "确认借调申请存在操作失败数据：" + (errorList.size() + fillOldList.size() + fillNewList.size()) + "条。其中："
                + (errorList.size() == 0 ? "" : String.join(",", errorList) + "不需要进行确认操作；")
                + (fillOldList.size() == 0 ? "" : String.join(",", fillOldList) + "需要借调至单位进行确认操作；")
                + (fillNewList.size() == 0 ? "" : String.join(",", fillNewList) + "需要原单位进行确认操作；");
            result.put("checkCode", 500);
            result.put("checkMsg", checkMsg);
        } else {
            result.put("checkCode", 200);
            result.put("checkMsg", "操作成功！");
        }
        return result;
    }

    /**
     * 员工借调-->业务员批量审核
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public Map<String, Object> salesmanApproval(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrStaffSecondmentDTO> hrStaffSecondmentDTOList = this.hrStaffSecondmentRepository.findBatch(batchOptDTO.getApplyIdList());
        List<String> error = new ArrayList<>();//离职中、离职员工
        List<String> errorList = new ArrayList<>();//状态值错误集合
        for (HrStaffSecondmentDTO hrStaffSecondmentDTO : hrStaffSecondmentDTOList) {
            if (hrStaffSecondmentDTO.getOldStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION.getKey())
                || hrStaffSecondmentDTO.getOldStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey())
                || hrStaffSecondmentDTO.getOldStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())) {
                error.add(hrStaffSecondmentDTO.getName());
                continue;
            }
            String message = "";
            if (!hrStaffSecondmentDTO.getStep().equals(ServiceCenterEnum.SecondmentStepEnum.NEW_COMPANY_CONFIRMED.getKey())
                && !hrStaffSecondmentDTO.getStep().equals(ServiceCenterEnum.SecondmentStepEnum.OLD_COMPANY_CONFIRMED.getKey())) {
                errorList.add(hrStaffSecondmentDTO.getName());
                continue;
            }
            HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(hrStaffSecondmentDTO.getStaffId());
            if (batchOptDTO.getOpt()) {
                LocalDate now = LocalDate.now();
                if (hrStaffSecondmentDTO.getIsDefault() == 0) {
                    if (hrStaffSecondmentDTO.getStartDate().isBefore(now) || hrStaffSecondmentDTO.getStartDate().isEqual(now)) {
                        hrTalentStaff.setClientId(hrStaffSecondmentDTO.getNewClientId()).setStaffStatus(StaffEnum.StaffStatusEnum.BORROWING.getKey());
                        hrTalentStaffRepository.updateById(hrTalentStaff);
                        hrStaffSecondmentDTO.setStates(ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_ON.getKey());
                    }
                    hrStaffSecondmentDTO.setStep(ServiceCenterEnum.SecondmentStepEnum.SECONDMENT_COMPLETED.getKey());
                } else {
                    if (hrStaffSecondmentDTO.getEndDate().isBefore(now) || hrStaffSecondmentDTO.getEndDate().isEqual(now)) {
                        hrTalentStaff.setClientId(hrStaffSecondmentDTO.getOldClientId())
                            .setStaffStatus(hrStaffSecondmentDTO.getStaffStatus())
                            .setSecondmentId(null);
                        hrTalentStaffRepository.updateHrTalentStaff(hrTalentStaff);
                        hrStaffSecondmentDTO.setStates(ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_END.getKey());
                    }
                    hrStaffSecondmentDTO.setStep(ServiceCenterEnum.SecondmentStepEnum.SECONDMENT_END.getKey());
                }
                message = jwtUserDTO.getRealName() + "同意了" + hrStaffSecondmentDTO.getName() + "的借调申请";
            } else {
                hrStaffSecondmentDTO.setStep(ServiceCenterEnum.SecondmentStepEnum.SECONDMENT_REFUSE.getKey());
                hrStaffSecondmentDTO.setStates(ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_REFUSE.getKey());
                //审核拒绝更改员工状态为之前状态
                hrTalentStaff.setClientId(hrStaffSecondmentDTO.getOldClientId())
                    .setStaffStatus(hrStaffSecondmentDTO.getStaffStatus())
                    .setSecondmentId(null);
                hrTalentStaffRepository.updateHrTalentStaff(hrTalentStaff);
                message = jwtUserDTO.getRealName() + "拒绝了" + hrStaffSecondmentDTO.getName() + "的借调申请。拒绝原因：" + batchOptDTO.getCheckerReason();
            }
            hrStaffSecondmentRepository.updateStepAndStates(hrStaffSecondmentDTO);
            hrUpcomingService.updateUpcoming(hrStaffSecondmentDTO.getId());
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrStaffSecondmentDTO.getId(), null, jwtUserDTO.getId(), message, batchOptDTO.getRemark(), false, null, ServiceCenterEnum.STAFF_SECONDMENT.getKey());
            this.hrNotificationUserService.saveRemindContent(hrStaffSecondmentDTO.getOldClientId(), ServiceCenterEnum.STAFF_SECONDMENT.getKey(), ServiceCenterEnum.SecondmentApplyEnum.MANAGER_REVIEW.getKey(), message, jwtUserDTO.getId());
        }
        if (CollectionUtils.isNotEmpty(error)) {
            throw new CommonException(String.join(",", error) + "已是离职中或是离职不可继续借调流程");
        }
        Map<String, Object> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(errorList)) {
            result.put("checkCode", 500);
            result.put("checkMsg", String.join(",", errorList) + "不是需要业务员审批的流程");
        } else {
            result.put("checkCode", 200);
            result.put("checkMsg", "操作成功！");
        }
        return result;
    }

    /**
     * 每天0点检测员工借调状态
     */
    @Override
    public void updateStaffStatusSecondment() {
        List<HrTalentStaffDTO> hrTalentStaffDTOList = hrStaffSecondmentRepository.findStaff();
        if (CollectionUtils.isNotEmpty(hrTalentStaffDTOList)) {
            LocalDate localDate = LocalDate.now();
            for (HrTalentStaffDTO hrTalentStaffDTO : hrTalentStaffDTOList) {
                HrStaffSecondmentDTO hrStaffSecondment = new HrStaffSecondmentDTO();
                hrStaffSecondment.setId(hrTalentStaffDTO.getSecondmentId());
                boolean needUpdate = false;
                if (hrTalentStaffDTO.getSecondmentIsDefault() == 0) {
                    if (ServiceCenterEnum.SecondmentStatesEnum.TO_BE_SECONDMENT.getKey().equals(hrTalentStaffDTO.getSecondmentStates()) &&
                        (hrTalentStaffDTO.getSecondmentStartDate().isEqual(localDate) || hrTalentStaffDTO.getSecondmentStartDate().isBefore(localDate))) {
                        needUpdate = true;
                        hrTalentStaffDTO.setClientId(hrTalentStaffDTO.getNewClientId());
                        hrTalentStaffDTO.setStaffStatus(StaffEnum.StaffStatusEnum.BORROWING.getKey());
                        hrStaffSecondment.setStates(ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_ON.getKey());
                    }
                } else {
                    if (ObjectUtil.isEmpty(hrTalentStaffDTO.getSecondmentEndDate())) {
                        // 如果结束时间为空，则不进行处理
                        continue;
                    }
                    if (ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_ON.getKey().equals(hrTalentStaffDTO.getSecondmentStates()) &&
                        (hrTalentStaffDTO.getSecondmentEndDate().isEqual(localDate) || hrTalentStaffDTO.getSecondmentEndDate().isBefore(localDate))) {
                        needUpdate = true;
                        hrTalentStaffDTO.setClientId(hrTalentStaffDTO.getOldClientId());
                        hrTalentStaffDTO.setStaffStatus(hrTalentStaffDTO.getSecondmentStaffStatus());
                        hrTalentStaffDTO.setSecondmentId(null);
                        hrStaffSecondment.setIsDefault(1)
                            .setStates(ServiceCenterEnum.SecondmentStatesEnum.SECONDMENT_END.getKey())
                            .setStep(ServiceCenterEnum.SecondmentStepEnum.SECONDMENT_END.getKey());
                    }
                }
                if (needUpdate) {
                    hrTalentStaffRepository.updateHrTalentStaff(hrTalentStaffMapper.toEntity(hrTalentStaffDTO));
                    hrStaffSecondmentRepository.updateStepAndStates(hrStaffSecondment);
                }
            }
        }
    }
}
