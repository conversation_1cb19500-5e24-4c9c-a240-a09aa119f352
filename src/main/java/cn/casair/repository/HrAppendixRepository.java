package cn.casair.repository;

import cn.casair.domain.HrAppendix;
import cn.casair.domain.HrAppendixUnion;
import cn.casair.dto.HrAppendixDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 附件表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Repository
public interface HrAppendixRepository extends BaseMapper<HrAppendix> {

    /**
     * 根据url获取文件信息
     *
     * @param fileUrl
     * @return cn.casair.domain.HrAppendix
     * <AUTHOR>
     * @date 2022/1/17
     **/
    HrAppendix selectByFileUrl(@Param("fileUrl") String fileUrl);

    /**
     * 删除关联附件信息
     *
     * @return int
     * <AUTHOR>
     * @date 2022/1/17
     **/
    int deleteUnion(@Param("appendixId") String appendixId);


    /**
     * 根据id列表获取附件信息
     *
     * @param ids
     * @return java.util.List<cn.casair.dto.HrAppendixDTO>
     * <AUTHOR>
     * @date 2021/9/16
     **/
    List<HrAppendixDTO> getHrAppendixListByIds(@Param("ids") List<String> ids);

    /**
     * 保存附件与业务表id关联信息
     *
     * @param hrAppendixUnion
     * @return int
     * <AUTHOR>
     * @date 2021/10/11
     **/
    int saveAppendixUnion(HrAppendixUnion hrAppendixUnion);

    /**
     * 根据业务id删除附件的关联关系
     * @param id
     */
    @Delete(" delete from hr_appendix_union hau where hau.union_id = #{id}")
    void deleteByUnionId(@Param("id") String id);


    List<HrAppendixDTO> getByUnionId(@Param("unionId") String unionId);

}
