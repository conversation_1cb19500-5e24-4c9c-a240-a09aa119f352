<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrLaborAppraisalRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, staff_id, client_id, injury_date, work_stoppage_start_date, work_stoppage_end_date, application, application_date, appraisal_status, injury_description, status,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>


    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrLaborAppraisal">
                    <id column="id" property="id"/>
                    <result column="staff_id" property="staffId"/>
                    <result column="client_id" property="clientId"/>
                    <result column="injury_date" property="injuryDate"/>
                    <result column="work_stoppage_start_date" property="workStoppageStartDate"/>
                    <result column="work_stoppage_end_date" property="workStoppageEndDate"/>
                    <result column="application" property="application"/>
                    <result column="application_date" property="applicationDate"/>
                    <result column="appraisal_status" property="appraisalStatus"/>
                    <result column="injury_description" property="injuryDescription"/>
                    <result column="status" property="status"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>
    <select id="selectDetatil" resultType="cn.casair.dto.HrLaborAppraisalDTO">
        SELECT
            hla.id,
            hla.staff_id,
            hla.work_injury_id,
            hla.created_by,
            ht.client_id,
            hc.client_name,
            ht.`name`,
            ht.certificate_num,
            ht.sex,
            ht.phone,
            hs.profession_name,
            hs.id,
            ht.personnel_type,
            hla.injury_date,
            hla.work_stoppage_start_date,
            hla.work_stoppage_end_date,
            hla.`status`,
            hla.appraisal_status,
            hla.injury_description,
            hla.application_date,
            hla.application,
            su.real_name AS specialized,
            su.id,
            hse.basic_wage,
            hse.social_security_cardinal,
            hse.medical_insurance_cardinal,
            hse.accumulation_fund_cardinal,
            hce.contract_start_date,
            hce.contract_end_date
        FROM
            hr_labor_appraisal hla
                LEFT JOIN hr_talent_staff ht ON hla.staff_id = ht.id
                AND ht.is_delete = 0
                LEFT JOIN hr_staff_work_experience hsw ON hla.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id
                AND hsw.is_delete = 0
                AND hsw.iz_default = 1
                LEFT JOIN hr_station hs ON hsw.station_id = hs.id
                AND hs.is_delete = 0
                LEFT JOIN hr_client hc ON hc.id = hla.client_id
                AND hc.is_delete = 0
                LEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id
                AND huc.is_delete = 0
                AND huc.is_specialized = 1
                LEFT JOIN sys_user su ON huc.user_id = su.id
                AND su.is_delete = 0
                LEFT JOIN hr_staff_emolument hse ON hse.staff_id = ht.id
                AND hse.is_delete = 0
                LEFT JOIN (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
                           FROM (SELECT * FROM hr_contract WHERE is_delete = 0 AND state in (1,2) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) hce ON hce.staff_id = ht.id
                AND hce.is_delete = 0
        WHERE
            hla.is_delete = 0 AND ht.is_delete = 0 AND hla.id = #{id}
    </select>
    <select id="exportLaborAppraisals" resultType="cn.casair.dto.excel.HrLaborAppraisalExport">
        SELECT
            hla.id,
            hla.staff_id,
            hla.created_by,
            ht.client_id,
            hc.client_name,
            ht.`name`,
            ht.staff_status,
            ht.certificate_num,
            ht.sex,
            ht.phone,
            hs.profession_name,
            hs.id,
            ht.personnel_type,
            hla.injury_date,
            hla.work_stoppage_start_date,
            hla.work_stoppage_end_date,
            hla.`status`,
            hla.appraisal_status,
            hla.injury_description,
            hla.application_date,
            hla.application,
            su.real_name AS specialized,
            su.id

        FROM
            hr_labor_appraisal hla
                LEFT JOIN hr_talent_staff ht ON hla.staff_id = ht.id
                AND ht.is_delete = 0
                LEFT JOIN hr_staff_work_experience hsw ON hla.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id
                AND hsw.is_delete = 0
                AND hsw.iz_default = 1
                LEFT JOIN hr_station hs ON hsw.station_id = hs.id
                AND hs.is_delete = 0
                LEFT JOIN hr_client hc ON hc.id = hla.client_id
                AND hc.is_delete = 0
                LEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id
                AND huc.is_delete = 0
                AND huc.is_specialized = 1
                LEFT JOIN sys_user su ON huc.user_id = su.id
                AND su.is_delete = 0
        ${ew.customSqlSegment}
    </select>

    <select id="laborAppraisalListByStaffId" resultType="cn.casair.dto.HrLaborAppraisalDTO">
        SELECT
            hc.client_name,
            hla.*
        FROM
            hr_labor_appraisal hla
        LEFT JOIN hr_talent_staff hts ON hla.staff_id = hts.id
        LEFT JOIN hr_client hc ON hc.id = hla.client_id
        WHERE
            hla.is_delete = 0 AND hla.staff_id = #{staffId}
        ORDER BY hla.created_date
    </select>
</mapper>
