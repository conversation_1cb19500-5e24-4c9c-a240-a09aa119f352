package cn.casair.web.rest;

import cn.casair.CasairwebbaseApp;
import cn.casair.dto.nc.result.NcResultDTO;
import cn.casair.dto.nc.result.NccCustomerDTO;
import cn.casair.ncc.NccService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import nccloud.open.api.auto.token.itf.IAPIUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/12/03 14:45
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CasairwebbaseApp.class)
public class SpringbootNccTest {

    @Autowired
    private IAPIUtils iAPIUtils;
    @Autowired
    private NccService nccService;


    @Test
    public void getToken() throws Exception {
        log.info("token:{}", iAPIUtils.getToken());
    }

    @Test
    public void getApiUrl() throws Exception {
        log.info("getApiUrl:{}", iAPIUtils.getApiUrl());
//        log.info("token:{}", iAPIUtils.getToken());
        NcResultDTO<NccCustomerDTO> resultDTO = new NcResultDTO<>();
        NccCustomerDTO ncCustomerDTO = new NccCustomerDTO();
        ncCustomerDTO.setName("test");
        resultDTO.setErrorNo("0");
        resultDTO.setData(ncCustomerDTO);
        String json = JSON.toJSONString(resultDTO);
        log.info("resultDTO:{}", json);
        log.info("ncCustomerDTO:{}", JSON.parseObject(json, new TypeReference<NcResultDTO<NccCustomerDTO>>() {
        }));
        NcResultDTO<NccCustomerDTO> parseObject = JSON.parseObject(json, NcResultDTO.class);
        log.info("parseObject:{}", parseObject);
    }

    @Test
    public void testCreateCustomer() throws Exception {
        log.info("开始测试创建客商");
        // 测试创建客商
        Object result = nccService.createCustomer("测试客户001", "01");
        log.info("创建客商结果:{}", JSON.toJSONString(result));
        // 验证结果不为空
        assert result != null;
    }

    @Test
    public void testCreateCustomerWithDifferentTypes() throws Exception {
        log.info("测试不同类型的客商创建");
        // 测试不同客商类型
        String[] customerTypes = {"01", "02", "03"};
        for (String type : customerTypes) {
            String customerName = "测试客户_" + type + "_" + System.currentTimeMillis();
            Object result = nccService.createCustomer(customerName, type);
            log.info("创建客商 - 名称:{}, 类型:{}, 结果:{}", customerName, type, JSON.toJSONString(result));
        }
    }
}
