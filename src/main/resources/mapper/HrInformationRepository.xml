<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrInformationRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, information_title, information_content, release_date, state,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrInformation">
        <id column="id" property="id"/>
        <result column="information_title" property="informationTitle"/>
        <result column="information_content" property="informationContent"/>
        <result column="release_date" property="releaseDate"/>
        <result column="state" property="state"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Find_SQL">
        select
        <include refid="Base_Column_List"/>
        from hr_information
        <where>
            is_delete = 0
            <if test="param.ids != null and param.ids.size() > 0">
                and id in
                <foreach collection="param.ids" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
            <if test="param.informationTitle!=null and param.informationTitle!=''">
                and information_title like concat('%',#{param.informationTitle},'%')
            </if>
            <if test="param.informationContent!=null and param.informationContent!=''">
                and information_content like concat('%',#{param.informationContent},'%')
            </if>
            <if test="param.createdBy!=null and param.createdBy!=''">
                and created_by like concat('%',#{param.createdBy},'%')
            </if>
            <if test="param.createdDateStart!=null">
                and created_date >= #{param.createdDateStart}
            </if>
            <if test="param.createdDateEnd!=null">
                and created_date &lt; date_add(#{param.createdDateEnd}, interval 1 day)
            </if>
            <if test="param.releaseDateStart!=null">
                and release_date >= #{param.releaseDateStart}
            </if>
            <if test="param.releaseDateEnd!=null">
                and release_date &lt; date_add(#{param.releaseDateEnd}, interval 1 day)
            </if>
            <if test="param.state!=null">
                and state = #{param.state}
            </if>
            and is_delete = 0
            <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
                ORDER BY ${param.field} ${param.order}
            </if>
        </where>
    </sql>

    <select id="selectHrInformation" resultType="cn.casair.dto.HrInformationDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="selectExportList" resultType="cn.casair.dto.excel.HrInformationExport">
        <include refid="Find_SQL"/>
    </select>
</mapper>
