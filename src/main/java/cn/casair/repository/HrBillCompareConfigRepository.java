package cn.casair.repository;

import cn.casair.domain.HrBillCompareConfig;
import cn.casair.dto.HrBillCompareConfigDTO;
import cn.casair.dto.billl.BillFieldInfoDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 对账配置信息表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Repository
public interface HrBillCompareConfigRepository extends BaseMapper<HrBillCompareConfig> {

    /**
     *  根据id查询对账配置
     * @param id
     * @return
     */
    @Select("SELECT * FROM hr_bill_compare_config bc WHERE bc.is_delete = 0 AND bc.id = #{id}")
    HrBillCompareConfigDTO getById(String id);

    void delByBillIdAndType(@Param("billId") String billId, @Param("type") Integer type, @Param("isDelete") Integer isDelete,@Param("billConfigIds") List<String> billConfigIds);

    List<HrBillCompareConfigDTO> findCompareConfig(@Param("billIds") List<String> billIds, @Param("type")Integer key);

    @Select("SELECT * FROM hr_bill_compare_config bc WHERE bc.id = #{id}")
    HrBillCompareConfigDTO findById(String id);

    List<HrBillCompareConfigDTO> findLastData(@Param("params") HrBillCompareConfigDTO hrBillCompareConfigDTO);

    HrBillCompareConfigDTO getBillCompareConfigById(@Param("id") String id);

    /**
     * 更新对账配置
     * @param config
     * @param id
     */
    void updateConfig(@Param("config") String config, @Param("id") String id);

    /**
     * 根据ID删除
     * @param ids
     * @param lastModifiedBy
     */
    void delBatchIds(@Param("ids") List<String> ids, @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 根据账单查询导盘配置信息
     * @param billId
     * @return
     */
    HrBillCompareConfigDTO getByBillId(@Param("billId") String billId);

}
