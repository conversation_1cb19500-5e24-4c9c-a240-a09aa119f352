package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.StringUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.HrClient;
import cn.casair.domain.HrExpenseManage;
import cn.casair.domain.HrQuickDeduction;
import cn.casair.domain.HrStation;
import cn.casair.domain.Role;
import cn.casair.domain.User;
import cn.casair.domain.UserRole;
import cn.casair.dto.HrExpenseManageDTO;
import cn.casair.dto.HrQuickDeductionDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrStationImport;
import cn.casair.dto.excel.HrUserImport;
import cn.casair.mapper.HrExpenseManageMapper;
import cn.casair.mapper.HrQuickDeductionMapper;
import cn.casair.repository.HrClientRepository;
import cn.casair.repository.HrExpenseManageRepository;
import cn.casair.repository.HrQuickDeductionRepository;
import cn.casair.repository.HrStationRepository;
import cn.casair.repository.RoleRepository;
import cn.casair.repository.UserRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrAppendixService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.UserRoleService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.casair.common.utils.ValidateUtil.isCellPhoneNo;

@Slf4j
@Component
@RequiredArgsConstructor
public class QuickDeductionExpenseComponent {

    @Value("${file.temp-path}")
    private String tempPath;

    private final SysOperLogService sysOperLogService;
    private final HrAppendixService hrAppendixService;
    private final RedisCache redisCache;
    private final HrQuickDeductionMapper hrQuickDeductionMapper;
    private final HrQuickDeductionRepository hrQuickDeductionRepository;
    private final HrExpenseManageMapper hrExpenseManageMapper;
    private final HrExpenseManageRepository hrExpenseManageRepository;
    private final CodeTableService codeTableService;
    private final HrStationRepository hrStationRepository;
    private final UserRoleService userRoleService;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final HrClientRepository hrClientRepository;

    @Async
    public void quickDeduction(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrQuickDeductionDTO> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrQuickDeductionDTO.class);


            ImportResultDTO importResult;

            int listSize = result.getList().size();
            int scale = 0;
            //定义级数的集合
            List<String> arrayList = new ArrayList<>();
            ArrayList<HrQuickDeduction> successResult = new ArrayList<>();
            for (HrQuickDeductionDTO hrQuickDeductionDTO : result.getList()) {
                try {
                    checkQuickData(result.getList());
                    String quickDeductionSeries = hrQuickDeductionDTO.getQuickDeductionSeries();
                    if (arrayList.contains(quickDeductionSeries)) {
                        throw new CommonException("级数不可以重复");
                    }
                    arrayList.add(quickDeductionSeries);
                    if (hrQuickDeductionDTO.getMinPayTaxes() != null && hrQuickDeductionDTO.getMaxPayTaxes() != null && hrQuickDeductionDTO.getMinPayTaxes() > hrQuickDeductionDTO.getMaxPayTaxes()) {
                        throw new CommonException("全年应缴纳所得额最小值不能大于全年应缴纳所得额最大值");
                    }
                    if (hrQuickDeductionDTO.getMinPayTaxes() == null && hrQuickDeductionDTO.getMaxPayTaxes() == null) {
                        throw new CommonException("全年应缴纳所得额最小值和全年应缴纳所得额最大值不能同时为空");
                    }
                    HrQuickDeduction hrQuickDeduction = hrQuickDeductionMapper.toEntity(hrQuickDeductionDTO);
                    String taxRateExcel = hrQuickDeductionDTO.getTaxRateExcel();

                    BigDecimal taxRate = null;
                    //如果包含% 并去掉 在转化成小数
                    if (taxRateExcel.contains("%")) {
                        taxRateExcel = taxRateExcel.replace("%", "").replaceAll(" ", "");
                        BigDecimal bigDecimal = null;
                        try {
                            bigDecimal = new BigDecimal(taxRateExcel);
                        } catch (Exception e) {
                            throw new CommonException("个人比例数据格式不正确");
                        }
                        taxRate = extracted(1, bigDecimal);
                        taxRate = importVerify(taxRate.toString(), "个人比例数据格式不正确");
                    }
                    //如果不包含%判断是否小于1
                    else {
                        taxRate = importVerify(taxRateExcel, "个人比例数据格式不正确");
                    }
                    hrQuickDeduction.setTaxRate(taxRate);
                    //save(hrQuickDeduction);
                    successResult.add(hrQuickDeduction);
                } catch (Exception e) {
                    log.error("速算扣除数异常:{}", e.getMessage());
                    hrQuickDeductionDTO.setErrorMsg(e.getMessage());
                } finally {
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }

            }
            if (result.getList().size() == successResult.size()) {
                //先删除所有数据
                QueryWrapper<HrQuickDeduction> qw = new QueryWrapper<>();
                qw.eq("is_delete", 0);
                hrQuickDeductionRepository.delete(qw);
                for (HrQuickDeduction hrQuickDeduction : successResult) {
                    hrQuickDeductionRepository.insert(hrQuickDeduction);
                }
                redisCache.setCacheObject(redisKey, 100, 10, TimeUnit.MINUTES);
            } else {
                for (HrQuickDeductionDTO hrQuickDeductionDTO : result.getList()) {
                    if (hrQuickDeductionDTO.getErrorMsg() == null) {
                        hrQuickDeductionDTO.setErrorMsg("正常数据");
                    }
                }
            }


            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("速算扣除数批量导入", HrQuickDeductionDTO.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.QUICK_DEDUCTION.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("速算扣除数异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "速算扣除数导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    private void checkQuickData(List<HrQuickDeductionDTO> list) {
        //级别的数组
        ArrayList<Integer> numberList = new ArrayList<>();
        HashMap<Integer, HrQuickDeductionDTO> hashMap = new HashMap<>();
        for (HrQuickDeductionDTO hrQuickDeductionDTO : list) {
            try {
                int number = Integer.parseInt(hrQuickDeductionDTO.getQuickDeductionSeries());
                numberList.add(number);
                hashMap.put(number, hrQuickDeductionDTO);
            } catch (NumberFormatException e) {
                throw new CommonException("级别必须是数字");
            }
        }
        Collections.sort(numberList);
        Integer integer = numberList.get(0);
        if (integer != 1) {
            throw new CommonException("级别要已1为最高等级");
        }
        for (int i = 0; i < numberList.size() - 1; i++) {
            int a = (int) numberList.get(i + 1);
            int b = (int) numberList.get(i);
            int c = a - b;
            if (c != 1) {
                throw new CommonException("级别需要是连续的数字,请注意级别" + (i + 1) + "以及下一等级");
            }
            HrQuickDeductionDTO hrQuickDeductionDTO = hashMap.get(i + 2);
            HrQuickDeductionDTO hrQuickDeductionDTO1 = hashMap.get(i + 1);
            if (hrQuickDeductionDTO.getMinPayTaxes() != null && hrQuickDeductionDTO1.getMaxPayTaxes() != null) {
                if (!hrQuickDeductionDTO.getMinPayTaxes().equals(hrQuickDeductionDTO1.getMaxPayTaxes())) {
                    throw new CommonException("级别" + (i + 2) + "的最小值，不等于级别" + (i + 1) + "的最大值");
                }
            }
        }
        //Integer[] integers = numberList.toArray(new Integer[0]);
    }

    /**
     * 处理小数
     *
     * @param type                0 乘法 1除法
     * @param personageScaleExcel
     * @return
     */
    private BigDecimal extracted(int type, BigDecimal personageScaleExcel) {
        BigDecimal bignum = new BigDecimal("100");
        BigDecimal multiply = null;
        if (type == 0) {
            multiply = personageScaleExcel.multiply(bignum).setScale(2, BigDecimal.ROUND_DOWN);
        } else {
            multiply = personageScaleExcel.divide(bignum, 4, BigDecimal.ROUND_DOWN);
        }
        return multiply;
    }

    /**
     * 验证是否大一1
     *
     * @param unitPension
     * @param message
     */
    private BigDecimal importVerify(String unitPension, String message) {
        BigDecimal unitPensionBig = null;
        try {
            unitPensionBig = new BigDecimal(unitPension);
        } catch (Exception e) {
            throw new CommonException(message);
        }
        BigDecimal bigDecimal = new BigDecimal("1.00");
        if (unitPensionBig.compareTo(bigDecimal) == 1) {
            throw new CommonException(message);
        }
        return unitPensionBig;
    }

    @Async
    public void expenseManage(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrExpenseManageDTO> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrExpenseManageDTO.class);
            ImportResultDTO importResult;
            int listSize = result.getList().size();
            int scale = 0;
            Map<String, List<HrExpenseManageDTO>> map = result.getList().stream().collect(Collectors.groupingBy(o -> o.getClientName() + "_" + o.getExpenseName()));
            List<HrClient> hrClientList = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("is_delete", 0));
            Map<Integer, String> expenseType = codeTableService.findCodeTableByInnerName("expensetype");
            StringBuilder sbError = new StringBuilder();
            for (HrExpenseManageDTO hrExpenseManageDTO : result.getList()) {
                try {
                    sbError.delete(0, sbError.length());
                    String key = hrExpenseManageDTO.getClientName() + "_" + hrExpenseManageDTO.getExpenseName();
                    List<HrExpenseManageDTO> mapList = map.get(key);
                    if (CollectionUtils.isNotEmpty(mapList) && mapList.size() > 1) {
                        throw new CommonException("导入Excel中该数据，同公司的费用项名称存在重复数据!");
                    }
                    List<HrClient> hrClients = hrClientList.stream().filter(ls -> ls.getUnitNumber().equals(hrExpenseManageDTO.getUnitNumber()) && ls.getClientName().equals(hrExpenseManageDTO.getClientName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(hrClients)) {
                        throw new CommonException("该单位编号、客户名称对应的客户在系统中不存在!");
                    }
                    hrExpenseManageDTO.setClientId(hrClients.get(0).getId());
                    HrExpenseManage hrExpenseManage = hrExpenseManageMapper.toEntity(hrExpenseManageDTO);
                    //校验名称是否存在
                    QueryWrapper<HrExpenseManage> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("expense_name", hrExpenseManageDTO.getExpenseName());
                    queryWrapper.eq("client_id", hrExpenseManageDTO.getClientId());
                    List<HrExpenseManage> list = hrExpenseManageRepository.selectList(queryWrapper);
                    if (CollectionUtils.isNotEmpty(list)) {
                        sbError.append("该客户对应的名称在系统中已存在");
                    }
                    //校验类型是否符合
                    int expenseInt = -1;
                    try {
                        expenseInt = Integer.parseInt(hrExpenseManageDTO.getExpenseType());
                    } catch (NumberFormatException e) {
                        sbError.append("费用类型不存在");
                    }
                    String type = expenseType.get(expenseInt);
                    if (StringUtils.isBlank(type)) {
                        sbError.append("费用类型不存在!");
                    }
                    if (StringUtils.isEmpty(sbError)) {
                        hrExpenseManageRepository.insert(hrExpenseManage);
                    }
                } catch (Exception e) {
                    log.error("费用项管理异常:{}", e.getMessage());
                    if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("已存在")) {
                        hrExpenseManageDTO.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    scale++;
                    hrExpenseManageDTO.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }

            }

            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("费用项管理批量导入", HrExpenseManageDTO.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.FEE_REVIEW.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("费用项管理异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "费用项管理导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    @Async
    public void stationImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrStationImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrStationImport.class);
            ImportResultDTO importResult;

            int listSize = result.getList().size();
            int scale = 0;
            for (HrStationImport hrStationImport : result.getList()) {

                try {
                    HrStation hrStation = checkData(hrStationImport);
                    hrStationRepository.insert(hrStation);
                } catch (Exception e) {
                    log.error("岗位异常:{}", e.getMessage());
                    if (e.getMessage().contains("已存在")) {
                        hrStationImport.setDuplicate(true);
                        hrStationImport.setErrorMsg(e.getMessage());
                    } else {
                        hrStationImport.setErrorMsg(e.getMessage());
                    }
                } finally {
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }

            }

            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("岗位管理批量导入", HrStationImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.STATION.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("岗位管理异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "岗位管理导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    private HrStation checkData(HrStationImport hrStationImport) {
        Map<String, Integer> industryType = codeTableService.findCodeTableByInnerValue("industryType");
        Integer industryTypeName = industryType.get(hrStationImport.getIndustryTypeName());
        StringBuilder sbError = new StringBuilder();
        if (industryTypeName == null) {
            sbError.append("此行业分类系统中不存在!");
        }
        Map<String, Integer> professionType = codeTableService.findCodeTableByInnerValue("professionType");
        Integer professionTypeName = professionType.get(hrStationImport.getProfessionTypeName());
        if (professionTypeName == null) {
            sbError.append("此职业分类系统中不存在!");
        }
        //
        String professionName = hrStationImport.getProfessionName();
        QueryWrapper<HrStation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("profession_name", professionName);
        List<HrStation> list = hrStationRepository.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            sbError.append("系统中职业名称已存在!");
        }
        HrStation hrStation = new HrStation();
        BeanUtils.copyProperties(hrStationImport, hrStation);
        hrStation.setIndustryType(industryTypeName);
        hrStation.setProfessionType(professionTypeName);
        if (StringUtils.isNotEmpty(sbError)) {
            throw new CommonException(sbError.toString());
        }

        return hrStation;
    }

    @Async
    public void userImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrUserImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrUserImport.class);
            ImportResultDTO importResult;
            int listSize = result.getList().size();
            int scale = 0;
            Map<String, List<HrUserImport>> map = result.getList().stream().collect(Collectors.groupingBy(HrUserImport::getUserName));
            StringBuilder sbError = new StringBuilder();
            for (HrUserImport hrUserImport : result.getList()) {
                try {
                    sbError.delete(0, sbError.length());
                    List<HrUserImport> mapList = map.get(hrUserImport.getUserName());
                    if (CollectionUtils.isNotEmpty(mapList) && mapList.size() > 1) {
                        throw new CommonException("导入Excel中该数据用户名称重复!");
                    }
                    checkData(hrUserImport, sbError);
                    // 创建用户
                    User user = new User();
                    BeanUtils.copyProperties(hrUserImport, user);
                    user.setPassword(passwordEncoder.encode("c123456"));
                    if (hrUserImport.getUserStatus() == 0) {
                        user.setUserStatus(false);
                    }
                    if (StringUtils.isEmpty(sbError)) {
                        userRepository.insert(user);
                        // 创建角色关联
                        UserRole userRole = new UserRole();
                        userRole.setUserId(user.getId());
                        userRole.setRoleId(hrUserImport.getRoleId());
                        this.userRoleService.save(userRole);
                    }

                } catch (Exception e) {
                    log.error("用户异常:{}", e.getMessage());
                    if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("已存在")) {
                        hrUserImport.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    scale++;
                    hrUserImport.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }

            }

            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("用户管理批量导入", HrUserImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.USER.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("用户管理步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "用户管理导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    private void checkData(HrUserImport hrUserImport) {
        //用户名是否存在
        List<User> userNameList = this.userRepository.selectList(new QueryWrapper<User>().eq("user_name", hrUserImport.getUserName()));
        if (userNameList != null && userNameList.size() > 0) {
            throw new CommonException("用户名已存在");
        }
        String regex = "^[0-9a-zA-Z]{1,}$";
        boolean matches = hrUserImport.getUserName().matches(regex);
        if (!matches) {
            throw new CommonException("用户名格式不正确");
        }
        // 验证重复
        if (StringUtils.isNotBlank(hrUserImport.getPhone())) {
            List<User> userList = this.userRepository.selectForPhone(hrUserImport.getPhone());
            if (userList != null && userList.size() > 0) {
                throw new CommonException("手机号已经存在");
            }
            String phone = hrUserImport.getPhone();
            boolean cellPhoneNo = isCellPhoneNo(phone);
            if (!cellPhoneNo) {
                throw new CommonException("手机号格式不正确");
            }
        }
        if (StringUtils.isNotBlank(hrUserImport.getWorkPhone())) {
            if (!isCellPhoneNo(hrUserImport.getWorkPhone())) {
                throw new CommonException("工作电话格式不正确");
            }
        }
        //验证角色是否存在
        String roleName = hrUserImport.getRoleName();
        if (roleName.equals("超级管理员") || roleName.equals("客户")) {
            throw new CommonException("此角色不可以导入");
        }

        QueryWrapper<Role> roleQueryWrapper = new QueryWrapper<>();
        roleQueryWrapper.eq("role_name", roleName);
        List<Role> roleList = roleRepository.selectList(roleQueryWrapper);
        if (org.springframework.util.CollectionUtils.isEmpty(roleList)) {
            throw new CommonException("角色不存在");
        } else {
            Integer id = roleList.get(0).getId();
            hrUserImport.setRoleId(id);
        }

    }

    private void checkData(HrUserImport hrUserImport, StringBuilder sbError) {
        //用户名是否存在
        List<User> userNameList = this.userRepository.selectList(new QueryWrapper<User>().eq("user_name", hrUserImport.getUserName()));
        if (userNameList != null && userNameList.size() > 0) {
            sbError.append("用户名已存在!");
        }
        String regex = "^[0-9a-zA-Z]{1,}$";
        boolean matches = hrUserImport.getUserName().matches(regex);
        if (!matches) {
            sbError.append("用户名格式不正确!");
        }
        // 验证重复
        if (StringUtils.isNotBlank(hrUserImport.getPhone())) {
            List<User> userList = this.userRepository.selectForPhone(hrUserImport.getPhone());
            if (userList != null && userList.size() > 0) {
                sbError.append("手机号已经存在!");
            }
            String phone = hrUserImport.getPhone();
            boolean cellPhoneNo = isCellPhoneNo(phone);
            if (!cellPhoneNo) {
                sbError.append("手机号格式不正确!");
            }
        }
        if (StringUtils.isNotBlank(hrUserImport.getWorkPhone())) {
            if (!isCellPhoneNo(hrUserImport.getWorkPhone())) {
                sbError.append("工作电话格式不正确!");
            }
        }
        //验证角色是否存在
        String roleName = hrUserImport.getRoleName();
        if (roleName.equals("超级管理员") || roleName.equals("客户")) {
            sbError.append("此角色不可以导入!");
        }

        QueryWrapper<Role> roleQueryWrapper = new QueryWrapper<>();
        roleQueryWrapper.eq("role_name", roleName);
        List<Role> roleList = roleRepository.selectList(roleQueryWrapper);
        if (org.springframework.util.CollectionUtils.isEmpty(roleList)) {
            sbError.append("角色不存在!");
        } else {
            Integer id = roleList.get(0).getId();
            hrUserImport.setRoleId(id);
        }

    }
}
