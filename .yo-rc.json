{"generator-jhipster": {"promptValues": {"packageName": "cn.casair", "nativeLanguage": "en"}, "jhipsterVersion": "6.0.1", "applicationType": "monolith", "baseName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "packageName": "cn.casair", "packageFolder": "cn/casair", "serverPort": "8080", "authenticationType": "jwt", "cacheProvider": "no", "websocket": false, "databaseType": "sql", "devDatabaseType": "mysql", "prodDatabaseType": "mysql", "searchEngine": false, "messageBroker": false, "serviceDiscoveryType": false, "buildTool": "maven", "enableSwaggerCodegen": false, "jwtSecretKey": "ZDMzN2MxNzk3MDZkN2MyNmU0YTczNmJkMDkxMTM0OTM1NWE0MmQ2Y2M0NTg1Yzk1OWVjYjZmNDkxNWQ5MjhmMjFlOThhOTU2MGZlMzYzOGE5MTlhMTI1M2FmZmI3Y2M3ODM4Zjc1YWU2NDBmMjVjODYzYjg1M2QxYjM2YWE4Y2U=", "clientFramework": "angularX", "clientTheme": "none", "clientThemeVariant": "", "useSass": true, "clientPackageManager": "npm", "testFrameworks": [], "jhiPrefix": "jhi", "entitySuffix": "", "dtoSuffix": "DTO", "otherModules": [], "enableTranslation": true, "nativeLanguage": "en", "languages": ["en", "zh-cn"]}}