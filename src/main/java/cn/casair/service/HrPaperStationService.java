package cn.casair.service;

import cn.casair.domain.HrPaperStation;
import cn.casair.dto.HrPaperStationDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 试卷外键岗位表服务类
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
public interface HrPaperStationService extends IService<HrPaperStation> {


    /**
     * 创建试卷外键岗位表
     * @param hrPaperStationDTO
     * @return
     */
    HrPaperStationDTO createHrPaperStation(HrPaperStationDTO hrPaperStationDTO);

    /**
     * 修改试卷外键岗位表
     * @param hrPaperStationDTO
     * @return
     */
    Optional<HrPaperStationDTO> updateHrPaperStation(HrPaperStationDTO hrPaperStationDTO);

    /**
     * 查询试卷外键岗位表详情
     * @param id
     * @return
     */
    HrPaperStationDTO getHrPaperStation(String id);

    /**
     * 删除试卷外键岗位表
     * @param id
     */
    void deleteHrPaperStation(String id);

    /**
     * 批量删除试卷外键岗位表
     * @param ids
     */
    void deleteHrPaperStation(List<String> ids);

    /**
     * 分页查询试卷外键岗位表
     * @param hrPaperStationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrPaperStationDTO hrPaperStationDTO,Long pageNumber,Long pageSize);
    }
