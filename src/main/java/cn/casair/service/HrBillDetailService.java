package cn.casair.service;

import cn.casair.domain.HrBillDetail;
import cn.casair.dto.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 账单详情服务类
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
public interface HrBillDetailService extends IService<HrBillDetail> {

    /**
     * 导出银行报账单
     *
     * @param params
     * @return void
     * <AUTHOR>
     * @date 2021/11/10
     **/
    void exportBankStatement(Map<String, Object> params, HttpServletResponse response);

    /**
     * 导出未入账账单明细
     *
     * @param billId
     * @param httpServletResponse
     */
    void exportBillDetailList(String billId, HttpServletResponse httpServletResponse);

    /**
     * 导出多个账单未入账员工明细
     * @param billIdIds
     * @param httpServletResponse
     */
    void exportBillDetailBatch(List<String> billIdIds, HttpServletResponse httpServletResponse);

    /**
     * 获取员工账单明细
     *
     * @param billDetailId 账单明细id
     * @return cn.casair.dto.HrStaffSalaryDetailDTO
     * <AUTHOR>
     * @date 2021/11/5
     **/
    HrStaffSalaryDetailDTO getStaffSalaryDetail(String billDetailId);

    /**
     * 获取员工薪资列表
     *
     * @return java.util.List<cn.casair.dto.HrStaffSalaryBillDTO>
     * <AUTHOR>
     * @date 2021/11/5
     **/
    List<HrStaffSalaryDTO> getStaffSalaryList();

    /**
     * 批量增加未入账员工
     *
     * @param ids
     * @return
     */
    List<HrBillDetailDTO> batchAddHrBillDetail(HrBillDetailDTO hrBillDetailDTO);

    /**
     * 查询账单详情
     *
     * @param billId 账单id
     * @param isUsed 是否可用
     * @return
     */
    List<HrBillDetailDTO> findList(String billId, Integer isUsed);

    /**
     * 创建账单详情
     *
     * @param hrBillDetailDTO
     * @return
     */
    HrBillDetailDTO createHrBillDetail(HrBillDetailDTO hrBillDetailDTO);

    /**
     * 修改账单详情
     *
     * @param hrBillDetailDTO
     * @return
     */
    Optional<HrBillDetailDTO> updateHrBillDetail(HrBillDetailDTO hrBillDetailDTO);

    /**
     * 查询账单详情详情
     *
     * @param id
     * @return
     */
    HrBillDetailDTO getHrBillDetail(String id);

    /**
     * 删除账单详情
     *
     * @param id
     */
    void deleteHrBillDetail(String id);

    /**
     * 批量删除账单详情
     *
     * @param ids
     */
    void deleteHrBillDetail(List<String> ids);

    /**
     * 分页查询账单详情
     *
     * @param hrBillDetailDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillDetailDTO hrBillDetailDTO, Long pageNumber, Long pageSize);

    /**
     * 获取月平均收入分布结果
     *
     * @param hrBillDetailDTO
     * @return
     */
    Map<String, Integer> getAvgSalaryMonthlyData(HrBillDetailDTO hrBillDetailDTO);

    /**
     * 导出数据明细
     *  @param hrDataDetailDTO
     * @param response
     * @return
     */
    String exportDataList(HrDataDetailDTO hrDataDetailDTO, HttpServletResponse response);

    /**
     * 查询多个账单详情列表
     *
     * @param billIdList
     * @return
     */
    List<HrBillDetailDTO> getListByBillIdBatch(List<String> billIdList, Integer isUsed);

    /**
     * 赋值账单费用详情
     * @param hrStaffSalaryDetailDTO
     * @param billDetailIds
     */
    void assignmentStaffSalaryDetail(HrStaffSalaryDetailDTO hrStaffSalaryDetailDTO, List<String> billDetailIds);

    /**
     * 导出中石化账单各项目模板
     * @param param
     * @param response
     * @return
     */
    String exportSinopecBill(Map<String, Object> param, HttpServletResponse response);

    /**
     * 查询数据明细数据合计
     * @param hrBillDetailDTO
     * @return
     */
    HrBillDetailDTO getTotalData(HrBillDetailDTO hrBillDetailDTO);

    /**
     * 员工工资发放列表
     *
     * @param hrBillDetailDTO
     * @return
     */
    List<HrBillDetailDTO> findSalaryPaymentList(HrBillDetailDTO hrBillDetailDTO);

    /**
     * 维护工资发放状态
     *
     * @param billDetailGrantDTO
     * @return
     */
    void maintainBillDetailGrant(HrBillDetailGrantDTO billDetailGrantDTO);

    /**
     * 员工明细导出银行报盘文件
     *
     * @param billDetailDTO
     * @return
     */
    String bankReportExport(HrBillDetailDTO billDetailDTO);
}
