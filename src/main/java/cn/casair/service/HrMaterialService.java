package cn.casair.service;
import cn.casair.domain.HrMaterial;
import cn.casair.dto.HrMaterialDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

/**
 * 材料表服务类
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
public interface HrMaterialService extends IService<HrMaterial> {


    /**
     * 创建材料表
     * @param hrMaterialDTO
     * @return
     */
    HrMaterialDTO createHrMaterial(HrMaterialDTO hrMaterialDTO);

    /**
     * 修改材料表
     * @param hrMaterialDTO
     * @return
     */
    Optional<HrMaterialDTO> updateHrMaterial(HrMaterialDTO hrMaterialDTO);

    /**
     * 查询材料表详情
     * @param id
     * @return
     */
    HrMaterialDTO getHrMaterial(String id);

    /**
     * 删除材料表
     * @param id
     */
    void deleteHrMaterial(String id);

    /**
     * 批量删除材料表
     * @param ids
     */
    void deleteHrMaterial(List<String> ids);

    /**
     * 分页查询材料表
     * @param hrMaterialDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrMaterialDTO hrMaterialDTO,Long pageNumber,Long pageSize);


}
