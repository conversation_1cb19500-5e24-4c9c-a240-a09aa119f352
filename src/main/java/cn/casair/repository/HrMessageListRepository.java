package cn.casair.repository;

import cn.casair.domain.HrMessageList;
import cn.casair.domain.HrMessageRole;
import cn.casair.dto.HrMessageListDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 消息列表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
@Repository
public interface HrMessageListRepository extends BaseMapper<HrMessageList> {

    void updateMessageList(HrMessageListDTO hrMessageListDTO);

    void updateMessageRole(HrMessageRole hrMessageRole);

    IPage<HrMessageListDTO> selectPageMessageList(Page<HrMessageList> page,  @Param("param1")HrMessageListDTO hrMessageListDTO);


    void BatchUpdateHrMessageList(@Param("param1")String messageId, @Param("param2")String uid);

    List<String> getHrMessageListType();
}
