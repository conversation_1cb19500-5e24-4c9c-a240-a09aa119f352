<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRegistrationDetailsRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, brochure_id, station_id, client_id, info_id, staff_id, status, hiring_publicity, publicity_date, appendix_id, admission_ticket_info,admission_ticket_url,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <sql id="listSQl">
        SELECT
            hrd.*,
            hrs.id as stationId,
            hrs.recruitment_station_name as recruitmentStationName,
            hrs.exam_format as examFormat,
            hts.`name` AS 'name',
            hts.certificate_num AS certificateNum,
            hts.sex,
            hts.highest_education AS highestEducation,
            hts.marital_status AS maritalStatus,
            hts.birthday,
            hts.phone,
            her.score,
            her.iz_extremum,
            her.interview_score AS interviewScore,
            her.interview_score_result AS interviewScoreResult,
            her.add_result AS addResult,
            her.exam_result AS examResult,
            her.final_result AS finalResult,
            her.physical_examination_result AS physicalExaminationResult,
            hrd.created_date as createdDate,
            hrs.interview_pass_line as interviewPassLine,
            hrs.written_pass_line as writtenPassLine,
            hrs.register_template_id AS registerTemplateId,
            hc.client_name as clientName,
            hrs.is_need_pay as isNeedPay,
            hrb.audit_start_date as auditStartDate,
            hrb.audit_end_date as auditEndDate,
            hrb.recruit_brochure_name as recruitBrochureName,
            hrs.is_need_pay as isNeedPay,
            hrs.recruitment_station_id as systemStationId,
            hrs.interview_score_weight as interviewScoreWeight,
             hrs.written_score_weight AS writtenScoreWeight
        FROM
            hr_registration_details hrd
                LEFT JOIN hr_recruitment_brochure hrb ON hrd.brochure_id = hrb.id AND hrb.is_delete = 0
                LEFT JOIN hr_recruitment_station hrs ON hrd.station_id = hrs.id AND hrs.is_delete = 0
                LEFT JOIN hr_talent_staff hts ON hrd.staff_id = hts.id
                 LEFT JOIN hr_exam_result her ON hrd.staff_id = her.staff_id  AND her.is_delete =0 and her.profession_name= hrd.station_name and hrb.recruit_brochure_name =her.exam_name
                 LEFT JOIN hr_client hc on hrb.client_id=hc.id and hc.is_delete=0
    </sql>

    <sql id="Select_SQL">
        <include refid="listSQl"/>
        WHERE
        hrd.is_delete = 0 AND hts.is_delete = 0
        <if test="param1.id !=null">
            and hrb.id = #{param1.id}
        </if>
        <if test="param1.recruitmentBrochureId !=null ">
            AND hrd.brochure_id = #{param1.recruitmentBrochureId}
        </if>
        <if test="param1.stationNameList != null and param1.stationNameList.size() >0">
            AND hrd.station_name in
            <foreach collection="param1.stationNameList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.number!=null  ">
            and hrd.number like concat('%',#{param1.number},'%')
        </if>
        <if test="param1.ids!=null and param1.ids.size() > 0">
            and hrd.id in
            <foreach collection="param1.ids" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.stationNameLists!=null and param1.stationNameLists.size() > 0">
            and hrd.station_name in
            <foreach collection="param1.stationNameLists" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.examFormatList!=null and param1.examFormatList.size() > 0">
            and hrs.exam_format IN
            <foreach collection="param1.examFormatList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.name!=null  and param1.name!='' ">
            and hts.`name` like concat('%',#{param1.name},'%')
        </if>

        <if test="param1.certificateNum!=null  and param1.certificateNum!='' ">
            and hts.certificate_num like concat('%',#{param1.certificateNum},'%')
        </if>

        <if test="param1.sexList!=null and param1.sexList.size() > 0">
            and hts.sex IN
            <foreach collection="param1.sexList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.highestEducationList!=null and param1.highestEducationList.size() > 0">
            and hts.highest_education IN
            <foreach collection="param1.highestEducationList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.maritalStatusList!=null and param1.maritalStatusList.size() > 0">
            and hts.marital_status IN
            <foreach collection="param1.maritalStatusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.contractStartDateStart!=null ">
            AND hts.birthday <![CDATA[ >= ]]> #{param1.contractStartDateStart}
        </if>
        <if test="param1.contractStartDateEnd!=null ">
            AND hts.birthday<![CDATA[ <= ]]> #{param1.contractStartDateEnd}
        </if>

        <if test="param1.phone!=null  and param1.phone!='' ">
            and hts.phone like concat('%',#{param1.phone},'%')
        </if>

        <if test="param1.scoreStart!=null ">
            AND her.score <![CDATA[ >= ]]> #{param1.scoreStart}
        </if>
        <if test="param1.scoreEnd!=null ">
            AND her.score <![CDATA[ <= ]]> #{param1.scoreEnd}
        </if>

        <if test="param1.interviewScoreResultStart!=null ">
            AND her.interview_score_result <![CDATA[ >= ]]> #{param1.interviewScoreResultStart}
        </if>
        <if test="param1.interviewScoreResultEnd!=null ">
            AND her.interview_score_result <![CDATA[ <= ]]> #{param1.interviewScoreResultEnd}
        </if>

        <if test="param1.finalResultStart!=null ">
            AND her.final_result <![CDATA[ >= ]]> #{param1.finalResultStart}
        </if>
        <if test="param1.finalResultEnd!=null ">
            AND her.final_result <![CDATA[ <= ]]> #{param1.finalResultEnd}
        </if>

        <if test="param1.addResultStart!=null ">
            AND her.add_result <![CDATA[ >= ]]> #{param1.addResultStart}
        </if>
        <if test="param1.addResultEnd!=null ">
            AND her.add_result <![CDATA[ <= ]]> #{param1.addResultEnd}
        </if>


        <if test="param1.examResultList!=null and param1.examResultList.size() > 0">
            and her.exam_result IN
            <foreach collection="param1.examResultList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.examResultList!=null and param1.examResultList.size() > 0">
            and her.exam_result IN
            <foreach collection="param1.examResultList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.physicalExaminationResultList!=null and param1.physicalExaminationResultList.size() > 0">
            and her.physical_examination_result IN
            <foreach collection="param1.physicalExaminationResultList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.remark!=null  and param1.remark!='' ">
            and hrd.remark like concat('%',#{param1.remark},'%')
        </if>

        <if test="param1.statusList!=null and param1.statusList.size() > 0">
            and hrd.status IN
            <foreach collection="param1.statusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </sql>
    <!-- 通用查询映射结果 -->
    <sql id="findSQL">
        SELECT
            hrd.*, her.*
        FROM
            hr_registration_details hrd
        LEFT JOIN hr_recruitment_brochure hrb ON hrd.brochure_id = hrb.id
        AND hrb.is_delete = 0
        LEFT JOIN hr_exam_result her ON hrd.staff_id = her.staff_id
        AND her.is_delete = 0
        AND her.profession_name = hrd.station_name
        AND hrb.recruit_brochure_name = her.exam_name
    </sql>

    <resultMap id="BaseResultMap" type="cn.casair.domain.HrRegistrationDetails">
                    <id column="id" property="id"/>
                    <result column="brochure_id" property="brochureId"/>
                    <result column="station_id" property="stationId"/>
                    <result column="client_id" property="clientId"/>
                    <result column="info_id" property="infoId"/>
                    <result column="staff_id" property="staffId"/>
                    <result column="status" property="status"/>
                    <result column="hiring_publicity" property="hiringPublicity"/>
                    <result column="publicity_date" property="publicityDate"/>
                    <result column="appendix_id" property="appendixId"/>
                    <result column="admission_ticket_info" property="admissionTicketInfo"/>
                    <result column="admission_ticket_url" property="admissionTicketUrl"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

    <select id="selectByBrochureId" resultType="cn.casair.domain.HrRegistrationDetails">
        SELECT * FROM hr_registration_details WHERE brochure_id = #{brochureId} AND is_delete = 0
    </select>

    <select id="selectPages" resultType="cn.casair.dto.HrRegistrationDetailsDTO">
        <include refid="Select_SQL"/>
    </select>

    <select id="importDrawLotsNumberTemplate" resultType="cn.casair.dto.excel.HrDrawLotsNumberTemplate">
        SELECT
            *
        FROM
            hr_registration_details hrb
                LEFT JOIN hr_talent_staff hts ON hrb.staff_id = hts.id
            where hrb.brochure_id = #{brochureId}  AND hrb.is_delete = 0
        ORDER BY
            hrb.station_name
    </select>
    <select id="detailsExport" resultType="cn.casair.dto.excel.HrRegistrationDetailsExport">
        <include refid="listSQl"/>
        WHERE
            hrd.is_delete = 0 AND hts.is_delete = 0
        and hrd.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

    <select id="findPaymentAndPassNum" resultType="cn.casair.domain.HrRegistrationDetails">
        SELECT
            hrd.*
        FROM
            hr_registration_details hrd
        LEFT JOIN hr_registration_order hro ON hrd.id = hro.info_id AND hro.is_delete = 0
        WHERE hrd.is_delete = 0 AND hro.pay_status = 1 AND hrd.brochure_id = #{recruitmentBrochureId}
            AND hrd.station_id = #{recruitmentStationId} AND hrd.`status` = #{status} ORDER BY pay_time ASC
    </select>

    <update id="updateExam">
        UPDATE hr_exam_result set interview_score=#{interviewScore} ,interview_score_result=#{interviewScoreResult},final_result=#{finalResult} where profession_name=#{stationName} and staff_id=#{staffId} and is_delete=0 and exam_name=#{recruitBrochureName}
    </update>

    <select id="findRegistrationDetails" resultType="cn.casair.dto.HrRegistrationDetailsDTO">
        <include refid="listSQl"/>
        WHERE hrd.is_delete = 0 AND hts.is_delete = 0
        AND hrd.brochure_id = #{recruitmentBrochureId}
        AND hrd.station_id = #{recruitmentStationId}
        AND hrd.`status` in
        <foreach collection="statusList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

        <select id="selectBybrochureId" resultType="cn.casair.dto.HrTemplateDTO">
            SELECT
                hrd.id,
                ht.preview,
                ht.content_id
            FROM
                hr_registration_details hrd
                    LEFT JOIN hr_recruitment_station hrb ON hrd.station_id = hrb.id
                    AND hrb.is_delete = 0
                    LEFT JOIN hr_template ht ON ht.id = hrb.register_template_id
                    AND ht.is_delete = 0
            WHERE hrd.is_delete = 0
            <if test="id != null and id != ''">
                AND hrd.id =  #{id}
            </if>
            <if test="ids != null and ids.size() > 0">
                AND hrd.id IN
                <foreach collection="ids" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
    </select>
    <select id="selectOnea" resultType="cn.casair.domain.HrRegistrationDetails">
        SELECT
            *
        FROM
            hr_registration_details
        WHERE
            is_delete = 0
          AND
            staff_id =#{id}
                AND brochure_id = #{brochureId} and   status != 1

    </select>
    <select id="selectRoleName" resultType="java.lang.String">
        SELECT
            sr.role_name
        FROM
            sys_user su
                LEFT JOIN sys_user_role sur ON sur.user_id = su.id
                AND su.is_delete = 0
                AND su.user_status = 1
                and sur.is_delete = 0
                LEFT JOIN sys_role sr ON sur.role_id = sr.id
        where su.id=#{id}
    </select>

    <update id="updatestatus">
            UPDATE hr_registration_details set `status`=#{param1.type} where station_name=#{param1.stationName} and staff_id=#{param1.staffId} and is_delete=0 and brochure_id=#{param1.brochureId}

    </update>

        <update id="updatePassNum">
            UPDATE hr_recruitment_station set pass_num=#{i} where id=#{id}
    </update>

        <update id="updatestas">
            UPDATE hr_registration_details
            SET STATUS = 16
            WHERE
                is_delete = 0
              AND id=#{s}
    </update>

        <update id="updateState">
            UPDATE hr_registration_details set `status` =19 where id =#{id} and is_delete=0

        </update>

    <select id="findDetailsWrittenExam" resultType="cn.casair.dto.HrRegistrationDetailsDTO">
        SELECT
            hrd.*, her.*
        FROM
            hr_registration_details hrd
        LEFT JOIN hr_recruitment_brochure hrb ON hrd.brochure_id = hrb.id
        AND hrb.is_delete = 0
        LEFT JOIN hr_exam_result her ON hrd.staff_id = her.staff_id
        AND her.is_delete = 0
        AND her.profession_name = hrd.station_name
        AND hrb.recruit_brochure_name = her.exam_name
        WHERE hrd.is_delete = 0 AND hrd.brochure_id = #{recruitmentBrochureId} AND hrd.station_id = #{recruitmentStationId}
        <if test="statusList!=null and statusList.size() > 0">
            AND hrd.status IN
            <foreach collection="statusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="statusList == null and statusList.size() = 0">
            AND hrd.`status` IN (4,6,7,9,10,12,13,15,16,17,20)
        </if>
    </select>

        <update id="updateBrochureStates">
            UPDATE hr_recruitment_brochure set `register_state` =6 where id =#{brochureId} and is_delete=0
    </update>

        <update id="updateStatusReview">
            UPDATE hr_registration_details set `status` =#{status} where id =#{id} and is_delete=0
    </update>

        <update id="updateStatusReviews">
            UPDATE hr_registration_details set `status` =#{status} ,denial_reason=#{denialReason} where id =#{id} and is_delete=0
    </update>

    <select id="detailsExports" resultType="cn.casair.dto.HrRegistrationDetailsDTO">

        SELECT
        hrd.*,
        hrs.id as stationId,
        hrs.recruitment_station_name as recruitmentStationName,
        hrs.exam_format as examFormat,
        hts.`name` AS 'name',
        hts.certificate_num AS certificateNum,
        hts.sex,
        hts.highest_education AS highestEducation,
        hts.marital_status AS maritalStatus,
        hts.birthday,
        hts.phone,
        her.score,
        her.interview_score AS interviewScore,
        her.interview_score_result AS interviewScoreResult,
        her.add_result AS addResult,
        her.exam_result AS examResult,
        her.final_result AS finalResult,
        her.physical_examination_result AS physicalExaminationResult,
        hrd.created_date as createdDate,
        hrs.interview_pass_line as interviewPassLine,
        hrs.written_pass_line as writtenPassLine,
        hrs.register_template_id AS registerTemplateId,
        hc.client_name as clientName,
        hrs.is_need_pay as isNeedPay,
        hrb.audit_start_date as auditStartDate,
        hrb.audit_end_date as auditEndDate,
        hrb.recruit_brochure_name as recruitBrochureName,
        hrs.is_need_pay as isNeedPay,
        hrs.recruitment_station_id as systemStationId,
        hrs.interview_score_weight as interviewScoreWeight,
        hrs.written_score_weight AS writtenScoreWeight
        FROM
        hr_registration_details hrd
        LEFT JOIN hr_recruitment_brochure hrb ON hrd.brochure_id = hrb.id AND hrb.is_delete = 0
        LEFT JOIN hr_recruitment_station hrs ON hrd.station_id = hrs.id AND hrs.is_delete = 0
        LEFT JOIN hr_talent_staff hts ON hrd.staff_id = hts.id
        LEFT JOIN hr_exam_result her ON hrd.staff_id = her.staff_id  AND her.is_delete =0 and her.profession_name= hrd.station_name and hrb.recruit_brochure_name =her.exam_name
        LEFT JOIN hr_client hc on hrb.client_id=hc.id and hc.is_delete=0
        WHERE
        hrd.is_delete = 0
        and hrd.id  =#{id}
    </select>
    <select id="detailsExportById" resultType="cn.casair.dto.excel.HrRegistrationDetailsExport">
        <include refid="listSQl"/>
        WHERE
        hrd.is_delete = 0 AND hts.is_delete = 0
        and hrd.id = #{id}
    </select>

    <select id="findRegistrationDetailsList" resultType="cn.casair.domain.HrRegistrationDetails">
        <include refid="Select_SQL"/>
    </select>

    <update id="updateAchievement">
            UPDATE hr_exam_result set  exam_result= #{param1.examResult} where exam_name=#{param1.examName} and profession_name=#{param1.professionName} and staff_id=#{param1.staffId} and is_delete=0

    </update>

        <update id="updateAchievements">
            UPDATE hr_exam_result set  physical_examination_result= #{param1.physicalExaminationResult} where exam_name=#{param1.examName} and profession_name=#{param1.professionName} and staff_id=#{param1.staffId} and is_delete=0

        </update>

        <update id="updatestatuss">
            UPDATE hr_registration_details set `status` =#{status} where id =#{id} and is_delete=0
        </update>

    <select id="findList" resultType="cn.casair.dto.HrRegistrationDetailsDTO">
        SELECT
            hrd.*, hrb.recruit_brochure_name,
            hrs.recruitment_station_name AS stationName,
            hts.`name`,
            hts.phone
        FROM
            hr_registration_details hrd
        LEFT JOIN hr_recruitment_brochure hrb ON hrd.brochure_id = hrb.id
        AND hrb.is_delete = 0
        LEFT JOIN hr_recruitment_station hrs ON hrd.station_id = hrs.id
        AND hrs.is_delete = 0
        LEFT JOIN hr_talent_staff hts ON hrd.staff_id = hts.id
        WHERE
            hrd.is_delete = 0
            AND hrd.brochure_id = #{brochureId}
            AND hrd.station_id IN
            <foreach collection="recruitmentStationIds" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
    </select>

    <delete id="deleteRegistrationDetailsById">
        DELETE FROM hr_registration_details WHERE id = #{id}
    </delete>
</mapper>
