<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.RoleRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_name, role_key, is_delete, created_by ,last_modified_by ,created_date ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.Role">
        <id column="id" property="id"/>
        <result column="role_name" property="roleName"/>
        <result column="role_key" property="roleKey"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getByUserId" resultType="cn.casair.domain.Role">
        select *
        from sys_role r
                 left join sys_user_role ur
                           on r.id = ur.role_id and ur.is_delete = 0
        where ur.user_id = #{userId}
          and r.is_delete = 0
    </select>

    <select id="getRolesByIds" resultType="string">
        SELECT GROUP_CONCAT(sr.`role_name`) FROM sys_role sr WHERE sr.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="getByRoleKeys" resultType="cn.casair.dto.RoleDTO">
        SELECT
            *
        FROM sys_role sr
        WHERE sr.is_delete = 0
          AND sr.role_key IN
        <foreach collection="list" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

</mapper>
