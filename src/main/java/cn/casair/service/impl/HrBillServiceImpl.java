package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.billl.BillExcelDataDTO;
import cn.casair.dto.billl.BillFieldInfoDTO;
import cn.casair.dto.excel.HrAnnualBonusExport;
import cn.casair.dto.excel.HrBankStatementExport;
import cn.casair.dto.excel.HrBillDetailExport;
import cn.casair.mapper.*;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 账单服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class HrBillServiceImpl extends ServiceImpl<HrBillRepository, HrBill> implements HrBillService {

    @Value("${file.temp-path}")
    private String fileTempPath;

    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrBillRepository hrBillRepository;
    private final HrBillMapper hrBillMapper;
    private final HrBillDetailRepository hrBillDetailRepository;
    private final HrBillDetailMapper hrBillDetailMapper;
    private final HrBillTotalRepository hrBillTotalRepository;
    private final HrBillTotalMapper hrBillTotalMapper;
    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final HrMakeUpUseRecordRepository hrMakeUpUseRecordRepository;
    private final HrBillDynamicFieldsRepository hrBillDynamicFieldsRepository;
    private final HrBillDynamicFieldsMapper hrBillDynamicFieldsMapper;
    private final HrBillDetailItemsRepository hrBillDetailItemsRepository;
    private final HrBillDetailItemsMapper hrBillDetailItemsMapper;
    private final HrAppendixService hrAppendixService;
    private final RedisCache redisCache;
    private final HrBillContentRecordRepository hrBillContentRecordRepository;
    private final HrClientService hrClientService;
    private final SysOperLogService sysOperLogService;
    private final HrProtocolRepository hrProtocolRepository;
    private final HrBillCompareResultRepository hrBillCompareResultRepository;
    private final HrBillCompareConfigRepository hrBillCompareConfigRepository;
    private final HrClientMapper hrClientMapper;
    private final HrTalentStaffMapper hrTalentStaffMapper;
    private final HrClientRepository hrClientRepository;
    private final HrSocialSecurityService hrSocialSecurityService;
    private final HrNormalSalaryService hrNormalSalaryService;
    private final CodeTableRepository codeTableRepository;
    private final HrSpecialDeductionRepository hrSpecialDeductionRepository;
    private final HrQuickDeductionRepository hrQuickDeductionRepository;

    @Lazy
    @Autowired
    private HrBillDetailItemsService hrBillDetailItemsService;
    @Lazy
    @Autowired
    private HrBillDetailService hrBillDetailService;

    @Override
    public String downloadBillDetail(Map<String, Object> params) {
        if (params.isEmpty()) {
            throw new CommonException("参数不能为空！");
        }
        String billId = String.valueOf(params.get("billId"));
        List<String> billDetailIds = new ArrayList<>();
        if (params.get("billDetailIds") != null) {
            billDetailIds = (List<String>) params.get("billDetailIds");
        }
        List<String> billIds = (List<String>) params.get("billIdList");
        List<String> billIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(billIds)) {
            billIdList = billIds;
        }else {
            if (StringUtils.isBlank(billId) || "null".equals(billId)) {
                throw new CommonException("账单id不能为空！");
            }
            billIdList.add(billId);
        }
        if (CollectionUtils.isEmpty(billIdList)){
            throw new CommonException("账单id不能为空！");
        }
        int listSize;
        String fileUrl;
        // 获取账单详情
        List<HrBillDTO> hrBillDTOList = hrBillRepository.getBillInfoBatch(billIdList);
        // 此时批量下载的时候，传过来的是账单编号，匹配这个单号即可
        HrBillDTO hrBill = hrBillDTOList.stream().filter(lst->lst.getId().equals(billId) || lst.getBillNo().equals(billId)).findFirst().orElse(null);
        if (hrBill == null) {
            throw new CommonException("未查询到相关账单信息！");
        }
        // 账单明细
        List<HrBillDetailDTO> billDetailList = this.hrBillDetailRepository.getBillDetailByIds(billIdList, billDetailIds);
        if (billDetailList.isEmpty()) {
            throw new CommonException("未查询到相关账单明细信息！");
        }
        // 表头
        List<ExcelExportEntity> excelHeader = new ArrayList<>();
        // 数据
        List<Map<String, Object>> paramsList = new ArrayList<>();
        HrSocialSecurityDTO socialSecurityDTO = this.getClientSocialSecurity(Collections.singletonList(hrBill.getClientId()));
        for (HrBillDetailDTO ls : billDetailList) {
            hrBillDTOList.stream().filter(lst -> lst.getId().equals(ls.getBillId())).findFirst().ifPresent(hrBillDTO -> this.dealSalaryBillExportHeader(hrBillDTO, ls, excelHeader, paramsList, socialSecurityDTO));
        }
        // 去重
        List<ExcelExportEntity> collect = excelHeader.stream().distinct().collect(Collectors.toList());
        listSize = paramsList.size();
        String title = "";
        if (billIdList.size() == 1) {
            title = hrBill.getTitle();
        } else {
            title = hrBill.getOptTitle();
        }
        String filePath = ExcelUtils.dynamicColumnExportLocal(title + "明细", null, collect, paramsList, fileTempPath);
        fileUrl = this.hrAppendixService.uploadLocalFile(filePath);
        // 操作日志
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.BILL_DETAIL.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(Collections.singletonList(billId)), listSize, fileUrl);
        return fileUrl;
    }

    @Override
    public String downloadBillBatch(HrBillDTO hrBillDTO, HttpServletResponse response) {
        if (hrBillDTO.getIds() == null) {
            throw new CommonException("请选择账单");
        }
        // 查询客户权限   角色为客户   只能查看自己的
        List<String> clientIdList = hrClientService.selectClientIdByUserId();
        // 账单核算列表 客户、总经理、总裁、监事会主席和董事长 只能查看已经锁定的账单
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String roleKey = jwtUserDTO.getCurrentRoleKey();
        if (roleKey.equals(UserRoleTypeEnum.CLIENT.getKey()) || roleKey.equals(UserRoleTypeEnum.TOTAL_MANAGER.getKey()) || roleKey.equals(UserRoleTypeEnum.CEO.getKey()) || roleKey.equals(UserRoleTypeEnum.SUPERVISORY_BOARD_CHAIRMAN.getKey()) || roleKey.equals(UserRoleTypeEnum.CHAIRMAN.getKey())) {
            hrBillDTO.setBillState(BillEnum.BillState.LOCKED.getKey());
        }
        hrBillDTO.setClientIdList(clientIdList);
        List<File> fileList = new ArrayList<>();
        hrBillDTO.getIds().forEach(id -> {
            // 新批量下载，使用billNo号下载
            List<HrBillDTO> bills = hrBillRepository.findListByBillNo(id);
            if (bills == null || bills.isEmpty()) {
                HrBillDTO hrBill = hrBillRepository.getBillInfoById(id);
                if (hrBill != null){
                    bills = new ArrayList<>();
                    bills.add(hrBill);
                }
            }
            if (bills == null || bills.isEmpty()) {
                throw new CommonException("下载失败，找不到该账单下的数据，请联系开发人员！");
            }
            HrBillDTO hrBill = bills.get(0);
            List<String> billIds = bills.stream().map(HrBillDTO::getId).collect(Collectors.toList());
            HrSocialSecurityDTO socialSecurityDTO = this.getBillSocialSecurity(billIds);
            List<Map<String, Object>> paramsList = new ArrayList<>();
            // 表头
            List<ExcelExportEntity> excelHeader = new ArrayList<>();
            List<HrBillDetailDTO> unrecordedEmployee = hrBillDetailRepository.getListByBillIdBatch(billIds, 0);
            //账单明细
            if (hrBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())){
                List<HrBillDetailDTO> detailList = hrBillDetailRepository.getListByBillIdBatch(billIds, 1);
                hrBill.setBillDetailList(detailList);
            }else {
                List<HrBillDetailDTO> detailList = hrBillDetailRepository.getBillDetailByIds(billIds, null);
                hrBill.setBillDetailList(detailList);
            }
            dealBillDetailExcelBatch(hrBill, socialSecurityDTO, paramsList, excelHeader);
            List<ExcelExportEntity> collect = excelHeader.stream().distinct().collect(Collectors.toList());
            //账单汇总
            HrBillTotalDTO hrBillTotalDTO = hrBillTotalRepository.getBillTotalByBatchBill(billIds);
            this.dealBillTotalExcel(hrBill, hrBillTotalDTO, fileList, socialSecurityDTO);
            //账单明细
            fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicColumnExportLocal(hrBill.getOptTitle() + "_" + BillEnum.BillType.getValueByKey(bills.get(0).getBillType())+"明细", null, collect, paramsList, fileTempPath))));
            // 未入账员工
            fileList.add(new File(Objects.requireNonNull(ExcelUtils.exportLocal(hrBill.getOptTitle() + "_" + "未入账员工", HrBillDetailDTO.class, unrecordedEmployee, fileTempPath))));

        });
        String fileUrl = this.hrAppendixService.zipAndUploadFile(fileList, "账单核算");
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.BILL_BUSINESS_ACCOUNTING.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(hrBillDTO.getIds()), hrBillDTO.getIds().size(), fileUrl);
        return fileUrl;
    }

    private HrSocialSecurityDTO getBillSocialSecurity(List<String> billIds) {
        List<HrBillDTO> hrBillList = hrBillRepository.findSocialConfig(billIds, BillEnum.BillType.SECURITY_BILL.getKey());
        if (hrBillList == null || hrBillList.isEmpty()){
            return null;
        }
        List<String> specialField = hrBillList.stream().filter(lst -> lst.getSpecialField() != null).map(HrBillDTO::getSpecialField).distinct().collect(Collectors.toList());
        List<String> aloneCardinal = hrBillList.stream().filter(lst -> lst.getAloneCardinal() != null).map(HrBillDTO::getAloneCardinal).distinct().collect(Collectors.toList());
        List<String> mergeCardinal = hrBillList.stream().filter(lst -> lst.getMergeCardinal() != null).map(HrBillDTO::getMergeCardinal).distinct().collect(Collectors.toList());
        if (specialField != null && !specialField.isEmpty() && specialField.size() > 1){
            return null;
        }
        if (aloneCardinal != null && !aloneCardinal.isEmpty() && aloneCardinal.size() > 1){
            return null;
        }
        if (mergeCardinal != null && !mergeCardinal.isEmpty() && mergeCardinal.size() > 1){
            return null;
        }
        HrSocialSecurityDTO hrSocialSecurityDTO = new HrSocialSecurityDTO();
        if (specialField != null && !specialField.isEmpty()){
            hrSocialSecurityDTO.setSpecialField(specialField.get(0));
        }
        if (aloneCardinal != null && !aloneCardinal.isEmpty()){
            hrSocialSecurityDTO.setAloneCardinal(aloneCardinal.get(0));
        }
        if (mergeCardinal != null && !mergeCardinal.isEmpty()){
            hrSocialSecurityDTO.setMergeCardinal(mergeCardinal.get(0));
        }
        return hrSocialSecurityDTO;
    }

    /**
     * 处理账单汇总
     *
     * @param hrBill
     * @param fileList
     * @param socialSecurityDTO
     */
    private void dealBillTotalExcel(HrBillDTO hrBill, HrBillTotalDTO hrBillTotal, List<File> fileList, HrSocialSecurityDTO socialSecurityDTO) {
        int billType = hrBill.getBillType();
        // 表头
        List<ExcelExportEntity> excelHeader = new ArrayList<>();
        // 数据
        List<Map<String, Object>> paramsList = new ArrayList<>();

        this.totalBillExportHeader(hrBill, hrBillTotal, excelHeader, paramsList, socialSecurityDTO);
        // 去重
        List<ExcelExportEntity> collect = excelHeader.stream().distinct().collect(Collectors.toList());
        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicColumnExportLocal(hrBill.getOptTitle() + "_" + BillEnum.BillType.getValueByKey(billType)+"汇总", null, collect, paramsList, fileTempPath))));
    }

    /**
     * 处理汇总账单表头数据
     * @param hrBill
     * @param hrBillTotal
     * @param excelHeader
     * @param paramsList
     * @param socialSecurityDTO
     */
    private void totalBillExportHeader(HrBillDTO hrBill, HrBillTotalDTO hrBillTotal, List<ExcelExportEntity> excelHeader, List<Map<String, Object>> paramsList, HrSocialSecurityDTO socialSecurityDTO) {
        Map<String, Object> param = new HashMap<>();
        excelHeader.add(new ExcelExportEntity("人数", "staffNum", 20));
        param.put("staffNum", hrBillTotal.getStaffNum());
        // 保障账单
        if (hrBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())) {
            Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(hrBillTotal), Map.class);
            if (socialSecurityDTO == null){
                ExcelExportEntity workInjuryCardinal = new ExcelExportEntity("单位工伤基数", "workInjuryCardinal");
                workInjuryCardinal.setGroupName("单位基数");
                workInjuryCardinal.setType(10);
                workInjuryCardinal.setOrderNum(8);
                excelHeader.add(workInjuryCardinal);
                param.put("workInjuryCardinal", hrBillTotal.getWorkInjuryCardinal());
                ExcelExportEntity medicalInsuranceCardinal = new ExcelExportEntity("单位医疗基数", "medicalInsuranceCardinal");
                medicalInsuranceCardinal.setGroupName("单位基数");
                medicalInsuranceCardinal.setOrderNum(8);
                medicalInsuranceCardinal.setType(10);
                excelHeader.add(medicalInsuranceCardinal);
                param.put("medicalInsuranceCardinal", hrBillTotal.getMedicalInsuranceCardinal());
                ExcelExportEntity unitMaternityCardinal = new ExcelExportEntity("单位生育基数", "unitMaternityCardinal");
                unitMaternityCardinal.setGroupName("单位基数");
                unitMaternityCardinal.setType(10);
                unitMaternityCardinal.setOrderNum(8);
                excelHeader.add(unitMaternityCardinal);
                param.put("unitMaternityCardinal", hrBillTotal.getUnitMaternityCardinal());
                ExcelExportEntity unitUnemploymentCardinal = new ExcelExportEntity("单位失业基数", "unitUnemploymentCardinal");
                unitUnemploymentCardinal.setGroupName("单位基数");
                unitUnemploymentCardinal.setType(10);
                unitUnemploymentCardinal.setOrderNum(8);
                excelHeader.add(unitUnemploymentCardinal);
                param.put("unitUnemploymentCardinal", hrBillTotal.getUnitUnemploymentCardinal());
                ExcelExportEntity unitPensionCardinal = new ExcelExportEntity("单位养老基数", "unitPensionCardinal");
                unitPensionCardinal.setGroupName("单位基数");
                unitPensionCardinal.setType(10);
                unitPensionCardinal.setOrderNum(8);
                excelHeader.add(unitPensionCardinal);
                param.put("unitPensionCardinal", hrBillTotal.getUnitPensionCardinal());
                ExcelExportEntity personalMaternityCardinal = new ExcelExportEntity("个人生育基数", "personalMaternityCardinal");
                personalMaternityCardinal.setGroupName("个人基数");
                personalMaternityCardinal.setType(10);
                personalMaternityCardinal.setOrderNum(9);
                excelHeader.add(personalMaternityCardinal);
                param.put("personalMaternityCardinal", hrBillTotal.getPersonalMaternityCardinal());
                ExcelExportEntity medicalInsuranceCardinalPersonal = new ExcelExportEntity("个人医疗基数", "medicalInsuranceCardinalPersonal");
                medicalInsuranceCardinalPersonal.setGroupName("个人基数");
                medicalInsuranceCardinalPersonal.setType(10);
                medicalInsuranceCardinalPersonal.setOrderNum(9);
                excelHeader.add(medicalInsuranceCardinalPersonal);
                param.put("medicalInsuranceCardinalPersonal", hrBillTotal.getMedicalInsuranceCardinalPersonal());
                ExcelExportEntity personalUnemploymentCardinal = new ExcelExportEntity("个人失业基数", "personalUnemploymentCardinal");
                personalUnemploymentCardinal.setGroupName("个人基数");
                personalUnemploymentCardinal.setType(10);
                personalUnemploymentCardinal.setOrderNum(9);
                excelHeader.add(personalUnemploymentCardinal);
                param.put("personalUnemploymentCardinal", hrBillTotal.getPersonalUnemploymentCardinal());
                ExcelExportEntity personalPensionCardinal = new ExcelExportEntity("个人养老基数", "personalPensionCardinal");
                personalPensionCardinal.setGroupName("个人基数");
                personalPensionCardinal.setType(10);
                personalPensionCardinal.setOrderNum(9);
                excelHeader.add(personalPensionCardinal);
                param.put("personalPensionCardinal", hrBillTotal.getPersonalPensionCardinal());
            }else {
                List<HrSocialSecurityDTO.HrSocialHeadersDTO> aloneCardinalDTOList = socialSecurityDTO.getAloneCardinalDTOList();
                if (aloneCardinalDTOList != null && !aloneCardinalDTOList.isEmpty()){
                    aloneCardinalDTOList.forEach(ls ->{
                        String valueOf = String.valueOf(map.get(ls.getFieldKey()));
                        ExcelExportEntity temp = new ExcelExportEntity(ls.getFieldName(), ls.getFieldKey(), 20);
                        param.put(ls.getFieldKey(), (StringUtils.isBlank(valueOf) || valueOf.equals("null")) ? BigDecimal.ZERO : new BigDecimal(valueOf));
                        temp.setType(10);
                        if (ls.getGroupName().equals("unit")){
                            temp.setGroupName("单位基数");
                            temp.setOrderNum(8);
                        }else {
                            temp.setGroupName("个人基数");
                            temp.setOrderNum(9);
                        }
                        excelHeader.add(temp);
                    });
                }
                List<HrSocialSecurityDTO.HrSocialHeadersDTO> mergeCardinalDTOList = socialSecurityDTO.getMergeCardinalDTOList();
                if (mergeCardinalDTOList != null && !mergeCardinalDTOList.isEmpty()){
                    mergeCardinalDTOList.forEach(ls ->{
                        final String fieldKey = ls.getFieldKeyList().get(0);
                        String valueOf = String.valueOf(map.get(fieldKey));
                        ExcelExportEntity temp = new ExcelExportEntity(String.join("|",ls.getFieldNameList()), fieldKey, 20);
                        param.put(fieldKey, (StringUtils.isBlank(valueOf) || valueOf.equals("null")) ? BigDecimal.ZERO : new BigDecimal(valueOf));
                        temp.setType(10);
                        if (ls.getGroupName().equals("unit")){
                            temp.setGroupName("单位基数");
                            temp.setOrderNum(8);
                        }else {
                            temp.setGroupName("个人基数");
                            temp.setOrderNum(9);
                        }
                        excelHeader.add(temp);
                    });
                }
            }
            //单位缴纳部分
            ExcelExportEntity temp6 = new ExcelExportEntity("小计", "unitSubtotal");
            temp6.setGroupName("单位缴纳部分");
            temp6.setOrderNum(10);
            temp6.setType(10);
            excelHeader.add(temp6);
            param.put("unitSubtotal", hrBillTotal.getUnitSubtotal());
            ExcelExportEntity unitOtherFee = new ExcelExportEntity("其他", "unitOtherFeeTotal");
            unitOtherFee.setGroupName("单位缴纳部分");
            unitOtherFee.setOrderNum(10);
            unitOtherFee.setType(10);
            excelHeader.add(unitOtherFee);
            param.put("unitOtherFeeTotal", hrBillTotal.getUnitOtherFeeTotal());
            ExcelExportEntity unitLateFee = new ExcelExportEntity("滞纳金", "unitLateFeeTotal");
            unitLateFee.setGroupName("单位缴纳部分");
            unitLateFee.setOrderNum(10);
            unitLateFee.setType(10);
            excelHeader.add(unitLateFee);
            param.put("unitLateFeeTotal", hrBillTotal.getUnitLateFeeTotal());
            if (socialSecurityDTO == null) {
                ExcelExportEntity unitEnterpriseAnnuity = new ExcelExportEntity("企业年金", "unitEnterpriseAnnuityTotal");
                unitEnterpriseAnnuity.setGroupName("单位缴纳部分");
                unitEnterpriseAnnuity.setType(10);
                unitEnterpriseAnnuity.setOrderNum(10);
                excelHeader.add(unitEnterpriseAnnuity);
                param.put("unitEnterpriseAnnuityTotal", hrBillTotal.getUnitEnterpriseAnnuityTotal());
                ExcelExportEntity commercialInsurance = new ExcelExportEntity("商业保险", "commercialInsuranceTotal");
                commercialInsurance.setGroupName("单位缴纳部分");
                commercialInsurance.setType(10);
                commercialInsurance.setOrderNum(10);
                excelHeader.add(commercialInsurance);
                param.put("commercialInsuranceTotal", hrBillTotal.getCommercialInsuranceTotal());
                ExcelExportEntity unitLargeMedicalExpense = new ExcelExportEntity("大额医疗", "unitLargeMedicalExpenseTotal");
                unitLargeMedicalExpense.setGroupName("单位缴纳部分");
                unitLargeMedicalExpense.setType(10);
                unitLargeMedicalExpense.setOrderNum(10);
                excelHeader.add(unitLargeMedicalExpense);
                param.put("unitLargeMedicalExpenseTotal", hrBillTotal.getUnitLargeMedicalExpenseTotal());
                ExcelExportEntity replenishWorkInjuryExpense = new ExcelExportEntity("补充工伤", "replenishWorkInjuryExpenseTotal");
                replenishWorkInjuryExpense.setGroupName("单位缴纳部分");
                replenishWorkInjuryExpense.setType(10);
                replenishWorkInjuryExpense.setOrderNum(10);
                excelHeader.add(replenishWorkInjuryExpense);
                param.put("replenishWorkInjuryExpenseTotal", hrBillTotal.getReplenishWorkInjuryExpenseTotal());
            }else {
                List<HrSocialSecurityDTO.HrSocialHeadersDTO> specialFieldDTOList = socialSecurityDTO.getSpecialFieldDTOList();
                if (specialFieldDTOList != null && !specialFieldDTOList.isEmpty()){
                    List<HrSocialSecurityDTO.HrSocialHeadersDTO> dtoList = specialFieldDTOList.stream().filter(lst -> lst.getGroupName().equals("unit")).collect(Collectors.toList());
                    dtoList.forEach(ls ->{
                        String valueOf = String.valueOf(map.get(ls.getFieldKey()+"Total"));
                        BigDecimal amount = BigDecimal.ZERO;
                        if (StringUtils.isNotBlank(valueOf) && !valueOf.equals("null")){
                            amount = new BigDecimal(valueOf);
                        }
                        ExcelExportEntity temp = new ExcelExportEntity(ls.getFieldName(), ls.getFieldKey(), 20);
                        param.put(ls.getFieldKey(), amount);
                        temp.setType(10);
                        temp.setGroupName("单位缴纳部分");
                        temp.setOrderNum(10);
                        excelHeader.add(temp);
                    });
                }
            }
            ExcelExportEntity temp5 = new ExcelExportEntity("补差", "unitSocialSecurityMakeUpTotal");
            temp5.setGroupName("单位缴纳部分");
            temp5.setOrderNum(10);
            temp5.setType(10);
            excelHeader.add(temp5);
            param.put("unitSocialSecurityMakeUpTotal", hrBillTotal.getUnitSocialSecurityMakeUpTotal());
            ExcelExportEntity unitMaternityScale = new ExcelExportEntity("生育" + CalculateUtils.toPercentage(hrBill.getUnitMaternityScale(), 2), "unitMaternityScaleTotal");
            unitMaternityScale.setGroupName("单位缴纳部分");
            unitMaternityScale.setOrderNum(10);
            unitMaternityScale.setType(10);
            excelHeader.add(unitMaternityScale);
            param.put("unitMaternityScaleTotal", hrBillTotal.getUnitMaternityTotal());
            ExcelExportEntity temp4 = new ExcelExportEntity("工伤" + CalculateUtils.toPercentage(hrBill.getWorkInjuryScale(), 2), "workInjuryTotal");
            temp4.setGroupName("单位缴纳部分");
            temp4.setOrderNum(10);
            temp4.setType(10);
            excelHeader.add(temp4);
            param.put("workInjuryTotal", hrBillTotal.getWorkInjuryTotal());
            ExcelExportEntity temp3 = new ExcelExportEntity("医疗" + CalculateUtils.toPercentage(hrBill.getUnitMedicalScale(), 2), "unitMedicalTotal");
            temp3.setGroupName("单位缴纳部分");
            temp3.setOrderNum(10);
            temp3.setType(10);
            excelHeader.add(temp3);
            param.put("unitMedicalTotal", hrBillTotal.getUnitMedicalTotal());
            ExcelExportEntity temp2 = new ExcelExportEntity("失业" + CalculateUtils.toPercentage(hrBill.getUnitUnemploymentScale(), 2), "unitUnemploymentTotal");
            temp2.setGroupName("单位缴纳部分");
            temp2.setOrderNum(10);
            temp2.setType(10);
            excelHeader.add(temp2);
            param.put("unitUnemploymentTotal", hrBillTotal.getUnitUnemploymentTotal());
            ExcelExportEntity temp1 = new ExcelExportEntity("养老" + CalculateUtils.toPercentage(hrBill.getUnitPensionScale(), 2), "unitPensionTotal");
            temp1.setGroupName("单位缴纳部分");
            temp1.setOrderNum(10);
            temp1.setType(10);
            excelHeader.add(temp1);
            param.put("unitPensionTotal", hrBillTotal.getUnitPensionTotal());
            //个人缴纳部分
            ExcelExportEntity temp11 = new ExcelExportEntity("小计", "personalSubtotal");
            temp11.setGroupName("个人缴纳部分");
            temp11.setOrderNum(11);
            temp11.setType(10);
            excelHeader.add(temp11);
            param.put("personalSubtotal", hrBillTotal.getPersonalSubtotal());
            ExcelExportEntity personalOtherFee = new ExcelExportEntity("其他", "personalOtherFeeTotal");
            personalOtherFee.setGroupName("个人缴纳部分");
            personalOtherFee.setOrderNum(11);
            personalOtherFee.setType(10);
            excelHeader.add(personalOtherFee);
            param.put("personalOtherFeeTotal", hrBillTotal.getPersonalOtherFeeTotal());
            if (socialSecurityDTO == null) {
                ExcelExportEntity personalEnterpriseAnnuity = new ExcelExportEntity("企业年金", "personalEnterpriseAnnuityTotal");
                personalEnterpriseAnnuity.setGroupName("个人缴纳部分");
                personalEnterpriseAnnuity.setType(10);
                personalEnterpriseAnnuity.setOrderNum(11);
                excelHeader.add(personalEnterpriseAnnuity);
                param.put("personalEnterpriseAnnuityTotal", hrBillTotal.getPersonalEnterpriseAnnuityTotal());
                ExcelExportEntity personalLargeMedicalExpense = new ExcelExportEntity("大额医疗", "personalLargeMedicalExpenseTotal");
                personalLargeMedicalExpense.setGroupName("个人缴纳部分");
                personalLargeMedicalExpense.setType(10);
                personalLargeMedicalExpense.setOrderNum(11);
                excelHeader.add(personalLargeMedicalExpense);
                param.put("personalLargeMedicalExpenseTotal", hrBillTotal.getPersonalLargeMedicalExpenseTotal());
            }else {
                List<HrSocialSecurityDTO.HrSocialHeadersDTO> specialFieldDTOList = socialSecurityDTO.getSpecialFieldDTOList();
                if (specialFieldDTOList != null && !specialFieldDTOList.isEmpty()){
                    List<HrSocialSecurityDTO.HrSocialHeadersDTO> dtoList = specialFieldDTOList.stream().filter(lst -> lst.getGroupName().equals("personal")).collect(Collectors.toList());
                    dtoList.forEach(ls ->{
                        String valueOf = String.valueOf(map.get(ls.getFieldKey()+"Total"));
                        BigDecimal amount = BigDecimal.ZERO;
                        if (StringUtils.isNotBlank(valueOf) && !valueOf.equals("null")){
                            amount = new BigDecimal(valueOf);
                        }
                        ExcelExportEntity temp = new ExcelExportEntity(ls.getFieldName(), ls.getFieldKey(), 20);
                        param.put(ls.getFieldKey(), amount);
                        temp.setType(10);
                        temp.setGroupName("个人缴纳部分");
                        temp.setOrderNum(11);
                        excelHeader.add(temp);
                    });
                }
            }
            ExcelExportEntity temp10 = new ExcelExportEntity("补差", "personalSocialSecurityMakeUpTotal");
            temp10.setGroupName("个人缴纳部分");
            temp10.setOrderNum(11);
            temp10.setType(10);
            excelHeader.add(temp10);
            param.put("personalSocialSecurityMakeUpTotal", hrBillTotal.getPersonalSocialSecurityMakeUpTotal());
            ExcelExportEntity personalMaternity = new ExcelExportEntity("生育" + CalculateUtils.toPercentage(hrBill.getPersonalMaternityScale(), 2), "personalMaternityTotal");
            personalMaternity.setGroupName("个人缴纳部分");
            personalMaternity.setOrderNum(11);
            personalMaternity.setType(10);
            excelHeader.add(personalMaternity);
            param.put("personalMaternityTotal", hrBillTotal.getPersonalMaternityTotal());
            ExcelExportEntity temp9 = new ExcelExportEntity("医疗" + CalculateUtils.toPercentage(hrBill.getPersonalMedicalScale(), 2), "personalMedicalTotal");
            temp9.setGroupName("个人缴纳部分");
            temp9.setOrderNum(11);
            temp9.setType(10);
            excelHeader.add(temp9);
            param.put("personalMedicalTotal", hrBillTotal.getPersonalMedicalTotal());
            ExcelExportEntity temp8 = new ExcelExportEntity("失业" + CalculateUtils.toPercentage(hrBill.getPersonalUnemploymentScale(), 2), "personalUnemploymentTotal");
            temp8.setGroupName("个人缴纳部分");
            temp8.setOrderNum(11);
            temp8.setType(10);
            excelHeader.add(temp8);
            param.put("personalUnemploymentTotal", hrBillTotal.getPersonalUnemploymentTotal());
            ExcelExportEntity temp7 = new ExcelExportEntity("养老" + CalculateUtils.toPercentage(hrBill.getPersonalPensionScale(), 2), "personalPensionTotal");
            temp7.setGroupName("个人缴纳部分");
            temp7.setOrderNum(11);
            temp7.setType(10);
            excelHeader.add(temp7);
            param.put("personalPensionTotal", hrBillTotal.getPersonalPensionTotal());

            ExcelExportEntity socialSecurityTotal = new ExcelExportEntity("社保总金额", "socialSecurityTotal", 15);
            socialSecurityTotal.setType(10);
            socialSecurityTotal.setOrderNum(12);
            excelHeader.add(socialSecurityTotal);
            param.put("socialSecurityTotal", hrBillTotal.getSocialSecurityTotal());
            ExcelExportEntity accumulationFundCardinal = new ExcelExportEntity("公积金基数", "accumulationFundCardinal");
            accumulationFundCardinal.setType(10);
            accumulationFundCardinal.setOrderNum(13);
            excelHeader.add(accumulationFundCardinal);
            param.put("accumulationFundCardinal", hrBillTotal.getAccumulationFundCardinal());

            // 住房公积金
            ExcelExportEntity temp13 = new ExcelExportEntity("单位补差", "unitAccumulationFundMakeUpTotal");
            temp13.setGroupName("公积金单位缴纳部分");
            temp13.setOrderNum(14);
            temp13.setType(10);
            temp13.setNumFormat("0");
            excelHeader.add(temp13);
            param.put("unitAccumulationFundMakeUpTotal", hrBillTotal.getUnitAccumulationFundMakeUpTotal());
            ExcelExportEntity temp12 = new ExcelExportEntity("单位" + CalculateUtils.toPercentage(hrBill.getUnitAccumulationFundScale(), 2), "unitAccumulationFundTotal");
            temp12.setGroupName("公积金单位缴纳部分");
            temp12.setType(10);
            temp12.setNumFormat("0");
            temp12.setOrderNum(14);
            excelHeader.add(temp12);
            param.put("unitAccumulationFundTotal", hrBillTotal.getUnitAccumulationFundTotal());
            ExcelExportEntity temp15 = new ExcelExportEntity("个人补差", "personalAccumulationFundMakeUpTotal");
            temp15.setGroupName("公积金个人缴纳部分");
            temp15.setOrderNum(15);
            temp15.setType(10);
            temp15.setNumFormat("0");
            excelHeader.add(temp15);
            param.put("personalAccumulationFundMakeUpTotal", hrBillTotal.getPersonalAccumulationFundMakeUpTotal());
            ExcelExportEntity temp14 = new ExcelExportEntity("个人" + CalculateUtils.toPercentage(hrBill.getPersonalAccumulationFundScale(), 2), "personalAccumulationFundTotal");
            temp14.setGroupName("公积金个人缴纳部分");
            temp14.setOrderNum(15);
            temp14.setType(10);
            temp14.setNumFormat("0");
            excelHeader.add(temp14);
            param.put("personalAccumulationFundTotal", hrBillTotal.getPersonalAccumulationFundTotal());

            ExcelExportEntity temp16 = new ExcelExportEntity("个人", "personalAccumulationFundMakeUpTotal");
            temp16.setGroupName("公积金补差合计");
            temp16.setOrderNum(16);
            temp16.setType(10);
            temp16.setNumFormat("0");
            excelHeader.add(temp16);
            param.put("personalAccumulationFundMakeUpTotal", hrBillTotal.getPersonalAccumulationFundMakeUpTotal());
            ExcelExportEntity temp17 = new ExcelExportEntity("单位", "unitAccumulationFundMakeUpTotal");
            temp17.setGroupName("公积金补差合计");
            temp17.setOrderNum(16);
            temp17.setType(10);
            temp17.setNumFormat("0");
            excelHeader.add(temp17);
            param.put("unitAccumulationFundMakeUpTotal", hrBillTotal.getUnitAccumulationFundMakeUpTotal());

            ExcelExportEntity accumulationFundTotal = new ExcelExportEntity("公积金总金额", "accumulationFundTotal", 15);
            accumulationFundTotal.setType(10);
            accumulationFundTotal.setNumFormat("0");
            accumulationFundTotal.setOrderNum(17);
            excelHeader.add(accumulationFundTotal);
            param.put("accumulationFundTotal", hrBillTotal.getAccumulationFundTotal());
            ExcelExportEntity total = new ExcelExportEntity("总金额", "total",20);
            total.setType(10);
            total.setOrderNum(18);
            excelHeader.add(total);
            param.put("total", hrBillTotal.getTotal());
        }
        // 薪酬账单
        else if (hrBill.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())){
            ExcelExportEntity temp1 = new ExcelExportEntity("个税", "personalTaxTotal",20);
            temp1.setType(10);
            temp1.setGroupName("代发工资");
            temp1.setOrderNum(2);
            excelHeader.add(temp1);
            param.put("personalTaxTotal", hrBillTotal.getPersonalTaxTotal());
            ExcelExportEntity temp2 = new ExcelExportEntity("个税补差", "personalTaxMakeUpTotal",20);
            temp2.setType(10);
            temp2.setOrderNum(3);
            temp2.setGroupName("代发工资");
            excelHeader.add(temp2);
            param.put("personalTaxMakeUpTotal", hrBillTotal.getPersonalTaxMakeUpTotal());
            ExcelExportEntity temp3 = new ExcelExportEntity("实发", "realSalaryTotal",20);
            temp3.setType(10);
            temp3.setGroupName("代发工资");
            temp3.setOrderNum(4);
            excelHeader.add(temp3);
            param.put("realSalaryTotal", hrBillTotal.getRealSalaryTotal());
            ExcelExportEntity temp4 = new ExcelExportEntity("其他费用", "otherFeeTotal",20);
            temp4.setType(10);
            temp4.setOrderNum(5);
            excelHeader.add(temp4);
            param.put("otherFeeTotal", hrBillTotal.getOtherFeeTotal());
            ExcelExportEntity temp5 = new ExcelExportEntity("上月补差", "lastMonthMakeUp",20);
            temp5.setType(10);
            temp5.setOrderNum(6);
            excelHeader.add(temp5);
            param.put("lastMonthMakeUp", hrBillTotal.getLastMonthMakeUp());
            ExcelExportEntity total = new ExcelExportEntity("总金额", "total",20);
            total.setType(10);
            total.setOrderNum(7);
            excelHeader.add(total);
            param.put("total", hrBillTotal.getTotal());
        }
        //其他账单
        else {
            ExcelExportEntity total = new ExcelExportEntity("总金额", "total",20);
            total.setType(10);
            total.setOrderNum(3);
            excelHeader.add(total);
            param.put("total", hrBillTotal.getTotal());
        }
        paramsList.add(param);


    }

    /**
     * 处理账单明细
     *
     * @return void
     * <AUTHOR>
     * @date 2022/1/25
     **/
    private void dealBillDetailExcel(HrBillDTO hrBill, List<File> fileList, HrSocialSecurityDTO socialSecurityDTO) {
        int billType = hrBill.getBillType();
        // 账单明细
        List<HrBillDetailDTO> billDetailList = hrBill.getBillDetailList();
        // 表头
        List<ExcelExportEntity> excelHeader = new ArrayList<>();
        // 数据
        List<Map<String, Object>> paramsList = new ArrayList<>();

        for (HrBillDetailDTO ls : billDetailList) {
            this.dealSalaryBillExportHeader(hrBill, ls, excelHeader, paramsList, socialSecurityDTO);
        }
        // 去重
        List<ExcelExportEntity> collect = excelHeader.stream().distinct().collect(Collectors.toList());
        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicColumnExportLocal(hrBill.getClientName() + BillEnum.BillType.getValueByKey(billType)+"明细", null, collect, paramsList, fileTempPath))));
    }

    /**
     * 处理账单明细
     *
     * @return void
     * <AUTHOR>
     * @date 2022/1/25
     **/
    private void dealBillDetailExcelBatch(HrBillDTO hrBill, HrSocialSecurityDTO socialSecurityDTO, List<Map<String, Object>> paramsList, List<ExcelExportEntity> excelHeader) {
        // 账单明细
        List<HrBillDetailDTO> billDetailList = hrBill.getBillDetailList();

        for (HrBillDetailDTO ls : billDetailList) {
            this.dealSalaryBillExportHeader(hrBill, ls, excelHeader, paramsList, socialSecurityDTO);
        }
    }

    /**
     * 处理薪酬账单动态表头数据
     *  @param hrBill
     * @param hrBillDetail
     * @param excelHeader
     * @param paramsList
     * @param socialSecurityDTO
     */
    private void dealSalaryBillExportHeader(HrBillDTO hrBill, HrBillDetailDTO hrBillDetail, List<ExcelExportEntity> excelHeader, List<Map<String, Object>> paramsList, HrSocialSecurityDTO socialSecurityDTO) {
        Map<String, Object> param = new HashMap<>();
        // 保障账单
        if (hrBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())) {
            Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(hrBillDetail), Map.class);
            excelHeader.add(new ExcelExportEntity("姓名", "name", 20));
            param.put("name", hrBillDetail.getName());
            excelHeader.add(new ExcelExportEntity("身份证号", "certificateNum", 30));
            param.put("certificateNum", hrBillDetail.getCertificateNum());
            excelHeader.add(new ExcelExportEntity("社保地区", "socialSecurityArea", 30));
            param.put("socialSecurityArea", hrBillDetail.getSocialSecurityArea());
            excelHeader.add(new ExcelExportEntity("员工状态", "staffStatus", 20));
            param.put("staffStatus", hrBillDetail.getStaffStatusStr());
            excelHeader.add(new ExcelExportEntity("个人社保编号", "socialSecurityNum", 30));
            param.put("socialSecurityNum", hrBillDetail.getSocialSecurityNum());
            excelHeader.add(new ExcelExportEntity("个人医保编号", "medicalInsuranceNum", 30));
            param.put("medicalInsuranceNum", hrBillDetail.getMedicalInsuranceNum());
            excelHeader.add(new ExcelExportEntity("个人公积金编号", "accumulationFundNum", 30));
            param.put("accumulationFundNum", hrBillDetail.getAccumulationFundNum());
            if (socialSecurityDTO == null){
                ExcelExportEntity workInjuryCardinal = new ExcelExportEntity("单位工伤基数", "workInjuryCardinal");
                workInjuryCardinal.setGroupName("单位基数");
                workInjuryCardinal.setType(10);
                workInjuryCardinal.setOrderNum(8);
                excelHeader.add(workInjuryCardinal);
                param.put("workInjuryCardinal", hrBillDetail.getWorkInjuryCardinal());
                ExcelExportEntity unitMaternityCardinal = new ExcelExportEntity("单位生育基数", "unitMaternityCardinal");
                unitMaternityCardinal.setGroupName("单位基数");
                unitMaternityCardinal.setType(10);
                unitMaternityCardinal.setOrderNum(8);
                excelHeader.add(unitMaternityCardinal);
                param.put("unitMaternityCardinal", hrBillDetail.getUnitMaternityCardinal());
                ExcelExportEntity medicalInsuranceCardinal = new ExcelExportEntity("单位医疗基数", "medicalInsuranceCardinal");
                medicalInsuranceCardinal.setGroupName("单位基数");
                medicalInsuranceCardinal.setOrderNum(8);
                medicalInsuranceCardinal.setType(10);
                excelHeader.add(medicalInsuranceCardinal);
                param.put("medicalInsuranceCardinal", hrBillDetail.getMedicalInsuranceCardinal());
                ExcelExportEntity unitUnemploymentCardinal = new ExcelExportEntity("单位失业基数", "unitUnemploymentCardinal");
                unitUnemploymentCardinal.setGroupName("单位基数");
                unitUnemploymentCardinal.setType(10);
                unitUnemploymentCardinal.setOrderNum(8);
                excelHeader.add(unitUnemploymentCardinal);
                param.put("unitUnemploymentCardinal", hrBillDetail.getUnitUnemploymentCardinal());
                ExcelExportEntity unitPensionCardinal = new ExcelExportEntity("单位养老基数", "unitPensionCardinal");
                unitPensionCardinal.setGroupName("单位基数");
                unitPensionCardinal.setType(10);
                unitPensionCardinal.setOrderNum(8);
                excelHeader.add(unitPensionCardinal);
                param.put("unitPensionCardinal", hrBillDetail.getUnitPensionCardinal());
                ExcelExportEntity personalMaternityCardinal = new ExcelExportEntity("个人生育基数", "personalMaternityCardinal");
                personalMaternityCardinal.setGroupName("个人基数");
                personalMaternityCardinal.setType(10);
                personalMaternityCardinal.setOrderNum(9);
                excelHeader.add(personalMaternityCardinal);
                param.put("personalMaternityCardinal", hrBillDetail.getPersonalMaternityCardinal());
                ExcelExportEntity medicalInsuranceCardinalPersonal = new ExcelExportEntity("个人医疗基数", "medicalInsuranceCardinalPersonal");
                medicalInsuranceCardinalPersonal.setGroupName("个人基数");
                medicalInsuranceCardinalPersonal.setType(10);
                medicalInsuranceCardinalPersonal.setOrderNum(9);
                excelHeader.add(medicalInsuranceCardinalPersonal);
                param.put("medicalInsuranceCardinalPersonal", hrBillDetail.getMedicalInsuranceCardinalPersonal());
                ExcelExportEntity personalUnemploymentCardinal = new ExcelExportEntity("个人失业基数", "personalUnemploymentCardinal");
                personalUnemploymentCardinal.setGroupName("个人基数");
                personalUnemploymentCardinal.setType(10);
                personalUnemploymentCardinal.setOrderNum(9);
                excelHeader.add(personalUnemploymentCardinal);
                param.put("personalUnemploymentCardinal", hrBillDetail.getPersonalUnemploymentCardinal());
                ExcelExportEntity personalPensionCardinal = new ExcelExportEntity("个人养老基数", "personalPensionCardinal");
                personalPensionCardinal.setGroupName("个人基数");
                personalPensionCardinal.setType(10);
                personalPensionCardinal.setOrderNum(9);
                excelHeader.add(personalPensionCardinal);
                param.put("personalPensionCardinal", hrBillDetail.getPersonalPensionCardinal());
            }else {
                List<HrSocialSecurityDTO.HrSocialHeadersDTO> aloneCardinalDTOList = socialSecurityDTO.getAloneCardinalDTOList();
                if (aloneCardinalDTOList != null && !aloneCardinalDTOList.isEmpty()){
                    aloneCardinalDTOList.forEach(ls ->{
                        String valueOf = String.valueOf(map.get(ls.getFieldKey()));
                        ExcelExportEntity temp = new ExcelExportEntity(ls.getFieldName(), ls.getFieldKey(), 20);
                        param.put(ls.getFieldKey(), (StringUtils.isBlank(valueOf) || valueOf.equals("null")) ? BigDecimal.ZERO : new BigDecimal(valueOf));
                        temp.setType(10);
                        if (ls.getGroupName().equals("unit")){
                            temp.setGroupName("单位基数");
                            temp.setOrderNum(8);
                        }else {
                            temp.setGroupName("个人基数");
                            temp.setOrderNum(9);
                        }
                        excelHeader.add(temp);
                    });
                }
                List<HrSocialSecurityDTO.HrSocialHeadersDTO> mergeCardinalDTOList = socialSecurityDTO.getMergeCardinalDTOList();
                if (mergeCardinalDTOList != null && !mergeCardinalDTOList.isEmpty()){
                    mergeCardinalDTOList.forEach(ls ->{
                        final String fieldKey = ls.getFieldKeyList().get(0);
                        String valueOf = String.valueOf(map.get(fieldKey));
                        ExcelExportEntity temp = new ExcelExportEntity(String.join("|",ls.getFieldNameList()), fieldKey, 20);
                        param.put(fieldKey, (StringUtils.isBlank(valueOf) || valueOf.equals("null")) ? BigDecimal.ZERO : new BigDecimal(valueOf));
                        temp.setType(10);
                        if (ls.getGroupName().equals("unit")){
                            temp.setGroupName("单位基数");
                            temp.setOrderNum(8);
                        }else {
                            temp.setGroupName("个人基数");
                            temp.setOrderNum(9);
                        }
                        excelHeader.add(temp);
                    });
                }
            }
            //单位缴纳部分
            ExcelExportEntity temp6 = new ExcelExportEntity("小计", "unitSubtotal");
            temp6.setGroupName("单位缴纳部分");
            temp6.setOrderNum(10);
            temp6.setType(10);
            excelHeader.add(temp6);
            param.put("unitSubtotal", hrBillDetail.getUnitSubtotal());
            ExcelExportEntity unitOtherFee = new ExcelExportEntity("其他", "unitOtherFee");
            unitOtherFee.setGroupName("单位缴纳部分");
            unitOtherFee.setOrderNum(10);
            unitOtherFee.setType(10);
            excelHeader.add(unitOtherFee);
            param.put("unitOtherFee", hrBillDetail.getUnitOtherFee());
            ExcelExportEntity unitLateFee = new ExcelExportEntity("滞纳金", "unitLateFee");
            unitLateFee.setGroupName("单位缴纳部分");
            unitLateFee.setOrderNum(10);
            unitLateFee.setType(10);
            excelHeader.add(unitLateFee);
            param.put("unitLateFee", hrBillDetail.getUnitLateFee());
            if (socialSecurityDTO == null) {
                ExcelExportEntity unitEnterpriseAnnuity = new ExcelExportEntity("企业年金", "unitEnterpriseAnnuity");
                unitEnterpriseAnnuity.setGroupName("单位缴纳部分");
                unitEnterpriseAnnuity.setType(10);
                unitEnterpriseAnnuity.setOrderNum(10);
                excelHeader.add(unitEnterpriseAnnuity);
                param.put("unitEnterpriseAnnuity", hrBillDetail.getUnitEnterpriseAnnuity());
                ExcelExportEntity commercialInsurance = new ExcelExportEntity("商业保险", "commercialInsurance");
                commercialInsurance.setGroupName("单位缴纳部分");
                commercialInsurance.setType(10);
                commercialInsurance.setOrderNum(10);
                excelHeader.add(commercialInsurance);
                param.put("commercialInsurance", hrBillDetail.getCommercialInsurance());
                ExcelExportEntity unitLargeMedicalExpense = new ExcelExportEntity("大额医疗", "unitLargeMedicalExpense");
                unitLargeMedicalExpense.setGroupName("单位缴纳部分");
                unitLargeMedicalExpense.setType(10);
                unitLargeMedicalExpense.setOrderNum(10);
                excelHeader.add(unitLargeMedicalExpense);
                param.put("unitLargeMedicalExpense", hrBillDetail.getUnitLargeMedicalExpense());
                ExcelExportEntity replenishWorkInjuryExpense = new ExcelExportEntity("补充工伤", "replenishWorkInjuryExpense");
                replenishWorkInjuryExpense.setGroupName("单位缴纳部分");
                replenishWorkInjuryExpense.setType(10);
                replenishWorkInjuryExpense.setOrderNum(10);
                excelHeader.add(replenishWorkInjuryExpense);
                param.put("replenishWorkInjuryExpense", hrBillDetail.getReplenishWorkInjuryExpense());
            }else {
                List<HrSocialSecurityDTO.HrSocialHeadersDTO> specialFieldDTOList = socialSecurityDTO.getSpecialFieldDTOList();
                if (specialFieldDTOList != null && !specialFieldDTOList.isEmpty()){
                    List<HrSocialSecurityDTO.HrSocialHeadersDTO> dtoList = specialFieldDTOList.stream().filter(lst -> lst.getGroupName().equals("unit")).collect(Collectors.toList());
                    dtoList.forEach(ls ->{
                        String valueOf = String.valueOf(map.get(ls.getFieldKey()));
                        BigDecimal amount = BigDecimal.ZERO;
                        if (StringUtils.isNotBlank(valueOf) && !valueOf.equals("null")){
                            amount = new BigDecimal(valueOf);
                        }
                        ExcelExportEntity temp = new ExcelExportEntity(ls.getFieldName(), ls.getFieldKey(), 20);
                        param.put(ls.getFieldKey(), amount);
                        temp.setType(10);
                        temp.setGroupName("单位缴纳部分");
                        temp.setOrderNum(10);
                        excelHeader.add(temp);
                    });
                }
            }
            ExcelExportEntity temp5 = new ExcelExportEntity("补差", "unitSocialSecurityMakeUp");
            temp5.setGroupName("单位缴纳部分");
            temp5.setOrderNum(10);
            temp5.setType(10);
            excelHeader.add(temp5);
            param.put("unitSocialSecurityMakeUp", hrBillDetail.getUnitSocialSecurityMakeUp());
            ExcelExportEntity temp4 = new ExcelExportEntity("工伤" + CalculateUtils.toPercentage(hrBill.getWorkInjuryScale(), 2), "workInjury");
            temp4.setGroupName("单位缴纳部分");
            temp4.setOrderNum(10);
            temp4.setType(10);
            excelHeader.add(temp4);
            param.put("workInjury", hrBillDetail.getWorkInjury());
            ExcelExportEntity unitMaternityScale = new ExcelExportEntity("生育" + CalculateUtils.toPercentage(hrBill.getUnitMaternityScale(), 2), "unitMaternityScale");
            unitMaternityScale.setGroupName("单位缴纳部分");
            unitMaternityScale.setOrderNum(10);
            unitMaternityScale.setType(10);
            excelHeader.add(unitMaternityScale);
            param.put("unitMaternityScale", hrBillDetail.getUnitMaternity());
            ExcelExportEntity temp3 = new ExcelExportEntity("医疗" + CalculateUtils.toPercentage(hrBill.getUnitMedicalScale(), 2), "unitMedical");
            temp3.setGroupName("单位缴纳部分");
            temp3.setOrderNum(10);
            temp3.setType(10);
            excelHeader.add(temp3);
            param.put("unitMedical", hrBillDetail.getUnitMedical());
            ExcelExportEntity temp2 = new ExcelExportEntity("失业" + CalculateUtils.toPercentage(hrBill.getUnitUnemploymentScale(), 2), "unitUnemployment");
            temp2.setGroupName("单位缴纳部分");
            temp2.setOrderNum(10);
            temp2.setType(10);
            excelHeader.add(temp2);
            param.put("unitUnemployment", hrBillDetail.getUnitUnemployment());
            ExcelExportEntity temp1 = new ExcelExportEntity("养老" + CalculateUtils.toPercentage(hrBill.getUnitPensionScale(), 2), "unitPension");
            temp1.setGroupName("单位缴纳部分");
            temp1.setOrderNum(10);
            temp1.setType(10);
            excelHeader.add(temp1);
            param.put("unitPension", hrBillDetail.getUnitPension());
            //个人缴纳部分
            ExcelExportEntity temp11 = new ExcelExportEntity("小计", "personalSubtotal");
            temp11.setGroupName("个人缴纳部分");
            temp11.setOrderNum(11);
            temp11.setType(10);
            excelHeader.add(temp11);
            param.put("personalSubtotal", hrBillDetail.getPersonalSubtotal());
            ExcelExportEntity personalOtherFee = new ExcelExportEntity("其他", "personalOtherFee");
            personalOtherFee.setGroupName("个人缴纳部分");
            personalOtherFee.setOrderNum(11);
            personalOtherFee.setType(10);
            excelHeader.add(personalOtherFee);
            param.put("personalOtherFee", hrBillDetail.getPersonalOtherFee());
            if (socialSecurityDTO == null) {
                ExcelExportEntity personalEnterpriseAnnuity = new ExcelExportEntity("企业年金", "personalEnterpriseAnnuity");
                personalEnterpriseAnnuity.setGroupName("个人缴纳部分");
                personalEnterpriseAnnuity.setType(10);
                personalEnterpriseAnnuity.setOrderNum(11);
                excelHeader.add(personalEnterpriseAnnuity);
                param.put("personalEnterpriseAnnuity", hrBillDetail.getPersonalEnterpriseAnnuity());
                ExcelExportEntity personalLargeMedicalExpense = new ExcelExportEntity("大额医疗", "personalLargeMedicalExpense");
                personalLargeMedicalExpense.setGroupName("个人缴纳部分");
                personalLargeMedicalExpense.setType(10);
                personalLargeMedicalExpense.setOrderNum(11);
                excelHeader.add(personalLargeMedicalExpense);
                param.put("personalLargeMedicalExpense", hrBillDetail.getPersonalLargeMedicalExpense());
            }else {
                List<HrSocialSecurityDTO.HrSocialHeadersDTO> specialFieldDTOList = socialSecurityDTO.getSpecialFieldDTOList();
                if (specialFieldDTOList != null && !specialFieldDTOList.isEmpty()){
                    List<HrSocialSecurityDTO.HrSocialHeadersDTO> dtoList = specialFieldDTOList.stream().filter(lst -> lst.getGroupName().equals("personal")).collect(Collectors.toList());
                    dtoList.forEach(ls ->{
                        String valueOf = String.valueOf(map.get(ls.getFieldKey()));
                        BigDecimal amount = BigDecimal.ZERO;
                        if (StringUtils.isNotBlank(valueOf) && !valueOf.equals("null")){
                            amount = new BigDecimal(valueOf);
                        }
                        ExcelExportEntity temp = new ExcelExportEntity(ls.getFieldName(), ls.getFieldKey(), 20);
                        param.put(ls.getFieldKey(), amount);
                        temp.setType(10);
                        temp.setGroupName("个人缴纳部分");
                        temp.setOrderNum(11);
                        excelHeader.add(temp);
                    });
                }
            }
            ExcelExportEntity temp10 = new ExcelExportEntity("补差", "personalSocialSecurityMakeUp");
            temp10.setGroupName("个人缴纳部分");
            temp10.setOrderNum(11);
            temp10.setType(10);
            excelHeader.add(temp10);
            param.put("personalSocialSecurityMakeUp", hrBillDetail.getPersonalSocialSecurityMakeUp());
            ExcelExportEntity personalMaternity = new ExcelExportEntity("生育" + CalculateUtils.toPercentage(hrBill.getPersonalMaternityScale(), 2), "personalMaternity");
            personalMaternity.setGroupName("个人缴纳部分");
            personalMaternity.setOrderNum(11);
            personalMaternity.setType(10);
            excelHeader.add(personalMaternity);
            param.put("personalMaternity", hrBillDetail.getPersonalMaternity());
            ExcelExportEntity temp9 = new ExcelExportEntity("医疗" + CalculateUtils.toPercentage(hrBill.getPersonalMedicalScale(), 2), "personalMedical");
            temp9.setGroupName("个人缴纳部分");
            temp9.setOrderNum(11);
            temp9.setType(10);
            excelHeader.add(temp9);
            param.put("personalMedical", hrBillDetail.getPersonalMedical());
            ExcelExportEntity temp8 = new ExcelExportEntity("失业" + CalculateUtils.toPercentage(hrBill.getPersonalUnemploymentScale(), 2), "personalUnemployment");
            temp8.setGroupName("个人缴纳部分");
            temp8.setOrderNum(11);
            temp8.setType(10);
            excelHeader.add(temp8);
            param.put("personalUnemployment", hrBillDetail.getPersonalUnemployment());
            ExcelExportEntity temp7 = new ExcelExportEntity("养老" + CalculateUtils.toPercentage(hrBill.getPersonalPensionScale(), 2), "personalPension");
            temp7.setGroupName("个人缴纳部分");
            temp7.setOrderNum(11);
            temp7.setType(10);
            excelHeader.add(temp7);
            param.put("personalPension", hrBillDetail.getPersonalPension());

            ExcelExportEntity socialSecurityTotal = new ExcelExportEntity("社保总金额", "socialSecurityTotal", 15);
            socialSecurityTotal.setType(10);
            socialSecurityTotal.setOrderNum(12);
            excelHeader.add(socialSecurityTotal);
            param.put("socialSecurityTotal", hrBillDetail.getSocialSecurityTotal());
            ExcelExportEntity accumulationFundCardinal = new ExcelExportEntity("公积金基数", "accumulationFundCardinal");
            accumulationFundCardinal.setType(10);
            accumulationFundCardinal.setOrderNum(13);
            excelHeader.add(accumulationFundCardinal);
            param.put("accumulationFundCardinal", hrBillDetail.getAccumulationFundCardinal());

            // 住房公积金
            ExcelExportEntity temp13 = new ExcelExportEntity("单位补差", "unitAccumulationFundMakeUp");
            temp13.setGroupName("公积金单位缴纳部分");
            temp13.setOrderNum(14);
            temp13.setType(10);
            temp13.setNumFormat("0");
            excelHeader.add(temp13);
            param.put("unitAccumulationFundMakeUp", hrBillDetail.getUnitAccumulationFundMakeUp());
            ExcelExportEntity temp12 = new ExcelExportEntity("单位" + CalculateUtils.toPercentage(hrBill.getUnitAccumulationFundScale(), 2), "unitAccumulationFund");
            temp12.setGroupName("公积金单位缴纳部分");
            temp12.setType(10);
            temp12.setNumFormat("0");
            temp12.setOrderNum(14);
            excelHeader.add(temp12);
            param.put("unitAccumulationFund", hrBillDetail.getUnitAccumulationFund());
            ExcelExportEntity temp15 = new ExcelExportEntity("个人补差", "personalAccumulationFundMakeUp");
            temp15.setGroupName("公积金个人缴纳部分");
            temp15.setOrderNum(15);
            temp15.setType(10);
            temp15.setNumFormat("0");
            excelHeader.add(temp15);
            param.put("personalAccumulationFundMakeUp", hrBillDetail.getPersonalAccumulationFundMakeUp());
            ExcelExportEntity temp14 = new ExcelExportEntity("个人" + CalculateUtils.toPercentage(hrBill.getPersonalAccumulationFundScale(), 2), "personalAccumulationFund");
            temp14.setGroupName("公积金个人缴纳部分");
            temp14.setOrderNum(15);
            temp14.setType(10);
            temp14.setNumFormat("0");
            excelHeader.add(temp14);
            param.put("personalAccumulationFund", hrBillDetail.getPersonalAccumulationFund());
            ExcelExportEntity accumulationFundTotal = new ExcelExportEntity("公积金总金额", "accumulationFundTotal", 15);
            accumulationFundTotal.setType(10);
            accumulationFundTotal.setNumFormat("0");
            accumulationFundTotal.setOrderNum(16);
            excelHeader.add(accumulationFundTotal);
            param.put("accumulationFundTotal", hrBillDetail.getAccumulationFundTotal());
            ExcelExportEntity total = new ExcelExportEntity("总金额", "total",20);
            total.setType(10);
            total.setOrderNum(17);
            excelHeader.add(total);
            param.put("total", hrBillDetail.getTotal());
        }
        // 薪酬账单
        else if (hrBill.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())){
            excelHeader.add(new ExcelExportEntity("姓名", "name", 20));
            param.put("name", hrBillDetail.getName());
            excelHeader.add(new ExcelExportEntity("身份证号", "certificateNum", 30));
            param.put("certificateNum", hrBillDetail.getCertificateNum());
            // 动态列表
            hrBillDetail.getHrBillDetailItemsList().forEach(ls -> {
                ExcelExportEntity temp = new ExcelExportEntity(ls.getExpenseName(), ls.getExpenseName(), 20);
                temp.setType(10);
                temp.setOrderNum(3);
                param.put(ls.getExpenseName(), ls.getAmount());
                // 增项
                if (DynamicFeeTypesEnum.FEE_TYPE_ADD.getKey().equals(ls.getExpenseType())) {
                    temp.setGroupName("工资增项");
                }
                // 减项
                else if (ls.getExpenseType().equals(DynamicFeeTypesEnum.FEE_TYPE_REDUCE.getKey())) {
                    temp.setGroupName("工资减项");
                }
                // 免税
                else if (ls.getExpenseType().equals(DynamicFeeTypesEnum.TAX_EXEMPT_INCOME.getKey())) {
                    temp.setGroupName("免税收入");
                }
                // 其他
                else {
                    temp.setGroupName("其他");
                }
                excelHeader.add(temp);
            });
            if (hrBillDetail.getTaxCalculationMethod() == 0){
                ExcelExportEntity salary = new ExcelExportEntity("应发工资", "salary",20);
                salary.setType(10);
                salary.setOrderNum(4);
                excelHeader.add(salary);
                param.put("salary", hrBillDetail.getSalary());
                param.put("accumulationFundCardinal", hrBillDetail.getAccumulationFundCardinal());
                ExcelExportEntity preTaxSalary = new ExcelExportEntity("税前应发", "preTaxSalary",20);
                preTaxSalary.setType(10);
                preTaxSalary.setOrderNum(5);
                excelHeader.add(preTaxSalary);
                param.put("preTaxSalary", hrBillDetail.getPreTaxSalary());
            }
            ExcelExportEntity personalTax = new ExcelExportEntity("个税", "personalTax",20);
            personalTax.setType(10);
            personalTax.setOrderNum(6);
            excelHeader.add(personalTax);
            param.put("personalTax", hrBillDetail.getPersonalTax());
            ExcelExportEntity personalTaxMakeUp = new ExcelExportEntity("个税补差", "personalTaxMakeUp",20);
            personalTaxMakeUp.setType(10);
            personalTaxMakeUp.setOrderNum(7);
            excelHeader.add(personalTaxMakeUp);
            param.put("personalTaxMakeUp", hrBillDetail.getPersonalTaxMakeUp());
            ExcelExportEntity realSalary = new ExcelExportEntity("实发工资", "realSalary",20);
            realSalary.setType(10);
            realSalary.setOrderNum(8);
            excelHeader.add(realSalary);
            param.put("realSalary", hrBillDetail.getRealSalary());
            ExcelExportEntity total = new ExcelExportEntity("总金额", "total",20);
            total.setType(10);
            total.setOrderNum(9);
            excelHeader.add(total);
            param.put("total", hrBillDetail.getTotal());
        }
        //其他账单
        else {
            int orderNUm = 2;
            if (hrBill.getOtherBillFlag() == null || hrBill.getOtherBillFlag() == 0){
                excelHeader.add(new ExcelExportEntity("姓名", "name", 20));
                param.put("name", hrBillDetail.getName());
                excelHeader.add(new ExcelExportEntity("身份证号", "certificateNum", 30));
                param.put("certificateNum", hrBillDetail.getCertificateNum());
            }else {
                excelHeader.add(new ExcelExportEntity("客户名称", "name", 20));
                param.put("name", hrBillDetail.getName());
                orderNUm = 1;
            }
            // 动态列表
            int finalOrderNUM = orderNUm;
            hrBillDetail.getHrBillDetailItemsList().forEach(ls -> {
                ExcelExportEntity temp = new ExcelExportEntity(ls.getExpenseName(), ls.getExpenseName(), 20);
                temp.setType(10);
                temp.setOrderNum(finalOrderNUM);
                param.put(ls.getExpenseName(), ls.getAmount());
                // 收费项
                if (DynamicFeeTypesEnum.CHARGE_ITEM.getKey().equals(ls.getExpenseType())) {
                    temp.setGroupName("收费项");
                }
                // 退费项
                else if (ls.getExpenseType().equals(DynamicFeeTypesEnum.REFUND_ITEM.getKey())) {
                    temp.setGroupName("退费项");
                }
                // 其他
                else {
                    temp.setGroupName("其他");
                }
                excelHeader.add(temp);
            });
            ExcelExportEntity total = new ExcelExportEntity("总金额", "total",20);
            total.setType(10);
            total.setOrderNum(hrBillDetail.getHrBillDetailItemsList().size() + finalOrderNUM);
            excelHeader.add(total);
            param.put("total", hrBillDetail.getTotal());
        }
        paramsList.add(param);
    }

    @Override
    public String getNormalSalaryEffectivePaymentDate() {
        Page<HrBilNormalDTO> page = new Page<>(1, 1);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        HrBillDTO hrBillDTO = new HrBillDTO();
        LocalDate now = LocalDate.now();
        hrBillDTO.setPayYear(now.getYear());
        hrBillDTO.setPayMonthly(now.getMonthValue());
        IPage<HrBilNormalDTO> iPage = this.hrBillRepository.findNormalSalaryPage(page, hrBillDTO, clientIds);
        if (iPage.getRecords().isEmpty()) {
            LocalDate lastMonth = now.minusMonths(1);
            hrBillDTO.setPayYear(lastMonth.getYear());
            hrBillDTO.setPayMonthly(lastMonth.getMonthValue());
            return hrBillDTO.getPaymentDate();
        }
        return hrBillDTO.getPaymentDate();
    }

    /**
     * 正常薪金导出
     * 若有选定数据，导出选中的数据，若无选中数据，导出全部符合筛选条件的数据
     * 导出内容：（删掉生育保险）
     *  1、包含本期未发的员工 ，本期收入和福利默认为0
     *  2、纳税所属账期为本期费用年月，并且为已发状态, 以员工为分组，本期收入为合计，福利为合计
     *  3、社保补差直接合集到养老里面，公积金补差就合计到公积金里
     *
     * @param hrBillDTO
     * @param response
     * @return
     */
    @Override
    public String normalSalaryExport(HrBillDTO hrBillDTO, HttpServletResponse response) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<String> exportIds = hrBillDTO.getIds();
        HrBillDTO hrBillParam = new HrBillDTO();
        if (exportIds != null && !exportIds.isEmpty()) {
            hrBillParam.setIds(exportIds);
            clientIds = null;
        } else {
            BeanUtils.copyProperties(hrBillDTO, hrBillParam);
        }
        List<HrBillDetailExport> billDetailExports = this.hrBillDetailRepository.getNormalSalaryExportList(hrBillParam, clientIds);
        if (CollectionUtils.isEmpty(billDetailExports)) {
            throw new CommonException("未查询到相关数据！");
        }
        // 更新正常薪金 为已导出状态
        Set<String> ids = new HashSet<>();
        for (HrBillDetailExport billDetailExport : billDetailExports) {
            ids.addAll(Arrays.asList(billDetailExport.getIds().split(",")));
        }
        ids.forEach(idStr -> {
            hrNormalSalaryService.saveNormalSalary(idStr);
        });
        int listSize = billDetailExports.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(billDetailExports, "正常工资薪金收入", HrBillDetailExport.class);
        // 操作日志
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.NORMAL_SALARY.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     *  获取正常薪金分页列表
     *
     * 人数=当前费用年月的人数（不管发放状态）+当前月份以前的已发放的员工    去重后的数量
     * 本期收入两种情况：
     *  1、应发工资=0时   本期收入=个人的五险一金+社保补差+公积金补差
     *  2、应发工资>0时   本期收入=应发工资
     * 本期福利=个人五险一金++社保补差+公积金补差
     *
     */
    @Override
    public IPage<HrBilNormalDTO> findNormalSalaryPage(HrBillDTO hrBillDTO, Long pageNumber, Long pageSize) {
        Page<HrBilNormalDTO> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        // 查询分页数据
        IPage<HrBilNormalDTO> normalSalaryPage = this.hrBillRepository.findNormalSalaryPage(page, hrBillDTO, clientIds);
        return normalSalaryPage;
    }

    /**
     * 复制保障账单或者薪酬账单，同时会生成对应的账单明细+账单汇总，薪酬相关的字段不用比对直接复制
     *
     * <p>
     * 验证账单参数是否一致：社保公积金比例
     * <p>
     * 只是比对旧账单中的可用的员工，旧账单中的不可用员工不做比较，直接放弃。
     * <p>
     * 比对的情况：
     * 1.前提是有员工ID，如果旧账单中的（可入账并且可用状态+不可入账并且可用状态）和新账单中的员工可入账状态不一致，算差异处理
     * 2.前提是有员工ID，如果旧账单中的可入账并且可用状态，检查三个基数不同，算差异处理
     * 3.如果旧账单为不可入账但是可用状态，新账单为不可入账时，新账单当做不可入账但是可用状态处理，直接复制
     * 4.如果旧账单中为没有员工ID那种，直接复制，直接当做不可入账但是可用的处理
     * 5.旧账单中的不可用员工不做比较，直接放弃
     * 6.新账单中没在旧账单的可用列表中的其他不可入账员工，按照新增账单明细处理
     * 7.新账单中的比旧账单多出来的可入账员工，按照新增账单明细处理
     *
     * @param hrBillDTO （billId + paymentDate）
     * @return
     */
    @Override
    public HrBillDTO copyHrBill(HrBillDTO hrBillDTO) {
        log.info("Copy HrBill:被复制的账单billId={}");
        // 验证被复制的账单是否存在
        HrBill oldBill = hrBillRepository.selectById(hrBillDTO.getId());
        if (oldBill == null || oldBill.getIsDelete()) {
            throw new CommonException("被复制的账单不存在!");
        }
        // 验证账单是否已经通过审核
        Integer reviewState = oldBill.getReviewState();
        if (reviewState == null || reviewState != 1) {
            throw new CommonException("系统只能复制已经审核通过的账单!");
        }
        hrBillDTO.setClientId(oldBill.getClientId());
        // 设置新账单参数
        HrBillDTO newBill = new HrBillDTO();
        String billNo = "";
        if (oldBill.getBillType() == 0) {
            billNo = "XC";
        } else if (oldBill.getBillType() == 1) {
            billNo = "BZ";
        } else if (oldBill.getBillType() == 2) {
            billNo = "OT";
        } else if (oldBill.getBillType() == 3) {
            billNo = "ZSH";
        }
        String suffix = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
        int random = (int)(Math.random()*1000 + 1);
        billNo = billNo + suffix + random;
        newBill.setBillNo(billNo);
        newBill.setOptClientId(oldBill.getOptClientId());
        if (oldBill.getOptTitle() != null) {
            newBill.setOptTitle(oldBill.getOptTitle().replace(oldBill.getPayMonthly() + "月份", hrBillDTO.getPayMonthly() + "月份"));
        } else {
            newBill.setOptTitle(oldBill.getTitle().replace(oldBill.getPayMonthly() + "月份", hrBillDTO.getPayMonthly() + "月份"));
        }

        newBill.setBillType(oldBill.getBillType());
        newBill.setClientId(oldBill.getClientId());
        newBill.setPayYear(hrBillDTO.getPayYear());
        newBill.setPayMonthly(hrBillDTO.getPayMonthly());
        this.generateBasicBill(newBill);
        // 验证社保参数是否存在
        if (!BigDecimalCompare.of(oldBill.getUnitPensionScale()).eq(newBill.getUnitPensionScale()) || !BigDecimalCompare.of(oldBill.getUnitUnemploymentScale()).eq(newBill.getUnitUnemploymentScale()) || !BigDecimalCompare.of(oldBill.getUnitMedicalScale()).eq(newBill.getUnitMedicalScale()) || !BigDecimalCompare.of(oldBill.getWorkInjuryScale()).eq(newBill.getWorkInjuryScale()) || !BigDecimalCompare.of(oldBill.getPersonalPensionScale()).eq(newBill.getPersonalPensionScale()) || !BigDecimalCompare.of(oldBill.getPersonalUnemploymentScale()).eq(newBill.getPersonalUnemploymentScale()) || !BigDecimalCompare.of(oldBill.getPersonalMedicalScale()).eq(newBill.getPersonalMedicalScale())) {
            throw new CommonException("该客户社保比例调整！");
        }
        // 验证公积金参数是否存在
        if (!BigDecimalCompare.of(oldBill.getUnitAccumulationFundScale()).eq(newBill.getUnitAccumulationFundScale()) || !BigDecimalCompare.of(oldBill.getPersonalAccumulationFundScale()).eq(newBill.getPersonalAccumulationFundScale())) {
            throw new CommonException("该客户公积金比例不同！");
        }
        // 只比对旧账单中可用的账单明细
        List<HrBillDetailDTO> oldBillDetailList = hrBillDetailRepository.getListByBillId(oldBill.getId(), oldBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey()) ? null : 1);
        List<HrBillDetailDTO> differentList = new ArrayList<>();
        BigDecimal halfYearlyServiceFee = newBill.getHalfYearlyServiceFee();
        if(oldBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
            if (oldBillDetailList.isEmpty()) {
                throw new CommonException("复制账单暂无可用的项目部信息！");
            }
            newBill.setMonthlyServiceFee(BigDecimal.ZERO);
            newBill.setHalfYearlyServiceFee(BigDecimal.ZERO);
            newBill.setBillDetailList(oldBillDetailList);
        }else {
            this.generateBillDetailList(newBill,null);
        }
        List<HrBillDetailDTO> hrBillDetailList = newBill.getBillDetailList();
        if(!oldBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
            // 比对明细的差异
            for (HrBillDetailDTO oldDetailDTO : oldBillDetailList) {
                boolean billUsed = oldDetailDTO.getBillUsed();
                String staffId = oldDetailDTO.getStaffId();
                if (StringUtils.isNotBlank(staffId)) {
                    boolean isExist = false;
                    for (HrBillDetailDTO detailDTO : hrBillDetailList) {
                        if (detailDTO.getStaffId().equals(staffId)) {
                            // 直接复制动态费用项,不需要做任何处理
                            detailDTO.setHrBillDetailItemsList(oldDetailDTO.getHrBillDetailItemsList());
                            isExist = true;
                            // 对比检查三个基数
                            if (!BigDecimalCompare.of(detailDTO.getUnitPensionCardinal()).eq(oldDetailDTO.getUnitPensionCardinal())
                                || !BigDecimalCompare.of(detailDTO.getUnitUnemploymentCardinal()).eq(oldDetailDTO.getUnitUnemploymentCardinal())
                                || !BigDecimalCompare.of(detailDTO.getWorkInjuryCardinal()).eq(oldDetailDTO.getWorkInjuryCardinal())
                                || !BigDecimalCompare.of(detailDTO.getUnitMaternityCardinal()).eq(oldDetailDTO.getUnitMaternityCardinal())
                                || !BigDecimalCompare.of(detailDTO.getMedicalInsuranceCardinal()).eq(oldDetailDTO.getMedicalInsuranceCardinal())
                                || !BigDecimalCompare.of(detailDTO.getAccumulationFundCardinal()).eq(oldDetailDTO.getAccumulationFundCardinal())
                                || !BigDecimalCompare.of(detailDTO.getPersonalPensionCardinal()).eq(oldDetailDTO.getPersonalPensionCardinal())
                                || !BigDecimalCompare.of(detailDTO.getPersonalUnemploymentCardinal()).eq(oldDetailDTO.getPersonalUnemploymentCardinal())
                                || !BigDecimalCompare.of(detailDTO.getMedicalInsuranceCardinalPersonal()).eq(oldDetailDTO.getMedicalInsuranceCardinalPersonal())
                                || !BigDecimalCompare.of(detailDTO.getPersonalMaternityCardinal()).eq(oldDetailDTO.getPersonalMaternityCardinal())
                            ) {
                                String reason = "";
                                if (!BigDecimalCompare.of(detailDTO.getUnitPensionCardinal()).eq(oldDetailDTO.getUnitPensionCardinal())) {
                                    reason += "单位养老调整 ";
                                }
                                if (!BigDecimalCompare.of(detailDTO.getUnitUnemploymentCardinal()).eq(oldDetailDTO.getUnitUnemploymentCardinal())) {
                                    reason += "单位失业调整 ";
                                }
                                if (!BigDecimalCompare.of(detailDTO.getWorkInjuryCardinal()).eq(oldDetailDTO.getWorkInjuryCardinal())) {
                                    reason += "单位工伤调整 ";
                                }
                                if (!BigDecimalCompare.of(detailDTO.getUnitMaternityCardinal()).eq(oldDetailDTO.getUnitMaternityCardinal())) {
                                    reason += "单位生育调整 ";
                                }
                                if (!BigDecimalCompare.of(detailDTO.getMedicalInsuranceCardinal()).eq(oldDetailDTO.getMedicalInsuranceCardinal())) {
                                    reason += "单位医疗基数调整 ";
                                }
                                if (!BigDecimalCompare.of(detailDTO.getAccumulationFundCardinal()).eq(oldDetailDTO.getAccumulationFundCardinal())) {
                                    reason += "公积金基数调整 ";
                                }
                                if (!BigDecimalCompare.of(detailDTO.getPersonalPensionCardinal()).eq(oldDetailDTO.getPersonalPensionCardinal())) {
                                    reason += "个人养老基数调整 ";
                                }
                                if (!BigDecimalCompare.of(detailDTO.getPersonalUnemploymentCardinal()).eq(oldDetailDTO.getPersonalUnemploymentCardinal())) {
                                    reason += "个人失业基数调整 ";
                                }
                                if (!BigDecimalCompare.of(detailDTO.getMedicalInsuranceCardinalPersonal()).eq(oldDetailDTO.getMedicalInsuranceCardinalPersonal())) {
                                    reason += "个人医疗基数调整 ";
                                }
                                if (!BigDecimalCompare.of(detailDTO.getPersonalMaternityCardinal()).eq(oldDetailDTO.getPersonalMaternityCardinal())) {
                                    reason += "个人生育基数调整 ";
                                }
                                oldDetailDTO.setReason(reason);
                                differentList.add(oldDetailDTO);
                            } else {
                                // 对比可入账状态
                                if (billUsed != detailDTO.getBillUsed()) {
                                    oldDetailDTO.setReason("可入账状态不同");
                                    differentList.add(oldDetailDTO);
                                } else {
                                    //  如果旧账单为不可入账但是可用状态，新账单为不可入账时，新账单当做不可入账但是可用状态处理，直接复制
                                    if (!billUsed) {
                                        this.copyBillDetail(oldDetailDTO, detailDTO);
                                    }
                                }
                            }
                            // 对比新旧账单中员工的入账状态是否一致
                            break;
                        }
                    }
                    if (!isExist) {
                        oldDetailDTO.setReason("新账单中不存在该员工");
                        differentList.add(oldDetailDTO);
                    }
                } else {
                    // 如果旧账单中为没有员工ID那种，直接复制，直接当做不可入账但是可用的处理
                    HrBillDetailDTO hrBillDetailDTO = new HrBillDetailDTO();
                    this.copyBillDetail(oldDetailDTO, hrBillDetailDTO);
                    hrBillDetailList.add(hrBillDetailDTO);
                }
            }
        }
        // 通过验证 把数据插入数据库
        if (differentList.isEmpty()) {
            // 把账单设置为正式
            newBill.setIsOfficial(1);
            HrBillDTO hrBill = this.saveBasicBill(newBill);
            // 如果为薪酬账单，复制hr_bill_dynamic_fields+hr_bill_detail_items需要初始化账单的增项、减项、其他项
            if (!newBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())) {
                HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO = hrBillDynamicFieldsRepository.getByBillId(oldBill.getId());
                hrBillDynamicFieldsDTO.setId(null).setBillId(hrBill.getId());
                hrBillDynamicFieldsRepository.insert(hrBillDynamicFieldsMapper.toEntity(hrBillDynamicFieldsDTO));
//                hrBill.setBillDetailItemsList(hrBillDetailItemsRepository.getBillDynamicItemsByAnyDetail(oldBill.getId()));
            }
            this.saveBillDetailList(hrBill, hrBillDetailList, false);
            // 生成汇总账单
            this.createBillTotal(hrBill, halfYearlyServiceFee);
            // 账单日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.BILL.getValue(), BusinessTypeEnum.INSERT.getKey(), JSON.toJSONString(hrBillDTO), HrBillDTO.class, null, null, null, JSON.toJSONString(hrBill), HrBillDTO.class);
        }
        return new HrBillDTO().setDifferentList(differentList);
    }


    private void copyBillDetail(HrBillDetailDTO source, HrBillDetailDTO target) {
        BeanUtils.copyProperties(source, target, new String[]{"id", "billId", "isDelete", "createdBy", "lastModifiedBy", "createdDate", "lastModifiedDate"});
    }

    /**
     * 生成汇总账单
     *
     * @param hrBill
     * @param halfYearlyServiceFee
     */
    private void createBillTotal(HrBillDTO hrBill, BigDecimal halfYearlyServiceFee) {
        // 验证是否有可用的账单明细
        if(hrBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
            this.createSinopecBillTotal(hrBill);
        }else if (hrBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())){
            List<HrBillDetailDTO> billDetailDTOS = hrBillDetailRepository.getListByBillId(hrBill.getId(), 1);
            if (CollectionUtils.isNotEmpty(billDetailDTOS)) {
                HrBillTotal billTotal = hrBillTotalRepository.createByBillId(hrBill.getId());
                billTotal.setBillId(hrBill.getId())
                    .setType(hrBill.getBillType())
                    .setPayYear(hrBill.getPayYear())
                    .setPayMonthly(hrBill.getPayMonthly())
                    .setUnitSubtotal(CalculateUtils.decimalListAddition(this.getValue(billTotal.getUnitPensionTotal()), this.getValue(billTotal.getUnitMedicalTotal()), this.getValue(billTotal.getUnitUnemploymentTotal()),
                        this.getValue(billTotal.getWorkInjuryTotal()), this.getValue(billTotal.getUnitMaternityTotal()), this.getValue(billTotal.getUnitSocialSecurityMakeUpTotal()), this.getValue(billTotal.getUnitLargeMedicalExpenseTotal()),
                        this.getValue(billTotal.getReplenishWorkInjuryExpenseTotal()), this.getValue(billTotal.getUnitEnterpriseAnnuityTotal()), this.getValue(billTotal.getCommercialInsuranceTotal()), this.getValue(billTotal.getUnitLateFeeTotal()), this.getValue(billTotal.getUnitOtherFeeTotal())))
                    .setPersonalSubtotal(CalculateUtils.decimalListAddition(this.getValue(billTotal.getPersonalPensionTotal()), this.getValue(billTotal.getPersonalMedicalTotal()), this.getValue(billTotal.getPersonalUnemploymentTotal()), this.getValue(billTotal.getPersonalMaternityTotal()),
                        this.getValue(billTotal.getPersonalSocialSecurityMakeUpTotal()), this.getValue(billTotal.getPersonalLargeMedicalExpenseTotal()), this.getValue(billTotal.getPersonalEnterpriseAnnuityTotal()), this.getValue(billTotal.getPersonalOtherFeeTotal())))
                    .setLastMonthMakeUp(BigDecimal.ZERO)
                    .setServiceFeeTotal(CalculateUtils.decimalListAddition(this.getValue(billTotal.getServiceFeeTotal()), halfYearlyServiceFee));
                // 获取总的费用 = 社保总金额 + 公积金总金额 + 服务费总计 + 上月补差 + 实发总计 + 个税总计 + 其他费用总计
                BigDecimal total = CalculateUtils.decimalListAddition(this.getValue(billTotal.getSocialSecurityTotal()), this.getValue(billTotal.getAccumulationFundTotal()),
                    this.getValue(billTotal.getServiceFeeTotal()), this.getValue(billTotal.getLastMonthMakeUp()),
                    this.getValue(billTotal.getRealSalaryTotal()), this.getValue(billTotal.getPersonalTaxTotal()),
                    this.getValue(billTotal.getOtherFeeTotal())
                );
                billTotal.setTotal(total);
                hrBillTotalRepository.insert(billTotal);
            }
        }else if (hrBill.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())){
            List<HrBillDetailDTO> billDetailDTOS = hrBillDetailRepository.getListByBillId(hrBill.getId(), 1);
            HrBillTotal billTotal = this.hrBillTotalRepository.createByBillId(hrBill.getId());
            billTotal.setBillId(hrBill.getId())
                .setType(hrBill.getBillType())
                .setPayYear(hrBill.getPayYear())
                .setPayMonthly(hrBill.getPayMonthly())
                .setUnitSubtotal(CalculateUtils.decimalListAddition(billTotal.getUnitPensionTotal(), billTotal.getUnitMedicalTotal(), billTotal.getUnitUnemploymentTotal(), billTotal.getWorkInjuryTotal()))
                .setPersonalSubtotal(CalculateUtils.decimalListAddition(billTotal.getPersonalPensionTotal(), billTotal.getPersonalMedicalTotal(), billTotal.getPersonalUnemploymentTotal()))
                .setLastMonthMakeUp(BigDecimal.ZERO)
                .setServiceFeeTotal(CalculateUtils.decimalListAddition(billTotal.getServiceFeeTotal(), halfYearlyServiceFee));
            BigDecimal total = billDetailDTOS.stream().map(i -> i.getTotal() == null ? BigDecimal.ZERO : i.getTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            billTotal.setTotal(total);
            this.hrBillTotalRepository.insert(billTotal);
        }else {
            List<HrBillDetailDTO> billDetailDTOS = hrBillDetailRepository.getListByBillId(hrBill.getId(), 1);
            BigDecimal billTotalNum = billDetailDTOS.stream().map(i -> i.getTotal() == null ? BigDecimal.ZERO : i.getTotal()).reduce(BigDecimal.ZERO, BigDecimal::add);
            HrBillTotal  billTotal = new HrBillTotal();
            billTotal.setBillId(hrBill.getId())
                .setType(hrBill.getBillType())
                .setPayYear(hrBill.getPayYear())
                .setPayMonthly(hrBill.getPayMonthly())
                .setTotal(billTotalNum);
            BigDecimal chargeTotal = BigDecimal.ZERO;
            BigDecimal refundTotal = BigDecimal.ZERO;
            List<HrBillDetailItemsDTO> hrBillDetailItems = new ArrayList<>();
            billDetailDTOS.forEach(lst->{
                hrBillDetailItems.addAll(lst.getHrBillDetailItemsList());
            });
            if (CollectionUtils.isNotEmpty(hrBillDetailItems)){
                for(HrBillDetailItemsDTO param: hrBillDetailItems) {
                    DynamicFeeTypesEnum typesEnum = EnumUtils.getEnumByKey(DynamicFeeTypesEnum.class, param.getExpenseType());
                    BigDecimal amount = param.getAmount() == null? BigDecimal.ZERO: param.getAmount();
                    switch (typesEnum) {
                        case CHARGE_ITEM:
                            chargeTotal = chargeTotal.add(amount);
                            break;
                        case REFUND_ITEM:
                            refundTotal = refundTotal.add(amount.abs());
                            break;
                        default:
                            break;
                    }
                }
            }
            billTotal.setChargeTotal(chargeTotal);
            billTotal.setRefundTotal(refundTotal);
            this.hrBillTotalRepository.insert(billTotal);
        }
    }

    private BigDecimal getValue(BigDecimal key) {
        return key != null ? key : BigDecimal.ZERO;
    }

    /**
     * 生成账单明细数据 不存入数据库
     *
     * @param hrBill
     */
    private HrBillDTO generateBillDetailList(HrBillDTO hrBill, List<String> staffIds) {
        // 创建账单明细
        List<HrBillDetailDTO> hrBillDetailList = hrBillDetailRepository.getStaffParamsByClientId(Collections.singletonList(hrBill.getClientId()));
        if (hrBillDetailList.isEmpty()) {
            throw new CommonException("该客户暂无员工信息！");
        }
        HrSocialSecurityDTO socialSecurityDTO = this.getClientSocialSecurity(Collections.singletonList(hrBill.getClientId()));
        // TODO 初始化服务费
        // this.initServiceFee(hrBill);
        hrBill.setMonthlyServiceFee(BigDecimal.ZERO);
        hrBill.setHalfYearlyServiceFee(BigDecimal.ZERO);
        //保障账单
        List<HrBillDetailDTO> billDetailByBill = hrBillDetailRepository.getBillDetailByBill(
            new HrBillDetailDTO().setPayYear(hrBill.getPayYear()).setPayMonthly(hrBill.getPayMonthly()).setBillType(BillEnum.BillType.SECURITY_BILL.getKey()).setBillPurpose(1)
        );
        //薪酬账单
        List<HrBillDetailDTO> hrBillDetailDTOS = hrBillDetailRepository.selectSalaryBill(new HrBillDetailDTO().setPayYear(hrBill.getPayYear()).setPayMonthly(hrBill.getPayMonthly()));
        //获取未使用的补差
        List<HrWelfareCompensation>  welfareCompensations = hrWelfareCompensationRepository.getUnUsedWelfareCompensation(hrBill.getBillType(), hrBill.getPaymentDate());
        //获取同月份以及上个月已保存补差的对账数据
        int payYear = hrBill.getPayYear();
        int payMonthly = hrBill.getPayMonthly();
        int lastPayYear;
        int lastPayMonthly;
        // 上个缴费年月
        if (payMonthly > 1) {
            lastPayYear = payYear;
            lastPayMonthly = payMonthly - 1;
        } else {
            lastPayYear = payYear - 1;
            lastPayMonthly = 12;
        }
        List<Integer> typeList = Arrays.asList(BillEnum.GuidePlateType.SOCIAL_SECURITY_GUIDE.getKey(),
            BillEnum.GuidePlateType.MEDICAL_GUIDE.getKey(),
            BillEnum.GuidePlateType.ACCUMULATION_FUND_GUIDE.getKey(),
            BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey());
        List<HrBillCompareResultDTO> hrBillCompareResultDTOList = hrBillCompareResultRepository.getBillCompareResult(payYear, payMonthly, lastPayYear, lastPayMonthly, typeList);

        for (HrBillDetailDTO hrBillDetailDTO : hrBillDetailList) {
            // 填充账单明细必须的参数
            this.fillHrBillDetail(hrBillDetailDTO, hrBill, staffIds, socialSecurityDTO,hrBillCompareResultDTOList, billDetailByBill, hrBillDetailDTOS, welfareCompensations,lastPayYear,lastPayMonthly);
        }
        hrBill.setBillDetailList(hrBillDetailList);
        hrBill.setHalfYearlyServiceFee(hrBill.getHalfYearlyServiceFee());
        return hrBill;
    }

    /**
     * 保存账单明细列表
     *
     * @param hrBillDTO
     * @param hrBillDetailList
     * @param isSaveRedis      是否把补差关系表存入Redis中    true存入redis、false存入DB
     */
    private void saveBillDetailList(HrBillDTO hrBillDTO, List<HrBillDetailDTO> hrBillDetailList, boolean isSaveRedis) {
        JWTUserDTO user = SecurityUtils.getCurrentUser().get();
        List<HrBillDetail> detailList = new ArrayList<>();
        int sort = 0;
        Integer personalTaxStart = null;
        List<Map<String, BigDecimal>> incomeMap = new ArrayList<>();
        List<HrSpecialDeduction> specialDeductionList = new ArrayList<>();
        List<HrQuickDeduction> hrQuickDeductions = new ArrayList<>();
        if (hrBillDTO.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())){
            Integer payYear = hrBillDTO.getPayYear();
            Integer payMonthly = hrBillDTO.getPayMonthly();
            personalTaxStart = this.codeTableRepository.getValueByInnerName("personalTaxStart");
            incomeMap = this.hrSpecialDeductionRepository.getSumCurrentIncomeGroupClient(LocalDate.of(payYear, payMonthly, 1));
            // 若为1月 不再获取前两个月的数据
            // 若为2月，只获取第一个月的数据
            // 若大于2月，获取前两个月数据
            int lastMonthly = payMonthly;
            if (payMonthly == 2) {
                // 获取上月月份
                lastMonthly = payMonthly - 1;
            } else if (payMonthly > 2) {
                // 获取上月月份
                lastMonthly = payMonthly - 1;
                // 获取上上月月份
                // beforeLastMonthly = payMonthly - 2;
            }
            specialDeductionList = this.hrSpecialDeductionRepository.getAllByStartDate(LocalDate.of(payYear, payMonthly, 1),LocalDate.of(payYear, lastMonthly, 1));
            hrQuickDeductions = this.hrQuickDeductionRepository.selectAll();
        }
        for (HrBillDetailDTO billDetail : hrBillDetailList) {
            // 生成uuid
            billDetail.setId(UUID.randomUUID().toString().replace("-",""));
            billDetail.setBillId(hrBillDTO.getId());
            billDetail.setPaymentDate(null);
            billDetail.setPayYear(hrBillDTO.getPayYear()).setPayMonthly(hrBillDTO.getPayMonthly());
            // 如果为薪酬账单，需要初始化账单的增项、减项、其他项, 个税等
            if (hrBillDTO.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())) {
                List<HrBillDetailItemsDTO> billDetailItemsList = billDetail.getHrBillDetailItemsList();
                if (billDetailItemsList != null && !billDetailItemsList.isEmpty()) {
                    // 处理薪酬账单特有的字段
                    AtomicReference<BigDecimal> sumIncome = new AtomicReference<>(BigDecimal.ZERO);
                    incomeMap.forEach(map ->{
                        sumIncome.set(map.get(billDetail.getClientId()));
                    });
                    BigDecimal sumCurrentIncome = sumIncome.get();
                    this.handleBillDynamicInfo(billDetailItemsList, billDetail, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
                }
            }else if (hrBillDTO.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey())){
                List<HrBillDetailItemsDTO> billDetailItemsList = billDetail.getHrBillDetailItemsList();
                if (billDetailItemsList != null && !billDetailItemsList.isEmpty()){
                    List<HrBillDetailItems> hrBillDetailItems = this.fillOtherBillParam(billDetail, billDetailItemsList,true);
                    hrBillDetailItemsService.saveBatch(hrBillDetailItems);
                }
            }else if (hrBillDTO.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
                List<HrBillDetailItemsDTO> billDetailItemsList = billDetail.getHrBillDetailItemsList();
                if (billDetailItemsList != null && !billDetailItemsList.isEmpty()){
                    List<HrBillDetailItems> detailItems = new ArrayList<>();
                    for (HrBillDetailItemsDTO billDetailItemsDTO : billDetailItemsList) {
                        HrBillDetailItems hrBillDetailItems = hrBillDetailItemsMapper.toEntity(billDetailItemsDTO.setId(null).setBillDetailId(billDetail.getId()));
                        detailItems.add(hrBillDetailItems);
                    }
                    // 批量插入动态费用项
                    this.hrBillDetailItemsService.saveBatch(detailItems);
                }
            }

            HrBillDetail newBillDetail = this.hrBillDetailMapper.toEntity(billDetail);
            // 获取补差明细信息
            List<HrWelfareCompensation> welfareCompensationList = billDetail.getWelfareCompensationList();
            if (isSaveRedis) {
                // 增加账单明细表与补差明细表的关系 缓存到redis中
                this.addMakeUpUserRecordToRedis(newBillDetail, welfareCompensationList);
            } else {
                // 增加账单明细表与补差明细表的关系 缓存到DB中
                this.addMakeUpUserRecord(newBillDetail, welfareCompensationList);
            }
            newBillDetail.setType(hrBillDTO.getBillType());
            newBillDetail.setCreatedDate(LocalDateTime.now());
            newBillDetail.setCreatedBy(user.getUserName());
            newBillDetail.setSortValue(sort);
            detailList.add(newBillDetail);
            sort++;
        }
        // 批量插入数据
        hrBillDetailRepository.batchSave(detailList);
    }

    /**
     * 处理薪酬账单特有的字段
     *  @param billDetailItemsList
     * @param newBillDetail
     * @param personalTaxStart
     * @param sumCurrentIncome
     * @param specialDeductionList
     * @param hrQuickDeductions
     */
    private void handleBillDynamicInfo(List<HrBillDetailItemsDTO> billDetailItemsList, HrBillDetailDTO newBillDetail, Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions) {
        // 应发工资
        BigDecimal salary = BigDecimal.ZERO;
        // 其他费用
        BigDecimal otherFee = BigDecimal.ZERO;

        List<HrBillDetailItems> detailItems = new ArrayList<>();
        for (HrBillDetailItemsDTO billDetailItemsDTO : billDetailItemsList) {
            DynamicFeeTypesEnum typesEnum = EnumUtils.getEnumByKey(DynamicFeeTypesEnum.class, billDetailItemsDTO.getExpenseType());
            switch (typesEnum) {
                case FEE_TYPE_ADD:
                case TAX_EXEMPT_INCOME:
                    salary = salary.add(billDetailItemsDTO.getAmount());
                    break;
                case FEE_TYPE_REDUCE:
                    salary = salary.subtract(billDetailItemsDTO.getAmount().abs());
                case FEE_TYPE_OTHER:
                    otherFee = otherFee.add(billDetailItemsDTO.getAmount());
                    break;
                default:
                    break;
            }
            HrBillDetailItems hrBillDetailItems = hrBillDetailItemsMapper.toEntity(billDetailItemsDTO.setId(null).setBillDetailId(newBillDetail.getId()));
            detailItems.add(hrBillDetailItems);
        }
        // 批量插入动态费用项
        this.hrBillDetailItemsService.saveBatch(detailItems);

        // 计算个税
        newBillDetail.setSalary(salary);
        this.hrBillDetailItemsService.calculatePersonalIncomeTax(newBillDetail, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);

        //薪酬原单计税方式有改动--弃用
//        this.hrBillDetailItemsService.annualLumpSumBonusTax(newBillDetail);
        // 个税
        BigDecimal personalTax = newBillDetail.getPersonalTax() == null ? BigDecimal.ZERO : newBillDetail.getPersonalTax();

        // 税前应发=应发工资-个人社保小计-个人公积金小计
        BigDecimal preTaxSalary = CalculateUtils.decimalListSubstract(salary, newBillDetail.getPersonalSubtotal(),
            newBillDetail.getPersonalAccumulationFund(), newBillDetail.getPersonalAccumulationFundMakeUp());

        // 费用合计 =  税前应发
        BigDecimal totalFee = preTaxSalary;
        // 实发工资=税前应发-个税
        BigDecimal realSalary = preTaxSalary.subtract(personalTax);
        newBillDetail
            .setPreTaxSalary(preTaxSalary)
            .setPersonalTax(personalTax)
            .setRealSalary(realSalary)
            .setOtherSalary(otherFee)
            .setTotal(totalFee);
    }

    /**
     * 创建账单，同时会生成对应的账单明细+账单汇总
     *
     * @param hrBillDTO
     * @return
     */
    @Override
    public HrBillDTO createHrBill(HrBillDTO hrBillDTO) {
        log.info("Create new HrBill:{}", hrBillDTO);
        hrBillDTO.setOptClientId(hrBillDTO.getClientId());
        List<String> usedClientIdList = hrBillDTO.getUsedClientIdList();
        List<String> staffIds = hrBillDTO.getStaffIds();
        if (usedClientIdList.isEmpty()) {
            throw new CommonException("没有可创建账单的客户！");
        }
        List<String> billIdList = new ArrayList<>();
        boolean flag = false;
        HrBillDTO newBillDTO = new HrBillDTO();
        BeanUtils.copyProperties(hrBillDTO, newBillDTO);
        if (!usedClientIdList.contains(hrBillDTO.getClientId())) {
            usedClientIdList.add(hrBillDTO.getClientId());
            flag = true;
        }
        // 为可用公司创建账单
        String dtoClientId = hrBillDTO.getClientId();
        String billNo = "";
        if (hrBillDTO.getBillType() == 0) {
            billNo = "XC";
        } else if (hrBillDTO.getBillType() == 1) {
            billNo = "BZ";
        } else if (hrBillDTO.getBillType() == 2) {
            billNo = "OT";
        } else if (hrBillDTO.getBillType() == 3) {
            billNo = "ZSH";
        }
        String suffix = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
        int random = (int)(Math.random()*1000 + 1);
        billNo = billNo + suffix + random;
        hrBillDTO.setBillNo(billNo);
        hrBillDTO.setOptTitle(hrBillDTO.getTitle());
        for (String clientId : usedClientIdList) {
            if (clientId.equals(dtoClientId)) {
                hrBillDTO.setIsChoice(true);
                if (flag) {
                    HrBill hrBill = this.saveBillNotUsed(usedClientIdList, newBillDTO);
                    billIdList.add(hrBill.getId());
                    continue;
                }
            } else {
                hrBillDTO.setIsChoice(false);
            }
            hrBillDTO.setClientId(clientId);
            hrBillDTO.setFlag(true);
            HrBillDTO hrBill = this.createBasicBill(hrBillDTO);
            // 创建账单明细
            this.generateBillDetailList(hrBill, staffIds);
            this.saveBillDetailList(hrBill, hrBill.getBillDetailList(), true);
            // 生成汇总账单
            this.createBillTotal(hrBill, hrBill.getHalfYearlyServiceFee());
            billIdList.add(hrBill.getId());
        }
        return this.getHrBillBatch(billIdList);
        // return this.getHrBill(billId);
    }

    /**
     * 增加账单明细表与补差明细表的关系 直接存入DB中
     *
     * @param billDetail              账单明细
     * @param welfareCompensationList
     */
    @Override
    public void addMakeUpUserRecord(HrBillDetail billDetail, List<HrWelfareCompensation> welfareCompensationList) {
        this.deleteMakeUpUseRecord(billDetail);
        if (welfareCompensationList != null && !welfareCompensationList.isEmpty()) {
            for (HrWelfareCompensation compensation : welfareCompensationList) {
                hrMakeUpUseRecordRepository.insert(this.getMakeUpUseRecord(billDetail, compensation));
            }
        }
    }

    /**
     * 增加账单明细表与补差明细表的关系 从redis中取值
     *
     * @param billDetail 账单明细
     */
    @Override
    public void addMakeUpUserRecordFromRedis(HrBillDetail billDetail) {
        // 从redis中获取中间数据
        List<HrMakeUpUseRecord> recordList = redisCache.getCacheList(RedisKeyEnum.bill.BILL_DETAIL_MAKEUP + billDetail.getId());
        if (recordList != null && !recordList.isEmpty()) {
            this.deleteMakeUpUseRecord(billDetail);
            for (HrMakeUpUseRecord record : recordList) {
                if (StringUtils.isNotBlank(record.getMakeUpId()) && StringUtils.isNotBlank(record.getBillId()) &&
                    StringUtils.isNotBlank(record.getBillDetailId()) && StringUtils.isNotBlank(record.getStaffId())){
                    hrMakeUpUseRecordRepository.insert(record);
                }
            }
            redisCache.deleteObject(RedisKeyEnum.bill.BILL_DETAIL_MAKEUP + billDetail.getId());
        }
    }

    /**
     * 删除账单明细表与补差明细表的关系
     *
     * @param billDetail
     */
    private void deleteMakeUpUseRecord(HrBillDetail billDetail) {
        hrMakeUpUseRecordRepository.delete(new QueryWrapper<HrMakeUpUseRecord>().eq("bill_id", billDetail.getBillId()).eq("bill_detail_id", billDetail.getId()).eq("is_delete", 0));
    }

    /**
     * 增加账单明细表与补差明细表的关系 缓存到redis中
     *
     * @param billDetail              账单明细
     * @param welfareCompensationList
     */
    @Override
    public void addMakeUpUserRecordToRedis(HrBillDetail billDetail, List<HrWelfareCompensation> welfareCompensationList) {
        if (welfareCompensationList != null && !welfareCompensationList.isEmpty()) {
            redisCache.deleteObject(RedisKeyEnum.bill.BILL_DETAIL_MAKEUP + billDetail.getId());
            List<HrMakeUpUseRecord> recordList = new ArrayList<>();
            for (HrWelfareCompensation compensation : welfareCompensationList) {
                HrMakeUpUseRecord makeUpUseRecord = this.getMakeUpUseRecord(billDetail, compensation);
                recordList.add(makeUpUseRecord);
            }
            if (!recordList.isEmpty()) {
                redisCache.setCacheList(RedisKeyEnum.bill.BILL_DETAIL_MAKEUP + billDetail.getId(), recordList);
                redisCache.expire(RedisKeyEnum.bill.BILL_DETAIL_MAKEUP + billDetail.getId(), 24, TimeUnit.HOURS);
            }
        }
    }

    /**
     * 修改保障账单基数前查询
     * @param hrBillDTO
     * @return
     */
    @Override
    public ResponseEntity<?> getPromptLanguage(HrBillDTO hrBillDTO) {
        List<HrBillDetailDTO> newBillDetail = hrBillDTO.getBillDetailList();
        List<String> billDetailIds = newBillDetail.stream().map(HrBillDetailDTO::getId).collect(Collectors.toList());
        List<HrBillDetail> oldBillDetail = hrBillDetailRepository.selectBatchIds(billDetailIds);
        boolean flag = false;
        List<HrBillDetailDTO> collect = newBillDetail.stream().filter(ls -> ls.getUpdateState() != null && (ls.getUpdateState().equals(BillEnum.BillDetailUpdateState.ADD.getKey()) || ls.getUpdateState().equals(BillEnum.BillDetailUpdateState.DELETE.getKey()) )).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)){
            flag = true;
        }else {
            for (HrBillDetailDTO newData : newBillDetail) {
                boolean anyMatch = oldBillDetail.stream().anyMatch(oldData ->
                        BigDecimalCompare.of(newData.getUnitPensionCardinal()).eq(oldData.getUnitPensionCardinal())
                            && BigDecimalCompare.of(newData.getUnitUnemploymentCardinal()).eq(oldData.getUnitUnemploymentCardinal())
                            && BigDecimalCompare.of(newData.getWorkInjuryCardinal()).eq(oldData.getWorkInjuryCardinal())
                            && BigDecimalCompare.of(newData.getUnitMaternityCardinal()).eq(oldData.getUnitMaternityCardinal())
                            && BigDecimalCompare.of(newData.getMedicalInsuranceCardinal()).eq(oldData.getMedicalInsuranceCardinal())
                            && BigDecimalCompare.of(newData.getAccumulationFundCardinal()).eq(oldData.getAccumulationFundCardinal())
                            && BigDecimalCompare.of(newData.getPersonalPensionCardinal()).eq(oldData.getPersonalPensionCardinal())
                            && BigDecimalCompare.of(newData.getPersonalUnemploymentCardinal()).eq(oldData.getPersonalUnemploymentCardinal())
                            && BigDecimalCompare.of(newData.getMedicalInsuranceCardinalPersonal()).eq(oldData.getMedicalInsuranceCardinalPersonal())
                            && BigDecimalCompare.of(newData.getPersonalMaternityCardinal()).eq(oldData.getPersonalMaternityCardinal())
                    && oldData.getUnitAccumulationFundMakeUp().compareTo(newData.getUnitAccumulationFundMakeUp()) == 0
                    && oldData.getPersonalAccumulationFundMakeUp().compareTo(newData.getPersonalAccumulationFundMakeUp()) == 0
                    && oldData.getUnitSocialSecurityMakeUp().compareTo(newData.getUnitSocialSecurityMakeUp()) == 0
                    && oldData.getPersonalSocialSecurityMakeUp().compareTo(newData.getPersonalSocialSecurityMakeUp()) == 0
                );
                if (!anyMatch){
                    flag = true;
                }
            }
        }
        HrBillTotalDTO billTotalDTO = hrBillDTO.getHrBillTotal();
        if (billTotalDTO != null) {
            // 获取旧的数据
            HrBillTotalDTO oldBillTotal = hrBillTotalMapper.toDto(hrBillTotalRepository.selectById(billTotalDTO.getId()));
            if (billTotalDTO.getIsModifyServiceFee() != null && billTotalDTO.getServiceFeeTotal() != null && billTotalDTO.getIsModifyServiceFee()) {
                if (!BigDecimalCompare.of(oldBillTotal.getServiceFeeTotal()).eq(billTotalDTO.getServiceFeeTotal())) {
                    flag = true;
                }
            }
            if (billTotalDTO.getLastMonthMakeUp() != null) {
                if (!BigDecimalCompare.of(oldBillTotal.getLastMonthMakeUp()).eq(billTotalDTO.getLastMonthMakeUp())) {
                    flag = true;
                }
            }
        }
        Map<String, Object> hashMap = new HashMap<>();
        boolean reconciliationFlag = false;
        boolean salaryBillFlag = false;
        if (flag){
            HrBill hrBill = hrBillRepository.selectById(hrBillDTO.getId());
            //查询是否有生成批量对账单
            HrBillCompareResultDTO hrBillCompareResultDTO = new HrBillCompareResultDTO();
            hrBillCompareResultDTO.setTitle("全部公司");
            hrBillCompareResultDTO.setPayYear(hrBillDTO.getPayYear());
            hrBillCompareResultDTO.setPayMonthly(hrBillDTO.getPayMonthly());
            List<HrBillCompareResultDTO> billCompareResultDTOS = hrBillCompareResultRepository.findList(hrBillCompareResultDTO);
            if (CollectionUtils.isNotEmpty(billCompareResultDTOS)){
                reconciliationFlag = true;
            }

            //查询是否有生成薪酬账单
            QueryWrapper<HrBill> qw = new QueryWrapper<>();
            qw.eq("is_official",1);
            qw.eq("client_id",hrBillDTO.getClientId());
            qw.eq("pay_year",hrBillDTO.getPayYear());
            qw.eq("pay_monthly",hrBillDTO.getPayMonthly());
            qw.eq("bill_type",BillEnum.BillType.SALARY_BILL.getKey());
            qw.ge("created_date",hrBill.getCreatedDate());
            List<HrBill> billList = hrBillRepository.selectList(qw);
            if (CollectionUtils.isNotEmpty(billList)){
                salaryBillFlag = true;
                List<String> billIdList = billList.stream().map(HrBill::getId).collect(Collectors.toList());
                hashMap.put("salaryBillIdList",billIdList);
            }
        }
        hashMap.put("reconciliationFlag",reconciliationFlag);
        hashMap.put("salaryBillFlag",false);
        return ResponseUtil.buildSuccess(hashMap);
    }

    /**
     * 工资发放分页列表
     * @param hrBillDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrSalaryPaymentDTO> findSalaryPaymentPage(HrBillDTO hrBillDTO, Long pageNumber, Long pageSize) {
        Page<HrSalaryPaymentDTO> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        IPage<HrSalaryPaymentDTO> iPage = this.hrBillRepository.findSalaryPaymentPage(page, hrBillDTO, clientIds);
        return iPage;
    }

    /**
     * 工资发放导出
     * @param hrBillDTO
     * @param response
     * @return
     */
    @Override
    public String salaryPaymentBankReportExport(HrBillDTO hrBillDTO, HttpServletResponse response) {
        List<File> fileList = new ArrayList<>();
        // 若有选定数据，导出选中的数据，若无选中数据，导出全部符合筛选条件的数据
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        // 更新账单为已导出状态
        List<String> ids = hrBillDTO.getIds();
        hrBillDTO.setIds(null);
        List<HrSalaryPaymentDTO> hrSalaryPaymentDTOList = hrBillRepository.findSalaryPaymentList(hrBillDTO,clientIds);
        if (CollectionUtils.isNotEmpty(ids)){
            List<HrSalaryPaymentDTO> hrSalaryPaymentList = new ArrayList<>();
            for (String id : ids) {
                List<HrSalaryPaymentDTO> collect = hrSalaryPaymentDTOList.stream().filter(ls -> ls.getBillIds().contains(id)).collect(Collectors.toList());
                hrSalaryPaymentList.addAll(collect);
            }
            hrSalaryPaymentDTOList = hrSalaryPaymentList;
        }
        List<String> billIds = hrSalaryPaymentDTOList.stream().map(HrSalaryPaymentDTO::getBillIds).distinct().collect(Collectors.toList());
        List<String> billIdList = new ArrayList<>();
        billIds.forEach(ls->billIdList.addAll(Arrays.asList(ls.split(","))));
        this.hrBillRepository.updateSalaryExportState(billIdList, BillEnum.NormalBillExportState.EXPORTED.getKey());
        List<HrBankStatementExport> list = this.hrBillDetailRepository.exportSalaryPayment(billIdList, null);
        List<HrBankStatementExport> economicList = new ArrayList<>();
        //不在常用银行范围内的数据
        List<HrBankStatementExport> economicCollectList = list.stream().filter(lst -> lst.getOwnedBankLabel() == null || StaffEnum.OwnedBankEnum.getValueByKey(lst.getOwnedBankLabel()) == null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(economicCollectList)){
            economicList.addAll(economicCollectList);
        }
        for (StaffEnum.OwnedBankEnum ownedBankEnum : StaffEnum.OwnedBankEnum.class.getEnumConstants()) {
            List<HrBankStatementExport> hrBankStatementExports = list.stream().filter(lst -> lst.getOwnedBankLabel() != null && lst.getOwnedBankLabel().contains(ownedBankEnum.getValue())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hrBankStatementExports)){
                switch (ownedBankEnum){
                    case CHINA_MERCHANTS_BANK:
                        List<ExcelExportEntity> excelHeader1 = new ArrayList<>();
                        List<Map<String, Object>> paramsList1 = new ArrayList<>();
                        for (HrBankStatementExport hrBankStatementExport : hrBankStatementExports) {
                            Map<String, Object> param = new HashMap<>();
                            excelHeader1.add(new ExcelExportEntity("户名", "name", 10));
                            param.put("name", hrBankStatementExport.getName());
                            excelHeader1.add(new ExcelExportEntity("账号", "salaryCardNum", 25));
                            param.put("salaryCardNum", hrBankStatementExport.getSalaryCardNum());
                            ExcelExportEntity economicConstructionTemp = new ExcelExportEntity("金额", "realSalary", 10);
                            economicConstructionTemp.setType(10);
                            excelHeader1.add(economicConstructionTemp);
                            param.put("realSalary", hrBankStatementExport.getRealSalary() == null ? BigDecimal.ZERO : hrBankStatementExport.getRealSalary());
                            excelHeader1.add(new ExcelExportEntity("参考号", "referenceNum", 13));
                            excelHeader1.add(new ExcelExportEntity("交易摘要", "summary", 13));
                            paramsList1.add(param);
                        }
                        List<ExcelExportEntity> collect1 = excelHeader1.stream().distinct().collect(Collectors.toList());
                        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicGenerationExportLocal(ownedBankEnum.getValue(),null,"Sheet1", collect1, paramsList1, fileTempPath, null,0))));
                        break;
                    case INDUSTRIAL_MERCHANTS_BANK:
                        List<ExcelExportEntity> excelHeader2 = new ArrayList<>();
                        List<Map<String, Object>> paramsList2 = new ArrayList<>();
                        // 表头
                        excelHeader2.add(new ExcelExportEntity("付款账号名称/卡名称", "paymentAccountName", 30));
                        excelHeader2.add(new ExcelExportEntity("付款账号/卡号", "paymentAccount", 25));
                        excelHeader2.add(new ExcelExportEntity("收款账号名称", "name", 20)); // 员工姓名
                        excelHeader2.add(new ExcelExportEntity("收款账号", "salaryCardNum", 25));
                        ExcelExportEntity economicConstructionTemp2 = new ExcelExportEntity("金额", "realSalary", 10);
                        economicConstructionTemp2.setType(10);
                        excelHeader2.add(economicConstructionTemp2);
                        excelHeader2.add(new ExcelExportEntity("汇款用途", "purpose", 10));
                        excelHeader2.add(new ExcelExportEntity("顺序号", "index", 10));
                        excelHeader2.add(new ExcelExportEntity("收款账号地区码", "area", 15));
                        excelHeader2.add(new ExcelExportEntity("付方账号是否他行账号", "isOtherBankAccount", 20));
                        excelHeader2.add(new ExcelExportEntity("备注信息", "remark", 20));
                        excelHeader2.add(new ExcelExportEntity("收款账户短信通知手机号码", "phone", 25));
                        excelHeader2.add(new ExcelExportEntity("报销号", "reimbursementNum", 20));
                        excelHeader2.add(new ExcelExportEntity("单据张数", "count", 20));
                        excelHeader2.add(new ExcelExportEntity("明细标志", "detailFlag", 20));
                        excelHeader2.add(new ExcelExportEntity("日期", "date", 20));
                        for (HrBankStatementExport hrBankStatementExport : hrBankStatementExports) {
                            Map<String, Object> param = new HashMap<>();
                            param.put("paymentAccountName", CompanyInfoEnum.FIRST_PART_NAME.getValue());
                            param.put("paymentAccount", "37101988141051001067");
                            param.put("name", hrBankStatementExport.getName());
                            param.put("salaryCardNum", hrBankStatementExport.getSalaryCardNum());
                            param.put("realSalary", hrBankStatementExport.getRealSalary() == null ? BigDecimal.ZERO : hrBankStatementExport.getRealSalary());
                            param.put("purpose", "工资");
                            paramsList2.add(param);
                        }
                        List<ExcelExportEntity> collect2 = excelHeader2.stream().distinct().collect(Collectors.toList());
                        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicGenerationExportLocal(ownedBankEnum.getValue(),null,"Sheet1", collect2, paramsList2, fileTempPath, null,0))));
                        break;
                    case COMMUNICATIONS_BANK:
                        List<ExcelExportEntity> excelHeader3 = new ArrayList<>();
                        List<Map<String, Object>> paramsList3 = new ArrayList<>();
                        for (HrBankStatementExport hrBankStatementExport : hrBankStatementExports) {
                            Map<String, Object> param = new HashMap<>();
                            excelHeader3.add(new ExcelExportEntity("账号", "salaryCardNum", 25));
                            param.put("salaryCardNum", hrBankStatementExport.getSalaryCardNum());
                            excelHeader3.add(new ExcelExportEntity("姓名", "name", 15));
                            param.put("name", hrBankStatementExport.getName());
                            ExcelExportEntity economicConstructionTemp = new ExcelExportEntity("金额", "realSalary", 20);
                            economicConstructionTemp.setType(10);
                            excelHeader3.add(economicConstructionTemp);
                            param.put("realSalary", hrBankStatementExport.getRealSalary() == null ? BigDecimal.ZERO : hrBankStatementExport.getRealSalary());
                            excelHeader3.add(new ExcelExportEntity("备注（附言）", "remark", 20));
                            param.put("remark", "工资");
                            excelHeader3.add(new ExcelExportEntity("空", "emptyOne", 10));
                            excelHeader3.add(new ExcelExportEntity("空", "emptyTwo", 10));
                            excelHeader3.add(new ExcelExportEntity("是否", "whether", 15));
                            param.put("whether", "否");
                            paramsList3.add(param);
                        }
                        List<ExcelExportEntity> collect3 = excelHeader3.stream().distinct().collect(Collectors.toList());
                        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicGenerationExportLocal(ownedBankEnum.getValue(),null,"代发登记表", collect3, paramsList3, fileTempPath, hrBankStatementExports.size(),1))));
                        break;
                    case AGRICULTURAL_BANK:
                        List<ExcelExportEntity> excelHeader4 = new ArrayList<>();
                        List<Map<String, Object>> paramsList4 = new ArrayList<>();
                        int i = 0;
                        for (HrBankStatementExport hrBankStatementExport : hrBankStatementExports) {
                            i = i + 1;
                            Map<String, Object> param = new HashMap<>();
                            excelHeader4.add(new ExcelExportEntity("编号", "number", 10));
                            param.put("number", i);
                            excelHeader4.add(new ExcelExportEntity("收款方账号", "salaryCardNum", 25));
                            param.put("salaryCardNum", hrBankStatementExport.getSalaryCardNum());
                            excelHeader4.add(new ExcelExportEntity("收款方户名", "name", 13));
                            param.put("name", hrBankStatementExport.getName());
                            ExcelExportEntity economicConstructionTemp = new ExcelExportEntity("金额", "realSalary", 10);
                            economicConstructionTemp.setType(10);
                            excelHeader4.add(economicConstructionTemp);
                            param.put("realSalary", hrBankStatementExport.getRealSalary() == null ? BigDecimal.ZERO : hrBankStatementExport.getRealSalary());
                            excelHeader4.add(new ExcelExportEntity("备注（附言）", "remark", 15));
                            param.put("remark", "工资");
                            paramsList4.add(param);
                        }
                        List<ExcelExportEntity> collect4 = excelHeader4.stream().distinct().collect(Collectors.toList());
                        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicGenerationExportLocal(ownedBankEnum.getValue(),"中国农业银行代发工资（农行）上传文件","Sheet1", collect4, paramsList4, fileTempPath, null,0))));
                        break;
                    case PUDONG_DEVELOPMENT_BANK:
                        List<ExcelExportEntity> excelHeader5 = new ArrayList<>();
                        List<Map<String, Object>> paramsList5 = new ArrayList<>();
                        int num = 0;
                        for (HrBankStatementExport hrBankStatementExport : hrBankStatementExports) {
                            num = num + 1;
                            Map<String, Object> param = new HashMap<>();
                            excelHeader5.add(new ExcelExportEntity("卡号", "salaryCardNum", 25));
                            param.put("salaryCardNum", hrBankStatementExport.getSalaryCardNum());
                            ExcelExportEntity economicConstructionTemp = new ExcelExportEntity("金额", "realSalary",10);
                            economicConstructionTemp.setType(10);
                            excelHeader5.add(economicConstructionTemp);
                            param.put("realSalary", hrBankStatementExport.getRealSalary() == null ? BigDecimal.ZERO : hrBankStatementExport.getRealSalary());
                            excelHeader5.add(new ExcelExportEntity("姓名", "name", 10));
                            param.put("name", hrBankStatementExport.getName());
                            excelHeader5.add(new ExcelExportEntity("编号", "number", 10));
                            param.put("number", String.format("%04d", num));
                            excelHeader5.add(new ExcelExportEntity("备注", "remark", 15));
                            param.put("remark", "工资");
                            paramsList5.add(param);
                        }
                        List<ExcelExportEntity> collect5 = excelHeader5.stream().distinct().collect(Collectors.toList());
                        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicGenerationExportLocal(ownedBankEnum.getValue(),null,"Sheet1", collect5, paramsList5, fileTempPath,hrBankStatementExports.size(),null))));
                        break;
                    case AGRIBUSINESS_BANK:
                        List<ExcelExportEntity> excelHeader6 = new ArrayList<>();
                        List<Map<String, Object>> paramsList6 = new ArrayList<>();
                        for (HrBankStatementExport hrBankStatementExport : hrBankStatementExports) {
                            Map<String, Object> param = new HashMap<>();
                            ExcelExportEntity economicConstructionTemp = new ExcelExportEntity("金额", "realSalary",10);
                            economicConstructionTemp.setType(10);
                            excelHeader6.add(economicConstructionTemp);
                            param.put("realSalary", hrBankStatementExport.getRealSalary() == null ? BigDecimal.ZERO : hrBankStatementExport.getRealSalary());
                            excelHeader6.add(new ExcelExportEntity("名称", "name", 10));
                            param.put("name", hrBankStatementExport.getName());
                            excelHeader6.add(new ExcelExportEntity("账号", "salaryCardNum", 25));
                            param.put("salaryCardNum", hrBankStatementExport.getSalaryCardNum());
                            paramsList6.add(param);
                        }
                        List<ExcelExportEntity> collect6 = excelHeader6.stream().distinct().collect(Collectors.toList());
                        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicGenerationExportLocal(ownedBankEnum.getValue(),null,"Sheet2", collect6, paramsList6, fileTempPath,null,null))));
                        break;
                    case QINGDAO_BANK:
                        List<ExcelExportEntity> excelHeader7 = new ArrayList<>();
                        List<Map<String, Object>> paramsList7 = new ArrayList<>();
                        for (HrBankStatementExport hrBankStatementExport : hrBankStatementExports) {
                            Map<String, Object> param = new HashMap<>();
                            excelHeader7.add(new ExcelExportEntity("姓名", "name", 10));
                            param.put("name", hrBankStatementExport.getName());
                            excelHeader7.add(new ExcelExportEntity("卡号/账号", "salaryCardNum", 25));
                            param.put("salaryCardNum", hrBankStatementExport.getSalaryCardNum());
                            ExcelExportEntity economicConstructionTemp = new ExcelExportEntity("实发金额", "realSalary",10);
                            economicConstructionTemp.setType(10);
                            excelHeader7.add(economicConstructionTemp);
                            param.put("realSalary", hrBankStatementExport.getRealSalary() == null ? BigDecimal.ZERO : hrBankStatementExport.getRealSalary());
                            paramsList7.add(param);
                        }
                        List<ExcelExportEntity> collect7 = excelHeader7.stream().distinct().collect(Collectors.toList());
                        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicGenerationExportLocal(ownedBankEnum.getValue(),null,"Sheet1", collect7, paramsList7, fileTempPath,null,null))));
                        break;
                    case CHINA_BANK:
                        List<ExcelExportEntity> excelHeader8 = new ArrayList<>();
                        List<Map<String, Object>> paramsList8 = new ArrayList<>();
                        for (HrBankStatementExport hrBankStatementExport : hrBankStatementExports) {
                            Map<String, Object> param = new HashMap<>();

                            excelHeader8.add(new ExcelExportEntity("账号", "salaryCardNum", 25));
                            param.put("salaryCardNum", hrBankStatementExport.getSalaryCardNum());
                            excelHeader8.add(new ExcelExportEntity("户名", "name", 10));
                            param.put("name", hrBankStatementExport.getName());
                            ExcelExportEntity economicConstructionTemp = new ExcelExportEntity("金额", "realSalary");
                            economicConstructionTemp.setType(10);
                            excelHeader8.add(economicConstructionTemp);
                            param.put("realSalary", hrBankStatementExport.getRealSalary() == null ? BigDecimal.ZERO : hrBankStatementExport.getRealSalary());
                            excelHeader8.add(new ExcelExportEntity("账号所属省份", "province", 15));
                            excelHeader8.add(new ExcelExportEntity("证件类型", "idType", 13));
                            excelHeader8.add(new ExcelExportEntity("证件号码", "idNum", 13));
                            excelHeader8.add(new ExcelExportEntity("用途", "purpose", 10));
                            excelHeader8.add(new ExcelExportEntity("附言", "postscript", 10));
                            paramsList8.add(param);
                        }
                        List<ExcelExportEntity> collect8 = excelHeader8.stream().distinct().collect(Collectors.toList());
                        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicGenerationExportLocal(ownedBankEnum.getValue(),null,"工资发放批量文件-行内", collect8, paramsList8, fileTempPath,null,2))));
                        break;
                    default:
                        economicList.addAll(hrBankStatementExports);
                        break;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(economicList)){
            this.createConstructionBank(economicList,fileList);
        }
        int listSize = hrSalaryPaymentDTOList.size();
        String fileUrl = this.hrAppendixService.zipAndUploadFile(fileList, "工资发放银行报盘");
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.SALARY_PAYMENT_BANK_REPORT.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(billIds), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 工资发放导出
     *
     * @param hrBillDTO
     * @param response
     * @return
     */
    @Override
    public String exportDataList(HrBillDTO hrBillDTO, HttpServletResponse response) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrSalaryPaymentDTO> list = this.hrBillRepository.findSalaryPaymentList(hrBillDTO, clientIds);
        int listSize = list.size();
        List<String> ids = list.stream().map(HrSalaryPaymentDTO::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "工资发放", HrSalaryPaymentDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.SALARY_PAYMENT.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 导出建设银行模板
     * @param hrBankStatementExports
     * @param fileList
     */
    private void createConstructionBank(List<HrBankStatementExport> hrBankStatementExports, List<File> fileList) {
        List<ExcelExportEntity> excelHeader9 = new ArrayList<>();
        List<Map<String, Object>> paramsList9 = new ArrayList<>();
        for (HrBankStatementExport hrBankStatementExport : hrBankStatementExports) {
            Map<String, Object> param = new HashMap<>();
            excelHeader9.add(new ExcelExportEntity("卡号", "salaryCardNum", 25));
            param.put("salaryCardNum", hrBankStatementExport.getSalaryCardNum());
            excelHeader9.add(new ExcelExportEntity("姓名", "name", 10));
            param.put("name", hrBankStatementExport.getName());
            ExcelExportEntity economicConstructionTemp = new ExcelExportEntity("金额", "realSalary",10);
            economicConstructionTemp.setType(10);
            excelHeader9.add(economicConstructionTemp);
            param.put("realSalary", hrBankStatementExport.getRealSalary() == null ? BigDecimal.ZERO : hrBankStatementExport.getRealSalary());
            paramsList9.add(param);
        }
        List<ExcelExportEntity> collect9 = excelHeader9.stream().distinct().collect(Collectors.toList());
        fileList.add(new File(Objects.requireNonNull(ExcelUtils.dynamicGenerationExportLocal("建设银行",null,"Sheet0", collect9, paramsList9, fileTempPath,hrBankStatementExports.size(),null))));
    }

    /**
     * 查询重复表单数据
     * @param hrBillDTO 账单
     * @return
     */
    @Override
    public ResponseEntity<?> queryDuplicateFormData(HrBillDTO hrBillDTO) {
        JSONObject jsonObject = new JSONObject();
        List<HrBillDetailDTO> billDetailList = hrBillDTO.getBillDetailList();
        if (CollectionUtils.isEmpty(billDetailList)){
            return ResponseUtil.buildSuccess(jsonObject);
        }
        List<String> expenseNameNew = hrBillDetailItemsRepository.getBillDynamicItemsByBillDetailId(billDetailList.stream().map(HrBillDetailDTO::getId).collect(Collectors.toList()));
        //查询该年度该公司的所有账单--除本账单
        QueryWrapper<HrBill> qw = new QueryWrapper<>();
        if (CollectionUtils.isNotEmpty(hrBillDTO.getBillIdList())){
            qw.notIn("id",hrBillDTO.getBillIdList());
        }else {
            qw.ne("id",hrBillDTO.getId());
        }
        qw.eq("bill_type",BillEnum.BillType.SALARY_BILL.getKey());
        qw.eq("pay_year",hrBillDTO.getPayYear());
        qw.eq("pay_monthly",hrBillDTO.getPayMonthly());
        if (CollectionUtils.isNotEmpty(hrBillDTO.getClientIdList())){
            qw.in("client_id",hrBillDTO.getClientIdList());
        }else {
            qw.eq("client_id",hrBillDTO.getClientId());
        }
        qw.eq("is_official",1);
        qw.eq("is_delete",0);
        List<HrBill> hrBills = hrBillRepository.selectList(qw);
        if (hrBills == null || hrBills.isEmpty()){
            return ResponseUtil.buildSuccess(jsonObject);
        }
//        List<String> staffIds = billDetailList.stream().map(HrBillDetailDTO::getStaffId).collect(Collectors.toList());
        List<String> billIds = hrBills.stream().map(HrBill::getId).collect(Collectors.toList());
        List<HrBillDetailDTO> hrBillDetailDTOList = hrBillDetailRepository.getListByBillIdAndStaffId(billIds, null, 1);
        List<String> billTitle = new ArrayList<>();
        for (HrBill hrBill : hrBills) {
            List<HrBillDetailDTO> billDetailDTOS = hrBillDetailDTOList.stream().filter(ls -> ls.getBillId().equals(hrBill.getId())).collect(Collectors.toList());
            if (billDetailDTOS.isEmpty()){
                continue;
            }else {
                List<String> expenseNameOld = hrBillDetailItemsRepository.getBillDynamicItemsByBillDetailId(billDetailDTOS.stream().map(HrBillDetailDTO::getId).distinct().collect(Collectors.toList()));
                int sizeOld = expenseNameOld.size();
                expenseNameOld.removeAll(expenseNameNew);
                if (expenseNameOld.size() != sizeOld){
                    billTitle.add(hrBill.getTitle());
                    continue;
                }
            }
        }
        jsonObject.put("title",String.join(",",billTitle));
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 全年一次性奖金计税
     * @param hrBillDTO
     * @return
     */
    @Override
    public List<HrBillDetailDTO> annualLumpSumBonusTax(HrBillDTO hrBillDTO) {
        List<String> staffIds = hrBillDTO.getBillDetailList().stream().map(HrBillDetailDTO::getStaffId).distinct().collect(Collectors.toList());
        List<String> ids = hrBillDTO.getBillDetailList().stream().map(HrBillDetailDTO::getId).distinct().collect(Collectors.toList());
        QueryWrapper<HrBillDetail> qw = new QueryWrapper<>();
        qw.eq("is_delete",0);
        qw.eq("is_used",1);
        qw.eq("tax_calculation_method",1);
        qw.eq("pay_year",hrBillDTO.getPayYear());
        qw.in("staff_id",staffIds);
        qw.notIn("id",ids);
        List<HrBillDetail> hrBillDetails = hrBillDetailRepository.selectList(qw);
        return hrBillDetailMapper.toDto(hrBillDetails);
    }

    /**
     * 全年一次性奖金分页列表
     * @param hrBillDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrAnnualBonusDTO> findAnnualLumpSumBonusPage(HrBillDTO hrBillDTO, Long pageNumber, Long pageSize) {
        Page<HrAnnualBonusDTO> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        return this.hrBillRepository.findAnnualLumpSumBonusPage(page, hrBillDTO, clientIds);
    }

    /**
     * 全年一次性奖金导出
     * @param hrBillDTO
     * @return
     */
    @Override
    public String annualLumpSumBonusExportCheck(HrBillDTO hrBillDTO) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrAnnualBonusExport> hrAnnualBonusExports = this.hrBillDetailRepository.getBonusExportList(hrBillDTO,clientIds);
        if (hrAnnualBonusExports.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        // 更新账单为已导出状态
        List<String> ids = hrAnnualBonusExports.stream().map(HrAnnualBonusExport::getBillId).distinct().collect(Collectors.toList());
        this.hrBillRepository.updateBonusState(ids, BillEnum.NormalBillExportState.EXPORTED.getKey());
        int listSize = hrAnnualBonusExports.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(hrAnnualBonusExports, "全年一次性奖金", HrAnnualBonusExport.class);
        // 操作日志
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.ANNUAL_LUMP_SUM_BONUS.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    private HrMakeUpUseRecord getMakeUpUseRecord(HrBillDetail billDetail, HrWelfareCompensation compensation) {
        HrMakeUpUseRecord makeUpUseRecord = new HrMakeUpUseRecord();
        makeUpUseRecord.setMakeUpId(compensation.getId());
        makeUpUseRecord.setBillId(billDetail.getBillId());
        makeUpUseRecord.setStaffId(billDetail.getStaffId());
        makeUpUseRecord.setBillDetailId(billDetail.getId());
        return makeUpUseRecord;
    }

    /**
     * 生成账单基础信息
     *
     * @param hrBillDTO
     */
    private HrBillDTO generateBasicBill(HrBillDTO hrBillDTO) {
        if (hrBillDTO.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())) {
            // 验证费用年月的账单是否已经存在
            HrBill findOne = hrBillRepository.getBillByPaymentDate(hrBillDTO.getClientId(), hrBillDTO.getPayYear(), hrBillDTO.getPayMonthly());
            if (findOne != null) {
                HrClient hrClient = hrClientService.getById(findOne.getClientId());
                throw new CommonException(hrClient.getClientName() + "该费用年月的账单已经存在");
            }
        } else if (hrBillDTO.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            int count = this.hrBillRepository.getBillCountByPaymentDate(hrBillDTO);
            if (count > 0) {
                throw new CommonException("该客户在该年月已经存在中石化费用统计账单！");
            }
        }
        HrBillDTO billParams = hrBillRepository.getBillParamsByClientId(hrBillDTO.getClientId());
        // 验证社保参数是否存在
        if (!hrBillDTO.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            if (billParams.getUnitPensionScale() == null || billParams.getWorkInjuryScale() == null || billParams.getUnitUnemploymentScale() == null
                || billParams.getUnitMedicalScale() == null || billParams.getPersonalPensionScale() == null || billParams.getPersonalUnemploymentScale() == null
                || billParams.getPersonalMedicalScale() == null || billParams.getUnitMaternityScale() == null || billParams.getPersonalMaternityScale() == null) {
                throw new CommonException("客户" + billParams.getClientName() + "的养老、失业、工伤、医疗、生育比例未设置！");
            }
            // 验证公积金参数是否存在
            if (billParams.getUnitAccumulationFundScale() == null || billParams.getPersonalAccumulationFundScale() == null) {
                throw new CommonException("该客户" + billParams.getClientName() + "的公积金比例未设置！");
            }
        }
        // 如果标题为空，设置默认标题 比如xx客户1月份保障账单
        String billTypeName = BillEnum.BillType.getValueByKey(hrBillDTO.getBillType());
        hrBillDTO.setTitle(billParams.getClientName() + hrBillDTO.getPayMonthly() + "月份" + billTypeName);
        hrBillDTO.setUnitPensionScale(billParams.getUnitPensionScale());
        hrBillDTO.setWorkInjuryScale(billParams.getWorkInjuryScale());
        hrBillDTO.setUnitUnemploymentScale(billParams.getUnitUnemploymentScale());
        hrBillDTO.setUnitMedicalScale(billParams.getUnitMedicalScale());
        hrBillDTO.setPersonalPensionScale(billParams.getPersonalPensionScale());
        hrBillDTO.setPersonalUnemploymentScale(billParams.getPersonalUnemploymentScale());
        hrBillDTO.setPersonalMedicalScale(billParams.getPersonalMedicalScale());
        hrBillDTO.setUnitAccumulationFundScale(billParams.getUnitAccumulationFundScale());
        hrBillDTO.setPersonalAccumulationFundScale(billParams.getPersonalAccumulationFundScale());
        hrBillDTO.setUnitMaternityScale(billParams.getUnitMaternityScale());
        hrBillDTO.setPersonalMaternityScale(billParams.getPersonalMaternityScale());
        hrBillDTO.setBillState(BillEnum.BillState.NOT_LOCKED.getKey());
        if (hrBillDTO.getIsOfficial() == null) {
            hrBillDTO.setIsOfficial(0);
        }
        return hrBillDTO;
    }

    /**
     * 保存账单信息 入库
     *
     * @param hrBillDTO
     * @return
     */
    private HrBillDTO saveBasicBill(HrBillDTO hrBillDTO) {
        HrBill hrBill = this.hrBillMapper.toEntity(hrBillDTO);
        // 设置更新时间和创建时间
        LocalDateTime dateTime = LocalDateTime.now();
        hrBill.setCreatedDate(dateTime);
        hrBill.setLastModifiedDate(dateTime);
        this.hrBillRepository.insert(hrBill);
        return this.hrBillMapper.toDto(hrBill);
    }

    /**
     * 创建账单基础信息
     *
     * @param hrBillDTO
     */
    @Override
    public HrBillDTO createBasicBill(HrBillDTO hrBillDTO) {

        if (hrBillDTO.getFlag() != null && hrBillDTO.getFlag()) {
            this.generateBasicBill(hrBillDTO);
            return this.saveBasicBill(hrBillDTO);
        }

        if (hrBillDTO.getUsedClientIdList().isEmpty()) {
            throw new CommonException("没有可创建账单的客户！");
        }
        List<String> clientIdList = hrBillDTO.getUsedClientIdList();
        List<String> usedClientIdList = hrBillDTO.getUsedClientIdList();

        // 中石化费用统计账单
        if (hrBillDTO.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            int count = this.hrBillRepository.getBillCountByPaymentDate(hrBillDTO);
            if (count > 0) {
                throw new CommonException("该客户在该年月已经存在中石化费用统计账单！");
            }
        }
        // 特殊处理客户
        SpecialBillClient specialBillClient = judgeSocialBillClient(clientIdList);
        if (specialBillClient != null) {
            // 政法委每月只能创建一个薪酬账单
            log.info("特殊客户：{}", specialBillClient);
            if (specialBillClient.equals(SpecialBillClient.EAST_DISTRICT_PUBLIC_SECURITY) || specialBillClient.equals(SpecialBillClient.SECONDARY_POLITICS_LAW_COMMITTEE)) {
                int count = this.hrBillRepository.getBillCountByPaymentDate(hrBillDTO);
                if (count > 0) {
                    throw new CommonException("特殊处理客户每月只能创建一条薪酬账单！");
                }
            }
        }

        List<String> billIdList = new ArrayList<>();
        if (!usedClientIdList.contains(hrBillDTO.getClientId())) {
            usedClientIdList.add(hrBillDTO.getClientId());
        }
        String dtoClientId = hrBillDTO.getClientId();
        String billNo = "";
        if (hrBillDTO.getBillType() == 0) {
            billNo = "XC";
        } else if (hrBillDTO.getBillType() == 1) {
            billNo = "BZ";
        } else if (hrBillDTO.getBillType() == 2) {
            billNo = "OT";
        } else if (hrBillDTO.getBillType() == 3) {
            billNo = "ZSH";
        }
        String suffix = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
        int random = (int)(Math.random()*1000 + 1);
        billNo = billNo + suffix + random;
        hrBillDTO.setOptClientId(hrBillDTO.getClientId());
        hrBillDTO.setBillNo(billNo);
        hrBillDTO.setOptTitle(hrBillDTO.getTitle());
        for (String clientId : usedClientIdList) {
            hrBillDTO.setIsChoice(clientId.equals(dtoClientId));
            hrBillDTO.setClientId(clientId);
            this.generateBasicBill(hrBillDTO);
            HrBillDTO billDTO = this.saveBasicBill(hrBillDTO);
            billIdList.add(billDTO.getId());
        }

        QueryWrapper<HrBill> qw = new QueryWrapper<>();
        qw.in("id", billIdList);
        qw.eq("is_choice", 1);
        qw.last("LIMIT 1");
        HrBill selectOne = hrBillRepository.selectOne(qw);
        HrBillDTO billDTO = hrBillMapper.toDto(selectOne);
        billDTO.setBillIdList(billIdList);
        billDTO.setClientIdList(clientIdList);
        return billDTO;
    }

    /**
     * 填充账单明细部分参数,主要是社保公积金等
     * 若该方法有修改则同时修改 hrBillDetailServiceImpl-batchAddHrBillDetail
     *
     * @param billDetail 账单明细
     * @param hrBill     账单基础信息
     * @param staffIds   确认入账员工ID
     * @param billDetailByBill 同月份保障账单
     * @param hrBillDetailDTOS 同月份薪酬账单
     * @param welfareCompensations 补差
     * @return
     */
    @Override
    public HrBillDetailDTO fillHrBillDetail(HrBillDetailDTO billDetail, HrBillDTO hrBill, List<String> staffIds, HrSocialSecurityDTO socialSecurityDTO, List<HrBillCompareResultDTO> hrBillCompareResultDTOList,
                                            List<HrBillDetailDTO> billDetailByBill, List<HrBillDetailDTO> hrBillDetailDTOS, List<HrWelfareCompensation> welfareCompensations, int lastPayYear, int lastPayMonthly) {
        // 判断该员工是否可以入账
        boolean billUsed = false;
        String reason = "";
        String staffId = billDetail.getStaffId();
        int staffStatus = billDetail.getStaffStatus();
        int insuredState = billDetail.getIzInsured();

        // 已参保
        if (insuredState == StaffEnum.InsuredStatusEnum.INSURED.getKey()) {
            StaffEnum.StaffStatusEnum staffStatusEnum = EnumUtils.getEnumByKey(StaffEnum.StaffStatusEnum.class, staffStatus);
            switch (staffStatusEnum) {
                // 待入职
                case TO_BE_HIRED:
                    reason = "员工状态处于待入职";
                    billUsed = false;
                    break;
                // 入职中
                case ENROLLING:
                    reason = "员工状态处于入职中";
                    billUsed = false;
                    break;
                // 产假期
                case MATERNITY_LEAVE:
                    reason = "员工状态处于产假中";
                    billUsed = false;
                    break;
                // 离职中
                case STAY_CONFIRM_RESIGNATION_LEAVING:
                    reason = "员工状态处于离职中";
                    billUsed = false;
                    break;
                // 待确认离职
                case STAY_CONFIRM_RESIGNATION:
                    reason = "员工状态处于待确认离职";
                    billUsed = false;
                    break;
                // 离职
                case SEPARATION:
                    reason = "员工状态处于离职";
                    billUsed = false;
                    break;
                // 退休
                case RETIRE:
                    reason = "员工状态处于退休";
                    billUsed = false;
                    break;
                default:
                    billUsed = true;
                    break;
            }
        }
        // 未参保
        else {
            reason = "该员工未参保";
        }
        billDetail.setCheckOn(0);
        if (CollectionUtils.isNotEmpty(staffIds) && staffIds.contains(staffId)) {
            billUsed = true;
            billDetail.setCheckOn(1);
        }

        // 计算单个账单明细的金额
        billDetail.setBillId(hrBill.getId());
        billDetail.setPayYear(hrBill.getPayYear());
        billDetail.setPayMonthly(hrBill.getPayMonthly());
        List<HrWelfareCompensation> welfareCompensationList = new ArrayList<>();
        billDetail.setBillUsed(billUsed);
        // 可入账员工
        if (billUsed) {
            billDetail.setIsUsed(true);
            if (hrBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())) {
                // 验证社保参数是否存在
                /*if (billDetail.getUnitPensionCardinal() == null
                    || billDetail.getUnitUnemploymentCardinal() == null
                    || billDetail.getWorkInjuryCardinal() == null
                    || billDetail.getUnitMaternityCardinal() == null
                    || billDetail.getMedicalInsuranceCardinal() == null
                    || billDetail.getPersonalPensionCardinal() == null
                    || billDetail.getPersonalUnemploymentCardinal() == null
                    || billDetail.getMedicalInsuranceCardinalPersonal() == null
                    || billDetail.getAccumulationFundCardinal() == null
                ){
                    throw new CommonException("该员工薪酬参数设置不全！");
                }*/
                // 计算员工缴费年月与账单年月相差月数
                int emolumentMonthNum;
                if (billDetail.getSupplementaryPayment() == null || billDetail.getSupplementaryPayment() == 0) {
                    emolumentMonthNum = 0;
                } else {
                    if (hrBill.getPayYear().equals(billDetail.getEmolumentPayYear())) {
                        emolumentMonthNum = hrBill.getPayMonthly() - billDetail.getEmolumentPayMonth();
                    } else {
                        emolumentMonthNum = 12 - (billDetail.getEmolumentPayMonth() - hrBill.getPayMonthly());
                    }
                }
                // 计算员工的补差 判断同月收支对账中是否存在不是当月使用补差的收支对账，如果存在，则不计算这部分的收支对账
                if (hrBillCompareResultDTOList != null && !hrBillCompareResultDTOList.isEmpty()){
                    List<HrBillCompareResultDTO> currentMonthResult = hrBillCompareResultDTOList.stream().filter(lst -> lst.getCurrentMonthUsed() == 0 && lst.getPayYear().equals(billDetail.getPayYear()) && lst.getPayMonthly().equals(billDetail.getPayMonthly())).collect(Collectors.toList());
                    List<String> resultIds = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(currentMonthResult)){
                        resultIds = currentMonthResult.stream().map(HrBillCompareResultDTO::getId).collect(Collectors.toList());
                    }
                    for (HrWelfareCompensation welfareCompensation : welfareCompensations) {
                        if (welfareCompensation.getIdNo().equals(billDetail.getCertificateNum())){
                            if (resultIds != null && !resultIds.isEmpty()){
                                if (!resultIds.contains(welfareCompensation.getBillResultId())){
                                    welfareCompensationList.add(welfareCompensation);
                                }
                            }else {
                                welfareCompensationList.add(welfareCompensation);
                            }
                        }
                    }
                }
                BigDecimal unitSocialSecurityMakeUp = BigDecimal.ZERO;
                BigDecimal personalSocialSecurityMakeUp = BigDecimal.ZERO;
                BigDecimal unitAccumulationFundMakeUp = BigDecimal.ZERO;
                BigDecimal personalAccumulationFundMakeUp = BigDecimal.ZERO;
                for (HrWelfareCompensation compensation : welfareCompensationList) {
                    unitSocialSecurityMakeUp = CalculateUtils.decimalAddition(unitSocialSecurityMakeUp, compensation.getUnitSubtotal());
                    personalSocialSecurityMakeUp = CalculateUtils.decimalAddition(personalSocialSecurityMakeUp, compensation.getPersonalSubtotal());
                    unitAccumulationFundMakeUp = CalculateUtils.decimalAddition(unitAccumulationFundMakeUp, compensation.getUnitAccumulationFund());
                    personalAccumulationFundMakeUp = CalculateUtils.decimalAddition(personalAccumulationFundMakeUp, compensation.getPersonalAccumulationFund());
                }
                if (socialSecurityDTO != null) {
                    this.fillDynamicCardinal(socialSecurityDTO, billDetail);
                }
                // 填充补差
                billDetail.setUnitSocialSecurityMakeUp(unitSocialSecurityMakeUp);
                billDetail.setPersonalSocialSecurityMakeUp(personalSocialSecurityMakeUp);
                billDetail.setUnitAccumulationFundMakeUp(unitAccumulationFundMakeUp);
                billDetail.setPersonalAccumulationFundMakeUp(personalAccumulationFundMakeUp);

                // 账单缴费年月与福利缴费年月相差月数为负值时,社保医保公积金置零
                if (emolumentMonthNum < 0) {
                    this.resetStaffBillDetail(billDetail);
                }
                // 按正常逻辑计算
                else if (emolumentMonthNum == 0) {
                    this.calStaffBillDetail(billDetail, hrBill, emolumentMonthNum);
                }
                // 社保医保公积金按对应月数补差
                else {
                    this.calStaffBillDetail(billDetail, hrBill, emolumentMonthNum);
                }

                // 计算各项的合计
                billDetail.setUnitSubtotal(CalculateUtils.decimalListAddition(billDetail.getUnitPension(), billDetail.getUnitUnemployment(), billDetail.getUnitMedical(),
                    billDetail.getWorkInjury(), billDetail.getUnitSocialSecurityMakeUp(), billDetail.getUnitMaternity(), billDetail.getUnitLargeMedicalExpense(),
                    billDetail.getReplenishWorkInjuryExpense(), billDetail.getUnitEnterpriseAnnuity(), billDetail.getCommercialInsurance()));
                billDetail.setPersonalSubtotal(CalculateUtils.decimalListAddition(billDetail.getPersonalPension(), billDetail.getPersonalUnemployment(), billDetail.getPersonalMedical(),
                    billDetail.getPersonalSocialSecurityMakeUp(), billDetail.getPersonalLargeMedicalExpense(), billDetail.getPersonalMaternity(), billDetail.getPersonalEnterpriseAnnuity()));
                billDetail.setSocialSecurityTotal(CalculateUtils.decimalListAddition(billDetail.getUnitSubtotal(), billDetail.getPersonalSubtotal()));
                billDetail.setAccumulationFundTotal(CalculateUtils.decimalListAddition(billDetail.getUnitAccumulationFund(), billDetail.getUnitAccumulationFundMakeUp(),
                    billDetail.getPersonalAccumulationFund(), billDetail.getPersonalAccumulationFundMakeUp()));

                // 服务费
                billDetail.setServiceFee(hrBill.getMonthlyServiceFee());
                billDetail.setTotal(CalculateUtils.decimalListAddition(billDetail.getSocialSecurityTotal(), billDetail.getAccumulationFundTotal(), billDetail.getServiceFee()));
                billDetail.setSalary(BigDecimal.ZERO);
            } else if (hrBill.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())) {
                //从对应的保障账单中获取数据
                billDetail.setClientId(hrBill.getClientId());
                this.getDataBySecurityBill(billDetail, billDetailByBill, hrBillDetailDTOS, welfareCompensations, hrBillCompareResultDTOList, lastPayYear, lastPayMonthly);
                welfareCompensationList = billDetail.getWelfareCompensationList() == null || billDetail.getWelfareCompensationList().isEmpty() ? new ArrayList<>() : billDetail.getWelfareCompensationList();
            }
        } else {
            billDetail.setIsUsed(false);
            billDetail.setReason(reason);
            billDetail.setServiceFee(BigDecimal.ZERO);
        }
        billDetail.setWelfareCompensationList(welfareCompensationList);
        return billDetail;
    }

    @Override
    public void calStaffBillDetail(HrBillDetailDTO billDetail, HrBillDTO hrBill, int emolumentMonthNum) {
        // 为了乘法计算翻倍
        int multiple = emolumentMonthNum + 1;
        billDetail.setEmolumentMultiple(multiple);
        // 社保单位缴纳部分
        billDetail.setUnitPension(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getUnitPensionCardinal(), hrBill.getUnitPensionScale(), 2)));
        billDetail.setUnitUnemployment(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getUnitUnemploymentCardinal(), hrBill.getUnitUnemploymentScale(), 2)));
        billDetail.setUnitMedical(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getMedicalInsuranceCardinal(), hrBill.getUnitMedicalScale(), 2)));
        billDetail.setWorkInjury(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getWorkInjuryCardinal(), hrBill.getWorkInjuryScale(), 2)));
        billDetail.setUnitMaternity(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getUnitMaternityCardinal(), hrBill.getUnitMaternityScale(), 2)));

        // 社保个人缴纳部分
        billDetail.setPersonalPension(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getPersonalPensionCardinal(), hrBill.getPersonalPensionScale(), 2)));
        billDetail.setPersonalUnemployment(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getPersonalUnemploymentCardinal(), hrBill.getPersonalUnemploymentScale(), 2)));
        billDetail.setPersonalMedical(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getMedicalInsuranceCardinalPersonal(), hrBill.getPersonalMedicalScale(), 2)));
        billDetail.setPersonalMaternity(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getPersonalMaternityCardinal(), hrBill.getPersonalMaternityScale(), 2)));

        // 公积金部分
        // 单位公积金 要求 保留整数 四舍五入
        billDetail.setUnitAccumulationFund(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getAccumulationFundCardinal(), hrBill.getUnitAccumulationFundScale(), 0)));
        // 个人公积金 要求 保留整数 四舍五入
        billDetail.setPersonalAccumulationFund(CalculateUtils.objectMultiply(multiple, CalculateUtils.decimalMultiply(billDetail.getAccumulationFundCardinal(), hrBill.getPersonalAccumulationFundScale(), 0)));
    }

    /**
     * 重置员工社保医保公积金为0
     *
     * @param billDetail
     */
    @Override
    public void resetStaffBillDetail(HrBillDetailDTO billDetail) {
        billDetail.setEmolumentMultiple(0);
        // 社保单位缴纳部分
        billDetail.setUnitPension(BigDecimal.ZERO);
        billDetail.setUnitUnemployment(BigDecimal.ZERO);
        billDetail.setUnitMedical(BigDecimal.ZERO);
        billDetail.setWorkInjury(BigDecimal.ZERO);
        billDetail.setUnitMaternity(BigDecimal.ZERO);
        // 社保个人缴纳部分
        billDetail.setPersonalPension(BigDecimal.ZERO);
        billDetail.setPersonalUnemployment(BigDecimal.ZERO);
        billDetail.setPersonalMedical(BigDecimal.ZERO);
        billDetail.setPersonalMaternity(BigDecimal.ZERO);
        // 公积金部分
        billDetail.setUnitAccumulationFund(BigDecimal.ZERO);
        billDetail.setPersonalAccumulationFund(BigDecimal.ZERO);
    }

    /**
     * 保险基数重新赋值
     *
     * @param socialSecurityDTO
     * @param billDetail
     */
    @Override
    public void fillDynamicCardinal(HrSocialSecurityDTO socialSecurityDTO, HrBillDetailDTO billDetail) {
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.UNIT_PENSION_CARDINAL.getFieldName(), socialSecurityDTO.getUnitPensionUpperLimit(), socialSecurityDTO.getUnitPensionLowerLimit(), billDetail.getUnitPensionCardinal(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT_CARDINAL.getFieldName(), socialSecurityDTO.getUnitUnemploymentUpperLimit(), socialSecurityDTO.getUnitUnemploymentLowerLimit(), billDetail.getUnitUnemploymentCardinal(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.UNIT_MATERNITY_CARDINAL.getFieldName(), socialSecurityDTO.getUnitMaternityUpperLimit(), socialSecurityDTO.getUnitMaternityLowerLimit(), billDetail.getUnitMaternityCardinal(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL.getFieldName(), socialSecurityDTO.getUnitMedicalUpperLimit(), socialSecurityDTO.getUnitMedicalLowerLimit(), billDetail.getMedicalInsuranceCardinal(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.WORK_INJURY_CARDINAL.getFieldName(), socialSecurityDTO.getWorkInjuryUpperLimit(), socialSecurityDTO.getWorkInjuryLowerLimit(), billDetail.getWorkInjuryCardinal(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.PERSONAL_PENSION_CARDINAL.getFieldName(), socialSecurityDTO.getPersonalPensionUpperLimit(), socialSecurityDTO.getPersonalPensionLowerLimit(), billDetail.getPersonalPensionCardinal(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT_CARDINAL.getFieldName(), socialSecurityDTO.getPersonalUnemploymentUpperLimit(), socialSecurityDTO.getPersonalUnemploymentLowerLimit(), billDetail.getPersonalUnemploymentCardinal(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.PERSONAL_MATERNITY_CARDINAL.getFieldName(), socialSecurityDTO.getPersonalMaternityUpperLimit(), socialSecurityDTO.getPersonalMaternityLowerLimit(), billDetail.getPersonalMaternityCardinal(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL_PERSONAL.getFieldName(), socialSecurityDTO.getPersonalMedicalUpperLimit(), socialSecurityDTO.getPersonalMedicalLowerLimit(), billDetail.getMedicalInsuranceCardinalPersonal(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.UNIT_LARGE_MEDICAL_EXPENSE.getFieldName(),null, null, billDetail.getUnitLargeMedicalExpense(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.PERSONAL_LARGE_MEDICAL_EXPENSE.getFieldName(),null, null, billDetail.getPersonalLargeMedicalExpense(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.REPLENISH_WORK_INJURY_EXPENSE.getFieldName(),null, null, billDetail.getReplenishWorkInjuryExpense(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.UNIT_ENTERPRISE_ANNUITY.getFieldName(),null, null, billDetail.getUnitEnterpriseAnnuity(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.PERSONAL_ENTERPRISE_ANNUITY.getFieldName(),null, null, billDetail.getPersonalEnterpriseAnnuity(), billDetail);
        this.handleCardinalValue(socialSecurityDTO, DynamicFeeTypesEnum.COMMERCIAL_INSURANCE.getFieldName(),null, null, billDetail.getCommercialInsurance(), billDetail);
    }

    private void handleCardinalValue(HrSocialSecurityDTO socialSecurityDTO, String key, BigDecimal upperLimit, BigDecimal lowerLimit, BigDecimal cardinal, HrBillDetailDTO billDetail) {
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> aloneCardinalDTOList = socialSecurityDTO.getAloneCardinalDTOList();
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> mergeCardinalDTOList = socialSecurityDTO.getMergeCardinalDTOList();
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> specialFieldDTOList = socialSecurityDTO.getSpecialFieldDTOList();
        HrSocialSecurityDTO.HrSocialHeadersDTO aloneHeadersDTO = aloneCardinalDTOList.stream().filter(lst -> lst.getFieldKey().equals(key)).findAny().orElse(null);
        HrSocialSecurityDTO.HrSocialHeadersDTO mergeHeadersDTO = mergeCardinalDTOList.stream().filter(lst -> lst.getFieldKeyList().contains(key)).findAny().orElse(null);
        HrSocialSecurityDTO.HrSocialHeadersDTO specialHeadersDTO = specialFieldDTOList.stream().filter(lst -> lst.getFieldKey().equals(key)).findAny().orElse(null);
        BigDecimal newCardinal = ( aloneHeadersDTO == null && mergeHeadersDTO == null && specialHeadersDTO == null) ? null : cardinal;
        if (lowerLimit != null && BigDecimalCompare.of(cardinal).le(lowerLimit)){
            newCardinal = lowerLimit;
        }
        if (upperLimit != null && BigDecimalCompare.of(cardinal).ge(upperLimit)){
            newCardinal = upperLimit;
        }
        DynamicFeeTypesEnum enumByKey = DynamicFeeTypesEnum.getEnumByFieldName(key);
        switch (enumByKey) {
            case UNIT_PENSION_CARDINAL: billDetail.setUnitPensionCardinal(newCardinal);break;
            case MEDICAL_INSURANCE_CARDINAL: billDetail.setMedicalInsuranceCardinal(newCardinal);break;
            case UNIT_UNEMPLOYMENT_CARDINAL: billDetail.setUnitUnemploymentCardinal(newCardinal);break;
            case WORK_INJURY_CARDINAL: billDetail.setWorkInjuryCardinal(newCardinal);break;
            case UNIT_MATERNITY_CARDINAL: billDetail.setUnitMaternityCardinal(newCardinal);break;
            case PERSONAL_PENSION_CARDINAL: billDetail.setPersonalPensionCardinal(newCardinal);break;
            case MEDICAL_INSURANCE_CARDINAL_PERSONAL: billDetail.setMedicalInsuranceCardinalPersonal(newCardinal);break;
            case PERSONAL_UNEMPLOYMENT_CARDINAL: billDetail.setPersonalUnemploymentCardinal(newCardinal);break;
            case PERSONAL_MATERNITY_CARDINAL: billDetail.setPersonalMaternityCardinal(newCardinal);break;
            case UNIT_LARGE_MEDICAL_EXPENSE: billDetail.setUnitLargeMedicalExpense(newCardinal);break;
            case PERSONAL_LARGE_MEDICAL_EXPENSE: billDetail.setPersonalLargeMedicalExpense(newCardinal);break;
            case REPLENISH_WORK_INJURY_EXPENSE: billDetail.setReplenishWorkInjuryExpense(newCardinal);break;
            case UNIT_ENTERPRISE_ANNUITY: billDetail.setUnitEnterpriseAnnuity(newCardinal);break;
            case PERSONAL_ENTERPRISE_ANNUITY: billDetail.setPersonalEnterpriseAnnuity(newCardinal);break;
            case COMMERCIAL_INSURANCE: billDetail.setCommercialInsurance(newCardinal);break;
            default:break;
        }
    }

    /**
     * 初始化服务费
     *
     * @param hrBill 账单基础
     * @param billDetailDTO 员工账单信息
     * @param clientType //0普通单位 1外包单位
     * @return
     */
    @Override
    public void initServiceFee(HrBillDTO hrBill, HrBillDetailDTO billDetailDTO, Integer clientType) {
        BigDecimal monthlyServiceFee = BigDecimal.ZERO;
        BigDecimal halfYearlyServiceFee = BigDecimal.ZERO;
        // 在协议表中  根据客户id和使用状态来查协议
        HrProtocol hrProtocol = this.checkProtocol(hrBill.getClientId());
        if (hrProtocol == null) {
            throw new CommonException("客户未签署任何协议!");
        }
        // 协议中的服务费状态（1.每人每月，2.打包价，3.半年支付）
        String serviceChargeType = hrProtocol.getServiceChargeType();
        BigDecimal serviceCharge = hrProtocol.getServiceCharge() != null ? hrProtocol.getServiceCharge() : BigDecimal.ZERO;
        if (serviceChargeType.equals("1") || serviceChargeType.equals("3")){
            hrBill.setServiceCharge(serviceCharge);
        }else {
            hrBill.setServiceCharge(BigDecimal.ZERO);
        }
        if (hrProtocol.getServiceFeeType() == 1){
            BigDecimal serviceFeeData =  BigDecimal.ZERO;
            //浮动服务费
            BillEnum.ServiceFeeData enumByKey = EnumUtils.getEnumByKey(BillEnum.ServiceFeeData.class, hrProtocol.getServiceFeeData());
            switch (enumByKey) {
                //应发工资
                case WAGES_PAYABLE:
                    serviceFeeData = billDetailDTO.getSalary();
                    break;
                //总金额
                case TOTAL_AMOUNT:
                    serviceFeeData = billDetailDTO.getTotal();
                    break;
                case SERVICE_FEE_FORMULA:
                    serviceFeeData = CalculateUtils.decimalSubtraction(
                        CalculateUtils.decimalListAddition(billDetailDTO.getSalary(),billDetailDTO.getUnitSubtotal(),billDetailDTO.getUnitAccumulationFund()),
                        billDetailDTO.getUnitLargeMedicalExpense());
                    break;
                default:
                    break;
            }
            // 税费=总金额*税费计算公式    （总金额=保障账单+薪酬账单+固定值）
            BigDecimal calculate = CalculateUtils.calculate(hrProtocol.getCalculationFormula());
            if (clientType == 0){
                BigDecimal totalAmount = serviceFeeData.add(serviceCharge);//总金额
                BigDecimal bigDecimal = totalAmount.multiply(calculate).setScale(2, BigDecimal.ROUND_HALF_UP);
                serviceCharge = bigDecimal.add(serviceCharge);
            }else {
                serviceCharge = serviceFeeData.multiply(calculate).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
        }

        LocalDate agreementStartDate = hrProtocol.getAgreementStartDate();
        if (serviceChargeType == null) {
            throw new CommonException("客户协议中的服务费类型未设置!");
        }
        if (serviceChargeType.equals("1")) {
            monthlyServiceFee = serviceCharge;
        } else if (serviceChargeType.equals("3")) {
            // 判断当前月是否是6月的整数倍
            LocalDate endDate = LocalDate.of(hrBill.getPayYear(), hrBill.getPayMonthly(), 1);
            LocalDate startDate = LocalDate.of(agreementStartDate.getYear(), agreementStartDate.getMonth(), 1);
            long m = ChronoUnit.MONTHS.between(startDate, endDate);
            if ((m + 1) % 6 == 0) {
                halfYearlyServiceFee = serviceCharge;
            }
        }
        if (hrProtocol.getServiceFeeType() == 0 && clientType == 1){
            //外包单位固定服务器没有税费
            monthlyServiceFee = BigDecimal.ZERO;
        }
        hrBill.setMonthlyServiceFee(monthlyServiceFee);
        hrBill.setHalfYearlyServiceFee(halfYearlyServiceFee);
    }


    /**
     * 修改账单
     * 账单只允许修改标题；明细表允许修改（ 基数+服务费+补差）， 汇总账单允许修改（上月补差 +服务费）。明细表更新后 ，汇总账单会更新
     *
     * @param hrBillDTO
     * @return
     */
    @Override
    public void updateHrBill(HrBillDTO hrBillDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrBill> list = new ArrayList<>();
        List<HrBillDetailDTO> billDetailList = hrBillDTO.getBillDetailList();
        if (billDetailList != null && !billDetailList.isEmpty()) {
            List<String> staffIdList = new ArrayList<>();
            for (HrBillDetailDTO detailDTO : billDetailList) {
                staffIdList.add(detailDTO.getStaffId());
                if (detailDTO.getTaxCalculationMethod() == null || (detailDTO.getTaxCalculationMethod() != null && detailDTO.getTaxCalculationMethod() != hrBillDTO.getTaxCalculationMethod())) {
                    detailDTO.setTaxCalculationMethod(hrBillDTO.getTaxCalculationMethod());
                }
                //
                if (detailDTO.getUpdateState() != null) {
                    if (detailDTO.getUpdateState().equals(BillEnum.BillDetailUpdateState.ADD.getKey()) || detailDTO.getUpdateState() == BillEnum.BillDetailUpdateState.DELETE.getKey() || detailDTO.getUpdateState() == BillEnum.BillDetailUpdateState.UPDATE.getKey()) {
                        StringBuilder operDetail;
                        HrBillContentRecord billContentRecord = new HrBillContentRecord();
                        HrBillDetail billDetail = hrBillDetailMapper.toEntity(detailDTO);
                        // 获取旧的数据
                        HrBillDetailDTO oldDetailDTO = hrBillDetailMapper.toDto(hrBillDetailRepository.selectById(detailDTO.getId()));
                        if (detailDTO.getUpdateState().equals(BillEnum.BillDetailUpdateState.ADD.getKey())) {
                            // 新增的      直接更新   isUsed=1
                            billDetail.setIsUsed(true);
                            billContentRecord.setBusinessType(BusinessTypeEnum.INSERT.getKey());
                            if(hrBillDTO.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
                                operDetail = new StringBuilder("新增未入账项目组：" + detailDTO.getProjectName() + "（" + detailDTO.getProjectCode() + "）；");
                            }else {
                                operDetail = new StringBuilder("新增未入账员工：" + billDetail.getName() + "（" + billDetail.getCertificateNum() + "）；");
                            }
                        } else if (detailDTO.getUpdateState().equals(BillEnum.BillDetailUpdateState.DELETE.getKey())) {
                            // 删除的      更新 isUsed=0   删除明细表与补差表的关系
                            billDetail.setIsUsed(false);
                            billDetail.setReason("手动删除");
                            this.deleteMakeUpUseRecord(billDetail);
                            billContentRecord.setBusinessType(BusinessTypeEnum.DELETE.getKey());
                            if(hrBillDTO.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
                                operDetail = new StringBuilder("删除入账项目组：" + detailDTO.getProjectName() + "（" + detailDTO.getProjectCode() + "）；");
                            }else {
                                operDetail = new StringBuilder("删除入账员工：" + billDetail.getName() + "（" + billDetail.getCertificateNum() + "）；");
                            }
                        } else {
                            // 修改
                            operDetail = new StringBuilder(DataUtils.getObjectDifferent(oldDetailDTO, detailDTO));
                            billContentRecord.setBusinessType(BusinessTypeEnum.UPDATE.getKey());
                        }
                        // 更新账单动态字段内容
                        if (detailDTO.getHrBillDetailItemsList() != null && !detailDTO.getHrBillDetailItemsList().isEmpty()) {
                            List<HrBillDetailItems> hrBillDetailItemsList = this.hrBillDetailItemsRepository.selectList(new QueryWrapper<HrBillDetailItems>().eq("bill_detail_id", detailDTO.getId()));
                            for (HrBillDetailItemsDTO ls : detailDTO.getHrBillDetailItemsList()) {
                                // 添加修改记录日志
                                Optional<HrBillDetailItems> optional = hrBillDetailItemsList.stream().filter(lst -> lst.getId().equals(ls.getId())).findFirst();
                                if(optional != null && optional.isPresent()) {
                                    HrBillDetailItems oldData = optional.get();
                                    if (!BigDecimalCompare.of(oldData.getAmount()).eq(ls.getAmount())) {
                                        operDetail.append(ls.getExpenseName()).append("：修改前为").append(oldData.getAmount()).append(" ，修改后为").append(ls.getAmount()).append("；");
                                        this.hrBillDetailItemsRepository.updateById(this.hrBillDetailItemsMapper.toEntity(ls));
                                    }
                                }
                            }
                        }
                        //全年一次性奖金没有应发工资以及税前应发也不需要绑定保障账单扣除社保公积金金额
                        if (detailDTO.getTaxCalculationMethod() != null && detailDTO.getTaxCalculationMethod() == 1){
                            billDetail.setSalary(BigDecimal.ZERO).setPreTaxSalary(BigDecimal.ZERO).setGuaranteeBillId(null);
                        }
                        hrBillDetailRepository.updateById(billDetail);
                        billContentRecord.setBillId(billDetail.getBillId());
                        billContentRecord.setBillDetailId(billDetail.getId());
                        billContentRecord.setType(BillEnum.BillContentRecordType.BILL_DETAIL.getKey());
                        billContentRecord.setOperDetail(operDetail.toString());
                        billContentRecord.setOperName(jwtUserDTO.getRealName());
                        // 日志 过滤掉  修改明细为空的
                        boolean isInsert = true;
                        if (detailDTO.getUpdateState().equals(BillEnum.BillDetailUpdateState.UPDATE.getKey()) && StringUtils.isBlank(operDetail.toString())) {
                            isInsert = false;
                        }
                        if (isInsert) {
                            hrBillContentRecordRepository.insert(billContentRecord);
                        }
                        // 从redis中获取中间数据
                        this.addMakeUpUserRecordFromRedis(billDetail);
                    }
                }
            }
            // 修改账单员工缴费年月计算倍数状态为[不计算]
            this.hrTalentStaffRepository.updateSupplementaryPayment(staffIdList, 0);
        }
        //删除没有可用的账单明细的账单
        List<String> billIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hrBillDTO.getBillIdList())){
            billIdList = hrBillDTO.getBillIdList();
            List<HrBill> billList = hrBillRepository.selectBatchIds(billIdList);
            HrBillDynamicFields hrBillDynamicField = hrBillDynamicFieldsRepository.selectOne(new QueryWrapper<HrBillDynamicFields>().eq("is_delete", 0).in("bill_id", billIdList).last("LIMIT 1"));
            for (HrBill hrBill : billList) {
                boolean flag = false;
                List<HrBillDetailDTO> billDetailDTOList = hrBillDetailRepository.getListByBillId(hrBill.getId(), 1);
                if (CollectionUtils.isEmpty(billDetailDTOList)){
                    billIdList.remove(hrBill.getId());
                    hrBillDetailRepository.deleteByBillIdBatch(Arrays.asList(hrBill.getId()));
                    hrBillRepository.deleteByBillIds(Arrays.asList(hrBill.getId()));
                    flag = true;
                }
                if (flag){
                    continue;
                }
                //添加账单时上传的的薪酬原单
                if (!hrBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())){
                    HrBillDynamicFields hrBillDynamicFields = hrBillDynamicFieldsRepository.selectOne(new QueryWrapper<HrBillDynamicFields>().eq("is_delete", 0).eq("bill_id", hrBill.getId()).last("LIMIT 1"));
                    if (hrBillDynamicFields == null){
                        HrBillDynamicFields billDynamicFields = new HrBillDynamicFields();
                        BeanUtils.copyProperties(hrBillDynamicField, billDynamicFields);
                        billDynamicFields.setBillId(hrBill.getId());
                        billDynamicFields.setId(null);
                        hrBillDynamicFieldsRepository.insert(billDynamicFields);
                    }
                }
                list.add(hrBill);
                if (hrBill.getIsOfficial() == 0 && hrBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())){
                    List<HrSocialSecurityDTO> clientSocialSecurity = hrSocialSecurityService.getClientSocialSecurity(Collections.singletonList(hrBill.getClientId()));
                    if (clientSocialSecurity != null && !clientSocialSecurity.isEmpty()){
                        HrSocialSecurityDTO hrSocialSecurityDTO = clientSocialSecurity.get(0);
                        String specialField = hrSocialSecurityDTO.getSpecialField();
                        String aloneCardinal = hrSocialSecurityDTO.getAloneCardinal();
                        String mergeCardinal = hrSocialSecurityDTO.getMergeCardinal();
                        if (StringUtils.isNotBlank(specialField) && !specialField.equals("null")){
                            hrBill.setSpecialField(specialField);
                        }
                        if (StringUtils.isNotBlank(aloneCardinal) && !aloneCardinal.equals("null")){
                            hrBill.setAloneCardinal(aloneCardinal);
                        }
                        if (StringUtils.isNotBlank(mergeCardinal) && !mergeCardinal.equals("null")){
                            hrBill.setMergeCardinal(mergeCardinal);
                        }
                    }
                }
                // 账单日志 新增账单
                HrBillDTO billDTO = hrBillMapper.toDto(hrBill);
                billDTO.setBillDetailList(billDetailDTOList);
                // 账单日志 更新
                this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.BILL.getValue(), hrBill.getIsOfficial() != 1 ? BusinessTypeEnum.INSERT.getKey() :BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrBillDTO), HrBillDTO.class, null, JSON.toJSONString(hrBill), JSON.toJSONString(hrBillDTO), null, HrBillDTO.class);
                // 如果账单为草稿状态，把账单改成正式状态
                hrBill.setIsOfficial(1);
                // 修改主标题为传过来的标题
                hrBill.setOptTitle(hrBillDTO.getTitle());
                hrBillRepository.updateById(hrBill);
            }
            if (CollectionUtils.isEmpty(billIdList)){
                throw new CommonException("账单数据中至少存在一条可用的明细数据！");
            }
        }else {//修改
            billIdList.add(hrBillDTO.getId());
            if (StringUtils.isBlank(hrBillDTO.getId())) {
                throw new CommonException("参数账单id不能为空");
            }
            HrBill oldBill = hrBillRepository.selectById(hrBillDTO.getId());
            if (oldBill.getBillState().equals(BillEnum.BillState.LOCKED.getKey())) {
                throw new CommonException(oldBill.getTitle() + "为锁定状态，不能进行修改操作");
            }
            if (StringUtils.isNotBlank(hrBillDTO.getTitle())) {
                hrBillRepository.updateById(new HrBill().setOptTitle(hrBillDTO.getTitle()).setId(hrBillDTO.getId()));
                log.info("更新账单id={}的标题为{}", hrBillDTO.getId(), hrBillDTO.getTitle());
            }
            if (oldBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())){
                this.deleteBatchReconciliation(hrBillDTO,oldBill);
            }
            // 如果账单为草稿状态，把账单改成正式状态
            if (oldBill.getIsOfficial() != 1) {
                hrBillRepository.updateById(new HrBill().setIsOfficial(1).setId(hrBillDTO.getId()));
            }
            // 账单日志 更新
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.BILL.getValue(), oldBill.getIsOfficial() != 1 ? BusinessTypeEnum.INSERT.getKey() :BusinessTypeEnum.UPDATE.getKey(),
                JSON.toJSONString(hrBillDTO), HrBillDTO.class, null, JSON.toJSONString(oldBill), JSON.toJSONString(this.hrBillMapper.toDto(this.hrBillRepository.selectById(oldBill.getId()))), null, HrBillDTO.class);

        }
        // 验证数据明细至少为一条
        List<HrBillDetailDTO> detailList = hrBillDetailRepository.getListByBillIdBatch(billIdList, 1);
        if (detailList == null || detailList.isEmpty()) {
            throw new CommonException("账单数据中至少存在一条可用的明细数据！");
        }
        if (CollectionUtils.isNotEmpty(list)){//新增时修改
            if (list.size() == 1){
                HrBillTotalDTO billTotalDTO = hrBillDTO.getHrBillTotal();
                if (billTotalDTO != null) {
                    HrBillTotal hrBillTotal = this.hrBillTotalMapper.toEntity(billTotalDTO);
                    if (billTotalDTO.getId() != null){
                        hrBillTotalRepository.updateById(hrBillTotal);
                    }else {
                        if (StringUtils.isBlank(billTotalDTO.getBillId())){
                            throw new CommonException("账单ID参数缺失！");
                        }
                        QueryWrapper<HrBillTotal> qw = new QueryWrapper<>();
                        qw.eq("bill_id",billTotalDTO.getBillId());
                        List<HrBillTotal> hrBillTotals = hrBillTotalRepository.selectList(qw);
                        if (hrBillTotals == null || hrBillTotals.isEmpty()){
                            createBillTotal(hrBillDTO, BigDecimal.ZERO);
                        }else {
                            hrBillTotalRepository.update(hrBillTotal,qw);
                        }
                    }
                }
            }else {
                List<String> collect = list.stream().map(HrBill::getId).collect(Collectors.toList());
                List<HrBillTotal> hrBillTotals = hrBillTotalRepository.selectList(new QueryWrapper<HrBillTotal>().eq("is_delete", 0).in("bill_id", collect));
                for (HrBill hrBill : list) {
                    HrBillTotal hrBillTotal = hrBillTotals.stream().filter(lst -> lst.getBillId().equals(hrBill.getId())).findFirst().orElse(null);
                    if (hrBillTotal != null){
                        hrBillTotalRepository.deleteById(hrBillTotal.getId());
                    }
                    createBillTotal(hrBillMapper.toDto(hrBill),BigDecimal.ZERO);
                }
            }
        }else {//修改
            if (hrBillDTO.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
                List<HrBillTotalDTO> hrBillTotalList = hrBillDTO.getHrBillTotalList();
                hrBillTotalList.forEach(billTotalDTO -> {
                    hrBillTotalRepository.updateById(this.hrBillTotalMapper.toEntity(billTotalDTO));
                });
            }else {
                HrBillTotalDTO billTotalDTO = hrBillDTO.getHrBillTotal();
                if (billTotalDTO != null && billTotalDTO.getId() != null) {
                    StringBuilder operDetail = new StringBuilder();
                    // 获取旧的数据
                    HrBillTotalDTO oldBillTotal = hrBillTotalMapper.toDto(hrBillTotalRepository.selectById(billTotalDTO.getId()));
                    hrBillTotalRepository.updateById(this.hrBillTotalMapper.toEntity(billTotalDTO));
                    HrBillContentRecord record = new HrBillContentRecord();
                    record.setBusinessType(BusinessTypeEnum.UPDATE.getKey());
                    record.setBillId(hrBillDTO.getId());
                    record.setBillTotalId(billTotalDTO.getId());
                    record.setType(BillEnum.BillContentRecordType.BILL_TOTAL.getKey());
                    if (billTotalDTO.getIsModifyServiceFee() != null && billTotalDTO.getServiceFeeTotal() != null && billTotalDTO.getIsModifyServiceFee()) {
                        if (!BigDecimalCompare.of(oldBillTotal.getServiceFeeTotal()).eq(billTotalDTO.getServiceFeeTotal())) {
                            operDetail.append("服务费").append("：修改前为").append(oldBillTotal.getServiceFeeTotal()).append(" ，修改后为").append(billTotalDTO.getServiceFeeTotal()).append("；");
                        }
                    }
                    if (billTotalDTO.getLastMonthMakeUp() != null) {
                        if (!BigDecimalCompare.of(oldBillTotal.getLastMonthMakeUp()).eq(billTotalDTO.getLastMonthMakeUp())) {
                            operDetail.append("上月补差").append("：修改前为").append(oldBillTotal.getLastMonthMakeUp()).append(" ，修改后为").append(billTotalDTO.getLastMonthMakeUp()).append("；");
                        }
                    }
                    record.setOperDetail(operDetail.toString());
                    record.setOperName(jwtUserDTO.getRealName());
                    // 日志 过滤掉  修改明细为空的
                    if (StringUtils.isNotBlank(operDetail.toString())) {
                        hrBillContentRecordRepository.insert(record);
                    }
                }
            }
        }

    }

    /**
     * 是否需要删除批量对账
     * @param hrBillDTO 前端传参
     * @param oldBill 数据库数据
     */
    private void deleteBatchReconciliation(HrBillDTO hrBillDTO, HrBill oldBill) {
        if (hrBillDTO.getReconciliationFlag() != null && hrBillDTO.getReconciliationFlag()){
            //删除该月的批量对账结果
            HrBillCompareResultDTO hrBillCompareResultDTO = new HrBillCompareResultDTO();
            hrBillCompareResultDTO.setTitle("全部公司");
            hrBillCompareResultDTO.setPayYear(oldBill.getPayYear());
            hrBillCompareResultDTO.setPayMonthly(oldBill.getPayMonthly());
            List<HrBillCompareResultDTO> billCompareResultDTOS = hrBillCompareResultRepository.findList(hrBillCompareResultDTO);
            if (CollectionUtils.isNotEmpty(billCompareResultDTOS)){
                List<String> billCompareResultIds = billCompareResultDTOS.stream().map(HrBillCompareResultDTO::getId).collect(Collectors.toList());
                List<String> billCompareConfigIds = billCompareResultDTOS.stream().map(HrBillCompareResultDTO::getBillCompareConfigId).collect(Collectors.toList());
                List<HrBillCompareConfig> hrBillCompareConfigs = hrBillCompareConfigRepository.selectBatchIds(billCompareConfigIds);
                List<String> billIds = hrBillCompareConfigs.stream().map(HrBillCompareConfig::getBillId).collect(Collectors.toList());
                hrBillRepository.deleteBatchIds(billIds);
                hrBillCompareConfigRepository.deleteBatchIds(billCompareConfigIds);
                hrBillCompareResultRepository.deleteBatchIds(billCompareResultIds);
            }
        }
        /*if (hrBillDTO.getSalaryBillFlag() != null && hrBillDTO.getSalaryBillFlag()){
            //删除该保障账单后创建的薪酬账单
            this.deleteHrBill(hrBillDTO.getSalaryBillIdList());
        }*/
    }

    /**
     * 查询账单详情 返回账单信息+账单明细列表+汇总账单
     *
     * @param id
     * @return
     */
    @Override
    public HrBillDTO getHrBill(String id) {
        log.info("Get HrBill :{}", id);
        if (StringUtils.isBlank(id)) {
            throw new CommonException("账单id不能为空");
        }
        HrBillDTO hrBill = hrBillRepository.getBillInfoById(id);
        if (hrBill == null){
            throw new CommonException("账单数据异常！");
        }
        this.setSpecialFlag(hrBill);
        if (hrBill != null) {
            List<HrBillDetailDTO> detailList = hrBillDetailRepository.getListByBillId(id, 1);
            hrBill.setBillDetailList(detailList);
            if (!hrBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
                HrBillTotal billTotal = hrBillTotalRepository.selectOne(new QueryWrapper<HrBillTotal>().eq("bill_id", id).eq("is_delete", 0));
                if (billTotal == null) {
                    throw new CommonException("汇总账单数据异常！");
                }
                hrBill.setHrBillTotal(hrBillTotalMapper.toDto(billTotal));
            } else {
                List<HrBillTotalDTO> hrBillTotalDTOList = hrBillTotalRepository.getBillTotalByBillId(Collections.singletonList(id));
                hrBill.setHrBillTotalList(hrBillTotalDTOList);
            }
            if (hrBill.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())){
                //薪酬账单处理是否展示项目名称
                this.handleProjectName(hrBill);
            }
        }
        return hrBill;
    }

    /**
     * 薪酬账单处理是否展示项目名称
     * @param hrBill
     */
    private void handleProjectName(HrBillDTO hrBill) {
        HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO = hrBillDynamicFieldsRepository.getByBillId(hrBill.getId());
        if (hrBillDynamicFieldsDTO != null){
            BillFieldInfoDTO billFieldInfoDTO = hrBillDynamicFieldsDTO.getBillFieldInfoDTO();
            if (billFieldInfoDTO!=null){
                List<HrExpenseManageDTO> collect = billFieldInfoDTO.getMappingFields().stream().filter(lst -> lst.getExpenseType().equals(DynamicFeeTypesEnum.PROJECT_NAME.getKey())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)){
                    hrBill.setIsShowProjectName(1);
                }
            }
        }
    }

    /**
     * 查询多个账单详情
     * @param billIdList 账单ID
     * @return 账单信息+账单明细列表+汇总账单
     */
    @Override
    public HrBillDTO getHrBillBatch(List<String> billIdList) {
        log.info("Get HrBill Batch:{}", billIdList);
        if (billIdList.isEmpty()){
            throw new CommonException("账单id不能为空");
        }
        HrBillDTO hrBill = this.hrBillRepository.getBillInfoByIdList(billIdList, null);
        if (hrBill == null){
            throw new CommonException("账单数据异常！");
        }
        HrBillDTO hrBillChoice = this.hrBillRepository.getBillInfoByIdList(billIdList, 1);
        if (hrBillChoice != null){
            hrBill = hrBillChoice;
        }
        if (billIdList.size() > 1) {
            hrBill.setTitle(hrBill.getOptTitle());
        }
        this.setSpecialFlag(hrBill);
        hrBill.setBillIdList(billIdList);
        List<HrBillDetailDTO> detailList = hrBillDetailRepository.findListBatch(billIdList,1);
        /*if (!hrBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())){
            for (HrBillDetailDTO hrBillDetailDTO : detailList) {
                List<HrBillDetailItems> hrBillDetailItems = hrBillDetailItemsRepository.selectList(new QueryWrapper<HrBillDetailItems>().eq("bill_detail_id", hrBillDetailDTO.getId()).eq("is_delete", 0));
                hrBillDetailDTO.setHrBillDetailItemsList(hrBillDetailItemsMapper.toDto(hrBillDetailItems));
            }
        }*/
        hrBill.setBillDetailList(detailList);
        if (!hrBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            if (billIdList.size() > 1){
                HrBillTotalDTO billTotalDTO = hrBillTotalRepository.getBillTotalByBatchBill(billIdList);
                if (billTotalDTO != null){
                    billTotalDTO.setBillId(hrBill.getId());
                    hrBill.setHrBillTotal(billTotalDTO);
                }
            }else {
                HrBillTotal billTotal = hrBillTotalRepository.selectOne(new QueryWrapper<HrBillTotal>().in("bill_id", billIdList).eq("is_delete", 0).last("LIMIT 1"));
                if (billTotal == null) {
                    throw new CommonException("汇总账单数据异常, 该客户账单明细不存在入账员工, 请选择入账员工后再操作");
                }
                billTotal.setBillId(hrBill.getId());
                hrBill.setHrBillTotal(hrBillTotalMapper.toDto(billTotal));
            }
        } else {
            List<HrBillTotalDTO> hrBillTotalDTOList = hrBillTotalRepository.getBillTotalByBillId(billIdList);
            hrBill.setHrBillTotalList(hrBillTotalDTOList);
        }
        List<HrBill> billList = hrBillRepository.selectBatchIds(billIdList);
        if (!billList.isEmpty()){
            List<String> usedClientId = billList.stream().map(HrBill::getClientId).distinct().collect(Collectors.toList());
            if (!usedClientId.contains(hrBill.getOptClientId())) {
                // 如果不含有操作的客户id，说明是查看，需要构造一下完整的客户id树
                List<HrClientDTO> clientDTOS = hrClientRepository.getParentTree(usedClientId);
                hrBill.setHrClientDTOList(clientDTOS);
            } else{
                hrBill.setClientIdList(usedClientId);
                List<HrClient> hrClientList = hrClientService.listByIds(usedClientId);
                hrBill.setHrClientDTOList(hrClientMapper.toDto(hrClientList));
            }
        }
        hrBill.setHrBillDTOList(hrBillMapper.toDto(billList));
        if (hrBill.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())){
            //薪酬账单处理是否展示项目名称
            this.handleProjectName(hrBill);
        }
        if (hrBill.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())){
            List<DynamicHeadersDTO> dynamicHeadersDTOList = this.showDynamicHeader(billIdList);
            if (dynamicHeadersDTOList != null && !dynamicHeadersDTOList.isEmpty()){
                hrBill.setDynamicHeadersDTOList(dynamicHeadersDTOList);
            }
        }
        return hrBill;
    }

    /**
     * 赋值特殊客户标识
     * @param hrBill
     */
    private void setSpecialFlag(HrBillDTO hrBill) {
        HrClient rootParentClient = hrClientRepository.getRootParentClient(hrBill.getClientId());
        SpecialBillClient enumByKey = EnumUtils.getEnumByKey(SpecialBillClient.class, rootParentClient.getId());
        if (enumByKey != null) {
            switch (enumByKey){
                case HAIER:
                    hrBill.setSpecialFlag(1);
                    break;
                case POLITICS_LAW_COMMITTEE:
                case EAST_DISTRICT_PUBLIC_SECURITY:
                case SECONDARY_POLITICS_LAW_COMMITTEE:
                    hrBill.setSpecialFlag(2);
                    break;
                case SOCIAL_GOVERNANCE:
                    hrBill.setSpecialFlag(3);
                    break;
                default:break;
            }
        }
    }

    /**
     * 暂时创建不可用账单
     * @param usedClientIdList 可以创建账单的客户
     * @param hrBillDTO
     * @return
     */
    @Override
    public HrBill saveBillNotUsed(List<String> usedClientIdList, HrBillDTO hrBillDTO) {
        HrBill hrBill = this.hrBillMapper.toEntity(hrBillDTO);
        // 设置更新时间和创建时间
        if (StringUtils.isBlank(hrBillDTO.getTitle())) {
            String billTypeName = BillEnum.BillType.getValueByKey(hrBillDTO.getBillType());
            hrBillDTO.setTitle(hrBillDTO.getClientName() + hrBillDTO.getPayMonthly() + "月份" + billTypeName);
        }
        hrBillDTO.setBillState(BillEnum.BillState.NOT_LOCKED.getKey());
        hrBillDTO.setIsOfficial(0);
        hrBill.setIsChoice(true);
        LocalDateTime dateTime = LocalDateTime.now();
        hrBill.setCreatedDate(dateTime);
        hrBill.setLastModifiedDate(dateTime);
        this.hrBillRepository.insert(hrBill);
        return hrBill;
    }

    /**
     * 从保障账单中获取社保数据
     * @param billDetail
     * @param billDetailByBill 同月份保障账单
     * @param hrBillDetailDTOS 同月份薪酬账单
     * @param welfareCompensations 员工福利补差
     * @param hrBillCompareResultDTOList 对账数据
     * @param lastPayYear
     * @param lastPayMonthly
     */
    @Override
    public void getDataBySecurityBill(HrBillDetailDTO billDetail, List<HrBillDetailDTO> billDetailByBill, List<HrBillDetailDTO> hrBillDetailDTOS,
                                      List<HrWelfareCompensation> welfareCompensations, List<HrBillCompareResultDTO> hrBillCompareResultDTOList, int lastPayYear, int lastPayMonthly) {
        List<HrBillDetailDTO> hrBillDetailDTOList = new ArrayList<>();
        for (HrBillDetailDTO detail : hrBillDetailDTOS) {
            //不统计全年一次性的薪酬账单
            if (detail.getTaxCalculationMethod() == 0){
                if (detail.getStaffId() != null && detail.getStaffId().equals(billDetail.getStaffId())){
                    if (billDetail.getId() != null){
                        if (!detail.getId().equals(billDetail.getId())){
                            hrBillDetailDTOList.add(detail);
                        }
                    }else {
                        hrBillDetailDTOList.add(detail);
                    }
                }
            }
        }
        HrBillDetailDTO detailDTO = billDetailByBill.stream().filter(detail -> detail.getStaffId().equals(billDetail.getStaffId())).findFirst().orElse(null);//保障账单对象
        if (detailDTO == null){
            detailDTO = new HrBillDetailDTO();
        }else {
            if (CollectionUtils.isEmpty(hrBillDetailDTOList)){
                //该员工第一次创建计税方式是并入综合所得税的薪酬账单
                billDetail.setGuaranteeBillId(detailDTO.getId());
            }else {
                //判断之前创建的薪酬账单是否绑定了保障账单 如果绑定在判断这个保障账单是否可用，如果已删除则需要重新绑定保障账单
                List<String> collect = hrBillDetailDTOList.stream().filter(lst -> lst.getGuaranteeBillId() != null).map(HrBillDetailDTO::getGuaranteeBillId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)){//存在绑定保障账单的薪酬账单
                    List<HrBillDetail> batchIds = hrBillDetailRepository.selectBatchIds(collect);
                    if (CollectionUtils.isEmpty(batchIds)){
                        billDetail.setGuaranteeBillId(detailDTO.getId());
                    }else {
                        detailDTO = new HrBillDetailDTO();
                    }
                }else {//根据社保、公积金判断是否绑定保障账单
                    //A：取出应发工资和税前应发不一致的薪酬账单，如果金额一致则说明没有绑定保障账单
                    for (HrBillDetailDTO billDetailDTO : hrBillDetailDTOList) {
                        if (billDetailDTO.getSalary() == null){
                            billDetailDTO.setSalary(new BigDecimal(0));
                        }
                        if (billDetailDTO.getPreTaxSalary() == null){
                            billDetailDTO.setPreTaxSalary(new BigDecimal(0));
                        }
                    }
                    HrBillDetailDTO collect1 = hrBillDetailDTOList.stream().filter(lst ->lst.getSalary().compareTo(lst.getPreTaxSalary()) != 0).findFirst().orElse(null);
                    //如果保障账单的创建日期在A之后
                    if (collect1 == null){
                        billDetail.setGuaranteeBillId(detailDTO.getId());
                    }else {//判断保障账单的创建时间是否在A之前
                        if (detailDTO.getBillCreatedDate().isAfter(collect1.getCreatedDate())){
                            billDetail.setGuaranteeBillId(detailDTO.getId());
                        }else {
                            detailDTO = new HrBillDetailDTO();
                        }
                    }
                }
            }
        }
        // 个税补差
        BigDecimal taxMakeUp = BigDecimal.ZERO;
        List<HrWelfareCompensation>  welfareCompensationList = new ArrayList<>();
        if (hrBillCompareResultDTOList != null && !hrBillCompareResultDTOList.isEmpty()){
            List<HrBillCompareResultDTO> currentMonthResult = hrBillCompareResultDTOList.stream().filter(lst -> lst.getBillId().equals(billDetail.getBillId()) && lst.getCurrentMonthUsed() == 0 && lst.getPayYear().equals(billDetail.getPayYear()) && lst.getPayMonthly().equals(billDetail.getPayMonthly())).collect(Collectors.toList());
            List<String> resultIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(currentMonthResult)){
                resultIds = currentMonthResult.stream().map(HrBillCompareResultDTO::getId).collect(Collectors.toList());
            }
            for (HrWelfareCompensation welfareCompensation : welfareCompensations) {
                if (welfareCompensation.getIdNo().equals(billDetail.getCertificateNum())){
                    if (resultIds != null && !resultIds.isEmpty()){
                        if (!resultIds.contains(welfareCompensation.getBillResultId())){
                            welfareCompensationList.add(welfareCompensation);
                        }
                    }else {
                        welfareCompensationList.add(welfareCompensation);
                    }
                }
            }
        }
        for (HrWelfareCompensation compensation : welfareCompensationList) {
            taxMakeUp = CalculateUtils.decimalListAddition(taxMakeUp, compensation.getPersonalTax());
        }
        billDetail.setPersonalTaxMakeUp(taxMakeUp);
        billDetail.setSocialSecurityNum(null)
            .setMedicalInsuranceNum(null)
            .setAccumulationFundNum(null)
            .setUnitLargeMedicalExpense(BigDecimal.ZERO)
            .setReplenishWorkInjuryExpense(BigDecimal.ZERO)
            .setPersonalLargeMedicalExpense(BigDecimal.ZERO);
        this.fillSecurityAccumulation(billDetail, detailDTO);
        billDetail.setWelfareCompensationList(welfareCompensationList);
    }

    /**
     * 填充薪酬账单社保公积金金额
     * @param billDetail
     * @param detailDTO
     */
    @Override
    public void fillSecurityAccumulation(HrBillDetailDTO billDetail, HrBillDetailDTO detailDTO) {
        billDetail.setMedicalInsuranceCardinal(detailDTO.getMedicalInsuranceCardinal() == null ? BigDecimal.ZERO : detailDTO.getMedicalInsuranceCardinal());
        billDetail.setMedicalInsuranceCardinalPersonal(detailDTO.getMedicalInsuranceCardinalPersonal() == null ? BigDecimal.ZERO : detailDTO.getMedicalInsuranceCardinalPersonal());
        billDetail.setAccumulationFundCardinal(detailDTO.getAccumulationFundCardinal() == null ? BigDecimal.ZERO : detailDTO.getAccumulationFundCardinal());
        billDetail.setUnitPensionCardinal(detailDTO.getUnitPensionCardinal() == null ? BigDecimal.ZERO : detailDTO.getUnitPensionCardinal());
        billDetail.setUnitUnemploymentCardinal(detailDTO.getUnitUnemploymentCardinal() == null ? BigDecimal.ZERO : detailDTO.getUnitUnemploymentCardinal());
        billDetail.setWorkInjuryCardinal(detailDTO.getWorkInjuryCardinal() == null ? BigDecimal.ZERO : detailDTO.getWorkInjuryCardinal());
        billDetail.setUnitMaternityCardinal(detailDTO.getUnitMaternityCardinal() == null ? BigDecimal.ZERO : detailDTO.getUnitMaternityCardinal());
        billDetail.setPersonalPensionCardinal(detailDTO.getPersonalPensionCardinal() == null ? BigDecimal.ZERO : detailDTO.getPersonalPensionCardinal());
        billDetail.setPersonalUnemploymentCardinal(detailDTO.getPersonalUnemploymentCardinal() == null ? BigDecimal.ZERO : detailDTO.getPersonalUnemploymentCardinal());
        billDetail.setPersonalMaternityCardinal(detailDTO.getPersonalMaternityCardinal() == null ? BigDecimal.ZERO : detailDTO.getPersonalMaternityCardinal());

        billDetail.setPersonalPension(detailDTO.getPersonalPension() == null ? BigDecimal.ZERO : detailDTO.getPersonalPension());
        billDetail.setPersonalUnemployment(detailDTO.getPersonalUnemployment() == null ? BigDecimal.ZERO : detailDTO.getPersonalUnemployment());
        billDetail.setPersonalMedical(detailDTO.getPersonalMedical() == null ? BigDecimal.ZERO : detailDTO.getPersonalMedical());
        billDetail.setPersonalMaternity(detailDTO.getPersonalMaternity() == null ? BigDecimal.ZERO : detailDTO.getPersonalMaternity());
        billDetail.setUnitSubtotal(detailDTO.getUnitSubtotal() == null ? BigDecimal.ZERO : detailDTO.getUnitSubtotal());
        billDetail.setPersonalSubtotal(detailDTO.getPersonalSubtotal() == null ? BigDecimal.ZERO : detailDTO.getPersonalSubtotal());
        billDetail.setUnitAccumulationFund(detailDTO.getUnitAccumulationFund() == null ? BigDecimal.ZERO : detailDTO.getUnitAccumulationFund());
        billDetail.setPersonalAccumulationFund(detailDTO.getPersonalAccumulationFund() == null ? BigDecimal.ZERO : detailDTO.getPersonalAccumulationFund());
        billDetail.setUnitSocialSecurityMakeUp(detailDTO.getUnitSocialSecurityMakeUp() == null ? BigDecimal.ZERO : detailDTO.getUnitSocialSecurityMakeUp());
        billDetail.setPersonalSocialSecurityMakeUp(detailDTO.getPersonalSocialSecurityMakeUp() == null ? BigDecimal.ZERO : detailDTO.getPersonalSocialSecurityMakeUp());
        billDetail.setUnitAccumulationFundMakeUp(detailDTO.getUnitAccumulationFundMakeUp() == null ? BigDecimal.ZERO : detailDTO.getUnitAccumulationFundMakeUp());
        billDetail.setPersonalAccumulationFundMakeUp(detailDTO.getPersonalAccumulationFundMakeUp() == null ? BigDecimal.ZERO : detailDTO.getPersonalAccumulationFundMakeUp());
    }

    @Override
    public void fillSpecialSalaryParamForRetire(HrBillDetailDTO detailDTO, List<HrBillDetailDTO> lastBillDetailDTOList, List<HrBillDetailItemsDTO> initDynamicFee, Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions) {
        HrBillDetailDTO billDetailDTO = lastBillDetailDTOList.stream().filter(lst -> lst.getStaffId().equals(detailDTO.getStaffId())).findAny().orElse(null);
        if (billDetailDTO != null) {
            this.fillSecurityAccumulation(detailDTO, billDetailDTO);
            List<HrBillDetailItemsDTO> dynamicFeeItems = billDetailDTO.getHrBillDetailItemsList();
            Map<String, BigDecimal> collect = dynamicFeeItems.stream().collect(Collectors.toMap(HrBillDetailItemsDTO::getExpenseName, HrBillDetailItemsDTO::getAmount, (key1, key2) -> key2));
            for (HrBillDetailItemsDTO itemsDTO : initDynamicFee) {
                BigDecimal amount = collect.get(itemsDTO.getExpenseName());
                itemsDTO.setAmount(amount);
            }
            detailDTO.setHrBillDetailItemsList(initDynamicFee);
            this.fillSalaryParam(detailDTO, initDynamicFee, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
        }
    }

    @Override
    public void fillSpecialSalaryParamForNewJoined(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItems, HrBillDetailDTO hrSecurityBillDetailDTO,
                                                   Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions) {
        // Map<String, BigDecimal> collect = dynamicFeeItems.stream().collect(Collectors.toMap(HrBillDetailItemsDTO::getExpenseName, HrBillDetailItemsDTO::getAmount));
        Map<String, BigDecimal> collect = new HashMap<>();
        dynamicFeeItems.forEach(dynamicFeeItem -> {
            collect.remove(dynamicFeeItem.getExpenseName());
            collect.put(dynamicFeeItem.getExpenseName(), dynamicFeeItem.getAmount());
        });
        // 获取当月补拨金额
        BigDecimal supplementaryAmountForTheMonth = collect.get(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue());
        if (BigDecimalCompare.of(supplementaryAmountForTheMonth).gt(BigDecimal.ZERO)) {
            // 计算上月工作天数
            int workingDay = DateUtils.calJoinedWorkingDay(detailDTO.getBoardDate());
            // 日工资 = （当月财政拨款额合计 - 劳务派遣费用 - 月经费统计单位负担社会保险费 - 月经费统计单位负担住房公积金 - 月经费统计代扣个人负担社会保险费 - 月经费统计个人负担住房公积金 - 当月补拨金额）/ 21.75
            BigDecimal thisMonthFullSalary = CalculateUtils.decimalListSubstract(collect.get(DynamicFeeTypesEnum.TOTAL_FINANCIAL_ALLOCATION_FOR_THE_MONTH.getValue()),
                collect.get(DynamicFeeTypesEnum.LABOR_DISPATCH_FEE.getValue()),
                collect.get(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue()),
                collect.get(DynamicFeeTypesEnum.CLIENT_SOCIAL_SECURITY_MONTHLY.getValue()),
                collect.get(DynamicFeeTypesEnum.CLIENT_PROVIDENT_FUND_MONTHLY.getValue()),
                collect.get(DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MONTHLY.getValue()),
                collect.get(DynamicFeeTypesEnum.PERSONAL_PROVIDENT_FUND_MONTHLY.getValue()));
            // 本科人员+200元
            if (StaffEnum.HighestEducationEnum.UNDERGRADUATE.getKey().equals(detailDTO.getHighestEducation()) || StaffEnum.HighestEducationEnum.GRADUATE_STUDENT.getKey().equals(detailDTO.getHighestEducation())) {
                thisMonthFullSalary = CalculateUtils.decimalAddition(thisMonthFullSalary, BigDecimal.valueOf(200));
            }
            BigDecimal dailyWage = CalculateUtils.objectDivide(thisMonthFullSalary, BigDecimal.valueOf(21.75), 2);
            BigDecimal preTaxSalary = CalculateUtils.objectMultiply(dailyWage, workingDay);
            BigDecimal salary = BigDecimal.ZERO;
            // 本月保障账单
            if (hrSecurityBillDetailDTO != null) {
                salary = CalculateUtils.decimalListAddition(preTaxSalary, hrSecurityBillDetailDTO.getPersonalSubtotal(), hrSecurityBillDetailDTO.getPersonalAccumulationFund(), hrSecurityBillDetailDTO.getPersonalAccumulationFundMakeUp());
                detailDTO.setSalary(salary);
            }
            this.calculateTaxAndOther(detailDTO, salary, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
        } else {
            this.fillSalaryParam(detailDTO, dynamicFeeItems, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
        }
    }

    @Override
    public void fillSpecialSalaryParamForResigned(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItems) {
        // 上月足月工资 = 当月财政拨款额合计 - 劳务派遣费用 - 月经费统计单位负担社会保险费 - 月经费统计单位负担住房公积金 - 月经费统计代扣个人负担社会保险费 - 月经费统计个人负担住房公积金
        Map<String, BigDecimal> collect = dynamicFeeItems.stream().collect(Collectors.toMap(HrBillDetailItemsDTO::getExpenseName, HrBillDetailItemsDTO::getAmount, (key1, key2) -> key2));
        BigDecimal lastMonthFullSalary = CalculateUtils.decimalListSubstract(
            collect.get(DynamicFeeTypesEnum.TOTAL_FINANCIAL_ALLOCATION_FOR_THE_MONTH.getValue()),
            collect.get(DynamicFeeTypesEnum.LABOR_DISPATCH_FEE.getValue()),
            collect.get(DynamicFeeTypesEnum.CLIENT_SOCIAL_SECURITY_MONTHLY.getValue()),
            collect.get(DynamicFeeTypesEnum.CLIENT_PROVIDENT_FUND_MONTHLY.getValue()),
            collect.get(DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MONTHLY.getValue()),
            collect.get(DynamicFeeTypesEnum.PERSONAL_PROVIDENT_FUND_MONTHLY.getValue())
        );
        log.info("上月足月工资:{}", lastMonthFullSalary);
        BigDecimal dailyWage = CalculateUtils.objectDivide(lastMonthFullSalary, detailDTO.getResignationDate().lengthOfMonth(), 2);
        int workingDay = DateUtils.calResignWorkingDay(detailDTO.getResignationDate());
        BigDecimal salary = CalculateUtils.objectMultiply(dailyWage, workingDay);
        log.info("实际应发工资:{}", salary);
        detailDTO.setSalary(salary);
        detailDTO.setPreTaxSalary(salary);
        detailDTO.setOtherSalary(BigDecimal.ZERO);
        detailDTO.setPersonalTax(BigDecimal.ZERO);
        detailDTO.setRealSalary(salary);

        // this.calculateTaxAndOther(detailDTO, salary);
    }

    /**
     * 东区公安
     * 上月离职员工
     * 薪酬账填充应发工资,税前应发,个税,实发工资,费用合计,免税收入等
     *  @param detailDTO
     * @param dynamicFeeItems
     */
    @Override
    public void fillEastSecuritySalaryParamForResigned(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItems) {
        // 岗位工资 = （岗位工资字段 - 月经费统计-单位负担社会保险费-月经费统计-单位负担住房公积金 ） / 离职月份的自然天数 * （实际离职日期 - 1天） - 月经费统计-个人负担住房公积金 - 月经费统计-代扣个人负担社会保险费
        Map<String, BigDecimal> collect = dynamicFeeItems.stream().collect(Collectors.toMap(HrBillDetailItemsDTO::getExpenseName, HrBillDetailItemsDTO::getAmount, (key1, key2) -> key2));
        BigDecimal lastMonthFullSalary = CalculateUtils.decimalListSubstract(
            collect.get(DynamicFeeTypesEnum.POST_GRADE_SYSTEM.getValue()),
            collect.get(DynamicFeeTypesEnum.CLIENT_SOCIAL_SECURITY_MONTHLY.getValue()),
            collect.get(DynamicFeeTypesEnum.CLIENT_PROVIDENT_FUND_MONTHLY.getValue())
        );
        //离职月份天数
        LocalDate resignationDate = detailDTO.getResignationDate();
        BigDecimal dailyWage = CalculateUtils.objectDivide(lastMonthFullSalary, resignationDate.lengthOfMonth(), 2);
        BigDecimal lastSalary = CalculateUtils.objectMultiply(dailyWage, resignationDate.getDayOfMonth() - 1);
        //岗位工资
        BigDecimal postSalary = CalculateUtils.decimalListSubstract(lastSalary,
            collect.get(DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MONTHLY.getValue()),
            collect.get(DynamicFeeTypesEnum.PERSONAL_PROVIDENT_FUND_MONTHLY.getValue()));
        //应发工资 = 离职人员岗位工资 + 等级工资 + 月绩效考核 + 服役年限金额 + 防暑降温金额 + 本年度社会保险补差额 + 当月补拨金额
        BigDecimal salary = CalculateUtils.decimalListAddition(postSalary,
            collect.get(DynamicFeeTypesEnum.GRADED_WAGES.getValue()),
            collect.get(DynamicFeeTypesEnum.MONTHLY_PERFORMANCE_APPRAISAL.getValue()),
            collect.get(DynamicFeeTypesEnum.CURRENT_LIFE_AMOUNT.getValue()),
            collect.get(DynamicFeeTypesEnum.AMOUNT_OF_HEATSTROKE_PREVENTION_FEE.getValue()),
            collect.get(DynamicFeeTypesEnum.SOCIAL_INSURANCE_MARGIN.getValue()),
            collect.get(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue()));
        log.info("实际应发工资:{}", salary);
        detailDTO.setSalary(salary);
        detailDTO.setPreTaxSalary(salary);
        detailDTO.setOtherSalary(BigDecimal.ZERO);
        detailDTO.setPersonalTax(BigDecimal.ZERO);
        detailDTO.setRealSalary(salary);
        detailDTO.setTotal(salary);
        detailDTO.setPostSalary(postSalary);
    }

    /**
     * 将东区公安离职的员工费用增项重新赋值
     * @param detailDTO
     * @param specialBillExcelDataMap
     * @param hrBillDetailItemsList
     */
    @Override
    public void fillEastSecurityBillDetailItems(HrBillDetailDTO detailDTO, Map<String, BillExcelDataDTO> specialBillExcelDataMap, List<HrBillDetailItemsDTO> hrBillDetailItemsList) {
        BillExcelDataDTO billExcelDataDTO = specialBillExcelDataMap.get(detailDTO.getCertificateNum());
        if (billExcelDataDTO != null){
            Map<String, BigDecimal> collect = billExcelDataDTO.getDynamicFeeItems().stream().collect(Collectors.toMap(HrBillDetailItemsDTO::getExpenseName, HrBillDetailItemsDTO::getAmount, (key1, key2) -> key2));
            for (HrBillDetailItemsDTO itemsDTO : hrBillDetailItemsList) {
                if (itemsDTO.getExpenseName().equals(DynamicFeeTypesEnum.POST_GRADE_SYSTEM.getValue())){
                    itemsDTO.setAmount(detailDTO.getPostSalary());
                }else if (itemsDTO.getExpenseName().equals(DynamicFeeTypesEnum.GRADED_WAGES.getValue())){
                    itemsDTO.setAmount(collect.get(DynamicFeeTypesEnum.GRADED_WAGES.getValue()));
                }else if (itemsDTO.getExpenseName().equals(DynamicFeeTypesEnum.MONTHLY_PERFORMANCE_APPRAISAL.getValue())){
                    itemsDTO.setAmount(collect.get(DynamicFeeTypesEnum.MONTHLY_PERFORMANCE_APPRAISAL.getValue()));
                }else if (itemsDTO.getExpenseName().equals(DynamicFeeTypesEnum.CURRENT_LIFE_AMOUNT.getValue())){
                    itemsDTO.setAmount(collect.get(DynamicFeeTypesEnum.CURRENT_LIFE_AMOUNT.getValue()));
                }else if (itemsDTO.getExpenseName().equals(DynamicFeeTypesEnum.AMOUNT_OF_HEATSTROKE_PREVENTION_FEE.getValue())){
                    itemsDTO.setAmount(collect.get(DynamicFeeTypesEnum.AMOUNT_OF_HEATSTROKE_PREVENTION_FEE.getValue()));
                }else if (itemsDTO.getExpenseName().equals(DynamicFeeTypesEnum.SOCIAL_INSURANCE_MARGIN.getValue())){
                    itemsDTO.setAmount(collect.get(DynamicFeeTypesEnum.SOCIAL_INSURANCE_MARGIN.getValue()));
                }else if (itemsDTO.getExpenseName().equals(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue())){
                    itemsDTO.setAmount(collect.get(DynamicFeeTypesEnum.SUPPLEMENTARY_AMOUNT_FOR_THE_MONTH.getValue()));
                }
            }
        }
    }

    /**
     * 填充薪酬账单 应发工资,税前应发,个税,实发工资,费用合计,免税收入等
     *
     * @param detailDTO
     * @param dynamicFeeItemsDTOS
     */
    @Override
    public void fillSalaryParam(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItemsDTOS, Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions) {
        // 应发工资
        BigDecimal salaryWage = BigDecimal.ZERO;
        // 其他费用
        BigDecimal otherSalary = BigDecimal.ZERO;
        for (HrBillDetailItemsDTO param : dynamicFeeItemsDTOS) {
            DynamicFeeTypesEnum typesEnum = EnumUtils.getEnumByKey(DynamicFeeTypesEnum.class, param.getExpenseType());
            BigDecimal amount = param.getAmount() == null ? BigDecimal.ZERO : param.getAmount();
            switch (typesEnum) {
                case FEE_TYPE_ADD:
                case TAX_EXEMPT_INCOME:
                    // 增项,使用原始的excel数据
                    salaryWage = salaryWage.add(amount);
                    // 差旅补助为不计入个税收入
                    if ("差旅补助".equals(param.getExpenseName())) {
                        detailDTO.setTravelAllowance(param.getAmount());
                    }
                    break;
                case FEE_TYPE_REDUCE:
                    // 工资减项,统一减去绝对值
                    salaryWage = salaryWage.subtract(amount.abs());
                    break;
                case FEE_TYPE_OTHER:
                    otherSalary = otherSalary.add(amount);
                    break;
                default:
                    break;
            }
        }
        detailDTO.setOtherSalary(otherSalary);
        detailDTO.setSalary(salaryWage);

        this.calculateTaxAndOther(detailDTO, salaryWage, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
    }

    /**
     * 计算个税等数据
     *
     * @param detailDTO
     * @param salary
     */
    private void calculateTaxAndOther(HrBillDetailDTO detailDTO, BigDecimal salary, Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions) {
        // 计算个税=根据应发工资计算个税
        this.hrBillDetailItemsService.calculatePersonalIncomeTax(detailDTO, personalTaxStart, sumCurrentIncome, specialDeductionList,hrQuickDeductions);

        // 个人社保/公积金小计
        BigDecimal personalSubTotal = detailDTO.getPersonalSubtotal() == null ? BigDecimal.ZERO : detailDTO.getPersonalSubtotal();
        BigDecimal personalAccumuTotal = detailDTO.getSocailAccumulationXj();
        // 税前应发=应发工资-个人社保小计-个人公积金小计
        BigDecimal preTaxSalary = CalculateUtils.decimalListSubstract(salary,personalSubTotal,personalAccumuTotal);
        detailDTO.setPreTaxSalary(preTaxSalary);

        // 计算实发工资=税前应发- (个税 + 补差）
        BigDecimal bigDecimal = CalculateUtils.decimalAddition(detailDTO.getPersonalTax(), detailDTO.getPersonalTaxMakeUp());
        detailDTO.setRealSalary(preTaxSalary.subtract(bigDecimal));

        //计算费用合计 = 税前应发
        detailDTO.setTotal(preTaxSalary);

        // 计费费用合计 = 应发工资 + 单位社保 + 单位公积金 + 其他费用之和 + 服务费
        /*BigDecimal totalFee = CalculateUtils.decimalListAddition(
            salary,
            detailDTO.getUnitSubtotal(),
            detailDTO.getUnitAccumulationFund(),
            otherSayary,
            detailDTO.getServiceFee()
        );
        detailDTO.setTotal(totalFee);
        // 薪酬原单计税方式有改动
        if (detailDTO != null) {
            this.annualLumpSumBonusTax(detailDTO);
        }*/
    }

    /**
     * 计算其他账单费用合计
     *
     * @param detailDTO
     * @param dynamicFeeItemsDTOS
     * @param flag
     */
    @Override
    public List<HrBillDetailItems> fillOtherBillParam(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItemsDTOS, Boolean flag) {
        // 费用合计
        BigDecimal total = BigDecimal.ZERO;
        List<HrBillDetailItems> detailItems = new ArrayList<>();
        for (HrBillDetailItemsDTO param : dynamicFeeItemsDTOS) {
            DynamicFeeTypesEnum typesEnum = EnumUtils.getEnumByKey(DynamicFeeTypesEnum.class, param.getExpenseType());
            BigDecimal amount = param.getAmount() == null ? BigDecimal.ZERO : param.getAmount();
            switch (typesEnum) {
                case CHARGE_ITEM:
                    // 收费项,使用原始的excel数据
                    total = total.add(amount);
                    break;
                case REFUND_ITEM:
                    // 退费项,统一减去绝对值
                    total = total.subtract(amount.abs());
                    break;
                default:
                    break;
            }
            if (flag){
                HrBillDetailItems hrBillDetailItems = hrBillDetailItemsMapper.toEntity(param.setId(null).setBillDetailId(detailDTO.getId()));
                detailItems.add(hrBillDetailItems);
            }else {
                detailItems.add(hrBillDetailItemsMapper.toEntity(param));
            }
        }
        detailDTO.setTotal(total);
        detailDTO.setSocialSecurityNum(null).setMedicalInsuranceNum(null).setAccumulationFundNum(null)
//            .setSocialSecurityCardinal(null) .setSocialSecurityCardinalPersonal(null)
            .setMedicalInsuranceCardinal(null).setMedicalInsuranceCardinalPersonal(null)
            .setUnitPensionCardinal(null)
            .setUnitUnemploymentCardinal(null)
            .setWorkInjuryCardinal(null)
            .setUnitMaternityCardinal(null)
            .setPersonalPensionCardinal(null)
            .setPersonalUnemploymentCardinal(null)
            .setAccumulationFundCardinal(null).setSalary(BigDecimal.ZERO);

        return detailItems;
    }

    /**
     * 删除账单
     * 需要把关联的账单明细+汇总账单+账单明细与补差的关联都删掉
     *
     * @param id
     */
    @Override
    public void deleteHrBill(String id) {
        Optional.ofNullable(this.hrBillRepository.selectById(id))
            .ifPresent(hrBill -> {
                if (hrBill.getBillState().equals(BillEnum.BillState.LOCKED.getKey())) {
                    throw new CommonException(hrBill.getTitle() + "为锁定状态，不能进行删除操作");
                }
                hrBillRepository.deleteById(id);
                List<HrBillDetail> billDetails = this.hrBillDetailRepository.selectList(new QueryWrapper<HrBillDetail>().eq("bill_id", id).eq("is_delete", 0));
                List<String> staffIdList = new ArrayList<>();
                for (HrBillDetail detail : billDetails) {
                    hrBillDetailRepository.deleteById(detail.getId());
                    if (detail.getEmolumentMultiple() != null && detail.getEmolumentMultiple() > 1) {
                        staffIdList.add(detail.getStaffId());
                    }
                }
                if (staffIdList != null && !staffIdList.isEmpty()){
                    // 恢复员工缴费年月计算倍数状态
                    this.hrTalentStaffRepository.updateSupplementaryPayment(staffIdList, 1);
                }
                hrBillTotalRepository.delete(new QueryWrapper<HrBillTotal>().eq("bill_id", id).eq("is_delete", 0));
                hrMakeUpUseRecordRepository.delete(new QueryWrapper<HrMakeUpUseRecord>().eq("bill_id", id).eq("is_delete", 0));

                // 删除薪酬账单特有的
                this.hrBillDynamicFieldsRepository.deleteByBillId(id);
                this.hrBillDetailItemsRepository.deleteByBillId(id);
                log.info("Delete HrBill:{},", hrBill);
            });
    }

    /**
     * 批量删除账单
     *
     * @param ids
     */
    @Override
    public void deleteHrBill(List<String> ids) {
        log.info("Delete HrBills:{}", ids);
        if (ids == null || ids.isEmpty()) {
            throw new CommonException("请选择要删除的账单！");
        }

        for (String id : ids) {
            //现在前台传过来的ids其实是billnos或者账单ids，要根据每个编号查询出所有账单，然后再遍历删除
            List<HrBillDTO> billList = hrBillRepository.findListByBillNo(id);
            if (billList.isEmpty()) {
                // 如果没有查询到账单，说明此条id应该是账单id
                this.deleteHrBill(id);
            } else {
                for (HrBillDTO billDTO : billList) {
                    this.deleteHrBill(billDTO.getId());
                }
            }
        }
        // 账单日志 删除
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.BILL.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids), null, null, null, null, null, null);

    }

    /**
     * 分页查询账单
     *
     * @param hrBillDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrBillDTO> findPage(HrBillDTO hrBillDTO, Long pageNumber, Long pageSize) {
        // 查询客户权限   角色为客户   只能查看自己的
        List<String> clientIdList = hrClientService.selectClientIdByUserId();
        if (clientIdList == null || clientIdList.isEmpty()) {
            return new Page<>();
        }
        // 账单核算列表 客户、总经理、总裁、监事会主席和董事长 只能查看已经锁定的账单
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String roleKey = jwtUserDTO.getCurrentRoleKey();
        if (roleKey.equals(UserRoleTypeEnum.CLIENT.getKey()) || roleKey.equals(UserRoleTypeEnum.TOTAL_MANAGER.getKey()) || roleKey.equals(UserRoleTypeEnum.CEO.getKey()) || roleKey.equals(UserRoleTypeEnum.SUPERVISORY_BOARD_CHAIRMAN.getKey()) || roleKey.equals(UserRoleTypeEnum.CHAIRMAN.getKey())) {
            hrBillDTO.setBillState(BillEnum.BillState.LOCKED.getKey());
        }
        hrBillDTO.setClientIdList(clientIdList);
        Page<HrBill> page = new Page<>(pageNumber, pageSize);
//        IPage<HrBillDTO> iPage = this.hrBillRepository.findPage(page, hrBillDTO);
        IPage<HrBillDTO> iPage = this.hrBillRepository.findRecordPage(page, hrBillDTO);
        iPage.getRecords().forEach(ls -> {
            if (ls.getStrClientIdList() != null) {
                if (ls.getStrClientIdList().size() > 1) {
                    ls.setChildren(new ArrayList<>());
                } else {
                    if (!ls.getStrClientIdList().get(0).equals(ls.getOptClientId())) {
                        ls.setChildren(new ArrayList<>());
                    }
                }

            }
            // 获取账单明细
//            ls.setBillDetailList(this.hrBillDetailRepository.getByBillId(ls.getId()));
            ls.setBdCount(this.hrBillDetailRepository.getByBillNo(ls.getBillNo()).size());
        });
        return iPage;
    }

    /**
     * 入账检查
     * 假设账单缴费年月为7月。
     * 可参与账单核算的人有：目前已参保的在职、试用中、实习中、产假期、医疗期员工，6月已参保的离职员工，7月已参保的离职员工；
     * 不可参与账单核算的人有：6-7月所有未参保的员工，6-7月所有待入职、入职中、退休的员工。
     * 另外，6月之前离职、退休的员工不参与账单计算，也不参与未入账员工数量的计算。
     *
     * @return
     * <AUTHOR>
     * @date 2021/11/3
     */
    @Override
    public Map<String, Object> createHrBillCheck(HrBillDTO hrBillDTO) {
        //可以创建账单的客户
        String clientId = hrBillDTO.getClientId();
        List<String> usedClientIds;
        List<String> notUsedClientIds = new ArrayList<>();
        List<String> clientIds = hrClientService.querySubordinateClient(hrBillDTO.getClientId());
        if (hrBillDTO.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())) {
            // 检查此费用年月是否已经存在账单
            for (String usedClientId : clientIds) {
                hrBillDTO.setClientId(usedClientId);
                int count = this.hrBillRepository.getBillCountByPaymentDate(hrBillDTO);
                if (count > 0) {
                    notUsedClientIds.add(usedClientId);
                }
            }
        }
        clientIds.removeAll(notUsedClientIds);
        if (CollectionUtils.isEmpty(clientIds)) {
            throw new CommonException("该客户以及子级客户在"+hrBillDTO.getPaymentDate()+"已创建账单！");
        }
        List<HrTalentStaffDTO> hrTalentStaffList = this.hrTalentStaffRepository.selectByClientId(clientIds);
        if (hrTalentStaffList.isEmpty()) {
            throw new CommonException("该客户下的可以创建账单的子客户暂无员工信息！");
        } else {
            usedClientIds = hrTalentStaffList.stream().map(HrTalentStaffDTO::getClientId).distinct().collect(Collectors.toList());
        }
        // 检查社保公积金比例是否配置
        List<HrClientDTO> hrClientDTOList = hrClientRepository.selectFundAndSocial(clientIds);
        List<String> socialSecurityTypeClientNameList = hrClientDTOList.stream().filter(lst -> lst.getSocialSecurityTypeId() == null).map(HrClientDTO::getClientName).collect(Collectors.toList());
        if (!socialSecurityTypeClientNameList.isEmpty()){
            throw new CommonException("【"+ String.join(",",socialSecurityTypeClientNameList)+"】未配置社保参数！");
        }
        List<String> providentFundClientNameList = hrClientDTOList.stream().filter(lst -> lst.getProvidentFundTypeId() == null).map(HrClientDTO::getClientName).collect(Collectors.toList());
        if (!providentFundClientNameList.isEmpty()){
            throw new CommonException("【"+ String.join(",",providentFundClientNameList)+"】未配置公积金参数！");
        }
        /*List<HrAccumulationFund> hrAccumulationFundList = this.hrAccumulationFundRepository.getClientAccumulationFund(clientIds);
        if (hrAccumulationFundList == null || hrAccumulationFundList.size() != clientIds.size()) {
            throw new CommonException("该客户下的某些子客户未配置公积金参数！");
        }
        List<HrSocialSecurity> hrSocialSecurityList = this.hrSocialSecurityRepository.getClientSocialSecurity(clientIds);
        if (hrSocialSecurityList == null || hrSocialSecurityList.size() != clientIds.size()) {
            throw new CommonException("该客户下的某些子客户未配置社保参数！");
        }
        // 检查客户协议是否存在 并且唯一
        this.checkProtocol(clientId);*/
        BillCheckResultDTO billCheckResult = new BillCheckResultDTO();
        // LocalDate paymentDate = LocalDate.of(hrBillDTO.getPayYear(), hrBillDTO.getPayMonthly(), 1);
        Map<String, String> staffMap = new HashMap<>();//未入账员工ID
        for (HrTalentStaffDTO talentStaff : hrTalentStaffList) {
            // 员工状态
            int staffStatus = talentStaff.getStaffStatus();
            // 参保状态
            int insuredState = talentStaff.getIzInsured();

            // 已参保
            if (insuredState == StaffEnum.InsuredStatusEnum.INSURED.getKey()) {
                billCheckResult.setInsured(billCheckResult.getInsured() + 1);
                StaffEnum.StaffStatusEnum staffStatusEnum = EnumUtils.getEnumByKey(StaffEnum.StaffStatusEnum.class, staffStatus);
                switch (staffStatusEnum) {
                    // 待入职
                    case TO_BE_HIRED:
                        billCheckResult.setWaitingEmployment(billCheckResult.getWaitingEmployment() + 1);
                        talentStaff.setReason("员工状态处于待入职");
                        break;
                    // 入职中
                    case ENROLLING:
                        billCheckResult.setInduction(billCheckResult.getInduction() + 1);
                        talentStaff.setReason("员工状态处于入职中");
                        break;
                    // 试用中
                    case ON_PROBATION:
                        billCheckResult.setOnTrial(billCheckResult.getOnTrial() + 1);
                        break;
                    // 实习中
                    case TO_INTERNSHIP:
                        billCheckResult.setInPractice(billCheckResult.getInPractice() + 1);
                        break;
                    // 在职
                    case ON_JOB:
                        billCheckResult.setOnJob(billCheckResult.getOnJob() + 1);
                        break;
                    // 产假期
                    case MATERNITY_LEAVE:
                        billCheckResult.setMaternityLeave(billCheckResult.getMaternityLeave() + 1);
                        talentStaff.setReason("员工状态处于产假中");
                        break;
                    // 医疗期
                    case MEDICAL_TREATMENT_PERIOD:
                        billCheckResult.setMedicalPeriod(billCheckResult.getMedicalPeriod() + 1);
                        break;
                    // 离职中
                    case STAY_CONFIRM_RESIGNATION_LEAVING:
                        billCheckResult.setLeaving(billCheckResult.getLeaving() + 1);
                        talentStaff.setReason("员工状态处于离职中");
                        break;
                    // 待确认离职
                    case STAY_CONFIRM_RESIGNATION:
                        billCheckResult.setLeaving(billCheckResult.getLeaving() + 1);
                        talentStaff.setReason("员工状态处于待确认离职");
                        break;
                    // 离职
                    case SEPARATION:
                        billCheckResult.setQuit(billCheckResult.getQuit() + 1);
                        talentStaff.setReason("员工状态处于离职");
                        break;
                    // 退休
                    case RETIRE:
                        billCheckResult.setRetire(billCheckResult.getRetire() + 1);
                        talentStaff.setReason("员工状态处于退休");
                        break;
                    default:
                        break;
                }
            }
            // 未参保
            else {
                billCheckResult.setUninsured(billCheckResult.getUninsured() + 1);
                talentStaff.setReason("员工未参保");
            }

            // 离职
            /*if (insuredState == StaffEnum.InsuredStatusEnum.INSURED.getKey() && staffStatus == StaffEnum.StaffStatusEnum.SEPARATION.getKey()) {
                billCheckResult.setQuit(billCheckResult.getQuit() + 1);
                talentStaff.setReason("员工状态处于离职");
                if (talentStaff.getResignationDate() != null) {
                    long quitBetween = ChronoUnit.MONTHS.between(talentStaff.getResignationDate(), paymentDate);
                    // 本月离职员工
                    if (quitBetween == 0) {
                        billCheckResult.setThisMonthQuit(billCheckResult.getThisMonthQuit() + 1);
                    }
                    // 上月离职员工
                    else if (quitBetween == 1) {
                        billCheckResult.setLastMonthQuit(billCheckResult.getLastMonthQuit());
                    }
                    // 上月之前离职员工
                    else {
                        billCheckResult.setBeforeLastMonthQuit(billCheckResult.getBeforeLastMonthQuit() + 1);
                    }
                }
            }*/
            // 待入职
            /*if (insuredState == StaffEnum.InsuredStatusEnum.INSURED.getKey() && staffStatus == StaffEnum.StaffStatusEnum.TO_BE_HIRED.getKey()) {
                billCheckResult.setWaitingEmployment(billCheckResult.getWaitingEmployment() + 1);
                talentStaff.setReason("员工状态处于待入职");
            }*/
            // 入职中
            /*if (insuredState == StaffEnum.InsuredStatusEnum.INSURED.getKey() && staffStatus == StaffEnum.StaffStatusEnum.ENROLLING.getKey()) {
                billCheckResult.setInduction(billCheckResult.getInduction() + 1);
                talentStaff.setReason("员工状态处于入职中");
            }*/
            // 退休
            /*if (insuredState == StaffEnum.InsuredStatusEnum.INSURED.getKey() && staffStatus == StaffEnum.StaffStatusEnum.RETIRE.getKey()) {
                billCheckResult.setRetire(billCheckResult.getRetire() + 1);
                talentStaff.setReason("员工状态处于退休");
                *//*if (talentStaff.getActualRetireDate() != null) {
                    long retireBetween = ChronoUnit.MONTHS.between(talentStaff.getActualRetireDate(), paymentDate);
                    // 本月退休
                    if (retireBetween == 0) {
                        billCheckResult.setThisMonthRetire(billCheckResult.getThisMonthRetire() + 1);
                    }
                    // 上月退休
                    else if (retireBetween == 1) {
                        billCheckResult.setLastMonthRetire(billCheckResult.getLastMonthRetire() + 1);
                    }
                    // 上月之前退休
                    else {
                        billCheckResult.setBeforeLastMonthRetire(billCheckResult.getBeforeLastMonthRetire() + 1);
                    }
                }*//*
            }*/
            /*if (insuredState == StaffEnum.InsuredStatusEnum.INSURED.getKey() && staffStatus == StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey()) {
                billCheckResult.setLeaving(billCheckResult.getLeaving() + 1);
                talentStaff.setReason("员工状态处于离职中");
            }*/
            /*if (insuredState == StaffEnum.InsuredStatusEnum.INSURED.getKey() && staffStatus == StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION.getKey()) {
                billCheckResult.setLeaving(billCheckResult.getLeaving() + 1);
                talentStaff.setReason("员工状态处于待确认离职");
            }*/
            if (StringUtils.isNotBlank(talentStaff.getReason())) {
                staffMap.put(talentStaff.getId(), talentStaff.getReason());
            }
        }
        if (!hrBillDTO.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey()) && billCheckResult.getInsured() == 0) {
            // 无人参保也可创建薪酬单
            throw new CommonException("该客户下的所有子客户不存在参保员工！");
        }
        List<HrClient> hrClientList = hrClientService.listByIds(usedClientIds);
        List<String> clientName = hrClientList.stream().map(HrClient::getClientName).collect(Collectors.toList());
        //可以创建薪酬账单的客户是否创建了保障账单
        if (!hrBillDTO.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())) {
            hrBillDTO.setClientIdList(usedClientIds);
            hrBillDTO.setBillType(BillEnum.BillType.SECURITY_BILL.getKey());
            List<HrBillDTO> billList = this.hrBillRepository.findBillList(hrBillDTO);
            if (!billList.isEmpty()) {
                List<String> collect = billList.stream().map(HrBillDTO::getClientName).collect(Collectors.toList());
                clientName.removeAll(collect);
            }
        }
        billCheckResult.setNotGuaranteeBill(clientName.size());

        Map<String, Object> result = new HashMap<>();
        if (billCheckResult.getNotInCalculation() != 0 || billCheckResult.getNotGuaranteeBill() != 0) {
            if (billCheckResult.getNotInCalculation() != 0) {
                String checkInCalculationMsg = "本期账单工包含已参保的员工 " + billCheckResult.getInsured() + " 人，其中："
                    + (billCheckResult.getOnJob() == 0 ? "" : "在职员工" + billCheckResult.getOnJob() + "人；")
                    + (billCheckResult.getMaternityLeave() == 0 ? "" : "产假期员工" + billCheckResult.getMaternityLeave() + "人；")
                    + (billCheckResult.getMedicalPeriod() == 0 ? "" : "医疗期员工" + billCheckResult.getMedicalPeriod() + "人；")
                    + (billCheckResult.getInPractice() == 0 ? "" : "实习中员工" + billCheckResult.getInPractice() + "人；")
                    + (billCheckResult.getOnTrial() == 0 ? "" : "试用中员工" + billCheckResult.getOnTrial() + "人；");
                String checkNotInCalculationMsg = "本期账单包含不可入账员工" + billCheckResult.getNotInCalculation() + "人，其中： "
                    + (billCheckResult.getUninsured() == 0 ? "" : "未参保员工" + billCheckResult.getUninsured() + "人；")
                    + (billCheckResult.getUninsuredOnJob() == 0 ? "" : "未参保在职员工" + billCheckResult.getUninsuredOnJob() + "人；")
                    + (billCheckResult.getWaitingEmployment() == 0 ? "" : "待入职员工" + billCheckResult.getWaitingEmployment() + "人；")
                    + (billCheckResult.getInduction() == 0 ? "" : "入职中员工" + billCheckResult.getInduction() + "人；")
                    + (billCheckResult.getLeaving() == 0 ? "" : "离职中员工" + billCheckResult.getLeaving() + "人；")
                    + (billCheckResult.getQuit() == 0 ? "" : "已参保的离职员工" + billCheckResult.getQuit() + "人；")
                    + (billCheckResult.getRetire() == 0 ? "" : "已参保的退休员工" + billCheckResult.getRetire() + "人；");
                result.put("checkCode", 500);
                result.put("checkInCalculationMsg", checkInCalculationMsg);
                result.put("checkNotInCalculationMsg", checkNotInCalculationMsg);
                result.put("billCheckResult", JSON.toJSONString(billCheckResult));
                result.put("usedClientIdList", usedClientIds);
                result.put("staffMap", staffMap);
            }
            if (billCheckResult.getNotGuaranteeBill() != 0) {
                result.put("checkCode", 500);
                String checkUsedClientMsg = "本期账单包含可以创建账单的客户 " + usedClientIds.size() + " 个"
                    + (billCheckResult.getNotGuaranteeBill() == 0 ? "；" : ", 其中 " + String.join(",", clientName) + " 未创建保障账单"
                    + "(未创建保障账单的员工在计算个税时其专项扣除将默认为0)");
                result.put("checkUsedClientMsg", checkUsedClientMsg);
                result.put("usedClientIdList", usedClientIds);
                result.put("staffMap", staffMap);
            }
        } else {
            result.put("checkCode", 200);
            result.put("checkMsg", "账单创建初步检查通过！");
            result.put("usedClientIdList", usedClientIds);
        }
        return result;
    }

    /**
     * 未入账员工明细
     * @param params
     * @return
     */
    @Override
    public List<HrTalentStaffDTO> unrecordedDetails(Map<String, Object> params) {
        List<String> usedClientIdList = (List<String>) params.get("usedClientIdList");
        Map<String, String> staffMap = (Map<String, String>) params.get("staffMap");
        if (CollectionUtils.isEmpty(usedClientIdList)) {
            throw new CommonException("暂无可以创建账单的客户！");
        }
        List<HrTalentStaff> hrTalentStaffList = hrTalentStaffRepository.selectBatchIds(staffMap.keySet());
        List<HrTalentStaffDTO> hrTalentStaffDTOList = hrTalentStaffMapper.toDto(hrTalentStaffList);;
        List<String> billIds = new ArrayList<>();
        usedClientIdList.forEach(clientId -> {
            HrBill hrBill = hrBillRepository.selectOne(new QueryWrapper<HrBill>()
                .eq("is_delete", 0).eq("is_official", 1).eq("client_id", clientId)
                .orderByDesc("created_date").last("LIMIT 1"));
            if (hrBill != null) {
                billIds.add(hrBill.getId());
            }
        });
        List<HrBillDetail> hrBillDetailList = new ArrayList<>();
        if (billIds != null && !billIds.isEmpty()) {
            hrBillDetailList = hrBillDetailRepository.selectList(new QueryWrapper<HrBillDetail>().eq("is_delete", 0).eq("check_on", 1).in("bill_id", billIds));
        }
        for (HrTalentStaffDTO hrTalentStaffDTO : hrTalentStaffDTOList) {
            String reason = staffMap.get(hrTalentStaffDTO.getId());
            hrTalentStaffDTO.setReason(reason);
            if (CollectionUtils.isNotEmpty(hrBillDetailList)){
                HrBillDetail hrBillDetail = hrBillDetailList.stream().filter(ls -> ls.getStaffId() != null && ls.getStaffId().equals(hrTalentStaffDTO.getId())).findAny().orElse(null);
                if (hrBillDetail != null) {
                    hrTalentStaffDTO.setCheckOn(1);
                }
            }
        }
        return hrTalentStaffDTOList.stream().sorted(Comparator.comparing(HrTalentStaffDTO::getCheckOn)).collect(Collectors.toList());
    }

    /**
     * 验证客户协议是否正常 并返回正常协议
     *
     * @param clientId
     * @return
     */
    @Override
    public HrProtocol checkProtocol(String clientId) {
        // 在协议表中  根据客户id和使用状态来查协议
        // new QueryWrapper<HrProtocol>().eq("client_id", clientId).eq("use_status", 1).eq("is_delete", 0);
        List<HrProtocol> hrProtocolList = hrProtocolRepository.selectUseingProtocolByClientId(clientId);
        if (hrProtocolList == null || hrProtocolList.isEmpty()) {
            throw new CommonException("客户未签署任何协议!");
        }
        if (hrProtocolList.size() != 1) {
            throw new CommonException("客户签署多份协议!");
        }
        return hrProtocolList.get(0);
    }

    /**
     * 薪酬账单-重新计算
     * @param hrBillDTO
     * @return
     */
    @Override
    public List<HrBillDetailDTO> recalculationHrBillDetail(HrBillDTO hrBillDTO) {
        List<HrBillDetailDTO> billDetailList = hrBillDTO.getBillDetailList();
        HrBillDetailDTO dto = billDetailList.get(0);
        Integer personalTaxStart = this.codeTableRepository.getValueByInnerName("personalTaxStart");
        List<Map<String, BigDecimal>> incomeMap = this.hrSpecialDeductionRepository.getSumCurrentIncomeGroupClient(LocalDate.of(dto.getPayYear(), dto.getPayMonthly(), 1));
        // 若为1月 不再获取前两个月的数据
        // 若为2月，只获取第一个月的数据
        // 若大于2月，获取前两个月数据
        int lastMonthly = dto.getPayMonthly();
        if (dto.getPayMonthly() == 2) {
            // 获取上月月份
            lastMonthly = dto.getPayMonthly() - 1;
        } else if (dto.getPayMonthly() > 2) {
            // 获取上月月份
            lastMonthly = dto.getPayMonthly() - 1;
            // 获取上上月月份
            // beforeLastMonthly = payMonthly - 2;
        }
        List<HrSpecialDeduction> specialDeductionList = this.hrSpecialDeductionRepository.getAllByStartDate(LocalDate.of(dto.getPayYear(), dto.getPayMonthly(), 1),LocalDate.of(dto.getPayYear(), lastMonthly, 1));
        List<HrQuickDeduction> hrQuickDeductions = this.hrQuickDeductionRepository.selectAll();
        List<HrBillDetailDTO> billDetailByBill = hrBillDetailRepository.getBillDetailByBill(
            new HrBillDetailDTO().setPayYear(hrBillDTO.getPayYear()).setPayMonthly(hrBillDTO.getPayMonthly()).setBillType(BillEnum.BillType.SECURITY_BILL.getKey()).setBillPurpose(1)
        );
        //获取未使用的补差
        List<HrWelfareCompensation>  welfareCompensations = hrWelfareCompensationRepository.getUnUsedWelfareCompensation(hrBillDTO.getBillType(), hrBillDTO.getPaymentDate());
        //薪酬账单
        List<HrBillDetailDTO> hrBillDetailDTOS = hrBillDetailRepository.selectSalaryBill(new HrBillDetailDTO().setPayYear(hrBillDTO.getPayYear()).setPayMonthly(hrBillDTO.getPayMonthly()));
        int lastPayYear = hrBillDTO.getPayMonthly() > 1 ?  hrBillDTO.getPayYear() : hrBillDTO.getPayYear() - 1;
        int lastPayMonthly = hrBillDTO.getPayMonthly() > 1 ? hrBillDTO.getPayMonthly() - 1 : 12;
        List<Integer> typeList = Arrays.asList(BillEnum.GuidePlateType.SOCIAL_SECURITY_GUIDE.getKey(),
            BillEnum.GuidePlateType.MEDICAL_GUIDE.getKey(),
            BillEnum.GuidePlateType.ACCUMULATION_FUND_GUIDE.getKey(),
            BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey());
        List<HrBillCompareResultDTO> hrBillCompareResultDTOList = hrBillCompareResultRepository.getBillCompareResult(hrBillDTO.getPayYear(), hrBillDTO.getPayMonthly(), lastPayYear, lastPayMonthly, typeList);
        for (HrBillDetailDTO detailDTO : billDetailList) {
            BigDecimal personalTaxMakeUp = detailDTO.getPersonalTaxMakeUp();
            this.getDataBySecurityBill(detailDTO, billDetailByBill, hrBillDetailDTOS, welfareCompensations, hrBillCompareResultDTOList, lastPayYear, lastPayMonthly);
            detailDTO.setPersonalTaxMakeUp(personalTaxMakeUp);
            AtomicReference<BigDecimal> sumIncome = new AtomicReference<>(BigDecimal.ZERO);
            incomeMap.forEach(map ->{
                sumIncome.set(map.get(detailDTO.getClientId()));
            });
            BigDecimal sumCurrentIncome = sumIncome.get();
            this.calculateTaxAndOther(detailDTO,detailDTO.getSalary(),personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
        }
        return billDetailList;
    }

    /**
     * 社会治理账单用途切换
     * @param hrBillDTO
     * @return
     */
    @Override
    public List<HrBillDetailDTO> handleSocialGovernanceDetail(HrBillDTO hrBillDTO) {
        List<String> clientIdList;
        if (CollectionUtils.isEmpty(hrBillDTO.getClientIdList())){
            clientIdList = Collections.singletonList(hrBillDTO.getClientId());
        }else {
            clientIdList = hrBillDTO.getClientIdList();
        }
        // 获取账单配置
        HrBillDynamicFieldsDTO dynamicFieldsDTO = this.hrBillDynamicFieldsRepository.getByBillId(hrBillDTO.getId());
        String originBillUrl = dynamicFieldsDTO.getOriginBillUrl();
        if (cn.casair.common.utils.StringUtils.isEmpty(originBillUrl)) {
            throw new CommonException("未查询到薪酬原单,请重新上传!");
        }
        // 解析工作簿的数据,得到map结构的数据
        BillFieldInfoDTO billFieldInfoDTO = dynamicFieldsDTO.getBillFieldInfoDTO();
        Integer dataStartRows = dynamicFieldsDTO.getDataStartRows();

        Map<String, BillExcelDataDTO> excelDataMap = BillParseUtils.parseExcel2MapData(billFieldInfoDTO, dataStartRows, dynamicFieldsDTO.getOriginBillUrl(), dynamicFieldsDTO.getOriginBillUrl(), 0);
        if (excelDataMap.size() == 0) {
            throw new CommonException("薪酬原单中未解析到数据,操作失败！");
        }
        // 获取一个值为0的动态费用列表,作为数据填充使用
        List<HrBillDetailItemsDTO> initDynamicFee = this.hrBillDetailItemsService.getInitDynamicFee(excelDataMap);
        Map<String, BillExcelDataDTO> specialBillExcelDataMap = new HashMap<>();
        SpecialBillClient specialBillClient = judgeSocialBillClient(clientIdList);

        int lastPayYear;
        int lastPayMonthly;
        // 上个缴费年月
        if (hrBillDTO.getPayMonthly() > 1) {
            lastPayYear = hrBillDTO.getPayYear();
            lastPayMonthly = hrBillDTO.getPayMonthly() - 1;
        } else {
            lastPayYear = hrBillDTO.getPayYear() - 1;
            lastPayMonthly = 12;
        }
        // 处理特殊处理客户 薪酬账单 组装表头
        this.hrBillDetailItemsService.dealSpecialBillClient(specialBillClient, clientIdList, specialBillExcelDataMap, lastPayYear, lastPayMonthly, dynamicFieldsDTO);

        Integer personalTaxStart = this.codeTableRepository.getValueByInnerName("personalTaxStart");
        List<Map<String, BigDecimal>> incomeMap = this.hrSpecialDeductionRepository.getSumCurrentIncomeGroupClient(LocalDate.of(hrBillDTO.getPayYear(), hrBillDTO.getPayMonthly(), 1));
        // 若为1月 不再获取前两个月的数据
        // 若为2月，只获取第一个月的数据
        // 若大于2月，获取前两个月数据
        int lastMonthly = hrBillDTO.getPayMonthly();
        if (hrBillDTO.getPayMonthly() == 2) {
            // 获取上月月份
            lastMonthly = hrBillDTO.getPayMonthly() - 1;
        } else if (hrBillDTO.getPayMonthly() > 2) {
            // 获取上月月份
            lastMonthly = hrBillDTO.getPayMonthly() - 1;
            // 获取上上月月份
            // beforeLastMonthly = payMonthly - 2;
        }
        List<HrSpecialDeduction> specialDeductionList = this.hrSpecialDeductionRepository.getAllByStartDate(LocalDate.of(hrBillDTO.getPayYear(), hrBillDTO.getPayMonthly(), 1),LocalDate.of(hrBillDTO.getPayYear(), lastMonthly, 1));
        List<HrQuickDeduction> hrQuickDeductions = this.hrQuickDeductionRepository.selectAll();
        List<HrBillDetailDTO> billDetailByBill = hrBillDetailRepository.getBillDetailByBill(
            new HrBillDetailDTO()
                .setPayYear(hrBillDTO.getPayYear())
                .setPayMonthly(hrBillDTO.getPayMonthly())
                .setBillType(BillEnum.BillType.SECURITY_BILL.getKey())
                .setBillPurpose(1)
        );
        List<HrBillDetailDTO> billDetailList = hrBillDTO.getBillDetailList();
        //上个月薪酬账单
        List<String> idCardList = billDetailList.stream().map(HrBillDetailDTO::getCertificateNum).collect(Collectors.toList());
        List<HrBillDetailDTO> lastBillDetailDTOList = hrBillDetailRepository.getSalaryBillBatchClientId(lastPayYear, lastPayMonthly, null, idCardList);
        for (HrBillDetailDTO billDetailDTO : billDetailList) {
            AtomicReference<BigDecimal> sumIncome = new AtomicReference<>(BigDecimal.ZERO);
            incomeMap.forEach(map ->{
                sumIncome.set(map.get(billDetailDTO.getClientId()));
            });
            BigDecimal sumCurrentIncome = sumIncome.get();
            if (hrBillDTO.getBillPurpose() == 1 ) {
                //上月离职人员的动态项需重新定义
                if (billDetailDTO.getStaffStatus() != null && ((billDetailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())
                    || billDetailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey()))
                    && billDetailDTO.getResignationDate().getYear() == lastPayYear && billDetailDTO.getResignationDate().getMonthValue() == lastPayMonthly)) {
                    List<HrBillDetailItemsDTO> itemsDTOList = hrBillDetailItemsRepository.getByBillDetailId(Collections.singletonList(billDetailDTO.getId()));
                    billDetailDTO.setHrBillDetailItemsList(itemsDTOList);
                }
                this.calculateTaxAndOther(billDetailDTO,billDetailDTO.getSalary(), personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
            }else {
                List<HrBillDetailItemsDTO> detailItemsList = billDetailDTO.getHrBillDetailItemsList();
                boolean notCalSign = this.handleFillSalaryParam(specialBillClient, billDetailDTO, detailItemsList, specialBillExcelDataMap, hrBillDTO, lastPayYear, lastPayMonthly, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions, billDetailByBill, lastBillDetailDTOList, initDynamicFee);
                if (notCalSign) {
                    if (!specialBillClient.equals(SpecialBillClient.EAST_DISTRICT_PUBLIC_SECURITY)) {
                        if (billDetailDTO.getStaffStatus() != null && !billDetailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.RETIRE.getKey())) {
                            this.fillSpecialBillDetailItems(detailItemsList, billDetailDTO.getSalary());
                        }
                    } else {
                        //将东区公安离职的员工费用增项重新赋值
                        this.fillEastSecurityBillDetailItems(billDetailDTO, specialBillExcelDataMap, detailItemsList);
                    }
                    billDetailDTO.setHrBillDetailItemsList(detailItemsList);
                }
            }
        }
        return billDetailList;
    }


    /**
     * 特殊客户填充薪酬账单 应发工资,税前应发,个税,实发工资,费用合计,免税收入等
     */
    @Override
    public boolean handleFillSalaryParam(SpecialBillClient specialBillClient, HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItems, Map<String, BillExcelDataDTO> specialBillExcelDataMap, HrBillDTO hrBillDTO, Integer lastPayYear, Integer lastPayMonthly,
                                         Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions, List<HrBillDetailDTO> billDetailByBill, List<HrBillDetailDTO> lastBillDetailDTOList, List<HrBillDetailItemsDTO> initDynamicFee) {
        // 不参与计算标识
        boolean notCalSign = false;
        switch (specialBillClient) {
            case EAST_DISTRICT_PUBLIC_SECURITY:
                // 上月入职人员
                if (detailDTO.getStaffStatus() != null && !detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey()) && detailDTO.getBoardDate() != null && detailDTO.getBoardDate().getYear() == lastPayYear && detailDTO.getBoardDate().getMonthValue() == lastPayMonthly) {
                    this.dealFillSpecialSalaryParamForNewJoined(detailDTO, hrBillDTO, dynamicFeeItems, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions, billDetailByBill);
                    notCalSign = true;
                }
                // 上月离职
                else if (detailDTO.getStaffStatus() != null && (detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey()) || detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey()))
                    && detailDTO.getResignationDate() != null && detailDTO.getResignationDate().getYear() == lastPayYear && detailDTO.getResignationDate().getMonthValue() == lastPayMonthly) {
                    BillExcelDataDTO billExcelDataDTO = specialBillExcelDataMap.get(detailDTO.getCertificateNum());
                    if (billExcelDataDTO != null){
                        notCalSign = true;
                        this.fillEastSecuritySalaryParamForResigned(detailDTO, billExcelDataDTO.getDynamicFeeItems());
                    }
                }
                //处理退休人员
                else if (detailDTO.getStaffStatus() != null && detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.RETIRE.getKey()) && lastBillDetailDTOList != null && !lastBillDetailDTOList.isEmpty()){
                    notCalSign = true;
                    this.fillSpecialSalaryParamForRetire(detailDTO, lastBillDetailDTOList, initDynamicFee, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
                }
                else {
                    this.fillSalaryParam(detailDTO, dynamicFeeItems, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
                }
                break;
            case SOCIAL_GOVERNANCE:
            case SECONDARY_POLITICS_LAW_COMMITTEE:
                // 上月入职人员
                if (detailDTO.getStaffStatus() != null && !detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey()) && detailDTO.getBoardDate() != null && detailDTO.getBoardDate().getYear() == lastPayYear && detailDTO.getBoardDate().getMonthValue() == lastPayMonthly) {
                    if (specialBillClient.equals(SpecialBillClient.SECONDARY_POLITICS_LAW_COMMITTEE)){
                        notCalSign = true;
                        this.dealFillSpecialSalaryParamForNewJoined(detailDTO, hrBillDTO, dynamicFeeItems, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions, billDetailByBill);
                    } else {
                        //社会治理正常处理上月入职人员
                        this.fillSalaryParam(detailDTO, dynamicFeeItems, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
                    }
                }
                // 上月离职
                else if (detailDTO.getStaffStatus() != null && (detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey()) || detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey()))
                    && detailDTO.getResignationDate() != null && detailDTO.getResignationDate().getYear() == lastPayYear && detailDTO.getResignationDate().getMonthValue() == lastPayMonthly) {
                    BillExcelDataDTO billExcelDataDTO = specialBillExcelDataMap.get(detailDTO.getCertificateNum());
                    if (billExcelDataDTO != null){
                        notCalSign = true;
                        this.fillSpecialSalaryParamForResigned(detailDTO, billExcelDataDTO.getDynamicFeeItems());
                    }
                } else {
                    this.fillSalaryParam(detailDTO, dynamicFeeItems, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
                }
                break;
            case HAIER:
                this.fillSalaryParam(detailDTO, dynamicFeeItems, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
                break;
            default:
                break;
        }
        return notCalSign;
    }

    /**
     * 特殊处理客户上月入职人员数据项填充
     *  @param detailDTO
     * @param hrBillDTO
     * @param dynamicFeeItems
     * @param billDetailByBill
     */
    private void dealFillSpecialSalaryParamForNewJoined(HrBillDetailDTO detailDTO, HrBillDTO hrBillDTO, List<HrBillDetailItemsDTO> dynamicFeeItems,
                                                        Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList,
                                                        List<HrQuickDeduction> hrQuickDeductions, List<HrBillDetailDTO> billDetailByBill) {
        // 获取本月保障账单
        HrBillDetailDTO hrSecurityBillDetailDTO = billDetailByBill.stream().filter(lst -> lst.getStaffId().equals(detailDTO.getStaffId())).findFirst().orElse(null);
        this.fillSpecialSalaryParamForNewJoined(detailDTO, dynamicFeeItems, hrSecurityBillDetailDTO, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
    }

    /**
     * 将特殊处理的员工其中一项费用增项设置为工资
     *
     * @param initDynamicFee
     * @param salary
     */
    @Override
    public void fillSpecialBillDetailItems(List<HrBillDetailItemsDTO> initDynamicFee, BigDecimal salary) {
        for (HrBillDetailItemsDTO hrBillDetailItemsDTO : initDynamicFee) {
            if (hrBillDetailItemsDTO.getExpenseType().equals("1")) {
                hrBillDetailItemsDTO.setAmount(salary);
                break;
            }
        }
    }

    @Override
    public void salaryBillAmount() {
        //先处理只有一个薪酬账单的数据
        List<String> ids = hrBillRepository.findRepeatBill();
        List<String> idList = new ArrayList<>();
        ids.forEach(id->{ idList.addAll(Arrays.asList(id.split(","))); });
        List<HrBillDetail> hrBillDetailList = hrBillDetailRepository.findByBillId(1,null);//保障账单数据
        List<HrBillDetail> hrBillDetailSalaryList = hrBillDetailRepository.findByBillId(0,null);//不存在重复账单的薪酬账单数据
        List<HrBillDetail> list = new ArrayList<>();
        for (HrBillDetail hrBillDetail : hrBillDetailSalaryList) {
            if (!BigDecimalCompare.of(hrBillDetail.getSalary()).eq(BigDecimal.ZERO) && !BigDecimalCompare.of(hrBillDetail.getSalary()).eq(hrBillDetail.getPreTaxSalary())) {
                HrBillDetail detailDTO = hrBillDetailList.stream().filter(lst -> lst.getPayYear().equals(hrBillDetail.getPayYear()) && lst.getPayMonthly().equals(hrBillDetail.getPayMonthly()) && lst.getCertificateNum().equalsIgnoreCase(hrBillDetail.getCertificateNum())).findFirst().orElse(null);
                if (detailDTO != null){
                    hrBillDetail.setPersonalPension(detailDTO.getPersonalPension() == null ? BigDecimal.ZERO : detailDTO.getPersonalPension());
                    hrBillDetail.setPersonalUnemployment(detailDTO.getPersonalUnemployment() == null ? BigDecimal.ZERO : detailDTO.getPersonalUnemployment());
                    hrBillDetail.setPersonalMedical(detailDTO.getPersonalMedical() == null ? BigDecimal.ZERO : detailDTO.getPersonalMedical());
                    hrBillDetail.setPersonalMaternity(detailDTO.getPersonalMaternity() == null ? BigDecimal.ZERO : detailDTO.getPersonalMaternity());
                    hrBillDetail.setPersonalSocialSecurityMakeUp(detailDTO.getPersonalSocialSecurityMakeUp() == null ? BigDecimal.ZERO : detailDTO.getPersonalSocialSecurityMakeUp());
                    hrBillDetail.setPersonalAccumulationFundMakeUp(detailDTO.getPersonalAccumulationFundMakeUp() == null ? BigDecimal.ZERO : detailDTO.getPersonalAccumulationFundMakeUp());
                    hrBillDetail.setLastModifiedDate(LocalDateTime.now());
                    hrBillDetailRepository.updateById(hrBillDetail);
                }else {
                    list.add(hrBillDetail);
                }
            }
        }
        //处理存在多个薪酬账单但只有一个账单存在社保数据
        List<HrBillDetail> list1 = new ArrayList<>();
        List<HrBillDetail> hrBillDetailList1 = hrBillDetailRepository.selectList(new QueryWrapper<HrBillDetail>().in("bill_id", idList).eq("is_used", 1).eq("type", 0));
        for (HrBillDetail billDetail : hrBillDetailList1) {
            if (!BigDecimalCompare.of(billDetail.getSalary()).eq(BigDecimal.ZERO) && !BigDecimalCompare.of(billDetail.getSalary()).eq(billDetail.getPreTaxSalary())){
                HrBillDetail detailDTO = hrBillDetailList.stream().filter(lst -> lst.getPayYear().equals(billDetail.getPayYear()) && lst.getPayMonthly().equals(billDetail.getPayMonthly()) && lst.getCertificateNum().equalsIgnoreCase(billDetail.getCertificateNum())).findFirst().orElse(null);
                if (detailDTO != null){
                    billDetail.setPersonalPension(detailDTO.getPersonalPension() == null ? BigDecimal.ZERO : detailDTO.getPersonalPension());
                    billDetail.setPersonalUnemployment(detailDTO.getPersonalUnemployment() == null ? BigDecimal.ZERO : detailDTO.getPersonalUnemployment());
                    billDetail.setPersonalMedical(detailDTO.getPersonalMedical() == null ? BigDecimal.ZERO : detailDTO.getPersonalMedical());
                    billDetail.setPersonalMaternity(detailDTO.getPersonalMaternity() == null ? BigDecimal.ZERO : detailDTO.getPersonalMaternity());
                    billDetail.setPersonalSocialSecurityMakeUp(detailDTO.getPersonalSocialSecurityMakeUp() == null ? BigDecimal.ZERO : detailDTO.getPersonalSocialSecurityMakeUp());
                    billDetail.setPersonalAccumulationFundMakeUp(detailDTO.getPersonalAccumulationFundMakeUp() == null ? BigDecimal.ZERO : detailDTO.getPersonalAccumulationFundMakeUp());
                    billDetail.setLastModifiedDate(LocalDateTime.now());
                    hrBillDetailRepository.updateById(billDetail);
                }else {
                    list1.add(billDetail);
                }
            }
        }

    }

    /**
     * 制动动态表头
     * @param clientIds
     * @return
     */
    @Override
    public List<DynamicHeadersDTO> dynamicHeader(List<String> clientIds) {
        HrSocialSecurityDTO hrSocialSecurity = this.getClientSocialSecurity(clientIds);
        if (hrSocialSecurity == null){
            return new ArrayList<>();
        }
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> specialFieldDTOList = hrSocialSecurity.getSpecialFieldDTOList();
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> aloneCardinalDTOList = hrSocialSecurity.getAloneCardinalDTOList();
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> mergeCardinalDTOList = hrSocialSecurity.getMergeCardinalDTOList();
        List<DynamicHeadersDTO> list = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(specialFieldDTOList)){
            dealComparePartDynamicHeader(list,specialFieldDTOList,"缴纳部分","Payment",3);
        }
        if (CollectionUtils.isNotEmpty(aloneCardinalDTOList)){
            dealComparePartDynamicHeader(list,aloneCardinalDTOList,"基数","Cardinal",2);
        }
        if (CollectionUtils.isNotEmpty(mergeCardinalDTOList)){
            dealComparePartDynamicHeader(list,mergeCardinalDTOList,"基数","Cardinal",1);
        }
        return list;
    }

    /**
     * 获取客户配置的社保类型
     *
     * @param clientIds
     * @return
     */
    @Override
    public HrSocialSecurityDTO getClientSocialSecurity(List<String> clientIds) {
        List<HrClient> hrClientList = hrClientRepository.selectBatchIds(clientIds);
        List<String> socialSecurityTypeIds = hrClientList.stream().filter(lst -> lst.getSocialSecurityTypeId() != null).map(HrClient::getSocialSecurityTypeId).collect(Collectors.toList());
        //所有客户没有绑定社保类型 或者 某个客户没有绑定社保类型
        if (socialSecurityTypeIds.isEmpty() || socialSecurityTypeIds.size() != hrClientList.size()) {
            return null;
        }
        //客户绑定的社保类型存在多个
        List<String> socialSecurityTypeIdList = socialSecurityTypeIds.stream().distinct().collect(Collectors.toList());
        if (socialSecurityTypeIdList.size() != 1){
            return null;
        }
        HrSocialSecurityDTO hrSocialSecurity = hrSocialSecurityService.getHrSocialSecurity(socialSecurityTypeIdList.get(0));
        //客户有绑定社保类型但是没有配置险种
        if (hrSocialSecurity == null || hrSocialSecurity.getSocialSecurityConfigId() == null){
            return null;
        }else {
            return hrSocialSecurity;
        }
    }

    /**
     * 根据查询条件查询账单，返回树形结构
     *
     * @param billNo
     * @param clientId
     * @return
     */
    @Override
    public List<HrBillDTO> findByBillNo(String billNo, String clientId) {
        // 先根据这个id查询该客户下的下级客户
        List<HrBillDTO> list = this.hrBillRepository.findByBillNo(billNo, clientId);
        list.forEach(ls -> {
            // 获取账单明细
//            ls.setBillDetailList(this.hrBillDetailRepository.getByBillId(ls.getId()));
        });

        Map<String, HrBillDTO> map = new LinkedHashMap<>();

        for (HrBillDTO bill : list) {
            bill.setChildren(new ArrayList<HrBillDTO>());
            map.put(bill.getClientId(), bill);
        }

        //当codeTable的parentId从 map中可以获取到时候，把这个codeTable对应的map中的node取出来，放到parentId的node的children中
        for (HrBillDTO bill : list) {
            if (map.containsKey(bill.getParentClientId())) {
                map.get(bill.getParentClientId()).getChildren().add(map.get(bill.getClientId()));
            }
            if (bill.getBillState() == 0) {
                if (!"0".equals(bill.getParentClientId())) {
                    if(map.containsKey(bill.getParentClientId())) {
                        map.get(bill.getParentClientId()).setBillState(0);
                    }
                }
            }
        }

        List<HrBillDTO> trees = new ArrayList<>();
        for (HrBillDTO treeNode : map.values()) {
            if (clientId.equals(treeNode.getClientId())) {
                removeEmptyItem(treeNode);
                trees.add(treeNode);
            }
        }

//        for (HrBillDTO treeNode : map.values()) {
//            if (clientId.equals(treeNode.getParentClientId())) {
//                removeEmptyItem(treeNode);
//                trees.add(treeNode);
//            }
//        }

        return trees;
    }

    /**
     * 批量拷贝账单
     *
     * @param hrBillDTO
     */
    @Override
    public HrBillDTO copyHrBillBatch(HrBillDTO hrBillDTO) {
        HrBillDTO result = new HrBillDTO();
        List<HrBillDetailDTO> diffList = new ArrayList<>();
        for (String billId : hrBillDTO.getBillIdList()) {
            HrBillDTO billDTO = new HrBillDTO();
            billDTO.setId(billId);
            billDTO.setPaymentDate(hrBillDTO.getPaymentDate());
            diffList.addAll(copyHrBill(billDTO).getDifferentList());
        }
        result.setDifferentList(diffList);
        return result;
    }

    private void removeEmptyItem(HrBillDTO treeNode) {
        if (!treeNode.getChildren().isEmpty()) {
            for (HrBillDTO child : treeNode.getChildren()) {
                removeEmptyItem(child);
            }
            List<HrBillDTO> children = treeNode.getChildren().stream().filter(item -> item.getBillNo() != null || item.getChildren() != null).collect(Collectors.toList());
            treeNode.setChildren(children.isEmpty() ? null: children);
        } else {
            treeNode.setChildren(null);
        }
    }

    private void dealComparePartDynamicHeader(List<DynamicHeadersDTO> list, List<HrSocialSecurityDTO.HrSocialHeadersDTO> headersDTOS, String topTitle, String topKey, Integer flag) {
        Map<String, List<HrSocialSecurityDTO.HrSocialHeadersDTO>> collect = headersDTOS.stream().collect(Collectors.groupingBy(HrSocialSecurityDTO.HrSocialHeadersDTO::getGroupName));
        for (Map.Entry<String, List<HrSocialSecurityDTO.HrSocialHeadersDTO>> stringListEntry : collect.entrySet()) {
            DynamicHeadersDTO dynamicHeadersDTO = new DynamicHeadersDTO();
            dynamicHeadersDTO.setTitle((stringListEntry.getKey().equals("unit") ? "单位" : "个人") + topTitle);
            dynamicHeadersDTO.setKey(stringListEntry.getKey() + topKey);
            dynamicHeadersDTO.setChildren(new ArrayList<>());

            stringListEntry.getValue().forEach(ls -> {
                DynamicHeadersDTO temp = new DynamicHeadersDTO();
                temp.setTitle(flag == 1 ? String.join("|",ls.getFieldNameList()) : ls.getFieldName());
                temp.setKey(flag == 1 ? ls.getFieldKeyList().get(0): ls.getFieldKey());
                if (flag == 1){
                    temp.setDataIndexS(ls.getFieldKeyList());
                }
                dynamicHeadersDTO.getChildren().add(temp);
            });
            list.add(dynamicHeadersDTO);
        }
    }


    /**
     * 判断账单特殊处理客户
     *
     * @param clientIdList
     */
    private SpecialBillClient judgeSocialBillClient(List<String> clientIdList) {
        // 初步匹配
        for (String clientId : clientIdList) {
            SpecialBillClient enumByKey = EnumUtils.getEnumByKey(SpecialBillClient.class, clientId);
            if (enumByKey != null) {
                return enumByKey;
            }
        }
        HrClient hrClient = hrClientRepository.getRootParentClient(clientIdList.get(0));
        return EnumUtils.getEnumByKey(SpecialBillClient.class, hrClient.getId());
    }


    /**
     * 根据账单查看动态表头
     * @param billIds
     * @return
     */
    @Override
    public List<DynamicHeadersDTO> showDynamicHeader(List<String> billIds) {
        List<HrBillDTO> hrBillList = hrBillRepository.findSocialConfig(billIds, BillEnum.BillType.SECURITY_BILL.getKey());
        if (hrBillList == null || hrBillList.isEmpty()){
            return new ArrayList<>();
        }
        List<String> specialField = hrBillList.stream().filter(lst -> lst.getSpecialField() != null).map(HrBillDTO::getSpecialField).distinct().collect(Collectors.toList());
        List<String> aloneCardinal = hrBillList.stream().filter(lst -> lst.getAloneCardinal() != null).map(HrBillDTO::getAloneCardinal).distinct().collect(Collectors.toList());
        List<String> mergeCardinal = hrBillList.stream().filter(lst -> lst.getMergeCardinal() != null).map(HrBillDTO::getMergeCardinal).distinct().collect(Collectors.toList());
        if (specialField != null && specialField.size() > 1){
            return new ArrayList<>();
        }
        if (aloneCardinal != null && aloneCardinal.size() > 1){
            return new ArrayList<>();
        }
        if (mergeCardinal != null && mergeCardinal.size() > 1){
            return new ArrayList<>();
        }
        HrBillDTO hrBillDTO = hrBillList.get(0);
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> specialFieldDTOList = hrBillDTO.getSpecialFieldDTOList();
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> aloneCardinalDTOList = hrBillDTO.getAloneCardinalDTOList();
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> mergeCardinalDTOList = hrBillDTO.getMergeCardinalDTOList();
        List<DynamicHeadersDTO> list = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(specialFieldDTOList)){
            dealComparePartDynamicHeader(list,specialFieldDTOList,"缴纳部分","Payment",3);
        }
        if (CollectionUtils.isNotEmpty(aloneCardinalDTOList)){
            dealComparePartDynamicHeader(list,aloneCardinalDTOList,"基数","Cardinal",2);
        }
        if (CollectionUtils.isNotEmpty(mergeCardinalDTOList)){
            dealComparePartDynamicHeader(list,mergeCardinalDTOList,"基数","Cardinal",1);
        }
        return list;
    }

    /**
     * 创建中石化账单汇总
     * @param hrBillDTO
     */
    @Override
    public void createSinopecBillTotal(HrBillDTO hrBillDTO) {
        HrBillTotal billTotalAll = this.hrBillTotalRepository.statisticCount(Collections.singletonList(hrBillDTO.getId()),null);
        billTotalAll.setBillId(hrBillDTO.getId())
            .setType(hrBillDTO.getBillType())
            .setPayYear(hrBillDTO.getPayYear())
            .setPayMonthly(hrBillDTO.getPayMonthly())
            .setSinopecType(1);
        this.hrBillTotalRepository.insert(billTotalAll);

        HrBillTotal billTotalDomestic = this.hrBillTotalRepository.statisticCount(Collections.singletonList(hrBillDTO.getId()),1);
        billTotalDomestic.setBillId(hrBillDTO.getId())
            .setType(hrBillDTO.getBillType())
            .setPayYear(hrBillDTO.getPayYear())
            .setPayMonthly(hrBillDTO.getPayMonthly())
            .setSinopecType(2);
        this.hrBillTotalRepository.insert(billTotalDomestic);
    }

    @Override
    public void maintainBillGrant(HrBillGrantDTO hrBillGrantDTO) {
        // 查询账单明细
        List<HrBillDetailDTO> billDetailList = new ArrayList<>();
        hrBillGrantDTO.getBillIds().forEach(it -> {
            HrBillDetailDTO billDetailDTO = new HrBillDetailDTO();
            billDetailDTO.setBillId(it);
            List<HrBillDetailDTO> list = this.hrBillDetailRepository.findSalaryPaymentList(billDetailDTO);
            if (!list.isEmpty()) {
                billDetailList.addAll(list);
            }
        });
        if (billDetailList.isEmpty()) {
            throw new CommonException("账单下没有查询到明细");
        }

        // 维护员工工资发放状态
        HrBillDetailGrantDTO hrBillDetailGrantDTO = new HrBillDetailGrantDTO();
        BeanUtils.copyProperties(hrBillGrantDTO, hrBillDetailGrantDTO);
        hrBillDetailGrantDTO.setBillDetailIds(billDetailList.stream().map(HrBillDetailDTO::getId).collect(Collectors.toList()));
        this.hrBillDetailService.maintainBillDetailGrant(hrBillDetailGrantDTO);
    }
}
