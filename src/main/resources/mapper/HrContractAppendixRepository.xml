<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrContractAppendixRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,contract_id,contract_num,`name`,type,state,template_id,seal_sign_id,sign_id,msg,appendix_id,appendix_path,fill_info,first_part_info,seconds_part_info,additional_info, original_path,
        is_delete,created_by,created_date,last_modified_by,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrContractAppendix">
        <id column="id" property="id"/>
        <result column="contract_id" property="contractId"/>
        <result column="contract_num" property="contractNum"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="state" property="state"/>
        <result column="template_id" property="templateId"/>
        <result column="seal_sign_id" property="sealSignId"/>
        <result column="sign_id" property="signId"/>
        <result column="msg" property="msg"/>
        <result column="appendix_id" property="appendixId"/>
        <result column="appendix_path" property="appendixPath"/>
        <result column="fill_info" property="fillInfo"/>
        <result column="first_part_info" property="firstPartInfo"/>
        <result column="seconds_part_info" property="secondsPartInfo"/>
        <result column="additional_info" property="additionalInfo"/>
        <result column="original_path" property="originalPath"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getFailAppendix" resultType="cn.casair.domain.HrContractAppendix">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_contract_appendix
        WHERE is_delete = 0
        AND state = 3
        AND contract_id = #{contractId}
    </select>

    <select id="getConfirmationTelegramList" resultType="cn.casair.dto.HrContractAppendixDTO">
        WITH a AS (
            SELECT
                id
            FROM
                hr_contract
            WHERE
                is_delete = 0
                AND staff_id = #{staffId}
            ORDER BY
                created_date
                DESC
            LIMIT 1
            )
        SELECT hca.id,
               hca.contract_id,
               hca.contract_num,
               hca.type,
               hca.template_id,
               hca.appendix_id,
               hca.appendix_path,
               hca.`name`,
               hca.state,
               hca.seal_sign_id,
               hca.sign_id,
               hca.msg,
               hct.type templateType
        FROM a
            LEFT JOIN hr_contract_appendix hca ON hca.contract_id = a.id
            LEFT JOIN hr_contract_template hct ON hca.template_id = hct.id
        WHERE hca.is_delete = 0
        ORDER BY hca.type,
                 hca.created_date
    </select>

    <select id="getStaffContractList" resultType="cn.casair.dto.HrContractAppendixDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_contract_appendix
        WHERE
            is_delete = 0
          AND contract_id = #{contractId}
          AND state IN (0 ,1, 2, 3, 4, 6)
        ORDER BY
            created_date
    </select>

    <select id="getStaffContractListByObject" resultType="cn.casair.dto.HrContractAppendixDTO">
        SELECT
            hca.*
        FROM
            hr_contract_appendix hca
            LEFT JOIN hr_contract hc ON hca.contract_id = hc.id
        WHERE
            hca.is_delete = 0
            AND hc.is_delete = 0
            AND hca.type IN ( 1, 2 )
            AND hc.staff_id = #{staffId}
            AND hca.contract_id = #{contractId}
        ORDER BY
            hca.type,
            hca.created_date DESC
    </select>

    <select id="getContractListByContractId" resultType="cn.casair.domain.HrContractAppendix">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_contract_appendix
        WHERE
            contract_id = #{contractId}
        ORDER BY
            type,
            created_date
    </select>

    <select id="getContractTemplateByContractId" resultType="cn.casair.domain.HrContractAppendix">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_contract_appendix
        WHERE contract_id = #{contractId}
        AND type = 1
        AND is_delete = 0
        ORDER BY created_date
    </select>

    <update id="setContractAppendixContractNum">
        UPDATE hr_contract_appendix
        SET contract_num = #{contractNum}
        WHERE id = #{id}
    </update>

    <update id="updateContractAppendix">
        UPDATE hr_contract_appendix
        SET
        <if test="param.appendixId!=null and param.appendixId!=''">
            appendix_id = #{param.appendixId},
        </if>
        <if test="param.appendixPath!=null and param.appendixPath!=''">
            appendix_path = #{param.appendixPath},
        </if>
        <if test="param.fillInfo!=null and param.fillInfo!=''">
            fill_info=#{param.fillInfo},
        </if>
        <if test="param.firstPartInfo!=null and param.firstPartInfo!=''">
            first_part_info = #{param.firstPartInfo},
        </if>
        <if test="param.secondsPartInfo!=null and param.secondsPartInfo!=''">
            seconds_part_info = #{param.secondsPartInfo},
        </if>
        <if test="param.originalPath!=null and param.originalPath!=''">
            original_path=#{param.originalPath},
        </if>
        last_modified_date=NOW(),
        is_delete = 0
        WHERE id = #{param.id}
    </update>

    <update id="changeContractAppendixState">
        UPDATE hr_contract_appendix
        SET is_delete = 0
        WHERE
        is_delete = 1
        AND id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="setContractAppendixSigned">
        UPDATE hr_contract_appendix
        SET state = 1
        WHERE id = #{id}
    </update>

    <update id="updateByContractId">
        UPDATE hr_contract_appendix
        SET is_delete = 0
        WHERE
            is_delete = 1
            AND contract_id = #{contractId}
            <if test="templateId!=null and templateId !=''">
                AND template_id = #{templateId}
            </if>
    </update>

    <delete id="deleteTemplatePretreatment">
        DELETE
        FROM `hr_contract_appendix`
        WHERE contract_id = #{contractId}
    </delete>

    <delete id="deleteAppendix">
        DELETE
        FROM `hr_contract_appendix`
        WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </delete>

    <delete id="deleteContractAppendix">
        DELETE
        FROM hr_contract_appendix
        WHERE contract_id = #{contractId}
        AND type IN ( 2, 3 )
    </delete>

    <delete id="deleteByObject">
        DELETE
        FROM hr_contract_appendix
        WHERE
            contract_id = #{params.contractId}
            AND template_id = #{params.templateId}
            AND type = #{params.type}
            AND state = #{params.state}
    </delete>

    <select id="getStaffContractListTemplate" resultType="cn.casair.dto.HrContractAppendixDTO">
        SELECT
            hca.*,
            hct.type templateType
        FROM
            hr_contract_appendix  hca
            LEFT JOIN hr_contract_template hct ON hca.template_id = hct.id
            AND hct.is_delete=0
        WHERE
            hca.is_delete = 0
            <if test="params.contractId!=null">
                AND contract_id = #{params.contractId}
            </if>
            <if test="params.type!=null">
                AND hca.type = #{params.type}
            </if>
        ORDER BY
            hca.created_date
    </select>

    <select id="getNewestLaborContract" resultType="java.lang.String">
        SELECT
            appendix_path
        FROM
            hr_contract_appendix
        WHERE
            is_delete = 0
          AND contract_id = #{contractId}
          AND type = 1
        ORDER BY
            created_date DESC
        LIMIT 1
    </select>

    <select id="getStaffContractListByContract" resultType="cn.casair.dto.HrContractAppendixDTO">
        SELECT id,
               name,
               appendix_id,
               appendix_path
        FROM hr_contract_appendix
        WHERE is_delete = 0
          AND contract_id = #{contractId}
          AND appendix_path IS NOT NULL
        ORDER BY type,
                 created_date
    </select>

    <select id="getStaffElectronicContractByStaffId" resultType="cn.casair.domain.HrContractAppendix">
        SELECT hca.id,
               hca.contract_id,
               hca.contract_num,
               hca.name,
               hca.type,
               hca.state,
               hca.template_id
        FROM hr_talent_staff hts
                 LEFT JOIN hr_contract hc ON hc.staff_id = hts.id
            AND hc.is_delete = 0
                 LEFT JOIN hr_contract_appendix hca ON hca.contract_id = hc.id
            AND hca.is_delete = 0
            AND hca.type = 1
        WHERE hts.is_delete = 0
          AND hts.id = #{staffId}
    </select>

    <select id="getStaffContractListBatch" resultType="cn.casair.dto.HrContractAppendixDTO">
        SELECT
            hca.id,
            hca.`name`,
            hca.appendix_id,
            hca.appendix_path,
            hc.staff_name
        FROM
            hr_contract_appendix hca
        LEFT JOIN hr_contract hc ON hca.contract_id = hc.id
        WHERE
            hca.is_delete = 0 AND hca.appendix_path IS NOT NULL AND hc.is_delete = 0
        <if test="params.ids!=null and params.ids.size()>0">
            AND hc.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="permissionClient!=null and permissionClient.size>0">
            AND hc.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.unitNumber!=null and params.unitNumber!=''">
            AND hc.unit_number LIKE CONCAT( '%', #{params.unitNumber}, '%' )
        </if>
        <if test="params.clientIds!=null and params.clientIds.size()>0">
            AND hc.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.systemNum!=null and params.systemNum!=''">
            AND hc.system_num LIKE CONCAT( '%', #{params.systemNum}, '%' )
        </if>
        <if test="params.staffName!=null and params.staffName!=''">
            AND hc.staff_name LIKE CONCAT( '%', #{params.staffName}, '%' )
        </if>
        <if test="params.idNo!=null and params.idNo!=''">
            AND hc.id_no LIKE CONCAT( '%', #{params.idNo}, '%' )
        </if>
        <if test="params.phone!=null and params.phone!=''">
            AND hc.phone LIKE CONCAT( '%', #{params.phone}, '%' )
        </if>
        <if test="params.states!=null and params.states.size()>0">
            AND hc.state IN
            <foreach collection="params.states" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.contractStartDateStart!=null">
            AND hc.contract_start_date &gt;= #{params.contractStartDateStart}
        </if>
        <if test="params.contractStartDateEnd!=null">
            AND hc.contract_start_date &lt;= #{params.contractStartDateEnd}
        </if>
        <if test="params.contractEndDateStart!=null">
            AND hc.contract_end_date &gt;= #{params.contractEndDateStart}
        </if>
        <if test="params.contractEndDateEnd!=null">
            AND hc.contract_end_date &lt;= #{params.contractEndDateEnd}
        </if>
        ORDER BY
            hca.type, hca.created_date
    </select>

</mapper>
