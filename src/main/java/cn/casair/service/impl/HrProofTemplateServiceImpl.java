package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.domain.HrContract;
import cn.casair.domain.HrProofTemplate;
import cn.casair.domain.User;
import cn.casair.dto.HrBillDetailDTO;
import cn.casair.dto.HrProofTemplateDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.formdata.InputLabel;
import cn.casair.dto.formdata.SelectionOption;
import cn.casair.dto.pdf.ContractInfoForPdf;
import cn.casair.mapper.HrProofTemplateMapper;
import cn.casair.repository.*;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrProofTemplateService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.util.SecurityUtils;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 证明模板服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrProofTemplateServiceImpl extends ServiceImpl<HrProofTemplateRepository, HrProofTemplate> implements HrProofTemplateService {

    private final HrProofTemplateRepository hrProofTemplateRepository;
    private final HrProofTemplateMapper hrProofTemplateMapper;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrContractRepository hrContractRepository;
    private final HrSealsRepository hrSealsRepository;
    private final HrBillDetailRepository hrBillDetailRepository;
    private final UserRepository userRepository;
    private final CodeTableService codeTableService;


    /**
     * 创建证明模板
     *
     * @param hrProofTemplateDTO
     * @return
     */
    @Override
    public HrProofTemplateDTO createHrProofTemplate(HrProofTemplateDTO hrProofTemplateDTO) {
        log.info("Create new HrProofTemplate:{}", hrProofTemplateDTO);
        List<HrProofTemplate> hrProofTemplates = hrProofTemplateRepository.selectList(new QueryWrapper<HrProofTemplate>().eq("title", hrProofTemplateDTO.getTitle()).eq("is_delete", 0));
        if (!hrProofTemplates.isEmpty()){
            throw new CommonException("该模板标题已存在！");
        }
        // 检测PDF模板文件表单域
        Set<String> formFieldList = PdfUtils.getAllFormField(hrProofTemplateDTO.getFileUrl());
        if (formFieldList == null || formFieldList.isEmpty()) {
            throw new CommonException("请上传有效的PDF模板！");
        }
        hrProofTemplateDTO.setLastModifiedDate(LocalDateTime.now());
        HrProofTemplate hrProofTemplate = this.hrProofTemplateMapper.toEntity(hrProofTemplateDTO);
        this.hrProofTemplateRepository.insert(hrProofTemplate);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.PROOF_TEMPLATE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrProofTemplateDTO),
            HrProofTemplateDTO.class,
            null,
            JSON.toJSONString(hrProofTemplate)
        );
        return this.hrProofTemplateMapper.toDto(hrProofTemplate);
    }

    /**
     * 修改证明模板
     *
     * @param hrProofTemplateDTO
     * @return
     */
    @Override
    public Optional<HrProofTemplateDTO> updateHrProofTemplate(HrProofTemplateDTO hrProofTemplateDTO) {
        return Optional.ofNullable(this.hrProofTemplateRepository.selectById(hrProofTemplateDTO.getId()))
            .map(roleTemp -> {
                List<HrProofTemplate> hrProofTemplates = hrProofTemplateRepository.selectList(
                    new QueryWrapper<HrProofTemplate>()
                        .eq("title", hrProofTemplateDTO.getTitle())
                        .ne("id",hrProofTemplateDTO.getId())
                        .eq("is_delete", 0));
                if (!hrProofTemplates.isEmpty()){
                    throw new CommonException("该模板标题已存在！");
                }
                Set<String> formFieldList = PdfUtils.getAllFormField(hrProofTemplateDTO.getFileUrl());
                if (formFieldList == null || formFieldList.isEmpty()) {
                    throw new CommonException("请上传有效的PDF模板！");
                }
                hrProofTemplateDTO.setLastModifiedDate(LocalDateTime.now());
                HrProofTemplate hrProofTemplate = this.hrProofTemplateMapper.toEntity(hrProofTemplateDTO);
                this.hrProofTemplateRepository.updateById(hrProofTemplate);
                log.info("Update HrProofTemplate:{}", hrProofTemplateDTO);
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.PROOF_TEMPLATE.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrProofTemplateDTO),
                    HrProofTemplateDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrProofTemplateDTO),
                    null,
                    HrProofTemplateDTO.class
                );
                return hrProofTemplateDTO;
            });
    }

    /**
     * 查询证明模板详情
     *
     * @param id
     * @return
     */
    @Override
    public HrProofTemplateDTO getHrProofTemplate(String id) {
        log.info("Get HrProofTemplate :{}", id);

        HrProofTemplate hrProofTemplate = this.hrProofTemplateRepository.selectById(id);
        return this.hrProofTemplateMapper.toDto(hrProofTemplate);
    }

    /**
     * 删除证明模板
     *
     * @param id
     */
    @Override
    public void deleteHrProofTemplate(String id) {
        Optional.ofNullable(this.hrProofTemplateRepository.selectById(id))
            .ifPresent(hrProofTemplate -> {
                this.hrProofTemplateRepository.deleteById(id);
                log.info("Delete HrProofTemplate:{}", hrProofTemplate);
            });
    }

    /**
     * 批量删除证明模板
     *
     * @param ids
     */
    @Override
    public void deleteHrProofTemplate(List<String> ids) {
        log.info("Delete HrProofTemplates:{}", ids);
        List<HrProofTemplate> hrProofTemplates = hrProofTemplateRepository.selectBatchIds(ids);
        String collect = hrProofTemplates.stream().map(HrProofTemplate::getTitle).collect(Collectors.joining(","));
        this.hrProofTemplateRepository.deleteBatchIds(ids);
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.PROOF_TEMPLATE.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除"+ ModuleTypeEnum.PROOF_TEMPLATE.getValue(),
            null,
            null,
            null,
            JSON.toJSONString(collect),
            null
        );
    }

    /**
     * 分页查询证明模板
     *
     * @param hrProofTemplateDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrProofTemplateDTO hrProofTemplateDTO, Long pageNumber, Long pageSize) {
        Page<HrProofTemplate> page = new Page<>(pageNumber, pageSize);
        IPage<HrProofTemplateDTO> iPage = this.hrProofTemplateRepository.findPage(page, hrProofTemplateDTO);
        Map<Integer, String> certificateTemplateType = codeTableService.findCodeTableByInnerName("certificateTemplateType");
        for (HrProofTemplateDTO record : iPage.getRecords()) {
            record.setTemplateTypeLabel(certificateTemplateType.get(record.getTemplateType()));
        }
        return iPage;
    }

    /**
     * 批量下载
     * @param hrProofTemplateDTO
     * @return
     */
    @Override
    public String batchDownload(HrProofTemplateDTO hrProofTemplateDTO) {
        List<HrProofTemplateDTO> hrProofTemplateDTOS = hrProofTemplateRepository.findList(hrProofTemplateDTO);
        List<File> fileList = new ArrayList<>();
        for (HrProofTemplateDTO proofTemplateDTO : hrProofTemplateDTOS) {
            File file = FileUtil.getFile(proofTemplateDTO.getFileUrl(), proofTemplateDTO.getFileName());
            if (file != null) {
                fileList.add(file);
            }
        }
        List<String> collect = hrProofTemplateDTOS.stream().map(HrProofTemplateDTO::getTitle).collect(Collectors.toList());
        String zipAndUploadFile = hrAppendixService.zipAndUploadFile(fileList, "证明模板");
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.PROOF_TEMPLATE.getValue(), BusinessTypeEnum.DOWNLOAD.getKey(), JSON.toJSONString(collect), hrProofTemplateDTOS.size(), zipAndUploadFile);
        return zipAndUploadFile;
    }

    /**
     * 查询所有证明模板
     * @return
     */
    @Override
    public List<HrProofTemplateDTO> getHrProofTemplateList() {
        List<HrProofTemplateDTO> hrProofTemplateDTOS = hrProofTemplateRepository.findList(new HrProofTemplateDTO());
        return hrProofTemplateDTOS;
    }

    /**
     * 处理证明模板
     * @param hrProofTemplateDTO 证明模板
     * @param staffId 员工信息
     * @return
     */
    @Override
    public Map<String, Object> proofTemplatePretreatment(HrProofTemplateDTO hrProofTemplateDTO, String staffId) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        User user = userRepository.selectById(jwtUserDTO.getId());
        Map<String, Object> result = new LinkedHashMap<>();
        //获取表单域
        Set<String> formFieldList = PdfUtils.getAllFormField(hrProofTemplateDTO.getFileUrl());
        List<InputLabel> labels = new LinkedList<>();
        result.put("templateId", hrProofTemplateDTO.getId());
        result.put("staffId", staffId);
        result.put("templatePath", hrProofTemplateDTO.getFileUrl());

        ContractInfoForPdf contractInfoForPdf = hrTalentStaffRepository.findSignInfoByStaffId(staffId);
        HrContract hrContractStartDate = hrContractRepository.selectOne(new QueryWrapper<HrContract>()
            .eq("client_id", contractInfoForPdf.getClientId())
            .eq("staff_id", contractInfoForPdf.getStaffId())
            .eq("is_delete", 0)
            .orderByAsc("contract_start_date")
            .last("LIMIT 1")
        );
        HrContract hrContractEndDate = hrContractRepository.selectOne(new QueryWrapper<HrContract>()
            .eq("client_id", contractInfoForPdf.getClientId())
            .eq("staff_id", contractInfoForPdf.getStaffId())
            .eq("is_delete", 0)
            .orderByDesc("contract_end_date")
            .last("LIMIT 1")
        );
        // 优先使用上面两个单独查询的 合同开始、结束时间 [原逻辑]
        LocalDate contractStartDate = Optional.ofNullable(hrContractStartDate).map(HrContract::getContractStartDate).orElse(contractInfoForPdf.getContractStartDate());
        LocalDate contractEndDate =  Optional.ofNullable(hrContractEndDate).map(HrContract::getContractEndDate).orElse(contractInfoForPdf.getContractEndDate());
        int deadlineYears = 0;
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = LocalDate.now();
        if (contractStartDate != null){
            startDate = contractStartDate;
            endDate = contractEndDate;
            int deadlineMonth = DateUtils.localDateBetweenMonth(startDate, endDate);
            deadlineYears = BigDecimal.valueOf(deadlineMonth).divide(BigDecimal.valueOf(12),0, BigDecimal.ROUND_UP).intValue();
        }
        //根据模板标题选择一年或者半年
        Map<String,Object> dateMap = StringUtil.intervalDate(hrProofTemplateDTO.getTitle().contains("半年")? false : true);
        String dateMin = String.valueOf(dateMap.get("dateMin"));
        String dateMax = String.valueOf(dateMap.get("dateMax"));
        //查询员工的账单信息
        List<HrBillDetailDTO> hrBillDetailDTOList = hrBillDetailRepository.averageMonthlyIncome(staffId,dateMin,dateMax);
        // 查询员工保障单信息
        List<HrBillDetailDTO> securityList = hrBillDetailRepository.averageMonthlySecurity(staffId, dateMin, dateMax);

        BigDecimal monthlyIncome = BigDecimal.ZERO;
        BigDecimal afterTaxMonthlyIncome = BigDecimal.ZERO; // 税后月工资
        BigDecimal annualIncome = BigDecimal.ZERO;
        BigDecimal monthUnitAccumulationFund = BigDecimal.ZERO; // 平均月单位公积金(单位缴纳+单位补差)
        BigDecimal annualUnitAccumulationFund = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(hrBillDetailDTOList)){
            annualIncome = hrBillDetailDTOList.stream().map(i -> i.getSalary() == null ? BigDecimal.ZERO : i.getSalary()).reduce(BigDecimal.ZERO, BigDecimal::add);
            monthlyIncome = annualIncome.divide(BigDecimal.valueOf(hrBillDetailDTOList.size()),2,BigDecimal.ROUND_HALF_UP);
            BigDecimal afterTaxIncome = hrBillDetailDTOList.stream().map(i -> i.getRealSalary() == null ? BigDecimal.ZERO : i.getRealSalary()).reduce(BigDecimal.ZERO, BigDecimal::add);
            afterTaxMonthlyIncome = afterTaxIncome.divide(BigDecimal.valueOf(hrBillDetailDTOList.size()), 2, BigDecimal.ROUND_HALF_UP);
        }
        if (CollectionUtils.isNotEmpty(securityList)) {
            annualUnitAccumulationFund = securityList.stream().map(i -> {
                BigDecimal found = CalculateUtils.decimalAddition(i.getUnitAccumulationFund(), i.getUnitAccumulationFundMakeUp());
                Integer emolumentMultiple = i.getEmolumentMultiple();
                if (emolumentMultiple != null && emolumentMultiple > 1) {
                    found = found.divide(BigDecimal.valueOf(emolumentMultiple), 2, BigDecimal.ROUND_HALF_UP);
                }
                return found;
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            monthUnitAccumulationFund = annualUnitAccumulationFund.divide(BigDecimal.valueOf(securityList.size()),2,BigDecimal.ROUND_HALF_UP);
        }
        String digitToChineseAnnual = Convert.digitToChinese(annualIncome);
        String digitToChineseMonth = Convert.digitToChinese(monthlyIncome);
        String digitToChineseAfterTaxMonth = Convert.digitToChinese(afterTaxMonthlyIncome);
        String digitToChineseMonthUnitAccumulationFund = Convert.digitToChinese(monthUnitAccumulationFund);
        if (formFieldList != null) {
            if (formFieldList.isEmpty()) {
                throw new CommonException("证明模板表单域获取异常，请检查模板:" + hrProofTemplateDTO.getTitle() + " 是否有效！");
            }
            int finalDeadlineYears = deadlineYears;
            LocalDate finalStartDate = startDate;
            LocalDate finalEndDate = endDate;
            BigDecimal finalAnnualIncome = annualIncome;
            BigDecimal finalMonthlyIncome = monthlyIncome;
            BigDecimal finalAfterTaxMonthlyIncome = afterTaxMonthlyIncome; // 税后月工资
            BigDecimal finalMonthUnitAccumulationFund = monthUnitAccumulationFund; // 单位月公积金
            // 处理工资大写
            Map<String, String> digitToChineseAnnualMap = StringUtil.salaryResolution(digitToChineseAnnual);
            Map<String, String> digitToChineseMonthMap = StringUtil.salaryResolution(digitToChineseMonth);
            for (String field : formFieldList) {
                Object o = null;
                switch (field) {
                    case "employerName":
                        o = contractInfoForPdf.getEmployerName();
                        labels.add(new InputLabel().setLabel("单位全称").setName("employerName").setValue(o));
                        break;
                    case "call":
                        o = contractInfoForPdf.getSecondPartSex();
                        labels.add(new InputLabel().setLabel("称呼").setName("call").setValue(o).setDisabled(true));
                        break;
                    case "deadlineYears":
                        o = finalDeadlineYears;
                        labels.add(new InputLabel().setLabel("工作年数").setName("deadlineYears").setValue(o));
                        break;
                    case "postName":
                        o = contractInfoForPdf.getPostName();
                        labels.add(new InputLabel().setLabel("岗位").setName("postName").setValue(o));
                        break;
                    case "tax":
                        List<SelectionOption> selectionOptions = new ArrayList<>();
                        selectionOptions.add(new SelectionOption().setValue("1").setLabel("税后"));
                        selectionOptions.add(new SelectionOption().setValue("2").setLabel("税前"));
                        labels.add(new InputLabel().setLabel("选项").setName("tax").setValue(selectionOptions.get(0).getValue()).setType("change").setOptions(selectionOptions));
                        break;
                    case "monthlyIncome":
                        o = finalMonthlyIncome;
                        labels.add(new InputLabel().setLabel("月收入").setName("monthlyIncome").setValue(o));
                        break;
                    case "afterTaxMonthlyIncome":
                        o = finalAfterTaxMonthlyIncome;
                        // 税后月收入  展示：月收入，搭配税前税后展示更友好
                        labels.add(new InputLabel().setLabel("月收入").setName("afterTaxMonthlyIncome").setValue(o));
                        break;
                    case "annualIncome":
                        o = finalAnnualIncome;
                        labels.add(new InputLabel().setLabel("年收入").setName("annualIncome").setValue(o));
                        break;
                    case "annualIncomeCapitalize":
                        o = digitToChineseAnnual;
                        labels.add(new InputLabel().setLabel("年收入大写").setName("annualIncomeCapitalize").setValue(o));
                        break;
                    case "monthlyIncomeCapitalize":
                        o = digitToChineseMonth;
                        labels.add(new InputLabel().setLabel("月收入大写").setName("monthlyIncomeCapitalize").setValue(o));
                        break;
                    case "afterTaxMonthlyIncomeCapitalize":
                        o = digitToChineseAfterTaxMonth;
                        labels.add(new InputLabel().setLabel("税后月收入大写").setName("afterTaxMonthlyIncomeCapitalize").setValue(o));
                        break;
                    case "tenThousandYear":
                        labels.add(new InputLabel().setLabel("万").setName("tenThousandYear").setValue(digitToChineseAnnualMap == null ? o : digitToChineseAnnualMap.get("tenThousand")));
                        break;
                    case "thousandYear":
                        labels.add(new InputLabel().setLabel("仟").setName("thousandYear").setValue(digitToChineseAnnualMap == null ? o : digitToChineseAnnualMap.get("thousand")));
                        break;
                    case "hundredYear":
                        labels.add(new InputLabel().setLabel("佰").setName("hundredYear").setValue(digitToChineseAnnualMap == null ? o : digitToChineseAnnualMap.get("hundred")));
                        break;
                    case "tenYear":
                        labels.add(new InputLabel().setLabel("拾").setName("tenYear").setValue(digitToChineseAnnualMap == null ? o : digitToChineseAnnualMap.get("ten")));
                        break;
                    case "elementYear":
                        labels.add(new InputLabel().setLabel("元").setName("elementYear").setValue(digitToChineseMonthMap == null ? o : digitToChineseMonthMap.get("element")));
                        break;
                    case "tenThousandMonth":
                        labels.add(new InputLabel().setLabel("万").setName("tenThousandMonth").setValue(digitToChineseMonthMap == null ? o : digitToChineseMonthMap.get("tenThousand")));
                        break;
                    case "thousandMonth":
                        labels.add(new InputLabel().setLabel("仟").setName("thousandMonth").setValue(digitToChineseMonthMap == null ? o : digitToChineseMonthMap.get("thousand")));
                        break;
                    case "hundredMonth":
                        labels.add(new InputLabel().setLabel("佰").setName("hundredMonth").setValue(digitToChineseMonthMap == null ? o : digitToChineseMonthMap.get("hundred")));
                        break;
                    case "tenMonth":
                        labels.add(new InputLabel().setLabel("拾").setName("tenMonth").setValue(digitToChineseMonthMap == null ? o : digitToChineseMonthMap.get("ten")));
                        break;
                    case "elementMonth":
                        labels.add(new InputLabel().setLabel("元").setName("elementMonth").setValue(digitToChineseMonthMap == null ? o : digitToChineseMonthMap.get("element")));
                        break;
                    case "unitAddress":
                        o = user.getWorkAddress();
                        labels.add(new InputLabel().setLabel("单位地址").setName("unitAddress").setValue(o));
                        break;
                    case "unitTelephone":
                        o = user.getWorkPhone();
                        labels.add(new InputLabel().setLabel("单位电话").setName("unitTelephone").setValue(o));
                        break;
                    case "handler":
                        o = user.getRealName();
                        labels.add(new InputLabel().setLabel("经办人").setName("handler").setValue(o));
                        break;
                    case "stampYears":
                        o = LocalDate.now();
                        labels.add(new InputLabel().setLabel("盖章日期").setName("stampDate").setValue(o).setType("date").setDisabled(true));
                        break;
                    case "secondPartName":
                        o = contractInfoForPdf.getSecondPartName();
                        labels.add(new InputLabel().setLabel("员工姓名").setName("secondPartName").setValue(o));
                        break;
                    case "secondPartIdNo":
                        o = contractInfoForPdf.getSecondPartIdNo();
                        labels.add(new InputLabel().setLabel("身份证号码").setName("secondPartIdNo").setValue(o));
                        break;
                    case "secondPartSex":
                        o = contractInfoForPdf.getSecondPartSex();
                        labels.add(new InputLabel().setLabel("性别").setName("secondPartSex").setValue(o));
                        break;
                    case "secondPartNation":
                        o = contractInfoForPdf.getSecondPartNation();
                        labels.add(new InputLabel().setLabel("民族").setName("secondPartNation").setValue(o));
                        break;
                    case "birthdayYears":
                        o = contractInfoForPdf.getBirthday();
                        labels.add(new InputLabel().setLabel("出生日期").setName("birthdayDate").setValue(o).setType("date"));
                        break;
                    case "contractTypeDuplicate":
                        List<SelectionOption> contractTypeDuplicate = new ArrayList<>();
                        contractTypeDuplicate.add(new SelectionOption().setValue("1").setLabel("正式工"));
                        contractTypeDuplicate.add(new SelectionOption().setValue("2").setLabel("合约工"));
                        contractTypeDuplicate.add(new SelectionOption().setValue("3").setLabel("临时工"));
                        labels.add(new InputLabel().setLabel("选项").setName("contractTypeDuplicate").setValue(contractTypeDuplicate.get(0).getValue()).setType("change").setOptions(contractTypeDuplicate));
                        break;
                    case "personnelType":
                        List<SelectionOption> personnelType = new ArrayList<>();
                        personnelType.add(new SelectionOption().setValue("1").setLabel("正式工"));
                        personnelType.add(new SelectionOption().setValue("2").setLabel("派遣制"));
                        personnelType.add(new SelectionOption().setValue("3").setLabel("临时工"));
                        labels.add(new InputLabel().setLabel("选项").setName("personnelType").setValue(personnelType.get(0).getValue()).setType("change").setOptions(personnelType));
                        break;
                    case "chargePersonName":
                        labels.add(new InputLabel().setLabel("人事（劳资）部门负责人姓名").setName("chargePersonName").setValue(o));
                        break;
                    case "optionInputBox":
                        o = "正式工";
                        labels.add(new InputLabel().setLabel("工作类型").setName("optionInputBox").setValue(o));
                        break;
                    case "remarks":
                        labels.add(new InputLabel().setLabel("备注").setName("remarks").setValue(o));
                        break;
                    case "legalAddress":
                        o = user.getWorkAddress();
                        labels.add(new InputLabel().setLabel("法定地址").setName("legalAddress").setValue(o));
                        break;
                    case "contactNumber":
                        labels.add(new InputLabel().setLabel("联系电话").setName("contactNumber").setValue(o));
                        break;
                    case "postalCode":
                        labels.add(new InputLabel().setLabel("邮政编码").setName("postalCode").setValue(o));
                        break;
                    case "contractStartYears":
                        o = finalStartDate;
                        labels.add(new InputLabel().setLabel("入职日期").setName("contractStartDate").setValue(o).setType("date"));
                        break;
                    case "contractEndYears":
                        o = finalEndDate;
                        labels.add(new InputLabel().setLabel("合同截止日期").setName("contractEndDate").setValue(o).setType("date"));
                        break;
                    case "baseWage":
                        o = contractInfoForPdf.getBasicWage();
                        labels.add(new InputLabel().setLabel("基本工资").setName("baseWage").setValue(o));
                        break;
                    case "baseWageYear":
                        o = contractInfoForPdf.getBasicWage().multiply(BigDecimal.valueOf(12));
                        labels.add(new InputLabel().setLabel("基本工资（年收入）").setName("baseWageYear").setValue(o));
                        break;
                    case "assessmentBonus":
                        labels.add(new InputLabel().setLabel("考核奖金").setName("assessmentBonus").setValue(o));
                        break;
                    case "annualBonus":
                        labels.add(new InputLabel().setLabel("年终奖金").setName("annualBonus").setValue(o));
                        break;
                    case "bankOutlets":
                        labels.add(new InputLabel().setLabel("银行网点").setName("bankOutlets").setValue(o));
                        break;
                    case "wageIncome":
                        o = finalAnnualIncome;
                        labels.add(new InputLabel().setLabel("工资收入").setName("wageIncome").setValue(o));
                        break;
                    case "incomeFromInvestment":
                        labels.add(new InputLabel().setLabel("投资收益").setName("incomeFromInvestment").setValue(0));
                        break;
                    case "otherIncome":
                        labels.add(new InputLabel().setLabel("其他收入").setName("otherIncome").setValue(0));
                        break;
                    case "department":
                        labels.add(new InputLabel().setLabel("部门").setName("department").setValue(o));
                        break;
                    case "contacts":
                        labels.add(new InputLabel().setLabel("联系人").setName("contacts").setValue(o));
                        break;
                    case "education":
                        o = contractInfoForPdf.getHighestEducationLabel();
                        labels.add(new InputLabel().setLabel("学历").setName("education").setValue(o));
                        break;
                    case "tenureYears":
                        labels.add(new InputLabel().setLabel("任职时间").setName("tenureDate").setValue(o).setType("date"));
                        break;
                    case "applicationAmount":
                        labels.add(new InputLabel().setLabel("申请(提供)金额").setName("applicationAmount").setValue(o));
                        break;
                    case "type":
                        labels.add(new InputLabel().setLabel("种类").setName("type").setValue(o));
                        break;
                    case "bonusesAndBenefits":
                        labels.add(new InputLabel().setLabel("奖金及福利").setName("bonusesAndBenefits").setValue(o));
                        break;
                    case "total":
                        labels.add(new InputLabel().setLabel("合计").setName("total").setValue(o));
                        break;
                    case "preparer":
                        labels.add(new InputLabel().setLabel("填表人").setName("preparer").setValue(o));
                        break;
                    case "bonus":
                        labels.add(new InputLabel().setLabel("奖金").setName("bonus").setValue(o));
                        break;
                    case "subsidy":
                        labels.add(new InputLabel().setLabel("补贴").setName("subsidy").setValue(o));
                        break;
                    case "halfYearIncome":
                        if (CollectionUtils.isNotEmpty(hrBillDetailDTOList)) {
                            StringBuilder stringBuilder = new StringBuilder();
                            for (HrBillDetailDTO hrBillDetailDTO : hrBillDetailDTOList) {
                                stringBuilder.append(hrBillDetailDTO.getPayMonthly()).append("月份实发：")
                                    .append(hrBillDetailDTO.getRealSalary() == null ? BigDecimal.ZERO : hrBillDetailDTO.getRealSalary()).append("元\n\n");
                            }
                            o = stringBuilder;
                        }
                        labels.add(new InputLabel().setLabel("半年收入").setName("halfYearIncome").setValue(o).setType("textarea"));
                        break;
                    case "annualInterval":
                        if (CollectionUtils.isNotEmpty(hrBillDetailDTOList)) {
                            List<HrBillDetailDTO> collect = hrBillDetailDTOList.stream().sorted(Comparator.comparingInt(HrBillDetailDTO::getPayMonthly)).collect(Collectors.toList());
                            o = collect.get(0).getPayYear() + "年-" + collect.get(collect.size()-1).getPayYear() + "年";
                        }
                        labels.add(new InputLabel().setLabel("年间隔").setName("annualInterval").setValue(o));
                        break;
                    case "travelYears":
                        labels.add(new InputLabel().setLabel("旅游日期").setName("travelDate").setValue(o).setType("date"));
                        break;
                    case "charge":
                        labels.add(new InputLabel().setLabel("负责人").setName("charge").setValue(o));
                        break;
                    case "marryName":
                        labels.add(new InputLabel().setLabel("结婚对象名称").setName("marryName").setValue(o));
                        break;
                    case "marryIdNo":
                        labels.add(new InputLabel().setLabel("结婚对象身份证号码").setName("marryIdNo").setValue(o));
                        break;
                    case "marrySex":
                        labels.add(new InputLabel().setLabel("结婚对象性别").setName("marrySex").setValue(o));
                        break;
                    case "marryNation":
                        labels.add(new InputLabel().setLabel("结婚对象民族").setName("marryNation").setValue(o));
                        break;
                    case "marryYears":
                        labels.add(new InputLabel().setLabel("结婚日期").setName("marryDate").setValue(o).setType("date"));
                        break;
                    case "civilAffairsBureau":
                        labels.add(new InputLabel().setLabel("民政局").setName("civilAffairsBureau").setValue(o));
                        break;
                    case "resignationReasons":
                        labels.add(new InputLabel().setLabel("离职原因").setName("resignationReasons").setValue(o));
                        break;
                    case "resignationYears":
                        labels.add(new InputLabel().setLabel("离职日期").setName("resignationDate").setValue(o).setType("date"));
                        break;
                    case "applyPeople":
                        o = contractInfoForPdf.getSecondPartName();
                        labels.add(new InputLabel().setLabel("申请人").setName("applyPeople").setValue(o));
                        break;
                    case "secondPartPhone":
                        o = contractInfoForPdf.getSecondPartPhone();
                        labels.add(new InputLabel().setLabel("联系电话").setName("secondPartPhone").setValue(o));
                        break;
                    case "applyYears":
                        o = LocalDate.now();
                        labels.add(new InputLabel().setLabel("申请日期").setName("applyDate").setValue(o).setType("date"));
                        break;
                    case "resignationApplyDate":
                        o = LocalDate.now();
                        labels.add(new InputLabel().setLabel("申请日期").setName("resignationApplyDate").setValue(o).setType("date"));
                        break;
                    case "dispatchYear":
                        o = LocalDate.now().getYear();
                        labels.add(new InputLabel().setLabel("年份").setName("dispatchYear").setValue(o));
                        break;
                    case "employer":
                        o = contractInfoForPdf.getEmployerName();
                        labels.add(new InputLabel().setLabel("用人单位").setName("employer").setValue(o));
                        break;
                    case "dispatchNum":
                        labels.add(new InputLabel().setLabel("派遣人数").setName("dispatchNum").setValue(1));
                        break;
                    case "policeStation":
                        labels.add(new InputLabel().setLabel("派出所").setName("policeStation").setValue(o));
                        break;
                    case "secondPartMaritalStatus":
                        o = contractInfoForPdf.getSecondPartMaritalStatus();
                        labels.add(new InputLabel().setLabel("婚姻").setName("secondPartMaritalStatus").setValue(o));
                        break;
                    case "dispatchToCompany":
                        o = contractInfoForPdf.getEmployerName();
                        labels.add(new InputLabel().setLabel("派遣至单位").setName("dispatchToCompany").setValue(o));
                        break;
                    case "recruitment":
                        o = "山东省公务员";
                        labels.add(new InputLabel().setLabel("招聘公司岗位").setName("recruitment").setValue(o));
                        break;
                    case "certificationCompany":
                        o = "青岛市黄岛区人力资源有限公司";
                        labels.add(new InputLabel().setLabel("公司").setName("certificationCompany").setValue(o));
                        break;
                    case "incomeTenureDate":
                        labels.add(new InputLabel().setLabel("任职时间").setName("incomeTenureDate").setValue(o).setType("date"));
                        break;
                    case "residence":
                        labels.add(new InputLabel().setLabel("公馆").setName("residence").setValue(o));
                        break;
                    case "passportNumber":
                        labels.add(new InputLabel().setLabel("护照号码").setName("passportNumber").setValue(o));
                        break;
                    case "paymentAmount":
                        labels.add(new InputLabel().setLabel("贷款金额").setName("paymentAmount").setValue(o));
                        break;
                    case "paymentType":
                        labels.add(new InputLabel().setLabel("贷款种类").setName("paymentType").setValue(o));
                        break;
                    case "bonusBenefits":
                        labels.add(new InputLabel().setLabel("奖金福利").setName("bonusBenefits").setValue(o));
                        break;
                    case "amountMoney":
                        labels.add(new InputLabel().setLabel("合计金额").setName("amountMoney").setValue(o));
                        break;
                    case "loanReason":
                        labels.add(new InputLabel().setLabel("贷款原因").setName("loanReason").setValue(o));
                        break;
                    case "certificateName":
                        // 证件名称 后台输入，默认身份证
                        o = "身份证";
                        labels.add(new InputLabel().setLabel("证件名称").setName("certificateName").setValue(o));
                        break;
                    case "employeeType":
                        // 手输 合同制/临时/返聘
                        labels.add(new InputLabel().setLabel("职工类型").setName("employeeType").setValue(o));
                        break;
                    case "monthlyUnitAccumulationFundCapitalize":
                        o = digitToChineseMonthUnitAccumulationFund;
                        labels.add(new InputLabel().setLabel("单位月公积金大写").setName("monthlyUnitAccumulationFundCapitalize").setValue(o));
                        break;
                    case "monthlyUnitAccumulationFund":
                        o = finalMonthUnitAccumulationFund;
                        labels.add(new InputLabel().setLabel("单位月公积金").setName("monthlyUnitAccumulationFund").setValue(o));
                        break;
                    case "certificateType":
                        List<SelectionOption> certificateTypeOptions = new ArrayList<>();
                        certificateTypeOptions.add(new SelectionOption().setValue("1").setLabel("身份证"));
                        certificateTypeOptions.add(new SelectionOption().setValue("2").setLabel("军官证"));
                        certificateTypeOptions.add(new SelectionOption().setValue("3").setLabel("护照"));
                        labels.add(new InputLabel().setLabel("选项").setName("certificateType").setValue(certificateTypeOptions.get(0).getValue()).setType("change").setOptions(certificateTypeOptions));
                        break;
                    case "work":
                        labels.add(new InputLabel().setLabel("从事工作").setName("work").setValue(o));
                        break;
                    default:
                        break;
                }
            }
            // 添加印章列表
            List<SelectionOption> officialSealList = new ArrayList<>();
            if (hrProofTemplateDTO.getTemplateType() == 1){
                List<String> stringList = Arrays.asList("企业公章", "人力专用章", "财务专用章");
                officialSealList = hrSealsRepository.getOfficialSealCondition(stringList);
            }else {
                officialSealList = this.hrSealsRepository.getOfficialSeal();
            }
            if (CollectionUtils.isEmpty(officialSealList)) {
                throw new CommonException("未配置印章，请先配置印章后再发起电签合同！");
            }
            labels.add(new InputLabel().setLabel("选择印章").setName("seal").setValue(officialSealList.get(0).getValue()).setType("change").setOptions(officialSealList).setImage("image").setRequired(true));
        }
        result.put("inputLabel", labels);
        return result;
    }
}
