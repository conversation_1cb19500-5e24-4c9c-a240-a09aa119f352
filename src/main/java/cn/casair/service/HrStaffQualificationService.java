package cn.casair.service;

import cn.casair.domain.HrStaffQualification;
import cn.casair.dto.HrStaffQualificationDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 职业(工种)资格服务类
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
public interface HrStaffQualificationService extends IService<HrStaffQualification> {


    /**
     * 创建职业(工种)资格
     * @param hrStaffQualificationDTO
     * @return
     */
    List<HrStaffQualificationDTO> createHrStaffQualification(HrStaffQualificationDTO hrStaffQualificationDTO);

    /**
     * 修改职业(工种)资格
     * @param hrStaffQualificationDTO
     * @return
     */
    Optional<List<HrStaffQualificationDTO>> updateHrStaffQualification(HrStaffQualificationDTO hrStaffQualificationDTO);

    /**
     * 查询职业(工种)资格详情
     * @param id
     * @return
     */
    HrStaffQualificationDTO getHrStaffQualification(String id);

    /**
     * 删除职业(工种)资格
     * @param id
     */
    List<HrStaffQualificationDTO> deleteHrStaffQualification(String id);

    /**
     * 查询职业(工种)资格
     * @param staffId 员工ID
     * @return
     */
    List<HrStaffQualificationDTO> findQualificationList(String staffId);

    void setDict(HrStaffQualificationDTO dto);
}
