<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrContractTemplateRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, template_number, title, type, template_path, update_time, usage_count,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrContractTemplate">
        <id column="id" property="id"/>
        <result column="template_number" property="templateNumber"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="template_path" property="templatePath"/>
        <result column="update_time" property="updateTime"/>
        <result column="usage_count" property="usageCount"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <update id="usageCountPlus">
        UPDATE hr_contract_template
        SET usage_count = usage_count + 1
        WHERE id = #{templateId}
    </update>

    <sql id="Find_SQL">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_contract_template
        WHERE is_delete = 0
        <if test="param.title!=null and param.title!=''">
            AND title LIKE CONCAT('%', #{param.title}, '%')
        </if>
        <if test="param.types!=null and param.types.size()>0">
            AND type IN
            <foreach collection="param.types" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.ids!=null and param.ids.size()>0">
            AND id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <choose>
            <when test="param.field!=null and param.field!=''">
                <choose>
                    <when test="param.field=='type_str'">
                        ORDER BY
                        type ${param.order}
                    </when>
                    <otherwise>
                        ORDER BY
                        ${param.field} ${param.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY
                type,
                created_date
            </otherwise>
        </choose>
    </sql>
    <select id="selectPageByObjet" resultType="cn.casair.dto.HrContractTemplateDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="getContractTemplateList" resultType="cn.casair.dto.HrContractTemplateDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_contract_template
        WHERE is_delete = 0
        ORDER BY
            type,
            created_date
    </select>

    <select id="findList" resultType="cn.casair.dto.HrContractTemplateDTO">
        <include refid="Find_SQL"/>
    </select>


</mapper>
