<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrExamRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, paper_id, exams_passing_rate, exams_number,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>



    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrExam">
                    <id column="id" property="id"/>
                    <result column="paper_id" property="paperId"/>
                    <result column="exams_passing_rate" property="examsPassingRate"/>
                    <result column="exams_number" property="examsNumber"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>
    <select id="pageSelect" resultType="cn.casair.dto.HrExamDTO">
        SELECT
        *
        FROM
        (
        SELECT
        a.paperId,
        a.id,
        a.clientId clientId,
        group_concat( hps.station_id SEPARATOR ',' ) stationId,-- hps.station_id,
        a.examsPassingRate examsPassingRate,
        a.examsNumber examsNumber,
        a.passLine passLine,
        a.paperName paperName
        FROM
        (
        SELECT
        any_value ( hpm.id ) id,
        any_value ( hp.id ) paperId,
        group_concat( hpc.client_id SEPARATOR ',' ) clientId,--         group_concat( hps.station_id SEPARATOR ',' ) stationId,
        any_value ( hpm.exams_passing_rate ) examsPassingRate,
        any_value ( hpm.exams_number ) examsNumber,
        any_value ( hp.pass_line ) passLine,
        any_value ( hp.paper_name ) paperName
        FROM
        hr_exam hpm
        LEFT JOIN hr_paper_management hp ON hp.id = hpm.paper_id
        LEFT JOIN hr_paper_client hpc ON hpc.paper_id = hp.id   AND hpc.is_delete = 0
        WHERE
        hpm.is_delete = 0
        AND hp.is_delete = 0
        GROUP BY paperId
        ) a
        LEFT JOIN hr_paper_station hps ON hps.paper_id = a.paperId  and hps.is_delete = 0
        GROUP BY
        paperId
        ) b
        <where>
            <if test="param1.stationIdList!=null and param1.stationIdList.size() > 0">
                and
                <foreach collection="param1.stationIdList" open="(" separator="or" close=")" item="val">
                    stationId like concat('%',#{val},'%')
                </foreach>
            </if>

            <if test="param1.paperName!=null and param1.paperName!=''">
                and paperName like concat('%',#{param1.paperName},'%')
            </if>
            <if test="param1.clientIdList!=null and param1.clientIdList.size() > 0">
                and
                <foreach collection="param1.clientIdList" open="(" separator="or" close=")" item="val">
                    clientId like concat('%',#{val},'%')
                </foreach>
            </if>
        </where>

        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>

    </select>
    <select id="HrExams" resultType="cn.casair.dto.HrExamDTO">
        SELECT
            *
        FROM
            (
                SELECT
                    a.paperId,
                    a.id,
                    a.clientId clientId,
                    group_concat( hps.station_id SEPARATOR ',' ) stationId,-- hps.station_id,
                    a.examsPassingRate examsPassingRate,
                    a.examsNumber examsNumber,
                    a.passLine passLine,
                    a.paperName paperName
                FROM
                    (
                        SELECT
                            any_value ( hpm.id ) id,
                            any_value ( hp.id ) paperId,
                            group_concat( hpc.client_id SEPARATOR ',' ) clientId,--         group_concat( hps.station_id SEPARATOR ',' ) stationId,
                            any_value ( hpm.exams_passing_rate ) examsPassingRate,
                            any_value ( hpm.exams_number ) examsNumber,
                            any_value ( hp.pass_line ) passLine,
                            any_value ( hp.paper_name ) paperName
                        FROM
                            hr_exam hpm
                                LEFT JOIN hr_paper_management hp ON hp.id = hpm.paper_id
                                LEFT JOIN hr_paper_client hpc ON hpc.paper_id = hp.id   AND hpc.is_delete = 0
                        WHERE
                            hpm.is_delete = 0
                          AND hp.is_delete = 0
                        GROUP BY paperId
                        ) a
                        LEFT JOIN hr_paper_station hps ON hps.paper_id = a.paperId  and hps.is_delete = 0
                GROUP BY
                    paperId
                ) b
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>
</mapper>
