package cn.casair.service.util;

import cn.afterturn.easypoi.handler.inter.IExcelDictHandler;
import cn.casair.common.utils.SpringContextUtil;
import cn.casair.dto.CodeTableDTO;
import cn.casair.repository.CodeTableRepository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 下拉列表
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/10/13 10:15
 */
public class ExcelDiceAddressList implements IExcelDictHandler {

    @Override
    public List<Map> getList(String dict) {
        List<Map> list = new ArrayList<>();
        CodeTableRepository codeTableRepository = SpringContextUtil.getBean("codeTableRepository");
        List<CodeTableDTO> codeTableList = codeTableRepository.getCodeTableListByInnerName(dict);
        codeTableList.forEach(ls -> {
            Map<String, String> dictMap = new HashMap<>();
            dictMap.put("dictKey", String.valueOf(ls.getItemValue()));
            dictMap.put("dictValue", ls.getItemName());
            list.add(dictMap);
        });
        return list;
    }

    @Override
    public String toName(String dict, Object obj, String name, Object value) {
        CodeTableRepository codeTableRepository = SpringContextUtil.getBean("codeTableRepository");
        List<CodeTableDTO> codeTableList = codeTableRepository.getCodeTableListByInnerName(dict);
        List<CodeTableDTO> collect = codeTableList.stream().filter(ls -> ls.getItemValue().equals(value)).collect(Collectors.toList());
        if (collect.isEmpty()) {
            return null;
        }
        return collect.get(0).getItemName();
    }

    @Override
    public String toValue(String dict, Object obj, String name, Object value) {
        CodeTableRepository codeTableRepository = SpringContextUtil.getBean("codeTableRepository");
        List<CodeTableDTO> codeTableList = codeTableRepository.getCodeTableListByInnerName(dict);
        List<CodeTableDTO> collect = codeTableList.stream().filter(ls -> ls.getItemName().equals(value)).collect(Collectors.toList());
        if (collect.isEmpty()) {
            return null;
        }
        return String.valueOf(collect.get(0).getItemValue());
    }
}
