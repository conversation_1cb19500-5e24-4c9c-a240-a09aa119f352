<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrMaternityAllowanceRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, staff_id,client_id, disease_type, operation_date, parity, fetus_num, manual_num, prove_num, gestational_weeks, phone, state, birth_appendixes, leave_hospital_appendixes, fertility_appendixes, fertility_amount, grant_start_date, grant_end_date, appendix_id, remark,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrMaternityAllowance">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="disease_type" property="diseaseType"/>
        <result column="operation_date" property="operationDate"/>
        <result column="parity" property="parity"/>
        <result column="fetus_num" property="fetusNum"/>
        <result column="manual_num" property="manualNum"/>
        <result column="prove_num" property="proveNum"/>
        <result column="gestational_weeks" property="gestationalWeeks"/>
        <result column="phone" property="phone"/>
        <result column="state" property="state"/>
        <result column="birth_appendixes" property="birthAppendixes"/>
        <result column="leave_hospital_appendixes" property="leaveHospitalAppendixes"/>
        <result column="fertility_appendixes" property="fertilityAppendixes"/>
        <result column="fertility_amount" property="fertilityAmount"/>
        <result column="grant_start_date" property="grantStartDate"/>
        <result column="grant_end_date" property="grantEndDate"/>
        <result column="appendix_id" property="appendixId"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Select_SQL">
        SELECT
        hc.client_name,
        hts.`name`,
        hts.certificate_num,
        hts.sex,
        IF ( hts.sex = 1, '男', '女' ) sexLabel,
        hts.phone AS staffPhone,
        hts.personnel_type,
        hts.staff_status,
        hs.profession_name,
        hma.*
        FROM
        hr_maternity_allowance hma
        LEFT JOIN hr_talent_staff hts ON hma.staff_id = hts.id
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        LEFT JOIN v_staff_work_experience hswe ON hswe.staff_id = hts.id AND ( CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END ) AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hswe.station_id = hs.id AND hs.is_delete = 0
        WHERE hma.is_delete = 0
        <if test="permissionClient!=null and permissionClient.size() > 0">
            AND hts.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hts.client_id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.id != null">
            AND hma.id = #{param.id}
        </if>
        <if test="param.staffId != null">
            AND hma.staff_id = #{param.staffId}
        </if>
        <if test="param.name != null and param.name != ''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND hts.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.personnelType != null">
            AND hts.personnel_type = #{param.personnelType}
        </if>
        <if test="param.professionName != null and param.professionName != ''">
            AND hs.profession_name LIKE concat('%', #{param.professionName}, '%')
        </if>
        <if test="param.sex!=null">
            AND hts.sex = #{param.sex}
        </if>
        <if test="param.createdStartDate != null">
            AND hma.created_date &gt;= #{param.createdStartDate}
        </if>
        <if test="param.createdEndDate!=null">
            AND hma.created_date &lt; date_add(#{param.createdEndDate}, interval 1 day)
        </if>
        <if test="param.operationStartDate != null">
            AND hma.operation_date &gt;= #{param.operationStartDate}
        </if>
        <if test="param.operationEndDate!=null">
            AND hma.operation_date &lt;= #{param.operationEndDate}
        </if>
        <if test="param.state!=null">
            AND hma.state = #{param.state}
        </if>
        <if test="param.stateList!=null and param.stateList.size() > 0">
            AND hma.state IN
            <foreach collection="param.stateList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.staffStatusList!=null and param.staffStatusList.size() > 0">
            AND hts.staff_status IN
            <foreach collection="param.staffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.ids!=null and param.ids.size() > 0">
            AND hma.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <choose>
            <when test="param.field!=null and param.field!=''">
                ORDER BY ${param.field} ${param.order}
            </when>
            <otherwise>
                ORDER BY hma.created_date DESC
            </otherwise>
        </choose>
    </sql>

    <select id="findPage" resultType="cn.casair.dto.HrMaternityAllowanceDTO">
        <include refid="Select_SQL"/>
    </select>

    <select id="findById" resultType="cn.casair.dto.HrMaternityAllowanceDTO">
         SELECT
            hc.client_name,
            hts.`name`,
            hts.staff_status ,
            hts.certificate_num,
            hts.sex,
            IF ( hts.sex = 1, '男', '女' ) sexLabel,
            hts.phone AS staffPhone,
            hts.personnel_type,
            hs.profession_name,
            hma.*
        FROM
            hr_maternity_allowance hma
        LEFT JOIN hr_talent_staff hts ON hma.staff_id = hts.id
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        LEFT JOIN v_staff_work_experience hswe ON hswe.staff_id = hts.id AND ( CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END ) AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hswe.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN
        (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
        FROM (SELECT id,client_id,staff_id,state,contract_start_date,contract_end_date,is_delete FROM hr_contract WHERE is_delete = 0 AND state in (1,2,3) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) hct
        ON hct.staff_id = hts.id AND hct.is_delete = 0
        WHERE hma.is_delete = 0 AND hma.id = #{id}
    </select>

    <select id="findList" resultType="cn.casair.dto.HrMaternityAllowanceDTO">
        <include refid="Select_SQL"/>
    </select>
</mapper>
