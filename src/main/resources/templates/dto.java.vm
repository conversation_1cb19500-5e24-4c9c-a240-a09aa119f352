package ${cfg.extPackage.Dto};
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

#foreach($pkg in ${table.importPackages})
    #if($pkg.indexOf("mybatisplus")< 0)
    import ${pkg} ;
    #end
#end
#if(!${entityLombokModel}&&${cfg.enableGuava})
#end
#if(${swagger2})
#end
#if(${entityLombokModel})
#end

/**
 * $!{table.comment}DTO
 *
 * <AUTHOR>
 * @since ${date}
 */
    #if(${entityLombokModel})
    @Data
        #if(${superEntityClass})
        @EqualsAndHashCode(callSuper = false)
        #else
        @EqualsAndHashCode(callSuper = false)
        #end
    @Accessors(chain = true)
    #end
    #if(${swagger2})
    @ApiModel(value = "${entity}DTO对象", description = "$!{table.comment}")
    #end
public class ${entity}DTO implements Serializable {
    private static final long serialVersionUID = 1L;
## ----------  BEGIN 字段循环遍历  ----------
    #foreach($field in ${table.fields})

        #if(${field.keyFlag})
            #set($keyPropertyName=${field.propertyName})
        #end
        #if("$field.comment" != "" && "$cfg.showFiledCommon" == true)
            #if(${swagger2})
            @ApiModelProperty(value = "${field.comment}")
            #else
            /**
             * ${field.comment}
             */
            #end
        #end
    private ${field.propertyType} ${field.propertyName};
    #end
    #foreach($field in ${table.commonFields})

        #if(${field.keyFlag})
            #set($keyPropertyName=${field.propertyName})
        #end
        #if("$field.comment" != "" && "$cfg.showFiledCommon" == true)
            #if(${swagger2})
            @ApiModelProperty(value = "${field.comment}")
            #else
            /**
             * ${field.comment}
             */
            #end
        #end
    private ${field.propertyType} ${field.propertyName};
    #end
## ----------  END 字段循环遍历  ----------

    #if(!${entityLombokModel})
        #foreach($field in ${table.fields})
            #if(${field.propertyType.equals("boolean")})
                #set($getprefix="is")
                #set($setprefix="set")
            #elseif(${field.propertyType.equals("Boolean")})
                #set($getprefix="getIs")
                #set($setprefix="setIs")
            #else
                #set($getprefix="get")
                #set($setprefix="set")
            #end

            public ${field.propertyType} ${getprefix}${field.capitalName}()

            {
                return ${field.propertyName};
            }

            #if(${entityBuilderModel})
                public ${entity} ${setprefix}${field.capitalName}(${field.propertyType} ${field.propertyName}) {
            #else
                public void ${setprefix}${field.capitalName}(${field.propertyType} ${field.propertyName}) {
            #end
            this.${field.propertyName} = ${field.propertyName};
            #if(${entityBuilderModel})
                return this;
            #end
        }
        #end
    #end

    #if(${entityColumnConstant})
        #foreach($field in ${table.fields})
            public static final String ${field.name.toUpperCase()} = "${field.name}";

        #end
    #end
    #if(${activeRecord})
        @Override
        protected Serializable pkVal() {
        #if(${keyPropertyName})
            return this.${keyPropertyName};
        #else
            return null;
        #end
    }

    #end
    #if(${cfg.equalsAndHashCodeFields.size()} > 0)
        #set($equalsAndHashCodeFieldsSize = ${cfg.equalsAndHashCodeFields.size()})
        @Override
        public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
            ${entity}DTO target = (${entity}DTO) o;
        #foreach($field in ${cfg.equalsAndHashCodeFields})
            #if($foreach.count == 1)        return Objects.equal(${field}, target.${field})#if($foreach.count < ${equalsAndHashCodeFieldsSize}) &&#end
            #else

                Objects.equal(${field}, target.${field})#if($foreach.count < ${equalsAndHashCodeFieldsSize}) &&#end
            #end
        #end;
    }

        @Override
        public int hashCode() {
        return Objects.hashCode(super.hashCode(), #foreach($field in ${cfg.equalsAndHashCodeFields})${field}#if($foreach.count < ${equalsAndHashCodeFieldsSize}), #end#end);
    }

    #end
    #if(!${entityLombokModel} && !${cfg.enableGuava})
        @Override
        public String toString() {
        return "${entity}{" +
            #foreach($field in ${table.fields})
                #if($!{foreach.index}==0)
                    "${field.propertyName}=" + ${field.propertyName} +
                #else
                    ", ${field.propertyName}=" + ${field.propertyName} +
                #end
            #end
            "}";
    }
    #end
    #if(!${entityLombokModel} && ${cfg.enableGuava})
        @Override
        public String toString() {
        return MoreObjects.toStringHelper(this)
            #foreach($field in ${table.fields})
            .add("${field.propertyName}", ${field.propertyName})
            #end
            #foreach($field in ${table.commonFields})
                .add("${field.propertyName}", ${field.propertyName})
                #end
            .toString();
    }
    #end
}



