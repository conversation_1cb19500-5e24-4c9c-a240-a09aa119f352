package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.LaborAppraisalServiceEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.enums.WorkInjuryServiceEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.StringUtils;
import cn.casair.domain.HrLaborAppraisal;
import cn.casair.domain.HrTalentStaff;
import cn.casair.domain.HrWorkInjury;
import cn.casair.dto.HrApplyOpLogsDTO;
import cn.casair.dto.HrLaborAppraisalDTO;
import cn.casair.dto.JWTMiniDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrLaborAppraisalExport;
import cn.casair.mapper.HrLaborAppraisalMapper;
import cn.casair.repository.HrLaborAppraisalRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrAppletMessageService;
import cn.casair.service.HrApplyOpLogsService;
import cn.casair.service.HrClientService;
import cn.casair.service.HrLaborAppraisalService;
import cn.casair.service.HrNotificationUserService;
import cn.casair.service.HrTalentStaffService;
import cn.casair.service.HrUpcomingService;
import cn.casair.service.HrWorkInjuryService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 劳动能力鉴定表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrLaborAppraisalServiceImpl extends ServiceImpl<HrLaborAppraisalRepository, HrLaborAppraisal> implements HrLaborAppraisalService {

    private final HrAppendixService hrAppendixService;
    private final HrLaborAppraisalRepository hrLaborAppraisalRepository;
    private final HrLaborAppraisalMapper hrLaborAppraisalMapper;
    private final HrClientService hrClientService;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrTalentStaffService hrTalentStaffService;
    private final CodeTableService codeTableService;
    private final HrNotificationUserService hrNotificationUserService;
    private final HrAppletMessageService hrAppletMessageService;
    private final SysOperLogService sysOperLogService;
    private final HrUpcomingService hrUpcomingService;
    @Autowired
    private HrWorkInjuryService hrWorkInjuryService;

    /**
     * 创建劳动能力鉴定表
     *
     * @param hrLaborAppraisalDTO
     * @return
     */
    @Override
    public HrLaborAppraisalDTO createHrLaborAppraisal(HrLaborAppraisalDTO hrLaborAppraisalDTO) {
        log.info("Create new HrLaborAppraisal:{}", hrLaborAppraisalDTO);
        HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(hrLaborAppraisalDTO.getStaffId());
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrLaborAppraisal hrLaborAppraisal = this.hrLaborAppraisalMapper.toEntity(hrLaborAppraisalDTO);
        //添加状态
        hrLaborAppraisal.setStatus(LaborAppraisalServiceEnum.NOTIFIED_STAFF.getKey());
        hrLaborAppraisal.setApplicationDate(LocalDate.now());
        hrLaborAppraisal.setApplication("用工单位");
        hrLaborAppraisal.setClientId(hrTalentStaff.getClientId());
        hrLaborAppraisal.setLastModifiedDate(LocalDateTime.now());

        this.hrLaborAppraisalRepository.insert(hrLaborAppraisal);
        //日志
        String enterpriseMessage = jwtUserDTO.getRealName() + "发起了" + hrTalentStaff.getName() + "的劳动能力鉴定 ";
        if (StringUtils.isNotBlank(hrLaborAppraisalDTO.getJuryMessage())) {
            enterpriseMessage = enterpriseMessage + " 备注：" + hrLaborAppraisalDTO.getJuryMessage();
        }
        //String appMessage = getMessage(LaborAppraisalServiceEnum.NOTIFIED_STAFF.getKey().toString(), hrTalentStaff.getName() + "","");
        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrLaborAppraisal.getId(), null, jwtUserDTO.getId(), enterpriseMessage, null, ServiceCenterEnum.LABOR_APPRAISAL.getKey());
        //创建通知配置
        this.hrNotificationUserService.saveRemindContent(hrTalentStaff.getClientId(), ServiceCenterEnum.LABOR_APPRAISAL.getKey(), LaborAppraisalServiceEnum.NOTIFIED_STAFF.getKey(), enterpriseMessage + "。待通知员工进行劳动能力鉴定", jwtUserDTO.getId());
        //添加待办
        hrUpcomingService.createServiceUpcoming(hrLaborAppraisal.getId(), hrLaborAppraisalDTO.getStaffId(), "劳动能力鉴定-通知" + hrTalentStaff.getName() + "进行劳动能力鉴定", LocalDate.now(), 0);
        // 操作日志
        hrLaborAppraisalDTO.setName(hrTalentStaff.getName());
        hrLaborAppraisalDTO.setCertificateNum(hrTalentStaff.getCertificateNum());
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.LABOR_APPRAISAL.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrLaborAppraisalDTO),
            HrLaborAppraisalDTO.class,
            null,
            JSON.toJSONString(hrLaborAppraisalDTO)
        );
        return this.hrLaborAppraisalMapper.toDto(hrLaborAppraisal);
    }

    /**
     * 修改劳动能力鉴定表
     *
     * @param hrLaborAppraisalDTO
     * @return
     */
    @Override
    public Optional<HrLaborAppraisalDTO> updateHrLaborAppraisal(HrLaborAppraisalDTO hrLaborAppraisalDTO) {
        return Optional.ofNullable(this.hrLaborAppraisalRepository.selectById(hrLaborAppraisalDTO.getId()))
            .map(roleTemp -> {
                JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
                HrLaborAppraisal hrLaborAppraisal = this.hrLaborAppraisalMapper.toEntity(hrLaborAppraisalDTO);
                //更改已发送处理通知
                if (hrLaborAppraisalDTO.getStatus() == LaborAppraisalServiceEnum.SEND_NOTIFY.getKey()) {
                    //添加日志 附件
                    String appendixId = "";
                    if (CollectionUtils.isNotEmpty(hrLaborAppraisalDTO.getAppendixIds())) {
                        appendixId = String.join(",", hrLaborAppraisalDTO.getAppendixIds());
                    }
                    //添加日志
                    HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(hrLaborAppraisalDTO.getStaffId());
                    String enterpriseMessage = jwtUserDTO.getRealName() + "上传了函件 ";
                    //创建通知配置
                    this.hrNotificationUserService.saveRemindContent(hrTalentStaff.getClientId(), ServiceCenterEnum.LABOR_APPRAISAL.getKey(), LaborAppraisalServiceEnum.REFUSE_STAFF.getKey(), enterpriseMessage + "。劳动能力鉴定结束", jwtUserDTO.getId());
                    //关闭待办
                    hrUpcomingService.updateUpcoming(hrLaborAppraisal.getId());
                    if (StringUtils.isNotBlank(hrLaborAppraisalDTO.getJuryMessage())) {
                        enterpriseMessage = enterpriseMessage + "说明：" + hrLaborAppraisalDTO.getJuryMessage();
                    }
                    String appMessage = getMessage(LaborAppraisalServiceEnum.REFUSE_STAFF.getKey().toString(), "公司给出了对您拒绝进行劳动能力鉴定的处理结果，请点击附件查看 ", null);
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrLaborAppraisalDTO.getId(), null, jwtUserDTO.getId(), enterpriseMessage + "####" + appMessage, appendixId, ServiceCenterEnum.LABOR_APPRAISAL.getKey());
                    //小程序消息中心
                    hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.LABOR_APPRAISAL.getKey(), hrLaborAppraisal.getId(), hrLaborAppraisal.getStaffId(), "公司给出了对您拒绝进行劳动能力鉴定的处理结果，请点击附件查看 ", true, ServiceCenterEnum.LABOR_APPRAISAL.getValue());
                }
                //非工伤进行鉴定结果
                if (!hrLaborAppraisalDTO.getAppraisalStatus() && hrLaborAppraisalDTO.getStatus() == LaborAppraisalServiceEnum.FINISH_APPRAISAL.getKey()) {
                    //添加日志 附件
                    String appendixId = "";
                    if (CollectionUtils.isNotEmpty(hrLaborAppraisalDTO.getAppendixIds())) {
                        appendixId = String.join(",", hrLaborAppraisalDTO.getAppendixIds());
                    }
                    //日志
                    HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(hrLaborAppraisalDTO.getStaffId());
                    String enterpriseMessage = jwtUserDTO.getRealName() + "更新了" + hrTalentStaff.getName() + "劳动能力鉴定结果 ";
                    //创建通知配置
                    this.hrNotificationUserService.saveRemindContent(hrTalentStaff.getClientId(), ServiceCenterEnum.LABOR_APPRAISAL.getKey(), LaborAppraisalServiceEnum.FINISH_APPRAISAL.getKey(), enterpriseMessage, jwtUserDTO.getId());
                    //关闭待办
                    hrUpcomingService.updateUpcoming(hrLaborAppraisal.getId());
                    if (StringUtils.isNotBlank(hrLaborAppraisalDTO.getJuryMessage())) {
                        enterpriseMessage = enterpriseMessage + "说明：" + hrLaborAppraisalDTO.getJuryMessage();
                    }
                    String appMessage = getMessage(LaborAppraisalServiceEnum.FEEDBACK_STAFF.getKey().toString(), "您的劳动能力鉴定结果已出，可点击附件查看或下载 ", null);
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrLaborAppraisal.getId(), null, hrTalentStaff.getId(), enterpriseMessage + "####" + appMessage, appendixId, ServiceCenterEnum.LABOR_APPRAISAL.getKey());
                    //小程序消息中心
                    hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.LABOR_APPRAISAL.getKey(), hrLaborAppraisal.getId(), hrLaborAppraisal.getStaffId(), "您的劳动能力鉴定结果已出，可点击附件查看或下载 ", true, ServiceCenterEnum.LABOR_APPRAISAL.getValue());

                }
                this.hrLaborAppraisalRepository.updateById(hrLaborAppraisal);
                log.info("Update HrLaborAppraisal:{}", hrLaborAppraisalDTO);
//                // 操作日志
//                this.sysOperLogService.insertSysOperLog(
//                    ModuleTypeEnum.LABOR_APPRAISAL.getValue(),
//                    BusinessTypeEnum.UPDATE.getKey(),
//                    JSON.toJSONString(hrLaborAppraisalDTO),
//                    HrLaborAppraisalDTO.class,
//                    null,
//                    JSON.toJSONString(roleTemp),
//                    JSON.toJSONString(hrLaborAppraisalDTO),
//                    null,
//                    HrLaborAppraisalDTO.class
//                );
                return hrLaborAppraisalDTO;
            });
    }

    /**
     * 查询劳动能力鉴定表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrLaborAppraisalDTO getHrLaborAppraisal(String id) {
        log.info("Get HrLaborAppraisal :{}", id);

        HrLaborAppraisal hrLaborAppraisal = this.hrLaborAppraisalRepository.selectById(id);
        return this.hrLaborAppraisalMapper.toDto(hrLaborAppraisal);
    }

    /**
     * 删除劳动能力鉴定表
     *
     * @param id
     */
    @Override
    public void deleteHrLaborAppraisal(String id) {
        Optional.ofNullable(this.hrLaborAppraisalRepository.selectById(id))
            .ifPresent(hrLaborAppraisal -> {
                this.hrLaborAppraisalRepository.deleteById(id);
                log.info("Delete HrLaborAppraisal:{}", hrLaborAppraisal);
            });
    }

    /**
     * 批量删除劳动能力鉴定表
     *
     * @param ids
     */
    @Override
    public void deleteHrLaborAppraisal(List<String> ids) {
        log.info("Delete HrLaborAppraisals:{}", ids);
        this.hrLaborAppraisalRepository.deleteBatchIds(ids);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.LABOR_APPRAISAL.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
    }

    /**
     * 分页查询劳动能力鉴定表
     *
     * @param hrLaborAppraisalDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrLaborAppraisalDTO hrLaborAppraisalDTO, Long pageNumber, Long pageSize) {
        //查询数据权限
        List<String> clientId = hrClientService.selectClientIdByUserId();
        if (CollectionUtils.isEmpty(clientId)) {
            return new Page<>();
        }
        Page<HrLaborAppraisal> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrLaborAppraisal> qw = this.queryWrapper(hrLaborAppraisalDTO, clientId);
        IPage iPage = this.hrLaborAppraisalRepository.page(page, qw);
        return iPage;
    }

    private QueryWrapper<HrLaborAppraisal> queryWrapper(HrLaborAppraisalDTO hrLaborAppraisalDTO, List<String> clientId) {
        QueryWrapper<HrLaborAppraisal> qw = new QueryWrapper<>();
        qw.in(CollectionUtils.isNotEmpty(hrLaborAppraisalDTO.getClientIdList()), "hla.client_id", hrLaborAppraisalDTO.getClientIdList());
        qw.like(StringUtils.isNotBlank(hrLaborAppraisalDTO.getName()), "ht.`name`", hrLaborAppraisalDTO.getName());
        qw.like(StringUtils.isNotBlank(hrLaborAppraisalDTO.getCertificateNum()), "ht.certificate_num", hrLaborAppraisalDTO.getCertificateNum());
        qw.like(StringUtils.isNotBlank(hrLaborAppraisalDTO.getPhone()), "ht.phone", hrLaborAppraisalDTO.getPhone());
        qw.in(CollectionUtils.isNotEmpty(hrLaborAppraisalDTO.getProfessionIdList()), "hs.id", hrLaborAppraisalDTO.getProfessionIdList());
        qw.in(CollectionUtils.isNotEmpty(hrLaborAppraisalDTO.getPersonnelTypeList()), "ht.personnel_type", hrLaborAppraisalDTO.getPersonnelTypeList());
        qw.ge(StringUtils.isNotBlank(hrLaborAppraisalDTO.getSelectStartBeginDate()), "hla.work_stoppage_start_date", hrLaborAppraisalDTO.getSelectStartBeginDate());
        qw.le(StringUtils.isNotBlank(hrLaborAppraisalDTO.getSelectStartFinishDate()), "hla.work_stoppage_start_date", hrLaborAppraisalDTO.getSelectStartFinishDate());
        qw.ge(StringUtils.isNotBlank(hrLaborAppraisalDTO.getSelectEndBeginDate()), "hla.work_stoppage_end_date", hrLaborAppraisalDTO.getSelectEndBeginDate());
        qw.le(StringUtils.isNotBlank(hrLaborAppraisalDTO.getSelectEndFinishDate()), "hla.work_stoppage_end_date", hrLaborAppraisalDTO.getSelectEndFinishDate());
        qw.ge(StringUtils.isNotBlank(hrLaborAppraisalDTO.getInjuryStartDate()), "hla.injury_date", hrLaborAppraisalDTO.getInjuryStartDate());
        qw.le(StringUtils.isNotBlank(hrLaborAppraisalDTO.getInjuryEndDate()), "hla.injury_date", hrLaborAppraisalDTO.getInjuryEndDate());
        qw.in(CollectionUtils.isNotEmpty(hrLaborAppraisalDTO.getStatusList()), "hla.status", hrLaborAppraisalDTO.getStatusList());
        //申请方
        qw.like(StringUtils.isNotBlank(hrLaborAppraisalDTO.getApplication()), "hla.application", hrLaborAppraisalDTO.getApplication());
        //申请日期
        qw.ge(StringUtils.isNotBlank(hrLaborAppraisalDTO.getSelectApplicationStartDate()), "hla.application_date", hrLaborAppraisalDTO.getSelectApplicationStartDate());
        qw.le(StringUtils.isNotBlank(hrLaborAppraisalDTO.getSelectApplicationEndDate()), "hla.application_date", hrLaborAppraisalDTO.getSelectApplicationEndDate());
        //专管员
        qw.in(CollectionUtils.isNotEmpty(hrLaborAppraisalDTO.getSpecializedList()), "su.id", hrLaborAppraisalDTO.getSpecializedList());
        qw.in(CollectionUtils.isNotEmpty(hrLaborAppraisalDTO.getIds()), "hla.id", hrLaborAppraisalDTO.getIds());
        qw.in(CollectionUtils.isNotEmpty(hrLaborAppraisalDTO.getStaffStatusList()),"ht.staff_status", hrLaborAppraisalDTO.getStaffStatusList());
        qw.eq("hla.is_delete ", 0);
        qw.eq("ht.is_delete", 0);
        if (CollectionUtils.isEmpty(hrLaborAppraisalDTO.getClientIdList())) {
            qw.in("ht.client_id ", clientId);
        }
        //排序
        if (StringUtils.isNotBlank(hrLaborAppraisalDTO.getOrder())) {
            if (hrLaborAppraisalDTO.getOrder().equals("DESC")) {
                qw.orderBy(StringUtils.isNotBlank(hrLaborAppraisalDTO.getField()), false, hrLaborAppraisalDTO.getField());
            } else {
                qw.orderBy(StringUtils.isNotBlank(hrLaborAppraisalDTO.getField()), true, hrLaborAppraisalDTO.getField());
            }

        }
        return qw;
    }

    /**
     * 查看劳动能力鉴定详情
     *
     * @param hrLaborAppraisalDTO
     * @return
     */
    @Override
    public HrLaborAppraisalDTO getDetail(HrLaborAppraisalDTO hrLaborAppraisalDTO) {
        HrLaborAppraisalDTO hrLaborAppraisalDTO1 = hrLaborAppraisalRepository.selectDetatil(hrLaborAppraisalDTO.getId());
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(hrLaborAppraisalDTO.getId(), null);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)) {
            hrLaborAppraisalDTO1.setApplyOpLogsList(applyOpLogsList);
        }
        return hrLaborAppraisalDTO1;
    }

    /**
     * 微信 添加劳动能力鉴定
     *
     * @param hrLaborAppraisalDTO
     * @return
     */
    @Override
    public HrLaborAppraisalDTO addLaborAppraisal(HrLaborAppraisalDTO hrLaborAppraisalDTO) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrLaborAppraisal hrLaborAppraisal = this.hrLaborAppraisalMapper.toEntity(hrLaborAppraisalDTO);
        String workInjuryId = hrLaborAppraisalDTO.getWorkInjuryId();
        if (StringUtils.isNotBlank(workInjuryId)) {
            QueryWrapper<HrLaborAppraisal> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("work_injury_id", workInjuryId);
            List<HrLaborAppraisal> list = list(queryWrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new CommonException("该工伤以进行了劳动能力鉴定");
            }
        }
        //添加状态
        hrLaborAppraisal.setStatus(LaborAppraisalServiceEnum.INJURY_RESULT.getKey());
        hrLaborAppraisal.setApplicationDate(LocalDate.now());
        hrLaborAppraisal.setApplication("员工");
        hrLaborAppraisal.setStaffId(jwtMiniDTO.getId());
        hrLaborAppraisal.setWorkInjuryId(workInjuryId);
        hrLaborAppraisal.setLastModifiedDate(LocalDateTime.now());
        this.hrLaborAppraisalRepository.insert(hrLaborAppraisal);
        //日志
        HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(jwtMiniDTO.getId());
        String enterpriseMessage = hrTalentStaff.getName() + "发起了劳动能力鉴定申请";
        String appMessage = getMessage(LaborAppraisalServiceEnum.NOTIFIED_STAFF.getKey().toString(), "您的劳动能力鉴定申请已提交", null);
        this.hrApplyOpLogsService.saveHrApplyOpLogsMiniPhaseTWO(hrLaborAppraisal.getId(), null, hrTalentStaff.getId(), enterpriseMessage + "####" + appMessage, true, ServiceCenterEnum.LABOR_APPRAISAL.getKey());
        //如果是工伤鉴定 修改该工伤状态
        if (hrLaborAppraisalDTO.getAppraisalStatus()) {
            HrWorkInjury hrWorkInjury = hrWorkInjuryService.getById(hrLaborAppraisalDTO.getWorkInjuryId());
            if (hrWorkInjury != null) {
                hrWorkInjury.setWorkStoppageEndDate(null);
                hrWorkInjury.setStatus(WorkInjuryServiceEnum.ON_HOLIDAY.getKey());
                hrWorkInjury.setAppraisalStatus(1);
                hrWorkInjury.setWorkStoppageStartDate(hrLaborAppraisalDTO.getInjuryDate());
                hrWorkInjuryService.updateById(hrWorkInjury);
                this.hrApplyOpLogsService.saveHrApplyOpLogsMiniPhaseTWO(workInjuryId, null, hrTalentStaff.getId(), enterpriseMessage + "####" + appMessage, true, ServiceCenterEnum.LABOR_APPRAISAL.getKey());
            }
        }
        //创建通知配置
        this.hrNotificationUserService.saveRemindContent(hrTalentStaff.getClientId(), ServiceCenterEnum.LABOR_APPRAISAL.getKey(), LaborAppraisalServiceEnum.NOTIFIED_STAFF.getKey(), enterpriseMessage + "。待专管员更新劳动能力鉴定结果", jwtMiniDTO.getId());
        //添加待办
        hrUpcomingService.createServiceUpcoming(hrLaborAppraisal.getId(), hrLaborAppraisal.getStaffId(), "劳动能力鉴定-待专管员更新" + hrTalentStaff.getName() + "的劳动能力鉴定结果", LocalDate.now(), 0);
        //小程序消息中心
        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.LABOR_APPRAISAL.getKey(), hrLaborAppraisal.getId(), hrLaborAppraisal.getStaffId(), "您的劳动能力鉴定申请已提交", false, ServiceCenterEnum.LABOR_APPRAISAL.getValue());
        // 操作日志
        hrLaborAppraisalDTO.setName(hrTalentStaff.getName());
        hrLaborAppraisalDTO.setCertificateNum(hrTalentStaff.getCertificateNum());
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.LABOR_APPRAISAL.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrLaborAppraisalDTO),
            HrLaborAppraisalDTO.class,
            null,
            JSON.toJSONString(hrLaborAppraisal)
        );
        return this.hrLaborAppraisalMapper.toDto(hrLaborAppraisal);
    }

    /**
     * 微信 是否同意进行劳动能力鉴定
     *
     * @param hrLaborAppraisalDTO
     * @return
     */
    @Override
    public HrLaborAppraisalDTO auditLaborAppraisal(HrLaborAppraisalDTO hrLaborAppraisalDTO) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrLaborAppraisal hrLaborAppraisal = getById(hrLaborAppraisalDTO.getId());
        Integer status = hrLaborAppraisalDTO.getStatus();
        hrLaborAppraisal.setStatus(status);
        updateById(hrLaborAppraisal);
        HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(jwtMiniDTO.getId());
        //本人同意劳动能力鉴定
        if (status == LaborAppraisalServiceEnum.INJURY_RESULT.getKey()) {
            //日志

            String enterpriseMessage = hrTalentStaff.getName() + "同意了进行劳动能力鉴定申请";
            String appMessage = getMessage(LaborAppraisalServiceEnum.FEEDBACK_STAFF.getKey().toString(), "您已接受公司发起的劳动能力鉴定，鉴定过程中所需资料可随时到公司处理 ", null);
            this.hrApplyOpLogsService.saveHrApplyOpLogsMiniPhaseTWO(hrLaborAppraisal.getId(), null, hrTalentStaff.getId(), enterpriseMessage + "####" + appMessage, true, ServiceCenterEnum.LABOR_APPRAISAL.getKey());
            //创建通知配置
            this.hrNotificationUserService.saveRemindContent(hrTalentStaff.getClientId(), ServiceCenterEnum.LABOR_APPRAISAL.getKey(), LaborAppraisalServiceEnum.INJURY_RESULT.getKey(), enterpriseMessage + "。待专管员更新劳动能力鉴定结果", jwtMiniDTO.getId());
            //添加待办
            hrUpcomingService.createServiceUpcoming(hrLaborAppraisal.getId(), hrTalentStaff.getId(), "劳动能力鉴定-更新" + hrTalentStaff.getName() + "的劳动能力鉴定结果", LocalDate.now(), 0);

        }
        //本人拒绝劳动能力鉴定
        if (status == LaborAppraisalServiceEnum.REFUSE_STAFF.getKey()) {

            //日志
            String enterpriseMessage = hrTalentStaff.getName() + "拒绝进行劳动能力鉴定申请";
            String appMessage = getMessage(LaborAppraisalServiceEnum.FEEDBACK_STAFF.getKey().toString(), "公司已记录到您拒绝进行劳动能力鉴定，请等待公司的处理通知 ", null);
            this.hrApplyOpLogsService.saveHrApplyOpLogsMiniPhaseTWO(hrLaborAppraisal.getId(), null, hrTalentStaff.getId(), enterpriseMessage + "####" + appMessage, true, ServiceCenterEnum.LABOR_APPRAISAL.getKey());
            //创建通知配置
            this.hrNotificationUserService.saveRemindContent(hrTalentStaff.getClientId(), ServiceCenterEnum.LABOR_APPRAISAL.getKey(), LaborAppraisalServiceEnum.REFUSE_STAFF.getKey(), enterpriseMessage + "。待客户进行处理通知", jwtMiniDTO.getId());
            //添加待办
            hrUpcomingService.createServiceUpcoming(hrLaborAppraisal.getId(), hrTalentStaff.getId(), "劳动能力鉴定-进行" + hrTalentStaff.getName() + "的劳动能力鉴定处理通知", LocalDate.now(), 1);

        }
        return this.hrLaborAppraisalMapper.toDto(hrLaborAppraisal);
    }

    /**
     * 微信 劳动能力鉴定的进度详情
     *
     * @return
     */
    @Override
    public List<HrLaborAppraisalDTO> laborAppraisalPlanned() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        //根据员工id获取所有生育服务的id
        QueryWrapper<HrLaborAppraisal> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("staff_id", jwtMiniDTO.getId());
        queryWrapper.ne("status", LaborAppraisalServiceEnum.NOTIFIED_STAFF.getKey());
        List<HrLaborAppraisal> list = list(queryWrapper);
        ArrayList<HrLaborAppraisalDTO> hrLaborAppraisalDTOS = new ArrayList<>();
        for (HrLaborAppraisal hrLaborAppraisal : list) {
            //获取每一个的日志
            HrLaborAppraisalDTO hrLaborAppraisal1 = hrLaborAppraisalRepository.selectDetatil(hrLaborAppraisal.getId());
            List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(hrLaborAppraisal.getId(), null);
            hrLaborAppraisal1.setApplyOpLogsList(applyOpLogsList);
            hrLaborAppraisalDTOS.add(hrLaborAppraisal1);
        }
        return hrLaborAppraisalDTOS;
    }

    /**
     * 批量通知
     *
     * @param hrLaborAppraisalDTOList
     * @return
     */
    @Override
    public ResponseEntity<?> updateStates(List<HrLaborAppraisalDTO> hrLaborAppraisalDTOList) {

        if (CollectionUtils.isEmpty(hrLaborAppraisalDTOList)) {
            throw new CommonException("请先选择员工");
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> successList = new ArrayList<>();//成功集合
        List<String> errorStatusList = new ArrayList<>();//失败集合

        for (HrLaborAppraisalDTO hrLaborAppraisalDTO : hrLaborAppraisalDTOList) {
            //状态值正确进行修改
            if (hrLaborAppraisalDTO.getStatus() == 0) {
//                    HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(hrFertility.getStaffId());
                hrLaborAppraisalDTO.setStatus(1);
                updateById(hrLaborAppraisalMapper.toEntity(hrLaborAppraisalDTO));
                //添加日志
                HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(hrLaborAppraisalDTO.getStaffId());
                String enterpriseMessage = jwtUserDTO.getRealName() + "通知" + hrTalentStaff.getName() + "进行劳动能力鉴定 ";
                this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrLaborAppraisalDTO.getId(), null, jwtUserDTO.getId(), enterpriseMessage, null, ServiceCenterEnum.LABOR_APPRAISAL.getKey());
                //小程序消息中心
                hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.LABOR_APPRAISAL.getKey(), hrLaborAppraisalDTO.getId(), hrLaborAppraisalDTO.getStaffId(), "公司通知您进行劳动能力鉴定", true, ServiceCenterEnum.LABOR_APPRAISAL.getValue());
                //创建通知配置
                this.hrNotificationUserService.saveRemindContent(hrTalentStaff.getClientId(), ServiceCenterEnum.LABOR_APPRAISAL.getKey(), LaborAppraisalServiceEnum.FEEDBACK_STAFF.getKey(), enterpriseMessage + "。待员工进行反馈", jwtUserDTO.getId());
                //关闭当前待办
                hrUpcomingService.updateUpcoming((hrLaborAppraisalDTO.getId()));
                successList.add(hrLaborAppraisalDTO.getName());
            } else {
                errorStatusList.add(hrLaborAppraisalDTO.getName());
            }

        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态不支持该操作");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * @return
     */
    @Override
    public List<HrWorkInjury> laborAppraisalWork() {
        //获取员工id
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        QueryWrapper<HrWorkInjury> queryWrapper = new QueryWrapper<>();
        ArrayList<Integer> arrayList = new ArrayList<>();
        arrayList.add(WorkInjuryServiceEnum.PAY_OFF_PERIOD.getKey());
        arrayList.add(WorkInjuryServiceEnum.ON_HOLIDAY.getKey());
        arrayList.add(WorkInjuryServiceEnum.RETURN_TO_WORK.getKey());
        arrayList.add(WorkInjuryServiceEnum.ARRIVED.getKey());
        arrayList.add(WorkInjuryServiceEnum.ABSENTEEISM.getKey());
        arrayList.add(WorkInjuryServiceEnum.RENEW_PERIOD.getKey());
        arrayList.add(WorkInjuryServiceEnum.ARRIVE_BY_DEFAULT.getKey());
        queryWrapper.in("status", arrayList);
        queryWrapper.eq("appraisal_status", 0);
        queryWrapper.eq("staff_id", jwtMiniDTO.getId());
        List<HrWorkInjury> list = hrWorkInjuryService.list(queryWrapper);
        return list;
    }

    @Override
    public String exportLaborAppraisals(HrLaborAppraisalDTO hrLaborAppraisalDTO, HttpServletResponse httpServletResponse) {
        //查询数据权限
        List<String> clientId = hrClientService.selectClientIdByUserId();
        QueryWrapper<HrLaborAppraisal> qw = this.queryWrapper(hrLaborAppraisalDTO, clientId);
        List<HrLaborAppraisalExport> list = this.hrLaborAppraisalRepository.exportLaborAppraisals(qw);
        List<String> ids = list.stream().map(HrLaborAppraisalExport::getId).collect(Collectors.toList());
        Map<Integer, String> staffType = codeTableService.findCodeTableByInnerName("staffType");
        Map<Integer, String> labourIdentifyState = codeTableService.findCodeTableByInnerName("labourIdentifyState");
        for (HrLaborAppraisalExport hrLaborAppraisalExport : list) {
            hrLaborAppraisalExport.setPersonnelTypeName(staffType.get(hrLaborAppraisalExport.getPersonnelType()));
            hrLaborAppraisalExport.setStatusName(labourIdentifyState.get(hrLaborAppraisalExport.getStatus()));
            if (hrLaborAppraisalExport.getWorkStoppageStartDate() != null) {
                String start = hrLaborAppraisalExport.getWorkStoppageStartDate().toString();
                String end = "";
                if (hrLaborAppraisalExport.getWorkStoppageEndDate() != null) {
                    end = hrLaborAppraisalExport.getWorkStoppageEndDate().toString();
                }
                hrLaborAppraisalExport.setWorkStoppageDate(start + "至" + end);
            }
        }
        int listSize = list.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "劳动能力鉴定", HrLaborAppraisalExport.class);
        // 操作日志
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.LABOR_APPRAISAL.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    @Override
    public HrLaborAppraisalDTO laborAppraisalPlannedOne(String serviceId) {
        HrLaborAppraisalDTO hrLaborAppraisal1 = hrLaborAppraisalRepository.selectDetatil(serviceId);
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(serviceId, null);
        hrLaborAppraisal1.setApplyOpLogsList(applyOpLogsList);
        return hrLaborAppraisal1;
    }

    /**
     * 日志信息
     *
     * @param status
     * @param message
     * @return
     */
    private String getMessage(String status, String message, String content) {
        JSONObject enterpriseMessage = new JSONObject();
        enterpriseMessage.put("status", status);
        enterpriseMessage.put("message", message);
        enterpriseMessage.put("content", content);
        return enterpriseMessage.toJSONString();
    }
}
