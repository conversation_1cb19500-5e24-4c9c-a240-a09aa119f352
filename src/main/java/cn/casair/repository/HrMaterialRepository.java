package cn.casair.repository;

import cn.casair.domain.HrMaterial;
import cn.casair.dto.HrMaterialDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 材料表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Repository
public interface HrMaterialRepository extends BaseMapper<HrMaterial> {

    IPage<HrMaterialDTO> selectMaterialPage(Page<HrMaterial> page,@Param("param") HrMaterialDTO hrMaterialDTO);

    HrMaterialDTO selectHrMaterialInfo(@Param("id") String id);
}
