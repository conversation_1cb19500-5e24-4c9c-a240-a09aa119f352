package cn.casair.repository;

import cn.casair.domain.HrRetire;
import cn.casair.dto.HrRetireDTO;
import cn.casair.dto.excel.HrRetireExport;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 退休数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Repository
public interface HrRetireRepository extends BaseMapper<HrRetire> {
    @Select("SELECT\n" +
        "\thr.id,\n" +
        "\thr.apply_retire_date,\n" +
        "\thr.apply_description,\n" +
        "\thr.staff_id,\n" +
        "\tht.client_id,\n" +
        "\thc.client_name,\n" +
        "\tht.`name`,\n" +
        "\tht.certificate_num,\n" +
        "\tht.sex,\n" +
        "\tht.phone,\n" +
        "\tht.`staff_status`,\n" +
        "\ths.profession_name,\n" +
        "\ths.id,\n" +
        "\tht.personnel_type,\n" +
        "\thr.`status`,\n" +
        "\tsu.real_name AS specialized,\n" +
        "\tsu.id,\n" +
        "\thr.`actual_retire_date`,\n" +
        "\thr.`original_retire_date`,\n" +
        "\thr.operate_cont,\n" +
        "\thr.remark\n" +
        "\n" +
        "FROM\n" +
        "\thr_retire hr\n" +
        "\tLEFT JOIN hr_talent_staff ht ON hr.staff_id = ht.id \n" +
        "\tAND ht.is_delete = 0\n" +
        "\tLEFT JOIN hr_staff_work_experience hsw ON hr.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id \n" +
        "\tAND hsw.is_delete = 0 \n" +
        "\tAND hsw.iz_default = 1\n" +
        "\tLEFT JOIN hr_station hs ON hsw.station_id = hs.id \n" +
        "\tAND hs.is_delete = 0\n" +
        "\tLEFT JOIN hr_client hc ON hc.id = hr.client_id \n" +
        "\tAND hc.is_delete = 0\n" +
        "\tLEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id \n" +
        "\tAND huc.is_delete = 0 \n" +
        "\tAND huc.is_specialized = 1\n" +
        "\tLEFT JOIN sys_user su ON huc.user_id = su.id \n" +
        "\tAND su.is_delete = 0 ${ew.customSqlSegment}")
    IPage<HrRetireDTO> page(Page page, @Param(Constants.WRAPPER)QueryWrapper<HrRetire> qw);

    HrRetireDTO selectDetatil(String id);

    List<HrRetireExport> exportRetire(@Param(Constants.WRAPPER)QueryWrapper<HrRetire> qw);
}
