package cn.casair.service;

import cn.casair.domain.HrSendSms;
import cn.casair.dto.HrSendSmsDTO;
import cn.casair.dto.WebLoginDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 发送短信记录表服务类
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
public interface HrSendSmsService extends IService<HrSendSms> {

    /**
     * 短信群发
     *
     * @param file
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/1/10
     **/
    String groupSending(MultipartFile file);

    /**
     * 发送验证码
     *
     * @param appLoginDTO 登录信息
     * @return void
     * <AUTHOR>
     * @date 2021/9/14
     **/
    String sendVerificationCode(WebLoginDTO appLoginDTO);


    /**
     * 分页查询发送短信记录表
     *
     * @param hrSendSmsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrSendSmsDTO hrSendSmsDTO, Long pageNumber, Long pageSize);

    /**
     * 用于个人中心修改手机号
     * @param phone 手机号
     * @param id     发送人id
     */
    String sendShortMessage(String phone, String id, String templateCode);

    /**
     * app端用户登录 发送验证码
     * @param appLoginDTO
     * @return
     */
    String sendAppCode(WebLoginDTO appLoginDTO);

    String exportSms(HrSendSmsDTO hrSendSmsDTO, HttpServletResponse httpServletResponse);
}
