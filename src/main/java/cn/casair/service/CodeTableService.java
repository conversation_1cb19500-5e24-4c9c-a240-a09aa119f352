package cn.casair.service;

import cn.casair.domain.CodeTable;
import cn.casair.dto.CodeTableDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 码表服务类
 *
 * <AUTHOR>
 * @since 2020-04-10
 */
public interface CodeTableService extends IService<CodeTable> {

    CodeTable getMaxChildrenByInnerName(String ownedBank);

    CodeTable getItemByBankName(String bankName);

    /**
     * 用户添加自定义字典项
     *
     * @param codeTableDTO
     * @return cn.casair.dto.CodeTableDTO
     * <AUTHOR>
     * @date 2021/9/14
     **/
    CodeTableDTO createCustomizeCodeTable(CodeTableDTO codeTableDTO);

    /**
     * 根据内部名获取字典信息
     *
     * @param innerName 内部名
     * @return cn.casair.dto.CodeTableDTO
     * <AUTHOR>
     * @date 2021/9/14
     **/
    CodeTableDTO getCodeTableByInnerName(String innerName);

    /**
     * 根据内部名获取字典项子级列表(特殊处理)
     *
     * @param innerName
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/8/31
     **/
    List<CodeTableDTO> getCodeTableSpecialListByInnerName(String innerName);

    /**
     * 根据内部名获取字典项子级列表
     *
     * @param innerName
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/8/31
     **/
    List<CodeTableDTO> getCodeTableListByInnerName(String innerName);

    /**
     * 根据内部名获取字典项
     *
     * @param innerName
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/8/31
     **/
    Map<Integer, String> findCodeTableByInnerName(String innerName);

    /**
     * 根据value值获取字典项
     * @param innerValue
     * @return
     */
    Map<String, Integer> findCodeTableByInnerValue(String innerValue);

    /**
     * 创建码表
     *
     * @param codeTableDTO
     * @return
     */
    CodeTableDTO createCodeTable(CodeTableDTO codeTableDTO);

    /**
     * 修改码表
     *
     * @param codeTableDTO
     * @return
     */
    Optional<CodeTableDTO> updateCodeTable(CodeTableDTO codeTableDTO);

    /**
     * 查询码表详情
     *
     * @param id
     * @return
     */
    CodeTableDTO getCodeTable(String id);

    /**
     * 删除码表
     *
     * @param id
     */
    void deleteCodeTable(String id);

    /**
     * 批量删除码表
     *
     * @param ids
     */
    void deleteCodeTable(List<String> ids);

    /**
     * 分页查询码表
     *
     * @param codeTableDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<CodeTableDTO> findPage(CodeTableDTO codeTableDTO, Long pageNumber, Long pageSize);

    /**
     * 获取码表子级列表
     *
     * @param codeTableDTO codeTableDTO
     * <AUTHOR>
     * @date 2020/5/9 11:51
     */
    List<CodeTableDTO> findChildrenList(CodeTableDTO codeTableDTO);

    /**
     * 根据父级key获取子项列表
     *
     * @param codeTableKey
     * @return java.util.List<cn.casair.domain.CodeTable>
     * <AUTHOR> yanglei 2020/6/4 15:27
     */
    List<CodeTable> getChildrenByKey(String codeTableKey);

    /**
     * 获取字典树形结构
     *
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/9/1
     **/
    List<CodeTableDTO> getCodeTableTree();

    /**
     * 根据父id获取子级列表
     *
     * @param parentId
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/10/9
     **/
    List<CodeTableDTO> getChildrenByParentId(Integer parentId);

    /**
     * 数据字典导出
     *
     * @param ids
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2021/10/9
     **/
    void exportCodeTable(List<Integer> ids, HttpServletResponse response);

    /**
     * 获取字典项导入模板
     *
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2021/10/9
     **/
    void downloadImportTemplate(HttpServletResponse response);

    /**
     * 数据字典 导入
     *
     * @param file
     * @param parentId
     * @return cn.casair.common.utils.excel.ImportResultDTO
     * <AUTHOR>
     * @date 2021/10/9
     **/
    String importCodeTable(MultipartFile file, Integer parentId);
}
