package cn.casair.repository;

import cn.casair.domain.HrCertificateIssuance;
import cn.casair.dto.HrCertificateIssuanceDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 证明开具数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Repository
public interface HrCertificateIssuanceRepository extends BaseMapper<HrCertificateIssuance> {

    /**
     * 证明开具列表
     * @param page
     * @param hrCertificateIssuanceDTO
     * @return
     */
    IPage<HrCertificateIssuanceDTO> findPage(Page<HrCertificateIssuance> page, @Param("param") HrCertificateIssuanceDTO hrCertificateIssuanceDTO);

    /**
     * 证明开具查看
     * @param id
     * @return
     */
    HrCertificateIssuanceDTO findById(String id);

    /**
     * 导出证明开具
     * @param hrCertificateIssuanceDTO
     * @return
     */
    List<HrCertificateIssuanceDTO> getCertificateIssuanceList(@Param("param") HrCertificateIssuanceDTO hrCertificateIssuanceDTO);

    List<HrCertificateIssuanceDTO> certificateIssuanceListByStaffId(@Param("staffId") String id);

    @Update("UPDATE hr_certificate_issuance SET certificate_status = #{certificateStatus},is_need = #{isNeed} WHERE id = #{id} ")
    void updateCertificateStatus(@Param("certificateStatus") Integer certificateStatus,@Param("isNeed") Integer isNeed, @Param("id") String id);

    /**
     * 首页申请列表对应角色展示对应数据
     * @param issuanceDTO
     * @return
     */
    List<HrCertificateIssuance> selectApplyListByRole(@Param("param") HrCertificateIssuanceDTO issuanceDTO);

}
