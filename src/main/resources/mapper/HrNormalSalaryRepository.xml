<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrNormalSalaryRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, client_id, pay_year, pay_monthly, is_export,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrNormalSalary">
                    <id column="id" property="id"/>
                    <result column="client_id" property="clientId"/>
                    <result column="pay_year" property="payYear"/>
                    <result column="pay_monthly" property="payMonthly"/>
                    <result column="is_export" property="isExport"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

</mapper>
