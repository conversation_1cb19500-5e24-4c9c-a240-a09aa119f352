package cn.casair.repository;

import cn.casair.domain.HrContractView;
import cn.casair.dto.HrContractViewDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 合同查看下载申请数据库操作类
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Repository
public interface HrContractViewRepository extends BaseMapper<HrContractView> {

    IPage<HrContractViewDTO> findPage(Page<HrContractView> page, @Param("param") HrContractViewDTO hrContractViewDTO, @Param("permissionClient") List<String> clientIds);

    List<HrContractViewDTO> findList(@Param("param") HrContractViewDTO hrContractViewDTO, @Param("permissionClient") List<String> clientIds);

    HrContractViewDTO findInfoById(@Param("id") String id);

    List<HrContractViewDTO> findBatchIds(@Param("ids") List<String> applyIdList);

    void updateContractView(@Param("param") HrContractViewDTO hrContractViewDTO);

}
