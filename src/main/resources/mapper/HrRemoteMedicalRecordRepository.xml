<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRemoteMedicalRecordRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, staff_id, medicine_place, register_start_date, register_end_date, phone, remark,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrRemoteMedicalRecord">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="medicine_place" property="medicinePlace"/>
        <result column="register_start_date" property="registerStartDate"/>
        <result column="register_end_date" property="registerEndDate"/>
        <result column="phone" property="phone"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findById" resultType="cn.casair.dto.HrRemoteMedicalRecordDTO">
        SELECT
                hc.client_name,
                hts.`name`,
                hts.certificate_num,
                hts.sex,
                IF ( hts.sex = 1, '男', '女' ) sexLabel,
                hts.phone AS staffPhone,
                hts.personnel_type,
                hs.profession_name,
                hse.salary,
                hrm.*
        FROM
                hr_remote_medical_record hrm
        LEFT JOIN hr_talent_staff hts ON hrm.staff_id = hts.id
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        LEFT JOIN v_staff_work_experience hswe ON hswe.staff_id = hts.id AND ( CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END ) AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hswe.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN
            (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
            FROM (SELECT id,client_id,staff_id,state,contract_start_date,contract_end_date,is_delete FROM hr_contract WHERE is_delete = 0 AND state in (1,2,3) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) hct
            ON hct.staff_id = hts.id AND hct.is_delete = 0
            LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hts.id AND hse.is_delete = 0
        WHERE hrm.is_delete = 0 AND hrm.id = #{id}
    </select>

    <sql id="Select_Sql">
        SELECT
        hc.client_name,
        hts.`name`,
        hts.staff_status,
        hts.certificate_num,
        hts.sex,
        IF ( hts.sex = 1, '男', '女' ) sexLabel,
        hts.phone AS staffPhone,
        hts.personnel_type,
        hts.staff_status,
        hs.profession_name,
        hrm.*
        FROM
        hr_remote_medical_record hrm
        LEFT JOIN hr_talent_staff hts ON hrm.staff_id = hts.id
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        LEFT JOIN v_staff_work_experience hswe ON hswe.staff_id = hts.id AND ( CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END ) AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hswe.station_id = hs.id AND hs.is_delete = 0
        WHERE hrm.is_delete = 0
        <if test="permissionClient!=null and permissionClient.size() > 0">
            AND hts.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hts.client_id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.name != null and param.name != ''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND hts.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.personnelType != null">
            AND hts.personnel_type = #{param.personnelType}
        </if>
        <if test="param.professionName != null and param.professionName != ''">
            AND hs.profession_name LIKE concat('%', #{param.professionName}, '%')
        </if>
        <if test="param.sex!=null">
            AND hts.sex = #{param.sex}
        </if>
        <if test="param.staffStatusList!=null and param.staffStatusList.size() > 0">
            AND hts.staff_status IN
            <foreach collection="param.staffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.ids!=null and param.ids.size() > 0">
            AND hrm.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.registerStartDateStart != null">
            AND hrm.register_start_date &gt;= #{param.registerStartDateStart}
        </if>
        <if test="param.registerStartDateEnd!=null">
            AND hrm.register_start_date &lt;= #{param.registerStartDateEnd}
        </if>
        <if test="param.registerEndDateStart != null">
            AND hrm.register_end_date &gt;= #{param.registerEndDateStart}
        </if>
        <if test="param.registerEndDateEnd!=null">
            AND hrm.register_end_date &lt;= #{param.registerEndDateEnd}
        </if>
        <choose>
            <when test="param.field!=null and param.field!=''">
                ORDER BY ${param.field} ${param.order}
            </when>
            <otherwise>
                ORDER BY hrm.created_date DESC
            </otherwise>
        </choose>
    </sql>
    <select id="findPage" resultType="cn.casair.dto.HrRemoteMedicalRecordDTO">
        <include refid="Select_Sql"></include>
    </select>

    <select id="findList" resultType="cn.casair.dto.HrRemoteMedicalRecordDTO">
        SELECT
            hc.client_name,
            hts.`name`,
            hts.certificate_num,
            hts.sex,
            IF ( hts.sex = 1, '男', '女' ) sexLabel,
            hts.phone AS staffPhone,
            hts.personnel_type,
            hrm.*
        FROM
            hr_remote_medical_record hrm
        LEFT JOIN hr_talent_staff hts ON hrm.staff_id = hts.id
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        WHERE hrm.is_delete = 0 AND hrm.staff_id = #{staffId}
    </select>
    <select id="findListByDTO" resultType="cn.casair.dto.HrRemoteMedicalRecordDTO">
        <include refid="Select_Sql"></include>
    </select>

</mapper>
