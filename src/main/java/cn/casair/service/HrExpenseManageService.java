package cn.casair.service;
import cn.casair.domain.HrExpenseManage;
import cn.casair.dto.HrExpenseManageDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 费用项管理表服务类
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface HrExpenseManageService extends IService<HrExpenseManage> {


    /**
     * 创建费用项管理表
     * @param hrExpenseManageDTO
     * @return
     */
    HrExpenseManageDTO createHrExpenseManage(HrExpenseManageDTO hrExpenseManageDTO);

    /**
     * 修改费用项管理表
     * @param hrExpenseManageDTO
     * @return
     */
    Optional<HrExpenseManageDTO> updateHrExpenseManage(HrExpenseManageDTO hrExpenseManageDTO);

    /**
     * 查询费用项管理表详情
     * @param id
     * @return
     */
    HrExpenseManageDTO getHrExpenseManage(String id);

    /**
     * 删除费用项管理表
     * @param id
     */
    void deleteHrExpenseManage(String id);

    /**
     * 批量删除费用项管理表
     * @param ids
     */
    void deleteHrExpenseManage(List<String> ids);

    /**
     * 分页查询费用项管理表
     * @param hrExpenseManageDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrExpenseManageDTO hrExpenseManageDTO,Long pageNumber,Long pageSize);

    String exportExpenseManage(HrExpenseManageDTO hrExpenseManageDTO, HttpServletResponse httpServletResponse);

    String importExpenseManage(MultipartFile file);

    String importExpenseManageTemplate(HttpServletResponse response);

}
