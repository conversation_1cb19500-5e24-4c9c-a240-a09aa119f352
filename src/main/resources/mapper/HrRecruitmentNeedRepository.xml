<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRecruitmentNeedRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, client_id, recruitment_number, status,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.dto.HrRecruitmentNeedDTO">
                    <id column="id" property="id"/>
                    <result column="client_id" property="clientId"/>
                    <result column="recruitment_number" property="recruitmentNumber"/>
                    <result column="status" property="status"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
            <collection property="hrRecruitmentStationList"
                        ofType="cn.casair.domain.HrRecruitmentStation"
                        column="rn.id"
                        select="cn.casair.repository.HrRecruitmentNeedRepository.page">
            </collection>
        </resultMap>

        <select id="page" resultMap="BaseResultMap">
            SELECT
            rn.*,
            rs.exam_format,
            rs.recruitment_term,
            rc.client_name,
            hs.profession_name,
            rs.total
            FROM
            hr_recruitment_need rn
            LEFT JOIN ( SELECT sum( recruitment_people_number ) total, service_id, any_value ( recruitment_station_id )recruitment_station_id , any_value ( exam_format ) exam_format  , any_value (recruitment_term ) recruitment_term FROM hr_recruitment_station GROUP BY service_id ) AS rs ON rn.id = rs.service_id
            LEFT JOIN hr_client rc ON rc.id = rn.client_id
            AND rc.is_delete = 0
            LEFT JOIN hr_station hs ON hs.id = rs.recruitment_station_id
            AND hs.is_delete = 0
                ${ew.customSqlSegment}
    </select>
</mapper>
