package cn.casair.service;

import cn.casair.common.utils.excel.CellItem;
import cn.casair.domain.HrBillDynamicFields;
import cn.casair.dto.EnumDTO;
import cn.casair.dto.HrBillDynamicFieldsDTO;
import cn.casair.dto.HrExpenseManageDTO;
import cn.casair.dto.billl.BillTableHeaderDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * 薪酬账单动态字段服务类
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
public interface HrBillDynamicFieldsService extends IService<HrBillDynamicFields> {


    /**
     * 创建薪酬账单动态字段
     * @param hrBillDynamicFieldsDTO
     * @return
     */
    HrBillDynamicFieldsDTO createHrBillDynamicFields(HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO);

    /**
     * 修改薪酬账单动态字段
     * @param hrBillDynamicFieldsDTO
     * @return
     */
    Optional<HrBillDynamicFieldsDTO> updateHrBillDynamicFields(HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO);

    /**
     * 查询薪酬账单动态字段详情
     * @param id
     * @return
     */
    HrBillDynamicFieldsDTO getHrBillDynamicFields(String id);

    /**
     * 删除薪酬账单动态字段
     * @param id
     */
    void deleteHrBillDynamicFields(String id);

    /**
     * 批量删除薪酬账单动态字段
     * @param ids
     */
    void deleteHrBillDynamicFields(List<String> ids);

    /**
     * 分页查询薪酬账单动态字段
     * @param hrBillDynamicFieldsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO,Long pageNumber,Long pageSize);

    /**
     *  导入工资明细后,获取表头
     * @param file excel文件
     * @param billId 账单id
     * @param originSalaryId 薪酬原单id
     * @param clientId 客户ID
     */
    BillTableHeaderDTO getOriginTableHeader(MultipartFile file, String billId, String originSalaryId,String clientId);

    /**
     * 功能描述: 工资账单设置字段映射
     * @param billId 账单id
     * @param param
     * @author: liu
     * @date: 2021/11/2 9:21
     */
    List<List<CellItem>> setFieldMappingRelation(String billId, HrExpenseManageDTO param);

    /**
     * 功能描述: 获取未映射的字段列表
     * @param billId 账单id
     * <AUTHOR>
     * @date  2021/11/2 10:34
     */
    List<CellItem> getUnUsedFieldList(String billId);

    /**
     *  更新数据开始行
     * @param billId 账单id
     * @param startRow 数据开始行
     */
    void updateStartRowByBillId(String billId, Integer startRow);

    /**
     *  薪酬账单详情页===前端获取动态表头
     * @param billIds 账单id
     * @return
     */
    List<CellItem> getTableDynamicHeader(List<String> billIds);

    /**
     *  excel导入后,获取展示对象
     * @param file excel文件
     * @param expenseTypes 费用项类型
     * @param clientId 客户ID
     * @param idStrs 身份标识集合
     * @param flag 0-薪酬账单 1-中石化账单 2 其他账单 3结算单-海尔集团
     * @param type 账单类型
     * @return
     */
    BillTableHeaderDTO getTableHeaderResult(MultipartFile file, List<String> expenseTypes,String clientId,List<String> idStrs,Integer flag, Integer type);

    /**
     *  导盘文件导入
     * @param file 导盘文件
     * @param billId 账单id
     * @param type 导盘类型 0 社保, 1 医保, 2 公积金,3 个税, 4第三方账单
     * @return
     */
    BillTableHeaderDTO importGuidePlate(MultipartFile file, String billId, Integer type);

    /**
     * 导盘对账设置字段映射
     * @param id 对账配置id
     * @param param 费用项映射
     * @return List<List<CellItem>>
     * <AUTHOR>
     * @date 2021/11/10 9:42
     */
    List<List<CellItem>> setFieldMrByGuidePlate(String id, HrExpenseManageDTO param);

    /**
     * 删除字段映射关系
     * @param billId
     * @param param
     * @return
     */
    List<List<CellItem>> delMappingRelationWithBill(String billId, HrExpenseManageDTO param);

    /**
     * 薪酬账单获取费用项类型
     * @return
     * @param billType 账单类型 0薪酬账单 1保障账单 3其他账单
     * @param clientId 用于薪酬账单
     */
    List<EnumDTO> getExpenseManage(Integer billType, String clientId);

    /**
     *  对账删除字段映射
     * @param billConfigId 对账配置id
     * @param param
     * @return
     */
    List<List<CellItem>> delMappingRelationWithCompare(String billConfigId, HrExpenseManageDTO param);

    /**
     * 批量对账导盘导入
     * @param paymentDate 缴费年月
     * @param type 导盘类型 0 社保, 1 医保, 2 公积金, 3 个税
     * @return
     */
    BillTableHeaderDTO batchReconciliationBill(MultipartFile file, String paymentDate, Integer type);

    /**
     * 处理账单字段映射
     * @param dynamicFieldsDTO
     * @param param
     */
    List<List<CellItem>> handleFieldMappingRelation(HrBillDynamicFieldsDTO dynamicFieldsDTO, HrExpenseManageDTO param);

    /**
     * 删除账单字段映射
     * @param dynamicFieldsDTO
     * @param param
     */
    List<List<CellItem>> handleDelMappingRelation(HrBillDynamicFieldsDTO dynamicFieldsDTO, HrExpenseManageDTO param);

}
