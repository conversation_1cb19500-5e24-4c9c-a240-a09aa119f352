package cn.casair.service;

import cn.casair.domain.HrContentTemplate;
import cn.casair.dto.HrContentTemplateDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 简历模板服务类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
public interface HrContentTemplateService extends IService<HrContentTemplate> {


    /**
     * 创建简历模板
     * @param hrContentTemplateDTO
     * @return
     */
    List<HrContentTemplateDTO> createHrContentTemplate(List<HrContentTemplateDTO> hrContentTemplateDTO);

    /**
     * 修改简历模板
     * @param hrContentTemplateDTO
     * @return
     */
    Optional<HrContentTemplateDTO> updateHrContentTemplate(HrContentTemplateDTO hrContentTemplateDTO);

    /**
     * 查询简历模板详情
     * @param
     * @return
     */
    List<HrContentTemplateDTO> getHrContentTemplate();

    /**
     * 删除简历模板
     * @param id
     */
    void deleteHrContentTemplate(String id);

    /**
     * 批量删除简历模板
     * @param ids
     */
    void deleteHrContentTemplate(List<String> ids);

    /**
     * 分页查询简历模板
     * @param hrContentTemplateDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrContentTemplateDTO hrContentTemplateDTO,Long pageNumber,Long pageSize);

    HrContentTemplateDTO createHrContentTemplateIndex(HrContentTemplateDTO hrContentTemplateDTO);
}
