<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrWelfareCompensationRecordRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, social_security_cardinal_base_old, medical_insurance_cardinal_base_old, accumulation_fund_cardinal_base_old, unit_pension_scale_old, unit_medical_scale_old, work_injury_scale_old, unit_unemployment_scale_old,
        personal_pension_scale_old, personal_medical_scale_old, personal_unemployment_scale_old, unit_accumulation_fund_scale_old, personal_accumulation_fund_scale_old,
        social_security_cardinal_base_new, medical_insurance_cardinal_base_new, accumulation_fund_cardinal_base_new, unit_pension_scale_new, unit_medical_scale_new, work_injury_scale_new, unit_unemployment_scale_new,
        personal_pension_scale_new, personal_medical_scale_new, personal_unemployment_scale_new, unit_accumulation_fund_scale_new, personal_accumulation_fund_scale_new, change_msg,
        social_security_cardinal_personal_base_old,medical_insurance_cardinal_personal_base_old,social_security_cardinal_personal_base_new,medical_insurance_cardinal_personal_base_new,
        personal_tax_system,personal_tax_source,unit_late_fee_system,unit_late_fee_source,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrWelfareCompensationRecord">
        <id column="id" property="id"/>
        <result column="social_security_cardinal_base_old" property="socialSecurityCardinalBaseOld"/>
        <result column="medical_insurance_cardinal_base_old" property="medicalInsuranceCardinalBaseOld"/>
        <result column="accumulation_fund_cardinal_base_old" property="accumulationFundCardinalBaseOld"/>
        <result column="unit_pension_scale_old" property="unitPensionScaleOld"/>
        <result column="unit_medical_scale_old" property="unitMedicalScaleOld"/>
        <result column="work_injury_scale_old" property="workInjuryScaleOld"/>
        <result column="unit_unemployment_scale_old" property="unitUnemploymentScaleOld"/>
        <result column="personal_pension_scale_old" property="personalPensionScaleOld"/>
        <result column="personal_medical_scale_old" property="personalMedicalScaleOld"/>
        <result column="personal_unemployment_scale_old" property="personalUnemploymentScaleOld"/>
        <result column="unit_accumulation_fund_scale_old" property="unitAccumulationFundScaleOld"/>
        <result column="personal_accumulation_fund_scale_old" property="personalAccumulationFundScaleOld"/>
        <result column="social_security_cardinal_base_new" property="socialSecurityCardinalBaseNew"/>
        <result column="medical_insurance_cardinal_base_new" property="medicalInsuranceCardinalBaseNew"/>
        <result column="accumulation_fund_cardinal_base_new" property="accumulationFundCardinalBaseNew"/>
        <result column="unit_pension_scale_new" property="unitPensionScaleNew"/>
        <result column="unit_medical_scale_new" property="unitMedicalScaleNew"/>
        <result column="work_injury_scale_new" property="workInjuryScaleNew"/>
        <result column="unit_unemployment_scale_new" property="unitUnemploymentScaleNew"/>
        <result column="personal_pension_scale_new" property="personalPensionScaleNew"/>
        <result column="personal_medical_scale_new" property="personalMedicalScaleNew"/>
        <result column="personal_unemployment_scale_new" property="personalUnemploymentScaleNew"/>
        <result column="unit_accumulation_fund_scale_new" property="unitAccumulationFundScaleNew"/>
        <result column="personal_accumulation_fund_scale_new" property="personalAccumulationFundScaleNew"/>
        <result column="change_msg" property="changeMsg"/>
        <result column="social_security_cardinal_personal_base_old" property="socialSecurityCardinalPersonalBaseOld"/>
        <result column="medical_insurance_cardinal_personal_base_old" property="medicalInsuranceCardinalPersonalBaseOld"/>
        <result column="social_security_cardinal_personal_base_new" property="socialSecurityCardinalPersonalBaseNew"/>
        <result column="medical_insurance_cardinal_personal_base_new" property="medicalInsuranceCardinalPersonalBaseNew"/>
        <result column="personal_tax_system" property="personalTaxSystem"/>
        <result column="personal_tax_source" property="personalTaxSource"/>
        <result column="unit_late_fee_system" property="unitLateFeeSystem"/>
        <result column="unit_late_fee_source" property="unitLateFeeSource"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByBillId" resultType="cn.casair.dto.HrWelfareCompensationRecordDTO">
        SELECT
            wcr.id,
            wcr.created_by,
            wcr.created_date,
            wcr.change_msg
        FROM
            hr_welfare_compensation_record wcr
                LEFT JOIN hr_welfare_compensation wc ON wc.record_id = wcr.id
                LEFT JOIN hr_make_up_use_record mur ON mur.make_up_id = wc.id
        WHERE
            wcr.is_delete = 0
          AND wc.is_delete = 0
          AND mur.is_delete = 0
          AND mur.bill_id = #{billId}
        ORDER BY
            wcr.created_date
    </select>

    <update id="deleteByWelfareCompensationIds">
        UPDATE hr_welfare_compensation_record  wcr
            LEFT JOIN hr_welfare_compensation wc ON wc.record_id = wcr.id
            SET wcr.is_delete = 1
        WHERE
            wc.is_delete = 0
            AND wc.id IN
            <foreach collection="welfareCompensationIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </update>

</mapper>
