package cn.casair.repository;
import cn.casair.domain.HrClient;
import cn.casair.domain.HrSocialSecurity;
import cn.casair.dto.HrAccumulationFundDTO;
import cn.casair.dto.HrPlatformAccountDTO;
import cn.casair.dto.HrSocialSecurityDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 社保医保表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Repository
public interface HrSocialSecurityRepository extends BaseMapper<HrSocialSecurity> {

    /**
     * 获取客户社保配置
     *
     * @param clientIds
     * @return cn.casair.domain.HrSocialSecurity
     * <AUTHOR>
     * @date 2021/11/2
     **/
    List<HrSocialSecurityDTO> getClientSocialSecurity(@Param("clientIds") List<String> clientIds);

    /**
     * 获取使用此社保类型的公司列表
     *
     * @param id
     * @return java.util.List<cn.casair.domain.HrClient>
     * <AUTHOR>
     * @date 2021/11/1
     **/
    List<HrClient> getClientBySocialSecurityTypeId(String id);

    /**
     * 社保类型下拉列表数据
     *
     * @return java.util.List<cn.casair.dto.HrSocialSecurityDTO>
     * <AUTHOR>
     * @date 2021/10/18
     **/
    List<HrSocialSecurityDTO> getSocialSecurityTypeList();

    List<HrPlatformAccountDTO> getSelectHrplatform( );



    List<HrAccumulationFundDTO> getAccumulationFund( );

    List<HrSocialSecurityDTO> getSocialSecurity( );

    /**
     * 根据员工查询薪酬参数
     * @param staffId
     * @return
     */
    HrSocialSecurityDTO socialSecurityListByStaffId(@Param("staffId") String staffId);

    /**
     * 修改社保类型
     * @param hrSocialSecurity
     */
    void updateHrSocialSecurity(@Param("param") HrSocialSecurity hrSocialSecurity);

}
