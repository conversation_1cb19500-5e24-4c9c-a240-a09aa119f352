package cn.casair.repository;

import cn.casair.domain.HrRecruitmentBrochure;
import cn.casair.dto.HrRecruitmentBrochureDTO;
import cn.casair.dto.HrRecruitmentStationDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 招聘简章数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Repository
public interface HrRecruitmentBrochureRepository extends BaseMapper<HrRecruitmentBrochure> {

    /**
     * 招聘简章列表
     * @param page
     * @param hrRecruitmentBrochureDTO
     * @return
     */
    IPage<HrRecruitmentBrochureDTO> findPage(Page<HrRecruitmentBrochure> page, @Param("param")HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO);

    /**
     * 查看--招聘简章
     * @param id
     * @return
     */
    HrRecruitmentBrochureDTO findRecruitmentBrochureById(String id);

    IPage<HrRecruitmentBrochureDTO> findPageSelectWX(Page<HrRecruitmentBrochure> page);

    List<HrRecruitmentStationDTO> getRecruitment(String id);

    @Update("UPDATE hr_recruitment_brochure SET register_state = #{registerState} WHERE id = #{id}")
    void updateRecruitmentEnd(@Param("id") String id, @Param("registerState") Integer registerState);

    /**
     * 不分页查询招聘简章列表
     * @param recruitmentBrochureDTO
     * @return
     */
    List<HrRecruitmentBrochureDTO> findList(@Param("param")HrRecruitmentBrochureDTO recruitmentBrochureDTO);

    /**
     * 获取最新有企业微信的招聘简章
     * @return
     */
    @Select("SELECT * FROM hr_recruitment_brochure WHERE enterprise_wechat_url IS NOT NULL  ORDER BY created_date DESC LIMIT 1")
    HrRecruitmentBrochureDTO findEnterpriseWechatUrl();

    /**
     * 官网查询招聘信息
     * @param hrRecruitmentBrochureDTO
     * @return
     */
    IPage<HrRecruitmentBrochureDTO> findPageSelectOW(Page<HrRecruitmentBrochure> page, @Param("param") HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO);

    /**
     * 更新招聘简章信息
     * @param hrRecruitmentBrochure
     */
    void updateBrochure(@Param("param") HrRecruitmentBrochure hrRecruitmentBrochure);
}
