package cn.casair.repository;

import cn.casair.domain.UserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户角色关联数据库操作类
 *
 * <AUTHOR>
 * @since 2020-05-12
 */
@Repository
public interface UserRoleRepository extends BaseMapper<UserRole> {

    List<UserRole> selectUserRoleListForKeys(@Param("roleKeys") List<String> roleKeys);
}
