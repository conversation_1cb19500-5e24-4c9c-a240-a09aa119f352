package cn.casair.repository;

import cn.casair.domain.HrBillInvoice;
import cn.casair.dto.HrBillInvoiceDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 开票申请数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Repository
public interface HrBillInvoiceRepository extends BaseMapper<HrBillInvoice> {

    /**
     * 根据筛选条件查询开票申请
     * @param page
     * @param hrBillInvoiceDTO
     * @param clientIds
     * @return
     */
    IPage<HrBillInvoiceDTO> findPage(Page<HrBillInvoice> page, @Param("params") HrBillInvoiceDTO hrBillInvoiceDTO, @Param("clientIds") List<String> clientIds);

    /**
     * 查询开票申请的详情，带申请人和客户名称
     * @param id
     * @return
     */
    HrBillInvoiceDTO getHrBillInvoice(@Param("id") String id);

    /**
     * 更新开票审批流程
     * @param feeReviewId
     * @param approveStatus
     */
    void updateApproveStatus(@Param("feeReviewId") String feeReviewId, @Param("approveStatus") Integer approveStatus, @Param("invoiceLockState") Integer invoiceLockState);

    /**
     * 查询多个开票申请的详情
     * @param ids
     * @return
     */
    List<HrBillInvoiceDTO> getBillInvoiceByIds(@Param("ids") List<String> ids);

    /**
     * 查询子级开票信息
     * @param parentId
     * @param neApproveStatusList
     * @return
     */
    List<HrBillInvoiceDTO> selectInfoList(@Param("parentId") String parentId, @Param("neApproveStatusList") List<Integer> neApproveStatusList);

    /**
     * 修改可开发票锁定状态
     * @param id
     * @param invoiceLockState
     */
    void updateInvoiceLockState(@Param("id") String id, @Param("invoiceLockState") Integer invoiceLockState);

    /**
     * 查询审核通过的开票信息--弃用
     * @param content
     * @param approveStatus
     * @param feeReviewIds
     * @return
     */
    List<HrBillInvoiceDTO> findByBillId(@Param("content") String content, @Param("approveStatus") Integer approveStatus, @Param("feeReviewIds") List<String> feeReviewIds);

    /**
     * 根据结算单ID查询开票信息
     * @param feeReviewIds 结算单ID
     * @return
     */
    List<HrBillInvoiceDTO> findByReviewId(@Param("feeReviewIds") List<String> feeReviewIds, @Param("isDefault") Integer isDefault);

    /**
     * 修改开票锁定以及展示状态
     * @param ids 开票IDS
     * @param invoiceLockState 锁定状态
     * @param isShow 展示状态
     */
    void updateBeforeInvoice(@Param("ids") List<String> ids, @Param("invoiceLockState") Integer invoiceLockState, @Param("isShow") Integer isShow);

    /**
     * 删除凭证
     *
     * @param hrBillInvoiceId
     */
    @Update("update hr_bill_invoice set nc_voucher = null, accounting_voucher_status = 0 where id = #{hrBillInvoiceId}")
     void deleteVoucher(@Param("hrBillInvoiceId") String hrBillInvoiceId);

}
