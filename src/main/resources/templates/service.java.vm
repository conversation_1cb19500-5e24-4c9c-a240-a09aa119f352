package ${package.Service};
import ${package.Entity}.${entity};
import ${cfg.extPackage.Dto}.${entity}DTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import ${superServiceClassPackage};

/**
 * $!{table.comment}服务类
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${kotlin})
interface ${table.serviceName} : ${superServiceClass}<${entity}DTO>
#else
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {

    #foreach($field in ${table.fields})
        #if(${field.keyFlag})
            #set($keyPropertyType=${field.propertyType})
        #end
    #end

    /**
     * 创建$!{table.comment}
     * @param ${cfg.entityProperty}DTO
     * @return
     */
    ${entity}DTO create${entity}(${entity}DTO ${cfg.entityProperty}DTO);

    /**
     * 修改$!{table.comment}
     * @param ${cfg.entityProperty}DTO
     * @return
     */
    Optional<${entity}DTO> update${entity}(${entity}DTO ${cfg.entityProperty}DTO);

    /**
     * 查询$!{table.comment}详情
     * @param id
     * @return
     */
    ${entity}DTO get${entity}($!{keyPropertyType} id);

    /**
     * 删除$!{table.comment}
     * @param id
     */
    void delete${entity}($!{keyPropertyType} id);

    /**
     * 批量删除$!{table.comment}
     * @param ids
     */
    void delete${entity}(List<$!{keyPropertyType}> ids);

    /**
     * 分页查询$!{table.comment}
     * @param ${cfg.entityProperty}DTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(${entity}DTO ${cfg.entityProperty}DTO,Long pageNumber,Long pageSize);
    }
#end
