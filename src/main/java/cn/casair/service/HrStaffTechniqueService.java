package cn.casair.service;

import cn.casair.domain.HrStaffTechnique;
import cn.casair.dto.HrStaffTechniqueDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 职业技术能力服务类
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
public interface HrStaffTechniqueService extends IService<HrStaffTechnique> {


    /**
     * 创建职业技术能力
     *
     * @param hrStaffTechniqueDTO
     * @return
     */
    List<HrStaffTechniqueDTO> createHrStaffTechnique(HrStaffTechniqueDTO hrStaffTechniqueDTO);

    /**
     * 修改职业技术能力
     *
     * @param hrStaffTechniqueDTO
     * @return
     */
    Optional<List<HrStaffTechniqueDTO>> updateHrStaffTechnique(HrStaffTechniqueDTO hrStaffTechniqueDTO);

    /**
     * 查询职业技术能力详情
     *
     * @param id
     * @return
     */
    HrStaffTechniqueDTO getHrStaffTechnique(String id);

    /**
     * 删除职业技术能力
     *
     * @param id
     */
    List<HrStaffTechniqueDTO> deleteHrStaffTechnique(String id);

    /**
     * 查询职业技术能力
     * @param staffId 员工ID
     * @return
     */
    List<HrStaffTechniqueDTO> findTechniqueList(String staffId);

    void setDict(HrStaffTechniqueDTO dto);
}
