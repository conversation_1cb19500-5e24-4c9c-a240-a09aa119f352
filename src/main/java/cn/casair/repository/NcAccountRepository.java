package cn.casair.repository;

import cn.casair.domain.NcAccount;
import cn.casair.dto.NcAccountDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用友NC会计科目及对应的辅助数据库操作类
 *
 * <AUTHOR>
 * @since 2022-10-19
 */
@Repository
public interface NcAccountRepository extends BaseMapper<NcAccount> {

    /**
     * 分页查询
     *
     * @param page
     * @param ncAccountDTO
     * @return
     */
    IPage<NcAccountDTO> findPage(Page page, @Param("param") NcAccountDTO ncAccountDTO);

    /**
     * 通过发票内容 查询科目
     *
     * @return
     */
    @Select("select na.* from nc_account na where na.is_delete = 0 and na.type = #{type} and na.content = #{content} and na.debt = #{debt}")
    List<NcAccountDTO> findByTypeAndContentAndDebt(@Param("type") Integer type, @Param("content") String content, @Param("debt") String debt);
}
