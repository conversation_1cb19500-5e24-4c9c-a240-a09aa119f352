package cn.casair.service;

import cn.casair.domain.HrApplyEntryStaff;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrApplyEntryStaffDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 申请入职员工服务类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface HrApplyEntryStaffService extends IService<HrApplyEntryStaff> {


    /**
     * 创建申请入职员工
     *
     * @param hrApplyEntryStaffDTO
     * @return
     */
    HrApplyEntryStaffDTO createHrApplyEntryStaff(HrApplyEntryStaffDTO hrApplyEntryStaffDTO);

    /**
     * 查询申请入职员工详情
     *
     * @param id 申请ID
     * @return
     */
    HrApplyEntryStaffDTO getHrApplyEntryStaff(String id);

    /**
     * 批量删除申请入职员工
     *
     * @param ids
     */
    void deleteHrApplyEntryStaff(List<String> ids);

    /**
     * 分页查询申请入职员工
     *
     * @param hrApplyEntryStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrApplyEntryStaffDTO hrApplyEntryStaffDTO, Long pageNumber, Long pageSize);

    /**
     * 审批拒绝
     * @param batchOptDTO
     * @return
     */
    ResponseEntity approveReject(BatchOptDTO batchOptDTO);

    /**
     * 审批通过
     * @param batchOptDTO
     * @return
     */
    ResponseEntity approvePassed(BatchOptDTO batchOptDTO);

    /**
     * 入职流程
     * @param applyStaffIdS 待入职员工ID
     * @return
     */
    ResponseEntity entryLevel(List<String> applyStaffIdS);

    /**
     * 统计每月入职数量
     * @param particularYear 年份
     * @return
     */
    List<Map<String, Object>> entryCountByYear(String particularYear);

    /**
     * 入职资料-提交入职资料
     * @param id
     */
    ResponseEntity submitStaffBasicInfo(String id);

    /**
     * 完善信息流程
     * @param applyStaffId 待入职员工ID
     * @return
     */
    ResponseEntity completeInForApplyStaff(String applyStaffId);

    /**
     * 确认信息流程
     * @param batchOptDTO
     * @return
     */
    ResponseEntity notarizeInForApplyStaff(BatchOptDTO batchOptDTO);


    /**
     * 查询申请信息对应的待入职员工信息
     * @param applyId 申请ID
     * @param pendingReview 待审批
     * @param applyStaffId 待审批员工ID
     * @return
     */
    List<HrApplyEntryStaffDTO> selectHrApplyEntryStaffList(String applyId,Integer pendingReview,List<String> applyStaffId);

    /**
     * 导出失败员工列表
     * @param applyId
     * @param response
     * @return
     */
    String exportFailureStaff(String applyId, HttpServletResponse response);

    /**
     * 导出待入职员工列表
     * @param hrApplyEntryStaffDTO 待入职员工ID
     * @param response
     * @return
     */
    String exportToHiredStaff(HrApplyEntryStaffDTO hrApplyEntryStaffDTO, HttpServletResponse response);
}
