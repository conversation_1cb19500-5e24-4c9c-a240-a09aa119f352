<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrTalentStaffRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,client_id,protocol_id,system_num,`name`,nationality,nation,certificate_type,certificate_num,birthday,sex,phone,native_place,height,email,marital_status,highest_education,household_registration,domicile_place,
        census_register_address,census_register_postcode,politics_status,party_date,party_branch,contact_address,contact_postcode,iz_military,military_start_date,military_end_date,rewards_punishment,remark,staff_status,
        personnel_type,signature_status,work_status,nick_name,`status`,avatar,alias_name,`password`,apply_staff_id,iz_default,appendix_ids,iz_start_end,iz_preserve,iz_insured,open_id,departure_staff_id,resignation_date,
        iz_retire,medical_record_date,renewal_service_id,renewal_process,is_renewal_contract,first_login_sign,secondment_id,supplementary_payment,
        is_delete,created_by,created_date,last_modified_by,last_modified_date
    </sql>

    <sql id="findSQl">
        SELECT
            hts.*, hc.client_name, hc.unit_number, hs.profession_name,hswe.id AS jobId, hswe.dept_name,
            hswe.station_id, hswe.salary_section, hswe.work_nature,hswe.work_location,hswe.board_date
        FROM
            hr_talent_staff hts
            LEFT JOIN hr_staff_work_experience hswe ON hswe.staff_id = hts.id AND hts.client_id = hswe.client_id
        AND hswe.is_delete = 0
        AND hswe.iz_default = 1
            LEFT JOIN hr_client hc ON hts.client_id = hc.id
        AND hc.is_delete = 0
            LEFT JOIN hr_station hs ON hswe.station_id = hs.id
    </sql>

    <sql id="Select_SQL">
        <if test="param.systemNum != null and param.systemNum != ''">
            AND a.system_num LIKE concat('%', #{param.systemNum}, '%')
        </if>
        <if test="param.name != null and param.name != ''">
            AND a.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.clientPay != null">
            AND b.client_pay = #{param.clientPay}
        </if>
        <if test="param.sex != null">
            AND a.sex = #{param.sex}
        </if>
        <if test="param.birthday != null">
            AND a.birthday = #{param.birthday}
        </if>
        <if test="param.highestEducation != null">
            AND a.highest_education = #{param.highestEducation}
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND a.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.contactAddress != null and param.contactAddress != ''">
            AND a.contact_address LIKE concat('%', #{param.contactAddress}, '%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND a.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.staffStatus != null">
            AND a.staff_status = #{param.staffStatus}
        </if>
        <if test="param.workStatus != null">
            AND a.work_status = #{param.workStatus}
        </if>
        <if test="param.personnelType != null">
            AND a.personnel_type = #{param.personnelType}
        </if>
        <if test="param.izInsured != null">
            AND a.iz_insured = #{param.izInsured}
        </if>
        <if test="param.clientId != null">
            AND a.client_id = #{param.clientId}
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND a.client_id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.contractStartDateStart!=null">
            AND c.contract_start_date >= #{param.contractStartDateStart}
        </if>
        <if test="param.contractStartDateEnd!=null">
            AND c.contract_start_date &lt; date_add(#{param.contractStartDateEnd}, interval 1 day)
        </if>
        <if test="param.contractEndDateStart!=null">
            AND c.contract_end_date &gt;= #{param.contractEndDateStart}
        </if>
        <if test="param.contractEndDateEnd!=null">
            AND c.contract_end_date &lt; date_add(#{param.contractEndDateEnd}, interval 1 day)
        </if>
        <if test="param.ids != null and param.ids.size() > 0">
            AND a.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="param.staffIds != null and param.staffIds.size() > 0">
            AND a.id IN
            <foreach collection="param.staffIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="param.entryExitPage != null and param.entryExitPage != ''">
            AND a.staff_status in (4,5,3,9)
        </if>
        <if test="param.clientName != null and param.clientName != ''">
            AND hc.client_name LIKE concat('%', #{param.clientName}, '%')
        </if>
        <if test="param.boardDateStart != null and param.boardDateStart !=''">
            AND DATE_FORMAT(f.board_date,'%Y-%m') &gt;= #{param.boardDateStart}
        </if>
        <if test="param.boardDateEnd != null and param.boardDateEnd != null">
            AND DATE_FORMAT(f.board_date,'%Y-%m') &lt;=  #{param.boardDateEnd}
        </if>
        <if test="param.departureDateStart != null and param.departureDateStart !=''">
            AND DATE_FORMAT(f.departure_date,'%Y-%m') &gt;= #{param.departureDateStart}
        </if>
        <if test="param.departureDateEnd != null and param.departureDateEnd != null">
            AND DATE_FORMAT(f.departure_date,'%Y-%m') &lt;=  #{param.departureDateEnd}
        </if>
        <if test="param.staffStatusList!=null and param.staffStatusList.size() > 0">
            AND a.staff_status IN
            <foreach collection="param.staffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.notStaffStatusList!=null and param.notStaffStatusList.size() > 0">
            AND a.staff_status NOT IN
            <foreach collection="param.notStaffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.personnelTypeList!=null and param.personnelTypeList.size() > 0">
            AND a.personnel_type IN
            <foreach collection="param.personnelTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.sexList!=null and param.sexList.size() > 0">
            AND a.sex IN
            <foreach collection="param.sexList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.highestEducationList!=null and param.highestEducationList.size() > 0">
            AND a.highest_education IN
            <foreach collection="param.highestEducationList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.workStatusList!=null and param.workStatusList.size() > 0">
            AND a.work_status IN
            <foreach collection="param.workStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.archivesNum != null and param.archivesNum != ''">
            AND ham.archives_num LIKE concat('%', #{param.archivesNum}, '%')
        </if>
        <if test="param.renewalProcessList!=null and param.renewalProcessList.size() > 0">
            AND a.renewal_process IN
            <foreach collection="param.renewalProcessList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <choose>
            <when test="param.renewalProcess != null">
                <choose>
                    <when test="param.renewalProcess == 0">
                        AND a.renewal_process IS NULL
                    </when>
                    <otherwise>
                        AND a.renewal_process = #{param.renewalProcess}
                    </otherwise>
                </choose>
            </when>
        </choose>
        <if test="param.contractState != null">
            AND c.state = #{param.contractState}
        </if>
        <if test="param.ruleDay != null">
            AND ABS(datediff(c.contract_end_date, now())) &lt;= #{param.ruleDay}
        </if>
        <if test="param.experience != null">
            AND f.board_date IS NULL
        </if>
        <choose>
            <when test="param.renewalSearch != null">
                <choose>
                    <when test="param.renewalSearch == 3">
                        AND c.state IN (2,3) AND a.renewal_process IN (4) AND a.is_renewal_contract = 0
                    </when>
                    <when test="param.renewalSearch == 2">
                        AND c.state IN (2,3) AND a.renewal_process IN (1,2)
                    </when>
                    <when test="param.renewalSearch == 1">
                        AND c.state IN (2,3) AND (a.renewal_process IN (3) OR a.renewal_process is null)
                    </when>
                </choose>
            </when>
        </choose>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            order by  ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY a.created_date DESC
        </if>
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrTalentStaff">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="protocol_id" property="protocolId"/>
        <result column="system_num" property="systemNum"/>
        <result column="name" property="name"/>
        <result column="nationality" property="nationality"/>
        <result column="nation" property="nation"/>
        <result column="certificate_type" property="certificateType"/>
        <result column="certificate_num" property="certificateNum"/>
        <result column="birthday" property="birthday"/>
        <result column="sex" property="sex"/>
        <result column="phone" property="phone"/>
        <result column="native_place" property="nativePlace"/>
        <result column="height" property="height"/>
        <result column="email" property="email"/>
        <result column="marital_status" property="maritalStatus"/>
        <result column="highest_education" property="highestEducation"/>
        <result column="household_registration" property="householdRegistration"/>
        <result column="domicile_place" property="domicilePlace"/>
        <result column="census_register_address" property="censusRegisterAddress"/>
        <result column="census_register_postcode" property="censusRegisterPostcode"/>
        <result column="politics_status" property="politicsStatus"/>
        <result column="party_date" property="partyDate"/>
        <result column="party_branch" property="partyBranch"/>
        <result column="contact_address" property="contactAddress"/>
        <result column="contact_postcode" property="contactPostcode"/>
        <result column="iz_military" property="izMilitary"/>
        <result column="military_start_date" property="militaryStartDate"/>
        <result column="military_end_date" property="militaryEndDate"/>
        <result column="rewards_punishment" property="rewardsPunishment"/>
        <result column="remark" property="remark"/>
        <result column="staff_status" property="staffStatus"/>
        <result column="personnel_type" property="personnelType"/>
        <result column="work_status" property="workStatus"/>
        <result column="nick_name" property="nickName"/>
        <result column="status" property="status"/>
        <result column="avatar" property="avatar"/>
        <result column="alias_name" property="aliasName"/>
        <result column="password" property="password"/>
        <result column="iz_default" property="izDefault"/>
        <result column="appendix_ids" property="appendixIds"/>
        <result column="iz_start_end" property="izStartEnd"/>
        <result column="iz_preserve" property="izPreserve"/>
        <result column="iz_insured" property="izInsured"/>
        <result column="apply_staff_id" property="applyStaffId"/>
        <result column="open_id" property="openId"/>
        <result column="resignation_date" property="resignationDate"/>
        <result column="departure_staff_id" property="departureStaffId"/>
        <result column="renewal_process" property="renewalProcess"/>
        <result column="first_login_sign" property="firstLoginSign"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByClientId" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT hts.id,
               hts.client_id,
               hts.staff_status,
               hswe.departure_date resignationDate,
               hts.iz_insured,
               hr.actual_retire_date
        FROM hr_talent_staff hts
        LEFT JOIN hr_client hc ON hc.id = hts.client_id AND hc.is_delete = 0
        LEFT JOIN hr_retire hr ON hr.staff_id = hts.id  AND hr.is_delete = 0 AND hr.`status` = 3
        LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id AND (CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END) AND hswe.is_delete = 0
        WHERE hts.is_delete = 0
          AND hts.iz_default = 0
          AND hc.id IN
          <foreach collection="clientIds" open="(" separator="," close=")" item="i">
              #{i}
          </foreach>
    </select>

    <select id="selectExportListByIds" resultType="cn.casair.dto.HrEmployeeWelfareDTO">
        SELECT
               hts.id,
               hc.unit_number,
               hc.client_name,
               hcp.client_name parentClientName,
               hts.system_num,
               hts.`name`,
               hts.sex,
               hts.birthday,
               TIMESTAMPDIFF(YEAR, hts.birthday, CURDATE()) age,
               hts.certificate_num,
               hts.phone,
               hts.personnel_type,
               hts.staff_status,
               hts.iz_insured,
               hse.basic_wage,
               hse.salary,
               hse.owned_bank,
               hse.salary_card_num,
               hse.social_security_num,
               hse.social_security_cardinal,
               hse.medical_insurance_num,
               hse.medical_insurance_cardinal,
               hse.accumulation_fund_num,
               hse.accumulation_fund_cardinal,
               hse.social_security_cardinal_personal,
               hse.medical_insurance_cardinal_personal,
               hse.seniority_wage_base,
               hse.seniority_pay,
               hcc.contract_start_date,
               CONCAT( hse.pay_year, '-', hse.pay_monthly ) paymentDate,
               hss.social_security_name,
               hss.area socialSecurityArea,
               hss.name_of_beneficiary,
               hss.receiving_account,
               hss.account_bank,
               hss.unit_pension,
               hss.unit_medical,
               hss.work_injury,
               hss.unit_unemployment,
               hss.personal_pension,
               hss.personal_medical,
               hss.personal_unemployment,
               haf.type_name,
               haf.area accumulationArea,
               haf.payee_name,
               haf.payee_account,
               haf.payee_bank,
               haf.unit_scale,
               haf.personage_scale,
               hss.unit_maternity,
               hss.personal_maternity,
               hse.unit_pension_cardinal,
               hse.unit_unemployment_cardinal,
               hse.unit_maternity_cardinal,
               hse.work_injury_cardinal,
               hse.unit_large_medical_expense,
               hse.replenish_work_injury_expense,
               hse.personal_pension_cardinal,
               hse.personal_unemployment_cardinal,
               hse.personal_maternity_cardinal,
               hse.personal_large_medical_expense,
               hse.unit_enterprise_annuity,
               hse.personal_enterprise_annuity,
               hse.commercial_insurance
        FROM hr_talent_staff hts
            LEFT JOIN hr_client hc ON hc.id = hts.client_id
            AND hc.is_delete = 0
#             AND hc.`status` = 0
            LEFT JOIN hr_client hcp ON hcp.id = hc.parent_id
            AND hcp.is_delete = 0
#             AND hcp.`status` = 0
            LEFT JOIN hr_social_security hss ON hss.id = hc.social_security_type_id
            AND hss.is_delete = 0
            LEFT JOIN hr_accumulation_fund haf ON haf.id = hc.provident_fund_type_id
            AND haf.is_delete = 0
            LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hts.id
            AND hse.is_delete = 0
            LEFT JOIN hr_contract hcc ON hcc.staff_id = hts.id
            AND hcc.client_id = hts.client_id
            AND hcc.state IN ( 1, 2 )
            AND hcc.is_delete = 0
        WHERE hts.is_delete = 0
          AND hts.iz_default = 0
#           AND hts.`status` = 0
        <if test="params.ids != null and params.ids.size() > 0">
            AND hts.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="permissionClient!=null and permissionClient.size>0">
            AND hts.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientIds!=null and params.clientIds.size>0">
            AND hts.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.name!=null and params.name!=''">
            AND hts.`name` LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.certificateNum!=null and params.certificateNum!=''">
            AND hts.certificate_num LIKE CONCAT('%',#{params.certificateNum},'%')
        </if>
        <if test="params.personnelType!=null">
            AND hts.personnel_type = #{params.personnelType}
        </if>
        <if test="params.staffStatusS!=null and params.staffStatusS.size>0">
            AND hts.staff_status IN
            <foreach collection="params.staffStatusS" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.socialSecurityIds!=null and params.socialSecurityIds.size>0">
            AND hss.id IN
            <foreach collection="params.socialSecurityIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.accumulationFundIds!=null and params.accumulationFundIds.size>0">
            AND haf.id IN
            <foreach collection="params.accumulationFundIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.socialSecurityCardinal!=null">
            AND hse.social_security_cardinal = #{params.socialSecurityCardinal}
        </if>
        <if test="params.medicalInsuranceCardinal!=null">
            AND hse.medical_insurance_cardinal = #{params.medicalInsuranceCardinal}
        </if>
        <if test="params.accumulationFundCardinal!=null">
            AND hse.accumulation_fund_cardinal = #{params.accumulationFundCardinal}
        </if>
        <if test="params.payMonthly!=null and params.payMonthly!=''">
            AND hse.pay_monthly = #{params.payMonthly}
        </if>
        <if test="params.izInsureds!=null and params.izInsureds.size>0">
            AND hts.iz_insured IN
            <foreach collection="params.izInsureds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='personnel_type_str'">
                        ORDER BY hts.personnel_type ${params.order}
                    </when>
                    <when test="params.field=='staff_status_str'">
                        ORDER BY hts.staff_status ${params.order}
                    </when>
                    <when test="params.field=='iz_insured_str'">
                        ORDER BY hts.iz_insured ${params.order}
                    </when>
                    <when test="params.field=='payment_date'">
                        ORDER BY  hse.pay_year ${params.order},
                        hse.pay_monthly ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY hts.created_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="findEmployeeWelfareByStaffId" resultType="cn.casair.dto.HrEmployeeWelfareDTO">
        SELECT
            hts.id,
            hts.client_id,
            hts.certificate_num,
--             hse.social_security_cardinal,
            hse.basic_wage,
            hse.salary,
            hse.owned_bank,
            hse.salary_card_num,
            hse.social_security_num,
            hse.medical_insurance_num,
            hse.accumulation_fund_num,
            hse.unit_pension_cardinal,
            hse.unit_unemployment_cardinal,
            hse.unit_maternity_cardinal,
            hse.work_injury_cardinal,
            hse.medical_insurance_cardinal,
--             hse.social_security_cardinal_personal,
            hse.personal_pension_cardinal,
            hse.personal_unemployment_cardinal,
            hse.medical_insurance_cardinal_personal,
            hse.accumulation_fund_cardinal,
            hse.seniority_wage_base,
            hse.seniority_pay,
            hse.unit_large_medical_expense,
            hse.replenish_work_injury_expense,
            hse.personal_large_medical_expense,
            hse.pay_year,
            hse.pay_monthly,
            hse.personal_maternity_cardinal,
            hse.unit_enterprise_annuity,
            hse.personal_enterprise_annuity,
            hse.commercial_insurance,
            hss.personal_maternity,
            hss.social_security_name,
            hss.area socialSecurityArea,
            hss.name_of_beneficiary,
            hss.receiving_account,
            hss.account_bank,
            hss.unit_pension,
            hss.unit_medical,
            hss.work_injury,
            hss.unit_unemployment,
            hss.unit_maternity,
            hss.personal_pension,
            hss.personal_medical,
            hss.personal_unemployment,
            haf.type_name,
            haf.area accumulationArea,
            haf.payee_name,
            haf.payee_account,
            haf.payee_bank,
            haf.unit_scale,
            haf.personage_scale
        FROM
            hr_talent_staff hts
                LEFT JOIN hr_client hc ON hc.id = hts.client_id
                AND hc.is_delete = 0
                LEFT JOIN hr_social_security hss ON hss.id = hc.social_security_type_id
                AND hss.is_delete = 0
                LEFT JOIN hr_accumulation_fund haf ON haf.id = hc.provident_fund_type_id
                AND haf.is_delete = 0
                LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hts.id
                AND hse.is_delete = 0
#                 LEFT JOIN hr_contract hcc ON hcc.staff_id = hts.id
#                 AND hcc.client_id = hts.client_id
#                 AND hcc.state IN ( 1, 2 )
#                 AND hcc.is_delete = 0
        WHERE
            hts.is_delete = 0
          AND hts.iz_default = 0
          AND hts.id = #{id}
    </select>

    <sql id="Find_Employee_Welfare">
        SELECT hts.id,
            hts.client_id,
            hc.unit_number,
            hc.client_name,
            hcp.client_name parentClientName,
            hts.system_num,
            hts.`name`,
            hts.sex,
            hts.birthday,
            TIMESTAMPDIFF(YEAR, hts.birthday, CURDATE()) age,
            hts.certificate_num,
            hts.phone,
            hts.personnel_type,
            hts.staff_status,
            hts.supplementary_payment,
            hts.iz_insured,
            hse.basic_wage,
            hse.salary,
            hse.owned_bank,
            hse.salary_card_num,
            hse.social_security_num,
            --             hse.social_security_cardinal,
            hse.medical_insurance_num,
            hse.medical_insurance_cardinal,
            hse.unit_pension_cardinal,
            hse.unit_unemployment_cardinal,
            hse.unit_maternity_cardinal,
            hse.work_injury_cardinal,
            hse.accumulation_fund_num,
            hse.accumulation_fund_cardinal,
            --             hse.social_security_cardinal_personal,
            hss.id  socialSecurityTypeId,
            hse.personal_pension_cardinal,
            hse.personal_unemployment_cardinal,
            hse.medical_insurance_cardinal_personal,
            hse.seniority_wage_base,
            hse.seniority_pay,
            hse.unit_large_medical_expense,
            hse.replenish_work_injury_expense,
            hse.personal_large_medical_expense,
            hse.pay_year,
            hse.pay_monthly,
            hse.personal_maternity_cardinal,
            hse.unit_enterprise_annuity,
            hse.personal_enterprise_annuity,
            hse.commercial_insurance,
            hss.personal_maternity,
            hss.unit_maternity,
            hss.social_security_name,
            hss.area socialSecurityArea,
            hss.name_of_beneficiary,
            hss.receiving_account,
            hss.account_bank,
            hss.unit_pension,
            hss.unit_medical,
            hss.work_injury,
            hss.unit_unemployment,
            hss.personal_pension,
            hss.personal_medical,
            hss.personal_unemployment,
            haf.type_name,
            haf.area accumulationArea,
            haf.payee_name,
            haf.payee_account,
            haf.payee_bank,
            haf.unit_scale,
            haf.personage_scale
        FROM hr_talent_staff hts
        LEFT JOIN hr_client hc ON hc.id = hts.client_id AND hc.is_delete = 0
        LEFT JOIN hr_client hcp ON hcp.id = hc.parent_id AND hcp.is_delete = 0
        LEFT JOIN hr_social_security hss ON hss.id = hc.social_security_type_id AND hss.is_delete = 0
        LEFT JOIN hr_accumulation_fund haf ON haf.id = hc.provident_fund_type_id AND haf.is_delete = 0
        LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hts.id AND hse.is_delete = 0
        WHERE hts.is_delete = 0 AND hts.iz_default = 0
        <if test="permissionClient!=null and permissionClient.size>0">
            AND hts.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientIds!=null and params.clientIds.size>0">
            AND hts.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.staffIds!=null and params.staffIds.size>0">
            AND hts.id IN
            <foreach collection="params.staffIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.name!=null and params.name!=''">
            AND hts.`name` LIKE CONCAT('%',#{params.name},'%')
        </if>
        <if test="params.certificateNum!=null and params.certificateNum!=''">
            AND hts.certificate_num LIKE CONCAT('%',#{params.certificateNum},'%')
        </if>
        <if test="params.personnelType!=null">
            AND hts.personnel_type = #{params.personnelType}
        </if>
        <if test="params.staffStatusS!=null and params.staffStatusS.size>0">
            AND hts.staff_status IN
            <foreach collection="params.staffStatusS" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.socialSecurityIds!=null and params.socialSecurityIds.size>0">
            AND hss.id IN
            <foreach collection="params.socialSecurityIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.accumulationFundIds!=null and params.accumulationFundIds.size>0">
            AND haf.id IN
            <foreach collection="params.accumulationFundIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.socialSecurityCardinalSearch!=null">
            AND hse.social_security_cardinal = #{params.socialSecurityCardinalSearch}
        </if>
        <if test="params.medicalInsuranceCardinalSearch!=null">
            AND hse.medical_insurance_cardinal = #{params.medicalInsuranceCardinalSearch}
        </if>
        <if test="params.socialSecurityCardinalPersonalSearch!=null">
            AND hse.social_security_cardinal_personal = #{params.socialSecurityCardinalPersonalSearch}
        </if>
        <if test="params.medicalInsuranceCardinalPersonalSearch!=null">
            AND hse.medical_insurance_cardinal_personal = #{params.medicalInsuranceCardinalPersonalSearch}
        </if>
        <if test="params.accumulationFundCardinalSearch!=null">
            AND hse.accumulation_fund_cardinal = #{params.accumulationFundCardinalSearch}
        </if>
        <if test="params.unitPensionCardinalSearch!=null">
            AND hse.unit_pension_cardinal = #{params.unitPensionCardinalSearch}
        </if>
        <if test="params.unitUnemploymentCardinalSearch!=null">
            AND hse.unit_unemployment_cardinal = #{params.unitUnemploymentCardinalSearch}
        </if>
        <if test="params.unitMaternityCardinalSearch!=null">
            AND hse.unit_maternity_cardinal = #{params.unitMaternityCardinalSearch}
        </if>
        <if test="params.workInjuryCardinalSearch!=null">
            AND hse.work_injury_cardinal = #{params.workInjuryCardinalSearch}
        </if>
        <if test="params.personalPensionCardinalSearch!=null">
            AND hse.personal_pension_cardinal = #{params.personalPensionCardinalSearch}
        </if>
        <if test="params.personalUnemploymentCardinalSearch!=null">
            AND hse.personal_unemployment_cardinal = #{params.personalUnemploymentCardinalSearch}
        </if>
        <if test="params.personalMaternityCardinalSearch!=null">
            AND hse.personal_maternity_cardinal = #{params.personalMaternityCardinalSearch}
        </if>
        <if test="params.payYearSearch!=null and params.payYearSearch!=''">
            AND hse.pay_year = #{params.payYearSearch}
        </if>
        <if test="params.payMonthlySearch!=null and params.payMonthlySearch!=''">
            AND hse.pay_monthly = #{params.payMonthlySearch}
        </if>
        <if test="params.izInsureds!=null and params.izInsureds.size>0">
            AND hts.iz_insured IN
            <foreach collection="params.izInsureds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.socialSecurityTypeId!=null and params.socialSecurityTypeId!=''">
            AND hss.id = #{params.socialSecurityTypeId}
        </if>
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='personnel_type_str'">
                        ORDER BY hts.personnel_type ${params.order}
                    </when>
                    <when test="params.field=='staff_status_str'">
                        ORDER BY hts.staff_status ${params.order}
                    </when>
                    <when test="params.field=='iz_insured_str'">
                        ORDER BY hts.iz_insured ${params.order}
                    </when>
                    <when test="params.field=='payment_date'">
                        ORDER BY  hse.pay_year ${params.order},
                        hse.pay_monthly ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY hts.created_date DESC
            </otherwise>
        </choose>
    </sql>

    <select id="findEmployeeWelfarePage" resultType="cn.casair.dto.HrEmployeeWelfareDTO">
        <include refid="Find_Employee_Welfare"/>
    </select>

    <select id="findEmployeeWelfareList" resultType="cn.casair.dto.HrEmployeeWelfareDTO">
        <include refid="Find_Employee_Welfare"/>
    </select>

    <update id="updateStaffIzStartEnd">
        UPDATE hr_talent_staff
        SET iz_start_end = #{state}
        WHERE id = #{staffId}
    </update>

    <update id="updateStaffStatus">
        UPDATE hr_talent_staff
        SET staff_status = #{staffStatus}
        WHERE id = #{staffId}
    </update>

    <update id="staffInductionCompletion">
        UPDATE hr_talent_staff
        SET staff_status = #{staffStatus},
            iz_start_end = #{izStartEnd}
        WHERE is_delete = 0
        AND id = #{staffId}
    </update>

    <select id="domicilePlaceList" resultType="java.lang.String">
        SELECT DISTINCT
            domicile_place
        FROM
            hr_talent_staff
        WHERE
            is_delete = 0
    </select>

    <select id="selectSignInfoByStaffId" resultType="cn.casair.dto.pdf.ContractInfoForPdf">
        SELECT swe.staff_id,
               swe.client_id,
               hts.`name`                   secondPartName,
               IF(hts.sex = 1, '男', '女')   secondPartSex,
               hts.certificate_num          secondPartIdNo,
               hts.birthday                 birthday,
               hts.census_register_address  secondPartHometown,
               hts.contact_address          secondPartAddress,
               hts.phone                    secondPartPhone,
               hc.client_name               employerName,
               hs.profession_name           postName,
               swe.work_location            workPlace,
               hse.basic_wage               salary,
               hc.pay_date                  payday,
               hts.contact_postcode         secondPartZipCode,
               aes.internship_date
        FROM hr_talent_staff hts
            LEFT JOIN hr_staff_work_experience swe ON swe.staff_id = hts.id
            AND swe.is_delete = 0
            AND swe.iz_default = 1
            LEFT JOIN hr_station hs ON hs.id = swe.station_id
            AND hs.is_delete = 0
            LEFT JOIN hr_staff_emolument hse ON hse.staff_id = swe.staff_id
            AND hse.is_delete = 0
            LEFT JOIN hr_client hc ON hc.id = swe.client_id
            AND hc.is_delete = 0
            LEFT JOIN hr_apply_entry_staff aes ON aes.staff_id = hts.id
            AND aes.client_id = hts.client_id
            AND aes.is_delete = 0  AND aes.entry_status <![CDATA[ < ]]> 7
        WHERE hts.iz_default = 0
          AND hts.id = #{staffId}
    </select>

    <select id="selectBySystemNum" resultType="cn.casair.domain.HrTalentStaff">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_talent_staff
        WHERE is_delete = 0
        AND system_num = #{systemNum}
    </select>

    <select id="getStaffNameList" resultType="cn.casair.domain.HrTalentStaff">
        SELECT id,
               `name`
        FROM hr_talent_staff
        WHERE is_delete = 0
          AND iz_default = 0
        ORDER BY created_date DESC
    </select>

    <select id="findPage" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            a.*,
            IF (a.sex = 1, '男', '女') sexlabel,
            TIMESTAMPDIFF(YEAR, a.birthday, CURDATE()) age,
            b.unit_number unitNumber,
            b.client_name client_name,
            c.contract_start_date contract_start_date,
            c.contract_end_date contract_end_date,
            c.state contractState,
            hss.old_client_id AS oldClientId,
            hss.is_default AS secondmentIsDefault,
            hss.staff_status AS secondmentStaffStatus,
            hss.states AS secondmentStates,
            ham.archives_num archives_num,
            hse.basic_wage,
            f.board_date board_date
        FROM
        hr_talent_staff a
        LEFT JOIN hr_client b ON a.client_id = b.id AND b.is_delete = 0
        LEFT JOIN
        (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
        FROM (SELECT id,client_id,staff_id,state,contract_start_date,contract_end_date,is_delete FROM hr_contract WHERE is_delete = 0 AND state in (1,2,3,5) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) c
        ON c.staff_id = a.id AND c.is_delete = 0
        LEFT JOIN v_staff_work_experience f ON f.staff_id = a.id AND f.is_delete = 0 AND (CASE a.staff_status WHEN 5 THEN f.iz_default = 0 AND f.client_id IS NOT NULL ELSE f.iz_default = 1 END )
        LEFT JOIN hr_staff_secondment hss ON hss.id = a.secondment_id AND hss.is_delete = 0
        LEFT JOIN hr_archives_manage ham ON a.id = ham.staff_id AND ham.is_delete = 0
        LEFT JOIN hr_staff_emolument hse ON hse.staff_id = a.id AND hse.is_delete = 0
        WHERE
            a.is_delete = 0 AND a.iz_default = #{param.izDefault}
        <include refid="Select_SQL"/>
    </select>

    <select id="findHrStaff" resultType="cn.casair.dto.excel.HrStaffExport">
        SELECT
            b.unit_number,
            b.client_name,
            a.*,
            hse.*,
            f.station_id,
            hs.profession_name,
            f.dept_name,
            f.board_date,
            IF(f.departure_date IS NOT NULL,f.departure_date,e.departure_date) departure_date,
            IF(e.departure_reason IS NOT NULL,e.departure_reason,e.return_reason) reason
        FROM
            hr_talent_staff a
        LEFT JOIN hr_client b ON a.client_id = b.id AND b.is_delete = 0
        LEFT JOIN
        (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
        FROM (SELECT id,client_id,staff_id,state,contract_start_date,contract_end_date,is_delete FROM hr_contract WHERE is_delete = 0 AND state in (1,2,3) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) c
        ON c.staff_id = a.id AND c.is_delete = 0
        LEFT JOIN hr_staff_emolument hse ON hse.staff_id = a.id AND hse.is_delete = 0
        LEFT JOIN v_staff_work_experience f ON f.staff_id = a.id AND (CASE a.staff_status WHEN 5 THEN f.iz_default = 0 AND f.client_id IS NOT NULL AND a.client_id = f.client_id ELSE f.iz_default = 1 END) AND f.is_delete = 0
        LEFT JOIN hr_station hs ON f.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN hr_apply_departure_staff e ON a.departure_staff_id = e.id AND e.is_delete = 0
        WHERE a.is_delete = 0  AND a.iz_default = 0
        <include refid="Select_SQL"/>
    </select>

    <select id="findHrTalent" resultType="cn.casair.dto.excel.HrTalentExport">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
            hr_talent_staff a
        WHERE a.is_delete = 0 AND a.iz_default = 1
        <include refid="Select_SQL"/>
    </select>

    <select id="getStaffBasicInfo" resultType="cn.casair.dto.HrTalentStaffDTO">
        <include refid="findSQl"/>
        WHERE
            hts.is_delete = 0
        AND hts.iz_default = 0
        AND hts.id = #{id}
    </select>

    <select id="findStaffInfo" resultType="cn.casair.domain.HrTalentStaff">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_talent_staff
        WHERE  is_delete = 0 AND
            certificate_num = #{certificateNum}
        AND (
            staff_status NOT IN (5, 6)
            OR staff_status IS NULL
        )
    </select>

    <select id="findStaffEntryQuit" resultType="cn.casair.dto.excel.HrStaffEntryQuitExport">
        SELECT
        hc.unit_number,hc.client_name,hts.id staffId,hts.system_num,hts.`name`,hts.staff_status,hts.certificate_type,hts.certificate_num,hts.phone,hts.staff_status,
        hts.sex,hts.nationality,hts.birthday,hswe.board_date,hswe.departure_date,
        CASE hts.staff_status WHEN 5 THEN '非正常' ELSE '正常' END as personnelState
        FROM
        hr_talent_staff hts
        LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id AND ( CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END) AND hswe.is_delete = 0
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        WHERE hts.is_delete = 0 AND hts.iz_default = 0 AND hts.staff_status in (4,5)
        <if test="param.ids != null and param.ids.size() > 0">
            AND hts.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hc.id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.name != null and param.name != ''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.staffStatus != null">
            AND hts.staff_status = #{param.staffStatus}
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.clientName != null and param.clientName != ''">
            AND hc.client_name LIKE concat('%', #{param.clientName}, '%')
        </if>
        <if test="param.boardDateStart != null">
            AND DATE_FORMAT(hswe.board_date,'%Y-%m') &gt;= #{param.boardDateStart}
        </if>
        <if test="param.boardDateEnd != null">
            AND DATE_FORMAT(hswe.board_date,'%Y-%m')  &lt;=  #{param.boardDateEnd}
        </if>
        <if test="param.departureDateStart != null">
            AND DATE_FORMAT(hswe.departure_date,'%Y-%m') &gt;= #{param.departureDateStart}
        </if>
        <if test="param.departureDateEnd != null">
            AND DATE_FORMAT(hswe.departure_date,'%Y-%m') &lt;= #{param.departureDateEnd}
        </if>
    </select>

    <select id="selectQuestion" resultType="cn.casair.dto.HrQuestionDTO">
        SELECT
            hq.*,pm.id paper_id,pm.time_limit,test_duration
        FROM
            hr_paper_client pc
                LEFT JOIN hr_paper_management pm on pc.paper_id=pm.id
                LEFT JOIN hr_paper_question pq on  pm.id=pq.paper_id
                LEFT JOIN hr_question hq on pq.question_id=hq.id
        where pc.client_id=#{id} and pq.is_delete=0 and hq.is_delete=0 and pm.is_preset = 1

    </select>

    <select id="searchDataHrStaff" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            hts.*,hs.id stationId,hs.profession_name,hswe.board_date
        FROM
            hr_talent_staff hts
        LEFT JOIN hr_staff_work_experience hswe ON hts.id = hswe.staff_id AND hts.client_id = hswe.client_id AND hswe.is_delete = 0 AND hswe.iz_default = 1
        LEFT JOIN hr_station hs ON hs.id = hswe.station_id AND hs.is_delete = 0
        WHERE hts.is_delete = 0 AND hts.iz_default = 0 and hts.staff_status in (3,4,7,8,9,10,11)
        <if test="param.name != null and param.name !=''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum !=''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hts.client_id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.name == null">
            LIMIT 10
        </if>
    </select>

    <select id="getStaffInfoById" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            hts.id,
            hts.client_id,
            hts.`name`,
            hc.client_name
        FROM
            hr_talent_staff hts
                LEFT JOIN hr_client hc ON hc.id = hts.client_id
                AND hc.is_delete = 0
        WHERE
            hts.is_delete = 0
          AND hts.id = #{staffId}
          AND hts.client_id = #{clientId}
    </select>

    <select id="findStaffStation" resultType="java.lang.String">
        SELECT
        staff_id
        FROM
        hr_staff_station d
        LEFT JOIN hr_station e ON d.station_id = e.id
        AND e.is_delete = 0
        WHERE d.is_delete = 0
        <if test="professionName != null and professionName !=''">
            AND e.profession_name  LIKE concat('%', #{professionName}, '%')
        </if>
    </select>

    <select id="obtainMedicalRecordInfo" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            hts.id,
            hts.`name`,
            hts.certificate_num,
            hts.sex,
            hts.phone,
            hs.id stationId,
            hs.profession_name,
            hse.basic_wage,
            hc.contract_start_date,
            hc.contract_end_date,
            hse.accumulation_fund_cardinal,
            hse.medical_insurance_cardinal,
            hse.social_security_cardinal,
            hts.medical_record_date
        FROM
            hr_talent_staff hts
        LEFT JOIN (
            SELECT
                staff_id,
                MAX(contract_start_date) contract_start_date,
                MAX(contract_end_date) contract_end_date,
                any_value (state) state,
                any_value (is_delete) is_delete
            FROM
                hr_contract
            GROUP BY
                staff_id
        ) hc ON hc.staff_id = hts.id AND hc.is_delete = 0
        LEFT JOIN hr_staff_work_experience hswe ON hswe.staff_id = hts.id AND hts.client_id = hswe.client_id AND hswe.iz_default = 1 AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hswe.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hts.id AND hse.is_delete = 0
        WHERE hts.is_delete = 0 AND hts.id = #{staffId}
    </select>

    <select id="findArchivesBringDetail" resultType="java.lang.String">
        SELECT bring_id from hr_archives_bring_detail
        <where>
            <if test="detailIds!=null and detailIds.size() > 0">
                detail_id IN
                <foreach collection="detailIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectByPhone" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            hts.*,hc.client_name
        FROM hr_talent_staff hts
        LEFT JOIN hr_client hc ON hts.client_id = hc.id AND hc.is_delete = 0
        WHERE hts.is_delete = 0
        and  hts.phone =  #{phone}

    </select>

    <select id="findStationByStaffId" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            DISTINCT hs.profession_name,hts.id
        FROM
            hr_staff_station hss
        LEFT JOIN hr_station hs ON hss.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN hr_talent_staff hts ON hss.staff_id = hts.id
        WHERE hss.is_delete = 0 AND hts.is_delete = 0
        <if test="ids != null and ids.size() > 0">
            AND hts.id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
    </select>

    <select id="entryResignationPage" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            a.*,
            IF (a.sex = 1, '男', '女') sexlabel,
            TIMESTAMPDIFF(YEAR, a.birthday, CURDATE()) age,
            b.unit_number unitNumber,
            b.client_name client_name,
            f.board_date board_date,
            f.departure_date departure_date
        FROM
            hr_talent_staff a
        LEFT JOIN hr_client b ON a.client_id = b.id AND b.is_delete = 0
        LEFT JOIN v_staff_work_experience f ON f.staff_id = a.id AND f.is_delete = 0 AND (CASE a.staff_status WHEN 5 THEN f.iz_default = 0 AND f.client_id IS NOT NULL AND a.client_id = f.client_id ELSE f.iz_default = 1 END )
        WHERE  a.is_delete = 0 AND a.iz_default = #{param.izDefault}
        <if test="param.systemNum != null and param.systemNum != ''">
            AND a.system_num LIKE concat('%', #{param.systemNum}, '%')
        </if>
        <if test="param.name != null and param.name != ''">
            AND a.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.clientPay != null">
            AND b.client_pay = #{param.clientPay}
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND a.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.staffStatus != null">
            AND a.staff_status = #{param.staffStatus}
        </if>
        <if test="param.clientId != null">
            AND a.client_id = #{param.clientId}
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND a.client_id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.ids != null and param.ids.size() > 0">
            AND a.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="param.staffIds != null and param.staffIds.size() > 0">
            AND a.id IN
            <foreach collection="param.staffIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="param.entryExitPage != null and param.entryExitPage != ''">
            AND a.staff_status in (4,5,3,9)
        </if>
        <if test="param.clientName != null and param.clientName != ''">
            AND b.client_name LIKE concat('%', #{param.clientName}, '%')
        </if>
        <if test="param.boardDateStart != null and param.boardDateStart !=''">
            AND DATE_FORMAT(f.board_date,'%Y-%m') &gt;= #{param.boardDateStart}
        </if>
        <if test="param.boardDateEnd != null and param.boardDateEnd != null">
            AND DATE_FORMAT(f.board_date,'%Y-%m') &lt;=  #{param.boardDateEnd}
        </if>
        <if test="param.departureDateStart != null and param.departureDateStart !=''">
            AND DATE_FORMAT(f.departure_date,'%Y-%m') &gt;= #{param.departureDateStart}
        </if>
        <if test="param.departureDateEnd != null and param.departureDateEnd != null">
            AND DATE_FORMAT(f.departure_date,'%Y-%m') &lt;=  #{param.departureDateEnd}
        </if>
        <if test="param.staffStatusList!=null and param.staffStatusList.size() > 0">
            AND a.staff_status IN
            <foreach collection="param.staffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            order by  ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY a.created_date DESC
        </if>
    </select>

    <select id="findSignInfoByStaffId" resultType="cn.casair.dto.pdf.ContractInfoForPdf">
        SELECT swe.staff_id,
               swe.client_id,
               hts.`name`                   secondPartName,
               IF(hts.sex = 1, '男', '女')   secondPartSex,
               IF(hts.sex = 1, '先生', '女士')   `call`,
               hts.birthday                 birthday,
               hts.nation                   secondPartNation,
               IF(hts.marital_status = 1, '未婚', '已婚') secondPartMaritalStatus,
               hts.certificate_num          secondPartIdNo,
               hts.census_register_address  secondPartHometown,
               hts.contact_address          secondPartAddress,
               hts.phone                    secondPartPhone,
               hc.client_name               employerName,
               hs.profession_name           postName,
               swe.work_location            workPlace,
               hse.basic_wage               basicWage,
	           hse.salary                   salary,
               hc.pay_date                  payday,
               hts.contact_postcode         secondPartZipCode,
               hts.highest_education        highestEducation,
               hct.contract_start_date      contractStartDate,
               hct.contract_end_date        contractEndDate
        FROM hr_talent_staff hts
        LEFT JOIN v_staff_work_experience swe ON swe.staff_id = hts.id AND swe.is_delete = 0 AND (CASE hts.staff_status WHEN 5 THEN swe.iz_default = 0 AND swe.client_id IS NOT NULL AND hts.client_id = swe.client_id ELSE swe.iz_default = 1 END )
        LEFT JOIN hr_station hs ON hs.id = swe.station_id AND hs.is_delete = 0
        LEFT JOIN hr_staff_emolument hse ON hse.staff_id = swe.staff_id AND hse.is_delete = 0
        LEFT JOIN hr_client hc ON hc.id = swe.client_id AND hc.is_delete = 0
        LEFT JOIN
             (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete FROM (
             SELECT id,client_id,staff_id,state,contract_start_date,contract_end_date,is_delete FROM hr_contract WHERE is_delete = 0 AND state in (1,2,3,5) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id)
         hct ON hct.staff_id = hts.id AND hct.is_delete = 0
        WHERE hts.iz_default = 0 AND hts.id = #{id}
    </select>

    <select id="findNotPageHrTalentStaff" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            a.*,
            b.unit_number,
            b.client_name,
            c.state contractState
        FROM hr_talent_staff a
        LEFT JOIN hr_client b ON a.client_id = b.id AND b.is_delete = 0
        LEFT JOIN
        (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
        FROM (SELECT id,client_id,staff_id,state,contract_start_date,contract_end_date,is_delete FROM hr_contract WHERE is_delete = 0 AND state in (1,2,3) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) c
        ON c.staff_id = a.id AND c.is_delete = 0
        WHERE a.is_delete = 0 AND a.iz_default = 0
        <if test="permissionClient!=null and permissionClient.size>0">
            AND a.client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <include refid="Select_SQL"/>
    </select>

    <select id="findResignationStaff" resultType="cn.casair.domain.HrTalentStaff">
        SELECT
            hswe.departure_date resignationDate,
            hts.*
        FROM
            hr_talent_staff hts
            LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id
                      AND (CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END) AND hswe.is_delete = 0
        WHERE
            hts.is_delete = 0
            AND hts.iz_default = 0
            <if test="param.clientId != null">
                AND hts.client_id = #{param.clientId}
            </if>
            <if test="param.staffStatus != null">
                AND hts.staff_status = #{param.staffStatus}
            </if>
            <if test="param.status != null">
                AND hts.status = #{param.status}
            </if>
    </select>

    <sql id="Find_Renewal">
        SELECT
            a.*,
            IF (a.sex = 1, '男', '女') sexlabel,
            TIMESTAMPDIFF(YEAR, a.birthday, CURDATE()) age,
            b.unit_number unitNumber,
            b.client_name client_name,
            c.contract_start_date contract_start_date,
            c.contract_end_date contract_end_date,
            c.state contractState,
            hss.old_client_id AS oldClientId,
            hss.is_default AS secondmentIsDefault,
            hss.staff_status secondmentStaffStatus,
            ham.archives_num archives_num
        FROM
            hr_talent_staff a
        LEFT JOIN hr_client b ON a.client_id = b.id AND b.is_delete = 0
        LEFT JOIN
        (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
        FROM (SELECT id,client_id,staff_id,state,contract_start_date,contract_end_date,is_delete FROM hr_contract WHERE is_delete = 0 AND state in (1,2,3) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) c
        ON c.staff_id = a.id AND c.is_delete = 0
        LEFT JOIN hr_staff_secondment hss ON hss.id = a.secondment_id AND hss.is_delete = 0
        LEFT JOIN hr_archives_manage ham ON a.id = ham.staff_id AND ham.is_delete = 0
        WHERE a.is_delete = 0 AND a.iz_default = 0 AND a.staff_status NOT IN (5,6) AND (a.renewal_process IS NOT NULL OR c.state IN (2,3))
        <if test="clientIds!=null and clientIds.size() > 0">
            AND a.client_id IN
            <foreach collection="clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <include refid="Select_SQL"/>
    </sql>

    <select id="findPageRenewal" resultType="cn.casair.dto.HrTalentStaffDTO">
        <include refid="Find_Renewal"/>
    </select>

    <select id="findListRenewal" resultType="cn.casair.dto.HrTalentStaffDTO">
        <include refid="Find_Renewal"/>
    </select>

    <select id="findStaffBatchIds" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            hc.client_name,
            hc.unit_number,
            hss.area AS socialSecurityArea,
            hp.agreement_number,
            hts.*
        FROM
            hr_talent_staff hts
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        LEFT JOIN hr_protocol hp ON hp.client_id = hc.id AND hp.is_delete = 0 AND hp.use_status = 1
        LEFT JOIN hr_social_security hss ON hc.social_security_type_id = hss.id
        WHERE hts.is_delete = 0 AND hts.iz_default = 0
        <if test="staffIds!=null and staffIds.size() > 0">
            AND hts.id IN
            <foreach collection="staffIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
    </select>

    <select id="selectByIdCards" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            hts.id,
            hts.certificate_num,
            hts.system_num,
            hc.client_name,
            hc.unit_number
        FROM hr_talent_staff hts
        LEFT JOIN  hr_client hc ON hts.client_id = hc.id
        WHERE certificate_num IN
        <foreach collection="idCardList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <delete id="deleteArchivesBringDetail">
        DELETE FROM hr_archives_bring_detail
        <where>
            <if test="detailIds!=null and detailIds.size() > 0">
                detail_id IN
                <foreach collection="detailIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
        </where>
    </delete>

    <delete id="deleteTalentStaff">
        DELETE  FROM hr_talent_staff  WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </delete>

    <update id="updateStaffRenewalProcess">
        UPDATE hr_talent_staff SET renewal_process = #{renewalProcess} WHERE id = #{staffId}
    </update>

    <update id="updateStatus">
        UPDATE hr_talent_staff set protocol_id =#{id}  where client_id=#{clientId} and is_delete=0 and `status`=0
    </update>

    <update id="updateTalentStaff">
        UPDATE hr_talent_staff
        <trim prefix="set" suffixOverrides=",">
            <if test="param.avatar != null">avatar = #{param.avatar},</if>
            <if test="param.name != null">`name` = #{param.name},</if>
            <if test="param.nationality != null">`nationality` = #{param.nationality},</if>
            <if test="param.nation != null">`nation` = #{param.nation},</if>
            <if test="param.certificateType != null">`certificate_type` = #{param.certificateType},</if>
            <if test="param.certificateNum != null">`certificate_num` = #{param.certificateNum},</if>
            <if test="param.birthday != null">`birthday` = #{param.birthday},</if>
            <if test="param.sex != null">`sex` = #{param.sex},</if>
            <if test="param.phone != null">`phone` = #{param.phone},</if>
            <if test="param.nativePlace != null">`native_place` = #{param.nativePlace},</if>
            <if test="param.height != null">`height` = #{param.height},</if>
            <if test="param.email != null">email = #{param.email},</if>
            <if test="param.maritalStatus != null">`marital_status` = #{param.maritalStatus},</if>
            <if test="param.highestEducation != null">`highest_education` = #{param.highestEducation},</if>
            <if test="param.householdRegistration != null">household_registration = #{param.householdRegistration},</if>
            <if test="param.domicilePlace != null">domicile_place = #{param.domicilePlace},</if>
            <if test="param.censusRegisterAddress != null">`census_register_address` = #{param.censusRegisterAddress},</if>
            <if test="param.censusRegisterPostcode != null">census_register_postcode = #{param.censusRegisterPostcode},</if>
            <if test="param.politicsStatus != null">politics_status = #{param.politicsStatus},</if>
            <if test="param.partyDate != null">party_date = #{param.partyDate},</if>
            <if test="param.partyBranch != null">party_branch = #{param.partyBranch},</if>
            <if test="param.contactAddress != null">`contact_address` = #{param.contactAddress},</if>
            <if test="param.contactPostcode != null">contact_postcode = #{param.contactPostcode},</if>
            <if test="param.nickName != null">nick_name = #{param.nickName},</if>
            <if test="param.izMilitary != null">`iz_military` = #{param.izMilitary},</if>
            <if test="param.militaryStartDate != null">military_start_date = #{param.militaryStartDate},</if>
            <if test="param.militaryEndDate != null">military_end_date = #{param.militaryEndDate},</if>
            <if test="param.rewardsPunishment != null">rewards_punishment = #{param.rewardsPunishment},</if>
            <if test="param.lastModifiedBy != null">last_modified_by = #{param.lastModifiedBy},</if>
            <if test="param.lastModifiedDate != null">last_modified_date = #{param.lastModifiedDate},</if>
            <if test="param.remark != null">remark = #{param.remark},</if>
        </trim>
        WHERE id = #{param.id}
    </update>

    <update id="updateApplyStaffId">
        UPDATE hr_talent_staff SET apply_staff_id = #{applyStaffId} WHERE id = #{staffId}
    </update>

    <update id="updateWorkStatus">
        UPDATE hr_talent_staff SET work_status = #{workStatus} WHERE id = #{staffId}
    </update>

    <update id="updateStaffFirstLoginSign">
        UPDATE hr_talent_staff
        SET first_login_sign = 1
        WHERE id = #{staffId}
    </update>

    <update id="updateBatchIds">
        UPDATE `hr_talent_staff`
        SET `client_id` = NULL,
         `protocol_id` = NULL,
         `staff_status` = NULL,
         `password` = NULL,
         `apply_staff_id` = NULL,
         `iz_start_end` = 0,
         `iz_preserve` = 0,
         `iz_insured` = 0,
         `open_id` = NULL,
         `departure_staff_id` = NULL,
         `resignation_date` = NULL,
         `last_modified_by` = #{lastModifiedBy},
         `last_modified_date` = #{lastModifiedDate}
        WHERE
            (
                `id` IN
                <foreach collection="ids" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            )
    </update>

    <update id="updateIzInsured">
        UPDATE hr_talent_staff SET iz_insured = #{izInsured} WHERE id = #{staffId}
    </update>

    <update id="updateStaffWorkExperience">
        UPDATE hr_staff_work_experience SET departure_date = #{now},iz_default = 0,iz_insured = 2,staff_status = 5 WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateContract">
        UPDATE hr_contract SET state = 5 WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateApplyDepartureStaff">
        UPDATE hr_apply_departure_staff SET departure_apply_status = 1 ,departure_staff_status = 6,website_departure_schedule = 6 WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateRenewalServiceId">
        UPDATE hr_talent_staff SET renewal_service_id = #{renewalServiceId} WHERE id = #{staffId}
    </update>

    <update id="updateRenewalContract">
        UPDATE hr_talent_staff SET is_renewal_contract = #{isRenewalContract} WHERE id IN
        <foreach collection="staffIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updatePassword">
        UPDATE hr_talent_staff SET password = #{params.password} WHERE id  = #{params.id}
    </update>

    <update id="updateOpenIdByStaffId">
        UPDATE hr_talent_staff SET open_id = #{openId} WHERE id IN
        <foreach collection="staffIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateHrTalentStaff">
        UPDATE hr_talent_staff
        <trim prefix="set" suffixOverrides=",">
            `name` = #{param.name},
            certificate_num = #{param.certificateNum},
            certificate_type = #{param.certificateType},
            sex = #{param.sex},
            phone = #{param.phone},
            personnel_type = #{param.personnelType},
            birthday = #{param.birthday},
            household_registration = #{param.householdRegistration},
            highest_education = #{param.highestEducation},
            `status` = #{param.status},
            iz_default = #{param.izDefault},
            client_id = #{param.clientId},
            protocol_id = #{param.protocolId},
            system_num = #{param.systemNum},
            nationality = #{param.nationality},
            nation = #{param.nation},
            politics_status = #{param.politicsStatus},
            party_date = #{param.partyDate},
            party_branch = #{param.partyBranch},
            marital_status = #{param.maritalStatus},
            native_place = #{param.nativePlace},
            height = #{param.height},
            email = #{param.email},
            census_register_address = #{param.censusRegisterAddress},
            census_register_postcode = #{param.censusRegisterPostcode},
            contact_address = #{param.contactAddress},
            contact_postcode = #{param.contactPostcode},
            nick_name = #{param.nickName},
            domicile_place = #{param.domicilePlace},
            iz_military = #{param.izMilitary},
            military_start_date = #{param.militaryStartDate},
            military_end_date = #{param.militaryEndDate},
            rewards_punishment = #{param.rewardsPunishment},
            remark = #{param.remark},
            staff_status = #{param.staffStatus},
            iz_start_end = #{param.izStartEnd},
            iz_preserve = #{param.izPreserve},
            iz_insured = #{param.izInsured},
            open_id = #{param.openId},
            apply_staff_id = #{param.applyStaffId},
            departure_staff_id = #{param.departureStaffId},
            resignation_date = #{param.resignationDate},
            renewal_process = #{param.renewalProcess},
            is_renewal_contract = #{param.isRenewalContract},
            renewal_service_id = #{param.renewalServiceId},
            secondment_id = #{param.secondmentId},
            avatar = #{param.avatar},
            alias_name = #{param.aliasName},
            appendix_ids = #{param.appendixIds},
            iz_retire = #{param.izRetire},
            medical_record_date = #{param.medicalRecordDate},
            last_modified_date = now(),
        </trim>
        WHERE id = #{param.id}
    </update>

    <update id="updateIzDefault">
        UPDATE `hr_talent_staff` SET
        `iz_default` = 1,
        `last_modified_by` = #{lastModifiedBy},
        `last_modified_date` = now()
        WHERE `id` IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="batchUpdateStaffSupplementaryPayment">
        UPDATE hr_talent_staff hts
        SET hts.supplementary_payment = #{params.supplementaryPayment},
            hts.last_modified_by = #{user.userName},
            hts.last_modified_date = NOW()
        WHERE
            hts.is_delete = 0
            AND hts.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </update>

    <update id="updateSupplementaryPayment">
        UPDATE hr_talent_staff hts
        SET supplementary_payment = #{supplementaryPayment}
        WHERE hts.id IN
        <foreach collection="staffIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="manualUpdateStaffInductionProcess">
        UPDATE hr_talent_staff
        <set>
            <if test="params.staffStatus!=null and params.izStartEnd!=null">
                staff_status = #{params.staffStatus},
                iz_start_end = #{params.izStartEnd}
            </if>
            <if test="params.staffStatus==null and params.izStartEnd==null">
                renewal_process = NULL,
                is_renewal_contract = NULL
            </if>
        </set>
        WHERE
            id = #{params.id}
    </update>

    <update id="updateStaffLoginStatus">
        UPDATE
            hr_talent_staff
        SET `status` = 0
        WHERE is_delete = 0
        AND iz_default = 0
        AND `status` = 1
        AND staff_status NOT IN (5)
        AND client_id NOT IN
        <foreach collection="clientIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateStatusByClient">
        UPDATE
            hr_talent_staff
        SET `status` = #{status} AND last_modified_date = now()
        WHERE is_delete = 0 AND iz_default = 0
        AND client_id IN
        <foreach collection="clientIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <insert id="insertDistinguish">
        INSERT INTO `hr_distinguish` ( `id`, `staff_id`, `type`, `created_date` )
        VALUES
            ( #{param.id}, #{param.staffId}, #{param.type}, now() )
    </insert>

    <select id="selectDistinguish" resultType="cn.casair.dto.HrDistinguishDTO">
        SELECT
            hd.*
        FROM
            hr_distinguish hd
            LEFT JOIN hr_talent_staff hts ON hd.staff_id = hts.id
        WHERE
            hd.is_delete = 0
            AND hts.is_delete = 0
            AND hd.type = #{type}
            AND hts.certificate_num = #{idCard}
    </select>

    <update id="resetSuccessRenewalProcess">
        UPDATE hr_talent_staff
        SET renewal_process     = null,
            is_renewal_contract = 0
        WHERE id IN
        <foreach collection="staffIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <select id="getStaffInfoByCertificateNum" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT hts.id,
               hts.system_num,
               hts.`name`,
               hts.`phone`
        FROM hr_talent_staff hts
        WHERE hts.is_delete = 0
          AND hts.certificate_num = #{certificateNum}
    </select>
</mapper>
