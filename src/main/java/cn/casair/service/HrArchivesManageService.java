package cn.casair.service;

import cn.casair.domain.HrArchivesManage;
import cn.casair.dto.HrArchivesDetailDTO;
import cn.casair.dto.HrArchivesManageDTO;
import cn.casair.dto.HrClientDTO;
import cn.casair.dto.HrTalentStaffDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * 档案管理服务类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
public interface HrArchivesManageService extends IService<HrArchivesManage> {

    /**
     * 批量更新档案状态
     *
     * @param ids
     * @param status
     * @return void
     * <AUTHOR>
     * @date 2022/2/9
     **/
    void updateStateByIds(List<String> ids, Integer status);

    /**
     * 获取档案详情
     *
     * @param id
     * @return cn.casair.dto.HrArchivesManageDTO
     * <AUTHOR>
     * @date 2021/11/29
     **/
    HrArchivesManageDTO getHrArchives(String id);

    /**
     * 分页查询档案列表
     *
     * @param hrArchivesManageDTO
     * @param pageNumber
     * @param pageSize
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * <AUTHOR>
     * @date 2021/9/16
     **/
    IPage<HrArchivesManageDTO> findPage(HrArchivesManageDTO hrArchivesManageDTO, Long pageNumber, Long pageSize);

    /**
     * 创建档案
     *
     * @param hrArchivesManageDTO
     * @return cn.casair.dto.HrArchivesManageDTO
     * <AUTHOR>
     * @date 2021/9/16
     **/
    HrArchivesManageDTO createHrArchivesManage(HrArchivesManageDTO hrArchivesManageDTO);

    /**
     * 修改档案管理
     *
     * @param hrArchivesManageDTO
     * @return
     * @date 2021/9/10
     */
    Optional<HrArchivesManageDTO> updateHrArchivesManage(HrArchivesManageDTO hrArchivesManageDTO);

    /**
     * 批量删除档案管理
     *
     * @param ids
     * @date 2021/9/10
     */
    void deleteHrArchivesManage(List<String> ids);


    /**
     * 档案批量导出
     *
     * @param hrArchivesManageDTO
     * @param httpResponse
     * @return void
     * <AUTHOR>
     * @date 2021/10/13
     **/
    String exportArchives(HrArchivesManageDTO hrArchivesManageDTO, HttpServletResponse httpResponse);

    /**
     * 分页查询未建档的员工
     *
     * @param hrTalentStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     * @date 2021/9/10
     */
    IPage<HrTalentStaffDTO> findEmployeePage(HrTalentStaffDTO hrTalentStaffDTO, Long pageNumber, Long pageSize);

    /**
     * 分页查询未建档的客户
     *
     * @param hrClientDTO
     * @param pageNumber
     * @param pageSize
     * @return
     * <AUTHOR>
     */
    IPage<HrClientDTO> findClientPage(HrClientDTO hrClientDTO, Long pageNumber, Long pageSize);

    /**
     * 档案批量导入
     *
     * @param file
     * @return
     * @date 2021/9/13
     */
    String importArchivesManage(MultipartFile file);

    /**
     * 下载档案导入模板
     *
     * @return void
     * <AUTHOR>
     * @Date 14:48 2021/9/22
     * @Param 下载导入模板
     **/
    String importArchivesManageTemplate();

    /**
     * 新增上传档案明细附加
     *
     * @param hrArchivesDetailDTO
     * @return int
     * <AUTHOR>
     * @date 2021/10/13
     **/
    int updateArchivesDetailAppendix(HrArchivesDetailDTO hrArchivesDetailDTO);

    /**
     * 根据员工id获取员工档案详情
     *
     * @param clientId
     * @param staffId
     * @return cn.casair.dto.HrArchivesManageDTO
     * <AUTHOR>
     * @date 2021/10/27
     **/
    HrArchivesManageDTO getStaffArchives(String clientId, String staffId);
}
