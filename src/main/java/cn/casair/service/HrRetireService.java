package cn.casair.service;
import cn.casair.domain.HrRetire;
import cn.casair.dto.HrRetireDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;

/**
 * 退休表服务类
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
public interface HrRetireService extends IService<HrRetire> {


    /**
     * 创建退休表
     * @param hrRetireDTO
     * @return
     */
    HrRetireDTO createHrRetire(HrRetireDTO hrRetireDTO);

    /**
     * 修改退休表
     * @param hrRetireDTO
     * @return
     */
    Optional<HrRetireDTO> updateHrRetire(HrRetireDTO hrRetireDTO);

    /**
     * 查询退休表详情
     * @param id
     * @return
     */
    HrRetireDTO getHrRetire(String id);

    /**
     * 删除退休表
     * @param id
     */
    void deleteHrRetire(String id);

    /**
     * 批量删除退休表
     * @param ids
     */
    void deleteHrRetire(List<String> ids);

    /**
     * 分页查询退休表
     * @param hrRetireDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRetireDTO hrRetireDTO,Long pageNumber,Long pageSize);

    /**
     * 查看退休详情
     * @param hrRetireDTO
     * @return
     */
    HrRetireDTO getDetail(HrRetireDTO hrRetireDTO);

    /**
     * 批量填写资料
     * @param hrRetireDTOList
     * @return
     */
    ResponseEntity<?> updateStates(List<HrRetireDTO> hrRetireDTOList);

    /**
     * 退休导出
     * @param hrRetireDTO
     * @param httpServletResponse
     * @return
     */
    String exportRetire(HrRetireDTO hrRetireDTO, HttpServletResponse httpServletResponse);

    /**
     *检查员工是否快要退休了(定时任务)
     */
    void updateRetireState();

    /**
     * 微信 退休的进度详情
     * @return
     */
    List<HrRetireDTO> RetirePlanned();

    /**
     * 微信 添加退休
     * @param hrRetireDTO
     * @return
     */
    HrRetire createRetirePlanned(HrRetireDTO hrRetireDTO);

    /**
     * 管理端 新增退休服务
     * @param hrRetireDTO
     * <AUTHOR> Lyric.Lin 2022/12/13 14:00
     */
    HrRetireDTO createHrRetireService(HrRetireDTO hrRetireDTO);

    /**
     * 每天0点创建员工退休
     */
    void createStaffRetire();

}
