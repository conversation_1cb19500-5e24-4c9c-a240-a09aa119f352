package cn.casair.repository;

import cn.casair.domain.CodeTable;
import cn.casair.dto.CodeTableDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 码表数据库操作类
 *
 * <AUTHOR>
 * @since 2020-04-10
 */
@Repository
public interface CodeTableRepository extends BaseMapper<CodeTable> {

    /**
     * 获取银行字典
     *
     * @param bankName
     * @return
     */
    CodeTable getItemByBankName(@Param("bankName") String bankName);

    @Select("select * from code_table ct where ct.parent_id in (select id from code_table where inner_name = #{key}) and ct.is_delete = 0")
    List<CodeTable> getChildrenByKey(@Param("key") String key);

    /**
     * 根据内部名获取字典项
     *
     * @param innerName
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/10/11
     **/
    List<CodeTableDTO> getCodeTableListByInnerName(String innerName);

    /**
     * 根据内部名获取字典项(特殊处理)
     *
     * @param innerName 内部名
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/8/31
     **/
    List<CodeTableDTO> getCodeTableSpecialByInnerName(@Param("innerName") String innerName);

    /**
     * 根据父级id获取最大的子级字典项
     *
     * @param parentId 父级id
     * @return cn.casair.dto.CodeTableDTO
     * <AUTHOR>
     * @date 2021/9/14
     **/
    CodeTableDTO getMaxChildren(@Param("parentId") Integer parentId);

    /**
     * 分页查询数据字典
     *
     * @param page
     * @param codeTableDTO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/10/8
     **/
    IPage<CodeTableDTO> findPage(Page<CodeTable> page, @Param("params") CodeTableDTO codeTableDTO);

    /**
     * 根据父id获取子级列表
     *
     * @param parentId
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/10/9
     **/
    List<CodeTableDTO> getChildrenByParentId(@Param("parentId") Integer parentId);

    /**
     * 根据ids获取数据字典列表
     *
     * @param id
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/10/9
     **/
    List<CodeTableDTO> getListById(@Param("id") Integer id);

    /**
     * 根据父级id获取子级列表（根据displayOrder排序）
     *
     * @param parentId
     * @return java.util.List<cn.casair.dto.CodeTableDTO>
     * <AUTHOR>
     * @date 2021/10/9
     **/
    List<CodeTableDTO> getChildrenListByParentId(@Param("parentId") Integer parentId);

    /**
     * 根据父级id与字典项名称获取数据数量
     *
     * @param parentId
     * @param itemName
     * @return int
     * <AUTHOR>
     * @date 2021/10/9
     **/
    int getItemByParentIdAndItemName(@Param("parentId") Integer parentId, @Param("itemName") String itemName);

    /**
     * 获取此父Id下字典名数量
     *
     * @param codeTableDTO
     * @return int
     * <AUTHOR>
     * @date 2021/10/27
     **/
    int uniqueVerification(CodeTableDTO codeTableDTO);

    /**
     * 根据内部名获取值 （专用接口）
     *
     * @param innerName
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021/11/11
     **/
    Integer getValueByInnerName(@Param("innerName") String innerName);

    CodeTable getMaxChildrenByInnerName(@Param("innerName") String innerName);

    /**
     * 特殊处理取出费用类型中的中石化字典部分
     * @param expensetype
     * @param itemValue
     * @return
     */
    List<CodeTableDTO> findSinopecBillCode(@Param("expensetype") String expensetype, @Param("itemValue") String itemValue);

}
