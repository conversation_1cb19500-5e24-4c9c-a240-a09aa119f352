<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRealNameAuthRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `name`, certificate_num, is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrRealNameAuth">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="certificate_num" property="certificateNum"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectAuthInfo" resultType="cn.casair.domain.HrRealNameAuth">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_real_name_auth
        WHERE
            is_delete = 0
            AND certificate_num = #{params.certificateNum}
            AND `name` = #{params.name}
        LIMIT 1
    </select>

</mapper>
