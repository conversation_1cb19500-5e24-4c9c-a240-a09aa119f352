<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrArrivalRecordRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, client_id, pay_year, pay_monthly, receivable_amount, total_arrival_amount, update_date, remark,is_default,fee_review_id,bill_invoice_id
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrArrivalRecord">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="receivable_amount" property="receivableAmount"/>
        <result column="total_arrival_amount" property="totalArrivalAmount"/>
        <result column="update_date" property="updateDate"/>
        <result column="remark" property="remark"/>
        <result column="is_default" property="isDefault"/>
        <result column="fee_review_id" property="feeReviewId"/>
        <result column="bill_invoice_id" property="billInvoiceId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByEntity" resultType="cn.casair.dto.HrArrivalRecordDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM hr_arrival_record
        WHERE is_delete = 0
            AND client_id = #{params.clientId}
            AND pay_year = #{params.payYear}
            AND pay_monthly = #{params.payMonthly}
    </select>

    <select id="getDetailById" resultType="cn.casair.dto.HrArrivalRecordDTO">
        SELECT har.id,
               har.client_id,
               hc.unit_number,
               hc.client_name,
               har.pay_year,
               har.pay_monthly,
               har.receivable_amount,
               har.total_arrival_amount,
               har.update_date,
               har.remark,
               har.fee_review_id,
               har.bill_invoice_id
        FROM hr_arrival_record har
            LEFT JOIN hr_client hc ON hc.id = har.client_id
            AND hc.is_delete = 0
        WHERE har.is_delete = 0
          AND har.id = #{id}
    </select>

    <sql id="Query_Criteria">
        SELECT
            *
        FROM
        (
            SELECT
                har.*,
                hc.unit_number,
                hc.client_name,
                ( har.total_arrival_amount - har.receivable_amount ) AS totalDifferenceAmount,
                CONCAT( har.pay_year,'-',IF(har.pay_monthly>9,har.pay_monthly,CONCAT('0',har.pay_monthly))) AS paymentDate
            FROM
                hr_arrival_record har
            LEFT JOIN hr_client hc ON hc.id = har.client_id AND hc.is_delete = 0
            WHERE har.is_delete = 0
            <if test="permissionClient!=null and permissionClient.size()>0">
                AND har.client_id IN
                <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
        ) har
        WHERE har.is_delete = 0
        <if test="params.clientIds!=null and params.clientIds.size()>0">
            AND har.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.ids!=null and params.ids.size()>0">
            AND har.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.unitNumber!=null and params.unitNumber!=''">
            AND har.unit_number LIKE CONCAT('%', #{params.unitNumber}, '%')
        </if>
        <if test="params.payYear!=null">
            AND har.pay_year = #{params.payYear}
        </if>
        <if test="params.payMonthly!=null">
            AND har.pay_monthly = #{params.payMonthly}
        </if>
        <if test="params.receivableAmountQuery!=null and params.receivableAmountQuery.size()>0">
            AND har.receivable_amount BETWEEN #{params.receivableAmountStart} AND #{params.receivableAmountEnd}
        </if>
        <if test="params.totalArrivalAmountQuery!=null and params.totalArrivalAmountQuery.size()>0">
            AND har.total_arrival_amount BETWEEN #{params.totalArrivalAmountStart} AND #{params.totalArrivalAmountEnd}
        </if>
        <if test="params.totalDifferenceAmountQuery!=null and params.totalDifferenceAmountQuery.size()>0">
            AND totalDifferenceAmount BETWEEN #{params.totalDifferenceAmountStart} AND #{params.totalDifferenceAmountEnd}
        </if>
        <if test="params.paymentDateQuery!=null and params.paymentDateQuery.size()>0">
            AND paymentDate BETWEEN #{params.paymentDateStart} AND #{params.paymentDateEnd}
        </if>
        <if test="params.differenceType!=null">
            <choose>
                <when test="params.differenceType==0">
                    AND totalDifferenceAmount = 0
                </when>
                <when test="params.differenceType==1">
                    AND totalDifferenceAmount &gt; 0
                </when>
                <otherwise>
                    AND totalDifferenceAmount &lt; 0
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY
                        har.pay_year ${params.order},
                        har.pay_monthly ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY
                        ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY
                har.created_date DESC
            </otherwise>
        </choose>
    </sql>

    <select id="selectPageList" resultType="cn.casair.dto.HrArrivalRecordDTO">
        <include refid="Query_Criteria"/>
    </select>

    <select id="selectRevenue" resultType="cn.casair.dto.HrArrivalRecordDTO">
        WITH a AS (
            SELECT
            har.client_id,
            STR_TO_DATE( concat( ANY_VALUE ( har.pay_year ), '-', ANY_VALUE ( har.pay_monthly ), '-', 01 ), '%Y-%m-%d' ) payment_date,
            har.total_arrival_amount
            FROM
            hr_arrival_record har
            WHERE
            har.is_delete = 0
        ) SELECT
            a.client_id,
            ANY_VALUE ( hc.client_name ) client_name,
            SUM( a.total_arrival_amount ) total_arrival_amount
        FROM
            a
            LEFT JOIN hr_client hc ON hc.id = a.client_id
        <where>
            <if test="permissionClient!=null and permissionClient.size>0">
                AND hc.id IN
                <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.clientIds!=null and params.clientIds.size>0">
                AND a.client_id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.paymentDateStart!=null">
                AND a.payment_date &gt;= #{params.paymentDateStart}
            </if>
            <if test="params.paymentDateEnd!=null">
                AND a.payment_date &lt;= #{params.paymentDateEnd}
            </if>
        </where>
        GROUP BY
            a.client_id
    </select>

    <select id="findList" resultType="cn.casair.dto.HrArrivalRecordDTO">
        <include refid="Query_Criteria"/>
    </select>

    <update id="subtractionTotalArrivalAmount">
        UPDATE hr_arrival_record
        SET total_arrival_amount = total_arrival_amount - #{arrivalAmount}
        WHERE id = #{arrivalId}
    </update>

    <update id="additionTotalArrivalAmount">
        UPDATE hr_arrival_record
        SET total_arrival_amount = total_arrival_amount + #{arrivalAmount}
        WHERE id = #{arrivalId}
    </update>


</mapper>
