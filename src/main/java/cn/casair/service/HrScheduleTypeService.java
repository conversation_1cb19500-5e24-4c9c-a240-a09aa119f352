package cn.casair.service;
import cn.casair.domain.HrScheduleType;
import cn.casair.dto.CodeTableDTO;
import cn.casair.dto.HrScheduleTypeDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用户日程类型表服务类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
public interface HrScheduleTypeService extends IService<HrScheduleType> {


    /**
     * 创建用户日程类型表
     * @param hrScheduleTypeDTO
     * @return
     */
    HrScheduleTypeDTO createHrScheduleType(HrScheduleTypeDTO hrScheduleTypeDTO);

    /**
     * 修改用户日程类型表
     * @param hrScheduleTypeDTO
     * @return
     */
    Optional<HrScheduleTypeDTO> updateHrScheduleType(HrScheduleTypeDTO hrScheduleTypeDTO);

    /**
     * 查询用户日程类型表详情
     * @param id
     * @return
     */
    HrScheduleTypeDTO getHrScheduleType(String id);

    /**
     * 删除用户日程类型表
     * @param id
     */
    void deleteHrScheduleType(String id);

    /**
     * 批量删除用户日程类型表
     * @param ids
     */
    void deleteHrScheduleType(List<String> ids);

    /**
     * 分页查询用户日程类型表
     * @param hrScheduleTypeDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrScheduleTypeDTO hrScheduleTypeDTO,Long pageNumber,Long pageSize);

    List<CodeTableDTO> selectListByUser();

}
