package cn.casair.repository;

import cn.casair.domain.HrWelfareCompensationRecord;
import cn.casair.dto.HrWelfareCompensationRecordDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 福利补差计算操作日志数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Repository
public interface HrWelfareCompensationRecordRepository extends BaseMapper<HrWelfareCompensationRecord> {

    /**
     * 根据员工福利补差统计id删除操作日志
     *
     * @param welfareCompensationIds
     * @return int
     * <AUTHOR>
     * @date 2021/12/6
     **/
    int deleteByWelfareCompensationIds(@Param("welfareCompensationIds") List<String> welfareCompensationIds);

    /**
     * 根据账单id关联查询补差操作日志
     *
     * @param billId
     * @return java.util.List<cn.casair.dto.HrWelfareCompensationRecordDTO>
     * <AUTHOR>
     * @date 2021/11/15
     **/
    List<HrWelfareCompensationRecordDTO> selectByBillId(@Param("billId") String billId);

    @Select("SELECT * FROM hr_welfare_compensation_record hr WHERE hr.is_delete = 0 AND hr.id = #{recordId}")
    HrWelfareCompensationRecordDTO getById(String recordId);
}
