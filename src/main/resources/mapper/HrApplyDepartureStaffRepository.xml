<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrApplyDepartureStaffRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, apply_departure_id, client_id, staff_id, station_id, work_duration_start_date, work_duration_end_date, compensation, departure_date, stop_pay_year, stop_pay_monthly,
            certifier, certifier_phone, departure_reason, departure_apply_status, departure_staff_status, departure_applicant, website_departure_schedule, iz_refund_social_security,rejection_reason,
            material_id, iz_online_contract,contract_num,return_reason,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrApplyDepartureStaff">
        <id column="id" property="id"/>
        <result column="apply_departure_id" property="applyDepartureId"/>
        <result column="client_id" property="clientId"/>
        <result column="staff_id" property="staffId"/>
        <result column="station_id" property="stationId"/>
        <result column="work_duration_start_date" property="workDurationStartDate"/>
        <result column="work_duration_end_date" property="workDurationEndDate"/>
        <result column="compensation" property="compensation"/>
        <result column="departure_date" property="departureDate"/>
        <result column="stop_pay_year" property="stopPayYear"/>
        <result column="stop_pay_monthly" property="stopPayMonthly"/>
        <result column="certifier" property="certifier"/>
        <result column="certifier_phone" property="certifierPhone"/>
        <result column="departure_reason" property="departureReason"/>
        <result column="departure_apply_status" property="departureApplyStatus"/>
        <result column="departure_staff_status" property="departureStaffStatus"/>
        <result column="departure_applicant" property="departureApplicant"/>
        <result column="website_departure_schedule" property="websiteDepartureSchedule"/>
        <result column="iz_refund_social_security" property="izRefundSocialSecurity"/>
        <result column="rejection_reason" property="rejectionReason"/>
        <result column="material_id" property="materialId"/>
        <result column="iz_online_contract" property="izOnlineContract"/>
        <result column="contract_num" property="contractNum"/>
        <result column="return_date" property="returnDate"/>
        <result column="return_reason" property="returnReason"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="findSQl">
        SELECT
            hts.`name`,
            hts.certificate_num,
            hts.phone,
            hts.personnel_type,
            hts.sex,IF (hts.sex = 1, '男', '女') sexLabel,
            hc.client_name,
            hads.*
        FROM
            hr_apply_departure_staff hads
        LEFT JOIN hr_apply_departure had ON hads.apply_departure_id = had.id AND had.is_delete = 0
        LEFT JOIN hr_talent_staff hts ON hads.staff_id = hts.id AND hts.is_delete = 0
        LEFT JOIN hr_client hc ON hc.id = hts.client_id
    </sql>

    <sql id="Select_SQL">
        SELECT
            hc.client_name,
            hts.`name`,
            hts.staff_status,
            hts.certificate_num,
            hts.phone,
            hts.personnel_type,
            hts.sex,
            IF (hts.sex = 1, '男', '女') sexLabel,
            su.real_name AS specialized,
            hswe.board_date,
            hads.*,
            had.is_default,
            had.is_launch
        FROM
            hr_apply_departure_staff hads
        LEFT JOIN hr_client hc ON hads.client_id = hc.id AND hc.is_delete = 0
        LEFT JOIN hr_talent_staff hts ON hads.staff_id = hts.id
        LEFT JOIN hr_staff_work_experience hswe ON hswe.staff_id = hts.id AND hts.client_id = hswe.client_id AND hswe.is_delete = 0 AND hswe.iz_default = 1
        LEFT JOIN sys_user su ON hc.specialized_id = su.id AND su.is_delete = 0 AND su.user_status = 1
        LEFT JOIN hr_apply_departure had ON had.id = hads.apply_departure_id
        WHERE hads.is_delete = 0 AND hts.is_delete = 0 AND hads.departure_staff_status != 0
        <if test="param.name != null and param.name != ''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND hts.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.compensation != null">
            AND hads.compensation = #{param.compensation}
        </if>
        <if test="param.certifier != null and param.certifier != ''">
            AND hads.certifier LIKE concat('%', #{param.certifier}, '%')
        </if>
        <if test="param.certifierPhone != null and param.certifierPhone != ''">
            AND hads.certifier_phone LIKE concat('%', #{param.certifierPhone}, '%')
        </if>
        <if test="param.clientId != null">
            AND hts.client_id = #{param.clientId}
        </if>
        <if test="param.createdDateStart!=null">
            AND hads.created_date &gt;= #{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd!=null">
            AND hads.created_date &lt; date_add(#{param.createdDateEnd}, interval 1 day)
        </if>
        <if test="param.workDurationStartDateStart!=null">
            AND hads.work_duration_start_date &gt;= #{param.workDurationStartDateStart}
        </if>
        <if test="param.workDurationStartDateEnd!=null">
            AND hads.work_duration_start_date &lt; date_add(#{param.workDurationStartDateEnd}, interval 1 day)
        </if>
        <if test="param.workDurationEndDateStart!=null">
            AND hads.work_duration_end_date &gt;= #{param.workDurationEndDateStart}
        </if>
        <if test="param.workDurationEndDateEnd!=null">
            AND hads.work_duration_end_date &lt; date_add(#{param.workDurationEndDateEnd}, interval 1 day)
        </if>
        <if test="param.departureDateStart!=null">
            AND hads.departure_date &gt;= #{param.departureDateStart}
        </if>
        <if test="param.departureDateEnd!=null">
            AND hads.departure_date &lt; date_add(#{param.departureDateEnd}, interval 1 day)
        </if>
        <if test="param.stopPayYear != null">
            AND hads.stop_pay_year = #{param.stopPayYear}
        </if>
        <if test="param.stopPayMonthly != null">
            AND hads.stop_pay_monthly = #{param.stopPayMonthly}
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hc.id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.personnelTypeList!=null and param.personnelTypeList.size() > 0">
            AND hts.personnel_type IN
            <foreach collection="param.personnelTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.departureStaffStatusList!=null and param.departureStaffStatusList.size() > 0">
            AND hads.departure_staff_status IN
            <foreach collection="param.departureStaffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.userIdList!=null and param.userIdList.size() > 0">
            AND su.id IN
            <foreach collection="param.userIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.ids!=null and param.ids.size() > 0">
            AND hads.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <choose>
            <when test="param.izOnlineContract == null"></when>
            <when test="param.izOnlineContract != null and param.izOnlineContract == true">
                AND hads.iz_online_contract = #{param.izOnlineContract} AND hads.departure_staff_status = 5
            </when>
            <when test="param.izOnlineContract != null and param.izOnlineContract == false">
                AND hads.iz_online_contract IS NULL AND hads.departure_staff_status = 5
            </when>
        </choose>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            ORDER BY  ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY hads.created_date DESC
        </if>
    </sql>

    <select id="findPage" resultType="cn.casair.dto.HrApplyDepartureStaffDTO">
        <include refid="Select_SQL"/>
    </select>

    <select id="findById" resultType="cn.casair.dto.HrApplyDepartureStaffDTO">
        SELECT
            hads.*, hts.`name`,hts.certificate_num,hts.sex,hts.phone,hts.personnel_type,hads.departure_reason,
            hs.profession_name,hc.contract_start_date,hc.contract_end_date,hse.basic_wage,
            hse.social_security_cardinal,hse.medical_insurance_cardinal,hse.accumulation_fund_cardinal,
            IF(hts.sex = 1, '男', '女') AS sexLabel
        FROM
            hr_apply_departure_staff hads
        LEFT JOIN hr_talent_staff hts ON hads.staff_id = hts.id AND hts.is_delete = 0
        LEFT JOIN (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
                   FROM (SELECT * FROM hr_contract WHERE is_delete = 0 AND state in (1,2,3) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id) hc ON hc.staff_id = hts.id AND hc.is_delete = 0
        LEFT JOIN hr_station hs ON hads.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hts.id AND hse.is_delete = 0
        WHERE hads.is_delete = 0 AND hads.id = #{id}
    </select>

    <select id="viewResignationInFor" resultType="cn.casair.dto.HrApplyDepartureStaffDTO">
        <include refid="findSQl"/>
        WHERE hads.is_delete = 0 AND hts.is_delete = 0
        <if test="departureId != null">
            AND hads.apply_departure_id = #{departureId}
        </if>
        <if test="departureApplyStatus != null">
            AND hads.departure_apply_status = #{departureApplyStatus}
        </if>
        <if test="departureStaffIds != null and departureStaffIds.size() > 0">
            AND hads.id IN
            <foreach collection="departureStaffIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        ORDER BY hads.created_date
    </select>

    <select id="resignedCountByYear" resultType="java.util.Map">
        SELECT
            m.id months,
	        IFNULL(countNum, 0) countNum
        FROM
            (
            select 1 as id union select 2 union select 3 union select 4 union select 5 union select 6
            union select 7 union select 8 union select 9 union select 10 union select 11 union select 12) m
        LEFT JOIN (
            SELECT
                DATE_FORMAT(hads.departure_date, '%c') months,
                COUNT(hads.id) countNum
            FROM
                hr_apply_departure_staff hads
            WHERE
                hads.is_delete = 0
            AND hads.departure_staff_status = 6
            AND YEAR (hads.departure_date) = #{particularYear}
            GROUP BY
                months
        ) n ON m.id = n.months ORDER BY m.id ASC
    </select>

    <select id="getHrApplyDepartureStaffStaffList" resultType="cn.casair.dto.excel.HrApplyDepartureStaffExport">
        <include refid="findSQl"/>
        WHERE hads.is_delete = 0 AND hts.is_delete = 0
        <if test="departureId != null">
            AND hads.apply_departure_id = #{departureId}
        </if>
        <if test="status != null">
            AND hads.departure_apply_status = #{status}
        </if>
        <if test="departureStaffIds != null and departureStaffIds.size() > 0">
            AND hads.id IN
            <foreach collection="departureStaffIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
    </select>

    <select id="selectByClientIdAndStaffId" resultType="cn.casair.dto.HrApplyDepartureStaffDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_apply_departure_staff
        WHERE is_delete = 0
          AND client_id = #{clientId}
          AND staff_id = #{staffId}
    </select>

    <select id="findList" resultType="cn.casair.dto.excel.HrApplyDepartureStaffExport">
        <include refid="Select_SQL"/>
    </select>
</mapper>
