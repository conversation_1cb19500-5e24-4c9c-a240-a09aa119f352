package ${package.Entity};
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
#if(!${entityLombokModel}&&${cfg.enableGuava})
#end
#if(${swagger2})
#end
#if(${entityLombokModel})
#end

/**
 * $!{table.comment}实体类
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${entityLombokModel})
@Data
    #if(${superEntityClass})
    @EqualsAndHashCode(callSuper = false)
    #else
    @EqualsAndHashCode(callSuper = false)
    #end
@Accessors(chain = true)
#end
#if(${table.convert})
@TableName("${table.name}")
#end
#if(${swagger2})
@ApiModel(value = "${entity}对象", description = "$!{table.comment}")
#end
#if(${superEntityClass})
public class ${entity} extends ${superEntityClass}#if(${activeRecord})<${entity}>#end {
#elseif(${activeRecord})
public class ${entity} extends Model<${entity}> {
#else
public class ${entity} implements Serializable{
#end
#if(${activeRecord})
private static final long serialVersionUID=1L;
#end
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})

    #if(${field.keyFlag})
        #set($keyPropertyName=${field.propertyName})
    #end
    #if("$field.comment" != "" && "$cfg.showFiledCommon" == true)
        #if(${swagger2})
        @ApiModelProperty(value = "${field.comment}")
        #else
        /**
         * ${field.comment}
         */
        #end
    #end
    #if(${field.keyFlag})
        #if(${field.keyIdentityFlag})
        @TableId(value = "${field.name}", type = IdType.AUTO)
        #elseif(!$null.isNull(${idType}) && "$!idType" != "")
        @TableId(value = "${field.name}", type = IdType.${idType})
        #elseif(${field.convert})
        @TableId("${field.name}")
        #else
        @TableId("${field.name}")
        #end
    #elseif(${field.fill})
        #if(${field.convert})
        @TableField(value = "${field.name}", fill = FieldFill.${field.fill})
        #else
        @TableField(fill = FieldFill.${field.fill})
        #end
    #elseif(${field.convert})
    @TableField("${field.name}")
    #end
    #if(${versionFieldName}==${field.name})
    @Version
    #end
    #if(${logicDeleteFieldName}==${field.name})
    @TableLogic
    #end
private ${field.propertyType} ${field.propertyName};
#end
## ----------  END 字段循环遍历  ----------

#if(!${entityLombokModel})
    #foreach($field in ${table.fields})
        #if(${field.propertyType.equals("boolean")})
            #set($getprefix="is")
            #set($setprefix="set")
        #elseif(${field.propertyType.equals("Boolean")})
            #set($getprefix="getIs")
            #set($setprefix="setIs")
        #else
            #set($getprefix="get")
            #set($setprefix="set")
        #end

    public ${field.propertyType} ${getprefix}${field.capitalName}(){
        return ${field.propertyName};
        }

        #if(${entityBuilderModel})
        public ${entity} ${setprefix}${field.capitalName}(${field.propertyType} ${field.propertyName}){
        #else
        public void ${setprefix}${field.capitalName}(${field.propertyType} ${field.propertyName}) {
        #end
        this.${field.propertyName} = ${field.propertyName};
        #if(${entityBuilderModel})
            return this;
        #end
        }
    #end
#end

#if(${entityColumnConstant})
    #foreach($field in ${table.fields})
    public static final String ${field.name.toUpperCase()} ="${field.name}";

    #end
#end
#if(${activeRecord})
@Override
protected Serializable pkVal(){
    #if(${keyPropertyName})
        return this.${keyPropertyName};
    #else
        return null;
    #end
    }

#end
#if(${cfg.equalsAndHashCodeFields.size()} > 0)
    #set($equalsAndHashCodeFieldsSize = ${cfg.equalsAndHashCodeFields.size()})
@Override
public boolean equals(Object o){
    if(this==o)return true;
    if(o==null||getClass()!=o.getClass())return false;
    if(!super.equals(o))return false;
    ${entity} target=(${entity})o;
    #foreach($field in ${cfg.equalsAndHashCodeFields})
        #if($foreach.count == 1)        return Objects.equal(${field},target.${field})#if($foreach.count < ${equalsAndHashCodeFieldsSize}) &&#end
        #else
            Objects.equal(${field},target.${field})#if($foreach.count < ${equalsAndHashCodeFieldsSize}) &&#end
        #end
    #end;
    }

@Override
public int hashCode(){
    return Objects.hashCode(super.hashCode(), #foreach($field in ${cfg.equalsAndHashCodeFields})${field}#if($foreach.count < ${equalsAndHashCodeFieldsSize}), #end#end);
    }

#end
#if(!${entityLombokModel} && !${cfg.enableGuava})
@Override
public String toString(){
    return"${entity}{" +
    #foreach($field in ${table.fields})
        #if($!{foreach.index}==0)
            "${field.propertyName}=" + ${field.propertyName} +
        #else
            ", ${field.propertyName}=" + ${field.propertyName} +
        #end
    #end
    "}";
    }
#end
#if(!${entityLombokModel} && ${cfg.enableGuava})
@Override
public String toString() {
    return MoreObjects.toStringHelper(this)
    #foreach($field in ${table.fields})
        .add("${field.propertyName}", ${field.propertyName})
    #end
    #foreach($field in ${table.commonFields})
        .add("${field.propertyName}", ${field.propertyName})
    #end
    .toString();
    }
#end
    }



