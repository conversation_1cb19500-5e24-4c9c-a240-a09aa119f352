package cn.casair.service.impl;

import cn.casair.domain.HrRegistrationDetailsEvaluation;
import cn.casair.dto.HrRegistrationDetailsEvaluationDTO;
import cn.casair.mapper.HrRegistrationDetailsEvaluationMapper;
import cn.casair.repository.HrRegistrationDetailsEvaluationRepository;
import cn.casair.service.HrRegistrationDetailsEvaluationService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrRegistrationDetailsEvaluationServiceImpl extends ServiceImpl<HrRegistrationDetailsEvaluationRepository, HrRegistrationDetailsEvaluation>implements HrRegistrationDetailsEvaluationService {


    private final HrRegistrationDetailsEvaluationRepository hrRegistrationDetailsEvaluationRepository;
    private final HrRegistrationDetailsEvaluationMapper hrRegistrationDetailsEvaluationMapper;

    public HrRegistrationDetailsEvaluationServiceImpl(HrRegistrationDetailsEvaluationRepository hrRegistrationDetailsEvaluationRepository, HrRegistrationDetailsEvaluationMapper hrRegistrationDetailsEvaluationMapper){
    this.hrRegistrationDetailsEvaluationRepository = hrRegistrationDetailsEvaluationRepository;
    this.hrRegistrationDetailsEvaluationMapper= hrRegistrationDetailsEvaluationMapper;
    }

    /**
     * 创建
     * @param hrRegistrationDetailsEvaluationDTO
     * @return
     */
    @Override
    public HrRegistrationDetailsEvaluationDTO createHrRegistrationDetailsEvaluation(HrRegistrationDetailsEvaluationDTO hrRegistrationDetailsEvaluationDTO){
    log.info("Create new HrRegistrationDetailsEvaluation:{}", hrRegistrationDetailsEvaluationDTO);

    HrRegistrationDetailsEvaluation hrRegistrationDetailsEvaluation =this.hrRegistrationDetailsEvaluationMapper.toEntity(hrRegistrationDetailsEvaluationDTO);
    this.hrRegistrationDetailsEvaluationRepository.insert(hrRegistrationDetailsEvaluation);
    return this.hrRegistrationDetailsEvaluationMapper.toDto(hrRegistrationDetailsEvaluation);
    }

    /**
     * 修改
     * @param hrRegistrationDetailsEvaluationDTO
     * @return
     */
    @Override
    public Optional<HrRegistrationDetailsEvaluationDTO>updateHrRegistrationDetailsEvaluation(HrRegistrationDetailsEvaluationDTO hrRegistrationDetailsEvaluationDTO){
    return Optional.ofNullable(this.hrRegistrationDetailsEvaluationRepository.selectById(hrRegistrationDetailsEvaluationDTO.getId()))
    .map(roleTemp->{
    HrRegistrationDetailsEvaluation hrRegistrationDetailsEvaluation =this.hrRegistrationDetailsEvaluationMapper.toEntity(hrRegistrationDetailsEvaluationDTO);
    this.hrRegistrationDetailsEvaluationRepository.updateById(hrRegistrationDetailsEvaluation);
    log.info("Update HrRegistrationDetailsEvaluation:{}", hrRegistrationDetailsEvaluationDTO);
    return hrRegistrationDetailsEvaluationDTO;
    });
    }

    /**
     * 查询详情
     * @param id
     * @return
     */
    @Override
    public HrRegistrationDetailsEvaluationDTO getHrRegistrationDetailsEvaluation(String id){
    log.info("Get HrRegistrationDetailsEvaluation :{}",id);

    HrRegistrationDetailsEvaluation hrRegistrationDetailsEvaluation =this.hrRegistrationDetailsEvaluationRepository.selectById(id);
    return this.hrRegistrationDetailsEvaluationMapper.toDto(hrRegistrationDetailsEvaluation);
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void deleteHrRegistrationDetailsEvaluation(String id){
    Optional.ofNullable(this.hrRegistrationDetailsEvaluationRepository.selectById(id))
    .ifPresent(hrRegistrationDetailsEvaluation ->{
    this.hrRegistrationDetailsEvaluationRepository.deleteById(id);
    log.info("Delete HrRegistrationDetailsEvaluation:{}", hrRegistrationDetailsEvaluation);
    });
    }

    /**
     * 批量删除
     * @param ids
     */
    @Override
    public void deleteHrRegistrationDetailsEvaluation(List<String>ids){
    log.info("Delete HrRegistrationDetailsEvaluations:{}",ids);
    this.hrRegistrationDetailsEvaluationRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询
     * @param hrRegistrationDetailsEvaluationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrRegistrationDetailsEvaluationDTO hrRegistrationDetailsEvaluationDTO,Long pageNumber,Long pageSize){
    Page<HrRegistrationDetailsEvaluation>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrRegistrationDetailsEvaluation>qw=new QueryWrapper<>(this.hrRegistrationDetailsEvaluationMapper.toEntity(hrRegistrationDetailsEvaluationDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrRegistrationDetailsEvaluationRepository.selectPage(page,qw);
    iPage.setRecords(this.hrRegistrationDetailsEvaluationMapper.toDto(iPage.getRecords()));
    return iPage;
    }
}
