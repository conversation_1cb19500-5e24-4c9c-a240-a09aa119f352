package cn.casair.repository;
import cn.casair.domain.HrApplyChecker;
import cn.casair.dto.HrApplyCheckerDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 审核明细数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Repository
public interface HrApplyCheckerRepository extends BaseMapper<HrApplyChecker> {

    List<HrApplyCheckerDTO> findApplyCheckList(String id);

    List<HrApplyCheckerDTO> findApplyChecker(String applyId);

}
