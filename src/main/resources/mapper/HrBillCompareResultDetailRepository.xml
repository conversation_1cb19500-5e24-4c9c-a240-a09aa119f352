<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillCompareResultDetailRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, bill_compare_result_id, staff_id, id_no, staff_name, system_num, unit_number, client_name, center_name, area, pay_year, pay_monthly, system_data, source_data, compare_result,
        is_delete,created_by,created_date,last_modified_by,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillCompareResultDetail">
        <id column="id" property="id"/>
        <result column="bill_compare_result_id" property="billCompareResultId"/>
        <result column="staff_id" property="staffId"/>
        <result column="id_no" property="idNo"/>
        <result column="staff_name" property="staffName"/>
        <result column="system_num" property="systemNum"/>
        <result column="unit_number" property="unitNumber"/>
        <result column="client_name" property="clientName"/>
        <result column="center_name" property="centerName"/>
        <result column="area" property="area"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="system_data" property="systemData"/>
        <result column="source_data" property="sourceData"/>
        <result column="compare_result" property="compareResult"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectHrBillCompareResultDetailByBillCompareResultId" resultType="cn.casair.dto.HrBillCompareResultDetailDTO">
        SELECT
            hbcrd.*,
            hts.client_id
        FROM
            hr_bill_compare_result_detail hbcrd
            LEFT JOIN hr_talent_staff hts ON hts.id = hbcrd.staff_id
        WHERE
            hbcrd.is_delete = 0 AND bill_compare_result_id = #{compareResultId}
    </select>

    <select id="selectDiffBatchId" resultType="cn.casair.dto.HrBillCompareResultDetailDTO">
        SELECT
            hc.client_name,
            hc.unit_number,
            hp.agreement_number,
            crd.staff_name,
            crd.id_no,
            crd.used_type,
            hts.staff_status,
            hpa1.account_number accountFundNumber,
            hpa2.account_number medicalInsuranceNumber,
            hpa3.account_number socialSecurityNumber
        FROM
            hr_bill_compare_result_detail crd
        LEFT JOIN hr_talent_staff hts ON hts.certificate_num = crd.id_no AND hts.is_delete = 0
        LEFT JOIN hr_client hc ON hts.client_id = hc.id AND hc.is_delete = 0
        LEFT JOIN hr_protocol hp ON hp.client_id = hc.id AND hp.is_delete = 0 AND hp.use_status = 1
        LEFT JOIN hr_platform_account hpa1 ON hc.provident_fund_account_id = hpa1.id AND hpa1.is_delete = 0
        LEFT JOIN hr_platform_account hpa2 ON hc.medical_insurance_account_id = hpa2.id AND hpa2.is_delete = 0
        LEFT JOIN hr_platform_account hpa3 ON hc.social_security_account_id = hpa3.id AND hpa3.is_delete = 0
        WHERE crd.is_delete = 0 AND crd.used_type != 1 AND crd.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        ORDER BY crd.used_type ASC
    </select>

    <select id="findList" resultType="cn.casair.dto.HrBillCompareResultDetailDTO">
        SELECT
            hb.pay_year,
            hb.pay_monthly,
            crd.*,
            hts.client_id,
            bcc.type
        FROM
            hr_bill_compare_result_detail crd
            LEFT JOIN hr_talent_staff hts ON hts.id = crd.staff_id
            LEFT JOIN hr_bill_compare_result bcr ON bcr.id = crd.bill_compare_result_id
            LEFT JOIN hr_bill_compare_config bcc ON bcc.id = bcr.bill_compare_config_id
            LEFT JOIN hr_bill hb ON hb.id = bcc.bill_id
        WHERE
            crd.is_delete = 0
            AND bcr.is_delete = 0
            AND bcc.is_delete = 0
            AND hb.is_delete = 0
            AND bcc.type IN (0,1,2)
        <if test="param.staffName != null and param.staffName != ''">
            AND crd.staff_name LIKE CONCAT('%', #{param.staffName}, '%')
        </if>
        <if test="param.idNo != null and param.idNo != ''">
            AND crd.id_no LIKE CONCAT('%', #{param.idNo}, '%')
        </if>
        <if test="param.payYear != null">
            AND crd.pay_year = #{param.payYear}
        </if>
        <if test="param.payMonthly != null">
            AND crd.pay_monthly = #{param.payMonthly}
        </if>
        <if test="param.ids != null and param.ids.size() > 0">
            AND crd.id IN
            <foreach collection="param.ids" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="param.clientIds != null and param.clientIds.size() > 0">
            AND hts.client_id IN
            <foreach collection="param.clientIds" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
        ORDER BY crd.pay_year, crd.pay_monthly DESC
    </select>

    <select id="selectResultDetailBatchResultId" resultType="cn.casair.dto.HrBillCompareResultDetailDTO">
        SELECT
            hbcrd.*,
            hts.client_id
        FROM
            hr_bill_compare_result_detail hbcrd
            LEFT JOIN hr_talent_staff hts ON hts.id = hbcrd.staff_id
        WHERE
            hbcrd.is_delete = 0 AND bill_compare_result_id IN
            <foreach collection="resultIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>

    <update id="delByResultId">
        UPDATE hr_bill_compare_result_detail SET is_delete = 1, last_modified_date = now() WHERE bill_compare_result_id IN
        <foreach collection="resultIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateBatch">
        <foreach collection="list" item="param" index = "index" open="" close = "" separator=";">
            UPDATE  hr_bill_compare_result_detail SET
                bill_compare_result_id = #{param.billCompareResultId},
                staff_id = #{param.staffId},
                id_no = #{param.idNo},
                staff_name = #{param.staffName},
                system_num = #{param.systemNum},
                unit_number = #{param.unitNumber},
                client_name = #{param.clientName},
                center_name = #{param.centerName},
                area = #{param.area},
                unit_no = #{param.unitNo},
                pay_year = #{param.payYear},
                pay_monthly = #{param.payMonthly},
                system_data = #{param.systemData},
                source_data = #{param.sourceData},
                compare_result = #{param.compareResult},
                system_total = #{param.systemTotal},
                source_total = #{param.sourceTotal},
                compare_total = #{param.compareTotal},
                used_type = #{param.usedType},
                last_modified_date = now()
            WHERE id = #{param.id} AND is_delete = 0
        </foreach>
    </update>

    <insert id="saveBatch">
        INSERT INTO hr_bill_compare_result_detail (
            id, bill_compare_result_id, staff_id, id_no, staff_name, system_num, unit_number, client_name, center_name, area, pay_year, pay_monthly,
            system_data, source_data, compare_result, system_total, source_total, compare_total, used_type, created_date, unit_no
        ) VALUES
        <foreach collection ="list" item="param" index= "index" separator =",">
            (
                #{param.id},  #{param.billCompareResultId},  #{param.staffId},  #{param.idNo},  #{param.staffName},  #{param.systemNum}, #{param.unitNumber},
                #{param.clientName},  #{param.centerName},  #{param.area},  #{param.payYear},  #{param.payMonthly}, #{param.systemData},  #{param.sourceData},
                #{param.compareResult},  #{param.systemTotal},  #{param.sourceTotal},  #{param.compareTotal}, #{param.usedType}, now(), #{param.unitNo}
            )
        </foreach>
    </insert>
</mapper>
