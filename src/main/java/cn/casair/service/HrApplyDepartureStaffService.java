package cn.casair.service;

import cn.casair.domain.HrApplyDepartureStaff;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrAppendixDTO;
import cn.casair.dto.HrApplyDepartureStaffDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.itextpdf.text.DocumentException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 待离职员工服务类
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
public interface HrApplyDepartureStaffService extends IService<HrApplyDepartureStaff> {


    /**
     * 创建待离职员工
     *
     * @param hrApplyDepartureStaffDTO
     * @return
     */
    String createHrApplyDepartureStaff(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO);

    /**
     * 修改待离职员工
     *
     * @param hrApplyDepartureStaffDTO
     * @return
     */
    Optional<HrApplyDepartureStaffDTO> updateHrApplyDepartureStaff(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO);

    /**
     * 查询待离职员工详情
     *
     * @param id
     * @return
     */
    HrApplyDepartureStaffDTO getHrApplyDepartureStaff(String id);

    /**
     * 删除待离职员工
     *
     * @param id
     */
    void deleteHrApplyDepartureStaff(String id);

    /**
     * 批量删除待离职员工
     *
     * @param ids
     */
    void deleteHrApplyDepartureStaff(List<String> ids);

    /**
     * 分页查询待离职员工
     *
     * @param hrApplyDepartureStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO, Long pageNumber, Long pageSize);

    /**
     * 客户审核
     * @param batchOptDTO
     * @return
     */
    ResponseEntity auditInForStaff(BatchOptDTO batchOptDTO);

    /**
     * 小程序创建离职申请
     * @param hrApplyDepartureStaffDTO
     * @return
     */
    HrApplyDepartureStaffDTO saveHrApplyDepartureStaff(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO);

    /**
     * 查询待离职员工信息
     * @param departureId 离职服务ID
     * @param departureApplyStatus 待离职员工审核结果
     * @param departureStaffIds 待离职员工ID
     * @return 待离职员工信息
     */
    List<HrApplyDepartureStaffDTO> viewResignationInFor(String departureId, Integer departureApplyStatus, List<String> departureStaffIds);

    /**
     * 经理审核--离职服务查询审核
     * @param batchOptDTO
     * @return
     */
    ResponseEntity managerReviewStaff(BatchOptDTO batchOptDTO);

    /**
     * 离职通知
     * @param batchOptDTO
     * @return
     */
    ResponseEntity resignationNoticeStaff(BatchOptDTO batchOptDTO);

    /**
     * 首頁：统计当前年每月离职数量
     * @param particularYear 年份
     * @return
     */
    List<Map<String, Object>> resignedCountByYear(String particularYear);

    /**
     * 导出待离职员工列表
     * @param hrApplyDepartureStaffDTO 待离职员工ID
     * @param response
     * @return
     */
    String exportDepartureStaff(HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO, HttpServletResponse response);

    /**
     * 获取辞职申请书模板
     * @return
     */
    HrAppendixDTO resignationApplyTemplate();

    /**
     * 上传辞职申请书
     * @param file
     * @param response
     */
    HrAppendixDTO pictureTransferPDF(MultipartFile file, HttpServletResponse response) throws IOException, DocumentException;

    /**
     * 员工查看进度
     * @return
     */
    HrApplyDepartureStaffDTO getResignationProgressStaff();

    /**
     * 审核线上电签信息
     * @param batchOptDTO
     * @return
     */
    ResponseEntity auditOnlineSignInFor(BatchOptDTO batchOptDTO);

    /**
     * 审核线下电签信息
     * @param batchOptDTO
     * @return
     */
    ResponseEntity auditOfflineSignInFor(BatchOptDTO batchOptDTO);

    /**
     * 检查辞职申请书签订状态
     * @param params
     * @return
     */
    Map<String, Object> checkDepartureItemState(Map<String, Object> params);

}
