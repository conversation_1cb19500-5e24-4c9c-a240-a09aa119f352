<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrApplyDepartureRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, client_id, protocol_id, staff_num, apply_status, appendix_ids, apply_remark,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrApplyDeparture">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="protocol_id" property="protocolId"/>
        <result column="staff_num" property="staffNum"/>
        <result column="apply_status" property="applyStatus"/>
        <result column="appendix_ids" property="appendixIds"/>
        <result column="apply_remark" property="applyRemark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="findSql">
        SELECT
            hc.unit_number, hc.client_name, had.*, su.real_name AS specialized
        FROM
            hr_apply_departure had
        LEFT JOIN hr_client hc ON had.client_id = hc.id AND hc.is_delete = 0
        LEFT JOIN sys_user su ON hc.specialized_id = su.id AND su.is_delete = 0 AND su.user_status = 1
    </sql>

    <select id="findPage" resultType="cn.casair.dto.HrApplyDepartureDTO">
        <include refid="findSql"/>
        WHERE had.is_delete = 0
        <if test="param.unitNumber != null and param.unitNumber != ''">
            AND hc.unit_number LIKE concat('%', #{param.unitNumber}, '%')
        </if>
        <if test="param.clientId != null">
            AND hc.id = #{param.clientId}
        </if>
        <if test="param.staffNum != null">
            AND had.staff_num = #{param.staffNum}
        </if>
        <if test="param.createdDateStart!=null">
            AND had.created_date &gt;= #{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd!=null">
            AND had.created_date &lt; date_add(#{param.createdDateEnd}, interval 1 day)
        </if>
        <if test="param.applyStatusList!=null and param.applyStatusList.size() > 0">
            AND had.apply_status IN
            <foreach collection="param.applyStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.userIdList!=null and param.userIdList.size() > 0">
            AND su.id IN
            <foreach collection="param.userIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.userId != null">
            AND su.id = #{param.userId}
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hc.id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            ORDER BY  ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY had.created_date DESC
        </if>
    </select>

    <select id="findById" resultType="cn.casair.dto.HrApplyDepartureDTO">
        <include refid="findSql"/>
        WHERE had.is_delete = 0 AND had.id = #{id}
    </select>

</mapper>
