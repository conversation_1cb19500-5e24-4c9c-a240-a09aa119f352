package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.common.utils.StringUtils;
import cn.casair.domain.HrClient;
import cn.casair.domain.HrPlatformAccount;
import cn.casair.dto.HrPlatformAccountDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrPlatformAccountTemplate;
import cn.casair.mapper.HrPlatformAccountMapper;
import cn.casair.repository.HrPlatformAccountRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrClientService;
import cn.casair.service.HrPlatformAccountService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.component.asynchronous.AccumulationFoundComponent;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 平台账户表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrPlatformAccountServiceImpl extends ServiceImpl<HrPlatformAccountRepository, HrPlatformAccount> implements HrPlatformAccountService {

    private static final Logger log = LoggerFactory.getLogger(HrPlatformAccountServiceImpl.class);

    private final HrPlatformAccountRepository hrPlatformAccountRepository;

    private final HrPlatformAccountMapper hrPlatformAccountMapper;

    private final HrAppendixService hrAppendixService;

    private final SysOperLogService sysOperLogService;

    @Autowired
    private HrClientService hrClientService;

    @Autowired
    private CodeTableServiceImpl codeTableService;

    private static HashMap<String, String> platformTypeHashMap;

    @Value("${file.temp-path}")
    private String fileTempPath;

    @Autowired
    private RedisCache redisCache;

    @Value("${minio.excelPrefix}")
    private String excelPrefix;

    @Autowired
    private AccumulationFoundComponent accumulationFoundComponent;


    public HrPlatformAccountServiceImpl(HrPlatformAccountRepository hrPlatformAccountRepository, HrPlatformAccountMapper hrPlatformAccountMapper, HrAppendixService hrAppendixService, SysOperLogService sysOperLogService) {
        this.hrPlatformAccountRepository = hrPlatformAccountRepository;
        this.hrPlatformAccountMapper = hrPlatformAccountMapper;
        this.hrAppendixService = hrAppendixService;
        this.sysOperLogService = sysOperLogService;
    }


    /**
     * 创建平台账户表
     *
     * @param hrPlatformAccountDTO
     * @return
     */
    @Override
    public HrPlatformAccountDTO createHrPlatformAccount(HrPlatformAccountDTO hrPlatformAccountDTO) {
        log.info("Create new HrPlatformAccount:{}", hrPlatformAccountDTO);

        HrPlatformAccount hrPlatformAccount = this.hrPlatformAccountMapper.toEntity(hrPlatformAccountDTO);
        //账户号相同和账户类型不能重复
        List<HrPlatformAccount> list = getHrPlatformAccounts(hrPlatformAccountDTO);
        if (CollectionUtils.isNotEmpty(list)){
            throw new CommonException("同种账户类型，账户号不可以重复！");
        }
        this.hrPlatformAccountRepository.insert(hrPlatformAccount);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.PLATFORM_ACCOUNT.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrPlatformAccountDTO),
            HrPlatformAccountDTO.class,
            null,
            JSON.toJSONString(hrPlatformAccountDTO)
        );
        return this.hrPlatformAccountMapper.toDto(hrPlatformAccount);
    }

    /**
     * 账户号相同和账户类型不能重复
     * @param hrPlatformAccountDTO
     * @return
     */
    private List<HrPlatformAccount> getHrPlatformAccounts(HrPlatformAccountDTO hrPlatformAccountDTO) {
        QueryWrapper<HrPlatformAccount> hrPlatformAccountQueryWrapper = new QueryWrapper<>();
        hrPlatformAccountQueryWrapper.eq("account_type", hrPlatformAccountDTO.getAccountType());
        hrPlatformAccountQueryWrapper.eq("account_number", hrPlatformAccountDTO.getAccountNumber());
        List<HrPlatformAccount> list = list(hrPlatformAccountQueryWrapper);
        return list;
    }

    /**
     * 修改平台账户表
     *
     * @param hrPlatformAccountDTO
     * @return
     */
    @Override
    public Optional<HrPlatformAccountDTO> updateHrPlatformAccount(HrPlatformAccountDTO hrPlatformAccountDTO) {
        return Optional.ofNullable(this.hrPlatformAccountRepository.selectById(hrPlatformAccountDTO.getId()))
            .map(roleTemp -> {
                if (!hrPlatformAccountDTO.getAccountType().equals(roleTemp.getAccountType()) || !hrPlatformAccountDTO.getAccountNumber().equals(roleTemp.getAccountNumber())){
                    //账户号相同和账户类型不能重复
                    List<HrPlatformAccount> list = getHrPlatformAccounts(hrPlatformAccountDTO);
                    if (CollectionUtils.isNotEmpty(list)){
                        throw new CommonException("同种账户类型，账户号不可以重复！");
                    }
                }
                HrPlatformAccount hrPlatformAccount = this.hrPlatformAccountMapper.toEntity(hrPlatformAccountDTO);
                this.hrPlatformAccountRepository.updateById(hrPlatformAccount);
                log.info("Update HrPlatformAccount:{}", hrPlatformAccountDTO);

                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.PLATFORM_ACCOUNT.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrPlatformAccountDTO),
                    HrPlatformAccountDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrPlatformAccountDTO),
                    null,
                    HrPlatformAccountDTO.class
                );
                return hrPlatformAccountDTO;
            });
    }

    /**
     * 查询平台账户表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrPlatformAccountDTO getHrPlatformAccount(String id) {
        log.info("Get HrPlatformAccount :{}", id);

        HrPlatformAccount hrPlatformAccount = this.hrPlatformAccountRepository.selectById(id);
        return this.hrPlatformAccountMapper.toDto(hrPlatformAccount);
    }

    /**
     * 删除平台账户表
     *
     * @param id
     */
    @Override
    public void deleteHrPlatformAccount(String id) {
        Optional.ofNullable(this.hrPlatformAccountRepository.selectById(id))
            .ifPresent(hrPlatformAccount -> {
                this.hrPlatformAccountRepository.deleteById(id);
                log.info("Delete HrPlatformAccount:{}", hrPlatformAccount);
            });
    }

    /**
     * 批量删除平台账户表
     *
     * @param ids
     */
    @Override
    public void deleteHrPlatformAccount(List<String> ids) {
        log.info("Delete HrPlatformAccounts:{}", ids);
        List<HrPlatformAccount> hrPlatformAccountList = hrPlatformAccountRepository.selectBatchIds(ids);
        this.hrPlatformAccountRepository.deleteBatchIds(ids);
        // 操作日志
        //this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.PLATFORM_ACCOUNT.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> collect = hrPlatformAccountList.stream().map(HrPlatformAccount::getAccountType).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.PLATFORM_ACCOUNT.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除平台账号: "+JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(hrPlatformAccountList),
            jwtUserDTO
        );
    }

    /**
     * 分页查询平台账户表
     *
     * @param hrPlatformAccountDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrPlatformAccountDTO hrPlatformAccountDTO, Long pageNumber, Long pageSize) {
        Page<HrPlatformAccount> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrPlatformAccount> qw = new QueryWrapper<>();
        qw.in(CollectionUtils.isNotEmpty(hrPlatformAccountDTO.getAccountTypeList()), "account_type", hrPlatformAccountDTO.getAccountTypeList());
        qw.like(StringUtils.isNotBlank(hrPlatformAccountDTO.getAccountNumber()),"account_number",hrPlatformAccountDTO.getAccountNumber());
        //排序
        if (StringUtils.isNotBlank(hrPlatformAccountDTO.getOrder())) {
            if (hrPlatformAccountDTO.getOrder().equals("DESC")) {
                qw.orderBy(StringUtils.isNotBlank(hrPlatformAccountDTO.getField()), false, hrPlatformAccountDTO.getField());
            } else {
                qw.orderBy(StringUtils.isNotBlank(hrPlatformAccountDTO.getField()), true, hrPlatformAccountDTO.getField());
            }

        }

        IPage iPage = this.hrPlatformAccountRepository.selectPage(page, qw);
        List<HrPlatformAccountDTO> list = this.hrPlatformAccountMapper.toDto(iPage.getRecords());
        for (HrPlatformAccountDTO platformAccountDTO : list) {
            QueryWrapper<HrClient> hrClientQueryWrapper = new QueryWrapper<>();
            String accountType = platformAccountDTO.getPlatformType();
            //判断是哪个类型的账户
            if ("1".equals(accountType)) {
                hrClientQueryWrapper.eq("social_security_account_id", platformAccountDTO.getId());
            }
            if ("2".equals(accountType)) {
                hrClientQueryWrapper.eq("medical_insurance_account_id", platformAccountDTO.getId());
            }
            if ("3".equals(accountType)) {
                hrClientQueryWrapper.eq("provident_fund_account_id", platformAccountDTO.getId());
            }
            if ("4".equals(accountType)) {
                hrClientQueryWrapper.eq("payroll_account_id", platformAccountDTO.getId());
            }
            int count = hrClientService.count(hrClientQueryWrapper);
            platformAccountDTO.setClientNumber(count);
        }

        iPage.setRecords(list);
        return iPage;
    }

    @Override
    public String exportPlatform(HrPlatformAccountDTO hrPlatformAccountDTO, HttpServletResponse httpServletResponse) {
        QueryWrapper<HrPlatformAccount> qw = new QueryWrapper<>();
        qw.in(CollectionUtils.isNotEmpty(hrPlatformAccountDTO.getAccountTypeList()), "account_type", hrPlatformAccountDTO.getAccountTypeList());
        qw.like(StringUtils.isNotBlank(hrPlatformAccountDTO.getAccountNumber()),"account_number",hrPlatformAccountDTO.getAccountNumber());
        qw.in(CollectionUtils.isNotEmpty(hrPlatformAccountDTO.getIds()),"id",hrPlatformAccountDTO.getIds());
        qw.eq("is_delete",0);
        List<HrPlatformAccount> list = this.hrPlatformAccountRepository.selectList(qw);
        if (list.isEmpty()) {
            throw new CommonException("未查到数据");
        }
        ArrayList<HrPlatformAccountTemplate> hrPlatformAccountTemplates = new ArrayList<>();
        for (HrPlatformAccount hrPlatformAccount : list) {
            HrPlatformAccountTemplate hrPlatformAccountTemplate = new HrPlatformAccountTemplate();
            BeanUtils.copyProperties(hrPlatformAccount, hrPlatformAccountTemplate);
            hrPlatformAccountTemplates.add(hrPlatformAccountTemplate);
        }
        int listSize = hrPlatformAccountTemplates.size();
        List<String> ids = list.stream().map(HrPlatformAccount::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(hrPlatformAccountTemplates, "平台账号管理", HrPlatformAccountTemplate.class);
        // 操作日志
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.PLATFORM_ACCOUNT.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    @Override
    public String importPlatform(MultipartFile file) {

        // 设置进度条
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.PLATFORM_ACCOUNT.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.accumulationFoundComponent.platformAccountImport(inputStream, redisKey, jwtUserDTO);
        return redisKey;
//        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
//
//        ExcelImportResult<HrPlatformAccountDTO> result =
//            ExcelUtils.importExcel(file, 0, 1, true, HrPlatformAccountDTO.class);
////        int size = result.getList().size();
////        if (size == 0 || size > 1000) {
////            throw new CommonException("最少导入一条数据，最多导入1000条数据");
////        }
//        //进度条
//        String key = jwtUserDTO.getId() + "Talent" + redisKey;
//        redisCache.setCacheObject(key, 0, 10, TimeUnit.MINUTES);
//        try {
//            //导入数据
//            this.saveData(result, key);
//        } catch (Exception e) {
//            redisCache.deleteObject(key);
//            throw new CommonException(e.getMessage());
//        }
//        ImportResultDTO resultDTO;
//        try {
//            resultDTO = ImportResultUtils.writeErrorFile("平台账号管理" + System.currentTimeMillis(), HrPlatformAccountDTO.class, result, fileTempPath);
//            // 判断是否需要上传错误文件
//            if (resultDTO.getFailureFileUrl() != null) {
//                String fileUrl = this.hrAppendixService.uploadErrorImportFile(resultDTO.getFailureFileUrl());
//                resultDTO.setFailureFileUrl(fileUrl);
//            }
//        } catch (IOException e) {
//            log.error("平台账号管理入异常:{}", e.getMessage());
//            return ResponseUtil.buildError(e.getMessage());
//        } finally {
//            redisCache.deleteObject(key);
//        }
//        // 操作日志
//        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.PLATFORM_ACCOUNT.getValue(), BusinessTypeEnum.IMPORT.getKey(), file, JSON.toJSONString(resultDTO), ImportResultDTO.class);
//        return ResponseUtil.buildSuccess(resultDTO);
    }

    @Override
    public String importPlatformTemplate(HttpServletResponse response) {
//        ExportParams params = new ExportParams(null, "平台账号");
//        //params.setStyle(ExcelStyleUtils.class);
//        params.setType(ExcelType.XSSF);
//        List<HrPlatformAccountTemplate> list = new ArrayList<>();
//        HrPlatformAccountTemplate hrPlatformAccountTemplate = new HrPlatformAccountTemplate();
//        hrPlatformAccountTemplate.setAccountType("公积金缴纳账户");
//        hrPlatformAccountTemplate.setAccountNumber("623xxx");
//        hrPlatformAccountTemplate.setPassword("qwexxx");
//        hrPlatformAccountTemplate.setIssuingBank("中国建设银行");
//        list.add(hrPlatformAccountTemplate);
//        ZipSecureFile.setMinInflateRatio(-1.0d);
//        Workbook workbook = ExcelExportUtil.exportExcel(params, HrPlatformAccountTemplate.class, list);
//        String[] bank = {"中国建设银行", "青岛银行", "中国农业银行", "招商银行", "青岛农商银行", "中国工商银行", "招商银行", "交通银行", "中国银行", "中国民生银行", "浦发银行", "中国邮政储蓄银行", "中信银行", "广发银行", "平安银行", "华夏银行", "渤海银行", "恒丰银行", "兴业银行", "浙商银行", "其他"};
//        ExcelUtils.selectList(workbook, 1, 1, bank, "隐藏", 1, true);
//        String[] type = {"公积金缴纳账户", "社保缴纳账户", "工资发放账户", "医保缴纳账户", "其他"};
//        ExcelUtils.selectList(workbook, 0, 0, type, "隐藏1", 2, true);
//        ExcelUtils.downLoadExcel("平台账号导入模板.xlsx", response, workbook);
        return excelPrefix + "平台账号导入模板.xlsx";
    }

    /**
     * 查询平台账户表
     *
     * @param platformType
     * @return
     */
    @Override
    public List<HrPlatformAccountDTO> findListByPlatformType(String platformType) {
        return this.hrPlatformAccountRepository.findListByPlatformType(platformType);
    }

    static {
        platformTypeHashMap = new HashMap<>();
        platformTypeHashMap.put("社保缴纳账户", "1");
        platformTypeHashMap.put("医保缴纳账户", "2");
        platformTypeHashMap.put("公积金缴纳账户", "3");
        platformTypeHashMap.put("工资发放账户", "4");
        platformTypeHashMap.put("其他", "0");
    }

    /**
     * @param result
     * @param key
     */
    private void saveData(ExcelImportResult<HrPlatformAccountDTO> result, String key) {
        //long l = System.currentTimeMillis();
        int listSize = result.getList().size();
        //Map<String, Integer> map = new HashMap<>(result.getList().size());
        int scale = 0;
        List<HrPlatformAccountDTO> list = result.getList();
        //List<HrPlatformAccount> hrPlatformAccounts = hrPlatformAccountMapper.toEntity(list);

        Map<String, Integer> ownedBank = codeTableService.findCodeTableByInnerValue("ownedBank");
        for (HrPlatformAccountDTO hrPlatformAccount : list) {
//                if ("公积金缴纳账户".equals(hrPlatformAccount.getAccountType()))
//                {
//                    hrPlatformAccount.setPlatformType("3");
//                }
//                if ("社保缴纳账户".equals(hrPlatformAccount.getAccountType()))
//                {
//                    hrPlatformAccount.setPlatformType("1");
//                }
//                if ("医保缴纳账户".equals(hrPlatformAccount.getAccountType()))
//                {
//                    hrPlatformAccount.setPlatformType("2");
//                }
//                if ("工资发放账户".equals(hrPlatformAccount.getAccountType()))
//                {
//                    hrPlatformAccount.setPlatformType("4");
//                }
//                if ("工资发放账户".equals(hrPlatformAccount.getAccountType()))
//                {
//                    hrPlatformAccount.setPlatformType("4");
//                }
            //转换国籍信息
            try {
                //账户号相同和账户类型不能重复
                List<HrPlatformAccount> hrPlatformAccountList = getHrPlatformAccounts(hrPlatformAccount);
                if (CollectionUtils.isNotEmpty(hrPlatformAccountList)){
                    throw new CommonException("同种账户类型，账户号不可以重复！");
                }
                if (StringUtils.isNotBlank(hrPlatformAccount.getIssuingBank())){
                    Integer ownedBankValue = ownedBank.get(hrPlatformAccount.getIssuingBank());
                    if (ownedBankValue == null) {
                        throw new CommonException("银行在系统中不存在!");
                    }
                }
                String plateForm = platformTypeHashMap.get(hrPlatformAccount.getAccountType());
                if (StringUtils.isBlank(plateForm)) {
                    throw new CommonException("暂不支持该账户类型");
                }
                hrPlatformAccount.setPlatformType(plateForm);
                HrPlatformAccount platformAccount = hrPlatformAccountMapper.toEntity(hrPlatformAccount);
                save(platformAccount);
            } catch (Exception e) {
                log.error("保存平台账号异常:{}", e.getMessage());
                hrPlatformAccount.setErrorMsg(e.getMessage());
            } finally {
                scale++;
                int i = CalculateUtils.calculationProgress(scale, listSize);
                redisCache.setCacheObject(key, i, 10, TimeUnit.MINUTES);

            }
        }

    }
}
