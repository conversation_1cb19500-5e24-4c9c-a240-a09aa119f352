package cn.casair.security.data;

import java.lang.reflect.Method;

/**
 * 记录请求方法的线程工具
 *
 * <AUTHOR>
 */
public class IdentityHolder {

    private static final ThreadLocal<Method> METHOD_CONTEXT = new InheritableThreadLocal<>();

    public static Method getMethodContext() {
        if (METHOD_CONTEXT.get() == null) {
            return null;
        }
        return METHOD_CONTEXT.get();
    }

    public static void setContext(Method context) {
        METHOD_CONTEXT.set(context);
    }

    public static void clear() {
        METHOD_CONTEXT.remove();
    }
}
