package cn.casair.service;

import cn.casair.domain.HrClient;
import cn.casair.domain.HrProtocol;
import cn.casair.dto.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户组织架构表服务类
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface HrClientService extends IService<HrClient> {

    /**
     * 检查客户及其次顶级客户是否为特殊客户
     *
     * @return
     */
    List<EnumDTO> specialClient();

    /**
     * 创建客户组织架构表
     *
     * @param hrClientDTO
     * @return
     */
    HrClientDTO createHrClient(HrClientDTO hrClientDTO);

    /**
     * 修改客户组织架构表
     *
     * @param hrClientDTO
     * @return
     */
    HrClientDTO updateHrClient(HrClientDTO hrClientDTO);

    /**
     * 查询客户组织架构表详情
     *
     * @param id
     * @return
     */
    HrsocialDTO getHrClient(String id);

    /**
     * 删除客户组织架构表
     *
     * @param id
     */
    void deleteHrClient(String id);

    /**
     * 批量删除客户组织架构表
     *
     * @param ids
     */
    void deleteHrClient(List<String> ids);

    /**
     * 分页查询客户组织架构表
     *
     * @param hrClientDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrClientDTO hrClientDTO, Long pageNumber, Long pageSize);

    //查询客户组织架构表
    List<HrClientDTO> getselectHrClient(String clientName);

    /**
     * 查询客户组织架构--无数据权限
     * @return
     */
    List<HrClientDTO> notDataPermissionHrClient();

    //客户查询社保类型
    List<HrSocialSecurityDTO> getHrSocialSelectSecurity();

    //客户查询公积金类型
    List<HrAccumulationFundDTO> getHrAccumulationSelectSecurity();

    //客户查询工资发放账户
    List<HrPlatformAccountDTO> getHrPlatformAccount( );

    // 客户查询专管员
    List<UserDTO> getclientsspecialized();

    //查询客户员工详情
    List<HrTalentStaffDTO> getTalentStaff(String id);

    List<HrProtocolDTO> getselectProtocol( );

    /**
     * 所属客户
     * @param id
     * @return
     */
    List<HrClient> getOwnedCustomerList(String id);

    String exportHrStaff(HrClientDTO hrClientDTO, HttpServletResponse response);

    String importHrStaff(MultipartFile file);

    String importHrClientsTemplate(HttpServletResponse response);

    List<HrProtocolDTO> selectProtocolId(String id);

    /**
     * 数据权限接口
     * @return
     */
    List<String> selectClientIdByUserId();

    /**
     * 角色权限控制接口
     */
    List<String> noticeAuthority(String currentRoleKey, String userId);

    List<HrProtocolDTO> getOwnedCustomerListselectclientse(String id);


    List<HrPlatformAccountDTO> getHrPlatformAccountAccount(HrPlatformAccountDTO hrPlatformAccountDTO);

    /**
     * 查询所有的上级客户
     * @param hrClientDTO
     * @param allList
     * @return
     */
    HrClientDTO getParentClient(HrClientDTO hrClientDTO,List<HrClient> allList);

    /**
     * 查询客户的所有子客户
     * @param clientId
     * @return
     */
    List<String> querySubordinateClient(String clientId);

    /**
     * 不分页查询客户列表
     * @param hrClientDTO
     * @return
     */
    List<HrClientDTO> nonFindPage(HrClientDTO hrClientDTO);

    /**
     * 创建客户协议恢复员工禁用状态
     * @param hrProtocol
     */
    void handleStaffStatus(HrProtocol hrProtocol);

    /**
     * 获取上级客户ID
     * @param clientId
     * @return
     */
    List<String> findClientIdList(String clientId);

    /**
     * 返回层级结构树
     * @return
     */
    List<HrClient> getStructure();
}
