package ${package.Controller};

import cn.casair.common.errors.CommonException;
import cn.casair.web.rest.util.HeaderUtil;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import cn.casair.web.rest.util.ResponseUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.casair.common.errors.NotFoundException;
import org.apache.commons.lang3.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import cn.casair.common.errors.CommonException;
import ${cfg.extPackage.Dto}.${entity}DTO;
import ${package.Service}.${table.serviceName};
import java.util.List;

#if(${restControllerStyle})
#else
#end
#if(${superControllerClassPackage})
    ${superControllerClassPackage}
    ;
#end

/**
 * $!{table.comment}资源
 *
 * <AUTHOR>
 * @since ${date}
 */
@Slf4j
#if(${restControllerStyle})
@RestController
#else
@Controller
#end
@RequestMapping("/api")
@RequiredArgsConstructor
#if(${kotlin})
class ${table.controllerName}#if(${superControllerClass}) : ${superControllerClass}()#end

#else
    #if(${superControllerClass})
    public class ${table.controllerName} extends ${superControllerClass} {
    #else
    public class ${table.controllerName} {
    #end

    #foreach($field in ${table.fields})
        #if(${field.keyFlag})
            #set($keyPropertyType=${field.propertyType})
        #end
    #end

## private final Logger log=LoggerFactory.getLogger(${table.controllerName}.class);
private final  ${table.serviceName} ${cfg.serviceProperty};

#*public ${table.controllerName}(${table.serviceName} ${cfg.serviceProperty}){
    this.${cfg.serviceProperty} = ${cfg.serviceProperty};
    }*#

/**
 * POST /${cfg.restControllerPath}
 *
 * 创建$!{table.comment}
 * @param ${cfg.entityProperty}DTO
 * @return
 */
@PostMapping("/${cfg.restControllerPath}")
public ResponseEntity<?> create${entity}(@RequestBody @Valid ${entity}DTO ${cfg.entityProperty}DTO){
    log.info("REST request to save ${entity}:{}", ${cfg.entityProperty}DTO);

    if(${cfg.entityProperty}DTO.getId()!=null){
        throw new CommonException("${cfg.entityProperty}","添加时不能设置Id");
    }
    ${entity}DTO new${entity} =this.${cfg.serviceProperty}.create${entity}(${cfg.entityProperty}DTO);
    return ResponseUtil.buildSuccess(new${entity});
    }

/**
 * PUT /${cfg.restControllerPath}
 *
 * 更新$!{table.comment}
 * @param ${cfg.entityProperty}DTO
 * @return
 */
@PutMapping("/${cfg.restControllerPath}")
public ResponseEntity<?> update${entity}(@RequestBody @Valid ${entity}DTO ${cfg.entityProperty}DTO){
    log.info("REST request to update ${entity}:{}", ${cfg.entityProperty}DTO);

    return this.${cfg.serviceProperty}.update${entity}(${cfg.entityProperty}DTO)
    .map(ResponseUtil::buildSuccess)
    .orElseThrow(()->new NotFoundException("$!{table.comment}不存在"));
    }

/**
 * GET /${cfg.restControllerPath}/:id
 *
 * 查询$!{table.comment}详情
 * @param id
 * @return
 */
@GetMapping("/${cfg.restControllerPath}/{id}")
public ResponseEntity<?> get${entity}(@PathVariable $!{keyPropertyType} id){
    log.info("REST request to get ${entity}:{}",id);

    return ResponseUtil.wrapOrNotFound(this.${cfg.serviceProperty}.get${entity}(id));
    }

/**
 * DELETE /${cfg.restControllerPath}/:id
 *
 * 删除$!{table.comment}
 * @param id
 * @return
 */
@DeleteMapping("/${cfg.restControllerPath}/{id}")
public ResponseEntity<?> delete${entity}(@PathVariable $!{keyPropertyType} id){
    log.info("REST request to delete ${entity}:{}",id);
    this.${cfg.serviceProperty}.delete${entity}(id);
    return ResponseUtil.buildSuccess(HeaderUtil.createAlert("${cfg.entityProperty}.deleted",id.toString()));
    }

/**
 * POST /${cfg.restControllerPath}/deletes
 *
 * 批量删除$!{table.comment}
 * @param ids
 * @return
 */
@PostMapping("/${cfg.restControllerPath}/deletes")
public ResponseEntity<?> delete${entity}(@RequestBody List<$!{keyPropertyType}> ids){
    log.info("Request to delete ${entity}s:{}",ids);
    this.${cfg.serviceProperty}.delete${entity}(ids);
    return ResponseUtil.buildSuccess((HeaderUtil.createAlert("${cfg.entityProperty}.deleted", StringUtils.join(ids.toArray(), ","))));
    }

/**
 * POST /${cfg.restControllerPath}/page
 *
 * 分页查询$!{table.comment}
 * @param ${cfg.entityProperty}DTO
 * @param pageNumber
 * @param pageSize
 * @return
 */
@PostMapping(value = "/${cfg.restControllerPath}/page", params = {"pageNumber", "pageSize"})
public ResponseEntity<?> findPage(@RequestBody ${entity}DTO ${cfg.entityProperty}DTO,Long pageNumber,Long pageSize){
    log.info("Request to find ${entity}s:{}, pageNumber: {}, pageSize: {}", ${cfg.entityProperty}DTO,pageNumber,pageSize);

    IPage<${entity}DTO> page=this.${cfg.serviceProperty}.findPage(${cfg.entityProperty}DTO,pageNumber,pageSize);
    return ResponseUtil.buildSuccess(page);
    }
}

#end
