package cn.casair.repository;

import cn.casair.domain.NcCustomer;
import cn.casair.dto.NcCustomerDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * NC客户与系统客户的关联关系数据库操作类
 *
 * <AUTHOR>
 * @since 2022-10-19
 */
@Repository
public interface NcCustomerRepository extends BaseMapper<NcCustomer> {

    /**
     * 分页查询
     *
     * @param page
     * @param ncCustomerDTO
     * @return
     */
    IPage<NcCustomerDTO> findPage(Page page, @Param("param") NcCustomerDTO ncCustomerDTO);

    /**
     * 通过客户id 查询NC关联客户
     *
     * @param clientId
     * @return
     */
    NcCustomerDTO findByClientId(@Param("clientId") String clientId);

    List<NcCustomerDTO> findAll();

}
