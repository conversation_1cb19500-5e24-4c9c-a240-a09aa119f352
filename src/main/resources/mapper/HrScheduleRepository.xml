<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrScheduleRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, type, repetition_type, repetition_cont, is_warn, priority, title, remark, valid_star_time, valid_end_time, warn_time,
created_by                    , user_id,created_date                    ,last_modified_by                    ,last_modified_date                    ,is_delete
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrSchedule">
                    <id column="id" property="id"/>
                    <result column="type" property="type"/>
                    <result column="repetition_type" property="repetitionType"/>
                    <result column="repetition_cont" property="repetitionCont"/>
                    <result column="user_id" property="userId"/>
                    <result column="is_warn" property="isWarn"/>
                    <result column="priority" property="priority"/>
                    <result column="title" property="title"/>
                    <result column="remark" property="remark"/>
                    <result column="valid_star_time" property="validStarTime"/>
                    <result column="valid_end_time" property="validEndTime"/>
                    <result column="warn_time" property="warnTime"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
                <result column="is_delete" property="isDelete"/>
        </resultMap>

</mapper>
