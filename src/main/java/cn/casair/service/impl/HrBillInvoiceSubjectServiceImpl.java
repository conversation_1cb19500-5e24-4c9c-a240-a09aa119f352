package cn.casair.service.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrBillInvoiceSubject;
import cn.casair.dto.HrBillInvoiceSubjectDTO;
import cn.casair.repository.HrBillInvoiceSubjectRepository;
import cn.casair.mapper.HrBillInvoiceSubjectMapper;
import cn.casair.service.HrBillInvoiceSubjectService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;
/**
 * 发票内容-科目关系表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrBillInvoiceSubjectServiceImpl extends ServiceImpl<HrBillInvoiceSubjectRepository, HrBillInvoiceSubject>implements HrBillInvoiceSubjectService {


    private final HrBillInvoiceSubjectRepository hrBillInvoiceSubjectRepository;
    private final HrBillInvoiceSubjectMapper hrBillInvoiceSubjectMapper;

    public HrBillInvoiceSubjectServiceImpl(HrBillInvoiceSubjectRepository hrBillInvoiceSubjectRepository, HrBillInvoiceSubjectMapper hrBillInvoiceSubjectMapper){
    this.hrBillInvoiceSubjectRepository = hrBillInvoiceSubjectRepository;
    this.hrBillInvoiceSubjectMapper= hrBillInvoiceSubjectMapper;
    }

    /**
     * 创建发票内容-科目关系表
     * @param hrBillInvoiceSubjectDTO
     * @return
     */
    @Override
    public HrBillInvoiceSubjectDTO createHrBillInvoiceSubject(HrBillInvoiceSubjectDTO hrBillInvoiceSubjectDTO){
    log.info("Create new HrBillInvoiceSubject:{}", hrBillInvoiceSubjectDTO);

    HrBillInvoiceSubject hrBillInvoiceSubject =this.hrBillInvoiceSubjectMapper.toEntity(hrBillInvoiceSubjectDTO);
    this.hrBillInvoiceSubjectRepository.insert(hrBillInvoiceSubject);
    return this.hrBillInvoiceSubjectMapper.toDto(hrBillInvoiceSubject);
    }

    /**
     * 修改发票内容-科目关系表
     * @param hrBillInvoiceSubjectDTO
     * @return
     */
    @Override
    public Optional<HrBillInvoiceSubjectDTO>updateHrBillInvoiceSubject(HrBillInvoiceSubjectDTO hrBillInvoiceSubjectDTO){
    return Optional.ofNullable(this.hrBillInvoiceSubjectRepository.selectById(hrBillInvoiceSubjectDTO.getId()))
    .map(roleTemp->{
    HrBillInvoiceSubject hrBillInvoiceSubject =this.hrBillInvoiceSubjectMapper.toEntity(hrBillInvoiceSubjectDTO);
    this.hrBillInvoiceSubjectRepository.updateById(hrBillInvoiceSubject);
    log.info("Update HrBillInvoiceSubject:{}", hrBillInvoiceSubjectDTO);
    return hrBillInvoiceSubjectDTO;
    });
    }

    /**
     * 查询发票内容-科目关系表详情
     * @param id
     * @return
     */
    @Override
    public HrBillInvoiceSubjectDTO getHrBillInvoiceSubject(String id){
    log.info("Get HrBillInvoiceSubject :{}",id);

    HrBillInvoiceSubject hrBillInvoiceSubject =this.hrBillInvoiceSubjectRepository.selectById(id);
    return this.hrBillInvoiceSubjectMapper.toDto(hrBillInvoiceSubject);
    }

    /**
     * 删除发票内容-科目关系表
     * @param id
     */
    @Override
    public void deleteHrBillInvoiceSubject(String id){
    Optional.ofNullable(this.hrBillInvoiceSubjectRepository.selectById(id))
    .ifPresent(hrBillInvoiceSubject ->{
    this.hrBillInvoiceSubjectRepository.deleteById(id);
    log.info("Delete HrBillInvoiceSubject:{}", hrBillInvoiceSubject);
    });
    }

    /**
     * 批量删除发票内容-科目关系表
     * @param ids
     */
    @Override
    public void deleteHrBillInvoiceSubject(List<String>ids){
    log.info("Delete HrBillInvoiceSubjects:{}",ids);
    this.hrBillInvoiceSubjectRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询发票内容-科目关系表
     * @param hrBillInvoiceSubjectDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillInvoiceSubjectDTO hrBillInvoiceSubjectDTO,Long pageNumber,Long pageSize){
    Page<HrBillInvoiceSubject>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrBillInvoiceSubject>qw=new QueryWrapper<>(this.hrBillInvoiceSubjectMapper.toEntity(hrBillInvoiceSubjectDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrBillInvoiceSubjectRepository.selectPage(page,qw);
    iPage.setRecords(this.hrBillInvoiceSubjectMapper.toDto(iPage.getRecords()));
    return iPage;
    }
}
