package cn.casair.repository;

import cn.casair.domain.HrLaborAppraisal;
import cn.casair.dto.HrLaborAppraisalDTO;
import cn.casair.dto.excel.HrLaborAppraisalExport;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 劳动能力鉴定表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Repository
public interface HrLaborAppraisalRepository extends BaseMapper<HrLaborAppraisal> {

    @Select("SELECT\n" +
        "\thla.id,\n" +
        "\thla.staff_id,\n" +
        "\thla.work_injury_id,\n" +
        "\tht.client_id,\n" +
        "\thc.client_name,\n" +
        "\tht.`name`,\n" +
        "\tht.certificate_num,\n" +
        "\tht.sex,\n" +
        "\tht.phone,\n" +
        "\tht.staff_status,\n" +
        "\ths.profession_name,\n" +
        "\ths.id,\n" +
        "\tht.personnel_type,\n" +
        "\thla.injury_date,\n" +
        "\thla.work_stoppage_start_date,\n" +
        "\thla.work_stoppage_end_date,\n" +
        "\thla.`status`,\n" +
        "\thla.appraisal_status,\n" +
        "\thla.injury_description,\n" +
        "\thla.application_date,\n" +
        "\thla.application,\n" +
        "\tsu.real_name AS specialized,\n" +
        "\tsu.id \n" +
        "FROM\n" +
            "\thr_labor_appraisal hla\n" +
        "\tLEFT JOIN hr_talent_staff ht ON hla.staff_id = ht.id \n" +
        "\tAND ht.is_delete = 0\n" +
        "\tLEFT JOIN hr_staff_work_experience hsw ON hla.staff_id = hsw.staff_id AND ht.client_id = hsw.client_id\n" +
        "\tAND hsw.is_delete = 0 \n" +
        "\tAND hsw.iz_default = 1\n" +
        "\tLEFT JOIN hr_station hs ON hsw.station_id = hs.id \n" +
        "\tAND hs.is_delete = 0\n" +
        "\tLEFT JOIN hr_client hc ON hc.id = hla.client_id \n" +
        "\tAND hc.is_delete = 0\n" +
        "\tLEFT JOIN hr_user_client huc ON huc.client_id = ht.client_id \n" +
        "\tAND huc.is_delete = 0 \n" +
        "\tAND huc.is_specialized = 1\n" +
        "\tLEFT JOIN sys_user su ON huc.user_id = su.id \n" +
        "\tAND su.is_delete = 0 ${ew.customSqlSegment}")

    IPage<HrLaborAppraisalDTO> page(Page page, @Param(Constants.WRAPPER)QueryWrapper<HrLaborAppraisal> qw);

    HrLaborAppraisalDTO selectDetatil(String id);

    List<HrLaborAppraisalExport> exportLaborAppraisals(@Param(Constants.WRAPPER)QueryWrapper<HrLaborAppraisal> qw);

    List<HrLaborAppraisalDTO> laborAppraisalListByStaffId(@Param("staffId") String staffId);

}
