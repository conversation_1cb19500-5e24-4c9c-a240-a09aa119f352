package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.domain.HrStaffQualification;
import cn.casair.dto.HrStaffQualificationDTO;
import cn.casair.mapper.HrStaffQualificationMapper;
import cn.casair.repository.HrStaffQualificationRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrStaffQualificationService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 职业(工种)资格服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrStaffQualificationServiceImpl extends ServiceImpl<HrStaffQualificationRepository, HrStaffQualification> implements HrStaffQualificationService {

    private static final Logger log = LoggerFactory.getLogger(HrStaffQualificationServiceImpl.class);

    private final HrStaffQualificationRepository hrStaffQualificationRepository;

    private final HrStaffQualificationMapper hrStaffQualificationMapper;

    private final CodeTableService codeTableService;

    private final SysOperLogService sysOperLogService;

    public HrStaffQualificationServiceImpl(HrStaffQualificationRepository hrStaffQualificationRepository, HrStaffQualificationMapper hrStaffQualificationMapper, CodeTableService codeTableService, SysOperLogService sysOperLogService) {
        this.hrStaffQualificationRepository = hrStaffQualificationRepository;
        this.hrStaffQualificationMapper = hrStaffQualificationMapper;
        this.codeTableService = codeTableService;
        this.sysOperLogService = sysOperLogService;
    }

    /**
     * 创建职业(工种)资格
     *
     * @param hrStaffQualificationDTO
     * @return
     */
    @Override
    public List<HrStaffQualificationDTO> createHrStaffQualification(HrStaffQualificationDTO hrStaffQualificationDTO) {
        log.info("Create new HrStaffQualification:{}", hrStaffQualificationDTO);

        HrStaffQualification hrStaffQualification = this.hrStaffQualificationMapper.toEntity(hrStaffQualificationDTO);
        this.hrStaffQualificationRepository.insert(hrStaffQualification);
        // 操作日志
        setDict(hrStaffQualificationDTO);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_QUALIFICATION.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffQualificationDTO),
            HrStaffQualificationDTO.class,
            null,
            JSON.toJSONString(hrStaffQualification)
        );
        return this.findQualificationList(hrStaffQualificationDTO.getStaffId());
    }

    /**
     * 修改职业(工种)资格
     *
     * @param hrStaffQualificationDTO
     * @return
     */
    @Override
    public Optional<List<HrStaffQualificationDTO>> updateHrStaffQualification(HrStaffQualificationDTO hrStaffQualificationDTO) {
        return Optional.ofNullable(this.hrStaffQualificationRepository.selectById(hrStaffQualificationDTO.getId()))
            .map(roleTemp -> {
                HrStaffQualification hrStaffQualification = this.hrStaffQualificationMapper.toEntity(hrStaffQualificationDTO);
                this.hrStaffQualificationRepository.updateById(hrStaffQualification);
                log.info("Update HrStaffQualification:{}", hrStaffQualificationDTO);
                // 操作日志
                setDict(hrStaffQualificationDTO);
                HrStaffQualificationDTO dto = hrStaffQualificationMapper.toDto(roleTemp);
                setDict(dto);
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.STAFF_QUALIFICATION.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrStaffQualificationDTO),
                    HrStaffQualificationDTO.class,
                    null,
                    JSON.toJSONString(dto),
                    JSON.toJSONString(hrStaffQualificationDTO),
                    null,
                    HrStaffQualificationDTO.class
                );
                return this.findQualificationList(hrStaffQualificationDTO.getStaffId());
            });
    }

    /**
     * 查询职业(工种)资格详情
     *
     * @param id
     * @return
     */
    @Override
    public HrStaffQualificationDTO getHrStaffQualification(String id) {
        log.info("Get HrStaffQualification :{}", id);

        HrStaffQualification hrStaffQualification = this.hrStaffQualificationRepository.selectById(id);
        return this.hrStaffQualificationMapper.toDto(hrStaffQualification);
    }

    /**
     * 删除职业(工种)资格
     *
     * @param id
     */
    @Override
    public List<HrStaffQualificationDTO> deleteHrStaffQualification(String id) {
        HrStaffQualification hrStaffQualification = this.hrStaffQualificationRepository.selectById(id);
        this.hrStaffQualificationRepository.deleteById(id);
        log.info("Delete HrStaffQualification:{}", hrStaffQualification);
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.STAFF_QUALIFICATION.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除" + ModuleTypeEnum.STAFF_QUALIFICATION.getValue()+": "+ hrStaffQualification.getQualificationName(),
            null,
            null,
            null,
            JSON.toJSONString(hrStaffQualification),
            null
        );
        return this.findQualificationList(hrStaffQualification.getStaffId());
    }

    /**
     * 查询职业(工种)资格
     * @param staffId 员工ID
     * @return
     */
    @Override
    public List<HrStaffQualificationDTO> findQualificationList(String staffId) {
        List<HrStaffQualificationDTO> hrStaffQualificationDTOS = this.hrStaffQualificationMapper.toDto(
            hrStaffQualificationRepository.selectList(new QueryWrapper<HrStaffQualification>().eq("staff_id", staffId).eq("is_delete", 0)));
        hrStaffQualificationDTOS.forEach(this::setDict);
        return hrStaffQualificationDTOS;
    }

    /**
     * 赋值字典值
     * @param hrStaffQualificationDTO
     */
    public void setDict(HrStaffQualificationDTO hrStaffQualificationDTO) {
        if (hrStaffQualificationDTO.getQualificationGrade()!=null){
            Map<Integer, String> gradeType = codeTableService.findCodeTableByInnerName("gradeType");//等级
            hrStaffQualificationDTO.setQualificationGradeLabel(gradeType.get(hrStaffQualificationDTO.getQualificationGrade()));
        }
    }
}
