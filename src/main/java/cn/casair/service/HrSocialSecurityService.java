package cn.casair.service;
import cn.casair.domain.HrSocialSecurity;
import cn.casair.dto.HrSocialSecurityDTO;
import cn.casair.dto.HrsocialDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 社保医保表服务类
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
public interface HrSocialSecurityService extends IService<HrSocialSecurity> {

    /**
     * 社保类型下拉列表数据
     *
     * @return java.util.List<cn.casair.dto.HrSocialSecurityDTO>
     * <AUTHOR>
     * @date 2021/10/18
     **/
    List<HrSocialSecurityDTO> getSocialSecurityTypeList();


    /**
     * 创建社保医保表
     * @param hrSocialSecurityDTO
     * @return
     */
    HrSocialSecurityDTO createHrSocialSecurity(HrSocialSecurityDTO hrSocialSecurityDTO);

    /**
     * 修改社保医保表
     * @param hrSocialSecurityDTO
     * @return
     */
    Optional<List<String>> updateHrSocialSecurity(HrSocialSecurityDTO hrSocialSecurityDTO);

    /**
     * 查询社保医保表详情
     * @param id
     * @return
     */
    HrSocialSecurityDTO getHrSocialSecurity(String id);

    /**
     * 删除社保医保表
     * @param id
     */
    void deleteHrSocialSecurity(String id);

    /**
     * 批量删除社保医保表
     * @param ids
     */
    void deleteHrSocialSecurity(List<String> ids);

    /**
     * 分页查询社保医保表
     * @param hrSocialSecurityDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrSocialSecurityDTO hrSocialSecurityDTO,Long pageNumber,Long pageSize);

    HrsocialDTO getSelectHrplatform();

    String exportSocial(HrSocialSecurityDTO hrSocialSecurityDTO, HttpServletResponse httpServletResponse);

    String importSocial(MultipartFile file);

    /**
     * 社保类型模板
     * @param response
     */
    String importSocialTemplate(HttpServletResponse response);

    /**
     * 获取客户社保配置
     * @param clientIdList
     * @return
     */
    List<HrSocialSecurityDTO> getClientSocialSecurity(List<String> clientIdList);

}
