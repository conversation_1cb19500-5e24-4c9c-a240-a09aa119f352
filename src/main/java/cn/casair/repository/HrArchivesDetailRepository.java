package cn.casair.repository;

import cn.casair.domain.HrArchivesDetail;
import cn.casair.dto.HrArchivesDetailDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 档案明细数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
@Repository
public interface HrArchivesDetailRepository extends BaseMapper<HrArchivesDetail> {

    /**
     * 获取变更记录管理档案明细
     *
     * @param bringId
     * @return java.util.List<cn.casair.dto.HrArchivesDetailDTO>
     * <AUTHOR>
     * @date 2021/11/11
     **/
    List<HrArchivesDetailDTO> getArchivesDetailByBringId(String bringId);

    /**
     * 根据档案id获取在档档案明细列表
     *
     * @param archivesId
     * @return java.util.List<cn.casair.dto.HrArchivesDetailDTO>
     * <AUTHOR>
     * @date 2021/10/14
     **/
    List<HrArchivesDetailDTO> getArchivesDetailByArchivesId(@Param("archivesId") String archivesId);

    /**
     * 更新档案明细状态
     *
     * @param hrArchivesDetailDTO
     * @return int
     * <AUTHOR>
     * @date 2021/10/14
     **/
    int changeDetailState(HrArchivesDetailDTO hrArchivesDetailDTO);

    /**
     * 根据档案id更新
     *
     * @param archivesId
     * @param state
     * @return int
     * <AUTHOR>
     * @date 2021/10/14
     **/
    int updateDetailStateByArchivesId(@Param("archivesId") String archivesId, @Param("state") Integer state);

    /**
     * 根据档案id获取所有档案明细列表
     *
     * @param archivesId
     * @return java.util.List<cn.casair.dto.HrArchivesDetailDTO>
     * <AUTHOR>
     * @date 2021/10/26
     **/
    List<HrArchivesDetailDTO> getDetailByArchivesId(String archivesId);

    /**
     * 根据档案申请id集合字符串获取所有档案明细列表
     *
     * @param ids
     * @return java.util.List<cn.casair.dto.HrArchivesDetailDTO>
     * <AUTHOR>
     * @date 2021/10/26
     **/
    List<HrArchivesDetailDTO> getDetailByDetailIds(@Param("idsStr") String idsStr);

    /**
     * 根据档案id删除档案明细
     *
     * @param archivesIds
     * @return void
     * <AUTHOR>
     * @date 2021/11/18
     **/
    void deleteBatchByArchivesId(@Param("archivesIds") List<String> archivesIds);

    /**
     * 获取档案明细
     *
     * @param archivesId
     * @return java.util.List<cn.casair.dto.HrArchivesDetailDTO>
     * <AUTHOR>
     * @date 2021/11/18
     **/
    List<HrArchivesDetailDTO> getAllArchivesDetailByArchivesId(String archivesId);

    /**
     * 根据员工查询档案明细
     * @param staffId 员工Id
     * @param clientId 客户Id
     * @return
     */
    List<HrArchivesDetailDTO> getDetailByStaffId(@Param("staffId") String staffId, @Param("clientId") String clientId);

    /**
     * 根据员工查询档案明细
     * @param staffId 员工Id
     * @return
     */
    List<HrArchivesDetail> getDealByStaffId(@Param("staffId") String staffId);

}
