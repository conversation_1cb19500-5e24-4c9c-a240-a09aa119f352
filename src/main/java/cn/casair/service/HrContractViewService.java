package cn.casair.service;

import cn.casair.domain.HrContractView;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrContractViewDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 合同查看下载申请服务类
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
public interface HrContractViewService extends IService<HrContractView> {


    /**
     * 创建合同查看下载申请
     *
     * @param hrContractViewDTO
     * @return
     */
    HrContractViewDTO createHrContractView(HrContractViewDTO hrContractViewDTO);

    /**
     * 查询合同查看下载申请详情
     *
     * @param id
     * @return
     */
    HrContractViewDTO getHrContractView(String id);

    /**
     * 批量删除合同查看下载申请
     *
     * @param ids
     */
    void deleteHrContractView(List<String> ids);

    /**
     * 分页查询合同查看下载申请
     *
     * @param hrContractViewDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrContractViewDTO> findPage(HrContractViewDTO hrContractViewDTO, Long pageNumber, Long pageSize);

    /**
     * 导出
     *
     * @param hrContractViewDTO
     * @return
     */
    String export(HrContractViewDTO hrContractViewDTO);

    /**
     * 客户批量操作--通过/拒绝
     *
     * @param batchOptDTO
     * @return
     */
    Map<String, Object> batchOperationContractView(BatchOptDTO batchOptDTO);

    /**
     * 单个操作--通过/拒绝
     *
     * @param batchOptDTO
     * @return
     */
    HrContractViewDTO specialSupervisorOperation(BatchOptDTO batchOptDTO);

    /**
     * 每天1点检测合同查看下载状态
     */
    void scheduleTaskToUpdateContractViewState();

}
