package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.DynamicFeeTypesEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.BigDecimalCompare;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.StringUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.CodeTable;
import cn.casair.dto.CodeTableDTO;
import cn.casair.dto.HrSocialSecurityDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.CodeTableImport;
import cn.casair.repository.CodeTableRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 无业务关联异步导入处理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/12/27 11:53
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CommonComponent {

    @Value("${file.temp-path}")
    private String tempPath;

    private final RedisCache redisCache;
    private final SysOperLogService sysOperLogService;
    private final HrAppendixService hrAppendixService;
    private final CodeTableRepository codeTableRepository;

    public void dealCodeTableImport(InputStream inputStream, Integer parentId, String redisKey, JWTUserDTO user) {
        try {
            ExcelImportResult<CodeTableImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, CodeTableImport.class);
            ImportResultDTO importResult;

            if (result.getList().size() > 0) {
                int scale = 0;
                int listSize = result.getList().size();
                // 根据parentId获取字典项子级项
                List<CodeTableDTO> codeTableList = this.codeTableRepository.getChildrenListByParentId(parentId);
                int sort = 0;
                int value = 0;

                // 判断是否存在子级(设置最大值与排序)
                if (!codeTableList.isEmpty()) {
                    sort = codeTableList.get(codeTableList.size() - 1).getDisplayOrder() + 1;
                    Optional<CodeTableDTO> collect = codeTableList.stream().max(Comparator.comparing(CodeTableDTO::getItemValue));
                    value = collect.get().getItemValue() + 1;
                }

                // 将Excel数据写入数据库
                for (CodeTableImport template : result.getList()) {
                    try {
                        // 判断子项值是否重复
                        int count = this.codeTableRepository.getItemByParentIdAndItemName(parentId, template.getItemName());
                        if (count > 0) {
                            throw new CommonException("该字典项已存在！");
                        }
                        CodeTable codeTable = new CodeTable();
                        codeTable.setParentId(parentId);
                        codeTable.setItemName(template.getItemName());
                        codeTable.setItemValue(value);
                        codeTable.setDisplayOrder(sort);
                        this.codeTableRepository.insert(codeTable);
                        value++;
                        sort++;
                    } catch (Exception e) {
                        log.error("导入数据字典异常：{}", e.getMessage());
                        template.setErrorMsg(e.getMessage());
                    } finally {
                        scale++;
                        int i = CalculateUtils.calculationProgress(scale, listSize);
                        redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                    }
                }
            }
            importResult = ImportResultUtils.writeErrorFile("数据字典批量导入", CodeTableImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);
            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CODE_TABLE.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, user);
        } catch (Exception e) {
            log.error("数据字典异步导入处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "数据字典导入发生异常：Excel文件读取失败" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    /**
     * 处理导入时合并基数是否一致
     * @param hrSocialSecurityDTO
     * @param staffParams
     * @param msgError
     */
    public void handleCardinal(HrSocialSecurityDTO hrSocialSecurityDTO, Map<String, Object> staffParams, StringBuilder msgError) {
        //判断社保类型中选择的险种是否有值
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> specialFieldDTOList = hrSocialSecurityDTO.getSpecialFieldDTOList();
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> list = new ArrayList<>();
        if (specialFieldDTOList != null && !specialFieldDTOList.isEmpty()) {
            list.addAll(specialFieldDTOList);
        }
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> aloneCardinalDTOList = hrSocialSecurityDTO.getAloneCardinalDTOList();
        List<HrSocialSecurityDTO.HrSocialHeadersDTO> mergeCardinalDTOList = hrSocialSecurityDTO.getMergeCardinalDTOList();
        if (aloneCardinalDTOList != null && !aloneCardinalDTOList.isEmpty()) {
            list.addAll(aloneCardinalDTOList);
        }
        if (list != null && !list.isEmpty()) {
            for (HrSocialSecurityDTO.HrSocialHeadersDTO dto : list) {
                String valueOf = String.valueOf(staffParams.get(dto.getFieldKey()));
                if (StringUtils.isEmpty(valueOf) || valueOf.equals("null")) {
                    msgError.append("社保类型配置了" + DynamicFeeTypesEnum.getEnumByFieldName(dto.getFieldKey()).getValue() + "，则是必填项;");
                }
            }
        }
        if (mergeCardinalDTOList != null && !mergeCardinalDTOList.isEmpty()) {
            //根据社保类型行数分组，将合并基数的单位和个人合并处理
            Map<Integer, List<HrSocialSecurityDTO.HrSocialHeadersDTO>> listMap = mergeCardinalDTOList.stream().collect(Collectors.groupingBy(HrSocialSecurityDTO.HrSocialHeadersDTO::getRelation));
            for (Map.Entry<Integer, List<HrSocialSecurityDTO.HrSocialHeadersDTO>> integerListEntry : listMap.entrySet()) {
                List<String> keyList = new ArrayList<>();
                List<String> fieldName = new ArrayList<>();
                for (HrSocialSecurityDTO.HrSocialHeadersDTO hrSocialHeadersDTO : integerListEntry.getValue()) {
                    keyList.addAll(hrSocialHeadersDTO.getFieldKeyList());
                    fieldName.addAll(hrSocialHeadersDTO.getFieldNameList());
                }
                int flagAgree = 0;
                BigDecimal decimal = null;
                for (String key : keyList) {
                    String valueOf = String.valueOf(staffParams.get(key));
                    BigDecimal bigDecimal = null;
                    if (StringUtils.isEmpty(valueOf) || valueOf.equals("null")) {
                        msgError.append("社保类型配置了" + DynamicFeeTypesEnum.getEnumByFieldName(key).getValue() + "，则是必填项;");
                        bigDecimal = BigDecimal.ZERO;
                    }else{
                        bigDecimal = new BigDecimal(valueOf);
                    }
                    if (decimal == null) {
                        decimal = bigDecimal;
                    } else {
                        if (!BigDecimalCompare.of(bigDecimal).eq(decimal)) {
                            flagAgree = 1;
                            break;
                        }
                        decimal = bigDecimal;
                    }
                }
                if (flagAgree == 1) {
                    msgError.append("社保类型配置了合并基数【" + String.join("|", fieldName) + "】则这几项值需一致;");
                }
            }
        }

    }
}
