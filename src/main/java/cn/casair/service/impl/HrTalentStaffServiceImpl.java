package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.aop.logging.enums.OperatorTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrStaffEntryQuitExport;
import cn.casair.dto.excel.HrStaffExport;
import cn.casair.dto.excel.HrTalentExport;
import cn.casair.dto.formdata.FormFieldDealDTO;
import cn.casair.dto.formdata.InputLabel;
import cn.casair.dto.formdata.InputLabelDTO;
import cn.casair.dto.formdata.TemplateDealParamsDTO;
import cn.casair.dto.pdf.ContractInfoForPdf;
import cn.casair.mapper.HrClientMapper;
import cn.casair.mapper.HrContractAppendixMapper;
import cn.casair.mapper.HrContractMapper;
import cn.casair.mapper.HrTalentStaffMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.asynchronous.HrTalentStaffComponent;
import cn.casair.service.component.ecloud.ElectronicSignComponent;
import cn.casair.service.component.sms.SmsComponent;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.casair.common.enums.ApprovalEntryStatusEnum.EntryStatus;
import static cn.casair.common.utils.DateUtils.getRetirementDate;

/**
 * 员工管理服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrTalentStaffServiceImpl extends ServiceImpl<HrTalentStaffRepository, HrTalentStaff> implements HrTalentStaffService {

    private final HrContractGroupRepository hrContractGroupRepository;
    private final ElectronicSignComponent electronicSignComponent;
    private final SmsComponent smsComponent;
    private final HrStaffSignCertService hrStaffSignCertService;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrTalentStaffMapper hrTalentStaffMapper;
    private final HrStaffFieldService hrStaffFieldService;
    private final HrStaffNoteService hrStaffNoteService;
    private final HrStaffWorkExperienceService hrStaffWorkExperienceService;
    private final CodeTableService codeTableService;
    private final HrStaffEducationService hrStaffEducationService;
    private final HrStaffInterviewService hrStaffInterviewService;
    private final HrStaffFamilyService hrStaffFamilyService;
    private final HrStaffContactsService hrStaffContactsService;
    private final HrStaffLanguageService hrStaffLanguageService;
    private final HrStaffProfessionService hrStaffProfessionService;
    private final HrStaffQualificationService hrStaffQualificationService;
    private final HrStaffTechniqueService hrStaffTechniqueService;
    private final HrStaffCertificateService hrStaffCertificateService;
    private final HrStaffStationService hrStaffStationService;
    private final HrStationService hrStationService;
    private final HrAppendixService hrAppendixService;
    private final RedisCache redisCache;
    private final HrClientRepository hrClientRepository;
    private final HrProtocolRepository hrProtocolRepository;
    private final HrApplyEntryStaffRepository hrApplyEntryStaffRepository;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrContractRepository hrContractRepository;
    private final HrContractAppendixRepository hrContractAppendixRepository;
    private final HrApplyEntryStaffService hrApplyEntryStaffService;
    private final HrArchivesDetailRepository hrArchivesDetailRepository;
    private final HrArchivesBringRepository hrArchivesBringRepository;
    private final HrArchivesManageRepository hrArchivesManageRepository;
    private final HrClientService hrClientService;
    private final HrCommonFunctionsRepository hrCommonFunctionsRepository;
    private final HrFertilityRepository hrFertilityRepository;
    private final HrRetireRepository hrRetireRepository;
    private final HrContractService hrContractService;
    private final HrContractTemplateService hrContractTemplateService;
    private final HrContractMapper hrContractMapper;
    private final HrContractAppendixMapper hrContractAppendixMapper;
    private final HrSmsTemplateService hrSmsTemplateService;
    private final HrRemindConfRepository hrRemindConfRepository;
    private final SysOperLogService sysOperLogService;
    private final HrTalentStaffComponent hrTalentStaffComponent;
    private final HrWorkInjuryRepository hrWorkInjuryRepository;
    private final HrLaborAppraisalRepository hrLaborAppraisalRepository;
    private final HrSocialSecurityRepository hrSocialSecurityRepository;
    private final HrCertificateIssuanceRepository hrCertificateIssuanceRepository;
    private final HrRegistrationDetailsRepository hrRegistrationDetailsRepository;
    private final HrRegistrationInfoRepository hrRegistrationInfoRepository;
    private final HrStaffSecondmentRepository hrStaffSecondmentRepository;
    private final HrClientMapper hrClientMapper;
    private final DistinguishOcrService distinguishOcrService;
    private final HrMaternityAllowanceRepository hrMaternityAllowanceRepository;
    private final HrUpcomingService hrUpcomingService;
    private final HrNotificationUserService hrNotificationUserService;
    private final HrAppletMessageService hrAppletMessageService;
    private final HrRealNameAuthService hrRealNameAuthService;
    private final HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;


    @Value("${minio.excelPrefix}")
    private String excelPrefix;

    @Override
    public void updateStaffSupplementaryPayment(HrTalentStaffDTO hrTalentStaffDTO) {
        if (hrTalentStaffDTO.getIds() == null || hrTalentStaffDTO.getIds().isEmpty()) {
            throw new CommonException("员工id不能为空!");
        }
        if (hrTalentStaffDTO.getSupplementaryPayment() == null) {
            throw new CommonException("是否计算补缴状态不能为空!");
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        this.hrTalentStaffRepository.batchUpdateStaffSupplementaryPayment(hrTalentStaffDTO, jwtUserDTO);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF.getValue(),
            BusinessTypeEnum.UPDATE.getKey(),
            JSON.toJSONString(hrTalentStaffDTO),
            HrTalentStaffDTO.class,
            null,
            null,
            JSON.toJSONString(hrTalentStaffDTO),
            null,
            HrTalentStaffDTO.class
        );
    }

    @Override
    public void confirmationTelegramForRenew(HrContractDTO hrContractDTO) {
        if (StringUtils.isBlank(hrContractDTO.getId())) {
            throw new CommonException("合同id不能为空！");
        }
        if (hrContractDTO.getCheckResult() == null) {
            throw new CommonException("请选择审核结果！");
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrContract hrContract = this.hrContractRepository.selectById(hrContractDTO.getId());
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(hrContract.getStaffId());
        List<HrContractAppendixDTO> hrContractAppendixDTOList = this.hrContractAppendixRepository.getStaffContractListTemplate(new HrContractAppendixDTO().setContractId(hrContract.getId()));
        hrContractAppendixDTOList.forEach(ls -> {
            ls.setState(ContractEnum.ExamineState.PASS.getKey());
            this.hrContractAppendixRepository.updateById(this.hrContractAppendixMapper.toEntity(ls));
        });
        // 审核续签通过后续操作
        this.contractRenewalApproved(hrContract, hrContractDTO, jwtUserDTO, hrTalentStaff);
        this.hrContractRepository.updateById(this.hrContractMapper.toEntity(hrContractDTO));
    }

    @Override
    public void updateStaffFirstLoginSign() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        log.info("更新员工第一次登陆标识token:{}", jwtMiniDTO);
        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(jwtMiniDTO.getId());
        log.info("更新员工第一次登陆标识员工信息:{}", hrTalentStaff);
        if (hrTalentStaff != null) {
            this.hrTalentStaffRepository.updateStaffFirstLoginSign(jwtMiniDTO.getId());
        }
    }

    @Override
    public Map<String, Object> checkStaffIdentityInfo() {

        return null;
    }

    @Override
    public String importEmployeeWelfare(MultipartFile file) {
        // 设置进度条
        JWTUserDTO user = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.EMPLOYEE_WELFARE_IMPORT.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.hrTalentStaffComponent.dealEmployeeWelfareImport(inputStream, redisKey, user);
        return redisKey;
    }

    @Override
    public String downloadImportTemplate() {
        /*List<HrStaffEmolumentDTO> list = new ArrayList<>();
        list.add(new HrStaffEmolumentDTO()
                .setSystemNum("SY1634551229531")
                .setBasicWage(BigDecimal.valueOf(5000))
                .setSalary(BigDecimal.valueOf(8000))
                .setOwnedBank("中国银行")
                .setSalaryCardNum("*************")
                .setSocialSecurityNum("371325199102392810")
                .setSocialSecurityCardinal(BigDecimal.valueOf(5000))
                .setMedicalInsuranceNum("371325199102392810")
                .setMedicalInsuranceCardinal(BigDecimal.valueOf(5000))
                .setAccumulationFundNum("371325199102392810")
                .setAccumulationFundCardinal(BigDecimal.valueOf(5000))
                .setSeniorityWageBase(BigDecimal.valueOf(100))
                .setPaymentDate("2021-10")
        );
        ExcelUtils.exportExcelWithNoHeader(list, "员工福利导入模板", "sheet1", HrStaffEmolumentDTO.class, response);*/
        return excelPrefix + "员工福利导入模板.xlsx";
    }

    @Override
    public String exportEmployeeWelfare(HrEmployeeWelfareDTO hrEmployeeWelfareDTO, HttpServletResponse response) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrEmployeeWelfareDTO> list = this.hrTalentStaffRepository.selectExportListByIds(hrEmployeeWelfareDTO, clientIds);
        if (list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        int listSize = list.size();
        List<String> ids = list.stream().map(HrEmployeeWelfareDTO::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "员工福利", HrEmployeeWelfareDTO.class);
        // ExcelUtils.exportExcelWithNoHeader(list, "员工福利", "sheet1", HrEmployeeWelfareDTO.class, response);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.STAFF_EMOLUMENT.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    @Override
    public IPage<HrEmployeeWelfareDTO> findEmployeeWelfarePage(HrEmployeeWelfareDTO hrEmployeeWelfareDTO, Long pageNumber, Long pageSize) {
        Page<HrEmployeeWelfareDTO> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        IPage<HrEmployeeWelfareDTO> iPage = this.hrTalentStaffRepository.findEmployeeWelfarePage(page, hrEmployeeWelfareDTO, clientIds);
        /*iPage.getRecords().forEach(ls -> {
            // 计算当前工资工龄
            if (ls.getContractStartDate() != null) {
                Integer years = DateUtils.calculateYearDifference(ls.getContractStartDate(), LocalDate.now());
                if (ls.getSeniorityWageBase() != null) {
                    ls.setSeniorityPay(CalculateUtils.multiplication(ls.getSeniorityWageBase(), years));
                }
            }
        });*/
        return iPage;
    }

    @Override
    public List<String> domicilePlaceList() {
        return this.hrTalentStaffRepository.domicilePlaceList();
    }

    /**
     * 创建员工管理
     *
     * @param hrTalentStaffDTO
     * @return
     */
    @Override
    public HrTalentStaffDTO createHrTalentStaff(HrTalentStaffDTO hrTalentStaffDTO) {
        log.info("Create new HrTalentStaff:{}", hrTalentStaffDTO);
        //生成系统编号
        if (hrTalentStaffDTO.getSystemNum() == null) {
            hrTalentStaffDTO.setSystemNum("SY" + System.currentTimeMillis());
        } else {
            //校验是否存在
            Integer count = hrTalentStaffRepository.selectCount(new QueryWrapper<HrTalentStaff>().eq("system_num", hrTalentStaffDTO.getSystemNum()));
            if (count > 0) {
                throw new CommonException("该系统编号已在系统中存在");
            }
        }
        if (hrTalentStaffDTO.getPhone() != null) {
            //手机号码：验证手机号码有效性。手机号码不可重复
            this.checkoutPhone(hrTalentStaffDTO.getPhone(), hrTalentStaffDTO.getCertificateNum(), hrTalentStaffDTO.getName());
        }
        //校验身份证号
        if (hrTalentStaffDTO.getCertificateNum() != null) {
            this.checkoutIDNumber(hrTalentStaffDTO.getCertificateType(), hrTalentStaffDTO.getCertificateNum());
        }
        // 实名认证
        this.hrRealNameAuthService.authNameCertificateNum(new HrRealNameAuthDTO().setName(hrTalentStaffDTO.getName()).setCertificateNum(hrTalentStaffDTO.getCertificateNum()));
        HrTalentStaff hrTalentStaff = this.hrTalentStaffMapper.toEntity(hrTalentStaffDTO);
        this.hrTalentStaffRepository.insert(hrTalentStaff);
        HrTalentStaffDTO toDto = this.hrTalentStaffMapper.toDto(hrTalentStaff);
        //其他信息不为空
        if (CollectionUtils.isNotEmpty(hrTalentStaffDTO.getStaffFieldList())) {
            //创建员工其他信息
            JSONObject jsonObject = new JSONObject();
            for (HrStaffField field : hrTalentStaffDTO.getStaffFieldList()) {
                jsonObject.put(field.getFieldName(), field.getFieldValue());
                field.setStaffId(toDto.getId());
                hrStaffFieldService.save(field);
            }
            hrTalentStaffDTO.setStaffField(jsonObject.toString());
        }
        //员工标签不为空
        if (CollectionUtils.isNotEmpty(hrTalentStaffDTO.getStaffNoteList())) {
            //创建员工标签
            for (HrStaffNote staffNote : hrTalentStaffDTO.getStaffNoteList()) {
                staffNote.setStaffId(toDto.getId());
                hrStaffNoteService.save(staffNote);
            }
            List<String> collect = hrTalentStaffDTO.getStaffNoteList().stream().map(HrStaffNote::getStaffNote).collect(Collectors.toList());
            hrTalentStaffDTO.setStaffNote(String.join(",", collect));
        }
        //适配岗位不为空
        if (CollectionUtils.isNotEmpty(hrTalentStaffDTO.getStationList())) {
            for (HrStation hrStation : hrTalentStaffDTO.getStationList()) {
                HrStaffStation hrStaffStation = new HrStaffStation();
                hrStaffStation.setStaffId(toDto.getId());
                hrStaffStation.setStationId(hrStation.getId());
                hrStaffStationService.save(hrStaffStation);
            }
            List<String> stationId = hrTalentStaffDTO.getStationList().stream().map(HrStation::getId).collect(Collectors.toList());
            List<HrStation> list = hrStationService.list(new QueryWrapper<HrStation>().in("id", stationId).eq("is_delete", 0));
            List<String> collect = list.stream().map(HrStation::getProfessionName).collect(Collectors.toList());
            hrTalentStaffDTO.setStation(String.join(",", collect));
        }
        // 操作日志
        setDict(hrTalentStaffDTO);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.TALENTS.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrTalentStaffDTO),
            HrTalentStaffDTO.class,
            null,
            JSON.toJSONString(hrTalentStaff)
        );
        return toDto;
    }

    /**
     * 校验证件号码
     *
     * @param certificateNum
     * @return
     */
    private void checkoutIDNumber(Integer certificateType, String certificateNum) {
        //校验格式
        boolean idNumber = ValidateUtil.isIdNumber(certificateNum);
        if (certificateType.equals(CertificateTypeEnum.CertificateType.RESIDENT_IDENTIFICATION_CARD.getKey()) && !idNumber) {
            throw new CommonException("身份证号格式不正确！");
        }
        HrTalentStaff staff = hrTalentStaffRepository.selectOne(
            new QueryWrapper<HrTalentStaff>().eq("certificate_num", certificateNum).eq("is_delete", 0));
        if (staff != null) {
            throw new CommonException("证件号码已存在！");
            //证件号码已存在，判断该条数据是人才库数据还是员工信息数据
        }
    }

    /**
     * 手机号有效性/重复性
     *
     * @param phone          手机号码
     * @param certificateNum 手机号码
     * @param name           姓名
     */
    private void checkoutPhone(String phone, String certificateNum, String name) {
        //验证手机号码有效性
        if (!ValidateUtil.isCellPhoneNo(phone)) {
            throw new CommonException("手机号码格式填写不正确！");
        }
        List<HrTalentStaff> hrTalentStaffs = hrTalentStaffRepository.selectList(
            new QueryWrapper<HrTalentStaff>().eq("phone", phone)
                .ne(StringUtils.isNotBlank(certificateNum), "certificate_num", certificateNum)
                .ne(StringUtils.isNotBlank(name), "name", name));
        if (CollectionUtils.isNotEmpty(hrTalentStaffs)) {
            throw new CommonException("手机号码已存在！");
        }
    }

    /**
     * 修改员工管理
     *
     * @param hrTalentStaffDTO
     * @return
     */
    @Override
    public Optional<HrTalentStaffDTO> updateHrTalentStaff(HrTalentStaffDTO hrTalentStaffDTO) {
        return Optional.ofNullable(this.hrTalentStaffRepository.selectById(hrTalentStaffDTO.getId()))
            .map(talentStaff -> {
                Integer count = hrTalentStaffRepository.selectCount(new QueryWrapper<HrTalentStaff>()
                    .eq("system_num", hrTalentStaffDTO.getSystemNum()).ne("id", talentStaff.getId()));
                if (count > 0) {
                    throw new CommonException("该系统编号已在系统中存在");
                }
                if (hrTalentStaffDTO.getPhone() != null && !hrTalentStaffDTO.getPhone().equalsIgnoreCase(talentStaff.getPhone())) {
                    //手机号码：验证手机号码有效性。手机号码不可重复
                    this.checkoutPhone(hrTalentStaffDTO.getPhone(), hrTalentStaffDTO.getCertificateNum(), hrTalentStaffDTO.getName());
                }
                if (hrTalentStaffDTO.getCertificateNum() != null && !hrTalentStaffDTO.getCertificateNum().equalsIgnoreCase(talentStaff.getCertificateNum())) {
                    //证件号码：根据证件类型，验证证件号码的有效性
                    this.checkoutIDNumber(hrTalentStaffDTO.getCertificateType(), hrTalentStaffDTO.getCertificateNum());
                    //判断是否出现退休按钮
                    if (hrTalentStaffDTO.getIzRetire() != 2) {
                        ifRetire(hrTalentStaffDTO);
                    }
                }
                HrTalentStaff hrTalentStaff = this.hrTalentStaffMapper.toEntity(hrTalentStaffDTO);
                this.hrTalentStaffRepository.updateHrTalentStaff(hrTalentStaff);
                HrTalentStaffDTO dto = hrTalentStaffMapper.toDto(talentStaff);
                // 实名认证
                this.hrRealNameAuthService.authNameCertificateNum(new HrRealNameAuthDTO().setName(hrTalentStaffDTO.getName()).setCertificateNum(hrTalentStaffDTO.getCertificateNum()));
                // 判断用户易云章证书是否需要更新
                this.judgeStaffSignCert(talentStaff, hrTalentStaffDTO);
                // 判断是否需要修改合同信息
                this.judgeStaffContract(hrTalentStaffDTO);
                //判断是否需要更新待入职员工信息
                if (talentStaff.getApplyStaffId() != null && StringUtils.isNotBlank(talentStaff.getApplyStaffId())) {
                    this.updateApplyStaff(hrTalentStaffDTO, talentStaff);
                }
                //判断是否需要修改员工标签
                this.createStaffNote(hrTalentStaffDTO, dto);
                //判断是否需要修改其他信息
                this.createStaffField(hrTalentStaffDTO, dto);
                //判断是否需要修改适配岗位
                this.createStaffStation(hrTalentStaffDTO, dto);
                log.info("Update HrTalentStaff:{}", hrTalentStaffDTO);
                // 操作日志
                setDict(hrTalentStaffDTO);
                setDict(dto);
                this.sysOperLogService.insertSysOperLog(
                    hrTalentStaffDTO.getIzDefault() ? ModuleTypeEnum.TALENTS.getValue() : ModuleTypeEnum.STAFF.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrTalentStaffDTO),
                    HrTalentStaffDTO.class,
                    null,
                    JSON.toJSONString(dto),
                    JSON.toJSONString(hrTalentStaffDTO),
                    null,
                    HrTalentStaffDTO.class
                );
                return hrTalentStaffDTO;
            });
    }

    /**
     * 计算退休按钮是否出现
     *
     * @param hrTalentStaffDTO
     */
    private void ifRetire(HrTalentStaffDTO hrTalentStaffDTO) {
        //获取要提醒的天数
        QueryWrapper<HrRemindConf> hrRemindConfQueryWrapper = new QueryWrapper<>();
        hrRemindConfQueryWrapper.eq("remind_key", "staff_retire");
        hrRemindConfQueryWrapper.eq("status", 0);
        hrRemindConfQueryWrapper.eq("is_delete", 0);
        HrRemindConf hrRemindConf = hrRemindConfRepository.selectOne(hrRemindConfQueryWrapper);
        String ruleAppend = hrRemindConf.getRuleAppend();
        JSONObject hrTalentStaff = JSON.parseObject(ruleAppend);
        int manAge = 60;
        if (hrTalentStaff != null && hrTalentStaff.getInteger("manAge") != null) {
            manAge = hrTalentStaff.getInteger("manAge");
        }
        int womanAge = 50;
        if (hrTalentStaff != null && hrTalentStaff.getInteger("womanAge") != null) {
            womanAge = hrTalentStaff.getInteger("womanAge");
        }
        //获取离退休的天数
        Integer retirementDate = getRetirementDate(hrTalentStaffDTO.getCertificateNum(), manAge, womanAge);
        Integer ruleDay = 90;
        if (hrRemindConf != null && hrRemindConf.getRuleDay() != null) {
            ruleDay = hrRemindConf.getRuleDay();
        }
        if (retirementDate <= ruleDay) {
            //显示退休通知按钮
            hrTalentStaffDTO.setIzRetire(3);
        }
    }

    private void updateApplyStaff(HrTalentStaffDTO hrTalentStaffDTO, HrTalentStaff talentStaff) {
        HrApplyEntryStaff hrApplyEntryStaff = hrApplyEntryStaffRepository.selectOne(new QueryWrapper<HrApplyEntryStaff>()
            .eq("id", talentStaff.getApplyStaffId()).le("entry_status", EntryStatus.SEND_CONTRACT.getKey()));
        if (hrApplyEntryStaff != null) {
            if (!talentStaff.getName().equals(hrTalentStaffDTO.getName())) {
                hrApplyEntryStaff.setName(hrTalentStaffDTO.getName());
            }
            if (!talentStaff.getPhone().equals(hrTalentStaffDTO.getPhone())) {
                hrApplyEntryStaff.setPhone(hrTalentStaffDTO.getPhone());
            }
            if (!talentStaff.getCertificateNum().equals(hrTalentStaffDTO.getCertificateNum())) {
                hrApplyEntryStaff.setCertificateNum(hrTalentStaffDTO.getCertificateNum());
            }
            if (!talentStaff.getSex().equals(hrTalentStaffDTO.getSex())) {
                hrApplyEntryStaff.setSex(hrTalentStaffDTO.getSex());
            }
            if (hrTalentStaffDTO.getHouseholdRegistration() != null) {
                hrApplyEntryStaff.setHouseholdRegistration(hrTalentStaffDTO.getHouseholdRegistration());
            }
            hrApplyEntryStaffRepository.updateById(hrApplyEntryStaff);
        }
    }

    /**
     * 判断是否需要修改合同信息
     *
     * @param hrTalentStaffDTO 新数据
     * @return void
     * <AUTHOR>
     * @date 2021/9/28
     **/
    private void judgeStaffContract(HrTalentStaffDTO hrTalentStaffDTO) {
        List<HrContract> hrContracts = this.hrContractRepository.getContractlistByStaffId(hrTalentStaffDTO.getId());
        hrContracts.forEach(hrContract -> {
            // 判断姓名
            if (!hrContract.getStaffName().equals(hrTalentStaffDTO.getName())) {
                hrContract.setStaffName(hrTalentStaffDTO.getName());
            }
            // 判断身份证号码
            if (!hrContract.getIdNo().equals(hrTalentStaffDTO.getCertificateNum())) {
                hrContract.setIdNo(hrTalentStaffDTO.getCertificateNum());
            }
            // 判断手机号
            if (!hrContract.getPhone().equals(hrTalentStaffDTO.getPhone())) {
                hrContract.setPhone(hrTalentStaffDTO.getPhone());
            }
            this.hrContractRepository.updateById(hrContract);
        });
    }

    /**
     * 判断用户易云章证书是否需要更新 todo 所有更新员工手机号的地方调用方法
     *
     * @param talentStaff
     * @param hrTalentStaffDTO
     * @return void
     * <AUTHOR>
     * @date 2021/9/28
     **/
    private void judgeStaffSignCert(HrTalentStaff talentStaff, HrTalentStaffDTO hrTalentStaffDTO) {
        HrStaffSignCert hrStaffSignCert = this.hrStaffSignCertService.getByStaffId(talentStaff.getId());
        if (hrStaffSignCert != null) {
            // 若身份证没变,判断姓名手机号是否更改
            if (talentStaff.getCertificateNum().equals(hrTalentStaffDTO.getCertificateNum())) {
                // 姓名修改则更新姓名
                if (!talentStaff.getName().equals(hrTalentStaffDTO.getName())) {
                    // 更新证书主体名称
                    this.hrStaffSignCertService.updateCertUserName(talentStaff.getCertificateNum(), hrTalentStaffDTO.getName(), talentStaff.getPhone(), hrStaffSignCert);
                }
                // 手机号修改则更新手机号
                if (!talentStaff.getPhone().equals(hrTalentStaffDTO.getPhone())) {
                    // 更新证书主体手机号
                    this.hrStaffSignCertService.updateCertUserPhone(talentStaff.getCertificateNum(), talentStaff.getName(), hrTalentStaffDTO.getPhone(), hrStaffSignCert);
                }
            } else {
                // 身份证信息改变 则删除证书数据
                this.hrStaffSignCertService.removeById(hrStaffSignCert.getId());
            }
        }
    }

    /**
     * 创建员工标签
     *
     * @param hrTalentStaffDTO 前端传值
     * @param dto              数据库查询
     */
    private void createStaffNote(HrTalentStaffDTO hrTalentStaffDTO, HrTalentStaffDTO dto) {
        //查询之前的
        boolean staffNoteFlag = false;
        List<HrStaffNote> staffNoteList = hrStaffNoteService.list(new QueryWrapper<HrStaffNote>().eq("staff_id", hrTalentStaffDTO.getId()));
        List<HrStaffNote> staffNotes = hrTalentStaffDTO.getStaffNoteList();
        if (CollectionUtils.isNotEmpty(staffNotes)) {
            //长度不一样，一定有所改变
            if (staffNotes.size() == staffNoteList.size()) {
                //长度一样，判断员工标签是否一样
                for (HrStaffNote staffNote : staffNotes) {
                    boolean anyMatch = staffNoteList.stream().anyMatch(hrStaffNote -> staffNote.getStaffNote().equals(hrStaffNote.getStaffNote()));
                    if (!anyMatch) {
                        staffNoteFlag = true;
                    }
                }
            } else {
                staffNoteFlag = true;
            }
            //是否修改员工标签
            if (staffNoteFlag) {
                dto.setStaffNote(staffNoteList.size() == 0 ? "" : String.join(",", staffNoteList.stream().map(HrStaffNote::getStaffNote).collect(Collectors.toList())));
                hrTalentStaffDTO.setStaffNote(hrTalentStaffDTO.getStaffNoteList().size() == 0 ? "" : String.join(",", hrTalentStaffDTO.getStaffNoteList().stream().map(HrStaffNote::getStaffNote).collect(Collectors.toList())));
                //根据员工ID删除之前的数据
                hrStaffNoteService.remove(new QueryWrapper<HrStaffNote>().eq("staff_id", hrTalentStaffDTO.getId()));
                //重新创建员工标签
                for (HrStaffNote staffNote : staffNotes) {
                    staffNote.setStaffId(hrTalentStaffDTO.getId());
                    hrStaffNoteService.save(staffNote);
                }
            }
        }
        if (CollectionUtils.isEmpty(staffNotes) && CollectionUtils.isNotEmpty(staffNoteList)) {
            dto.setStaffNote(staffNoteList.size() == 0 ? "" : String.join(",", staffNoteList.stream().map(HrStaffNote::getStaffNote).collect(Collectors.toList())));
            hrTalentStaffDTO.setStaffNote("");
            //根据员工ID删除之前的数据
            hrStaffNoteService.remove(new QueryWrapper<HrStaffNote>().eq("staff_id", hrTalentStaffDTO.getId()));
        }
    }

    /**
     * 创建其他信息
     *
     * @param hrTalentStaffDTO 前端传值
     * @param dto              数据库查询
     */
    private void createStaffField(HrTalentStaffDTO hrTalentStaffDTO, HrTalentStaffDTO dto) {
        JSONObject jsonObject = new JSONObject();
        JSONObject json = new JSONObject();
        boolean staffFieldFlag = false;
        List<HrStaffField> fieldList = hrStaffFieldService.list(new QueryWrapper<HrStaffField>().eq("staff_id", hrTalentStaffDTO.getId()));
        List<HrStaffField> staffFieldList = hrTalentStaffDTO.getStaffFieldList();
        if (CollectionUtils.isNotEmpty(staffFieldList)) {
            if (staffFieldList.size() == fieldList.size()) {
                //长度一样，判断其他信息是否一样
                for (HrStaffField hrStaffField : staffFieldList) {
                    boolean anyMatch = fieldList.stream().anyMatch(field -> hrStaffField.getFieldName().equals(field.getFieldName())
                        && hrStaffField.getFieldValue().equals(field.getFieldValue()));
                    if (!anyMatch) {
                        jsonObject.put(hrStaffField.getFieldName(), hrStaffField.getFieldValue());
                        staffFieldFlag = true;
                    }
                }
            } else {
                staffFieldFlag = true;
            }
            //是否修改其他信息
            if (staffFieldFlag) {
                fieldList.forEach(ls -> {
                    json.put(ls.getFieldName(), ls.getFieldValue());
                });
                dto.setStaffField(json.size() == 0 ? "" : json.toString());
                hrTalentStaffDTO.setStaffField(jsonObject.size() == 0 ? "" : jsonObject.toString());
                //根据员工ID删除之前的数据
                hrStaffFieldService.remove(new QueryWrapper<HrStaffField>().eq("staff_id", hrTalentStaffDTO.getId()));
                //重新创建其他信息
                for (HrStaffField hrStaffField : staffFieldList) {
                    hrStaffField.setStaffId(hrTalentStaffDTO.getId());
                    hrStaffFieldService.save(hrStaffField);
                }
            }
        }
        if (CollectionUtils.isEmpty(staffFieldList) && CollectionUtils.isNotEmpty(fieldList)) {
            fieldList.forEach(ls -> {
                json.put(ls.getFieldName(), ls.getFieldValue());
            });
            dto.setStaffField(json.size() == 0 ? "" : json.toString());
            hrTalentStaffDTO.setStaffField("");
            //根据员工ID删除之前的数据
            hrStaffFieldService.remove(new QueryWrapper<HrStaffField>().eq("staff_id", hrTalentStaffDTO.getId()));
        }
    }

    /**
     * 创建适配岗位
     *
     * @param hrTalentStaffDTO 前端传值
     * @param dto              数据库查询
     */
    private void createStaffStation(HrTalentStaffDTO hrTalentStaffDTO, HrTalentStaffDTO dto) {
        boolean staffStationFlag = false;
        List<HrStaffStation> staffStationList = hrStaffStationService.list(new QueryWrapper<HrStaffStation>().eq("staff_id", hrTalentStaffDTO.getId()));
        List<HrStation> stationList = hrTalentStaffDTO.getStationList();
        if (CollectionUtils.isNotEmpty(stationList)) {
            if (stationList.size() == staffStationList.size()) {
                //长度一样，判断其他信息是否一样
                for (HrStation hrStation : stationList) {
                    boolean anyMatch = staffStationList.stream().anyMatch(hrStaffStation -> hrStation.getId().equals(hrStaffStation.getId()));
                    if (!anyMatch) {
                        staffStationFlag = true;
                        break;
                    }
                }
            } else {//长度不一样，信息一定有所改变
                staffStationFlag = true;
            }
            if (staffStationFlag) {
                List<String> stationId = staffStationList.stream().map(HrStaffStation::getStationId).collect(Collectors.toList());
                List<HrStation> list = hrStationService.list(new QueryWrapper<HrStation>().in("id", stationId).eq("is_delete", 0));
                dto.setStation(list.size() == 0 ? "" : String.join(",", list.stream().map(HrStation::getProfessionName).collect(Collectors.toList())));
                List<String> collect = stationList.stream().map(HrStation::getId).collect(Collectors.toList());
                List<HrStation> hrStationList = hrStationService.list(new QueryWrapper<HrStation>().in("id", collect).eq("is_delete", 0));
                hrTalentStaffDTO.setStation(hrStationList.size() == 0 ? "" : String.join(",", hrStationList.stream().map(HrStation::getProfessionName).collect(Collectors.toList())));
                //根据员工ID删除之前的数据
                hrStaffStationService.remove(new QueryWrapper<HrStaffStation>().eq("staff_id", hrTalentStaffDTO.getId()));
                //重新创建适配岗位
                for (HrStation hrStation : hrTalentStaffDTO.getStationList()) {
                    HrStaffStation hrStaffStation = new HrStaffStation();
                    hrStaffStation.setStaffId(hrTalentStaffDTO.getId());
                    hrStaffStation.setStationId(hrStation.getId());
                    hrStaffStationService.save(hrStaffStation);
                }
            }
        }
        if (CollectionUtils.isEmpty(stationList) && CollectionUtils.isNotEmpty(staffStationList)) {
            List<String> stationId = staffStationList.stream().map(HrStaffStation::getStationId).collect(Collectors.toList());
            List<HrStation> list = hrStationService.list(new QueryWrapper<HrStation>().in("id", stationId).eq("is_delete", 0));
            dto.setStation(list.size() == 0 ? "" : String.join(",", list.stream().map(HrStation::getProfessionName).collect(Collectors.toList())));
            hrTalentStaffDTO.setStation("");
            //根据员工ID删除之前的数据
            hrStaffStationService.remove(new QueryWrapper<HrStaffStation>().eq("staff_id", hrTalentStaffDTO.getId()));
        }
    }

    /**
     * 查询员工管理详情
     *
     * @param id
     * @return
     */
    @Override
    public HrTalentStaffDTO getHrTalentStaff(String id) {
        log.info("Get HrTalentStaff :{}", id);
        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(id);
        HrTalentStaffDTO hrTalentStaffDTO = this.hrTalentStaffMapper.toDto(hrTalentStaff);
        if (hrTalentStaffDTO == null) {
            return new HrTalentStaffDTO();
        }
        this.dealHrTalentStaffInfo(id, hrTalentStaffDTO);
        return hrTalentStaffDTO;
    }

    /**
     * 处理员工信息
     *
     * @param id
     * @param hrTalentStaffDTO
     */
    private void dealHrTalentStaffInfo(String id, HrTalentStaffDTO hrTalentStaffDTO) {
        this.overheadInfo(hrTalentStaffDTO, id);
        this.copyMilitaryDate(hrTalentStaffDTO);
        // 处理省市区
        if (StringUtils.isNotBlank(hrTalentStaffDTO.getContactAddress())) {
            Map<String, String> addressMap = StringUtil.addressResolution(hrTalentStaffDTO.getContactAddress());
            if (addressMap != null && addressMap.size() > 0) {
                // 省市区
                String province = addressMap.get("province");
                String city = addressMap.get("city");
                String county = addressMap.get("county");
                String addressPrefix = (province == null ? "" : province) + "/" + (city == null ? "" : city) + "/" + (county == null ? "" : county);
                // 详细地址
                String town = addressMap.get("town");
                String village = addressMap.get("village");
                String houseNumber = addressMap.get("houseNumber");
                String addressDetail = (town == null ? "" : town) + (village == null ? "" : village) + (houseNumber == null ? "" : houseNumber);

                hrTalentStaffDTO.setContactAddressPrefix(addressPrefix);
                hrTalentStaffDTO.setContactAddressDetail(addressDetail);
            } else {
                hrTalentStaffDTO.setContactAddressDetail(hrTalentStaffDTO.getContactAddress());
            }
        }
    }

    /**
     * 返回员工字典值
     *
     * @param hrTalentStaffDTO
     */
    public void setDict(HrTalentStaffDTO hrTalentStaffDTO) {
        if (hrTalentStaffDTO.getCertificateType() != null) {//证件类型
            hrTalentStaffDTO.setCertificateTypeLabel(CertificateTypeEnum.CertificateType.getValueByKey(hrTalentStaffDTO.getCertificateType()));
        }
        if (hrTalentStaffDTO.getSex() != null) {//性别
            Map<Integer, String> sexType = codeTableService.findCodeTableByInnerName("sexType");
            hrTalentStaffDTO.setSexLabel(sexType.get(hrTalentStaffDTO.getSex()));
        }
        if (hrTalentStaffDTO.getMaritalStatus() != null) {//婚姻状态
            Map<Integer, String> marriageStates = codeTableService.findCodeTableByInnerName("marriageStates");
            hrTalentStaffDTO.setMaritalStatusLabel(marriageStates.get(hrTalentStaffDTO.getMaritalStatus()));
        }
        if (hrTalentStaffDTO.getHighestEducation() != null) {//最高学历
            Map<Integer, String> educationStates = codeTableService.findCodeTableByInnerName("educationStates");
            hrTalentStaffDTO.setHighestEducationLabel(educationStates.get(hrTalentStaffDTO.getHighestEducation()));
        }
        if (hrTalentStaffDTO.getHouseholdRegistration() != null) {//户口性质
            Map<Integer, String> residenceType = codeTableService.findCodeTableByInnerName("residenceType");
            hrTalentStaffDTO.setHouseholdRegistrationLabel(residenceType.get(hrTalentStaffDTO.getHouseholdRegistration()));
        }
        if (hrTalentStaffDTO.getStaffStatus() != null) {//员工状态
            Map<Integer, String> staffStates = codeTableService.findCodeTableByInnerName("staffStates");
            hrTalentStaffDTO.setStaffStatusLabel(staffStates.get(hrTalentStaffDTO.getStaffStatus()));
        }
        if (hrTalentStaffDTO.getPersonnelType() != null) {//人员类型
            Map<Integer, String> staffType = codeTableService.findCodeTableByInnerName("staffType");
            hrTalentStaffDTO.setPersonnelTypeLabel(staffType.get(hrTalentStaffDTO.getPersonnelType()));
        }
        if (hrTalentStaffDTO.getWorkStatus() != null) {//工作状态
            Map<Integer, String> workStatus = codeTableService.findCodeTableByInnerName("workStatus");
            hrTalentStaffDTO.setWorkStatusLabel(workStatus.get(hrTalentStaffDTO.getWorkStatus()));
        }
        if (hrTalentStaffDTO.getSalarySection() != null) {//月薪区间
            Map<Integer, String> monthlyPay = codeTableService.findCodeTableByInnerName("monthlyPay");
            hrTalentStaffDTO.setSalarySectionLabel(monthlyPay.get(hrTalentStaffDTO.getSalarySection()));
        }
        if (hrTalentStaffDTO.getWorkNature() != null) {//工作地性质
            Map<Integer, String> workNature = codeTableService.findCodeTableByInnerName("workNature");
            hrTalentStaffDTO.setWorkNatureLabel(workNature.get(hrTalentStaffDTO.getWorkNature()));
        }
        if (hrTalentStaffDTO.getNationality() != null) {//国籍
            Map<Integer, String> nationality = codeTableService.findCodeTableByInnerName("nationality");
            hrTalentStaffDTO.setNationalityLabel(nationality.get(hrTalentStaffDTO.getNationality()));
        }
        if (hrTalentStaffDTO.getPoliticsStatus() != null) {//政治面貌
            Map<Integer, String> politicalType = codeTableService.findCodeTableByInnerName("politicalType");
            hrTalentStaffDTO.setPoliticsStatusLabel(politicalType.get(hrTalentStaffDTO.getPoliticsStatus()));
        }
    }

    /**
     * 批量删除员工管理
     *
     * @param ids
     */
    @Override
    public void deleteHrTalentStaff(List<String> ids) {
        log.info("Delete HrTalentStaffs:{}", ids);
        if (ids.size() > 100) {
            throw new CommonException("一次只能删除100条数据！");
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        if (jwtUserDTO.getRealName() == null) {
            jwtUserDTO.setRealName("");
        }
        long l = System.currentTimeMillis();
        log.info("员工删除更新为人才开始：{}", l);
        hrTalentStaffRepository.updateIzDefault(ids, jwtUserDTO.getUserName());
        log.info("员工删除更新为人才结束：{}", System.currentTimeMillis() - l);
        //更新在职信息为工作经历
        hrStaffWorkExperienceService.processEmployeeDeletion(jwtUserDTO, ids);
    }

    /**
     * 批量删除人才信息
     *
     * @param ids
     */
    @Override
    public void deleteTalentedPerson(List<String> ids) {
        log.info("Delete HrTalentStaffs:{}", ids);
        if (ids.size() > 100) {
            throw new CommonException("一次只能删除100条数据！");
        }
        //批量删除附加信息
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        hrStaffWorkExperienceService.deleteBatchIds(ids, jwtUserDTO);
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.TALENTS.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
    }

    /**
     * 详情其他信息
     *
     * @param hrTalentStaffDTO
     * @param id
     */
    private void overheadInfo(HrTalentStaffDTO hrTalentStaffDTO, String id) {
        //根据出生日期计算年龄 年龄=（今日日期-出生日期）/10000
        if (hrTalentStaffDTO.getBirthday() != null) {
            String currentDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String birthday = hrTalentStaffDTO.getBirthday().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            hrTalentStaffDTO.setAge((Integer.parseInt(currentDate) - Integer.parseInt(birthday)) / 10000);
        }
        //返回字段值
        this.setDict(hrTalentStaffDTO);//返回字典值
        //查询其他信息
        List<HrStaffField> fieldList = hrStaffFieldService.list(new QueryWrapper<HrStaffField>().eq("staff_id", id));
        if (CollectionUtils.isNotEmpty(fieldList)) {
            hrTalentStaffDTO.setStaffFieldList(fieldList);
        }
        //查询员工标签
        List<HrStaffNote> staffNoteList = hrStaffNoteService.list(new QueryWrapper<HrStaffNote>().eq("staff_id", id));
        if (CollectionUtils.isNotEmpty(staffNoteList)) {
            hrTalentStaffDTO.setStaffNoteList(staffNoteList);
        }
        //查询适配岗位
        List<String> stationIdList = hrStaffStationService.list(new QueryWrapper<HrStaffStation>().eq("staff_id", id)).stream().map(HrStaffStation::getStationId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(stationIdList)) {
            List<HrStation> stationList = hrStationService.list(new QueryWrapper<HrStation>().in("id", stationIdList));
            hrTalentStaffDTO.setStationList(stationList);
        }
    }

    /**
     * 分页查询员工管理
     *
     * @param hrTalentStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrTalentStaffDTO hrTalentStaffDTO, Long pageNumber, Long pageSize) {
        Page<HrTalentStaff> page = new Page<>(pageNumber, pageSize);
        if (!hrTalentStaffDTO.getIzDefault()) {
            //获取当前登录人的数据权限---员工列表
            if (CollectionUtils.isEmpty(hrTalentStaffDTO.getClientIds())) {
                List<String> clientIds = hrClientService.selectClientIdByUserId();
                if (CollectionUtils.isEmpty(clientIds)) {
                    clientIds.add("");
                }
                hrTalentStaffDTO.setClientIds(clientIds);
            }
        }
        if (hrTalentStaffDTO.getEntryExitPage() != null) {//员工入离职列表
            IPage<HrTalentStaffDTO> iPage = this.hrTalentStaffRepository.entryResignationPage(page, hrTalentStaffDTO);
            return iPage;
        } else { //员工管理列表
            if (hrTalentStaffDTO.getJumpKey() != null) {
                if (hrTalentStaffDTO.getJumpKey().equals("staff_contract_expire") || hrTalentStaffDTO.getJumpKey().equals("staff_contract")){
                    this.jumpStaffContract(hrTalentStaffDTO);
                }else {
                    try {
                        List<String> ids = this.jumpParameterStaffId(hrTalentStaffDTO.getJumpKey());
                        if (ids == null || ids.isEmpty()) {
                            ids = new ArrayList<>();
                            ids.add("");
                        }
                        hrTalentStaffDTO.setIds(ids);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
            }
            if (StringUtils.isNotBlank(hrTalentStaffDTO.getProfessionName())) {
                List<String> ids = hrTalentStaffRepository.findStaffStation(hrTalentStaffDTO.getProfessionName());
                if (ids == null || ids.isEmpty()) {
                    ids = new ArrayList<>();
                    ids.add("");
                }
                hrTalentStaffDTO.setIds(ids);
            }
            IPage<HrTalentStaffDTO> iPage = this.hrTalentStaffRepository.findPage(page, hrTalentStaffDTO);
            if (hrTalentStaffDTO.getIzDefault()) {//人才
                List<String> ids = iPage.getRecords().stream().map(HrTalentStaffDTO::getId).collect(Collectors.toList());
                Map<String, List<HrTalentStaffDTO>> listMap = hrTalentStaffRepository.findStationByStaffId(ids).stream().collect(Collectors.groupingBy(HrTalentStaffDTO::getId));
                for (HrTalentStaffDTO staffDTO : iPage.getRecords()) {
                    String position = this.adaptationPosition(staffDTO.getId(), listMap);
                    if (position != null) {
                        staffDTO.setAdaptationPosition(position);
                    }
                }
            } else {//员工
                List<HrClient> allList = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("is_delete", 0));
                for (HrTalentStaffDTO staffDTO : iPage.getRecords()) {
                    this.setCustomerHierarchy(allList, new HrStaffExport(), staffDTO, staffDTO.getClientId());
                    if (hrTalentStaffDTO.getOptPage() != null && hrTalentStaffDTO.getOptPage() == 1) {
                        HrContract hrContract = hrContractRepository.selectOne(new QueryWrapper<HrContract>().eq("staff_id", staffDTO.getId()).orderByAsc("created_date").last("LIMIT 1"));
                        if (hrContract != null) {
                            staffDTO.setFirstContractStartDate(hrContract.getContractStartDate());
                        }
                    }
                }
            }
            return iPage;
        }
    }

    /**
     * 员工合同状态筛选
     * @param hrTalentStaffDTO
     */
    private void jumpStaffContract(HrTalentStaffDTO hrTalentStaffDTO) {
        List<Integer> staffStatus = Arrays.asList(StaffEnum.StaffStatusEnum.SEPARATION.getKey(),
            StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION.getKey(),
            StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey(),
            StaffEnum.StaffStatusEnum.RETIRE.getKey());
        hrTalentStaffDTO.setNotStaffStatusList(staffStatus);
        if (hrTalentStaffDTO.getJumpKey().equals("staff_contract")){
            hrTalentStaffDTO.setContractState(ContractEnum.ContractState.EXPIRING_SOON.getKey());
        }else if (hrTalentStaffDTO.getJumpKey().equals("staff_contract_expire")){
            JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
            HrRemindConfDTO hrRemindConfDTO = hrRemindConfRepository.getRemindConfByRoleAndKey(jwtUserDTO.getCurrentRoleId(), "staff_contract_expire");
            hrTalentStaffDTO.setContractState(ContractEnum.ContractState.EXPIRED.getKey()).setRuleDay(hrRemindConfDTO == null ? 0 : hrRemindConfDTO.getRuleDay());
        }
    }

    private List<String> jumpParameterStaffId(String jumpKey) throws ParseException {
        List<String> clientIdList = this.hrClientService.selectClientIdByUserId();
        QueryWrapper<HrTalentStaff> hs = new QueryWrapper<>();
        List<String> statusList = new ArrayList<>();
        LocalDate newdate = LocalDate.now();
        DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd");
        switch (jumpKey) {
            case "staff_retire"://员工退休
                statusList.add("3");
                List<HrTalentStaff> hrTalentStaffss = this.hrCommonFunctionsRepository.selectHrRetire(clientIdList, statusList);
                return hrTalentStaffss.stream().map(HrTalentStaff::getId).distinct().collect
                    (Collectors.toList());
            case "staff_medical"://医疗备案
                List<String> staffIds = new ArrayList<>();
                hs.eq("is_delete", "0");
                hs.in(CollectionUtils.isNotEmpty(clientIdList), "client_id", clientIdList);
                hs.eq("staff_status", "4");
                hs.isNotNull("medical_record_date");
                List<HrTalentStaff> hrTalentStaffs = this.hrTalentStaffRepository.selectList(hs);
                for (HrTalentStaff hrTalentStaff : hrTalentStaffs) {
                    if (hrTalentStaff.getMedicalRecordDate() != null) {
                        int old = DateUtils.calculateYearDifference(newdate, hrTalentStaff.getMedicalRecordDate());
                        if (old >= 3) {
                            staffIds.add(hrTalentStaff.getId());
                        }
                    }
                }
                if (statusList.size() > 0) {
                    return staffIds;
                }
                break;
            case "staff_birth"://生育薪资
                Integer ruleDay = this.hrProtocolRepository.selecttime("staff_birth");
                List<String> ids = new ArrayList<>();
                //获取到产假开始时间
                List<HrFertility> hrFertilityList = this.hrCommonFunctionsRepository.selectHrFertilityList(clientIdList);
                //获取当前日期
                for (HrFertility hrFertility : hrFertilityList) {
                    //时间加
                    Date d = newtime.parse(String.valueOf(hrFertility.getMaternityLeaveStartDate()));
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(d);
                    calendar.add(calendar.DATE, +ruleDay);
                    //产假提醒日期
                    LocalDate terminationtime = LocalDate.parse(newtime.format(calendar.getTime()));
                    if (hrFertility.getMaternityLeaveStartDate().isBefore(newdate) && (newdate.isBefore(terminationtime) || newdate.equals(terminationtime)) && terminationtime.isBefore(hrFertility.getMaternityLeaveEndDate())) {
                        ids.add(hrFertility.getStaffId());
                    }
                }
                if (ids.size() > 0) {
                    return ids;
                }
                break;
        }
        return null;
    }

    /**
     * 赋值适配岗位
     *
     * @param id
     * @return
     */
    private String adaptationPosition(String id, Map<String, List<HrTalentStaffDTO>> listMap) {
        //查询适配岗位
//        List<String> stationIdList = hrStaffStationService.list(new QueryWrapper<HrStaffStation>().eq("staff_id", id)).stream().map(HrStaffStation::getStationId).distinct().collect(Collectors.toList());
//       if (CollectionUtils.isNotEmpty(stationIdList)){
//           List<String> professionName = hrStationService.list(new QueryWrapper<HrStation>().in( "id", stationIdList)).stream().map(HrStation::getProfessionName).distinct().collect(Collectors.toList());
//           return String.join(";", professionName);
//       }
        if (CollectionUtils.isNotEmpty(listMap)) {
            List<HrTalentStaffDTO> hrTalentStaffDTOS = listMap.get(id);
            if (CollectionUtils.isNotEmpty(hrTalentStaffDTOS)) {
                String join = String.join(";", hrTalentStaffDTOS.stream().map(HrTalentStaffDTO::getProfessionName).distinct().collect(Collectors.toList()));
                return join;
            }
        }
        return null;
    }

    /**
     * 验证证件号码有效性
     *
     * @param certificateNum 证件号码
     * @return
     */
    @Override
    public HrTalentStaff getStaffByIdNumber(String certificateNum) {
        HrTalentStaff staff = hrTalentStaffRepository.selectOne(
            new QueryWrapper<HrTalentStaff>().eq("certificate_num", certificateNum).eq("is_delete", 0));
        if (staff == null) {//证件号码不存在
            staff = new HrTalentStaff();
        }
        return staff;
    }

    /**
     * 验证手机号码有效性
     *
     * @param phone 手机号码
     * @return
     */
    @Override
    public ResponseEntity getStaffByPhone(String phone) {
        List<HrTalentStaff> hrTalentStaffs = hrTalentStaffRepository.selectList(new QueryWrapper<HrTalentStaff>().eq("phone", phone));
        return ResponseUtil.buildSuccess(CollectionUtils.isNotEmpty(hrTalentStaffs) ? hrTalentStaffs.get(0) : new HrTalentStaff());
    }

    /**
     * 员工附加信息详情
     *
     * @param id 员工ID
     * @return
     */
    @Override
    public ExtrasDTO getExtrasById(String id) {
        //查询附加信息：在职信息
        ExtrasDTO extrasDTO = new ExtrasDTO();
        List<HrStaffWorkExperienceDTO> workCompanyDTOList = hrStaffWorkExperienceService.findWorkExperienceList(id, true);
        List<HrStaffWorkDTO> hrStaffWorkDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(workCompanyDTOList)) {
            for (HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO : workCompanyDTOList) {
                HrStaffWorkDTO hrStaffWorkDTO = new HrStaffWorkDTO();
                BeanUtils.copyProperties(hrStaffWorkExperienceDTO, hrStaffWorkDTO);
                //根据客户Id查询
                List<String> clientIdList = this.hrClientService.findClientIdList(hrStaffWorkExperienceDTO.getClientId());
                HrClientDTO hrClientDTO = new HrClientDTO();
                hrClientDTO.setClientIdList(clientIdList);
                hrClientDTO.setFlag(true);
                List<HrClientDTO> hrClientDTOList = hrClientService.findPage(hrClientDTO, (long) 1, (long) 999).getRecords();
                if (CollectionUtils.isNotEmpty(hrClientDTOList)) {
                    hrStaffWorkDTO.setHrClientDTOList(hrClientDTOList);
                }
                hrStaffWorkDTOS.add(hrStaffWorkDTO);
            }
            extrasDTO.setWorkCompanyDTOList(hrStaffWorkDTOS);
        }
        //查询附加信息：工作经历
        List<HrStaffWorkExperienceDTO> workExperienceDTOList = hrStaffWorkExperienceService.findWorkExperienceList(id, false);
        if (CollectionUtils.isNotEmpty(workExperienceDTOList)) {
            extrasDTO.setWorkExperienceDTOList(workExperienceDTOList);
        }
        //查询附加信息：教育经历
        List<HrStaffEducationDTO> staffEducationDTOList = hrStaffEducationService.findEducationList(id);
        if (CollectionUtils.isNotEmpty(staffEducationDTOList)) {
            extrasDTO.setStaffEducationDTOList(staffEducationDTOList);
        }
        //查询附加信息：应试经历
//        List<HrStaffInterviewDTO> staffInterviewDTOList = hrStaffInterviewService.findInterviewList(id);
//        if (CollectionUtils.isNotEmpty(staffInterviewDTOList)) {
//            extrasDTO.setStaffInterviewDTOList(staffInterviewDTOList);
//        }
        List<HrExamResultDTO> interviewList = hrStaffInterviewService.findInterviewList(id);
        //List<HrExamResultDTO> list = hrExamResultService.examResultExperience(id);
        if (CollectionUtils.isNotEmpty(interviewList)) {
            extrasDTO.setStaffInterviewDTOList(interviewList);
        }
        //查询附加信息：家庭成员
        List<HrStaffFamilyDTO> staffFamilyDTOList = hrStaffFamilyService.findFamilyList(id);
        if (CollectionUtils.isNotEmpty(staffFamilyDTOList)) {
            extrasDTO.setStaffFamilyDTOList(staffFamilyDTOList);
        }
        //查询附加信息：紧急联系人
        List<HrStaffContactsDTO> staffContactsDTOList = hrStaffContactsService.findContactsList(id);
        if (CollectionUtils.isNotEmpty(staffContactsDTOList)) {
            extrasDTO.setStaffContactsDTOList(staffContactsDTOList);
        }
        //查询附加信息：语言能力
        List<HrStaffLanguageDTO> staffLanguageDTOList = hrStaffLanguageService.findLanguageList(id);
        if (CollectionUtils.isNotEmpty(staffLanguageDTOList)) {
            extrasDTO.setStaffLanguageDTOList(staffLanguageDTOList);
        }
        //查询附加信息：专业能力
        List<HrStaffProfessionDTO> staffProfessionDTOList = hrStaffProfessionService.findProfessionList(id);
        if (CollectionUtils.isNotEmpty(staffProfessionDTOList)) {
            extrasDTO.setStaffProfessionDTOList(staffProfessionDTOList);
        }
        //查询附加信息：职业(工种)资格
        List<HrStaffQualificationDTO> staffQualificationDTOList = hrStaffQualificationService.findQualificationList(id);
        if (CollectionUtils.isNotEmpty(staffQualificationDTOList)) {
            extrasDTO.setStaffQualificationDTOList(staffQualificationDTOList);
        }
        //查询附加信息：职业技术能力
        List<HrStaffTechniqueDTO> staffTechniqueDTOList = hrStaffTechniqueService.findTechniqueList(id);
        if (CollectionUtils.isNotEmpty(staffTechniqueDTOList)) {
            extrasDTO.setStaffTechniqueDTOList(staffTechniqueDTOList);
        }
        //查询附加信息：证书
        List<HrStaffCertificateDTO> staffCertificateDTOList = hrStaffCertificateService.findCertificateList(id);
        if (CollectionUtils.isNotEmpty(staffCertificateDTOList)) {
            extrasDTO.setStaffCertificateDTOList(staffCertificateDTOList);
        }

        return extrasDTO;
    }

    /**
     * 查看报名详情
     *
     * @param hrRegistrationDetailsDTO
     * @return
     */
    @Override
    public ExtrasDTO getRegistrationDetails(HrRegistrationDetailsDTO hrRegistrationDetailsDTO) {
        HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsRepository.selectOne(
            new QueryWrapper<HrRegistrationDetails>().eq("brochure_id", hrRegistrationDetailsDTO.getBrochureId())
                .eq("station_id", hrRegistrationDetailsDTO.getStationId())
                .eq("staff_id", hrRegistrationDetailsDTO.getStaffId())
                .eq("is_delete", 0)
                .last("LIMIT 1"));
        ExtrasDTO extrasDTO = this.getExtrasById(hrRegistrationDetailsDTO.getStaffId());
        if (hrRegistrationDetails != null) {
            HrRegistrationInfo hrRegistrationInfo = hrRegistrationInfoRepository.selectById(hrRegistrationDetails.getInfoId());
            String registrationJson = hrRegistrationInfo.getRegistrationJson();
            List<HrStaffEducationDTO> highestEducationList = new ArrayList<>();
            List<HrStaffEducationDTO> firstEducationList = new ArrayList<>();
            if (StringUtils.isNotBlank(registrationJson)) {
                JSONObject jsonObject = JSON.parseObject(registrationJson);
                HrTalentStaffDTO hrTalentStaffsDTO = JSON.parseObject(registrationJson, HrTalentStaffDTO.class);
                for (Map.Entry<String, Object> stringObjectEntry : jsonObject.entrySet()) {
                    //最高学历 第一学历
                    if (stringObjectEntry.getKey().equals("highestEducationLists")) {
                        List<HrStaffEducationDTO> highestEducationLists = hrTalentStaffsDTO.getHighestEducationLists();
                        highestEducationList.addAll(highestEducationLists);
                    }
                    //第一学历
                    else if (stringObjectEntry.getKey().equals("firstEducation")) {
                        List<HrStaffEducationDTO> firstEducation = hrTalentStaffsDTO.getFirstEducation();
                        firstEducationList.addAll(firstEducation);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(highestEducationList)) {
                extrasDTO.setHighestEducationList(highestEducationList);
            }
            if (CollectionUtils.isNotEmpty(firstEducationList)) {
                extrasDTO.setFirstEducationList(firstEducationList);
            }
        }
        return extrasDTO;
    }

    /**
     * 人才附件上传
     *
     * @param staffId 人才ID
     * @param file    附件
     * @return
     */
    @Override
    public HrAppendixDTO uploadSingleFile(String staffId, MultipartFile file) {
        //附件信息
        HrAppendixDTO hrAppendixDTO = hrAppendixService.uploadSingleFile(file);
//        //添加人才附件信息
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(staffId);
//        String appendixIds = "";
//        if (hrTalentStaff.getAppendixIds()!=null){
//            appendixIds = hrTalentStaff.getAppendixIds()+","+hrAppendixDTO.getId();
//        }else {
//            appendixIds = hrAppendixDTO.getId();
//        }
//        if (appendixIds.startsWith(",")){
//            //判断第一个字符是不是 , 是则去除第一个字符、
//            appendixIds = appendixIds.substring(1);
//        }
        String appendixIds = hrTalentStaff.getAppendixIds() != null ? hrTalentStaff.getAppendixIds() + "," + hrAppendixDTO.getId() : hrAppendixDTO.getId();
        hrTalentStaff.setAppendixIds(appendixIds);
        hrTalentStaffRepository.updateById(hrTalentStaff);
        return hrAppendixDTO;
    }

    /**
     * 人才附件删除
     *
     * @param staffId    人才ID
     * @param appendixId 附件ID
     */
    @Override
    public void deleteMultipleFile(String staffId, String appendixId) {
        Optional.ofNullable(this.hrTalentStaffRepository.selectById(staffId))
            .ifPresent(hrTalentStaff -> {
                //删除附件
                hrAppendixService.deleteHrAppendix(appendixId);
                hrTalentStaff.setAppendixIds(hrTalentStaff.getAppendixIds().replaceAll(appendixId + ",", ""));
                this.hrTalentStaffRepository.updateById(hrTalentStaff);
            });
    }

    /**
     * 附件详情
     *
     * @param id 人才ID
     * @return
     */
    @Override
    public HrTalentStaffDTO getAppendixById(String id) {
        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(id);
        HrTalentStaffDTO hrTalentStaffDTO = this.hrTalentStaffMapper.toDto(hrTalentStaff);
        if (hrTalentStaffDTO.getAppendixIds() != null) {
            List<String> appendixIds = Arrays.asList(hrTalentStaffDTO.getAppendixIds().split(","));
            //查询附件信息
            List<HrAppendix> hrAppendixList = hrAppendixService.list(
                new QueryWrapper<HrAppendix>().in(CollectionUtils.isNotEmpty(appendixIds), "id", appendixIds).eq("is_delete", 0));
            if (CollectionUtils.isNotEmpty(hrAppendixList)) {
                hrTalentStaffDTO.setAppendixList(hrAppendixList);
            }
        }
        return hrTalentStaffDTO;
    }

    /**
     * 员工导出信息
     *
     * @param hrTalentStaffDTO
     * @param response
     * @return
     */
    @Override
    public String exportHrStaff(HrTalentStaffDTO hrTalentStaffDTO, HttpServletResponse response) {
        if (CollectionUtils.isEmpty(hrTalentStaffDTO.getClientIds())) {
            List<String> clientIds = hrClientService.selectClientIdByUserId();
            if (CollectionUtils.isEmpty(clientIds)) {
                clientIds.add("");
            }
            hrTalentStaffDTO.setClientIds(clientIds);
        }
        if (hrTalentStaffDTO.getJumpKey() != null) {
            if (hrTalentStaffDTO.getJumpKey().equals("staff_contract_expire") || hrTalentStaffDTO.getJumpKey().equals("staff_contract")) {
                this.jumpStaffContract(hrTalentStaffDTO);
            } else {
                try {
                    List<String> staffIds = this.jumpParameterStaffId(hrTalentStaffDTO.getJumpKey());
                    if (staffIds == null || staffIds.isEmpty()) {
                        staffIds = new ArrayList<>();
                        staffIds.add("");
                    }
                    hrTalentStaffDTO.setStaffIds(staffIds);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
        }
        //查询要导出的数据
        List<HrStaffExport> list = this.hrTalentStaffRepository.findHrStaff(hrTalentStaffDTO);
        if (list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        Map<Integer, String> nationality = codeTableService.findCodeTableByInnerName("nationality");
        Map<Integer, String> ownedBank = codeTableService.findCodeTableByInnerName("ownedBank");
        List<String> staffIds = list.stream().map(HrStaffExport::getId).collect(Collectors.toList());
        List<HrContract> hrContractList = hrContractRepository.selectLatestContractInfo(staffIds, "ASC");//导出员工的第一份合同
        List<HrContract> hrContractS = hrContractRepository.selectLatestContractInfo(staffIds, "DESC");//导出员工的最新的合同
        List<HrClient> allList = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("is_delete", 0));
        for (HrStaffExport hrStaffExport : list) {
            if (hrStaffExport.getNationality() != null) {
                hrStaffExport.setNationality(nationality.get(Integer.parseInt(hrStaffExport.getNationality())));
            }
            if (hrStaffExport.getOwnedBank() != null) {
                hrStaffExport.setOwnedBank(ownedBank.get(Integer.parseInt(hrStaffExport.getOwnedBank())));
            }
            List<HrContract> hrContracts = hrContractList.stream().filter(lst -> lst.getStaffId().equals(hrStaffExport.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hrContracts)) {
                HrContract hrContract = hrContracts.get(0);
                hrStaffExport.setContractStartDate(hrContract.getContractStartDate());
            }
            List<HrContract> contractList = hrContractS.stream().filter(lst -> lst.getStaffId().equals(hrStaffExport.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(contractList)) {
                HrContract hrContract = contractList.get(0);
                hrStaffExport.setContractEndDate(hrContract.getContractEndDate());
            }
            this.setCustomerHierarchy(allList, hrStaffExport, new HrTalentStaffDTO(), hrStaffExport.getClientId());
        }
        int listSize = list.size();
        List<String> ids = list.stream().map(HrStaffExport::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "员工信息", HrStaffExport.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.STAFF.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 赋值客户层级关系
     *
     * @param allList
     * @param hrStaffExport
     * @param hrTalentStaffDTO
     */
    private void setCustomerHierarchy(List<HrClient> allList, HrStaffExport hrStaffExport, HrTalentStaffDTO hrTalentStaffDTO, String clientId) {
        List<HrClientDTO> hrClientDTOList = hrClientMapper.toDto(allList);
        Optional<HrClientDTO> optional = hrClientDTOList.stream().filter(lst -> lst.getId().equals(clientId)).findFirst();
        if (optional != null && optional.isPresent()) {
            HrClientDTO parentClient = hrClientService.getParentClient(optional.get(), allList);
            List<HrClient> hrClientList = parentClient.getHrClientList();
            Optional<HrClient> one = hrClientList.stream().filter(lst -> !lst.getId().equals(clientId) && lst.getParentId().equals("0")).findFirst();
            if (one != null && one.isPresent()) {
                HrClient hrClient1 = one.get();
                hrStaffExport.setOneClientName(hrClient1.getClientName());
                hrTalentStaffDTO.setOneClientName(hrClient1.getClientName());
                Optional<HrClient> two = hrClientList.stream().filter(lst -> !lst.getId().equals(clientId) && lst.getParentId().equals(hrClient1.getId())).findFirst();
                if (two != null && two.isPresent()) {
                    HrClient hrClient2 = two.get();
                    hrStaffExport.setTwoClientName(hrClient2.getClientName());
                    hrTalentStaffDTO.setTwoClientName(hrClient2.getClientName());
                }
            }
        }
    }

    /**
     * 人才导出信息
     *
     * @param hrTalentStaffDTO
     * @param response
     * @return
     */
    @Override
    public String exportHrTalent(HrTalentStaffDTO hrTalentStaffDTO, HttpServletResponse response) {
        if (StringUtils.isNotBlank(hrTalentStaffDTO.getProfessionName())) {
            List<String> staffIds = hrTalentStaffRepository.findStaffStation(hrTalentStaffDTO.getProfessionName());
            if (staffIds == null || staffIds.isEmpty()) {
                staffIds = new ArrayList<>();
                staffIds.add("");
            }
            hrTalentStaffDTO.setStaffIds(staffIds);
        }
        //查询要导出的数据
        List<HrTalentExport> list = this.hrTalentStaffRepository.findHrTalent(hrTalentStaffDTO);
        if (list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        List<String> ids = list.stream().map(HrTalentExport::getId).collect(Collectors.toList());
        List<HrTalentStaffDTO> hrTalentStaffDTOList = hrTalentStaffRepository.findStationByStaffId(ids);
        Map<String, List<HrTalentStaffDTO>> listMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hrTalentStaffDTOList)) {
            listMap = hrTalentStaffRepository.findStationByStaffId(ids).stream().collect(Collectors.groupingBy(HrTalentStaffDTO::getId));
        }
        Map<Integer, String> nationality = codeTableService.findCodeTableByInnerName("nationality");
        for (HrTalentExport hrTalentExport : list) {
            if (hrTalentExport.getNationality() != null) {
                hrTalentExport.setNationality(nationality.get(Integer.parseInt(hrTalentExport.getNationality())));
            }
            //根据人才信息查询适配岗位
            String position = this.adaptationPosition(hrTalentExport.getId(), listMap);
            if (position != null) {
                hrTalentExport.setAdaptationPosition(position);
            }
        }
        int listSize = list.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "人才列表", HrTalentExport.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.TALENTS.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 人才导入信息
     *
     * @param file
     * @return
     */
    @Override
    public String importHrTalent(MultipartFile file) {
        // 设置进度条
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.TALENT.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.hrTalentStaffComponent.dealHrTalentImport(inputStream, redisKey, jwtUserDTO);
        return redisKey;
    }

    /**
     * 员工信息导入
     *
     * @param file
     * @return
     */
    @Override
    public String importHrStaff(MultipartFile file) {
        // 设置进度条
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.STAFF.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.hrTalentStaffComponent.dealHrStaffImport(inputStream, redisKey, jwtUserDTO);
        return redisKey;
    }

    /**
     * 人才导入模板
     *
     * @param response
     */
    @Override
    public String importHrTalentTemplate(HttpServletResponse response) {
        return excelPrefix + "人才信息导入模板.xlsx";

    }

    /**
     * 员工导入模板
     *
     * @param response
     */
    @Override
    public String importHrStaffTemplate(HttpServletResponse response) {
        return excelPrefix + "员工信息导入模板.xlsx";

    }

    /**
     * 模板处理国籍/银行下拉框
     *
     * @param workbook
     */
    private void disposeNationality(Workbook workbook, int start, int end, String sheetName, int sheetNameIndex, Boolean flag) {
        //查询国籍字典数据
        List<String> nationality = codeTableService.getCodeTableListByInnerName("nationality").stream().map(CodeTableDTO::getItemName).collect(Collectors.toList());
        String[] bank = nationality.toArray(new String[nationality.size()]);
        ExcelUtils.selectList(workbook, start, end, bank, sheetName, sheetNameIndex, true);
        if (flag) {
            //查询银行字典数据
            List<String> ownedBank = codeTableService.getCodeTableListByInnerName("ownedBank").stream().map(CodeTableDTO::getItemName).collect(Collectors.toList());
            String[] owned = ownedBank.toArray(new String[ownedBank.size()]);
            ExcelUtils.selectList(workbook, 35, 35, owned, "工资发放银行", 2, true);
        }
    }

    /**
     * 入职资料-基本信息
     *
     * @param id
     * @return
     */
    @Override
    public HrTalentStaffDTO getStaffBasicInfo(String id) {
        HrTalentStaffDTO hrTalentStaffDTO = this.hrTalentStaffRepository.getStaffBasicInfo(id);
        this.dealHrTalentStaffInfo(id, hrTalentStaffDTO);
        this.overheadInfo(hrTalentStaffDTO, id);//详情其他信息
        this.copyMilitaryDate(hrTalentStaffDTO);
        return hrTalentStaffDTO;
    }

    /**
     * 返回服兵役时间
     *
     * @param hrTalentStaffDTO
     */
    private void copyMilitaryDate(HrTalentStaffDTO hrTalentStaffDTO) {
        List<LocalDate> militaryDate = new ArrayList<>();
        militaryDate.add(hrTalentStaffDTO.getMilitaryStartDate());
        militaryDate.add(hrTalentStaffDTO.getMilitaryEndDate());
        if (CollectionUtils.isNotEmpty(militaryDate)) {
            hrTalentStaffDTO.setMilitaryDate(militaryDate);
        }
    }


    /**
     * 入职资料-保存基本信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    @Override
    public Optional<HrTalentStaffDTO> fillStaffBasicInfo(HrTalentStaffDTO hrTalentStaffDTO) {
        Optional<HrTalentStaffDTO> talentStaffDTO = this.updateHrTalentStaff(hrTalentStaffDTO);
        //修改在职信息
        HrStaffWorkExperience workExperience = this.hrStaffWorkExperienceService.getOne(new QueryWrapper<HrStaffWorkExperience>().eq("id", hrTalentStaffDTO.getJobId()));
        workExperience.setDeptName(hrTalentStaffDTO.getDeptName()).setSalarySection(hrTalentStaffDTO.getSalarySection())
            .setWorkNature(hrTalentStaffDTO.getWorkNature()).setWorkLocation(hrTalentStaffDTO.getWorkLocation());
        this.hrStaffWorkExperienceService.updateById(workExperience);

        log.info("Update HrTalentStaff:{}", talentStaffDTO);
        return talentStaffDTO;

    }

    /**
     * 员工入职资料填写状态
     *
     * @param id 员工状态
     * @return
     */
    @Override
    public Map<String, Object> getEntryInfoStatusById(String id) {
        HrTalentStaff staff = this.hrTalentStaffRepository.selectById(id);
        if (staff == null) {
            throw new CommonException("抱歉，员工信息不存在！");
        }
        // 检查有无电签合同
        List<HrContractAppendix> hrContractAppendixList = this.hrContractAppendixRepository.getStaffElectronicContractByStaffId(id);
        //检查有无生育信息
        List<HrFertility> fertilityList = this.hrFertilityRepository.selectList(new QueryWrapper<HrFertility>().eq("staff_id", id));
        //检查有无退休信息
        List<HrRetire> hrRetireList = this.hrRetireRepository.selectList(new QueryWrapper<HrRetire>().eq("staff_id", id).ne("status",5));
        JSONObject map = new JSONObject();
        map.put("staffStatus", staff.getStaffStatus());
        map.put("izStartEnd", staff.getIzStartEnd());
        map.put("izPreserve", staff.getIzPreserve());
        map.put("departureStaffId", staff.getDepartureStaffId());
        map.put("electronicContract", hrContractAppendixList.isEmpty() ? 0 : 1);
        map.put("fertilityList", fertilityList.isEmpty() ? 0 : 1);
        map.put("hrRetireList", hrRetireList.isEmpty() ? 0 : 1);
        map.put("renewalProcess", staff.getRenewalProcess());
        return map;
    }

    /**
     * 员工列表查询入职进度
     *
     * @param id 员工ID
     * @return
     */
    @Override
    public HrApplyEntryStaffDTO getSecondaryInfo(String id) {
        //根据员工ID查询对应的申请ID
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(id);
        if (StringUtils.isBlank(hrTalentStaff.getApplyStaffId())) {
            HrApplyEntryStaff staffServiceOne = hrApplyEntryStaffService.getOne(new QueryWrapper<HrApplyEntryStaff>().eq("client_id", hrTalentStaff.getClientId())
                .eq("station_id", hrTalentStaff.getId()).le("entry_status", EntryStatus.ENROLLMENT_COMPLETED.getKey()));
            hrTalentStaff.setApplyStaffId(staffServiceOne.getId());
        }
        return hrApplyEntryStaffService.getHrApplyEntryStaff(hrTalentStaff.getApplyStaffId());
    }

    /**
     * 查询员工入离职信息
     *
     * @param hrTalentStaffDTO
     * @return
     */

    @Override
    public Map<String, List> searchEntryHrStaff(HrTalentStaffDTO hrTalentStaffDTO) {
        /**
         * staffStatus 4----在职   5------离职
         * a.如果ids不为空，就导出选择的数据，前端不传其他搜索字段
         * b.如果在导出在职信息接口中存在离职的信息忽略或者提示信息
         * c.如果在导出离职信息接口中存在在职的信息忽略或者提示信息
         */
        //查询要导出的数据
        if (CollectionUtils.isEmpty(hrTalentStaffDTO.getClientIds())) {
            List<String> clientIds = hrClientService.selectClientIdByUserId();
            if (CollectionUtils.isEmpty(clientIds)) {
                clientIds.add("");
            }
            hrTalentStaffDTO.setClientIds(clientIds);
        }
        List<HrStaffEntryQuitExport> list = this.hrTalentStaffRepository.findStaffEntryQuit(hrTalentStaffDTO);
        if (list == null || list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        List<String> successList = new ArrayList<>();//成功返回id集合
        List<String> errorList = new ArrayList<>();//证件类型不是居民身份证集合
        for (HrStaffEntryQuitExport hrStaffEntryQuitExport : list) {
            //如果不是居民身份证不可导出
            if (hrStaffEntryQuitExport.getCertificateType() == CertificateTypeEnum.CertificateType.RESIDENT_IDENTIFICATION_CARD.getKey()) {
                successList.add(hrStaffEntryQuitExport.getStaffId());
            } else {//如果该数据不是前端传的在职信息数据也不可导出
                errorList.add(hrStaffEntryQuitExport.getName());
            }
        }
        Map<String, List> map = new HashMap<>();
        map.put("successList", successList);
        map.put("errorList", errorList);
        return map;
    }

    /**
     * 导出员工入离职信息
     *
     * @param hrTalentStaffDTO 员工ID集合
     * @param response
     */
    @Override
    public String exportEntryHrStaff(HrTalentStaffDTO hrTalentStaffDTO, HttpServletResponse response) {
        List<HrStaffEntryQuitExport> list = this.hrTalentStaffRepository.findStaffEntryQuit(hrTalentStaffDTO);
        List<String> ids = list.stream().map(HrStaffEntryQuitExport::getStaffId).collect(Collectors.toList());
        Map<Integer, String> nationality = codeTableService.findCodeTableByInnerName("nationality");
        list.forEach(hrStaffEntryQuitExport -> {
            if (hrStaffEntryQuitExport.getNationality() != null) {
                hrStaffEntryQuitExport.setNationality(nationality.get(Integer.parseInt(hrStaffEntryQuitExport.getNationality())));
            }
        });
        int listSize = list.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "员工" + hrTalentStaffDTO.getExcelName() + "信息", HrStaffEntryQuitExport.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.EMPLOYEE_ENTRY_RESIGNATION.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * GET /hr-talent-staffs/extras
     * <p>
     * 员工入职考试试卷查询
     *
     * @param
     * @return HrPaperManagementResource
     */
    @Override
    public List<HrQuestionDTO> getHrPaperManagementSelect() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        String staffId = jwtMiniDTO.getId();
        HrTalentStaff hrQuestion = this.hrTalentStaffRepository.selectById(staffId);
        List<HrQuestionDTO> hrQuestionDTO = this.hrTalentStaffRepository.selectQuestion(hrQuestion.getClientId());
        return hrQuestionDTO;
    }

    /**
     * 服务中心--根据名称以及身份证查询数据
     *
     * @param hrTalentStaffDTO 名称/身份证
     * @return
     */
    @Override
    public List<HrTalentStaffDTO> searchDataHrStaff(HrTalentStaffDTO hrTalentStaffDTO) {
        List<String> clientIds = new ArrayList<>();
        if (StringUtils.isNotBlank(hrTalentStaffDTO.getClientId())) {//客户不为null，根据选择的客户查询
            clientIds.add(hrTalentStaffDTO.getClientId());
        } else {//客户为null查询当前登录客户的数据
            List<String> clientIdByUserId = hrClientService.selectClientIdByUserId();
            clientIds.addAll(clientIdByUserId);
        }
        hrTalentStaffDTO.setClientIds(clientIds);
        List<HrTalentStaffDTO> talentStaffDTOS = hrTalentStaffRepository.searchDataHrStaff(hrTalentStaffDTO);
        talentStaffDTOS.forEach(this::setDict);
        return talentStaffDTOS;
    }

    /**
     * 检查并更改员工启用状态
     * 离职完成后45员工不可登录小程序
     */
    @Override
    public void updateStaffStatus() {
        //获取所有已经已经但是还能登录小程序的员工
        HrTalentStaffDTO hrTalentStaffDTO = new HrTalentStaffDTO();
        hrTalentStaffDTO.setStaffStatus(StaffEnum.StaffStatusEnum.SEPARATION.getKey()).setStatus(false);
        List<HrTalentStaff> hrTalentStaffs = hrTalentStaffRepository.findResignationStaff(hrTalentStaffDTO);
        LocalDate now = LocalDate.now();
        if (CollectionUtils.isNotEmpty(hrTalentStaffs)) {
            hrTalentStaffs.forEach(hrTalentStaff -> {
                if (hrTalentStaff.getResignationDate() != null) {
                    //离职完成后45员工不可登录小程序
                    LocalDate localDate = hrTalentStaff.getResignationDate().plusDays(45);
                    if (localDate.isEqual(now) || localDate.isBefore(now)) {
                        hrTalentStaff.setStatus(true);//状态更新为禁用
                    }
                    //员工离职180天后进入到人才库
                    LocalDate localDate1 = hrTalentStaff.getResignationDate().plusDays(180);
                    if (localDate1.isEqual(now) || localDate1.isBefore(now)) {
                        hrTalentStaff.setIzDefault(true).setClientId(null).setProtocolId(null).setStaffStatus(null).setIzStartEnd(0).setIzPreserve(0).setIzInsured(0)
                            .setApplyStaffId(null).setOpenId(null).setResignationDate(null).setDepartureStaffId(null).setRenewalProcess(null).setRenewalServiceId(null).setIsRenewalContract(0);
                    }
                    hrTalentStaffRepository.updateHrTalentStaff(hrTalentStaff);
                }
            });
        }
    }

    /**
     * 单人续签-合同模板预处理
     *
     * @return
     */
    @Override
    public TemplateDealParamsDTO renewalGenerateContract(TemplateDealParamsDTO params) {
        String staffId = params.getStaffId();
        String clientId = params.getClientId();

        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(staffId);
        // 检查并生成用户个人证书
        this.hrStaffSignCertService.checkAndGetStaffSignCert(staffId, clientId, hrTalentStaff.getName(), hrTalentStaff.getCertificateNum(), hrTalentStaff.getPhone());
        // 获取之前的合同信息
        HrContract oldHrContract = hrContractRepository.selectOne(new QueryWrapper<HrContract>()
            .eq("staff_id", staffId)
            .in("state", 1, 2, 3)
            .orderByDesc("contract_start_date")
            .last("LIMIT 1")
        );
        LocalDate contractStartDate = oldHrContract.getContractEndDate().plusDays(1);
        LocalDate contractEndDate = contractStartDate.plusYears(3).minusDays(1);
        // 查询数据库中历史未生效合同
        HrContract hrContract = hrContractRepository.selectOne(
            new QueryWrapper<HrContract>().eq("staff_id", staffId)
                .eq("contract_start_date", contractStartDate).eq("state", 0)
                .orderByDesc("created_date").last("LIMIT 1")
        );
        if (hrContract == null) {
            HrClient hrClient = hrClientRepository.selectById(clientId);
            hrContract = new HrContract()
                .setClientId(hrClient.getId())
                .setUnitNumber(hrClient.getUnitNumber())
                .setClientName(hrClient.getClientName())
                .setStaffId(hrTalentStaff.getId())
                .setSystemNum(hrTalentStaff.getSystemNum())
                .setStaffName(hrTalentStaff.getName())
                .setIdNo(hrTalentStaff.getCertificateNum())
                .setPhone(hrTalentStaff.getPhone())
                .setContractStartDate(contractStartDate)
                .setContractEndDate(contractEndDate)
                .setContractType(1);
            hrContractRepository.insert(hrContract);
        } else {
            hrContract.setContractStartDate(contractStartDate).setContractEndDate(contractEndDate).setContractType(1);
            hrContractRepository.updateById(hrContract);
        }
        // 删除数据库中历史预处理数据
        this.hrContractAppendixRepository.deleteTemplatePretreatment(hrContract.getId());

        List<String> templateIds;
        // 选择的合同来源方式
        Integer templateSource = params.getTemplateSource();
        // 合同模板
        if (ContractEnum.TemplateSource.TEMPLATE.getKey().equals(templateSource)) {
            // 模板id列表
            templateIds = params.getTemplateIds();
            if (templateIds.isEmpty()) {
                throw new CommonException("未选择合同模板！");
            }
        }
        // 合同组
        else if (ContractEnum.TemplateSource.CONTRACT_GROUP.getKey().equals(templateSource)) {
            if (StringUtils.isBlank(params.getContractGroupId())) {
                throw new CommonException("未选择合同组！");
            }
            // 获取合同组
            HrContractGroup hrContractGroup = this.hrContractGroupRepository.selectById(params.getContractGroupId());
            // 合同模板处理
            templateIds = Arrays.asList(hrContractGroup.getContractTemplate().split(","));
            // 合同模板组使用次数+1
            this.hrContractGroupRepository.frequencyPlus(params.getContractGroupId());
        } else {
            throw new CommonException("错误的合同模板来源!");
        }

        // 处理合同木本数据
        for (String ls : templateIds) {
            this.hrContractService.dealContractTemplate(hrContract, ls, true);
            this.hrContractTemplateService.usageCountPlus(ls);
        }

        // 查询返回信息
        List<HrContractAppendix> hrContractAppendices = this.hrContractAppendixRepository.getContractListByContractId(hrContract.getId());
        if (hrContractAppendices == null || hrContractAppendices.isEmpty()) {
            throw new CommonException("电签信息预处理失败！");
        }
        // 为前端处理电签合同项目显示列表
        this.hrContractService.dealContractAppendixList(hrContractAppendices, params);
        return params;
    }

    /**
     * 医疗备案页面数据
     *
     * @param staffId 员工ID
     * @return
     */
    @Override
    public HrTalentStaffDTO obtainMedicalRecordInfo(String staffId) {
        HrTalentStaffDTO hrTalentStaffDTO = hrTalentStaffRepository.obtainMedicalRecordInfo(staffId);
        //查询对应的操作信息
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(staffId, null);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)) {
            hrTalentStaffDTO.setApplyOpLogsList(applyOpLogsList);
        }
        return hrTalentStaffDTO;
    }

    /**
     * 医疗备案日期更新
     *
     * @param params
     * @return
     */
    @Override
    public void modifyMedicalRecordInfo(Map<String, Object> params) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String staffId = String.valueOf(params.get("id"));
        LocalDate medicalRecordDate = LocalDate.parse(String.valueOf(params.get("medicalRecordDate")), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(staffId);
        String message = "";
        if (hrTalentStaff.getMedicalRecordDate() == null) {
            message = jwtUserDTO.getRealName() + "填写了" + hrTalentStaff.getName() + "的医疗备案日期";
        } else {
            message = jwtUserDTO.getRealName() + "更新了" + hrTalentStaff.getName() + "的医疗备案日期";
        }
        hrTalentStaff.setMedicalRecordDate(medicalRecordDate);
        hrTalentStaffRepository.updateById(hrTalentStaff);
        this.hrApplyOpLogsService.saveHrApplyOpLogs(hrTalentStaff.getId(), jwtUserDTO.getId(), false, message);
    }

    /**
     * 查询员工对应的档案明细
     *
     * @return
     */
    @Override
    public List<HrArchivesDetail> fileDetailsStaff() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        return hrArchivesDetailRepository.getDealByStaffId(jwtMiniDTO.getId());
    }

    /**
     * 单人续签--确认发送
     *
     * @param electricSignDTO
     */
    @Override
    public void renewalSendConfirm(ElectricSignDTO electricSignDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        // 获取该员工未生效的合同
        LinkedList<HrContract> hrContractList = this.hrContractRepository.getNotEffectiveContractList(electricSignDTO.getStaffId(), electricSignDTO.getClientId());
        if (hrContractList.isEmpty()) {
            throw new CommonException("未查询到该员工未生效合同信息!");
        }
        HrContract hrContract = hrContractList.get(0);
        // 处理因系统异常导致员工存在多条未生效合同的问题
        if (hrContractList.size() > 1) {
            hrContractList.removeFirst();
            List<String> ids = hrContractList.stream().map(HrContract::getId).collect(Collectors.toList());
            this.hrContractRepository.deleteBatchIds(ids);
        }
        //赋值员工续签服务ID
        hrTalentStaffRepository.updateRenewalServiceId(RandomUtil.generateId(), hrContract.getStaffId());
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(hrContract.getStaffId());
        // 根据最终合同信息更新员工相关信息
        this.hrContractService.updateStaffRelatedInfo(hrContract, hrTalentStaff.getApplyStaffId());

        // 更新合同信息
        hrContract.setContractInitDate(LocalDate.now());
        this.hrContractRepository.updateById(hrContract);
        // 员工renewalProcess状态设置为待续签
        this.hrTalentStaffRepository.updateStaffRenewalProcess(electricSignDTO.getStaffId(), electricSignDTO.getClientId(), StaffEnum.RenewalProcessEnum.TO_BE_RENEWAL.getKey());
        // 发送短信通知
        HashMap<Integer, String> params = new HashMap<>();
        params.put(1, hrContract.getStaffName());
        params.put(2, hrContract.getClientName());
        this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.RENEWAL_NOTICE_USE.getTemplateCode(), hrContract.getPhone());
        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrTalentStaff.getRenewalServiceId(), null, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "通知" + hrContract.getStaffName() + "进行续签", null, ServiceCenterEnum.RENEWAL_SERVICE.getKey());
    }

    /**
     * 员工签署合同
     * @param contractId 合同ID
     */
    @Override
    public void renewalSignContract(String contractId) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        // 获取合同信息
        List<HrContractAppendixDTO> list = this.hrContractAppendixRepository.getStaffContractListByObject(jwtMiniDTO.getId(), jwtMiniDTO.getClientId(), contractId);
        list.forEach(ls -> {
            if (!ls.getState().equals(ContractEnum.ExamineState.SIGNED.getKey()) && !ls.getState().equals(ContractEnum.ExamineState.PASS.getKey())) {
                throw new CommonException(ls.getName() + "未完成！");
            }
        });
        // 员工renewalProcess状态设置为待审核
        this.hrTalentStaffRepository.updateStaffRenewalProcess(jwtMiniDTO.getId(), jwtMiniDTO.getClientId(), StaffEnum.RenewalProcessEnum.TO_BE_REVIEWED.getKey());
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(jwtMiniDTO.getId());
        this.hrApplyOpLogsService.saveHrApplyOpLogsMiniPhaseTWO(hrTalentStaff.getRenewalServiceId(), null, jwtMiniDTO.getId(), jwtMiniDTO.getName() + "完成了续签", true, ServiceCenterEnum.RENEWAL_SERVICE.getKey());
    }

    /**
     * 单人续签--合同模板项制作确认
     *
     * @param hrContractAppendixDTO
     */
    @Override
    public void renewalContractConfirmation(HrContractAppendixDTO hrContractAppendixDTO) {
        this.hrContractAppendixRepository.updateContractAppendix(hrContractAppendixDTO);
    }

    /**
     * 待签订续签劳动合同列表
     *
     * @param contractId
     * @return
     */
    @Override
    public Map<String, Object> getRenewalContractList(String contractId) {
        JWTMiniDTO user = SecurityUtils.getCurrentMini().get();
        // 获取合同信息
        HrContract hrContract = new HrContract();
        if (StringUtils.isNotBlank(contractId)) {
            hrContract = hrContractRepository.selectById(contractId);
        } else {
            hrContractRepository.selectOne(new QueryWrapper<HrContract>()
                .eq("staff_id", user.getId())
                .eq("state", ContractEnum.ContractState.NOT_ACTIVE.getKey())
                .eq("contract_type", 1)
                .orderByDesc("created_date").last("LIMIT 1"));
            hrContract = this.hrContractRepository.getContractByStaffIdAndClientId(user.getId(), user.getClientId());
        }
        if (hrContract == null) {
            throw new CommonException("未查询到合同信息！");
        }
        // 获取模板合同附件
        List<HrContractAppendixDTO> list = this.hrContractAppendixRepository.getStaffContractList(hrContract.getId());
        if (list == null || list.isEmpty()) {
            throw new CommonException("该合同无相关合同项信息！");
        }

        List<HrContractAppendixDTO> contractTemplateList = new ArrayList<>();
        // 将合同项进行分组
        list.forEach(ls -> {
            if (ls.getType().equals(ContractEnum.AppendixType.TEMPLATE_CONTRACT.getKey())) {
                HrContractTemplate hrContractTemplate = this.hrContractTemplateService.getById(ls.getTemplateId());
                if (hrContractTemplate != null) {
                    ls.setTemplateType(hrContractTemplate.getType());
                }
                contractTemplateList.add(ls);
            }
        });
        Map<String, Object> result = new HashMap<>();
        result.put("contractTemplateList", contractTemplateList);
        return result;
    }

    /**
     * 确认电签--审核续签
     *
     * @param hrContractDTO
     */
    @Override
    public void renewalConfirmationTelegram(HrContractDTO hrContractDTO) {
        if (StringUtils.isBlank(hrContractDTO.getId())) {
            throw new CommonException("合同id不能为空！");
        }
        if (hrContractDTO.getCheckResult() == null) {
            throw new CommonException("请选择审核结果！");
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrContract hrContract = this.hrContractRepository.selectById(hrContractDTO.getId());
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(hrContract.getStaffId());
        List<HrContractAppendixDTO> hrContractAppendixDTOList = this.hrContractAppendixRepository.getStaffContractListTemplate(
            new HrContractAppendixDTO().setContractId(hrContract.getId()));
        // 通知员工修改
        if (hrContractDTO.getCheckResult().equals(ContractEnum.CheckResult.FIX.getKey())) {
            // 处理合同附件审核
            this.electronicSignComponent.handleContractAuditFix(hrContractDTO, hrContractAppendixDTOList);

            // 更新员工RenewalProcess状态为待签合同
            this.hrTalentStaffRepository.updateStaffRenewalProcess(hrContract.getStaffId(), hrContract.getClientId(), StaffEnum.RenewalProcessEnum.TO_BE_RENEWAL.getKey());
            // 短信通知
            HashMap<Integer, String> params = new HashMap<>();
            params.put(1, hrContract.getStaffName());
            params.put(2, LocalDate.now().plusDays(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            params.put(3, CompanyInfoEnum.FIRST_PART_PHONE_THREE.getValue());
            this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.ELECTRIC_SIGN_FIX_USE.getTemplateCode(), hrContract.getPhone());
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrTalentStaff.getRenewalServiceId(), null, jwtUserDTO.getId(),
                jwtUserDTO.getRealName() + "通知" + hrContract.getStaffName() + "进行修改相关信息", null, ServiceCenterEnum.RENEWAL_SERVICE.getKey());
        } else if (hrContractDTO.getCheckResult().equals(ContractEnum.CheckResult.TO_VOID.getKey())) {// 合同作废
            this.toVoidRenewalContract(hrContractDTO, hrContract, jwtUserDTO, hrTalentStaff, hrContractAppendixDTOList);
        }
        // 审核通过
        else {
            // 劳动合同加盖电子章
            this.electronicSignComponent.handleContractAuditPass(hrContractAppendixDTOList);
            // 审核续签通过后续操作
            this.contractRenewalApproved(hrContract, hrContractDTO, jwtUserDTO, hrTalentStaff);
        }
        this.hrContractRepository.updateById(this.hrContractMapper.toEntity(hrContractDTO));

    }

    /**
     * 续签合同作废
     *
     * @param hrContractDTO
     * @param hrContract
     * @param jwtUserDTO
     * @param hrTalentStaff
     * @param hrContractAppendixList
     */
    private void toVoidRenewalContract(HrContractDTO hrContractDTO, HrContract hrContract, JWTUserDTO jwtUserDTO, HrTalentStaff hrTalentStaff, List<HrContractAppendixDTO> hrContractAppendixList) {
        // 处理合同附件作废
        this.electronicSignComponent.handleContractAuditToVoid(hrContractDTO, hrContractAppendixList);

        // 更新员工RenewalProcess状态为刚开始
        this.hrTalentStaffRepository.updateStaffRenewalProcess(hrContract.getStaffId(), hrContract.getClientId(), StaffEnum.RenewalProcessEnum.RENEWAL_FAILED.getKey());
        // 短信通知
        HashMap<Integer, String> params = new HashMap<>();
        params.put(1, hrContract.getStaffName());
        params.put(2, LocalDate.now().plusDays(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        params.put(3, CompanyInfoEnum.FIRST_PART_PHONE_THREE.getValue());
        this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.ELECTRIC_SIGN_FIX_USE.getTemplateCode(), hrContract.getPhone());
        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrTalentStaff.getRenewalServiceId(), null, jwtUserDTO.getId(),
            jwtUserDTO.getRealName() + "作废了" + hrContract.getStaffName() + "的续签合同。备注" + (StringUtils.isBlank(hrContractDTO.getCheckMsg()) ? "无" : hrContractDTO.getCheckMsg()), null, ServiceCenterEnum.RENEWAL_SERVICE.getKey());
    }

    /**
     * 审核续签通过
     *
     * @param hrContract
     * @param hrContractDTO
     * @param jwtUserDTO
     * @param hrTalentStaff
     */
    private void contractRenewalApproved(HrContract hrContract, HrContractDTO hrContractDTO, JWTUserDTO jwtUserDTO, HrTalentStaff hrTalentStaff) {
        // 根据合同日期标记合同状态
        if (hrContract.getContractStartDate().isAfter(LocalDate.now())) {
            hrContractDTO.setState(ContractEnum.ContractState.NOT_ACTIVE.getKey());
        } else {
            hrContractDTO.setState(ContractEnum.ContractState.IN_EFFECT.getKey());
        }
        Integer isRenewalContract = 0;
        HrRemindConf hrRemindConf = hrRemindConfRepository.selectByRemindKey("staff_contract");
        long l = LocalDate.now().toEpochDay();
        long day = hrContract.getContractEndDate().toEpochDay() - l;
        if (day <= (hrRemindConf == null ? 60 : hrRemindConf.getRuleDay()) || day <= 0) {
            isRenewalContract = 1;
        }
        hrTalentStaffRepository.updateRenewalContract(isRenewalContract, Collections.singletonList(hrContract.getStaffId()));

        this.hrTalentStaffRepository.updateStaffRenewalProcess(hrContract.getStaffId(), hrContract.getClientId(), StaffEnum.RenewalProcessEnum.RENEWAL_SUCCEEDED.getKey());
        HashMap<Integer, String> params = new HashMap<>();
        params.put(1, hrContract.getStaffName());
        this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.RENEWAL_PASS_NOTICE.getTemplateCode(), hrContract.getPhone());
        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrTalentStaff.getRenewalServiceId(), null, jwtUserDTO.getId(),
            jwtUserDTO.getRealName() + "审核通过了" + hrContract.getStaffName() + "的续签合同", null, ServiceCenterEnum.RENEWAL_SERVICE.getKey());
        // 更新员工档案添加档案电签信息
        HrArchivesManage hrArchivesManage = this.hrArchivesManageRepository.getArchivesByStaffId(hrContract.getStaffId());
        // 如果员工未创建档案，则建立档案
        if (hrArchivesManage == null) {
            hrArchivesManage = new HrArchivesManage();
            hrArchivesManage.setStaffId(hrContract.getStaffId());
            hrArchivesManage.setArchivesNum("DA" + RandomStringUtils.randomNumeric(13));
            hrArchivesManage.setArchivesName(hrContract.getStaffName());
            hrArchivesManage.setArchivesType(ArchivesEnum.ArchivesType.STAFF.getKey());
            hrArchivesManage.setArchivesStatus(ArchivesEnum.ArchivesState.ARCHIVED.getKey());
            hrArchivesManage.setWarehouseTime(LocalDate.now());
            this.hrArchivesManageRepository.insert(hrArchivesManage);
        }
        // 调入记录
        HrArchivesBring hrArchivesBring = new HrArchivesBring();
        hrArchivesBring.setArchivesId(hrArchivesManage.getId());
        hrArchivesBring.setCtType(ArchivesEnum.BringType.TRANSFER_IN.getKey());
        hrArchivesBring.setCtProposes(ArchivesEnum.BringProposesType.OTHER.getKey());
        this.hrArchivesBringRepository.insert(hrArchivesBring);
        // 获取电签附件列表
        List<HrContractAppendix> hrContractAppendices = this.hrContractAppendixRepository.getContractListByContractId(hrContract.getId());
        for (HrContractAppendix ls : hrContractAppendices) {
            HrArchivesDetail hrArchivesDetail = new HrArchivesDetail();
            hrArchivesDetail.setArchivesId(hrArchivesManage.getId());
            hrArchivesDetail.setName("XQ--" + ls.getName());
            hrArchivesDetail.setState(ArchivesEnum.DetailState.IN_GEAR.getKey());
            hrArchivesDetail.setType(ArchivesEnum.DetailType.ELECTRONIC_DOCUMENT.getKey());
            this.hrArchivesDetailRepository.insert(hrArchivesDetail);
            // 保存档案明细附件
            if (StringUtils.isNotBlank(ls.getAppendixId())) {
                Arrays.asList(ls.getAppendixId().split(",")).forEach(appendixId -> this.hrAppendixService.saveAppendixUnion(appendixId, hrArchivesDetail.getId()));
            }
            // 添加档案明细与调入调出关联
            ArchivesBringDetail archivesBringDetail = new ArchivesBringDetail();
            archivesBringDetail.setId(UUID.randomUUID().toString().replace("-", ""));
            archivesBringDetail.setBringId(hrArchivesBring.getId());
            archivesBringDetail.setDetailId(hrArchivesDetail.getId());
            this.hrArchivesBringRepository.insertArchivesBringDetail(archivesBringDetail);
        }
    }

    /**
     * 我的合同
     *
     * @return
     */
    @Override
    public List<HrContractDTO> myContract() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        List<HrContractDTO> hrContractDTOS = hrContractRepository.selectContract(new QueryWrapper<HrContract>()
            .ne("hc.state", ContractEnum.ContractState.NOT_ACTIVE.getKey())
            .eq("hc.staff_id", jwtMiniDTO.getId())
            .eq("hc.is_delete", 0)
            .orderByDesc("hc.contract_start_date"));
        if (CollectionUtils.isNotEmpty(hrContractDTOS)) {
            hrContractDTOS.forEach(hrContract -> {
                List<HrContractAppendix> hrContractAppendixList = this.hrContractAppendixRepository.selectList(new QueryWrapper<HrContractAppendix>()
                    .eq("contract_id", hrContract.getId())
                    .eq("type", ContractEnum.AppendixType.TEMPLATE_CONTRACT.getKey()));
                hrContract.setElectronicContract(hrContractAppendixList.isEmpty() ? 0 : 1);
                if (hrContract.getElectronicContract() == 0) {
                    //线下签去档案信息查询是否有上传附件，没有附件不可申请查看和下载服务
                    List<HrArchivesDetailDTO> hrArchivesDetailDTOS = this.hrArchivesDetailRepository.getDetailByStaffId(hrContract.getStaffId(), hrContract.getClientId());
                    hrContract.setIsAgainApply(hrArchivesDetailDTOS.isEmpty() ? 1 : 2);
                }
            });
        }
        return hrContractDTOS;
    }


    /**
     * 员工信息--关联信息
     *
     * @param id 员工ID
     * @return
     */
    @Override
    public ExtrasDTO staffRelatedInformation(String id) {
        ExtrasDTO extrasDTO = new ExtrasDTO();
        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(id);
        HrClientDTO hrClientDTO = hrClientRepository.findClientById(hrTalentStaff.getClientId());
        List<HrClient> allList = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("is_delete", 0));
        HrClientDTO parentClient = hrClientService.getParentClient(hrClientDTO, allList);
        extrasDTO.setHrClientDTO(parentClient);

        if (hrTalentStaff.getMedicalRecordDate() != null) {
            HrTalentStaffDTO hrTalentStaffDTO = hrTalentStaffMapper.toDto(hrTalentStaff);
            hrTalentStaffDTO.setClientName(hrClientDTO.getClientName());
            extrasDTO.setHrTalentStaffDTO(hrTalentStaffDTO);
        }
        List<HrWorkInjuryDTO> hrWorkInjuryDTOList = hrWorkInjuryRepository.workInjuryListByStaffId(id);
        if (CollectionUtils.isNotEmpty(hrWorkInjuryDTOList)) {
            hrWorkInjuryDTOList.forEach(ls -> {
                if (ls.getStatus().equals(WorkInjuryServiceEnum.NO_WORK_INJURY.getKey())) {
                    ls.setStatusLabel("未认定为工伤");
                }
                if (ls.getStatus().equals(WorkInjuryServiceEnum.PAY_OFF_PERIOD.getKey())
                    || ls.getStatus().equals(WorkInjuryServiceEnum.ON_HOLIDAY.getKey())
                    || ls.getStatus().equals(WorkInjuryServiceEnum.RETURN_TO_WORK.getKey())
                    || ls.getStatus().equals(WorkInjuryServiceEnum.ARRIVED.getKey())
                    || ls.getStatus().equals(WorkInjuryServiceEnum.ABSENTEEISM.getKey())
                    || ls.getStatus().equals(WorkInjuryServiceEnum.RENEW_PERIOD.getKey())
                    || ls.getStatus().equals(WorkInjuryServiceEnum.ARRIVE_BY_DEFAULT.getKey())) {
                    ls.setStatusLabel("认定为工伤");
                }
            });
            extrasDTO.setHrWorkInjuryDTOList(hrWorkInjuryDTOList);
        }
        List<HrLaborAppraisalDTO> hrLaborAppraisalDTOList = hrLaborAppraisalRepository.laborAppraisalListByStaffId(id);
        if (CollectionUtils.isNotEmpty(hrLaborAppraisalDTOList)) {
            hrLaborAppraisalDTOList.forEach(ls -> {
                HrApplyOpLogs hrApplyOpLogs = hrApplyOpLogsService.getOne(new QueryWrapper<HrApplyOpLogs>().eq("is_delete", 0).eq("apply_id", ls.getId()).isNotNull("appendix_ids").last("LIMIT 1"));
                if (hrApplyOpLogs != null && StringUtils.isNotBlank(hrApplyOpLogs.getAppendixIds())) {
                    HrAppendixDTO hrAppendixDTO = hrAppendixService.getHrAppendix(hrApplyOpLogs.getAppendixIds());
                    if (hrAppendixDTO != null) {
                        ls.setHrAppendixDTO(hrAppendixDTO);
                    }
                }
            });
            extrasDTO.setHrLaborAppraisalDTOList(hrLaborAppraisalDTOList);
        }
        List<HrFertilityDTO> hrFertilityDTOList = this.hrFertilityRepository.fertilityListByStaffId(id);
        if (CollectionUtils.isNotEmpty(hrFertilityDTOList)) {
            extrasDTO.setHrFertilityDTOList(hrFertilityDTOList);
        }
        HrSocialSecurityDTO hrSocialSecurityDTO = hrSocialSecurityRepository.socialSecurityListByStaffId(id);
        if (hrSocialSecurityDTO != null) {
            extrasDTO.setHrSocialSecurityDTO(hrSocialSecurityDTO);
        }
        List<HrCertificateIssuanceDTO> hrCertificateIssuanceDTOList = hrCertificateIssuanceRepository.certificateIssuanceListByStaffId(id);
        if (CollectionUtils.isNotEmpty(hrCertificateIssuanceDTOList)) {
            hrCertificateIssuanceDTOList.forEach(ls -> {
                if (StringUtils.isNotBlank(ls.getAppendixIds())) {
                    HrAppendixDTO hrAppendixDTO = hrAppendixService.getHrAppendix(ls.getAppendixIds());
                    if (hrAppendixDTO != null) {
                        ls.setHrAppendixDTO(hrAppendixDTO);
                    }
                }
            });
            extrasDTO.setHrCertificateIssuanceDTOList(hrCertificateIssuanceDTOList);
        }
        HrArchivesManageDTO hrArchivesManageDTO = hrArchivesManageRepository.getArchivesByClientIdAndStaffId(id);
        if (hrArchivesManageDTO != null) {
            extrasDTO.setHrArchivesManageDTO(hrArchivesManageDTO);
        }
        List<HrContract> hrContracts = hrContractRepository.selectList(new QueryWrapper<HrContract>().eq("is_delete", 0).eq("staff_id", id).orderByAsc("contract_start_date"));
        List<HrContractDTO> hrContractDTOList = hrContractMapper.toDto(hrContracts);
        if (CollectionUtils.isNotEmpty(hrContractDTOList)) {
            extrasDTO.setHrContractDTOList(hrContractDTOList);
        }
        List<HrStaffSecondmentDTO> hrStaffSecondmentDTOList = hrStaffSecondmentRepository.findSecondmentList(id);
        if (CollectionUtils.isNotEmpty(hrStaffSecondmentDTOList)) {
            extrasDTO.setHrStaffSecondmentDTOList(hrStaffSecondmentDTOList);
        }
        List<HrMaternityAllowanceDTO> hrMaternityAllowanceDTOS = hrMaternityAllowanceRepository.findList(new HrMaternityAllowanceDTO().setStaffId(id), null);
        if (CollectionUtils.isNotEmpty(hrMaternityAllowanceDTOS)) {
            extrasDTO.setHrMaternityAllowanceDTOS(hrMaternityAllowanceDTOS);
        }
        return extrasDTO;
    }

    /**
     * 不分页查询员工信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    @Override
    public List<HrTalentStaffDTO> findNotPageHrTalentStaff(HrTalentStaffDTO hrTalentStaffDTO) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        if (hrTalentStaffDTO.getJumpKey() != null) {
            if (hrTalentStaffDTO.getJumpKey().equals("staff_contract_expire") || hrTalentStaffDTO.getJumpKey().equals("staff_contract")) {
                this.jumpStaffContract(hrTalentStaffDTO);
            }else {
                try {
                    List<String> ids = this.jumpParameterStaffId(hrTalentStaffDTO.getJumpKey());
                    if (ids == null || ids.isEmpty()) {
                        return null;
                    }
                    hrTalentStaffDTO.setIds(ids);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
        }
        List<HrTalentStaffDTO> hrTalentStaffDTOList = hrTalentStaffRepository.findNotPageHrTalentStaff(hrTalentStaffDTO, clientIds);
        if (hrTalentStaffDTO.getDistinguish() != null && hrTalentStaffDTO.getDistinguish() == 1) {
            this.batchRenewalCheck(hrTalentStaffDTOList);
        }
        if (hrTalentStaffDTO.getDistinguish() != null && hrTalentStaffDTO.getDistinguish() == 2) {
            this.batchRenewalConfirmCheck(hrTalentStaffDTOList);
        }
        return hrTalentStaffDTOList;
    }

    private void batchRenewalConfirmCheck(List<HrTalentStaffDTO> hrTalentStaffDTOList) {
        if (CollectionUtils.isEmpty(hrTalentStaffDTOList)) {
            throw new CommonException("未查询到相关信息！");
        }
//        Set<String> stringSet = hrTalentStaffDTOList.stream().map(HrTalentStaffDTO::getClientId).collect(Collectors.toSet());
//        if (stringSet.size() > 1){
//            throw new CommonException("批量确认续签的员工需为同一个单位下员工！");
//        }
        List<HrTalentStaffDTO> collect = hrTalentStaffDTOList.stream().filter(lst -> lst.getRenewalProcess() == null || !lst.getRenewalProcess().equals(StaffEnum.RenewalProcessEnum.TO_BE_REVIEWED.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            throw new CommonException("选择的员工中有" + collect.size() + "个员工不是需要确认续签的流程！");
        }
        List<HrTalentStaffDTO> hrTalentStaffDTOS = hrTalentStaffDTOList.stream().filter(lst -> lst.getRenewalProcess() != null && lst.getRenewalProcess().equals(StaffEnum.RenewalProcessEnum.TO_BE_REVIEWED.getKey())).collect(Collectors.toList());
        List<String> staffNameS1 = new ArrayList<>();
        List<String> staffNameS2 = new ArrayList<>();
        List<String> staffNameS3 = new ArrayList<>();
        hrTalentStaffDTOS.forEach(hrTalentStaffDTO -> {
            List<HrContractAppendixDTO> confirmationTelegramList = this.hrContractAppendixRepository.getConfirmationTelegramList(hrTalentStaffDTO.getClientId(), hrTalentStaffDTO.getId());
            List<HrContractAppendixDTO> collect1 = confirmationTelegramList.stream().filter(ls -> ls.getState().equals(ContractEnum.ExamineState.FAIL.getKey())).collect(Collectors.toList());
            List<HrContractAppendixDTO> collect2 = confirmationTelegramList.stream().filter(ls -> ls.getState().equals(ContractEnum.ExamineState.COUNTERSIGN.getKey())).collect(Collectors.toList());
            List<HrContractAppendixDTO> collect3 = confirmationTelegramList.stream().filter(ls -> ls.getState().equals(ContractEnum.ExamineState.TO_VOID.getKey())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect1)) {
                staffNameS1.add(hrTalentStaffDTO.getName());
            }
            if (CollectionUtils.isNotEmpty(collect2)) {
                staffNameS2.add(hrTalentStaffDTO.getName());
            }
            if (CollectionUtils.isNotEmpty(collect3)) {
                staffNameS3.add(hrTalentStaffDTO.getName());
            }
        });
        if (CollectionUtils.isNotEmpty(staffNameS1) || CollectionUtils.isNotEmpty(staffNameS2) || CollectionUtils.isNotEmpty(staffNameS3)) {
            String message = (staffNameS1.size() == 0 ? "" : "" + String.join(",", staffNameS1) + "的合同已被拒绝通过,")
                + (staffNameS2.size() == 0 ? "" : "" + String.join(",", staffNameS2) + "的合同已被设置为重签,")
                + (staffNameS3.size() == 0 ? "" : "" + String.join(",", staffNameS3) + "的合同已被设置为作废,")
                + "请重新确认批量通过人员";
            throw new CommonException(message);
        }
    }

    /**
     * 批量发起续签检查员工信息
     *
     * @param hrTalentStaffDTOList
     * @return
     */
    public void batchRenewalCheck(List<HrTalentStaffDTO> hrTalentStaffDTOList) {
        if (CollectionUtils.isEmpty(hrTalentStaffDTOList)) {
            throw new CommonException("未查询到相关信息！");
        }
//        Set<String> stringSet = hrTalentStaffDTOList.stream().map(HrTalentStaffDTO::getClientId).collect(Collectors.toSet());
//        if (stringSet.size() > 1){
//            throw new CommonException("批量确认续签的员工需为同一个单位下员工！");
//        }
        List<HrTalentStaffDTO> hrTalentStaffDTOS = hrTalentStaffDTOList.stream().filter(ls -> ls.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hrTalentStaffDTOS)) {
            throw new CommonException("选择的数据中有" + hrTalentStaffDTOS.size() + "条员工处于离职状态，不可发起续签！");
        }
        List<HrTalentStaffDTO> collect = hrTalentStaffDTOList.stream().filter(ls -> ls.getRenewalProcess() == StaffEnum.RenewalProcessEnum.TO_BE_RENEWAL.getKey()
            || ls.getRenewalProcess() == StaffEnum.RenewalProcessEnum.TO_BE_REVIEWED.getKey()).collect(Collectors.toList());
        List<HrTalentStaffDTO> collect1 = hrTalentStaffDTOList.stream().filter(ls -> ls.getContractState() != ContractEnum.ContractState.EXPIRING_SOON.getKey()
            && ls.getContractState() != ContractEnum.ContractState.EXPIRED.getKey()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect) || CollectionUtils.isNotEmpty(collect1)) {
            String message = "本次批量续签选中员工 " + hrTalentStaffDTOList.size() + "人，其中："
                + (collect.size() == 0 ? "" : "正在续签的员工" + collect.size() + "人;")
                + (collect1.size() == 0 ? "" : "不可续签的员工" + collect1.size() + "人;");
            throw new CommonException(message);
        }
    }

    private HrContractAppendixDTO dealRenewalContractTemplate(HrContractTemplate hrContractTemplate, List<String> clientNames) {
        FormFieldDealDTO formFieldDealDTO = this.hrContractTemplateService.getRenewalContractTemplate(clientNames, hrContractTemplate);

        // 保存合同项信息
        HrContractAppendixDTO hrContractAppendix = new HrContractAppendixDTO();
        hrContractAppendix.setId(RandomUtil.generateId());
        hrContractAppendix.setName(hrContractTemplate.getTitle());
        hrContractAppendix.setState(ContractEnum.ExamineState.TO_BE_SIGNED.getKey());
        hrContractAppendix.setType(ContractEnum.AppendixType.TEMPLATE_CONTRACT.getKey());
        hrContractAppendix.setIsDelete(true);
        hrContractAppendix.setTemplateId(hrContractTemplate.getId());
        hrContractAppendix.setAppendixPath(hrContractTemplate.getTemplatePath());
        hrContractAppendix.setOriginalPath(hrContractTemplate.getTemplatePath());
        // 处理前端表单列表
        hrContractAppendix.setFirstPartInfo(JSON.toJSONString(formFieldDealDTO.getInputLabel().getFirstPartLabels()));
        return hrContractAppendix;
    }

    /**
     * 批量续签--合同模板预处理
     *
     * @param params
     * @return
     */
    @Override
    public TemplateDealParamsDTO batchRenewalGenerateContract(TemplateDealParamsDTO params) {
        StopWatch stopWatch = new StopWatch();
        log.info("批量续签预处理开始");
        stopWatch.start();
        if (params.getStaffIds() == null || params.getStaffIds().isEmpty()) {
            throw new CommonException("未选择员工!");
        }
        if (params.getTemplateSource() == null) {
            throw new CommonException("合同制作类型不能为空!");
        }

        List<String> templateIds = params.getTemplateIds();
        if (params.getTemplateSource().equals(ContractEnum.TemplateSource.TEMPLATE.getKey())) {
            if (params.getTemplateIds() == null || params.getTemplateIds().isEmpty()) {
                throw new CommonException("未选择合同模板!");
            }
            templateIds = params.getTemplateIds();
        } else if (params.getTemplateSource().equals(ContractEnum.TemplateSource.CONTRACT_GROUP.getKey())) {
            if (StringUtils.isBlank(params.getContractGroupId())) {
                throw new CommonException("未选择合同组！");
            }
            // 获取合同组
            HrContractGroup hrContractGroup = this.hrContractGroupRepository.selectById(params.getContractGroupId());
            this.hrContractGroupRepository.frequencyPlus(params.getContractGroupId());
            // 合同模板id列表
            List<String> contractTemplateList = Arrays.asList(hrContractGroup.getContractTemplate().split(","));
            // 合同模板应用次数+1
            contractTemplateList.forEach(this.hrContractTemplateService::usageCountPlus);
        } else {
            throw new CommonException("未知的合同制作类型!");
        }
        List<HrContractTemplate> hrContractTemplates = hrContractTemplateService.listByIds(templateIds);

        List<String> staffIds = params.getStaffIds();

        if (staffIds.size() > 100) {
            throw new CommonException("批量续签人数最大支持100人/次!");
        }
        // 获取之前的合同信息
        List<HrContract> hrContractList = hrContractRepository.selectList(new QueryWrapper<HrContract>()
            .in("staff_id", staffIds).in("state", 0, 1, 2, 3).orderByDesc("contract_end_date"));
        List<HrTalentStaffDTO> hrTalentStaffDTOList = hrTalentStaffRepository.findNotPageHrTalentStaff(new HrTalentStaffDTO().setIds(staffIds), null);

        List<String> clientNames = new ArrayList<>();
        hrTalentStaffDTOList.forEach(ls -> {
            // 检查并生成用户个人证书
            this.hrStaffSignCertService.checkAndGetStaffSignCert(ls.getId(), ls.getClientId(), ls.getName(), ls.getCertificateNum(), ls.getPhone());
            if (!clientNames.contains(ls.getClientName())) {
                clientNames.add(ls.getClientName());
            }
            HrContract hrContractData = hrContractList.stream().filter(lst -> lst.getStaffId().equalsIgnoreCase(ls.getId()) && !lst.getState().equals(ContractEnum.ContractState.NOT_ACTIVE.getKey())).findFirst().orElse(null);
            if (hrContractData == null) {
                throw new CommonException(ls.getName() + "不存在可用的合同信息");
            }
            LocalDate contractStartDate = hrContractData.getContractEndDate().plusDays(1);
            LocalDate contractEndDate = contractStartDate.plusYears(3).minusDays(1);
            List<HrContract> hrContracts = hrContractList.stream().filter(lst -> lst.getStaffId().equalsIgnoreCase(ls.getId()) && lst.getContractStartDate().isEqual(contractStartDate)).collect(Collectors.toList());
            HrContract contract;
            if (CollectionUtils.isEmpty(hrContracts)) {
                HrContract hrContract = new HrContract()
                    .setClientId(ls.getClientId())
                    .setUnitNumber(ls.getUnitNumber())
                    .setClientName(ls.getClientName())
                    .setStaffId(ls.getId())
                    .setSystemNum(ls.getSystemNum())
                    .setStaffName(ls.getName())
                    .setIdNo(ls.getCertificateNum())
                    .setPhone(ls.getPhone())
                    .setContractStartDate(contractStartDate)
                    .setContractEndDate(contractEndDate)
                    .setContractType(1);
                hrContractRepository.insert(hrContract);
                contract = hrContract;
            } else {
                if (hrContracts.size() > 1) {
                    throw new CommonException(ls.getName() + "存在多条未生效合同");
                }
                contract = hrContracts.get(0);
                contract.setContractStartDate(contractStartDate).setContractEndDate(contractEndDate).setContractType(1);
                hrContractRepository.updateById(contract);
            }
            this.hrContractAppendixRepository.deleteTemplatePretreatment(contract.getId());
        });

        // 组装前端显示信息
        List<HrContractAppendixDTO> contractList = new ArrayList<>();
        hrContractTemplates.forEach(ls -> {
            HrContractAppendixDTO hrContractAppendixDTO = this.dealRenewalContractTemplate(ls, clientNames);
            contractList.add(hrContractAppendixDTO);
        });
        params.setContractList(contractList);
        stopWatch.stop();
        log.info("批量续签预处理结束,耗时:{}", stopWatch.getTotalTimeSeconds());
        return params;
    }

    /**
     * 批量续签-制作合同确定
     *
     * @param params
     **/
    @Override
    public void batchRenewalConfirmation(TemplateDealParamsDTO params) {
        // inputLabelMap只用来取数据
        Map<String, Object> inputLabelMap = JSON.parseObject(params.getInputLabel(), Map.class);
        // 获取合同模板
        HrContractTemplate hrContractTemplate = this.hrContractTemplateService.getById(params.getTemplateId());
        Set<String> formFieldList;
        String redisKey = RedisKeyEnum.currencyKey.PDF_FORM_FIELD.getValue() + hrContractTemplate.getId();
        if (this.redisCache.hasKey(redisKey)) {
            formFieldList = this.redisCache.getCacheSet(redisKey);
        } else {
            formFieldList = PdfUtils.getAllFormField(hrContractTemplate.getTemplatePath());
            if (formFieldList == null) {
                throw new CommonException("模板表单域获取异常，请检查模板:" + hrContractTemplate.getTitle() + " 是否有效！");
            }
            this.redisCache.setCacheSet(redisKey, formFieldList);
        }
        // 判断模板类型数据校验
        ContractEnum.TemplateType templateTypeEnum = ContractEnum.TemplateType.getEnumByKey(hrContractTemplate.getType());
        if (templateTypeEnum == null) {
            throw new CommonException("未知的合同模板类型!");
        }
        // 是否统一合同开始日期
        boolean isUnifiedStart = false;
        // 是否统一合同结束日期
        boolean isUnified = false;
        LocalDate inputContractStartDate = null;
        LocalDate inputContractEndDate = null;
        int inputDeadlineYears = 0;
        int inputDeadlineMonth = 0;
        // 用户手动输入的 员工岗位
        String inputPositionName;
        // 劳动合同取值
        if (templateTypeEnum == ContractEnum.TemplateType.LABOR_CONTRACT) {
            String unifiedStart = String.valueOf(inputLabelMap.get("unifiedStart"));
            if (StringUtils.isBlank(unifiedStart) || "null".equals(unifiedStart)) {
                throw new CommonException("是否统一合同开始日期参数缺失！");
            } else {
                isUnifiedStart = "1".equals(unifiedStart);
            }
            String unified = String.valueOf(inputLabelMap.get("unified"));
            if (StringUtils.isBlank(unified) || "null".equals(unified)) {
                throw new CommonException("是否统一合同结束日期参数缺失！");
            } else {
                isUnified = "1".equals(unified);
            }

            if (isUnifiedStart) {
                if (inputLabelMap.get(ContractEnum.FormField.contractStartDate.getKey()) == null) {
                    throw new CommonException("合同日期开始日期为必填项");
                }
                inputContractStartDate = DateUtils.strToLocalDate(String.valueOf(inputLabelMap.get(ContractEnum.FormField.contractStartDate.getKey())));
                if (inputContractStartDate == null) {
                    throw new CommonException("合同开始时间格式错误!");
                }
            }
            if (isUnified) {
                if (inputLabelMap.get(ContractEnum.FormField.contractEndDate.getKey()) == null) {
                    throw new CommonException("合同日期结束日期为必填项");
                }
                inputContractEndDate = DateUtils.strToLocalDate(String.valueOf(inputLabelMap.get(ContractEnum.FormField.contractEndDate.getKey())));
                if (inputContractEndDate == null) {
                    throw new CommonException("统一合同结束日期合同结束日期为必填项!");
                }
            } else {
                String inputDeadlineYearsStr = String.valueOf(inputLabelMap.get(ContractEnum.FormField.contractDeadlineYears.getKey()));
                String inputDeadlineMonthStr = String.valueOf(inputLabelMap.get(ContractEnum.FormField.contractDeadlineMonth.getKey()));
                if ((StringUtils.isBlank(inputDeadlineYearsStr) || "null".equals(inputDeadlineYearsStr)) && (StringUtils.isBlank(inputDeadlineMonthStr) || "null".equals(inputDeadlineMonthStr))) {
                    throw new CommonException("不统一合同结束日期合同期限年数和合同期限月数必填一项");
                }
                if (StringUtils.isNotBlank(inputDeadlineMonthStr) && !inputDeadlineMonthStr.equals("null")) {
                    if (!NumberUtil.isNumber(inputDeadlineMonthStr)) {
                        throw new CommonException("合同期限月数只能输入整数!");
                    }
                    inputDeadlineMonth = Integer.parseInt(inputDeadlineMonthStr);
                } else {
                    if (!NumberUtil.isNumber(inputDeadlineYearsStr)) {
                        throw new CommonException("合同期限年数只能输入整数!");
                    }
                    inputDeadlineYears = Integer.parseInt(inputDeadlineYearsStr);
                }
            }
        }

        Object inputPositionNameObj = inputLabelMap.get(ContractEnum.FormField.positionName.getKey());
        if (inputPositionNameObj != null && StringUtils.isNotBlank(String.valueOf(inputPositionNameObj))) {
            // 批量续签 输入岗位，则使用用户输入的岗位
            inputPositionName = String.valueOf(inputPositionNameObj);
        } else {
            inputPositionName = null;
        }


        // 员工信息列表
        List<HrTalentStaff> hrTalentStaffList = hrTalentStaffRepository.selectBatchIds(params.getStaffIds());
        for (HrTalentStaff hrTalentStaff : hrTalentStaffList) {
            List<InputLabel> firstPartLabels = new LinkedList<>();
            List<InputLabel> secondPartLabels = new LinkedList<>();
            HrContract hrContract = this.hrContractRepository.getContractByStaffIdAndClientId(hrTalentStaff.getId(), hrTalentStaff.getClientId());
            String contractNo = RandomUtil.generateContractNo();
            ContractInfoForPdf contractInfo = this.hrTalentStaffRepository.selectSignInfoByStaffId(hrTalentStaff.getId());
            switch (templateTypeEnum) {
                // 劳动合同
                case LABOR_CONTRACT:
                    LocalDate contractStartDate;
                    LocalDate contractEndDate;
                    int contractDeadlineYears = 0;
                    int contractDeadlineMonth = 0;

                    if (isUnifiedStart) {
                        contractStartDate = inputContractStartDate;
                    } else {
                        contractStartDate = hrContract.getContractStartDate();
                    }
                    if (isUnified) {
                        contractEndDate = inputContractEndDate;
                        contractDeadlineMonth = DateUtils.localDateBetweenMonth(contractStartDate, contractEndDate);
                        /*if (contractStartDate.getDayOfMonth() != contractEndDate.getDayOfMonth()) {
                            contractDeadlineMonth = contractDeadlineMonth - 1;
                        }*/
                        contractDeadlineYears = BigDecimal.valueOf(contractDeadlineMonth).divide(BigDecimal.valueOf(12), 0, BigDecimal.ROUND_DOWN).intValue();
                    } else {
                        //不统一合同结束日期，根据填写的合同期限年数/合同期限月数计算合同结束日期
                        if (inputDeadlineMonth != 0) {
                            contractDeadlineMonth = inputDeadlineMonth;
                            contractDeadlineYears = BigDecimal.valueOf(contractDeadlineMonth).divide(BigDecimal.valueOf(12), 0, BigDecimal.ROUND_DOWN).intValue();
                            contractEndDate = contractStartDate.plusMonths(contractDeadlineMonth).minusDays(1);
                        } else {
                            contractDeadlineYears = inputDeadlineYears;
                            contractEndDate = contractStartDate.plusYears(contractDeadlineYears).minusDays(1);
                            contractDeadlineMonth = DateUtils.localDateBetweenMonth(contractStartDate, contractEndDate);
                        }
                    }

                    hrContract.setContractStartDate(contractStartDate);
                    hrContract.setContractEndDate(contractEndDate);
                    hrContractRepository.updateById(hrContract);

                    Integer probationDeadlineMonth = null;
                    LocalDate probationEndDate = contractInfo.getInternshipDate();
                    if (probationEndDate != null) {
                        probationDeadlineMonth = DateUtils.localDateBetweenMonth(contractStartDate, probationEndDate);
                    }

                    // 处理员工家庭住址
                    Map<String, String> addressMap = StringUtil.addressResolution(contractInfo.getSecondPartAddress());
                    Integer finalProbationDeadlineMonth = probationDeadlineMonth;
                    int finalContractDeadlineYears = contractDeadlineYears;
                    LocalDate finalContractStartDate = contractStartDate;
                    int finalContractDeadlineMonth = contractDeadlineMonth;
                    formFieldList.forEach(field -> {
                        Object o = null;
                        switch (field) {
                            case "contractNo":
                                o = contractNo;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractNo.getName()).setName(ContractEnum.FormField.contractNo.getKey()).setValue(o).setDisabled(true).setSort(1));
                                break;
                            case "firstPartName":
                                o = CompanyInfoEnum.FIRST_PART_NAME.getValue();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartName.getName()).setName(ContractEnum.FormField.firstPartName.getKey()).setValue(o).setSort(2));
                                break;
                            case "legalPersonName":
                                o = CompanyInfoEnum.LEGAL_PERSON_NAME.getValue();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.legalPersonName.getName()).setName(ContractEnum.FormField.legalPersonName.getKey()).setValue(o).setSort(3));
                                break;
                            case "firstPartResidence":
                                o = CompanyInfoEnum.FIRST_PART_RESIDENCE.getValue();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartResidence.getName()).setName(ContractEnum.FormField.firstPartResidence.getKey()).setValue(o).setSort(4));
                                break;
                            case "firstPartPhone":
                                o = CompanyInfoEnum.FIRST_PART_PHONE.getValue();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartPhone.getName()).setName(ContractEnum.FormField.firstPartPhone.getKey()).setValue(o).setSort(5));
                                break;
                            case "secondPartName":
                                o = contractInfo.getSecondPartName();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartName.getName()).setName(ContractEnum.FormField.secondPartName.getKey()).setValue(o).setDisabled(true).setSort(6));
                                break;
                            case "secondPartIdNo":
                                o = contractInfo.getSecondPartIdNo();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdNo.getName()).setName(ContractEnum.FormField.secondPartIdNo.getKey()).setValue(o).setDisabled(true).setSort(7));
                                break;
                            case "secondPartSex":
                                o = contractInfo.getSecondPartSex();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSex.getName()).setName(ContractEnum.FormField.secondPartSex.getKey()).setValue(o).setDisabled(true).setSort(8));
                                break;
                            case "sex":
                                o = contractInfo.getSecondPartSex();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.sex.getName()).setName(ContractEnum.FormField.sex.getKey()).setValue(o).setDisabled(true).setSort(8));
                                break;
                            case "birthday":
                                o = contractInfo.getBirthday();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBirthday.getName()).setName(ContractEnum.FormField.secondPartBirthday.getKey()).setValue(o).setDisabled(true).setSort(9));
                                break;
                            case "secondPartHometown":
                                o = contractInfo.getSecondPartHometown();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartHometown.getName()).setName(ContractEnum.FormField.secondPartHometown.getKey()).setValue(o).setSort(9));
                                break;
                            case "secondPartAddress":
                                o = contractInfo.getSecondPartAddress();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartAddress.getName()).setName(ContractEnum.FormField.secondPartAddress.getKey()).setValue(o).setSort(10));
                                String addressPrefix = null;
                                String addressDetail = null;
                                if (addressMap != null && addressMap.size() > 0) {
                                    String province = addressMap.get("province");
                                    String city = addressMap.get("city");
                                    String county = addressMap.get("county");
                                    addressPrefix = (province == null ? "" : province) + (city == null ? "" : city) + (county == null ? "" : county);
                                    // 详细地址
                                    String town = addressMap.get("town");
                                    String village = addressMap.get("village");
                                    String houseNumber = addressMap.get("houseNumber");
                                    addressDetail = (town == null ? "" : town) + (village == null ? "" : village) + (houseNumber == null ? "" : houseNumber);
                                }
                                secondPartLabels.add(new InputLabel().setLabel("通讯地址(省市区)").setName("secondPartAddressPrefix").setValue(addressPrefix).setType("change").setRequired(true).setSort(11));
                                secondPartLabels.add(new InputLabel().setLabel("通讯地址(详细地址)").setName("secondPartAddressDetail").setValue(addressDetail).setRequired(true).setSort(12));
                                break;
                            case "secondPartIdProvince":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdProvince.getName()).setName(ContractEnum.FormField.secondPartIdProvince.getKey()).setValue(addressMap == null ? o : addressMap.get("province")).setSort(11));
                                break;
                            case "secondPartIdCity":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdCity.getName()).setName(ContractEnum.FormField.secondPartIdCity.getKey()).setValue(addressMap == null ? o : addressMap.get("city")).setSort(12));
                                break;
                            case "secondPartIdCounty":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdCounty.getName()).setName(ContractEnum.FormField.secondPartIdCounty.getKey()).setValue(addressMap == null ? o : addressMap.get("county")).setSort(13));
                                break;
                            case "secondPartIdTown":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdTown.getName()).setName(ContractEnum.FormField.secondPartIdTown.getKey()).setValue(addressMap == null ? o : addressMap.get("town")).setSort(14));
                                break;
                            case "secondPartIdVillage":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdVillage.getName()).setName(ContractEnum.FormField.secondPartIdVillage.getKey()).setValue(addressMap == null ? o : addressMap.get("village")).setSort(15));
                                break;
                            case "secondPartHouseNo":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartHouseNo.getName()).setName(ContractEnum.FormField.secondPartHouseNo.getKey()).setValue(addressMap == null ? o : addressMap.get("houseNumber")).setSort(16));
                                break;
                            case "secondPartBuildingNo":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBuildingNo.getName()).setName(ContractEnum.FormField.secondPartBuildingNo.getKey()).setValue(null).setRequired(true).setSort(17));
                                break;
                            case "secondPartBuildingUnitNo":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBuildingUnitNo.getName()).setName(ContractEnum.FormField.secondPartBuildingUnitNo.getKey()).setValue(null).setRequired(true).setSort(18));
                                break;
                            case "secondPartBuildingRoomNo":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBuildingRoomNo.getName()).setName(ContractEnum.FormField.secondPartBuildingRoomNo.getKey()).setValue(null).setRequired(true).setSort(19));
                                break;
                            case "secondPartPhone":
                                o = contractInfo.getSecondPartPhone();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartPhone.getName()).setName(ContractEnum.FormField.secondPartPhone.getKey()).setValue(o).setDisabled(true).setSort(20));
                                break;
                            case "secondPartFamily":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartFamily.getName()).setName(ContractEnum.FormField.secondPartFamily.getKey()).setValue(o).setSort(21));
                                break;
                            case "secondPartZipCode":
                                o = contractInfo.getSecondPartZipCode();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartZipCode.getName()).setName(ContractEnum.FormField.secondPartZipCode.getKey()).setValue(o).setSort(22));
                                break;
                            case "contractDeadlineYears":
                                if (finalContractDeadlineYears != 0) {
                                    o = finalContractDeadlineYears;
                                }
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDeadlineYears.getName()).setName(ContractEnum.FormField.contractDeadlineYears.getKey()).setValue(o).setSort(6).setSort(23));
                                break;
                            case "contractStartDateYear":
                                o = finalContractStartDate;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractStartDate.getName()).setName(ContractEnum.FormField.contractStartDate.getKey()).setValue(o).setType("date").setDisabled(true).setSort(24));
                                break;
                            case "contractEndDateYear":
                                o = contractEndDate;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractEndDate.getName()).setName(ContractEnum.FormField.contractEndDate.getKey()).setValue(o).setType("date").setSort(25));
                                break;
                            case "contractDeadlineMonth":
                                if (finalContractDeadlineMonth != 0) {
                                    o = finalContractDeadlineMonth;
                                }
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDeadlineMonth.getName()).setName(ContractEnum.FormField.contractDeadlineMonth.getKey()).setValue(o).setSort(26));
                                break;
                            case "sendStartDateYear":
                                o = finalContractStartDate;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.sendStartDate.getName()).setName(ContractEnum.FormField.sendStartDate.getKey()).setValue(o).setType("date").setSort(27));
                                break;
                            case "probation":
                                o = finalProbationDeadlineMonth;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.probation.getName()).setName(ContractEnum.FormField.probation.getKey()).setValue(o).setSort(28));
                                break;
                            /*case "probationStartDateYear":
                                o = finalContractStartDate;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.probationStartDate.getName()).setName(ContractEnum.FormField.probationStartDate.getKey()).setValue(o).setType("date").setSort(29));
                                break;
                            case "probationEndDateYear":
                                o = probationEndDate;
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.probationEndDate.getName()).setName(ContractEnum.FormField.probationEndDate.getKey()).setValue(o).setType("date").setSort(30));
                                break;*/
                            case "employerName":
                                Object inputEmployerName = inputLabelMap.get(ContractEnum.FormField.employerName.getKey()); // 输入的用工单位
                                boolean isInputCustomEmployerName = inputEmployerName != null && !"多个".equals(inputEmployerName);
                                o = isInputCustomEmployerName ? inputEmployerName : contractInfo.getEmployerName();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.employerName.getName()).setName(ContractEnum.FormField.employerName.getKey()).setValue(o).setSort(31));
                                break;
                            case "positionName":
                                o = StringUtils.isNotBlank(inputPositionName) ? inputPositionName : contractInfo.getPostName();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.positionName.getName()).setName(ContractEnum.FormField.positionName.getKey()).setValue(o).setSort(32));
                                break;
                            case "workPlace":
                                o = contractInfo.getWorkPlace();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workPlace.getName()).setName(ContractEnum.FormField.workPlace.getKey()).setValue(o).setSort(33));
                                break;
                            case "workPlaceTownOrStreet":
                                o = inputLabelMap.get(ContractEnum.FormField.workPlaceTownOrStreet.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workPlaceTownOrStreet.getName()).setName(ContractEnum.FormField.workPlaceTownOrStreet.getKey()).setValue(o).setSort(34));
                                break;
                            case "workplaceVillageOrCommunity":
                                o = inputLabelMap.get(ContractEnum.FormField.workplaceVillageOrCommunity.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workplaceVillageOrCommunity.getName()).setName(ContractEnum.FormField.workplaceVillageOrCommunity.getKey()).setValue(o).setSort(35));
                                break;
                            case "workDaysPerWeek":
                                o = inputLabelMap.get(ContractEnum.FormField.workDaysPerWeek.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workDaysPerWeek.getName()).setName(ContractEnum.FormField.workDaysPerWeek.getKey()).setValue(o).setSort(36));
                                break;
                            case "workHoursPerDay":
                                o = inputLabelMap.get(ContractEnum.FormField.workHoursPerDay.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.workHoursPerDay.getName()).setName(ContractEnum.FormField.workHoursPerDay.getKey()).setValue(o).setSort(37));
                                break;
                            case "salary":
                                o = inputLabelMap.get(ContractEnum.FormField.salary.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.salary.getName()).setName(ContractEnum.FormField.salary.getKey()).setValue(o).setSort(38));
                                break;
                            case "dailyWage":
                                o = inputLabelMap.get(ContractEnum.FormField.dailyWage.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.dailyWage.getName()).setName(ContractEnum.FormField.dailyWage.getKey()).setValue(o).setSort(39));
                                break;
                            case "payday":
                                o = inputLabelMap.get(ContractEnum.FormField.payday.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.payday.getName()).setName(ContractEnum.FormField.payday.getKey()).setValue(o).setSort(40));
                                break;
                            case "agreedMatters":
                                o = inputLabelMap.get(ContractEnum.FormField.agreedMatters.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.agreedMatters.getName()).setName(ContractEnum.FormField.agreedMatters.getKey()).setValue(o).setSort(41));
                                break;
                            case "mainResponsible":
                                o = inputLabelMap.get(ContractEnum.FormField.mainResponsible.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.mainResponsible.getName()).setName(ContractEnum.FormField.mainResponsible.getKey()).setValue(o).setSort(42));
                                break;
                            case "firstPartSignDate":
                                o = inputLabelMap.get(ContractEnum.FormField.firstPartSignDate.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.firstPartSignDate.getName()).setName(ContractEnum.FormField.firstPartSignDate.getKey()).setValue(o).setType("date").setSort(43));
                                break;
                            case "secondPartSignDate":
                                o = inputLabelMap.get(ContractEnum.FormField.secondPartSignDate.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(o).setType("date").setSort(44));
                                break;
                            case "scopen":
                                o = inputLabelMap.get(ContractEnum.FormField.scopen.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.scopen.getName()).setName(ContractEnum.FormField.scopen.getKey()).setValue(o).setSort(45));
                                break;
                            case "allowance":
                                o = inputLabelMap.get(ContractEnum.FormField.allowance.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.allowance.getName()).setName(ContractEnum.FormField.allowance.getKey()).setValue(o).setSort(46));
                                break;
                            default:
                                break;
                        }
                    });
                    break;
                // 承诺书
                case COMMITMENT:
                    formFieldList.forEach(field -> {
                        Object o = null;
                        switch (field) {
                            case "secondPartName":
                                o = contractInfo.getSecondPartName();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartName.getName()).setName(ContractEnum.FormField.secondPartName.getKey()).setValue(o).setDisabled(true).setSort(1));
                                break;
                            case "secondPartIdNo":
                                o = contractInfo.getSecondPartIdNo();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdNo.getName()).setName(ContractEnum.FormField.secondPartIdNo.getKey()).setValue(o).setDisabled(true).setSort(2));
                                break;
                            case "reason":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.reason.getName()).setName(ContractEnum.FormField.reason.getKey()).setValue(o).setSort(3));
                                break;
                            case "socialSecurity":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.socialSecurity.getName()).setName(ContractEnum.FormField.socialSecurity.getKey()).setValue(o).setSort(4));
                                break;
                            case "expirationDate":
                                o = inputLabelMap.get(ContractEnum.FormField.expirationDate.getKey());
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.expirationDate.getName()).setName(ContractEnum.FormField.expirationDate.getKey()).setValue(o).setType("date").setSort(5));
                                break;
                            case "promiseDateYear":
                                o = inputLabelMap.get(ContractEnum.FormField.promiseDate.getKey());
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.promiseDate.getName()).setName(ContractEnum.FormField.promiseDate.getKey()).setValue(o).setType("date").setSort(6));
                                break;
                            case "employerId":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.employerId.getName()).setName(ContractEnum.FormField.employerId.getKey()).setValue(o).setSort(7));
                                break;
                            case "secondPartSignDate":
                                o = inputLabelMap.get(ContractEnum.FormField.secondPartSignDate.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(o).setType("date").setSort(8));
                                break;
                            default:
                                break;
                        }
                    });
                    break;
                // 确认函
                case CONFIRMATION:
                    formFieldList.forEach(field -> {
                        Object o;
                        switch (field) {
                            case "secondPartName":
                                o = contractInfo.getSecondPartName();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartName.getName()).setName(ContractEnum.FormField.secondPartName.getKey()).setValue(o).setDisabled(true).setSort(1));
                                break;
                            case "secondPartIdNo":
                                o = contractInfo.getSecondPartIdNo();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartIdNo.getName()).setName(ContractEnum.FormField.secondPartIdNo.getKey()).setValue(o).setDisabled(true).setSort(2));
                                break;
                            case "secondPartPhone":
                                o = contractInfo.getSecondPartPhone();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartPhone.getName()).setName(ContractEnum.FormField.secondPartPhone.getKey()).setValue(o).setDisabled(true).setSort(3));
                                break;
                            case "sex":
                                o = contractInfo.getSecondPartSex();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.sex.getName()).setName(ContractEnum.FormField.sex.getKey()).setValue(o).setDisabled(true).setSort(3));
                                break;
                            case "birthday":
                                o = contractInfo.getBirthday();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartBirthday.getName()).setName(ContractEnum.FormField.secondPartBirthday.getKey()).setValue(o).setDisabled(true).setSort(3));
                                break;
                            case "positionName":
                                o = StringUtils.isNotBlank(inputPositionName) ? inputPositionName : contractInfo.getPostName();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.positionName.getName()).setName(ContractEnum.FormField.positionName.getKey()).setValue(o).setSort(4));
                                break;
                            case "secondPartAddress":
                                o = contractInfo.getSecondPartAddress();
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartAddress.getName()).setName(ContractEnum.FormField.secondPartAddress.getKey()).setValue(o).setSort(4));
                                break;
                            case "secondPartSignDate":
                                o = inputLabelMap.get(ContractEnum.FormField.secondPartSignDate.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(o).setType("date").setSort(5));
                                break;
                            case "countExist":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.countExist.getName()).setName(ContractEnum.FormField.countExist.getKey()).setValue(null).setSort(6));
                                break;
                            case "countAgree":
                                secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.countAgree.getName()).setName(ContractEnum.FormField.countAgree.getKey()).setValue(null).setSort(7));
                                break;
                            case "employerName":
                                Object inputEmployerName = inputLabelMap.get(ContractEnum.FormField.employerName.getKey()); // 输入的用工单位
                                boolean isInputCustomEmployerName = inputEmployerName != null && !"多个".equals(inputEmployerName);
                                o = isInputCustomEmployerName ? inputEmployerName : contractInfo.getEmployerName();
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.employerName.getName()).setName(ContractEnum.FormField.employerName.getKey()).setValue(o).setSort(8));
                                break;
                            case "contractDischargeDate":
                                o = inputLabelMap.get(ContractEnum.FormField.contractDischargeDate.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDischargeDate.getName()).setName(ContractEnum.FormField.contractDischargeDate.getKey()).setValue(o).setType("date").setSort(9));
                                break;
                            case "contractDischargeReason":
                                o = inputLabelMap.get(ContractEnum.FormField.contractDischargeReason.getKey());
                                firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.contractDischargeReason.getName()).setName(ContractEnum.FormField.contractDischargeReason.getKey()).setValue(o).setSort(10));
                                break;
                            default:
                                break;
                        }
                    });
                    break;
                // 规章制度、一票否决、员工手册
                case RULES_REGULATIONS:
                case EMPLOYEE_HANDBOOK:
                case ONE_VOTE_VETO:
                    formFieldList.forEach(field -> {
                        if ("secondPartName".equals(field)) {
                            secondPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartName.getName()).setName(ContractEnum.FormField.secondPartName.getKey()).setValue(contractInfo.getSecondPartName()).setDisabled(true).setSort(1));
                        }
                        if ("secondPartSignDate".equals(field)) {
                            String secondPartSignDate = String.valueOf(inputLabelMap.get(ContractEnum.FormField.secondPartSignDate.getKey()));
                            firstPartLabels.add(new InputLabel().setLabel(ContractEnum.FormField.secondPartSignDate.getName()).setName(ContractEnum.FormField.secondPartSignDate.getKey()).setValue(secondPartSignDate).setType("date").setSort(2));
                        }
                    });
                    break;
                default:
                    break;
            }

            this.hrContractAppendixRepository.deleteByObject(new HrContractAppendix()
                .setContractId(hrContract.getId())
                .setTemplateId(hrContractTemplate.getId())
                .setState(ContractEnum.ExamineState.TO_BE_SIGNED.getKey())
                .setType(ContractEnum.AppendixType.TEMPLATE_CONTRACT.getKey())
            );
            // 创建合同附件
            HrContractAppendix hrContractAppendix = new HrContractAppendix();
            hrContractAppendix.setContractId(hrContract.getId());
            hrContractAppendix.setContractNum(contractNo);
            hrContractAppendix.setTemplateId(hrContractTemplate.getId());
            hrContractAppendix.setType(ContractEnum.AppendixType.TEMPLATE_CONTRACT.getKey());
            hrContractAppendix.setAppendixPath(hrContractTemplate.getTemplatePath());
            hrContractAppendix.setName(hrContractTemplate.getTitle());
            hrContractAppendix.setState(ContractEnum.ExamineState.TO_BE_SIGNED.getKey());
            hrContractAppendix.setFillInfo(JSON.toJSONString(new InputLabelDTO().setFirstPartLabels(firstPartLabels).setSecondPartLabels(secondPartLabels)));
            hrContractAppendix.setFirstPartInfo(JSON.toJSONString(firstPartLabels));
            hrContractAppendix.setSecondsPartInfo(JSON.toJSONString(secondPartLabels));
            hrContractAppendix.setOriginalPath(hrContractTemplate.getTemplatePath());
            hrContractAppendix.setIsDelete(true);
            this.hrContractAppendixRepository.insert(hrContractAppendix);
            // 模板使用次数+1
            this.hrContractTemplateService.usageCountPlus(hrContractTemplate.getId());
        }
    }

    /**
     * 批量续签-确认发送
     *
     * @param electricSignDTO
     */
    @Override
    public void renewalSendConfirmBatch(ElectricSignDTO electricSignDTO) {
        log.info("批量续签开始");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();

        Map<String, HashMap<Integer, String>> smsParamList = new HashMap<>();
        List<HrTalentStaffDTO> hrTalentStaffList = hrTalentStaffRepository.findStaffBatchIds(electricSignDTO.getStaffIds());
        for (HrTalentStaffDTO hrTalentStaff : hrTalentStaffList) {
            String staffId = hrTalentStaff.getId();
            // 获取该员工未生效的合同
            LinkedList<HrContract> hrContractList = this.hrContractRepository.getNotEffectiveContractList(staffId, hrTalentStaff.getClientId());
            if (hrContractList.isEmpty()) {
                throw new CommonException("未查询到员工[" + hrTalentStaff.getName() + "]未生效合同信息!");
            }
            HrContract hrContract = hrContractList.get(0);
            // 处理因系统异常导致员工存在多条未生效合同的问题
            if (hrContractList.size() > 1) {
                hrContractList.removeFirst();
                List<String> ids = hrContractList.stream().map(HrContract::getId).collect(Collectors.toList());
                this.hrContractRepository.deleteBatchIds(ids);
            }
            // 更新合同信息
            hrContract.setContractInitDate(LocalDate.now());
            this.hrContractRepository.updateById(hrContract);
            // 员工renewalProcess状态设置为待续签
            this.hrTalentStaffRepository.updateStaffRenewalProcess(staffId, hrTalentStaff.getClientId(), StaffEnum.RenewalProcessEnum.TO_BE_RENEWAL.getKey());

            // 获取合同项合同列表，更新合同为正常状态
            // List<HrContractAppendix> hrContractAppendices = this.hrContractAppendixRepository.getContractTemplateByContractId(hrContract.getId());
            this.hrContractAppendixRepository.updateByContractId(hrContract.getId(), null);

            // 保存操作记录
            String renewalServiceId = RandomUtil.generateId();
            hrTalentStaffRepository.updateRenewalServiceId(renewalServiceId, staffId);
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(renewalServiceId, null, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "通知" + hrContract.getStaffName() + "进行续签", null, ServiceCenterEnum.RENEWAL_SERVICE.getKey());

            HashMap<Integer, String> params = new HashMap<>();
            params.put(1, hrContract.getStaffName());
            params.put(2, hrContract.getClientName());
            smsParamList.put(hrContract.getPhone(), params);
            // params.put(3, LocalDate.now().plusDays(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            // params.put(4, CompanyInfoEnum.FIRST_PART_PHONE.getValue() + "," + CompanyInfoEnum.FIRST_PART_PHONE_TWO.getValue());

        }
        // 发送短信
        smsParamList.forEach((k, v) -> this.smsComponent.sendSmsSingle(k, MessageTemplateEnum.RENEWAL_NOTICE_USE.getTemplateCode(), v, jwtUserDTO.getRealName()));

        stopWatch.stop();
        log.info("批量续签结束,任务数:{},耗时:{}", electricSignDTO.getStaffIds(), stopWatch.getTotalTimeSeconds());
    }

    /**
     * 批量续签--批量确认通过
     *
     * @param electricSignDTO
     */
    @Override
    public void batchRenewalConfirm(ElectricSignDTO electricSignDTO) {
        if (electricSignDTO.getSignId() == null) {
            throw new CommonException("请选择印章后操作!");
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrTalentStaff> hrTalentStaffList = hrTalentStaffRepository.selectBatchIds(electricSignDTO.getStaffIds());
        for (HrTalentStaff hrTalentStaff : hrTalentStaffList) {
            // 获取该员工未生效的合同
            HrContract hrContract = this.hrContractRepository.getNotActiveContract(hrTalentStaff.getId(), electricSignDTO.getClientId());
            if (hrContract == null) {
                throw new CommonException("未查询到该员工未生效合同信息！");
            }
            HrContractDTO hrContractDTO = new HrContractDTO();
            hrContractDTO.setId(hrContract.getId());
            List<HrContractAppendixDTO> confirmationTelegramList = this.hrContractAppendixRepository.getConfirmationTelegramList(hrContract.getClientId(), hrContract.getStaffId());
            // 劳动合同加盖电子章
            confirmationTelegramList.forEach(ls -> ls.setSealSignId(electricSignDTO.getSignId()));
            this.electronicSignComponent.handleContractAuditPass(confirmationTelegramList);
            this.contractRenewalApproved(hrContract, hrContractDTO, jwtUserDTO, hrTalentStaff);
            this.hrContractRepository.updateById(this.hrContractMapper.toEntity(hrContractDTO));
        }
    }

    /**
     * 续签服务列表查询
     *
     * @param hrTalentStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrTalentStaffDTO> findPageRenewal(HrTalentStaffDTO hrTalentStaffDTO, Long pageNumber, Long pageSize) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        if (clientIds == null || clientIds.isEmpty()) {
            return new Page<>();
        }
        Page<HrBill> page = new Page<>(pageNumber, pageSize);
        IPage<HrTalentStaffDTO> iPage = this.hrTalentStaffRepository.findPageRenewal(page, hrTalentStaffDTO, clientIds);

        List<HrClient> allList = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("is_delete", 0));
        for (HrTalentStaffDTO staffDTO : iPage.getRecords()) {
            if (staffDTO.getRenewalProcess() == null) {
                staffDTO.setRenewalProcess(0);
            }
            this.setCustomerHierarchy(allList, new HrStaffExport(), staffDTO, staffDTO.getClientId());
        }
        return iPage;
    }

    @Override
    public String export(HrTalentStaffDTO hrTalentStaffDTO) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrTalentStaffDTO> list = ObjectUtils.isEmpty(clientIds) ? Collections.emptyList() : this.hrTalentStaffRepository.findListRenewal(hrTalentStaffDTO, clientIds);
        List<HrClient> allList = hrClientRepository.selectList(new QueryWrapper<HrClient>().eq("is_delete", 0));
        for(HrTalentStaffDTO talentStaffDTO : list) {
            this.setCustomerHierarchy(allList, new HrStaffExport(), talentStaffDTO, talentStaffDTO.getClientId());
        }
        List<String> ids = list.stream().map(HrTalentStaffDTO::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(list, ModuleTypeEnum.RENEWAL_SERVICE.getValue(), HrTalentStaffDTO.class);

        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.RENEWAL_SERVICE.getValue(), BusinessTypeEnum.EXPORT.getKey(),
            JSON.toJSONString(ids), ids.size(), fileUrl);
        return fileUrl;
    }

    /**
     * 续签服务详情
     *
     * @param id 员工ID
     * @return
     */
    @Override
    public HrTalentStaffDTO getRenewalInfo(String id) {
        HrTalentStaffDTO hrTalentStaffDTO = this.hrTalentStaffRepository.getStaffBasicInfo(id);
        HrProtocolDTO hrProtocolDTO = hrClientRepository.getprotocoendtime(hrTalentStaffDTO.getClientId());
        if (hrProtocolDTO != null) {
            hrTalentStaffDTO.setAgreementNumber(hrProtocolDTO.getAgreementNumber());
            hrTalentStaffDTO.setAgreementTitle(hrProtocolDTO.getAgreementTitle());
            hrTalentStaffDTO.setAgreementType(hrProtocolDTO.getAgreementType());
            Map<Integer, String> agreementType = codeTableService.findCodeTableByInnerName("agreementType");
            hrTalentStaffDTO.setAgreementTypeLabel(agreementType.get(hrProtocolDTO.getAgreementType()));
        }
        HrContractDTO hrContractDTO = hrContractRepository.getNewRenewalContract(id);
        if (hrContractDTO != null) {
            hrTalentStaffDTO.setContractStartDateRenewal(hrContractDTO.getContractStartDate());
            hrTalentStaffDTO.setContractEndDateRenewal(hrContractDTO.getContractEndDate());
        }
        if (hrTalentStaffDTO.getRenewalServiceId() != null) {
            List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(hrTalentStaffDTO.getRenewalServiceId(), null);
            if (CollectionUtils.isNotEmpty(applyOpLogsList)) {
                List<HrApplyOpLogsDTO> hrApplyOpLogsDTOList = applyOpLogsList.stream().filter(lst -> lst.getServeType() != null && lst.getServeType().equals(ServiceCenterEnum.RENEWAL_SERVICE.getKey())).collect(Collectors.toList());
                hrTalentStaffDTO.setApplyOpLogsList(hrApplyOpLogsDTOList);
            }
        }
        return hrTalentStaffDTO;
    }

    /**
     * 导出续签附件信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    @Override
    public List<ContractAppendixDownloadDTO> exportRenewal(HrTalentStaffDTO hrTalentStaffDTO) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrTalentStaffDTO> list = this.hrTalentStaffRepository.findListRenewal(hrTalentStaffDTO, clientIds);
        if (CollectionUtils.isEmpty(list)) {
            throw new CommonException("未查询到相关信息！");
        }
        List<String> contractIds = new ArrayList<>();
        for (HrTalentStaffDTO talentStaffDTO : list) {
            HrContractDTO hrContractDTO = hrContractRepository.getNewRenewalContract(talentStaffDTO.getId());
            if (hrContractDTO != null) {
                contractIds.add(hrContractDTO.getId());
            }
        }
        if (contractIds.isEmpty()) {
            return new ArrayList<>();
        }
        return hrContractService.downloadContractBatch(new HrContractDTO().setIds(contractIds));
    }

    /**
     * 每天0点检测员工续签状态
     */
    @Override
    public void updateStaffRenewalContract() {
        HrRemindConf hrRemindConf = hrRemindConfRepository.selectByRemindKey("staff_contract");
        HrTalentStaffDTO hrTalentStaffDTO = new HrTalentStaffDTO();
        hrTalentStaffDTO.setRenewalProcessList(Arrays.asList(StaffEnum.RenewalProcessEnum.RENEWAL_SUCCEEDED.getKey()));
        List<HrTalentStaffDTO> hrTalentStaffDTOList = hrTalentStaffRepository.findNotPageHrTalentStaff(hrTalentStaffDTO, null);
        if (CollectionUtils.isNotEmpty(hrTalentStaffDTOList)) {
            List<String> staffIds = new ArrayList<>();
            List<String> staffList = new ArrayList<>();
            hrTalentStaffDTOList.forEach(dto -> {
                HrContract hrContract = hrContractRepository.selectNewestRecord(dto.getId(), null);
                if (hrContract != null) {
                    long l = LocalDate.now().toEpochDay();
                    long day = hrContract.getContractEndDate().toEpochDay() - l;
                    if (day <= (hrRemindConf == null ? 60 : hrRemindConf.getRuleDay()) || day <= 0) {
                        staffIds.add(dto.getId());
                    } else {
                        staffList.add(dto.getId());
                    }
                }

            });
            if (CollectionUtils.isNotEmpty(staffIds)) {
                // 重置成功续签流程 续签成功-> 可续签，  is_renewal_contract(字段只用于续签成功后) -> 设为默认值 0不可续签
                hrTalentStaffRepository.resetSuccessRenewalProcess(staffIds);
            }
            if (CollectionUtils.isNotEmpty(staffList)) {
                hrTalentStaffRepository.updateRenewalContract(0, staffList);
            }
        }
    }

    /**
     * 获取当前员工客户发放银行
     *
     * @return
     */
    @Override
    public HrClientDTO findClientPlatformAccount() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        List<HrClientDTO> hrClientDTOList = hrClientRepository.findInfo(jwtMiniDTO.getClientId());
        if (CollectionUtils.isNotEmpty(hrClientDTOList)) {
            return hrClientDTOList.get(0);
        }
        return null;
    }

    /**
     * 批量处理之前合同
     *
     * @param serviceType 1入职合同 2续签合同
     * @param state       1通过 2作废
     * @param idNoList
     * @return
     */
    @Override
    public void batchHandleContract(Integer serviceType, Integer state, List<String> idNoList) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();

        List<HrTalentStaff> hrTalentStaffList = hrTalentStaffRepository.selectList(new QueryWrapper<HrTalentStaff>().in("certificate_num", idNoList));
        if (serviceType == 1) {
            for (HrTalentStaff talentStaff : hrTalentStaffList) {
                HrApplyEntryStaff hrApplyEntryStaff = this.hrApplyEntryStaffRepository.selectById(talentStaff.getApplyStaffId());
                HrContract hrContract = hrContractRepository.getNotActiveContract(talentStaff.getId(), talentStaff.getClientId());
                String message;
                JSONObject jsonObject = new JSONObject();
                if (state.equals(ContractEnum.ExamineState.FAIL.getKey())) {
                    List<HrContractAppendixDTO> hrContractAppendixDTOList = this.hrContractAppendixRepository.getStaffContractListTemplate(new HrContractAppendixDTO().setContractId(hrContract.getId()));
                    message = jwtUserDTO.getRealName() + " 作废了 " + hrContract.getStaffName() + " 的劳动合同。备注：无";
                    jsonObject.put("entryStatus", ApprovalEntryStatusEnum.EntryStatus.CONFIRMATION_CONTRACT.getKey());
                    jsonObject.put("message", jwtUserDTO.getRoleName() + " 作废了您的劳动合同");
                    this.hrContractService.toVoidContract(new HrContractDTO().setCheckMsg("审核拒绝"), hrContract, hrApplyEntryStaff, hrContractAppendixDTOList);
                } else {
                    message = jwtUserDTO.getRealName() + " 通过了 " + talentStaff.getName() + " 的劳动合同。备注：无";
                    jsonObject.put("entryStatus", ApprovalEntryStatusEnum.EntryStatus.ENROLLMENT_COMPLETED.getKey());
                    jsonObject.put("message", "您的入职手续已全部办理完成");
                    // 标记合同生效
                    hrContract.setState(ContractEnum.ContractState.IN_EFFECT.getKey());
                    this.hrContractService.passContract(hrContract, hrApplyEntryStaff, talentStaff);
                    hrContractService.updateById(hrContract);
                }
                this.hrApplyEntryStaffRepository.updateById(hrApplyEntryStaff);
                // 添加入职流程日志
                hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrApplyEntryStaff.getApplyId(), hrApplyEntryStaff.getId(), jwtUserDTO.getId(), message + "####" + jsonObject.toString(), null, 0);
                hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(), hrApplyEntryStaff.getId(), hrApplyEntryStaff.getStaffId(), String.valueOf(jsonObject.get("message")), false, ServiceCenterEnum.ENTRY_APPLICATIONS.getValue());
                hrNotificationUserService.saveRemindContent(hrApplyEntryStaff.getClientId(), ServiceCenterEnum.ENTRY_APPLICATIONS.getKey(), ServiceCenterEnum.ReminderContentEnum.CONTRACT_CONFIRMATION.getKey(), message, jwtUserDTO.getId());
            }
        } else {
            if (state.equals(ContractEnum.ExamineState.FAIL.getKey())) {
                for (HrTalentStaff hrTalentStaff : hrTalentStaffList) {
                    HrContract hrContract = hrContractRepository.getNotActiveContract(hrTalentStaff.getId(), hrTalentStaff.getClientId());
                    List<HrContractAppendixDTO> hrContractAppendixDTOList = this.hrContractAppendixRepository.getStaffContractListTemplate(new HrContractAppendixDTO().setContractId(hrContract.getId()));
                    this.toVoidRenewalContract(new HrContractDTO().setCheckMsg("审核拒绝"), hrContract, jwtUserDTO, hrTalentStaff, hrContractAppendixDTOList);
                }
            } else {
                for (HrTalentStaff hrTalentStaff : hrTalentStaffList) {
                    // 获取该员工未生效的合同
                    HrContract hrContract = this.hrContractRepository.getNotActiveContract(hrTalentStaff.getId(), hrTalentStaff.getClientId());
                    if (hrContract == null) {
                        throw new CommonException("未查询到该员工未生效合同信息！");
                    }
                    List<HrContractAppendixDTO> confirmationTelegramList = this.hrContractAppendixRepository.getConfirmationTelegramList(hrContract.getClientId(), hrContract.getStaffId());
                    confirmationTelegramList.forEach(lst -> {
                        lst.setState(ContractEnum.ExamineState.PASS.getKey());
                        hrContractAppendixRepository.updateById(hrContractAppendixMapper.toEntity(lst));
                    });
                    HrContractDTO hrContractDTO = new HrContractDTO();
                    hrContractDTO.setId(hrContract.getId());
                    this.contractRenewalApproved(hrContract, hrContractDTO, jwtUserDTO, hrTalentStaff);
                    this.hrContractRepository.updateById(this.hrContractMapper.toEntity(hrContractDTO));
                }
            }
        }
    }

    /**
     * 修改员工微信openid
     *
     * @param staffIds 员工Id
     * @param openId
     */
    @Override
    public void updateOpenIdByStaffId(List<String> staffIds, String openId) {
        hrTalentStaffRepository.updateOpenIdByStaffId(staffIds, openId);
    }

    @Override
    public void manualUpdateStaffInductionProcess(List<String> staffIds, Integer process) {
        if (staffIds.isEmpty()) {
            throw new CommonException("请至少选择一位员工进行操作!");
        }
        if (process == null) {
            throw new CommonException("员工操作类型不能为空!");
        }
        JWTUserDTO user = SecurityUtils.getCurrentUser().get();
        /*if (!UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey().equals(user.getCurrentRoleKey())) {
            throw new CommonException("此功能仅限[" + UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getName() + "]角色使用!");
        }*/
        StringBuilder sb = new StringBuilder();
        sb.append("手动修改员工入职状态为:[");
        if (process == 1) {
            sb.append("入职待发合同");
        } else if (process == 2) {
            sb.append("续签待发合同");
        } else {
            sb.append("操作状态:");
            sb.append(process);
        }
        sb.append("] 操作员工id列表:[");

        staffIds.forEach(ls -> {
            sb.append(ls);
            sb.append("; ");
            // 设置员工状态为入职待发合同
            if (process == 1) {
                this.hrTalentStaffRepository.manualUpdateStaffInductionProcess(new HrTalentStaff()
                    .setId(ls)
                    .setStaffStatus(StaffEnum.StaffStatusEnum.ENROLLING.getKey())
                    .setIzStartEnd(StaffEnum.IzStartEndEnum.CONFIRMATION_MESSAGE.getKey())
                );

                this.hrApplyEntryStaffRepository.manualUpdateStaffEntryState(new HrApplyEntryStaff()
                    .setStaffId(ls)
                    .setApplyStep(ApprovalEntryStatusEnum.ApplyStep.INITIATE_CONTRACT.getKey())
                    .setEntryStatus(ApprovalEntryStatusEnum.EntryStatus.SEND_CONTRACT.getKey())
                );

                this.hrContractRepository.manualUpdateStaffContract(new HrContract()
                    .setStaffId(ls)
                    .setState(ContractEnum.ContractState.NOT_ACTIVE.getKey())
                    .setContractType(0)
                );
            }
            // 设置员工为续签待发合同
            else if (process == 2) {
                this.hrTalentStaffRepository.manualUpdateStaffInductionProcess(new HrTalentStaff()
                    .setId(ls)
                );
                this.hrContractRepository.manualUpdateStaffContract(new HrContract()
                    .setStaffId(ls)
                    .setState(ContractEnum.ContractState.NOT_ACTIVE.getKey())
                    .setContractType(1)
                );
            }
        });
        sb.append("]");

        this.sysOperLogService.addSysOperLog(new SysOperLog()
            .setTitle(ModuleTypeEnum.STAFF.getValue())
            .setOperatorType(OperatorTypeEnum.MANAGE.getKey())
            .setBusinessType(BusinessTypeEnum.UPDATE.getKey())
            .setOperDetail(sb.toString())
            .setOperAccount(user.getUserName())
            .setOperName(user.getRealName())
            .setOperTime(LocalDateTime.now())
        );
    }

    /**
     * 小程序识别跳过
     * @param distinguishDTO
     */
    @Override
    public void distinguishCloud(HrDistinguishDTO distinguishDTO) {
        String staffId = distinguishDTO.getStaffId();
        Integer type = distinguishDTO.getType();
        if (StringUtils.isBlank(staffId) || type == null){
            throw new CommonException("参数缺失！");
        }
        distinguishDTO.setId(RandomUtil.generateId());
        List<HrDistinguishDTO> distinguishDTOS = hrTalentStaffRepository.selectDistinguish(distinguishDTO.getCertificateNum(), type);
        if (distinguishDTOS.isEmpty()){
            hrTalentStaffRepository.insertDistinguish(distinguishDTO);
        }
    }

    /**
     * 小程序员工跳过认证标识
     * @return false 正常认证 true跳过认证
     */
    @Override
    public Boolean findDistinguishVerify() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        List<HrDistinguishDTO> distinguishDTOS = hrTalentStaffRepository.selectDistinguish(jwtMiniDTO.getCertificateNum(), CloudEnum.InterfaceType.FACE_NUCLEUS.getKey());
        return distinguishDTOS.isEmpty() ? false : true;
    }

    /**
     * 每天0点处理员工在职信息
     */
    @Override
    public void handleStaffWorkExperience() {
        HrTalentStaffDTO staffDTO = new HrTalentStaffDTO();
        staffDTO.setIzDefault(false);
        staffDTO.setExperience(true);
        staffDTO.setNotStaffStatusList(Collections.singletonList(StaffEnum.StaffStatusEnum.SEPARATION.getKey()));
        List<HrTalentStaffDTO> hrTalentStaffDTOList = hrTalentStaffRepository.findPage(staffDTO);
        if (hrTalentStaffDTOList != null && !hrTalentStaffDTOList.isEmpty()){
            List<CodeTableDTO> monthlyPay = codeTableService.getCodeTableListByInnerName("monthlyPay");
            for (HrTalentStaffDTO talentStaff : hrTalentStaffDTOList) {
                //查询是否存在被删除的在职信息
                HrStaffWorkExperience experience = hrStaffWorkExperienceRepository.findDelExperience(talentStaff.getId(), talentStaff.getClientId());
                //存在恢复在职信息，不存在创建
                if (experience != null){
                    hrStaffWorkExperienceRepository.updateDelExperience(experience.getId());
                } else {
                    if (talentStaff.getContractStartDate() == null){
                        continue;
                    }
                    //创建在职公司信息
                    HrStaffWorkExperience workExperience = new HrStaffWorkExperience();
                    if (talentStaff.getBasicWage() != null){
                        Integer salarySection = CalculateUtils.figureBasicWage(talentStaff.getBasicWage(),monthlyPay);
                        if (salarySection != null){
                            workExperience.setSalarySection(salarySection);
                        }
                    }
                    workExperience.setIzDefault(true)
                        .setClientId(talentStaff.getClientId())
                        .setStaffId(talentStaff.getId())
                        .setPersonnelType(talentStaff.getPersonnelType())
                        .setContractStartDate(talentStaff.getContractStartDate())
                        .setContractEndDate(talentStaff.getContractEndDate())
                        .setBoardDate(talentStaff.getContractStartDate())
                        .setIzInsured(talentStaff.getIzInsured());
                    hrStaffWorkExperienceService.save(workExperience);
                }
            }
        }
    }
}
