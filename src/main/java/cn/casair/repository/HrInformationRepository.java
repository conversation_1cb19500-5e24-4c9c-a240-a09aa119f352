package cn.casair.repository;

import cn.casair.domain.HrInformation;
import cn.casair.dto.HrInformationDTO;
import cn.casair.dto.excel.HrInformationExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 资讯模块数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-12
 */
@Repository
public interface HrInformationRepository extends BaseMapper<HrInformation> {

    /**
     * 分页
     * @param page
     * @param hrInformationDTO
     * @return
     */
    IPage selectHrInformation(Page<HrInformation> page,@Param("param") HrInformationDTO hrInformationDTO);

    /**
     * 获取导出数据列表
     * @param hrInformationDTO
     * @return
     */
    List<HrInformationExport> selectExportList(@Param("param") HrInformationDTO hrInformationDTO);

}
