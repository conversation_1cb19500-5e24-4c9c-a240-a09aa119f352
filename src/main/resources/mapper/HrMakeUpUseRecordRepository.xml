<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrMakeUpUseRecordRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, make_up_id, bill_id, bill_detail_id, staff_id,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrMakeUpUseRecord">
        <id column="id" property="id"/>
        <result column="make_up_id" property="makeUpId"/>
        <result column="bill_id" property="billId"/>
        <result column="bill_detail_id" property="billDetailId"/>
        <result column="staff_id" property="staffId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByMakeUpId" resultType="cn.casair.domain.HrMakeUpUseRecord">
        SELECT
            *
        FROM
            hr_make_up_use_record
        WHERE
            make_up_id = #{makeUpId}
            AND is_delete = 0
        ORDER BY
            created_date DESC
        LIMIT 1
    </select>

</mapper>
