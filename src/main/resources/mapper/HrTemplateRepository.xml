<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrTemplateRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, template_name, content_id, frequency, built_in, preview, create_date,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>


    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrTemplate">
                    <id column="id" property="id"/>
                    <result column="template_name" property="templateName"/>
                    <result column="content_id" property="contentId"/>
                    <result column="frequency" property="frequency"/>
                    <result column="built_in" property="builtIn"/>
                    <result column="preview" property="preview"/>
                    <result column="create_date" property="createDate"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>
    <sql id="Find_SQL">
        SELECT * FROM `hr_template` where is_delete=0
        <if test="param1.templateName!=null and param1.templateName!=''">
            AND template_name like concat('%',#{param1.templateName},'%')
        </if>
        <if test="param1.templateStartDateStart!=null ">
            AND create_date <![CDATA[ >= ]]> #{param1.templateStartDateStart}
        </if>
        <if test="param1.templateStartDateEnd!=null ">
            AND create_date <![CDATA[ < ]]> date_add(#{param1.templateStartDateEnd}, interval 1 day )
        </if>
        <if test="param1.frequencyList!=null and param1.frequencyList.size() > 0">
            AND frequency IN
            <foreach collection="param1.frequencyList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.ids!=null and param1.ids.size() > 0">
            AND id IN
            <foreach collection="param1.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by  ${param1.field} ${param1.order}
        </if>
        <if test="param1.field == null  and param1.order == null ">
            ORDER BY built_in desc, created_date DESC
        </if>
    </sql>

    <select id="selectPages" resultType="cn.casair.dto.HrTemplateDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="selectFrequency" resultType="java.lang.String">
        SELECT frequency FROM `hr_template`  where is_delete=0 GROUP BY frequency
    </select>

    <select id="findList" resultType="cn.casair.dto.HrTemplateDTO">
        <include refid="Find_SQL"/>
    </select>
</mapper>
