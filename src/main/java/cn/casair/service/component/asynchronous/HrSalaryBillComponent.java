package cn.casair.service.component.asynchronous;

import cn.casair.common.enums.BillEnum;
import cn.casair.common.enums.SpecialBillClient;
import cn.casair.common.enums.StaffEnum;
import cn.casair.common.utils.RandomUtil;
import cn.casair.common.utils.StringUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.billl.BillExcelDataDTO;
import cn.casair.mapper.HrBillDetailItemsMapper;
import cn.casair.mapper.HrBillDetailMapper;
import cn.casair.repository.HrBillDetailItemsRepository;
import cn.casair.repository.HrBillDetailRepository;
import cn.casair.service.HrBillService;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * 招聘简章
 *
 * @version 1.0.0
 * @date 2022/12/16 9:57
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HrSalaryBillComponent {

    private final HrBillService hrBillService;
    private final HrBillDetailMapper hrBillDetailMapper;
    private final HrBillDetailRepository hrBillDetailRepository;
    private final HrBillDetailItemsMapper hrBillDetailItemsMapper;
    private final HrBillDetailItemsRepository hrBillDetailItemsRepository;


    /**
     * 异步处理薪酬账单数据
     * @param hrBillDTO 账单
     * @param detailDTO 员工明细
     * @param hrBillDTOList 全部账单
     * @param securityDTOList 社保类型配置模板
     * @param excelDataMap 薪酬原单数据
     * @param specialBillClient 特殊客户
     * @param lastPayYear 上一年
     * @param lastPayMonthly 上一月
     * @param initDynamicFee
     * @param staffIds 提前添加的未入账员工ID
     * @param specialBillExcelDataMap
     * @param hrBillDetailItems
     * @param hrBillDetails
     * @param jwtUserDTO
     * @param hrQuickDeductions
     * @param billDetailByBill
     * @param welfareCompensations
     * @param hrBillCompareResultDTOList
     */
    public void detailDataHandle(HrBillDTO hrBillDTO, HrBillDetailDTO detailDTO, List<HrBillDTO> hrBillDTOList, List<HrSocialSecurityDTO> securityDTOList,
                                           Map<String, BillExcelDataDTO> excelDataMap, SpecialBillClient specialBillClient, int lastPayYear, int lastPayMonthly,
                                           List<HrBillDetailItemsDTO> initDynamicFee, List<String> staffIds, Map<String, BillExcelDataDTO> specialBillExcelDataMap,
                                           List<HrBillDetailItems> hrBillDetailItems, List<HrBillDetail> hrBillDetails, List<HrBillDetailItemsDTO> insertHrBillDetailItems, JWTUserDTO jwtUserDTO,
                                           Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions,
                                           List<HrBillDetailDTO> billDetailByBill, List<HrBillDetailDTO> hrBillDetailDTOS, List<HrWelfareCompensation> welfareCompensations, List<HrBillDetailDTO> lastBillDetailDTOList, List<HrBillCompareResultDTO> hrBillCompareResultDTOList) {
        HrBillDTO dto = hrBillDTOList.stream().filter(ls -> ls.getClientId().equals(detailDTO.getClientId())).findFirst().orElse(hrBillDTO);
        dto.setMonthlyServiceFee(BigDecimal.ZERO);
        dto.setHalfYearlyServiceFee(BigDecimal.ZERO);
        HrSocialSecurityDTO hrSocialSecurityDTO = securityDTOList == null ? null : securityDTOList.stream().filter(ls -> ls.getId().equals(detailDTO.getSocialSecurityTypeId())).findFirst().orElse(null);
        // 匹配对应的账单
        String idCard = detailDTO.getCertificateNum();
        // excel中获取该员工
        BillExcelDataDTO excelDataDTO = excelDataMap.get(idCard);

        // TODO 记录一个需求,代码还没添加 [薪酬单, 客户想将excel中的所有人员,直接导入到页面中 isUsed = 1,而不是手动添加(系统中不存在, 非参保等)], [非参保 不校验还合理, 离职等状态,系统中不存在呢]
        // 填充社保 公积金 服务费等数据
        this.hrBillService.fillHrBillDetail(detailDTO, dto, staffIds,hrSocialSecurityDTO,hrBillCompareResultDTOList, billDetailByBill, hrBillDetailDTOS, welfareCompensations,lastPayYear,lastPayMonthly);
        // 判断 excel 是否解析到该员工
        boolean excelExistStaff = excelDataDTO != null;
        log.info("员工姓名:{}", detailDTO.getName());
        if (excelExistStaff) {
            if (StringUtils.isNotBlank(excelDataDTO.getProjectName())) {
                detailDTO.setProjectName(excelDataDTO.getProjectName());
            }
            // 设置排序值
            detailDTO.setSortValue(excelDataDTO.getSortValue());
            List<HrBillDetailItemsDTO> dynamicFeeItems = excelDataDTO.getDynamicFeeItems();
            // 有数据时,填充应发工资,实发工资,费用合计等
            if (hrBillDTO.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())) {
                // 薪酬账单
                if (specialBillClient != null && (!specialBillClient.equals(SpecialBillClient.HAIER) && !specialBillClient.equals(SpecialBillClient.SOCIAL_GOVERNANCE))) {
                    boolean notCalSign = this.hrBillService.handleFillSalaryParam(specialBillClient, detailDTO, dynamicFeeItems, specialBillExcelDataMap, hrBillDTO, lastPayYear, lastPayMonthly,
                        personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions, billDetailByBill, lastBillDetailDTOList, initDynamicFee);
                    if (notCalSign) {
                        if (!specialBillClient.equals(SpecialBillClient.EAST_DISTRICT_PUBLIC_SECURITY)) {
                            if (detailDTO.getStaffStatus() != null && !detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.RETIRE.getKey())) {
                                this.hrBillService.fillSpecialBillDetailItems(initDynamicFee, detailDTO.getSalary());
                            }
                        } else {
                            //将东区公安离职的员工费用增项重新赋值
                            this.hrBillService.fillEastSecurityBillDetailItems(detailDTO, specialBillExcelDataMap, initDynamicFee);
                        }
                        excelDataDTO.setDynamicFeeItems(initDynamicFee);
                    }
                } else {
                    this.hrBillService.fillSalaryParam(detailDTO, dynamicFeeItems, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
                }
            } else {
                //其他账单
                List<HrBillDetailItems> hrBillDetailItems1 = this.hrBillService.fillOtherBillParam(detailDTO, dynamicFeeItems, false);
                hrBillDetailItems.addAll(hrBillDetailItems1);
            }
            // map中移除该元素
            excelDataMap.remove(idCard);
        }
        // excel中未获取到该员工
        else {
            // 账单特殊处理客户
            if (specialBillClient != null) {
                switch (specialBillClient) {
                    case EAST_DISTRICT_PUBLIC_SECURITY:
                        // 额外入账人员为上月离职 则处理实际应发工资
                        if (CollectionUtils.isNotEmpty(staffIds) && staffIds.contains(detailDTO.getStaffId())
                            && (detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())
                            || detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.STAY_CONFIRM_RESIGNATION_LEAVING.getKey()))
                            && detailDTO.getResignationDate().getYear() == lastPayYear && detailDTO.getResignationDate().getMonthValue() == lastPayMonthly) {
                            BillExcelDataDTO billExcelDataDTO = specialBillExcelDataMap.get(detailDTO.getCertificateNum());
                            if (billExcelDataDTO != null){
                                this.hrBillService.fillEastSecuritySalaryParamForResigned(detailDTO, billExcelDataDTO.getDynamicFeeItems());
                                // 将计算金额填充到费用项
                                this.hrBillService.fillEastSecurityBillDetailItems(detailDTO, specialBillExcelDataMap, initDynamicFee);
                            }
                            // map中移除该元素
                            excelDataMap.remove(idCard);
                        }
                        //处理退休人员
                        else if (CollectionUtils.isNotEmpty(staffIds)
                            && staffIds.contains(detailDTO.getStaffId())
                            && detailDTO.getStaffStatus().equals(StaffEnum.StaffStatusEnum.RETIRE.getKey())){
                            if (lastBillDetailDTOList != null && !lastBillDetailDTOList.isEmpty()){
                                this.hrBillService.fillSpecialSalaryParamForRetire(detailDTO, lastBillDetailDTOList, initDynamicFee, personalTaxStart, sumCurrentIncome, specialDeductionList, hrQuickDeductions);
                            }
                            // map中移除该元素
                            excelDataMap.remove(idCard);
                        }
                        else {
                            // 设置为不可用
                            if (detailDTO.getCheckOn() == 0) {
                                detailDTO.setIsUsed(false);
                                // 同时设置未入账原因
                                String reason = StringUtils.isEmpty(detailDTO.getReason()) ? "" : detailDTO.getReason() + "; ";
                                reason = reason + "excel中不存在该员工";
                                detailDTO.setReason(reason);
                            }
                        }
                        break;
                    default:
                        // 设置为不可用
                        if (detailDTO.getCheckOn() == 0) {
                            detailDTO.setIsUsed(false);
                            // 同时设置未入账原因
                            String reason = StringUtils.isEmpty(detailDTO.getReason()) ? "" : detailDTO.getReason() + "; ";
                            reason = reason + "excel中不存在该员工";
                            detailDTO.setReason(reason);
                        }
                        break;
                }
            } else {
                // 设置为不可用
                if (detailDTO.getCheckOn() == 0) {
                    detailDTO.setIsUsed(false);
                    // 同时设置未入账原因
                    String reason = StringUtils.isEmpty(detailDTO.getReason()) ? "" : detailDTO.getReason() + "; ";
                    reason = reason + "excel中不存在该员工";
                    detailDTO.setReason(reason);
                }
            }
        }
        // 保存工资账单明细
        HrBillDetail hrBillDetail = this.hrBillDetailMapper.toEntity(detailDTO);
        hrBillDetail.setId(RandomUtil.generateId()).setType(hrBillDTO.getBillType());
        hrBillDetail.setCreatedBy(jwtUserDTO.getUserName());
        hrBillDetail.setCreatedDate(LocalDateTime.now());
//            this.hrBillDetailRepository.insert(hrBillDetail);
        hrBillDetails.add(hrBillDetail);
        // 保存账单明细与补差 到 redis
        this.hrBillService.addMakeUpUserRecordToRedis(hrBillDetail, detailDTO.getWelfareCompensationList());
        // 保存工资动态项明细
        this.saveDynamicFeeDetail(excelDataDTO, initDynamicFee, hrBillDetail.getId(), insertHrBillDetailItems, jwtUserDTO);
         //初始化动态项
        initDynamicFee.forEach(item->{item.setAmount(BigDecimal.ZERO);});
    }

    /**
     *  保存动态费用项明细
     * @param excelDataDTO
     * @param initDynamicFee
     * @param billDetailId
     * @param insertHrBillDetailItems
     * @param jwtUserDTO
     */
    private void saveDynamicFeeDetail(BillExcelDataDTO excelDataDTO, List<HrBillDetailItemsDTO> initDynamicFee, String billDetailId, List<HrBillDetailItemsDTO> insertHrBillDetailItems, JWTUserDTO jwtUserDTO) {
        List<HrBillDetailItemsDTO> dynamicFeeItems;
        if (excelDataDTO != null && excelDataDTO.getDynamicFeeItems() != null) {
            dynamicFeeItems = excelDataDTO.getDynamicFeeItems();
        } else {
            // 添加空白的费用项
            dynamicFeeItems = new ArrayList<>(initDynamicFee);
        }
        if(dynamicFeeItems == null || dynamicFeeItems.isEmpty()) {
            return;
        }
        for(HrBillDetailItemsDTO detailItemsDTO: dynamicFeeItems) {
            HrBillDetailItemsDTO itemsDTO = new HrBillDetailItemsDTO();
            BeanUtils.copyProperties(detailItemsDTO,itemsDTO);
            itemsDTO.setId(RandomUtil.generateId()).setBillDetailId(billDetailId);
            itemsDTO.setCreatedBy(jwtUserDTO.getUserName());
            itemsDTO.setCreatedDate(LocalDateTime.now());
            insertHrBillDetailItems.add(itemsDTO);
        }
    }
}
