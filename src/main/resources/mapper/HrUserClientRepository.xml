<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrUserClientRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , use_id, client_id,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrUserClient">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findHrStaff" resultType="cn.casair.dto.excel.HrClientExport">
    </select>

    <select id="getclientunitNumber" resultType="java.lang.String">
        SELECT parent_id
        FROM hr_client
        WHERE id = #{id}
          AND is_delete = 0
          AND `status` = 0
    </select>

    <select id="getIdNumber" resultType="cn.casair.dto.HrClientDTO">
        select unit_number as unitNumber, client_name as clientName
        from hr_client
        where id = #{parid}
          AND is_delete = 0
          AND `status` = 0
    </select>
</mapper>
