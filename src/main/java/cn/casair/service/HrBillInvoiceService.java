package cn.casair.service;

import cn.casair.domain.*;
import cn.casair.dto.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 开票申请服务类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface HrBillInvoiceService extends IService<HrBillInvoice> {


    /**
     * 创建开票申请
     * @param hrBillInvoiceDTO
     * @return
     */
    HrBillInvoiceDTO createHrBillInvoice(HrBillInvoiceDTO hrBillInvoiceDTO);

    /**
     * 新增开票申请
     * @param hrBillInvoiceDTO
     * @return
     */
    HrBillInvoiceDTO insertHrBillInvoice(HrBillInvoiceDTO hrBillInvoiceDTO);

    /**
     * 修改开票申请
     * @param hrBillInvoiceDTO
     * @return
     */
    Optional<HrBillInvoiceDTO> updateHrBillInvoice(HrBillInvoiceDTO hrBillInvoiceDTO);

    /**
     * 查询开票申请详情
     * @param id
     * @return
     */
    HrBillInvoiceDTO getHrBillInvoice(String id);

    /**
     * 删除开票申请
     * @param id
     */
    void deleteHrBillInvoice(String id);

    /**
     * 批量删除开票申请
     * @param ids
     */
    void deleteHrBillInvoice(List<String> ids);

    /**
     * 分页查询开票申请
     * @param hrBillInvoiceDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillInvoiceDTO hrBillInvoiceDTO,Long pageNumber,Long pageSize);

    /**
     * 审批
     * @param hrBillInvoiceApproveDTO
     */
    void approve(HrBillInvoiceApproveDTO hrBillInvoiceApproveDTO);

    /**
     * 开票
     * @param billInvoiceKpDTO
     */
    String kp(HrBillInvoiceKpDTO billInvoiceKpDTO);

    /**
     * 会计确认
     * @param id
     */
    void kjConfirm(String id);

    /**
     *  获取可通知的角色列表
     * @return
     */
    List<RoleDTO> getNoticeRoles();

    /**
     * 创建到账记录以及报销申请
     * @param roleTemp 结算单信息
     * @param hrBillTotal 结算单汇总
     * @param hrClient 客户信息
     * @param hrBills 账单
     * @param integer 0结算单审核 1开票单审核
     * @param invoiceRecordList
     * @param rootParentClient
     */
    void saveArrivalAndReimbursement(HrFeeReview roleTemp, HrBillTotal hrBillTotal, HrClient hrClient, List<HrBill> hrBills, Integer integer, List<HrBillInvoiceRecord> invoiceRecordList, HrClient rootParentClient);

    /**
     * 下载开票申请单
     * @param ids
     * @return
     */
    String downloadInvoiceApprove(List<String> ids);

    /**
     * 创建开票记录
     * @param hrBillInvoiceDTO
     * @return
     */
    HrBillInvoiceDTO saveHrBillInvoice(HrBillInvoiceDTO hrBillInvoiceDTO);

    /**
     * 生成外包客户开票信息
     * @param clientId 外包客户ID
     * @return
     */
    HrBillInvoiceDTO generateHrBillInvoice(String clientId);

    /**
     * 处理开票结算单中间表
     */
    void invoiceReview();

    /**
     * 赋值账单开票明细
     * @param hrClient 客户信息
     * @param totalAmount 金额
     * @param content 发票内容
     * @param value 发票内容名称
     * @param payYear 费用年
     * @param payMonthly 费用月
     * @return 开票明细
     */
    HrBillInvoiceRecordDTO assignmentBillInvoiceRecord(HrClient hrClient, BigDecimal totalAmount, String content, String value, Integer payYear, Integer payMonthly);

    /**
     * 生成凭证
     * @param hrBillInvoiceDTO
     */
    @Deprecated
    void createVoucher(HrBillInvoiceDTO hrBillInvoiceDTO);

    /**
     * 生成凭证
     *
     * @param hrBillInvoiceDTO
     */
    void createVoucherNcc(HrBillInvoiceDTO hrBillInvoiceDTO);

    /**
     * 删除凭证
     * @param hrBillInvoiceId
     */
    void deleteVoucher(String hrBillInvoiceId);

    /**
     * 处理结算单PDF
     * @param ids
     */
    Map<String, Object> handleFeeReviewPDF(List<String> ids);

    /**
     * excel转pdf
     *
     * @param hrFeeReview 结算单信息
     * @param hrAppendixDTO excel附件
     * @param hrBill 账单信息
     * @return PDF路径
     */
    String excelToPdf(HrFeeReview hrFeeReview, HrAppendixDTO hrAppendixDTO, HrBill hrBill) throws Exception;

}
