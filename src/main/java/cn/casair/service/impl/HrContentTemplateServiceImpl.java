package cn.casair.service.impl;

import cn.casair.common.errors.CommonException;
import cn.casair.domain.CodeTable;
import cn.casair.domain.HrCertificate;
import cn.casair.domain.HrContentTemplate;
import cn.casair.dto.CodeTableDTO;
import cn.casair.dto.HrCertificateDTO;
import cn.casair.dto.HrContentDTO;
import cn.casair.dto.HrContentTemplateDTO;
import cn.casair.mapper.CodeTableMapper;
import cn.casair.mapper.HrCertificateMapper;
import cn.casair.mapper.HrContentTemplateMapper;
import cn.casair.repository.CodeTableRepository;
import cn.casair.repository.HrCertificateRepository;
import cn.casair.repository.HrContentTemplateRepository;
import cn.casair.service.HrContentTemplateService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 简历模板服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrContentTemplateServiceImpl extends ServiceImpl<HrContentTemplateRepository, HrContentTemplate> implements HrContentTemplateService {


    private final HrContentTemplateRepository hrContentTemplateRepository;
    private final HrContentTemplateMapper hrContentTemplateMapper;
    private final CodeTableMapper CodeTableMapper;
    private final HrCertificateRepository hrCertificateRepository;
    private final CodeTableRepository codeTableRepository;
    private final HrCertificateMapper hrCertificateMapper;


    public HrContentTemplateServiceImpl(HrContentTemplateRepository hrContentTemplateRepository, HrContentTemplateMapper hrContentTemplateMapper, cn.casair.mapper.CodeTableMapper codeTableMapper, HrCertificateRepository hrCertificateRepository, CodeTableRepository codeTableRepository, HrCertificateMapper hrCertificateMapper) {
        this.hrContentTemplateRepository = hrContentTemplateRepository;
        this.hrContentTemplateMapper = hrContentTemplateMapper;
        CodeTableMapper = codeTableMapper;
        this.hrCertificateRepository = hrCertificateRepository;
        this.codeTableRepository = codeTableRepository;
        this.hrCertificateMapper = hrCertificateMapper;
    }

    /**
     * 创建简历模板
     *
     * @param hrContentTemplateDTO
     * @return
     */
    @Override
    public List<HrContentTemplateDTO> createHrContentTemplate(List<HrContentTemplateDTO> hrContentTemplateDTO) {
        log.info("Create new HrContentTemplate:{}", hrContentTemplateDTO);

        List<HrContentTemplate> hrContentTemplate = this.hrContentTemplateMapper.toEntity(hrContentTemplateDTO);
        for (int i = 0; i < hrContentTemplate.size(); i++) {
            hrContentTemplate.get(i).setOrders(i);
            this.hrContentTemplateRepository.insert(hrContentTemplate.get(i));
        }


        return this.hrContentTemplateMapper.toDto(hrContentTemplate);
    }

    /**
     * 修改简历模板
     *
     * @param hrContentTemplateDTO
     * @return
     */
    @Override
    public Optional<HrContentTemplateDTO> updateHrContentTemplate(HrContentTemplateDTO hrContentTemplateDTO) {
        return Optional.ofNullable(this.hrContentTemplateRepository.selectById(hrContentTemplateDTO.getId()))
            .map(roleTemp -> {
                HrContentTemplate hrContentTemplate = this.hrContentTemplateMapper.toEntity(hrContentTemplateDTO);
                this.hrContentTemplateRepository.updateById(hrContentTemplate);
                log.info("Update HrContentTemplate:{}", hrContentTemplateDTO);
                return hrContentTemplateDTO;
            });
    }

    /**
     * 查询简历模板详情
     *
     * @param
     * @return
     */
    @Override
    public List<HrContentTemplateDTO> getHrContentTemplate() {
        log.info("Get HrContentTemplate :{}");
        QueryWrapper<HrContentTemplate> qw = new QueryWrapper<>();
        qw.eq("is_delete", 0);
        qw.orderByAsc("orders");
        List<HrContentTemplate> hrContentTemplate = this.hrContentTemplateRepository.selectList(qw);
        List<HrContentTemplateDTO> hrContentTemplateDTOS = this.hrContentTemplateMapper.toDto(hrContentTemplate);
        for (HrContentTemplateDTO hrContentTemplateDTO : hrContentTemplateDTOS) {
            if (StringUtils.isNotBlank(hrContentTemplateDTO.getType())) {
                if (hrContentTemplateDTO.getType().equals("appendix")) {
                    HrCertificate hrCertificate = this.hrCertificateRepository.selectById(hrContentTemplateDTO.getName());
                    HrCertificateDTO hrCertificateDTO = this.hrCertificateMapper.toDto(hrCertificate);
                    hrContentTemplateDTO.setHrCertificateDTO(hrCertificateDTO);
                }
            }
        }
        return hrContentTemplateDTOS;
    }

    /**
     * 删除简历模板
     *
     * @param id
     */
    @Override
    public void deleteHrContentTemplate(String id) {
        Optional.ofNullable(this.hrContentTemplateRepository.selectById(id))
            .ifPresent(hrContentTemplate -> {
                this.hrContentTemplateRepository.deleteById(id);
                log.info("Delete HrContentTemplate:{}", hrContentTemplate);
            });
    }

    /**
     * 批量删除简历模板
     *
     * @param ids
     */
    @Override
    public void deleteHrContentTemplate(List<String> ids) {
        log.info("Delete HrContentTemplates:{}", ids);
        this.hrContentTemplateRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询简历模板
     *
     * @param hrContentTemplateDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrContentTemplateDTO hrContentTemplateDTO, Long pageNumber, Long pageSize) {
        Page<HrContentTemplate> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrContentTemplate> qw = new QueryWrapper<>();

        if (StringUtils.isNotBlank(hrContentTemplateDTO.getLabel())) {
            qw.like("label", hrContentTemplateDTO.getLabel());
        }
        if (CollectionUtils.isNotEmpty(hrContentTemplateDTO.getTypeList())) {
            qw.in("type", hrContentTemplateDTO.getTypeList());
        }
        if (CollectionUtils.isNotEmpty(hrContentTemplateDTO.getRequiredList())) {
            qw.in("required", hrContentTemplateDTO.getRequiredList());
        }

        //排序
        if (cn.casair.common.utils.StringUtils.isNotBlank(hrContentTemplateDTO.getOrder())) {
            if (hrContentTemplateDTO.getOrder().equals("DESC")) {
                qw.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrContentTemplateDTO.getField()), false, hrContentTemplateDTO.getField());
            } else {
                qw.orderBy(cn.casair.common.utils.StringUtils.isNotBlank(hrContentTemplateDTO.getField()), true, hrContentTemplateDTO.getField());
            }
        }

        //排序
        if (cn.casair.common.utils.StringUtils.isBlank(hrContentTemplateDTO.getOrder())) {
            qw.orderByDesc("created_date");
        }

        IPage iPage = this.hrContentTemplateRepository.selectPage(page, qw);
        List<HrContentTemplateDTO> hrContentTemplateDTOS = this.hrContentTemplateMapper.toDto(iPage.getRecords());
        for (HrContentTemplateDTO contentTemplateDTO : hrContentTemplateDTOS) {
            if (StringUtils.isNotBlank(contentTemplateDTO.getType())) {
                if (contentTemplateDTO.getType().equals("appendix")) {
                    HrCertificate hrCertificate = this.hrCertificateRepository.selectById(contentTemplateDTO.getName());
                    HrCertificateDTO hrCertificateDTO = this.hrCertificateMapper.toDto(hrCertificate);
                    contentTemplateDTO.setHrCertificateDTO(hrCertificateDTO);
                }
            }
        }
        iPage.setRecords(hrContentTemplateDTOS);
        return iPage;
    }

    /**
     * 创建简历模板字段
     */
    @Override
    public HrContentTemplateDTO createHrContentTemplateIndex(HrContentTemplateDTO hrContentTemplateDTO) {
        if (StringUtils.isNotBlank(hrContentTemplateDTO.getLabel())){
            QueryWrapper<HrContentTemplate>ew=new QueryWrapper<>();
            ew.eq("label",hrContentTemplateDTO.getLabel());
            List<HrContentTemplate>hrContentTemplateList=this.hrContentTemplateRepository.selectList(ew);
            if (StringUtils.isBlank(hrContentTemplateDTO.getId())){
                if (CollectionUtils.isNotEmpty(hrContentTemplateList)){
                    throw new CommonException("内容名字重复，请重新输入！");
                }
            }
        }
        HrContentTemplate hrContentTemplates = new HrContentTemplate();
        if (hrContentTemplateDTO != null) {
            //单项选择
            if (hrContentTemplateDTO.getType().equals("change")) {
                CodeTableDTO codeTableDTO = new CodeTableDTO();
                QueryWrapper<CodeTable> ce = new QueryWrapper<>();
                ce.eq("inner_name", "NM" + hrContentTemplateDTO.getName());
                CodeTable codeTableqs = this.codeTableRepository.selectOne(ce);
                //字典表里面有
                if (codeTableqs != null) {
                    if (CollectionUtils.isNotEmpty(hrContentTemplateDTO.getOptionsList())) {
                        for (HrContentDTO hrContentDTO : hrContentTemplateDTO.getOptionsList()) {
                            CodeTableDTO codeTableDTOs = new CodeTableDTO();
                            if (hrContentDTO.getId() != null) {//修改数据字典子项
                                codeTableDTOs.setId(hrContentDTO.getId());
                                codeTableDTOs.setParentId(codeTableqs.getId());
                                codeTableDTOs.setItemName(hrContentDTO.getItemName());
                                codeTableDTOs.setItemValue(hrContentDTO.getItemValue());
                                codeTableDTOs.setDisplayOrder(hrContentDTO.getDisplayOrder());
                                CodeTable codeTables = this.CodeTableMapper.toEntity(codeTableDTOs);
                                this.codeTableRepository.updateById(codeTables);
                            } else {//新增数据字典子项
                                codeTableDTOs.setParentId(codeTableqs.getId());
                                codeTableDTOs.setItemName(hrContentDTO.getItemName());
                                codeTableDTOs.setItemValue(hrContentDTO.getItemValue());
                                codeTableDTOs.setDisplayOrder(hrContentDTO.getDisplayOrder());
                                CodeTable codeTables = this.CodeTableMapper.toEntity(codeTableDTOs);
                                this.codeTableRepository.insert(codeTables);
                            }
                        }
                    }
                }
                //字典表里面没有
                else {
                    codeTableDTO.setItemName("NR" + hrContentTemplateDTO.getLabel());
                    codeTableDTO.setInnerName("NR" + hrContentTemplateDTO.getName());
                    codeTableDTO.setParentId(0);
                    CodeTable codeTable = this.CodeTableMapper.toEntity(codeTableDTO);
                    this.codeTableRepository.insert(codeTable);
                    if (CollectionUtils.isNotEmpty(hrContentTemplateDTO.getOptionsList())) {
                        for (HrContentDTO hrContentDTO : hrContentTemplateDTO.getOptionsList()) {
                            CodeTableDTO codeTableDTOs = new CodeTableDTO();
                            if (hrContentDTO.getId() != null) {//修改数据字典子项
                                codeTableDTOs.setId(hrContentDTO.getId());
                                codeTableDTOs.setParentId(codeTable.getId());
                                codeTableDTOs.setItemName(hrContentDTO.getItemName());
                                codeTableDTOs.setItemValue(hrContentDTO.getItemValue());
                                codeTableDTOs.setDisplayOrder(hrContentDTO.getDisplayOrder());
                                CodeTable codeTables = this.CodeTableMapper.toEntity(codeTableDTOs);
                                this.codeTableRepository.updateById(codeTables);
                            } else {//新增数据字典子项
                                codeTableDTOs.setParentId(codeTable.getId());
                                codeTableDTOs.setItemName(hrContentDTO.getItemName());
                                codeTableDTOs.setItemValue(hrContentDTO.getItemValue());
                                codeTableDTOs.setDisplayOrder(hrContentDTO.getDisplayOrder());
                                CodeTable codeTables = this.CodeTableMapper.toEntity(codeTableDTOs);
                                this.codeTableRepository.insert(codeTables);
                            }
                        }
                    }
                }
                //id不等于null的时候修改
                if(StringUtils.isNotBlank(hrContentTemplateDTO.getId())) {
                    hrContentTemplateDTO.setOptionsName("NR" + hrContentTemplateDTO.getName());
                    hrContentTemplates = this.hrContentTemplateMapper.toEntity(hrContentTemplateDTO);
                    this.hrContentTemplateRepository.updateById(hrContentTemplates);
                }else{
                    //id等于null的时候新增
                    hrContentTemplateDTO.setOptionsName("NR" + hrContentTemplateDTO.getName());
                    hrContentTemplates = this.hrContentTemplateMapper.toEntity(hrContentTemplateDTO);
                    List<HrContentTemplate>hrContentTemplateList=this.hrContentTemplateRepository.selectList(null);
                    hrContentTemplates.setOrders(hrContentTemplateList.size()+1);
                    hrContentTemplates.setPreset(1);
                this.hrContentTemplateRepository.insert(hrContentTemplates);
                }
            }
            //不是单项选择
            else {
                //id不等于null的时候修改
                if(StringUtils.isNotBlank(hrContentTemplateDTO.getId())){
                    hrContentTemplates = this.hrContentTemplateMapper.toEntity(hrContentTemplateDTO);
                    this.hrContentTemplateRepository.updateById(hrContentTemplates);
                }
                //id等于null的时候新增
                else{
                    hrContentTemplates = this.hrContentTemplateMapper.toEntity(hrContentTemplateDTO);
                    List<HrContentTemplate>hrContentTemplateList=this.hrContentTemplateRepository.selectList(null);
                    hrContentTemplates.setOrders(hrContentTemplateList.size()+1);
                    hrContentTemplates.setPreset(1);
                    this.hrContentTemplateRepository.insert(hrContentTemplates);
                }

            }
        }
        return this.hrContentTemplateMapper.toDto(hrContentTemplates);
    }
}
