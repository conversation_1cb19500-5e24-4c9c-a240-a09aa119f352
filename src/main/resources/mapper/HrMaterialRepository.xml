<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrMaterialRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, type, file_id,is_delete,created_by,created_date,last_modified_by,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrMaterial">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="file_id" property="fileId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>


    <!--    分页查询材料-->
    <select id="selectMaterialPage" resultType="cn.casair.dto.HrMaterialDTO">
        SELECT
        hm.id,
        hm.NAME,
        hm.type,
        ha.id fileId,
        ha.origin_name  fileName,
        ha.file_url fileUrl
        FROM
        hr_material hm
        LEFT JOIN hr_appendix ha ON hm.file_id = ha.id
        where hm.is_delete=0
        <if test=" param.typeList!=null and  param.typeList.size() > 0">
            and  hm.type in
            <foreach collection="param.typeList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param.name != null and param.name != ''">
            AND hm.name LIKE CONCAT('%',#{param.name},'%')
        </if>
        <choose>
            <when test="param.field!=null and param.field!=''">
                <choose>
                    <when test="param.field=='name'">
                        ORDER BY hm.NAME ${param.order}
                    </when>
                    <when test="param.field=='type'">
                        ORDER BY hm.type ${param.order}
                    </when>
                    <otherwise>
                        ORDER BY ha.origin_name ${param.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY hm.id desc
            </otherwise>
        </choose>
    </select>
    <!--    分页查询材料-->
    <select id="selectHrMaterialInfo" resultType="cn.casair.dto.HrMaterialDTO">
        SELECT
        hm.id,
        hm.NAME,
        hm.type,
        ha.origin_name  fileName,
        ha.file_url fileUrl
        FROM
        hr_material hm
        LEFT JOIN hr_appendix ha ON hm.file_id = ha.id
        where hm.is_delete=0
        AND hm.id=#{id}
    </select>


</mapper>
