package cn.casair.service;

import cn.casair.domain.HrStaffLanguage;
import cn.casair.dto.HrStaffLanguageDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 语言能力服务类
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface HrStaffLanguageService extends IService<HrStaffLanguage> {


    /**
     * 创建语言能力
     * @param hrStaffLanguageDTO
     * @return
     */
    List<HrStaffLanguageDTO> createHrStaffLanguage(HrStaffLanguageDTO hrStaffLanguageDTO);

    /**
     * 修改语言能力
     * @param hrStaffLanguageDTO
     * @return
     */
    Optional<List<HrStaffLanguageDTO>> updateHrStaffLanguage(HrStaffLanguageDTO hrStaffLanguageDTO);

    /**
     * 查询语言能力详情
     * @param id
     * @return
     */
    HrStaffLanguageDTO getHrStaffLanguage(String id);

    /**
     * 删除语言能力
     * @param id
     */
    List<HrStaffLanguageDTO> deleteHrStaffLanguage(String id);

    /**
     * 查询语言能力
     * @param staffId 员工ID
     * @return
     */
    List<HrStaffLanguageDTO> findLanguageList(String staffId);

    void setDict(HrStaffLanguageDTO dto);
}
