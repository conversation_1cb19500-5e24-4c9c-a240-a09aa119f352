package cn.casair.service;
import cn.casair.domain.HrRegistrationOrder;
import cn.casair.dto.HrRegistrationOrderDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.order.WxPayMwebOrderResult;

/**
 * 报名订单表服务类
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
public interface HrRegistrationOrderService extends IService<HrRegistrationOrder> {


    /**
     * 创建报名订单表
     * @param hrRegistrationOrderDTO
     * @return
     */
    HrRegistrationOrderDTO createHrRegistrationOrder(HrRegistrationOrderDTO hrRegistrationOrderDTO);

    /**
     * 修改报名订单表
     * @param hrRegistrationOrderDTO
     * @return
     */
    Optional<HrRegistrationOrderDTO> updateHrRegistrationOrder(HrRegistrationOrderDTO hrRegistrationOrderDTO);

    /**
     * 查询报名订单表详情
     * @param id
     * @return
     */
    HrRegistrationOrderDTO getHrRegistrationOrder(String id);

    /**
     * 删除报名订单表
     * @param id
     */
    void deleteHrRegistrationOrder(String id);

    /**
     * 批量删除报名订单表
     * @param ids
     */
    void deleteHrRegistrationOrder(List<String> ids);

    /**
     * 分页查询报名订单表
     * @param hrRegistrationOrderDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRegistrationOrderDTO hrRegistrationOrderDTO,Long pageNumber,Long pageSize);

    /**
     * 远程调用微信接口生成订单
     * @return
     */
    WxPayMpOrderResult generateOrder(HrRegistrationOrderDTO hrRegistrationOrderDTO);

}
