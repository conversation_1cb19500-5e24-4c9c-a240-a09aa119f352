<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.SysDictDataRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, iz_default, state, remark,
created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date                    ,is_delete
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.SysDictData">
                    <id column="id" property="id"/>
                    <result column="dict_sort" property="dictSort"/>
                    <result column="dict_label" property="dictLabel"/>
                    <result column="dict_value" property="dictValue"/>
                    <result column="dict_type" property="dictType"/>
                    <result column="css_class" property="cssClass"/>
                    <result column="list_class" property="listClass"/>
                    <result column="iz_default" property="izDefault"/>
                    <result column="state" property="state"/>
                    <result column="remark" property="remark"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
                <result column="is_delete" property="isDelete"/>
        </resultMap>

</mapper>
