package cn.casair.repository;
import cn.casair.domain.HrExpenseManage;
import cn.casair.dto.HrExpenseManageDTO;
import cn.casair.dto.excel.HrExpenseManageExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 费用项管理表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface HrExpenseManageRepository extends BaseMapper<HrExpenseManage> {

    List<HrExpenseManageDTO> getListByExpenseType(@Param("list") List<String> expenseTypes, @Param("clientId") String clientId);

    List<HrExpenseManageExport> selectBatch(@Param("ids") List<String> ids);

    HrExpenseManageDTO getByExpenseName(@Param("params") HrExpenseManageDTO hrExpenseManageDTO, @Param("clientId") String clientId, @Param("isDefault") Integer isDefault);

    IPage<HrExpenseManageDTO> selectPages(Page<HrExpenseManage> page, @Param("param") HrExpenseManageDTO hrExpenseManageDTO);

    /**
     * 导出查询费用管理项管理
     * @param hrExpenseManageDTO
     * @return
     */
    List<HrExpenseManageExport> findList(@Param("param") HrExpenseManageDTO hrExpenseManageDTO);

    List<HrExpenseManageDTO> getList(@Param("param") HrExpenseManageDTO hrExpenseManageDTO);

}
