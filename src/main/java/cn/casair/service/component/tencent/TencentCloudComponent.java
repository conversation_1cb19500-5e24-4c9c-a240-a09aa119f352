package cn.casair.service.component.tencent;

import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.dto.JWTMiniDTO;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 腾讯云
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/01/17 10:35
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TencentCloudComponent {

    @Value("${tencent.merchantId}")
    private String merchantId;

    private final Credential credential;
    private final RedisCache redisCache;


    /**
     * 获取E证通token
     *
     * @return com.tencentcloudapi.faceid.v20180301.models.GetEidTokenResponse
     * <AUTHOR>
     * @date 2022/1/17
     **/
    public GetEidTokenResponse getEidToken() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        try {
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("faceid.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            FaceidClient client = new FaceidClient(credential, "", clientProfile);
            GetEidTokenRequest req = new GetEidTokenRequest();
            req.setMerchantId(merchantId);
            GetEidTokenConfig config = new GetEidTokenConfig();
            config.setInputType("4");
            req.setConfig(config);
            req.setIdCard(jwtMiniDTO.getCertificateNum());
            req.setName(jwtMiniDTO.getName());
            GetEidTokenResponse resp = client.GetEidToken(req);

            String redisKey = RedisKeyEnum.tencentCloud.EID_TOKEN + jwtMiniDTO.getId();
            redisCache.setCacheObject(redisKey, JSON.toJSONString(resp), 600, TimeUnit.SECONDS);
            return resp;
        } catch (TencentCloudSDKException e) {
            log.error("获取E证通Token发生异常：{}", e.getMessage());
            throw new CommonException("获取E证通Token发生异常：" + e.getMessage());
        }

    }

    /**
     * 获取E证通人身核验结果
     *
     * @return void
     * <AUTHOR>
     * @date 2022/1/17
     **/
    public GetEidResultResponse getEidResult() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        try {
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("faceid.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            FaceidClient client = new FaceidClient(credential, "", clientProfile);
            GetEidResultRequest req = new GetEidResultRequest();

            String redisKey = RedisKeyEnum.tencentCloud.EID_TOKEN + jwtMiniDTO.getId();
            Object cacheObject = redisCache.getCacheObject(redisKey);
            if (cacheObject == null) {
                throw new CommonException("未查询到eidToken记录！");
            }
            GetEidTokenResponse eidTokenResponse = JSON.parseObject(cacheObject.toString(), GetEidTokenResponse.class);
            req.setEidToken(eidTokenResponse.getEidToken());
            GetEidResultResponse resp = client.GetEidResult(req);

            return resp;
        } catch (TencentCloudSDKException e) {
            log.error("获取E证通人身核验结果发生异常：{}", e.getMessage());
            throw new CommonException("获取E证通人身核验结果发生异常：" + e.getMessage());
        }
    }
}
