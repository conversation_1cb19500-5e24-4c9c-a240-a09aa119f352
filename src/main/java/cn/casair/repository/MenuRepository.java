package cn.casair.repository;

import cn.casair.domain.Menu;
import cn.casair.dto.MenuDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 菜单表数据库操作类
 *
 * <AUTHOR>
 * @since 2020-01-13
 */
@Repository
public interface MenuRepository extends BaseMapper<Menu> {

    /**
     * 根据用户ID查询当前用户菜单列表
     *
     * @param userId 用户ID
     * @return List<EsMenu>
     */
    List<MenuDTO> findListByAdminUserId(@Param("userId") String userId);

    /**
     * 根据角色ID查询当前角色菜单列表
     *
     * @param roleId 角色ID
     * @return List<EsMenu>
     */
    List<MenuDTO> findListByRoleId(@Param("roleId") Integer roleId);

    @Select("select *from es_menu m where m.table_name = #{tableName}")
    List<Menu> getByTableName(@Param("tableName") String tableName);

    List<String> findButtonListByAdminUserId(@Param("userId") String userId);
}
