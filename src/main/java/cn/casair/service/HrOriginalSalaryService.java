package cn.casair.service;

import cn.casair.domain.HrOriginalSalary;
import cn.casair.dto.HrOriginalSalaryDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 薪酬原单（客户）服务类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface HrOriginalSalaryService extends IService<HrOriginalSalary> {


    /**
     * 创建薪酬原单（客户）
     * @param hrOriginalSalaryDTO
     * @return
     */
    HrOriginalSalaryDTO createHrOriginalSalary(HrOriginalSalaryDTO hrOriginalSalaryDTO);

    /**
     * 修改薪酬原单（客户）
     * @param hrOriginalSalaryDTO
     * @return
     */
    Optional<HrOriginalSalaryDTO> updateHrOriginalSalary(HrOriginalSalaryDTO hrOriginalSalaryDTO);

    /**
     * 查询薪酬原单（客户）详情
     * @param id
     * @return
     */
    HrOriginalSalaryDTO getHrOriginalSalary(String id);

    /**
     * 删除薪酬原单（客户）
     * @param id
     */
    void deleteHrOriginalSalary(String id);

    /**
     * 批量删除薪酬原单（客户）
     * @param ids
     */
    void deleteHrOriginalSalary(List<String> ids);

    /**
     * 分页查询薪酬原单（客户）
     * @param hrOriginalSalaryDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrOriginalSalaryDTO hrOriginalSalaryDTO,Long pageNumber,Long pageSize);


    List<HrOriginalSalary> selectHrOriginalSalary(List<String> ids);

    List<HrOriginalSalary>   getHrOriginalSalarySelect(HrOriginalSalaryDTO hrOriginalSalaryDTO);

    /**
     * 不分页查询薪酬原单
     * @param hrOriginalSalaryDTO
     * @return
     */
    List<HrOriginalSalaryDTO> findList(HrOriginalSalaryDTO hrOriginalSalaryDTO);

}
