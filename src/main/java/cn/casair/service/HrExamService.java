package cn.casair.service;
import cn.casair.domain.HrExam;
import cn.casair.dto.HrExamDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-10-17
 */
public interface HrExamService extends IService<HrExam> {


    /**
     * 创建
     * @param hrExamDTO
     * @return
     */
    HrExamDTO createHrExam(HrExamDTO hrExamDTO);

    /**
     * 修改
     * @param hrExamDTO
     * @return
     */
    Optional<HrExamDTO> updateHrExam(HrExamDTO hrExamDTO);

    /**
     * 查询详情
     * @param id
     * @return
     */
    HrExamDTO getHrExam(String id);

    /**
     * 删除
     * @param id
     */
    void deleteHrExam(String id);

    /**
     * 批量删除
     * @param ids
     */
    void deleteHrExam(List<String> ids);

    /**
     * 分页查询
     * @param hrExamDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrExamDTO hrExamDTO,Long pageNumber,Long pageSize);

    /**
     * 导出考试结果试卷
     * @param examDTO
     * @param httpServletResponse
     * @return
     */
    String exportExams(HrExamDTO examDTO, HttpServletResponse httpServletResponse);

    HrExamDTO saveHrExam(HrExamDTO hrExamDTO);

}
