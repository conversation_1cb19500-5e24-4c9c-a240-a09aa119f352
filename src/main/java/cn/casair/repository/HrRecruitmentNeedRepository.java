package cn.casair.repository;

import cn.casair.domain.HrRecruitmentNeed;
import cn.casair.dto.HrRecruitmentNeedDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 招聘需求表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Repository
public interface HrRecruitmentNeedRepository extends BaseMapper<HrRecruitmentNeed> {

    IPage<HrRecruitmentNeedDTO> page(Page page, @Param(Constants.WRAPPER)QueryWrapper<HrRecruitmentNeed> qw);
}
