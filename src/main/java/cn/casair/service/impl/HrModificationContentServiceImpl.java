package cn.casair.service.impl;

import cn.casair.domain.HrModificationContent;
import cn.casair.dto.HrModificationContentDTO;
import cn.casair.mapper.HrModificationContentMapper;
import cn.casair.repository.HrModificationContentRepository;
import cn.casair.service.HrModificationContentService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 申请修改内容服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrModificationContentServiceImpl extends ServiceImpl<HrModificationContentRepository, HrModificationContent> implements HrModificationContentService {


    private final HrModificationContentRepository hrModificationContentRepository;
    private final HrModificationContentMapper hrModificationContentMapper;

    public HrModificationContentServiceImpl(HrModificationContentRepository hrModificationContentRepository, HrModificationContentMapper hrModificationContentMapper) {
        this.hrModificationContentRepository = hrModificationContentRepository;
        this.hrModificationContentMapper = hrModificationContentMapper;
    }

    /**
     * 创建申请修改内容
     *
     * @param hrModificationContentDTO
     * @return
     */
    @Override
    public HrModificationContentDTO createHrModificationContent(HrModificationContentDTO hrModificationContentDTO) {
        log.info("Create new HrModificationContent:{}", hrModificationContentDTO);

        HrModificationContent hrModificationContent = this.hrModificationContentMapper.toEntity(hrModificationContentDTO);
        this.hrModificationContentRepository.insert(hrModificationContent);
        return this.hrModificationContentMapper.toDto(hrModificationContent);
    }

    /**
     * 修改申请修改内容
     *
     * @param hrModificationContentDTO
     * @return
     */
    @Override
    public Optional<HrModificationContentDTO> updateHrModificationContent(HrModificationContentDTO hrModificationContentDTO) {
        return Optional.ofNullable(this.hrModificationContentRepository.selectById(hrModificationContentDTO.getId()))
            .map(roleTemp -> {
                HrModificationContent hrModificationContent = this.hrModificationContentMapper.toEntity(hrModificationContentDTO);
                this.hrModificationContentRepository.updateById(hrModificationContent);
                log.info("Update HrModificationContent:{}", hrModificationContentDTO);
                return hrModificationContentDTO;
            });
    }

    /**
     * 查询申请修改内容详情
     *
     * @param id
     * @return
     */
    @Override
    public HrModificationContentDTO getHrModificationContent(String id) {
        log.info("Get HrModificationContent :{}", id);

        HrModificationContent hrModificationContent = this.hrModificationContentRepository.selectById(id);
        return this.hrModificationContentMapper.toDto(hrModificationContent);
    }

    /**
     * 删除申请修改内容
     *
     * @param id
     */
    @Override
    public void deleteHrModificationContent(String id) {
        Optional.ofNullable(this.hrModificationContentRepository.selectById(id))
            .ifPresent(hrModificationContent -> {
                this.hrModificationContentRepository.deleteById(id);
                log.info("Delete HrModificationContent:{}", hrModificationContent);
            });
    }

    /**
     * 批量删除申请修改内容
     *
     * @param ids
     */
    @Override
    public void deleteHrModificationContent(List<String> ids) {
        log.info("Delete HrModificationContents:{}", ids);
        this.hrModificationContentRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询申请修改内容
     *
     * @param hrModificationContentDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrModificationContentDTO hrModificationContentDTO, Long pageNumber, Long pageSize) {
        Page<HrModificationContent> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrModificationContent> qw = new QueryWrapper<>(this.hrModificationContentMapper.toEntity(hrModificationContentDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrModificationContentRepository.selectPage(page, qw);
        iPage.setRecords(this.hrModificationContentMapper.toDto(iPage.getRecords()));
        return iPage;
    }
}
