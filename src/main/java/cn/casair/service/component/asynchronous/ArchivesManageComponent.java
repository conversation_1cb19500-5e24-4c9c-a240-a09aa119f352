package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.ArchivesEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.ArchivesBringDetail;
import cn.casair.domain.HrArchivesBring;
import cn.casair.domain.HrArchivesDetail;
import cn.casair.domain.HrArchivesManage;
import cn.casair.domain.HrClient;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.HrArchivesDetailDTO;
import cn.casair.dto.HrArchivesManageDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrArchivesManageImport;
import cn.casair.mapper.HrArchivesManageMapper;
import cn.casair.repository.HrArchivesBringRepository;
import cn.casair.repository.HrArchivesDetailRepository;
import cn.casair.repository.HrArchivesManageRepository;
import cn.casair.repository.HrClientRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 档案异步处理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/12/23 15:46
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ArchivesManageComponent {

    @Value("${file.temp-path}")
    private String tempPath;

    private final HrArchivesManageRepository hrArchivesManageRepository;
    private final HrArchivesDetailRepository hrArchivesDetailRepository;
    private final HrArchivesBringRepository hrArchivesBringRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrArchivesManageMapper hrArchivesManageMapper;
    private final HrClientRepository hrClientRepository;
    private final SysOperLogService sysOperLogService;
    private final HrAppendixService hrAppendixService;
    private final RedisCache redisCache;

    /**
     * 档案导入
     *
     * @param inputStream
     * @param redisKey
     * @param user
     */
    @Async
    public void dealArchivesImport(InputStream inputStream, String redisKey, JWTUserDTO user) {
        try {
            ExcelImportResult<HrArchivesManageImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrArchivesManageImport.class);
            ImportResultDTO importResult;

            List<String> existingList = this.hrArchivesManageRepository.selectExistingList();

            List<HrArchivesManageImport> list = result.getList();
            int listSize = list.size();
            int scale = 0;

            Map<String, List<HrArchivesManageImport>> collect = list.stream().collect(Collectors.groupingBy(HrArchivesManageImport::getArchivesNum));
            List<HrArchivesManageImport> collect1 = collect.values().stream().filter(ls -> ls.size() > 1).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            List<String> collect2 = collect1.stream().map(HrArchivesManageImport::getArchivesNum).collect(Collectors.toList());
            // 遍历档案
            StringBuilder sbError = new StringBuilder();
            for (HrArchivesManageImport importData : list) {
                try {
                    sbError.delete(0, sbError.length());
                    // 检查Excel是否存在重复档案编号
                    if (collect2.contains(importData.getArchivesNum())) {
                        importData.setDuplicate(true);
                        sbError.append("Excel中档案编号重复！");
                    }
                    // 检查档案编号是否存在
                    if (existingList.contains(importData.getArchivesNum())) {
                        sbError.append("档案编号已经存在！");
                    }

                    // 判断档案类型，分别处理员工档案与客户档案
                    // 员工档案
                    if (importData.getArchivesType().equals(ArchivesEnum.ArchivesType.STAFF.getKey())) {
                        // 根据员工编号查询员工信息
                        HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectBySystemNum(importData.getSystemNum());
                        if (hrTalentStaff == null) {
                            sbError.append("未查询到员工信息！");
                        }
                        // 检查员工档案是否已经存在，并且档案明细不为空
                        HrArchivesManageDTO oldArchives = this.hrArchivesManageRepository.getArchivesByTypeAndUnionId(ArchivesEnum.ArchivesType.STAFF.getKey(), hrTalentStaff.getId());
                        if (oldArchives != null) {
                            List<HrArchivesDetailDTO> archivesDetails = this.hrArchivesDetailRepository.getArchivesDetailByArchivesId(oldArchives.getId());
                            if (!archivesDetails.isEmpty()) {
                                importData.setDuplicate(true);
                                sbError.append("档案明细信息已经存在！");
                            }
                        }
                        if (StringUtils.isEmpty(sbError)) {
                            this.dealImportArchivesManageSave(oldArchives, importData, hrTalentStaff, null);
                        }
                    }
                    // 客户档案
                    else {
                        // 检查单位信息
                        HrClient hrClient = this.hrClientRepository.selectClientUnitNumber(importData.getUnitNumber());
                        if (hrClient == null) {
                            throw new CommonException("未查询到单位信息！");
                        }
                        // 检查客户档案是否已经存在
                        HrArchivesManageDTO oldArchives = this.hrArchivesManageRepository.getArchivesByTypeAndUnionId(ArchivesEnum.ArchivesType.CLIENT.getKey(), hrClient.getId());
                        if (oldArchives != null) {
                            List<HrArchivesDetailDTO> archivesDetails = this.hrArchivesDetailRepository.getArchivesDetailByArchivesId(oldArchives.getId());
                            if (!archivesDetails.isEmpty()) {
                                importData.setDuplicate(true);
                                sbError.append("档案明细信息已经存在！");
                            }
                        }
                        if (StringUtils.isEmpty(sbError)) {
                            this.dealImportArchivesManageSave(oldArchives, importData, null, hrClient);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("批量导入档案发生异常：{}", e.getMessage());
                    sbError.append(e.getMessage());
                } finally {
                    // 设置进度条进度
                    importData.setErrorMsg(sbError.toString());
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 30, TimeUnit.MINUTES);
                    log.info("批量导入档案Redis进度：{}", i);
                }
            }

            // 封装返回信息
            importResult = ImportResultUtils.writeErrorFile("档案批量导入", HrArchivesManageImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CONTRACT.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, user);
        } catch (Exception e) {
            log.error("档案异步导入处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "档案导入发生异常：Excel文件读取失败，" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    /**
     * 处理档案保存
     *
     * @param oldArchives
     * @param importData
     * @param hrTalentStaff
     * @param hrClient
     */
    private void dealImportArchivesManageSave(HrArchivesManageDTO oldArchives, HrArchivesManageImport importData, HrTalentStaff hrTalentStaff, HrClient hrClient) {
        HrArchivesManage hrArchivesManage = new HrArchivesManage();
        if (oldArchives != null) {
            hrArchivesManage = this.hrArchivesManageMapper.toEntity(oldArchives);
        } else {
            if (hrTalentStaff != null) {
                hrArchivesManage.setStaffId(hrTalentStaff.getId());
                hrArchivesManage.setArchivesName(hrTalentStaff.getName());
            } else {
                hrArchivesManage.setArchivesName(hrClient.getClientName());
            }
            if (importData.getArchivesType().equals(ArchivesEnum.ArchivesType.CLIENT.getKey())) {
                hrArchivesManage.setClientId(hrClient.getId());
            }
            if (StringUtils.isNotBlank(importData.getArchivesNum())) {
                hrArchivesManage.setArchivesNum(importData.getArchivesNum());
            } else {
                hrArchivesManage.setArchivesNum("DA" + RandomStringUtils.randomNumeric(13));
            }
            // hrArchivesManage.setEmpUnit(hrClient.getClientName());
            hrArchivesManage.setArchivesType(importData.getArchivesType());

            hrArchivesManage.setArchivesLocal(importData.getArchivesLocal());
            hrArchivesManage.setArchivesStatus(importData.getArchivesStatus());
            if (importData.getWarehouseTime() != null) {
                hrArchivesManage.setWarehouseTime(importData.getWarehouseTime());
            } else {
                hrArchivesManage.setWarehouseTime(LocalDate.now());
            }
            this.hrArchivesManageRepository.insert(hrArchivesManage);
        }

        // 档案调入
        HrArchivesBring hrArchivesBring = new HrArchivesBring();
        hrArchivesBring.setArchivesId(hrArchivesManage.getId());
        hrArchivesBring.setCtType(ArchivesEnum.BringType.TRANSFER_IN.getKey());
        hrArchivesBring.setCtProposes(ArchivesEnum.BringProposesType.OTHER.getKey());
        hrArchivesBring.setCtRemark("档案导入");
        this.hrArchivesBringRepository.insert(hrArchivesBring);

        // 处理档案明细
        if (StringUtils.isNotBlank(importData.getArchivesDetail())) {
            String[] split = importData.getArchivesDetail().split("、");
            for (String detail : split) {
                HrArchivesDetail hrArchivesDetail = new HrArchivesDetail();
                hrArchivesDetail.setArchivesId(hrArchivesManage.getId());
                hrArchivesDetail.setName(detail);
                hrArchivesDetail.setState(ArchivesEnum.DetailState.IN_GEAR.getKey());
                hrArchivesDetail.setType(ArchivesEnum.DetailType.PHYSICAL_FILE.getKey());
                this.hrArchivesDetailRepository.insert(hrArchivesDetail);

                // 档案明细与调入调出关联
                ArchivesBringDetail archivesBringDetail = new ArchivesBringDetail();
                archivesBringDetail.setId(UUID.randomUUID().toString().replace("-", ""));
                archivesBringDetail.setBringId(hrArchivesBring.getId());
                archivesBringDetail.setDetailId(hrArchivesDetail.getId());
                this.hrArchivesBringRepository.insertArchivesBringDetail(archivesBringDetail);
            }
        }
    }
}
