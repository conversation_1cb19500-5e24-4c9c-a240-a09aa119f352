package cn.casair.service;
import cn.casair.domain.HrLaborAppraisal;
import cn.casair.domain.HrWorkInjury;
import cn.casair.dto.HrLaborAppraisalDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;

/**
 * 劳动能力鉴定表服务类
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface HrLaborAppraisalService extends IService<HrLaborAppraisal> {


    /**
     * 创建劳动能力鉴定表
     * @param hrLaborAppraisalDTO
     * @return
     */
    HrLaborAppraisalDTO createHrLaborAppraisal(HrLaborAppraisalDTO hrLaborAppraisalDTO);

    /**
     * 修改劳动能力鉴定表
     * @param hrLaborAppraisalDTO
     * @return
     */
    Optional<HrLaborAppraisalDTO> updateHrLaborAppraisal(HrLaborAppraisalDTO hrLaborAppraisalDTO);

    /**
     * 查询劳动能力鉴定表详情
     * @param id
     * @return
     */
    HrLaborAppraisalDTO getHrLaborAppraisal(String id);

    /**
     * 删除劳动能力鉴定表
     * @param id
     */
    void deleteHrLaborAppraisal(String id);

    /**
     * 批量删除劳动能力鉴定表
     * @param ids
     */
    void deleteHrLaborAppraisal(List<String> ids);

    /**
     * 分页查询劳动能力鉴定表
     * @param hrLaborAppraisalDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrLaborAppraisalDTO hrLaborAppraisalDTO,Long pageNumber,Long pageSize);

    /**
     * 查看劳动能力鉴定详情
     * @param hrLaborAppraisalDTO
     * @return
     */
    HrLaborAppraisalDTO getDetail(HrLaborAppraisalDTO hrLaborAppraisalDTO);

    /**
     * 微信 添加劳动能力鉴定
     * @param hrLaborAppraisalDTO
     * @return
     */
    HrLaborAppraisalDTO addLaborAppraisal(HrLaborAppraisalDTO hrLaborAppraisalDTO);


    /**
     * 微信 是否同意进行劳动能力鉴定
     * @param hrLaborAppraisalDTO
     * @return
     */
    HrLaborAppraisalDTO auditLaborAppraisal(HrLaborAppraisalDTO hrLaborAppraisalDTO);

    /**
     * 微信 劳动能力鉴定的进度详情
     * @return
     */
    List<HrLaborAppraisalDTO> laborAppraisalPlanned();

    /**
     * 批量通知
     * @param hrLaborAppraisalDTOList
     * @return
     */
    ResponseEntity<?> updateStates(List<HrLaborAppraisalDTO> hrLaborAppraisalDTOList);

    /**
     * 微信 劳动能力鉴定获取符合添加的工伤
     * @return
     */
    List<HrWorkInjury> laborAppraisalWork();

    /**
     * 导出劳动能力鉴定
     * @param hrLaborAppraisalDTO
     * @param httpServletResponse
     * @return
     */
    String exportLaborAppraisals(HrLaborAppraisalDTO hrLaborAppraisalDTO, HttpServletResponse httpServletResponse);

    /**
     * 微信 劳动能力鉴定单条的进度详情
     * @param serviceId
     * @return
     */
    HrLaborAppraisalDTO laborAppraisalPlannedOne(String serviceId);
}
