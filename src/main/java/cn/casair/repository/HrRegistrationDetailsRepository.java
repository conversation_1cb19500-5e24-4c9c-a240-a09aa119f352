package cn.casair.repository;

import cn.casair.domain.HrRegistrationDetails;
import cn.casair.dto.HrExamResultDTO;
import cn.casair.dto.HrRegistrationDetailsDTO;
import cn.casair.dto.HrTemplateDTO;
import cn.casair.dto.excel.HrDrawLotsNumberTemplate;
import cn.casair.dto.excel.HrRegistrationDetailsExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 报名情况数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Repository
public interface HrRegistrationDetailsRepository extends BaseMapper<HrRegistrationDetails> {


    /**
     * 获取招聘报名列表
     *
     * @param brochureId
     * @return
     */
    List<HrRegistrationDetails> selectByBrochureId(@Param("brochureId") String brochureId);

    IPage<HrRegistrationDetailsDTO> selectPages(Page<HrRegistrationDetails> page, @Param("param1") HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    void updateExam(HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    List<HrDrawLotsNumberTemplate> importDrawLotsNumberTemplate(String brochureId);

    List<HrRegistrationDetailsExport> detailsExport(@Param("ids") List<String> ids);

    List<HrTemplateDTO> selectBybrochureId(@Param("id") String id, @Param("ids") List<String> ids);

    /**
     * 获取报名人数
     *
     * @param recruitmentBrochureId 招聘简章Id
     * @param recruitmentStationId  招聘简章对应岗位Id
     * @param status                状态
     * @return
     */
    List<HrRegistrationDetails> findPaymentAndPassNum(@Param("recruitmentBrochureId") String recruitmentBrochureId,
                                                      @Param("recruitmentStationId") String recruitmentStationId,
                                                      @Param("status") Integer status);

    List<HrRegistrationDetailsDTO> findRegistrationDetails(@Param("recruitmentBrochureId") String recruitmentBrochureId,
                                                           @Param("recruitmentStationId") String recruitmentStationId,
                                                           @Param("statusList") List<Integer> statusList);



    void updatePassNum(@Param("i") int i, @Param("id") String id);

    void updatestas(String s);

    List<HrRegistrationDetails> selectOnea(@Param("id") String id, @Param("brochureId") String brochureId);

    String selectRoleName(String id);

    void updateState(String id);

    void updatestatus(@Param("param1")HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    List<HrRegistrationDetailsDTO> findDetailsWrittenExam(@Param("recruitmentBrochureId") String recruitmentBrochureId,
                                                          @Param("recruitmentStationId") String recruitmentStationId,
                                                          @Param("statusList") List<Integer> statusList);

    void updateBrochureStates(String brochureId);

    void updateStatusReview(@Param("id")String id, @Param("status")String status);

    void updateStatusReviews(@Param("id")String id, @Param("status")String status, @Param("denialReason")String denialReason);

    HrRegistrationDetailsDTO detailsExports(String id);

    void updateAchievement(@Param("param1")HrExamResultDTO hrExamResultDTO);

    void updateAchievements(@Param("param1")HrExamResultDTO hrExamResultDTO);

    void updatestatuss(@Param("status")String status, @Param("id")String id);

    /**
     * 根据报名情况id或取页面报名情况详情
     * @param id
     * @return
     */
    HrRegistrationDetailsExport detailsExportById(String id);

    List<HrRegistrationDetailsDTO> findList(@Param("brochureId") String brochureId, @Param("recruitmentStationIds") List<String> recruitmentStationIds);

    /**
     * 不分页查询报名列表
     * @param hrRegistrationDetailsDTO
     * @return
     */
    List<HrRegistrationDetails> findRegistrationDetailsList(@Param("param1") HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    void deleteRegistrationDetailsById(@Param("id") String id);

}
