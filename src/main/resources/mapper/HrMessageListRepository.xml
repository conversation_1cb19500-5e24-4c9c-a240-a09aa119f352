<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrMessageListRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        title,
        type,
        states,
        created_by_id,
        top_date,
        top,
        recipient_role_ids,
        content,
        content_type,
        is_delete ,
        created_by ,
        created_date ,
        last_modified_by ,
        last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrMessageList">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="created_by_id" property="createdById"/>
        <result column="recipient_role_ids" property="recipientRoleIds"/>
        <result column="content" property="content"/>
        <result column="content_type" property="contentType"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <update id="updateMessageList">
        UPDATE hr_message_role
        set top_date=#{topDatesString},
            top=#{top}
        where message_id = #{id}
          and is_delete = 0
    </update>

    <update id="updateMessageRole">
        UPDATE hr_message_role
        set states=1
        where message_id = #{messageId}
          and user_id = #{userId}
          AND is_delete = 0
    </update>

    <select id="selectPageMessageList" resultType="cn.casair.dto.HrMessageListDTO">
        SELECT
        ml.*,
        mr.message_id as messageId,
        mr.user_id AS userId,
        mr.states as states,
        mr.top,
        mr.created_by as createdBy,
        ml.recipient_role_ids as recipientRoleIds,
        mr.top_date as topDate,
        su.real_name as realName
        FROM
        hr_message_list ml
        LEFT JOIN hr_message_role mr on mr.message_id=ml.id
        LEFT JOIN sys_user su on ml.created_by_id=su.id
        where
        mr.user_id =#{param1.userId}

     <!--   <if test="param1.userIdList!=null and   param1.userIdList.size() > 0">
            mr.user_id in
            <foreach collection="param1.userIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>-->


        <if test="param1.title!=null and param1.title!=''">
            and ml.title like concat('%',#{param1.title},'%')
        </if>
        <if test=" param1.typeList!=null and  param1.typeList.size() > 0">
            and ml.type in
            <foreach collection="param1.typeList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test=" param1.statesList!=null and  param1.statesList.size() > 0">
            and mr.states in
            <foreach collection="param1.statesList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.contractStartDateStart!=null ">
            AND ml.created_date <![CDATA[ >= ]]> #{param1.contractStartDateStart}
        </if>
        <if test="param1.contractStartDateEnd!=null ">

            AND ml.created_date <![CDATA[ <= ]]> date_add(#{param1.contractStartDateEnd}, interval 1 day)
        </if>
        and mr.is_delete=0 and ml.is_delete=0
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by case when mr.top = 1 then 0 else 1 end, ${param1.field} ${param1.order},mr.top_date DESC
        </if>
        <if test="param1 .field == null or param1.field == ''">
            order by case when top = 1 then 0 else 1 end, ml.created_date DESC, top_date   DESC
        </if>


    </select>
    <update id="BatchUpdateHrMessageList">
        UPDATE hr_message_role
        set states=1
        where user_id = #{param2}
          and message_id = #{param1}
          and is_delete = 0
    </update>

    <select id="getHrMessageListType" resultType="java.lang.String">
        SELECT any_value(type) type
        from hr_message_list
        where is_delete = 0
        GROUP BY type
    </select>
</mapper>
