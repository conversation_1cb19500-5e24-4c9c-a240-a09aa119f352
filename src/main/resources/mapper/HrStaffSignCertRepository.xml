<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffSignCertRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_id, client_id, type, card_type, id_card_num, `name`, mobile_phone, state, `issuer`, serial_number, cert_not_before, cert_not_after, user_name, user_num,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffSignCert">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="type" property="type"/>
        <result column="card_type" property="cardType"/>
        <result column="id_card_num" property="idCardNum"/>
        <result column="name" property="name"/>
        <result column="mobile_phone" property="mobilePhone"/>
        <result column="state" property="state"/>
        <result column="issuer" property="issuer"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="cert_not_before" property="certNotBefore"/>
        <result column="cert_not_after" property="certNotAfter"/>
        <result column="user_name" property="userName"/>
        <result column="user_num" property="userNum"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByObject" resultType="cn.casair.domain.HrStaffSignCert">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_staff_sign_cert
        WHERE
            is_delete = 0
          AND staff_id = #{staffId}
          AND type = #{type}
          AND card_type = #{cardType}
    </select>

    <select id="getByStaffId" resultType="cn.casair.domain.HrStaffSignCert">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_staff_sign_cert
        WHERE is_delete = 0
          AND staff_id = #{staffId}
    </select>

</mapper>
