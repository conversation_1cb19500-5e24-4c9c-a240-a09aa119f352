package cn.casair.service;

import cn.casair.domain.HrStaffContacts;
import cn.casair.dto.HrStaffContactsDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 紧急联系人服务类
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface HrStaffContactsService extends IService<HrStaffContacts> {


    /**
     * 创建紧急联系人
     * @param hrStaffContactsDTO
     * @return
     */
    List<HrStaffContactsDTO> createHrStaffContacts(HrStaffContactsDTO hrStaffContactsDTO);

    /**
     * 修改紧急联系人
     * @param hrStaffContactsDTO
     * @return
     */
    Optional<List<HrStaffContactsDTO>> updateHrStaffContacts(HrStaffContactsDTO hrStaffContactsDTO);

    /**
     * 查询紧急联系人详情
     * @param id
     * @return
     */
    HrStaffContactsDTO getHrStaffContacts(String id);

    /**
     * 删除紧急联系人
     * @param id
     */
    List<HrStaffContactsDTO> deleteHrStaffContacts(String id);

    /**
     * 查询紧急联系人
     * @param staffId 员工ID
     * @return
     */
    List<HrStaffContactsDTO> findContactsList (String staffId);
}
