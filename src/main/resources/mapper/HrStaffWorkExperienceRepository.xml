<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffWorkExperienceRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        staff_id,
        client_id,
        employer_unit,
        unit_location,
        staff_num,
        dept_name,
        station_id,
        personnel_type,
        work_location,
        contract_start_date,
        contract_end_date,
        salary_section,
        work_nature,
        board_date,
        departure_date,
        work_achievement,
        work_evaluation,
        self_evaluation,
        dimission_reason,
        certify_people,
        certify_phone,
        work_remark,
        iz_default,
        staff_status,
        iz_insured,
        is_delete,
        created_by,
        created_date,
        last_modified_by,
        last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffWorkExperience">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="employer_unit" property="employerUnit"/>
        <result column="unit_location" property="unitLocation"/>
        <result column="staff_num" property="staffNum"/>
        <result column="dept_name" property="deptName"/>
        <result column="station_id" property="stationId"/>
        <result column="personnel_type" property="personnelType"/>
        <result column="work_location" property="workLocation"/>
        <result column="contract_start_date" property="contractStartDate"/>
        <result column="contract_end_date" property="contractEndDate"/>
        <result column="salary_section" property="salarySection"/>
        <result column="board_date" property="boardDate"/>
        <result column="departure_date" property="departureDate"/>
        <result column="work_achievement" property="workAchievement"/>
        <result column="work_evaluation" property="workEvaluation"/>
        <result column="work_remark" property="workRemark"/>
        <result column="iz_default" property="izDefault"/>
        <result column="staff_status" property="staffStatus"/>
        <result column="iz_insured" property="izInsured"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getStaffCurrentWorkExperience" resultType="cn.casair.domain.HrStaffWorkExperience">
        SELECT * FROM hr_staff_work_experience WHERE is_delete = 0 AND staff_id = #{staffId} AND iz_default = 1
    </select>

    <select id="selectStaffNewstWorkExperience" resultType="cn.casair.domain.HrStaffWorkExperience">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_staff_work_experience
        WHERE is_delete = 0
          AND staff_id = #{staffId}
          AND client_id = #{clientId}
          AND iz_default = 1
        limit 1
    </select>

    <sql id="findSQl">
        SELECT
            a.id,
            a.staff_id,
            a.client_id,
            CASE WHEN a.client_id IS NULL THEN a.employer_unit ELSE c.client_name END as employer_unit,
            a.unit_location,
            a.staff_num,
            a.dept_name,
            a.station_id,
            a.personnel_type,
            a.work_location,
            a.contract_start_date,
            a.contract_end_date,
            a.salary_section,
            a.work_nature,
            a.board_date,
            a.departure_date,
            a.work_achievement,
            a.work_evaluation,
            a.self_evaluation,
            a.dimission_reason,
            a.certify_people,
            a.certify_phone,
            a.work_remark,
            a.iz_default,
            a.staff_status,
            a.iz_insured,
            a.is_delete,
            a.created_by,
            a.created_date,
            a.last_modified_by,
            a.last_modified_date,
            b.profession_name,
            c.client_name
         FROM hr_staff_work_experience a
         LEFT JOIN hr_station b ON a.station_id = b.id
         LEFT JOIN hr_client c ON a.client_id = c.id
    </sql>

    <select id="findWorkExperienceList" resultType="cn.casair.dto.HrStaffWorkExperienceDTO">
        <include refid="findSQl"/>
        WHERE a.is_delete = 0 AND a.iz_default = #{izDefault}
        <if test="staffId != null and staffId != ''">
            and a.staff_id = #{staffId}
        </if>
    </select>

    <select id="selectWorkExperienceById" resultType="cn.casair.dto.HrStaffWorkExperienceDTO">
        <include refid="findSQl"/>
        WHERE a.is_delete = 0 AND a.id = #{id}
    </select>

    <select id="findDelExperience" resultType="cn.casair.domain.HrStaffWorkExperience">
	    SELECT
            *
        FROM
            hr_staff_work_experience
        WHERE
            staff_id = #{staffId}
            AND client_id = #{clientId}
            AND iz_default = 1
            AND departure_date IS NULL
        ORDER BY board_date DESC LIMIT 1
    </select>

    <update id="updateDelExperience">
	    UPDATE hr_staff_work_experience SET is_delete = 0, last_modified_date = now() WHERE id = #{id}
    </update>

</mapper>
