sonar.projectKey=casairwebbase
sonar.projectName=casairwebbase generated by jhipster
sonar.projectVersion=1.0

sonar.sources=src/main/
sonar.host.url=http://localhost:9001

sonar.tests=src/test/
sonar.jacoco.reportPaths=target/jacoco/test.exec,target/jacoco/integrationTest.exec
sonar.java.codeCoveragePlugin=jacoco
sonar.junit.reportPaths=target/test-results/test,target/test-results/integrationTest
sonar.testExecutionReportPaths=target/test-results/jest/TESTS-results-sonar.xml
sonar.typescript.lcov.reportPaths=target/test-results/lcov.info

sonar.sourceEncoding=UTF-8
sonar.exclusions=src/main/webapp/content/**/*.*, src/main/webapp/i18n/*.js, target/classes/static/**/*.*

sonar.issue.ignore.multicriteria=S3437,S4502,S4684,Undocumented<PERSON><PERSON>,BoldAndItalicTagsCheck
# Rule https://sonarcloud.io/coding_rules?open=squid%3AS3437&rule_key=squid%3AS3437 is ignored, as a JPA-managed field cannot be transient
sonar.issue.ignore.multicriteria.S3437.resourceKey=src/main/java/**/*
sonar.issue.ignore.multicriteria.S3437.ruleKey=squid:S3437
# Rule https://sonarcloud.io/coding_rules?open=squid%3AUndocumentedApi&rule_key=squid%3AUndocumentedApi is ignored, as we want to follow "clean code" guidelines and classes, methods and arguments names should be self-explanatory
sonar.issue.ignore.multicriteria.UndocumentedApi.resourceKey=src/main/java/**/*
sonar.issue.ignore.multicriteria.UndocumentedApi.ruleKey=squid:UndocumentedApi
# Rule https://sonarcloud.io/coding_rules?open=squid%3AS4502&rule_key=squid%3AS4502 is ignored, as for JWT tokens we are not subject to CSRF attack
sonar.issue.ignore.multicriteria.S4502.resourceKey=src/main/java/**/*
sonar.issue.ignore.multicriteria.S4502.ruleKey=squid:S4502
# Rule https://sonarcloud.io/coding_rules?open=squid%3AS4684&rule_key=squid%3AS4684
sonar.issue.ignore.multicriteria.S4684.resourceKey=src/main/java/**/*
sonar.issue.ignore.multicriteria.S4684.ruleKey=squid:S4684
# Rule https://sonarcloud.io/coding_rules?open=Web%3ABoldAndItalicTagsCheck&rule_key=Web%3ABoldAndItalicTagsCheck is ignored. Even if we agree that using the "i" tag is an awful practice, this is what is recommended by http://fontawesome.io/examples/
sonar.issue.ignore.multicriteria.BoldAndItalicTagsCheck.resourceKey=src/main/webapp/app/**/*.*
sonar.issue.ignore.multicriteria.BoldAndItalicTagsCheck.ruleKey=Web:BoldAndItalicTagsCheck
