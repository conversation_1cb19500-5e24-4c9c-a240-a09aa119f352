package cn.casair.service;
import cn.casair.domain.HrUserClient;
import cn.casair.dto.HrUserClientDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用户客户关联表服务类
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
public interface HrUserClientService extends IService<HrUserClient> {


    /**
     * 创建用户客户关联表
     * @param hrUserClientDTO
     * @return
     */
    String createHrUserClient(HrUserClientDTO hrUserClientDTO);

    /**
     * 修改用户客户关联表
     * @param hrUserClientDTO
     * @return
     */
    Optional<HrUserClientDTO> updateHrUserClient(HrUserClientDTO hrUserClientDTO);

    /**
     * 查询用户客户关联表详情
     * @param id
     * @return
     */
    HrUserClientDTO getHrUserClient(String id);

    /**
     * 删除用户客户关联表
     * @param id
     */
    void deleteHrUserClient(String id);

    /**
     * 批量删除用户客户关联表
     * @param ids
     */
    void deleteHrUserClient(List<String> ids);

    /**
     * 分页查询用户客户关联表
     * @param hrUserClientDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrUserClientDTO hrUserClientDTO,Long pageNumber,Long pageSize);
    }
