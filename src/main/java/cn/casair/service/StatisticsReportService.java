package cn.casair.service;

import cn.casair.dto.report.EmployeeChangeDataDTO;
import cn.casair.dto.report.IncomeComparisonDataDTO;
import cn.casair.dto.report.ReportQueryParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 统计报表服务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/26 9:25
 */
public interface StatisticsReportService {


    /**
     * 个税分类汇总-个人-导出
     *
     * @param reportQueryParam
     * @param response
     */
    void taxSummaryStaffExport(ReportQueryParam reportQueryParam, HttpServletResponse response);

    /**
     * 报表管理-个税分类汇总-个人
     *
     * @param reportQueryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map<String, Object> taxSummaryStaff(ReportQueryParam reportQueryParam, Long pageNumber, Long pageSize);

    /**
     * 个税分类汇总-企业-导出
     *
     * @param reportQueryParam
     * @param response
     */
    void taxSummaryClientExport(ReportQueryParam reportQueryParam, HttpServletResponse response);

    /**
     * 个税分类汇总-企业
     *
     * @param reportQueryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map<String, Object> taxSummaryClient(ReportQueryParam reportQueryParam, Long pageNumber, Long pageSize);

    /**
     * 报表管理-客户信息变动-详情-导出
     *
     * @param reportQueryParam
     * @param response
     * @return
     */
    void clientInfoChangeDetailExport(ReportQueryParam reportQueryParam, HttpServletResponse response);

    /**
     * 报表管理-客户信息变动-详情
     *
     * @param reportQueryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map<String, Object> clientInfoChangeDetail(ReportQueryParam reportQueryParam, Long pageNumber, Long pageSize);

    /**
     * 报表管理-客户信息变动-导出
     *
     * @param reportQueryParam
     * @param response
     * @return
     */
    void clientInfoChangeExport(ReportQueryParam reportQueryParam, HttpServletResponse response);

    /**
     * 报表管理-客户信息变动
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map<String, Object> clientInfoChange(ReportQueryParam queryParam, Long pageNumber, Long pageSize);

    /**
     * 营业数据汇总
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map getBusinessData(ReportQueryParam queryParam, Long pageNumber, Long pageSize);

    /**
     * 工作数据汇总
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map getWorkData(ReportQueryParam queryParam, Long pageNumber, Long pageSize);

    /**
     * 导出工作汇总表
     * @param queryParam
     * @param response
     * @param request
     */
    void exportWorkData(ReportQueryParam queryParam, HttpServletResponse response, HttpServletRequest request);

    /**
     * 导出营业数据汇总表
     * @param queryParam
     * @param response
     * @param request
     */
    void exportBusinessData(ReportQueryParam queryParam, HttpServletResponse response, HttpServletRequest request);


    /**
     * 人员增减变化
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map getEmployeeChangeData(ReportQueryParam queryParam, Long pageNumber, Long pageSize);

    /**
     * 人员增减变化 图表
     *
     * @param queryParam
     * @return
     */
    List<EmployeeChangeDataDTO> getEmployeeChangeList(ReportQueryParam queryParam);

    /**
     * 导出人员增减变化表
     *
     * @param queryParam
     * @param response
     * @param request
     */
    void exportEmployeeChangeData(ReportQueryParam queryParam, HttpServletResponse response);

    /**
     * 收入同期对比数据
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map getIncomeComparisonData(ReportQueryParam queryParam, Long pageNumber, Long pageSize);

    /**
     * 收入同期对比数据  图表
     *
     * @param queryParam
     * @return
     */
    List<IncomeComparisonDataDTO> getIncomeComparisonList(ReportQueryParam queryParam);

    /**
     * 导出收入同期对比数据
     *
     * @param queryParam
     * @param response
     */
    void exportIncomeComparisonData(ReportQueryParam queryParam, HttpServletResponse response);

    /**
     *预算执行控制数据
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map getBudgetControlData(ReportQueryParam queryParam, Long pageNumber, Long pageSize);

    /**
     * 导出预算执行控制数据
     *
     * @param queryParam
     * @param response
     */
    void exportBudgetControlData(ReportQueryParam queryParam, HttpServletResponse response);

    /**
     * 招聘数据统计
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map getRecruitmentData(ReportQueryParam queryParam, Long pageNumber, Long pageSize);

    /**
     * 导出招聘数据统计
     *
     * @param queryParam
     * @param response
     */
    void exportRecruitmentData(ReportQueryParam queryParam, HttpServletResponse response);

    /**
     * 分页招聘信息汇总表
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map getRecruitmentInfoData(ReportQueryParam queryParam, Long pageNumber, Long pageSize);

    /**
     * 导出招聘信息汇总表
     *
     * @param queryParam
     * @param response
     */
    void exportRecruitmentInfoData(ReportQueryParam queryParam, HttpServletResponse response);

    /**
     * 分页PRO业务样表
     *
     * @param queryParam
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Map getRecruitmentPROData(ReportQueryParam queryParam, Long pageNumber, Long pageSize);

    /**
     * 导出PRO业务样表
     *
     * @param queryParam
     * @param response
     */
    void exportRecruitmentPROData(ReportQueryParam queryParam, HttpServletResponse response);

}
