package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.excel.ExcelImportModel;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.HrClient;
import cn.casair.domain.NcCustomer;
import cn.casair.dto.nc.result.NccCustomerDTO;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrClientService;
import cn.casair.service.NcCustomerService;
import cn.casair.service.NcService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ncc客户对应关系
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NccClientComponent {
    @Value("${file.temp-path}")
    private String tempPath;
    private final HrAppendixService hrAppendixService;
    private final HrClientService hrClientService;
    private final NcCustomerService ncCustomerService;
    private final NcService ncService;

    public ImportResultDTO importNccClients(InputStream inputStream) {
        ExcelImportResult<NccClientRelationDTO> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, NccClientRelationDTO.class);

        Map<String, HrClient> clientMap = getClientMap();
        Map<String, NccCustomerDTO> nccClientMap = getNccClientMap();
        StringBuilder sbError = new StringBuilder();
        for (NccClientRelationDTO nccClientRelationDTO : result.getList()) {
            try {
                HrClient hrClient = clientMap.get(nccClientRelationDTO.getName());
                if (hrClient == null) {
                    throw new CommonException("系统中不存在该客户:" + nccClientRelationDTO.getName());
                }
                NccCustomerDTO nccCustomerDTO = nccClientMap.get(nccClientRelationDTO.getNccName());
                if (nccCustomerDTO == null) {
                    throw new CommonException("NCC系统中不存在该客户:" + nccClientRelationDTO.getNccName());
                }
                NcCustomer ncCustomer = new NcCustomer();
                ncCustomer.setClientId(hrClient.getId());
                ncCustomer.setNcCode(nccCustomerDTO.getCode());
                ncCustomer.setNcName(nccCustomerDTO.getName());
                ncCustomer.setCustsuptype(nccCustomerDTO.getCustsuptype());
                this.ncCustomerService.save(ncCustomer);
            } catch (Exception e) {
                log.error("导入异常:{}", e.getMessage());
                sbError.append(e.getMessage());
            } finally {
                nccClientRelationDTO.setErrorMsg(sbError.toString());
                sbError.delete(0, sbError.length());
            }
        }

        // 将返回数据返回前端
        try {
            ImportResultDTO importResult = ImportResultUtils.writeErrorFile("导入失败数据", NccClientRelationDTO.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);
            return importResult;
        } catch (IOException e) {
            throw new CommonException("导入失败" + e.getMessage());
        }
    }

    /**
     * 获取客户信息map
     * 如果有相同的客户名称，返回父级
     *
     * @return name - client
     */
    private Map<String, HrClient> getClientMap() {
        return hrClientService.list().stream().collect(Collectors.toMap(HrClient::getClientName, it -> it, (existing, replacement) -> {
            // 有重复的返回父级
            if ("0".equals(existing.getParentId()) || existing.getParentId() == null) {
                return existing;
            }
            if ("0".equals(replacement.getParentId()) || replacement.getParentId() == null) {
                return replacement;
            }
            return existing;
        }));
    }

    /**
     * 获取客商信息map
     *
     * @return
     */
    private Map<String, NccCustomerDTO> getNccClientMap() {
        return ncService.qryCustomer(null, null).stream().collect(Collectors.toMap(NccCustomerDTO::getName, it -> it));
    }

    @Data
    public static class NccClientRelationDTO extends ExcelImportModel implements Serializable {
        @Excel(name = "客户")
        private String name;
        @Excel(name = "客商")
        private String nccName;
    }

}
