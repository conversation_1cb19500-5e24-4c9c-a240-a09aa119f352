package cn.casair.repository;

import cn.casair.domain.HrDataModification;
import cn.casair.dto.HrDataModificationDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 资料修改数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
@Repository
public interface HrDataModificationRepository extends BaseMapper<HrDataModification> {

    /**
     * 资料修改查看
     *
     * @param id
     * @return
     */
    HrDataModificationDTO findById(String id);

    /**
     * 资料修改列表
     *
     * @param page
     * @param hrDataModificationDTO
     * @return
     */
    IPage<HrDataModificationDTO> findPage(Page<HrDataModification> page, @Param("param") HrDataModificationDTO hrDataModificationDTO);

    /**
     * @param hrDataModificationDTO
     * @return
     */
    List<HrDataModificationDTO> findList(@Param("param") HrDataModificationDTO hrDataModificationDTO);

}
