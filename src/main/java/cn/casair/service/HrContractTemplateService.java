package cn.casair.service;

import cn.casair.domain.HrAppendix;
import cn.casair.domain.HrContract;
import cn.casair.domain.HrContractAppendix;
import cn.casair.domain.HrContractTemplate;
import cn.casair.dto.HrContractTemplateDTO;
import cn.casair.dto.formdata.FormFieldDealDTO;
import cn.casair.dto.formdata.InputLabel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 合同模板服务类
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
public interface HrContractTemplateService extends IService<HrContractTemplate> {

    /**
     * 合同模板使用次数+1
     *
     * @param templateId
     * @return int
     * <AUTHOR>
     * @date 2021/9/28
     **/
    int usageCountPlus(String templateId);

    /**
     * 生成劳动合同模板
     *
     * @param hrContractAppendix
     * @return void
     * <AUTHOR>
     * @date 2021/9/9
     **/
    HrAppendix makeTemplate(HrContractAppendix hrContractAppendix);

    /**
     * 一键生成劳动合同模板
     *
     * @param params
     * @return void
     * <AUTHOR>
     * @date 2021/9/9
     **/
    HrAppendix makeTemplate(Map<String, Object> params);

    /**
     * 获取合同模板列表 不分页
     *
     * @return java.util.List<cn.casair.dto.HrContractTemplateDTO>
     * <AUTHOR>
     * @date 2021/9/6
     **/
    List<HrContractTemplateDTO> getContractTemplateList();

    /**
     * 创建
     *
     * @param hrContractTemplateDTO
     * @return
     */
    HrContractTemplateDTO createHrContractTemplate(HrContractTemplateDTO hrContractTemplateDTO);

    /**
     * 修改
     *
     * @param hrContractTemplateDTO
     * @return
     */
    Optional<HrContractTemplateDTO> updateHrContractTemplate(HrContractTemplateDTO hrContractTemplateDTO);

    /**
     * 查询详情
     *
     * @param hrContract            合同信息
     * @param hrContractTemplate    模板
     * @param contractStartDateFlag 合同开始日期编辑允许编辑标识
     * @return
     */
    FormFieldDealDTO getHrContractTemplate(HrContract hrContract, HrContractTemplate hrContractTemplate, boolean contractStartDateFlag);

    /**
     * 删除
     *
     * @param id
     */
    void deleteHrContractTemplate(String id);

    /**
     * 批量删除
     *
     * @param ids
     */
    void deleteHrContractTemplate(List<String> ids);

    /**
     * 分页查询
     *
     * @param hrContractTemplateDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrContractTemplateDTO hrContractTemplateDTO, Long pageNumber, Long pageSize);

    /**
     * 批量下载合同模板
     * @param hrContractTemplateDTO
     * @return
     */
    List<HrContractTemplateDTO> downloadContractTemplatesBatch(HrContractTemplateDTO hrContractTemplateDTO);

    /**
     * 批量续签处理合同模板
     * @param clientNames
     * @param hrContractTemplate
     * @return
     */
    FormFieldDealDTO getRenewalContractTemplate(List<String> clientNames, HrContractTemplate hrContractTemplate);

    /**
     * 处理表单域到MAP
     * @param templateMap
     * @param labels
     */
    void handleInputLabelMap(Map<String, Object> templateMap,List<InputLabel> labels);

}
