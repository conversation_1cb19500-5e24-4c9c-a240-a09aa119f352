package cn.casair.ncc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * NCC客户传输对象（DTO）
 * 用于接收 NCC 客户查询接口返回的数据
 */
@Data
public class NccCustomerDTO {

    /**
     * 客商编码
     * 示例值：B01CFBFF-346B-407B-B1E4-842E410250CA
     */
    @JsonProperty("code")
    private String code;

    /**
     * 客商主键
     * 示例值：10011A1000000000FHVB
     */
    @JsonProperty("pk_cust_sup")
    private String pkCustSup;

    /**
     * 客商名称
     * 示例值：蔡春淑
     */
    @JsonProperty("name")
    private String name;

    /**
     * 客商类型
     * 示例值：1
     * 说明：1:客户;2：供应商;3:客商;
     */
    @JsonProperty("custsup_type")
    private String custsupType;

    /**
     * 客户税号
     * 示例值：222423197602146623
     */
    @JsonProperty("taxpayerid")
    private String taxpayerid;

    /**
     * 组织编码
     * 示例值：001
     */
    @JsonProperty("org_code")
    private String orgCode;

    /**
     * 集团编码
     * 示例值：00
     */
    @JsonProperty("group_code")
    private String groupCode;
}
