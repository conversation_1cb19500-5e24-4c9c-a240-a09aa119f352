<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillContentRecordRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, bill_id, bill_detail_id, bill_total_id, business_type, oper_detail, oper_name, is_delete, created_by, last_modified_by, created_date, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillContentRecord">
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="bill_id" property="billId"/>
        <result column="bill_detail_id" property="billDetailId"/>
        <result column="bill_total_id" property="billTotalId"/>
        <result column="business_type" property="businessType"/>
        <result column="oper_detail" property="operDetail"/>
        <result column="oper_name" property="operName"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Find_SQL">
        SELECT
        ANY_VALUE(hbcr.bill_id) bill_id,
        ANY_VALUE(hb.client_id) client_id,
        ANY_VALUE(hc.client_name) client_name,
        CONCAT(ANY_VALUE(hb.pay_year), '-', ANY_VALUE(hb.pay_monthly)) AS payment_date,
        ANY_VALUE(hb.bill_type) bill_type,
        ANY_VALUE(hb.title) title,
        COUNT(*) num
        FROM hr_bill_content_record hbcr
        LEFT JOIN hr_bill hb ON hbcr.bill_id = hb.id
        LEFT JOIN hr_client hc ON hc.id = hb.client_id
        WHERE hbcr.is_delete = 0
        AND hb.is_delete = 0
        AND hb.is_official = 1
        <if test="params.clientIdList!=null and params.clientIdList.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.ids!=null and params.ids.size()>0">
            AND hbcr.bill_id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.payYear!=null">
            AND hb.pay_year = #{params.payYear}
        </if>
        <if test="params.payMonthly!=null">
            AND hb.pay_monthly = #{params.payMonthly}
        </if>
        <if test="params.billType!=null">
            AND hb.bill_type = #{params.billType}
        </if>
        <if test="params.clientId!=null">
            AND hb.client_id = #{params.clientId}
        </if>
        <if test="params.title!=null and params.title!=''">
            AND hb.title LIKE CONCAT('%', #{params.title}, '%')
        </if>
        GROUP BY hbcr.bill_id
        ORDER BY hb.pay_year DESC,hb.pay_monthly DESC
    </sql>

    <select id="findPage" resultType="cn.casair.dto.HrBillContentRecordDTO">
        <include refid="Find_SQL"/>
    </select>
    <select id="findRecordPage" resultType="cn.casair.dto.HrBillContentRecordDTO">
        SELECT
        IF(hbcr.type=1,'账单汇总',hbd.name) name,
        hbd.certificate_num,
        hbcr.business_type,
        hbcr.oper_detail,
        hbcr.created_by,
        hbcr.oper_name,
        hbcr.created_date
        FROM hr_bill_content_record hbcr
        LEFT JOIN hr_bill_detail hbd ON hbcr.bill_detail_id = hbd.id
        WHERE hbcr.is_delete = 0
        <if test="params.billId!=null and params.billId!=''">
            AND hbcr.bill_id = #{params.billId}
        </if>
        ORDER BY hbcr.created_date desc
    </select>
    <select id="findList" resultType="cn.casair.dto.HrBillContentRecordDTO">
        SELECT
        IF(hbcr.type=1,'账单汇总',hbd.name) name,
        hbd.certificate_num,
        hbcr.business_type,
        hbcr.oper_detail,
        hbcr.created_by,
        hbcr.oper_name,
        hbcr.created_date
        FROM hr_bill_content_record hbcr
        LEFT JOIN hr_bill_detail hbd ON hbcr.bill_detail_id = hbd.id
        WHERE hbcr.is_delete = 0
        <if test="params.billId!=null and params.billId!=''">
            AND hbcr.bill_id = #{params.billId}
        </if>
        ORDER BY hbcr.created_date desc
    </select>

    <select id="selectListBatch" resultType="cn.casair.dto.HrBillContentRecordDTO">
        <include refid="Find_SQL"/>
    </select>

</mapper>
