package cn.casair.service;

import cn.casair.domain.HrRecruitmentStation;
import cn.casair.dto.HrRecruitmentStationDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 招聘岗位表服务类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
public interface HrRecruitmentStationService extends IService<HrRecruitmentStation> {


    /**
     * 创建招聘岗位表
     * @param hrRecruitmentStationDTO
     * @return
     */
    HrRecruitmentStationDTO createHrRecruitmentStation(HrRecruitmentStationDTO hrRecruitmentStationDTO);

    /**
     * 查询招聘岗位表详情
     * @param id
     * @return
     */
    HrRecruitmentStationDTO getHrRecruitmentStation(String id);

    /**
     * 删除招聘岗位表
     * @param id
     */
    void deleteHrRecruitmentStation(String id);

    /**
     * 批量删除招聘岗位表
     * @param ids
     */
    void deleteHrRecruitmentStation(List<String> ids);

    /**
     * 分页查询招聘岗位表
     * @param hrRecruitmentStationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRecruitmentStationDTO hrRecruitmentStationDTO,Long pageNumber,Long pageSize);

    /**
     * 查询
     * @param serviceId
     * @return
     */
    List<HrRecruitmentStationDTO> listByServiceId(String serviceId);
}
