package cn.casair.repository;

import cn.casair.domain.HrRemoteMedicalRecord;
import cn.casair.dto.HrRemoteMedicalRecordDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 异地医疗备案记录数据库操作类
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Repository
public interface HrRemoteMedicalRecordRepository extends BaseMapper<HrRemoteMedicalRecord> {

    HrRemoteMedicalRecordDTO findById(String id);

    IPage<HrRemoteMedicalRecordDTO> findPage(Page<HrRemoteMedicalRecord> page, @Param("param") HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO, @Param("permissionClient") List<String> clientIds);

    List<HrRemoteMedicalRecordDTO> findList(@Param("staffId") String staffId);

    List<HrRemoteMedicalRecordDTO> findListByDTO(@Param("param") HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO, @Param("permissionClient") List<String> clientIds);

}
