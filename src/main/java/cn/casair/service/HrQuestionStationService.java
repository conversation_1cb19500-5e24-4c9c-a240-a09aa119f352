package cn.casair.service;
import cn.casair.domain.HrQuestionStation;
import cn.casair.dto.HrQuestionStationDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 岗位题库关联表服务类
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
public interface HrQuestionStationService extends IService<HrQuestionStation> {


    /**
     * 创建岗位题库关联表
     * @param hrQuestionStationDTO
     * @return
     */
    HrQuestionStationDTO createHrQuestionStation(HrQuestionStationDTO hrQuestionStationDTO);

    /**
     * 修改岗位题库关联表
     * @param hrQuestionStationDTO
     * @return
     */
    Optional<HrQuestionStationDTO> updateHrQuestionStation(HrQuestionStationDTO hrQuestionStationDTO);

    /**
     * 查询岗位题库关联表详情
     * @param id
     * @return
     */
    HrQuestionStationDTO getHrQuestionStation(String id);

    /**
     * 删除岗位题库关联表
     * @param id
     */
    void deleteHrQuestionStation(String id);

    /**
     * 批量删除岗位题库关联表
     * @param ids
     */
    void deleteHrQuestionStation(List<String> ids);

    /**
     * 分页查询岗位题库关联表
     * @param hrQuestionStationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrQuestionStationDTO hrQuestionStationDTO,Long pageNumber,Long pageSize);
    }
