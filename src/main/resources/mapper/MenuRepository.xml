<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.MenuRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, pid, title, url, menutype, icon, has_data_authority, table_name, owner_type, owner_field, owner_id,
        is_delete, created_by, last_modified_by, created_date, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.Menu">
        <id column="id" property="id"/>
        <result column="pid" property="pid"/>
        <result column="title" property="title"/>
        <result column="permission" property="permission"/>
        <result column="menu_type" property="menuType"/>
        <result column="sort" property="sort"/>
        <result column="icon" property="icon"/>
        <result column="has_data_authority" property="hasDataAuthority"/>
        <result column="table_name" property="tableName"/>
        <result column="owner_type" property="ownerType"/>
        <result column="owner_field" property="ownerField"/>
        <result column="owner_id" property="ownerId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findListByAdminUserId" resultType="cn.casair.dto.MenuDTO">
        SELECT menu.*
        FROM sys_menu menu
                 LEFT JOIN sys_role_menu roleMenu ON menu.id = roleMenu.menu_id and roleMenu.is_delete = 0
                 LEFT JOIN sys_user_role userRole ON userRole.role_id = roleMenu.`role_id` and userRole.is_delete = 0
        WHERE userRole.`user_id` = #{userId}
          AND menu.`menu_type` = 1
          AND menu.`is_delete` = 0
        ORDER BY menu.sort
    </select>

    <select id="findListByRoleId" resultType="cn.casair.dto.MenuDTO">
        SELECT m.*,
               IF(rm.id IS NOT NULL, TRUE, FALSE) choose
        FROM sys_menu m
                 LEFT JOIN sys_role_menu rm ON m.id = rm.menu_id AND rm.role_id = #{roleId} and rm.is_delete = 0
        WHERE m.is_delete = 0
        ORDER BY m.sort

    </select>

    <select id="findButtonListByAdminUserId" resultType="string">
        SELECT DISTINCT menu.`permission` permission
        FROM sys_menu menu
                 LEFT JOIN sys_role_menu roleMenu ON menu.id = roleMenu.menu_id and roleMenu.is_delete = 0
                 LEFT JOIN sys_user_role userRole ON userRole.role_id = roleMenu.`role_id` and userRole.is_delete = 0
                 LEFT JOIN sys_menu pmenu ON menu.`pid` = pmenu.`id` and pmenu.is_delete = 0
        WHERE userRole.`user_id` = #{userId}
          AND menu.`menu_type` = 2
          AND menu.`is_delete` = 0
    </select>
</mapper>
