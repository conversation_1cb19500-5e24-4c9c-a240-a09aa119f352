package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.export.ExcelExportService;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.common.utils.excel.CellItem;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.HrBillCompareHeaderDTO.ComparePart;
import cn.casair.dto.billl.BillExcelDataDTO;
import cn.casair.dto.billl.BillFieldInfoDTO;
import cn.casair.mapper.HrBillCompareConfigMapper;
import cn.casair.mapper.HrBillCompareResultDetailMapper;
import cn.casair.mapper.HrBillCompareResultMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 对账配置信息表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrBillCompareConfigServiceImpl extends ServiceImpl<HrBillCompareConfigRepository, HrBillCompareConfig> implements HrBillCompareConfigService {

    private static final String FIELD_DIFF = "diffFields";
    /**
     * 数据类型 1 系统数据, 2 excel导入的数据
     */
    private static final String FIELD_TYPE = "dataType";
    /**
     * 是否存在比较数据, true是, false 否
     */
    private static final String FIELD_HAS_COMPARE_DATA = "hasCompareData";

    // 系统数据
    private static final String DATA_TYPE_SYSTEM = "1";
    // excel数据
    private static final String DATA_TYPE_EXCEL = "2";

    private static final String HARBIN_CENTER = "哈尔滨";
    private static final String JINZHOU_CENTER = "锦州";
    private static final String SHENYANG_CENTER = "沈阳";
    private static final String CHANGCHUN_CENTER = "长春";
    private static final String ZHENGZHOU_CENTER = "郑州";

    /**
     * 个税导盘专用字段,是否存在上个月的专项扣除数据
     */
    private static final String EXIST_LAST_MONTH_TAX_DEDCTIOIN = "elmtd";

    private final HrBillCompareResultDetailService hrBillCompareResultDetailService;
    private final HrBillCompareConfigRepository hrBillCompareConfigRepository;
    private final HrBillCompareConfigMapper hrBillCompareConfigMapper;
    private final HrBillDetailRepository hrBillDetailRepository;
    private final HrBillCompareResultRepository hrBillCompareResultRepository;
    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final HrBillRepository hrBillRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrBillCompareResultMapper hrBillCompareResultMapper;
    private final HrWelfareCompensationRecordRepository welfareCompensationRecordRepository;
    private final HrBillCompareResultDetailRepository hrBillCompareResultDetailRepository;
    private final HrBillCompareResultDetailMapper hrBillCompareResultDetailMapper;
    private final HrClientService hrClientService;
    private final HrClientRepository hrClientRepository;
    private final HrBillCompareResultService hrBillCompareResultService;
    private final HrAppendixService hrAppendixService;
    private final HrWelfareCompensationRecordService hrWelfareCompensationRecordService;
    private final HrWelfareCompensationService hrWelfareCompensationService;
    private final HrBillReimbursementApplyService hrBillReimbursementApplyService;

    @Value("${file.temp-path}")
    private String tempPath;
    // 对账标记的客户，下级生成报销时需合并
    @Value("${nc.markedClientIds}")
    private String markedClientIds;
    /**
     * 公积金:
     * 比较项:姓名,身份证,公积金基数, 单位缴纳金额, 个人缴纳金额, 公积金总金额
     * <p>
     * 社保:
     * 比较项: 姓名,身份证,单位/个人(养老, 失业)
     * <p>
     * 医保:
     * 比较项: 姓名,身份证,单位/个人(医疗, 工伤)
     * <p>
     * 个税:
     * 比较项: 姓名,身份证,个税(即个人实际应缴纳的税额)
     *
     * @param billCheckType
     * @return
     */
    @Override
    public List<EnumDTO> getExpenseTypeItem(Integer billCheckType) {
        if (billCheckType == null) {
            throw new CommonException("请选择账单核对类型！");
        }
        List<DynamicFeeTypesEnum> typesEnums = this.getDynamicFeeTypesByType(billCheckType);
        List<EnumDTO> enumDTOS = new ArrayList<>();

        typesEnums.forEach(t -> {
            EnumDTO enumDTO = new EnumDTO()
                .setKey(t.getKey())
                .setValue(t.getValue());
            enumDTOS.add(enumDTO);
        });

        return enumDTOS;
    }

    /**
     * 根据导盘类型获取比较项
     *
     * @param billCheckType 导盘类型
     * @return
     */
    public List<DynamicFeeTypesEnum> getDynamicFeeTypesByType(Integer billCheckType) {
        BillEnum.GuidePlateType guidePlateType = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, billCheckType);
        List<DynamicFeeTypesEnum> list = new LinkedList<>();
        list.add(DynamicFeeTypesEnum.NAME);
        list.add(DynamicFeeTypesEnum.ID_CARD);
        switch (guidePlateType) {
            // 社保:社保基数,单位/个人(养老,失业), 单位工伤
            case SOCIAL_SECURITY_GUIDE:
                list.addAll(
                    Arrays.asList(
                        // DynamicFeeTypesEnum.SOCIAL_SECURITY_BASE,
                        DynamicFeeTypesEnum.UNIT_NO,
                        DynamicFeeTypesEnum.UNIT_PENSION,
                        DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT,
                        DynamicFeeTypesEnum.UNIT_INJURY,
                        DynamicFeeTypesEnum.UNIT_SOCIAL_SECURITY_MAKE_UP,
                        DynamicFeeTypesEnum.PERSONAL_PENSION,
                        DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT,
                        DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MAKE_UP,
                        DynamicFeeTypesEnum.UNIT_LATE_FEE
                    )
                );
                break;
            // 医保: 医保基数, 单位/个人(医疗), 单位工伤
            case MEDICAL_GUIDE:
                list.addAll(
                    Arrays.asList(
                        // DynamicFeeTypesEnum.MEDICAL_BASE,
                        DynamicFeeTypesEnum.UNIT_NO,
                        DynamicFeeTypesEnum.UNIT_MEDICAL,
                        DynamicFeeTypesEnum.UNIT_MATERNITY,
                        DynamicFeeTypesEnum.PERSONAL_MEDICAL,
                        DynamicFeeTypesEnum.PERSONAL_MATERNITY,
                        DynamicFeeTypesEnum.UNIT_LATE_FEE
                    )
                );
                break;
            // 公积金: 公积金基数, 单位/个人缴纳金额, 公积金总金额
            case ACCUMULATION_FUND_GUIDE:
                list.addAll(
                    Arrays.asList(
                        // DynamicFeeTypesEnum.ACCUMULATION_FUND_BASE,
                        DynamicFeeTypesEnum.UNIT_NO,
                        DynamicFeeTypesEnum.UNIT_ACCUMULATION_FUND,
                        DynamicFeeTypesEnum.PERSONAL_ACCUMULATION_FUND,
                        DynamicFeeTypesEnum.TOTAL_ACCUMULATION_FUND
                    )
                );
                break;
            // 个税: 个税
            case PERSONAL_TAX_GUIDE:
                list.add(DynamicFeeTypesEnum.PERSONAL_TAX);
                break;
            case THIRD_PARTY_BILLS:
                // 添加所有的枚举
                list = new ArrayList<>();
                List<DynamicFeeTypesEnum> dynamicFeeTypesEnums = Arrays.asList(
                    DynamicFeeTypesEnum.FEE_TYPE_ADD,
                    DynamicFeeTypesEnum.FEE_TYPE_REDUCE,
                    DynamicFeeTypesEnum.FEE_TYPE_OTHER
                );
                for (DynamicFeeTypesEnum typesEnum : DynamicFeeTypesEnum.values()) {
                    if (!dynamicFeeTypesEnums.contains(typesEnum)) {
                        list.add(typesEnum);
                    }
                }
                break;
            case SPECIAL_CLIENT_BILL:
                list.addAll(
                    Arrays.asList(
                        DynamicFeeTypesEnum.CENTER_NAME,
                        DynamicFeeTypesEnum.UNIT_PENSION_CARDINAL,
                        DynamicFeeTypesEnum.PERSONAL_PENSION_CARDINAL,
                        DynamicFeeTypesEnum.UNIT_PENSION,
                        DynamicFeeTypesEnum.PERSONAL_PENSION,
                        DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL,
                        DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL_PERSONAL,
                        DynamicFeeTypesEnum.UNIT_MEDICAL,
                        DynamicFeeTypesEnum.PERSONAL_MEDICAL,
                        DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT_CARDINAL,
                        DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT_CARDINAL,
                        DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT,
                        DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT,
                        DynamicFeeTypesEnum.WORK_INJURY_CARDINAL,
                        DynamicFeeTypesEnum.PERSONAL_INJURY_CARDINAL,
                        DynamicFeeTypesEnum.UNIT_INJURY,
                        DynamicFeeTypesEnum.PERSONAL_INJURY,
                        DynamicFeeTypesEnum.UNIT_MATERNITY_CARDINAL,
                        DynamicFeeTypesEnum.PERSONAL_MATERNITY_CARDINAL,
                        DynamicFeeTypesEnum.UNIT_MATERNITY,
                        DynamicFeeTypesEnum.PERSONAL_MATERNITY,
                        DynamicFeeTypesEnum.ACCUMULATION_FUND_BASE,
                        DynamicFeeTypesEnum.UNIT_ACCUMULATION_FUND,
                        DynamicFeeTypesEnum.PERSONAL_ACCUMULATION_FUND
                    )
                );
                break;
            default:
                throw new CommonException("未知的账单核对类型！");
        }
        return list;
    }

    /**
     * 创建对账配置信息表
     *
     * @param hrBillCompareConfigDTO
     * @return
     */
    @Override
    public HrBillCompareConfigDTO createHrBillCompareConfig(HrBillCompareConfigDTO hrBillCompareConfigDTO) {
        log.info("Create new HrBillCompareConfig:{}", hrBillCompareConfigDTO);

        HrBillCompareConfig hrBillCompareConfig = this.hrBillCompareConfigMapper.toEntity(hrBillCompareConfigDTO);
        this.hrBillCompareConfigRepository.insert(hrBillCompareConfig);
        return this.hrBillCompareConfigMapper.toDto(hrBillCompareConfig);
    }

    /**
     * 修改对账配置信息表
     *
     * @param hrBillCompareConfigDTO
     * @return
     */
    @Override
    public Optional<HrBillCompareConfigDTO> updateHrBillCompareConfig(HrBillCompareConfigDTO hrBillCompareConfigDTO) {
        return Optional.ofNullable(this.hrBillCompareConfigRepository.selectById(hrBillCompareConfigDTO.getId()))
            .map(roleTemp -> {
                HrBillCompareConfig hrBillCompareConfig = this.hrBillCompareConfigMapper.toEntity(hrBillCompareConfigDTO);
                this.hrBillCompareConfigRepository.updateById(hrBillCompareConfig);
                log.info("Update HrBillCompareConfig:{}", hrBillCompareConfigDTO);
                return hrBillCompareConfigDTO;
            });
    }

    /**
     * 查询对账配置信息表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillCompareConfigDTO getHrBillCompareConfig(String id) {
        HrBillCompareConfig hrBillCompareConfig = this.hrBillCompareConfigRepository.selectById(id);
        HrBillCompareConfigDTO hrBillCompareConfigDTO = this.hrBillCompareConfigMapper.toDto(hrBillCompareConfig);
        HrBillCompareResultDTO billCompareResultDTO = this.hrBillCompareResultRepository.getByBillConfigId(hrBillCompareConfigDTO.getId());
        // 获取对账结果
        List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailList = this.hrBillCompareResultDetailRepository.selectHrBillCompareResultDetailByBillCompareResultId(billCompareResultDTO.getId());
        // 制作动态表头
        List<DynamicHeadersDTO> dynamicHeaders;
        if (hrBillCompareConfig.getType().equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            dynamicHeaders = this.makeDynamicHeaders(hrBillCompareResultDetailList);
        } else {
            dynamicHeaders = this.hrBillCompareResultDetailService.makeDynamicHeaders(hrBillCompareResultDetailList, hrBillCompareConfig.getType());
        }
        // 制作数据列表
        List<Map<String, Object>> dynamicData = this.makeDynamicData(hrBillCompareResultDetailList, dynamicHeaders, hrBillCompareConfig.getType());
        hrBillCompareConfigDTO.setDynamicHeaders(dynamicHeaders);
        hrBillCompareConfigDTO.setHrBillCompareResultDetailDTOList(dynamicData);
        hrBillCompareConfigDTO.setBillResultId(billCompareResultDTO.getId());
        hrBillCompareConfigDTO.setStatus(billCompareResultDTO.getStatus());
        hrBillCompareConfigDTO.setLockStatus(billCompareResultDTO.getLockStatus());
        return hrBillCompareConfigDTO;
    }

    /**
     * 删除对账配置信息表
     *
     * @param id
     */
    @Override
    public void deleteHrBillCompareConfig(String id) {
        Optional.ofNullable(this.hrBillCompareConfigRepository.selectById(id))
            .ifPresent(hrBillCompareConfig -> {
                List<HrBillCompareResult> hrBillCompareResultList = hrBillCompareResultRepository.selectList(new QueryWrapper<HrBillCompareResult>().eq("bill_compare_config_id", id));
                List<String> resultIds = hrBillCompareResultList.stream().map(HrBillCompareResult::getId).collect(Collectors.toList());
                this.hrBillCompareConfigRepository.deleteById(id);

                // 删除对账结果
                this.hrBillCompareResultRepository.delByBillCompareConfigId(id);
                //删除对账结果明细
                hrBillCompareResultDetailRepository.delByResultId(resultIds);

                log.info("Delete HrBillCompareConfig:{}", hrBillCompareConfig);
            });
    }

    /**
     * 批量删除对账配置信息表--批量个税对账
     *
     * @param ids
     */
    @Override
    public void deleteHrBillCompareConfig(List<String> ids) {
        log.info("取消批量对账:{}", ids);
        List<HrBillCompareConfig> hrBillCompareConfigs = hrBillCompareConfigRepository.selectBatchIds(ids);
        //删除对账结果明细
        if (CollectionUtils.isNotEmpty(hrBillCompareConfigs)) {
            //批量个税对账
            //取消对账删除本条记录恢复上一次记录
            List<String> delConfigIds = hrBillCompareConfigs.stream().filter(ls -> ls.getParentId() != null).map(HrBillCompareConfig::getParentId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delConfigIds)) {
                this.hrBillCompareConfigRepository.delByBillIdAndType(null, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey(), 0, delConfigIds);
                this.hrBillCompareResultRepository.delByBillIdAndType(null, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey(), 0, delConfigIds);
                this.hrWelfareCompensationRepository.delByBillIdAndIsUsed(null, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey(), 0, delConfigIds);
            }
            this.hrBillCompareConfigRepository.delByBillIdAndType(null, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey(), 1, ids);
            this.hrBillCompareResultRepository.delByBillIdAndType(null, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey(), 1, ids);
            this.hrWelfareCompensationRepository.delByBillIdAndIsUsed(null, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey(), 1, ids);
        }
        //海尔对账
        else {
            List<HrBillCompareResult> hrBillCompareResultList = hrBillCompareResultRepository.selectList(new QueryWrapper<HrBillCompareResult>().in("bill_compare_config_id", ids));
            List<String> resultIds = hrBillCompareResultList.stream().map(HrBillCompareResult::getId).collect(Collectors.toList());
            hrBillCompareResultDetailRepository.delByResultId(resultIds);
            hrBillCompareResultRepository.deleteBatchIds(resultIds);
        }
    }

    /**
     * 分页查询对账配置信息表
     *
     * @param hrBillCompareConfigDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillCompareConfigDTO hrBillCompareConfigDTO, Long pageNumber, Long pageSize) {
        Page<HrBillCompareConfig> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrBillCompareConfig> qw = new QueryWrapper<>(this.hrBillCompareConfigMapper.toEntity(hrBillCompareConfigDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrBillCompareConfigRepository.selectPage(page, qw);
        iPage.setRecords(this.hrBillCompareConfigMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    /**
     * 获取对账结果的表头
     *
     * @param configId 对账配置id
     * @return
     */
    @Override
    public List<CellItem> getDynamicHeader(String configId) {
        List<CellItem> result = new ArrayList<>();

        // 获取配置信息
        HrBillCompareConfigDTO configDTO = this.hrBillCompareConfigRepository.findById(configId);

        // 获取字段解析对象
        BillFieldInfoDTO billFieldInfoDTO = configDTO.getBillFieldInfoDTO().orElseThrow(() -> new CommonException("解析配置信息异常!"));

        // 获取已映射的字段
        List<HrExpenseManageDTO> mappingFields = billFieldInfoDTO.getMappingFields();
        if (mappingFields == null || mappingFields.isEmpty()) {
            return result;
        }
        boolean isTaxGuide = BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey().equals(configDTO.getType());

        // 已映射的字段转表头
        for (HrExpenseManageDTO mf : mappingFields) {
            String expenseType = mf.getExpenseType();
            DynamicFeeTypesEnum typesEnum = EnumUtils.getEnumByKey(DynamicFeeTypesEnum.class, expenseType);
            if (typesEnum == null) {
                continue;
            }
            //处理个人工伤
            if (typesEnum.equals(DynamicFeeTypesEnum.PERSONAL_INJURY) || typesEnum.equals(DynamicFeeTypesEnum.CENTER_NAME) || typesEnum.equals(DynamicFeeTypesEnum.UNIT_NO)) {
                continue;
            }
            String value = mf.getCellOriginName();
            // 个税导盘
            if (isTaxGuide) {
                if (DynamicFeeTypesEnum.PERSONAL_TAX.getKey().equals(expenseType)) {
                    value = "本期应扣缴税额";
                }
            }
            // 根据字段类型获取key
            CellItem cellItem = new CellItem()
                .setValue(value)
                .setKey(typesEnum.getFieldName())
                .setSqlField(typesEnum.getSqlField())
                .setFirstCol(mf.getColIndex())
                .setLastCol(mf.getColIndex());
            result.add(cellItem);
        }

        // 单独处理个税导盘
        if (isTaxGuide) {
            CellItem cellItem = new CellItem()
                .setValue("存在上月专项扣除")
                .setKey(EXIST_LAST_MONTH_TAX_DEDCTIOIN)
                .setSqlField("\"\"");
            result.add(cellItem);
        }

        return result;
    }

    /**
     * 生成对账结果
     *
     * @param hrBillCompareConfigDTO
     * @return
     */
    @Override
    public HrBillCompareConfigDTO generateBillCompareResult(HrBillCompareConfigDTO hrBillCompareConfigDTO) {
        // 校验对账结果是否已存在
        String configId = hrBillCompareConfigDTO.getConfigId();
        if (configId != null) {
            /*HrBillCompareResultDTO compareResultDTO = this.hrBillCompareResultRepository.getByBillConfigId(configId);
            if (compareResultDTO != null) {
                throw new CommonException("该对账结果已生成,请勿重复操作!");
            }*/
        } else {
            List<HrBillCompareResult> list = hrBillCompareResultRepository.selectList(new QueryWrapper<HrBillCompareResult>()
                .in("bill_compare_config_id", hrBillCompareConfigDTO.getBillConfigIds()));
            if (CollectionUtils.isNotEmpty(list)) {
                throw new CommonException("该对账结果已生成,请勿重复操作!");
            }
            configId = hrBillCompareConfigDTO.getBillConfigIds().get(0);
        }

        HrBillCompareConfigDTO hrBillCompareConfig = this.hrBillCompareConfigRepository.findById(configId);
        // 预检测,判断字段映射等配置是否合理
        this.preCheckWithCompare(hrBillCompareConfig);

        // 获取动态表头
        List<CellItem> cellItems = this.getDynamicHeader(configId);

        // 校验表头中必须要有身份证字段
        Optional<CellItem> first = cellItems.stream().filter(ls -> DynamicFeeTypesEnum.ID_CARD.getFieldName().equals(ls.getKey())).findFirst();
        if (!first.isPresent()) {
            throw new CommonException("对账失败,表单映射字段中必须存在[身份证]类型的字段!");
        }

        // 查询系统数据指定字段的数据
        List<String> billIds;
        if (hrBillCompareConfigDTO.getIdentification()) {
            QueryWrapper<HrBill> eq = new QueryWrapper<>();
            eq.eq("is_delete", 0);
            eq.eq("is_official", 1);
            eq.eq("bill_state", BillEnum.BillState.LOCKED.getKey());
            if (hrBillCompareConfigDTO.getType().equals(BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey())) {
                eq.eq("bill_type", BillEnum.BillType.SALARY_BILL.getKey());
            } else {
                eq.eq("bill_type", BillEnum.BillType.SECURITY_BILL.getKey());
            }
            if (hrBillCompareConfigDTO.getType().equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
                List<HrClientDTO> subordinateClient = hrClientRepository.getSubordinateClient(SpecialBillClient.HAIER.getKey());
                List<String> clientId = subordinateClient.stream().map(HrClientDTO::getId).collect(Collectors.toList());
                eq.in("client_id", clientId);
            }
            String[] split = hrBillCompareConfigDTO.getPaymentDate().split("-");
            eq.eq("pay_year", Integer.parseInt(split[0]));
            eq.eq("pay_monthly", Integer.parseInt(split[1]));
            List<HrBill> hrBills = hrBillRepository.selectList(eq);
            if (hrBills == null || hrBills.isEmpty()) {
                throw new CommonException("选择对账的费用年月中没有可以对账的账单！");
            }
            billIds = hrBills.stream().map(HrBill::getId).collect(Collectors.toList());
        } else {
            billIds = Collections.singletonList(hrBillCompareConfig.getBillId());
        }
        List<HrTalentStaffDTO> hrTalentStaffDTOS = this.hrTalentStaffRepository.findStaffBatchIds(null);
        Map<String, HrTalentStaffDTO> staffCollect = hrTalentStaffDTOS.stream().collect(Collectors.groupingBy(HrTalentStaffDTO::getCertificateNum, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));

        // 批量个税对账
        if (hrBillCompareConfigDTO.getIdentification() && hrBillCompareConfigDTO.getType().equals(BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey())) {
            // excel中获取指定列的数据
            BillFieldInfoDTO fieldInfoDTO = hrBillCompareConfig.getBillFieldInfoDTO().orElseThrow(() -> new CommonException("解析配置信息异常!"));
            Map<String, BillExcelDataDTO> excelDataDTOAllMap = BillParseUtils.parseExcel2MapData(fieldInfoDTO, hrBillCompareConfig.getDataStartRow(), hrBillCompareConfig.getOriginFileUrl(), hrBillCompareConfig.getOriginFileUrl(), 0);
            List<HrBillCompareConfig> configList = hrBillCompareConfigRepository.selectBatchIds(hrBillCompareConfigDTO.getBillConfigIds());
            List<HrBillCompareResultDetailDTO> compareResultList = new ArrayList<>();
            List<HrBillCompareResult> hrBillCompareResultList = new ArrayList();
            List<String> billConfigIds = new ArrayList<>();
            List<String> delConfigIds = new ArrayList<>();
            List<String> parentIds = configList.stream().filter(lst -> lst.getParentId() != null).map(HrBillCompareConfig::getParentId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(parentIds)) {
                delConfigIds.addAll(parentIds);
            }
            List<CellItem> cellItemsNew = new ArrayList<>(cellItems);
            cellItemsNew.add(new CellItem().setValue("缴费年").setKey("payYear").setSqlField("pay_year"));
            cellItemsNew.add(new CellItem().setValue("缴费月").setKey("payMonthly").setSqlField("pay_monthly"));
            cellItemsNew.add(new CellItem().setValue("员工编号").setKey("systemNum").setSqlField("system_num"));
            List<Map<String, Object>> systemDataList = this.hrBillDetailRepository.getListByDynamicFields(cellItemsNew, billIds);
            Map<Object, List<Map<String, Object>>> listMap = systemDataList.stream().collect(Collectors.groupingBy(x -> x.get("billId")));
            Map<Object, List<Map<String, Object>>> staffMap = systemDataList.stream().collect(Collectors.groupingBy(x -> x.get(DynamicFeeTypesEnum.ID_CARD.getFieldName())));
            for (String billId : billIds) {
                HrBillCompareConfig config = configList.stream().filter(lst -> lst.getBillId().equals(billId)).findAny().orElse(null);
                if (config == null) {
                    continue;
                }
                List<Map<String, Object>> systemDatas = listMap.get(billId);
                if (systemDatas == null || systemDatas.isEmpty()) {
                    delConfigIds.add(config.getId());
                    continue;
                }
                Map<String, BillExcelDataDTO> excelDataDTOMap = new HashMap<>();
                Map<String, Map<String, Object>> systemMapDatas = new HashMap<>(BillParseUtils.getInitCapacity(systemDatas.size()));
                for (Map<String, Object> map : systemDatas) {
                    String idCard = (String) map.get(DynamicFeeTypesEnum.ID_CARD.getFieldName());
                    BillExcelDataDTO billExcelDataDTO = excelDataDTOAllMap.get(idCard);
                    if (billExcelDataDTO != null) {
                        //处理同一月份存在员工存在多个薪酬账单个税计算
                        List<Map<String, Object>> mapList = staffMap.get(idCard);
                        if (mapList.size() > 1) {
                            String fieldName = DynamicFeeTypesEnum.PERSONAL_TAX.getFieldName();
                            BigDecimal decimal = BigDecimal.ZERO;
                            for (Map<String, Object> ls : mapList) {
                                Object personalTax = ls.get(fieldName);
                                decimal = CalculateUtils.objectAddition(personalTax, decimal);
                            }
                            map.put(fieldName, decimal);
                        }
                        systemMapDatas.put(idCard, map);
                        excelDataDTOMap.put(idCard, billExcelDataDTO);
                        //从导盘中去掉在系统中存在的数据；
                        excelDataDTOAllMap.remove(idCard);
                    }
                }
                if (CollectionUtils.isEmpty(excelDataDTOMap)) {
                    delConfigIds.add(config.getId());
                    continue;
                }
                // excel数据特殊处理
                this.handleExcelDataMap(excelDataDTOMap, hrBillCompareConfig, systemMapDatas);
                // 数据对比
                HrBillCompareResult compareResult = new HrBillCompareResult().setBillCompareConfigId(config.getId());
                List<HrBillCompareResultDetailDTO> HrBillCompareResultDetailDTOList = this.getCompareResult(systemMapDatas, excelDataDTOMap, hrBillCompareConfig.getType(), compareResult, 0, hrBillCompareConfigDTO, staffCollect);
                compareResultList.addAll(HrBillCompareResultDetailDTOList);
                // 保存对账结果
                compareResult.setStatus(0);
                hrBillCompareResultList.add(compareResult);
                billConfigIds.add(config.getId());
            }
            //删除上一次未应用的对账信息
            if (CollectionUtils.isNotEmpty(delConfigIds)) {
                this.hrBillCompareConfigRepository.delByBillIdAndType(null, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey(), 1, delConfigIds);
                this.hrBillCompareResultRepository.delByBillIdAndType(null, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey(), 1, delConfigIds);
                this.hrWelfareCompensationRepository.delByBillIdAndIsUsed(null, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey(), 1, delConfigIds);
            }
            if (hrBillCompareResultList == null || hrBillCompareResultList.isEmpty()) {
                throw new CommonException("对账导盘中不存在系统中的数据！");
            }
            hrBillCompareResultService.saveBatch(hrBillCompareResultList);
            // 制作动态表头
            List<DynamicHeadersDTO> dynamicHeaders = this.hrBillCompareResultDetailService.makeDynamicHeaders(compareResultList, hrBillCompareConfig.getType());
            // 制作数据列表
            List<Map<String, Object>> dynamicData = this.makeDynamicData(compareResultList, dynamicHeaders, hrBillCompareConfig.getType());
            // 获取对账详情
            HrBillCompareConfigDTO compareConfigDTO = this.hrBillCompareConfigRepository.getById(billConfigIds.get(0));
            compareConfigDTO.setHrBillCompareResultDetailDTOList(dynamicData);
            compareConfigDTO.setDynamicHeaders(dynamicHeaders);
            compareConfigDTO.setBillConfigIds(billConfigIds);
            compareConfigDTO.setStatus(0);
            //返回数据差异数据 excelDataDTOAllMap
            for (Map.Entry<String, BillExcelDataDTO> entry : excelDataDTOAllMap.entrySet()) {
                BillExcelDataDTO excelDataDTO = entry.getValue();
                String idCard = entry.getKey();
                HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO = new HrBillCompareResultDetailDTO();
                hrBillCompareResultDetailDTO.setIdNo(idCard).setStaffName(excelDataDTO.getName());
                HrTalentStaffDTO hrTalentStaffDTO = staffCollect.get(idCard);
                if (hrTalentStaffDTO != null) {
                    hrBillCompareResultDetailDTO.setStaffId(hrTalentStaffDTO.getId());
                    hrBillCompareResultDetailDTO.setUnitNumber(hrTalentStaffDTO.getUnitNumber());
                    hrBillCompareResultDetailDTO.setClientName(hrTalentStaffDTO.getClientName());
                    hrBillCompareResultDetailDTO.setArea(hrTalentStaffDTO.getSocialSecurityArea());
                    hrBillCompareResultDetailDTO.setSystemNum(hrTalentStaffDTO.getSystemNum());
                    hrBillCompareResultDetailDTO.setUsedType(4);
                } else {
                    hrBillCompareResultDetailDTO.setUsedType(3);
                }
                compareResultList.add(hrBillCompareResultDetailDTO);
            }
            Map<Integer, List<HrBillCompareResultDetailDTO>> mapList = compareResultList.stream().filter(lst -> lst.getUsedType() != null && lst.getUsedType() != 1).collect(Collectors.groupingBy(HrBillCompareResultDetailDTO::getUsedType));
            // 获取对账详情
            if (mapList != null && !mapList.isEmpty()) {
                compareConfigDTO.setAbnormalDetailMapList(mapList);
            }
            return compareConfigDTO;
        }
        //海尔对账结果
        else if (hrBillCompareConfigDTO.getIdentification() && hrBillCompareConfigDTO.getType().equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            // 获取员工的一些额外信息
            List<CellItem> cellItemsNew = new ArrayList<>(cellItems);
            cellItemsNew.add(new CellItem().setValue("缴保地").setKey("socialSecurityArea").setSqlField("social_security_area"));
            cellItemsNew.add(new CellItem().setValue("缴费年").setKey("payYear").setSqlField("pay_year"));
            cellItemsNew.add(new CellItem().setValue("缴费月").setKey("payMonthly").setSqlField("pay_monthly"));
            cellItemsNew.add(new CellItem().setValue("企业微调").setKey(DynamicFeeTypesEnum.UNIT_LATE_FEE.getFieldName()).setSqlField(DynamicFeeTypesEnum.UNIT_LATE_FEE.getSqlField()));

            cellItemsNew.add(new CellItem().setValue("企业微调").setKey(DynamicFeeTypesEnum.UNIT_LARGE_MEDICAL_EXPENSE_HAIER.getFieldName()).setSqlField(DynamicFeeTypesEnum.UNIT_LARGE_MEDICAL_EXPENSE.getSqlField()));
            cellItemsNew.add(new CellItem().setValue("个人微调").setKey(DynamicFeeTypesEnum.PERSONAL_LARGE_MEDICAL_EXPENSE_HAIER.getFieldName()).setSqlField(DynamicFeeTypesEnum.PERSONAL_LARGE_MEDICAL_EXPENSE.getSqlField()));
            List<Map<String, Object>> systemDatas = this.hrBillDetailRepository.getListByDynamicFields(cellItemsNew, billIds);
            Map<String, Map<String, Object>> systemMapDatas = new HashMap<>(BillParseUtils.getInitCapacity(systemDatas.size()));
            for (Map<String, Object> map : systemDatas) {
                String idCard = (String) map.get(DynamicFeeTypesEnum.ID_CARD.getFieldName());
                systemMapDatas.put(idCard, map);
            }
            // excel中获取指定列的数据
            BillFieldInfoDTO fieldInfoDTO = hrBillCompareConfig.getBillFieldInfoDTO().orElseThrow(() -> new CommonException("解析配置信息异常!"));
            Map<String, BillExcelDataDTO> excelDataDTOMap = BillParseUtils.parseExcel2MapData(fieldInfoDTO, hrBillCompareConfig.getDataStartRow(),
                hrBillCompareConfig.getOriginFileUrl(), hrBillCompareConfig.getOriginFileUrl(), 4);
            List<String> centerNames = excelDataDTOMap.values().stream().map(BillExcelDataDTO::getCenterName).distinct().collect(Collectors.toList());
            Set<String> centerNameList = new HashSet<>();
            centerNames.forEach(centerName -> {
                if (centerName.contains(HARBIN_CENTER)) {
                    centerNameList.add(HARBIN_CENTER);
                } else if (centerName.contains(JINZHOU_CENTER)) {
                    centerNameList.add(JINZHOU_CENTER);
                } else if (centerName.contains(SHENYANG_CENTER)) {
                    centerNameList.add(SHENYANG_CENTER);
                } else if (centerName.contains(CHANGCHUN_CENTER)) {
                    centerNameList.add(CHANGCHUN_CENTER);
                } else if (centerName.contains(ZHENGZHOU_CENTER)) {
                    centerNameList.add(ZHENGZHOU_CENTER);
                }
            });
            Set<String> idCard = excelDataDTOMap.keySet();
            //查询本月账单中未参与对账的员工信息
            List<HrBillCompareResultDetailDTO> detailDTOList = hrBillDetailRepository.findNotAccordStaff(new ArrayList<>(idCard), billIds);
            List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailDTOS = new ArrayList<>();
            List<String> configIds = new ArrayList<>();
            List<String> resultIds = new ArrayList<>();
            for (String centerName : centerNameList) {
                HrBillCompareConfig compareConfig = hrBillCompareConfigMapper.toEntity(hrBillCompareConfig);
                compareConfig.setId(RandomUtil.generateId())
                    .setCenterName(centerName)
                    .setType(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())
                    .setTitle(centerName + "中心" + hrBillCompareConfig.getPaymentDate() + "对账单");
                hrBillCompareConfigRepository.insert(compareConfig);
                configIds.add(compareConfig.getId());
                Map<String, BillExcelDataDTO> excelDataDTOMapNew = new HashMap<>();
                for (String idNo : excelDataDTOMap.keySet()) {
                    BillExcelDataDTO billExcelDataDTO = excelDataDTOMap.get(idNo);
                    if (billExcelDataDTO.getCenterName().contains(centerName)) {
                        excelDataDTOMapNew.put(idNo, billExcelDataDTO);
                    }
                }
                // 数据对比
                HrBillCompareResult compareResult = new HrBillCompareResult().setBillCompareConfigId(compareConfig.getId());
                List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailDTOList = this.getCompareResultSpecial(systemMapDatas, excelDataDTOMapNew, hrBillCompareConfig.getType(), compareResult, hrBillCompareConfigDTO.getPaymentDate(), detailDTOList, staffCollect);
                hrBillCompareResultDetailDTOS.addAll(hrBillCompareResultDetailDTOList);
                // 保存对账结果
                compareResult.setStatus(0);
                this.hrBillCompareResultRepository.insert(compareResult);
                resultIds.add(compareResult.getId());
            }
            // 制作动态表头
            List<DynamicHeadersDTO> dynamicHeaders = this.makeDynamicHeaders(hrBillCompareResultDetailDTOS);
            // 制作数据列表
            List<Map<String, Object>> dynamicData = this.makeDynamicData(hrBillCompareResultDetailDTOS, dynamicHeaders, hrBillCompareConfig.getType());
            // 获取对账详情
            HrBillCompareConfigDTO compareConfigDTO = this.hrBillCompareConfigRepository.findById(configId);
            compareConfigDTO.setHrBillCompareResultDetailDTOList(dynamicData);
            compareConfigDTO.setDynamicHeaders(dynamicHeaders);
            compareConfigDTO.setStatus(0);
            compareConfigDTO.setBillConfigIds(configIds);
            compareConfigDTO.setBillResultIds(resultIds);
            Map<Integer, List<HrBillCompareResultDetailDTO>> listMap = detailDTOList.stream().filter(lst -> lst.getUsedType() != null).collect(Collectors.groupingBy(HrBillCompareResultDetailDTO::getUsedType));
            if (listMap != null && !listMap.isEmpty()) {
                compareConfigDTO.setAbnormalDetailMapList(listMap);
            }
            return compareConfigDTO;
        } else {
            // 获取员工的一些额外信息
            List<CellItem> cellItemsNew = new ArrayList<>(cellItems);
            cellItemsNew.add(new CellItem().setValue("员工id").setKey("staffId").setSqlField("staff_id"));
            cellItemsNew.add(new CellItem().setValue("员工编号").setKey("systemNum").setSqlField("system_num"));
            cellItemsNew.add(new CellItem().setValue("缴费年").setKey("payYear").setSqlField("pay_year"));
            cellItemsNew.add(new CellItem().setValue("缴费月").setKey("payMonthly").setSqlField("pay_monthly"));
            List<Map<String, Object>> systemDatas = this.hrBillDetailRepository.getListByDynamicFields(cellItemsNew, billIds);
            Map<String, Map<String, Object>> systemMapDatas = new HashMap<>(BillParseUtils.getInitCapacity(systemDatas.size()));
            for (Map<String, Object> map : systemDatas) {
                String idCard = (String) map.get(DynamicFeeTypesEnum.ID_CARD.getFieldName());
                systemMapDatas.put(idCard, map);
            }

            // excel中获取指定列的数据
            BillFieldInfoDTO fieldInfoDTO = hrBillCompareConfig.getBillFieldInfoDTO().orElseThrow(() -> new CommonException("解析配置信息异常!"));
            Map<String, BillExcelDataDTO> excelDataMap = BillParseUtils.parseExcel2MapData(fieldInfoDTO, hrBillCompareConfig.getDataStartRow(), hrBillCompareConfig.getOriginFileUrl(), hrBillCompareConfig.getOriginFileUrl(), 0);

            // excel数据特殊处理
            this.handleExcelDataMap(excelDataMap, hrBillCompareConfig, systemMapDatas);

            // 数据对比
            HrBillCompareResultDTO compareResultDTO = hrBillCompareResultRepository.getByBillConfigId(configId);
            HrBillCompareResult compareResult = hrBillCompareResultMapper.toEntity(compareResultDTO);
            if (compareResult == null) {
                compareResult = new HrBillCompareResult().setBillCompareConfigId(configId).setStatus(0);
            }
            List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailList = this.getCompareResult(systemMapDatas, excelDataMap, hrBillCompareConfig.getType(), compareResult, 1, hrBillCompareConfigDTO, staffCollect);
            // 制作动态表头
            List<DynamicHeadersDTO> dynamicHeaders = this.hrBillCompareResultDetailService.makeDynamicHeaders(hrBillCompareResultDetailList, hrBillCompareConfig.getType());
            // 制作数据列表
            List<Map<String, Object>> dynamicData = this.makeDynamicData(hrBillCompareResultDetailList, dynamicHeaders, hrBillCompareConfig.getType());

            // 保存对账结果
            if (compareResultDTO == null) {
                this.hrBillCompareResultRepository.insert(compareResult);
            } else {
                this.hrBillCompareResultRepository.updateById(compareResult);
            }
            Map<Integer, List<HrBillCompareResultDetailDTO>> listMap = hrBillCompareResultDetailList.stream().filter(lst -> lst.getUsedType() != null && lst.getUsedType() != 1).collect(Collectors.groupingBy(HrBillCompareResultDetailDTO::getUsedType));
            // 获取对账详情
            HrBillCompareConfigDTO compareConfigDTO = this.hrBillCompareConfigRepository.getById(configId);
            compareConfigDTO.setHrBillCompareResultDetailDTOList(dynamicData);
            compareConfigDTO.setDynamicHeaders(dynamicHeaders);
            compareConfigDTO.setStatus(compareResult.getStatus());
            if (listMap != null && !listMap.isEmpty()) {
                compareConfigDTO.setAbnormalDetailMapList(listMap);
            }
            return compareConfigDTO;
        }
    }

    /**
     * 制作动态数据
     *
     * @param hrBillCompareResultDetailList
     * @param dynamicHeaders
     * @param type
     * @return
     */
    private List<Map<String, Object>> makeDynamicData(List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailList, List<DynamicHeadersDTO> dynamicHeaders, Integer type) {
        List<Map<String, Object>> mapList = new LinkedList<>();
        List<String> includeList = dynamicHeaders.stream().map(DynamicHeadersDTO::getKey).collect(Collectors.toList());
        includeList.remove("systemData");
        includeList.remove("sourceData");
        includeList.remove("compareResult");
        hrBillCompareResultDetailList.forEach(resultDetail -> {
            Map<String, Object> map = EntityUtils.object2Map(resultDetail, includeList);
            map.put("paymentDate", resultDetail.getPaymentDate());
            if (resultDetail.getSystemDataObject() != null) {
                this.dealDynamicDataChildren(map, resultDetail.getSystemDataObject(), "systemData", type);
            }
            if (resultDetail.getSourceDataObject() != null) {
                this.dealDynamicDataChildren(map, resultDetail.getSourceDataObject(), "sourceData", type);
            }
            if (resultDetail.getCompareResultObject() != null) {
                this.dealDynamicDataChildren(map, resultDetail.getCompareResultObject(), "compareData", type);
            }
            mapList.add(map);
        });
        return mapList;
    }

    private void dealDynamicDataChildren(Map<String, Object> map, HrBillCompareHeaderDTO dataObject, String topKey, Integer type) {
        if (type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            dataObject.getPensionCompareParts().forEach(part -> map.put(topKey + (part.getFieldKey().contains("unit") ? "Unit" : "Personal") + part.getFieldKey(), part.getFieldValue()));
            dataObject.getMedicalCompareParts().forEach(part -> map.put(topKey + (part.getFieldKey().contains("unit") ? "Unit" : "Personal") + part.getFieldKey(), part.getFieldValue()));
            dataObject.getUnemploymentCompareParts().forEach(part -> map.put(topKey + (part.getFieldKey().contains("unit") ? "Unit" : "Personal") + part.getFieldKey(), part.getFieldValue()));
            dataObject.getInjuryCompareParts().forEach(part -> map.put(topKey + (part.getFieldKey().contains("unit") ? "Unit" : "Personal") + part.getFieldKey(), part.getFieldValue()));
            dataObject.getMaternityCompareParts().forEach(part -> map.put(topKey + (part.getFieldKey().contains("unit") ? "Unit" : "Personal") + part.getFieldKey(), part.getFieldValue()));
            dataObject.getSubCompareParts().forEach(part -> map.put(topKey + (part.getFieldKey().contains("unit") ? "Unit" : "Personal") + part.getFieldKey(), part.getFieldValue()));
            dataObject.getAccumulationFundCompareParts().forEach(part -> map.put(topKey + (part.getFieldKey().contains("unit") ? "Unit" : "Personal") + part.getFieldKey(), part.getFieldValue()));
            map.put(topKey + "TotalAmount", dataObject.getTotalAmount());
        } else {
            List<ComparePart> unitPart = dataObject.getUnitPart();
            unitPart.forEach(part -> map.put(topKey + "Unit" + part.getFieldKey(), part.getFieldValue()));
            List<ComparePart> personalPart = dataObject.getPersonalPart();
            personalPart.forEach(part -> map.put(topKey + "Personal" + part.getFieldKey(), part.getFieldValue()));

            map.put(topKey + "PersonalTax", dataObject.getPersonalTax());
            map.put(topKey + "UnitLateFee", dataObject.getUnitLateFee());
            map.put(topKey + "Total", dataObject.getTotal());
            map.put(topKey + "TotalAmount", dataObject.getTotalAmount());
        }
    }

    /**
     * 海尔对账动态表头
     *
     * @param hrBillCompareResultDetailList
     * @return
     */
    private List<DynamicHeadersDTO> makeDynamicHeaders(List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailList) {
        HrBillCompareResultDetailDTO resultDetail = hrBillCompareResultDetailList.get(0);
        List<DynamicHeadersDTO> list = new LinkedList<>();
        list.add(new DynamicHeadersDTO().setTitle("员工姓名").setKey("staffName"));
        list.add(new DynamicHeadersDTO().setTitle("身份证号").setKey("idNo"));
        list.add(new DynamicHeadersDTO().setTitle("缴费年月").setKey("paymentDate"));
        list.add(new DynamicHeadersDTO().setTitle("中心").setKey("centerName"));
        list.add(new DynamicHeadersDTO().setTitle("缴保地").setKey("area"));
        HrBillCompareHeaderDTO systemDataObject = resultDetail.getSystemDataObject();
        HrBillCompareHeaderDTO sourceDataObject = resultDetail.getSourceDataObject();
        HrBillCompareHeaderDTO compareResultObject = resultDetail.getCompareResultObject();
        if (sourceDataObject.getPensionCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, sourceDataObject.getPensionCompareParts(), "养老保险系统数据", "sourceData", "#6894fe");
        }
        if (systemDataObject.getPensionCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, systemDataObject.getPensionCompareParts(), "养老保险劳务公司实际缴纳", "systemData", "#efa807");
        }
        if (compareResultObject.getPensionCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, compareResultObject.getPensionCompareParts(), "养老保险差异", "compareData", "#eb3333");
        }
        if (sourceDataObject.getMedicalCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, sourceDataObject.getMedicalCompareParts(), "医疗保险系统数据", "sourceData", "#6894fe");
        }
        if (systemDataObject.getMedicalCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, systemDataObject.getMedicalCompareParts(), "医疗保险劳务公司实际缴纳", "systemData", "#efa807");
        }
        if (compareResultObject.getMedicalCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, compareResultObject.getMedicalCompareParts(), "医疗保险差异", "compareData", "#eb3333");
        }
        if (sourceDataObject.getUnemploymentCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, sourceDataObject.getUnemploymentCompareParts(), "失业保险系统数据", "sourceData", "#6894fe");
        }
        if (systemDataObject.getUnemploymentCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, systemDataObject.getUnemploymentCompareParts(), "失业保险劳务公司实际缴纳", "systemData", "#efa807");
        }
        if (compareResultObject.getUnemploymentCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, compareResultObject.getUnemploymentCompareParts(), "失业保险差异", "compareData", "#eb3333");
        }
        if (sourceDataObject.getInjuryCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, sourceDataObject.getInjuryCompareParts(), "工伤保险系统数据", "sourceData", "#6894fe");
        }
        if (systemDataObject.getInjuryCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, systemDataObject.getInjuryCompareParts(), "工伤保险劳务公司实际缴纳", "systemData", "#efa807");
        }
        if (compareResultObject.getInjuryCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, compareResultObject.getInjuryCompareParts(), "工伤保险差异", "compareData", "#eb3333");
        }
        if (sourceDataObject.getMaternityCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, sourceDataObject.getMaternityCompareParts(), "生育保险系统数据", "sourceData", "#6894fe");
        }
        if (systemDataObject.getMaternityCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, systemDataObject.getMaternityCompareParts(), "生育保险劳务公司实际缴纳", "systemData", "#efa807");
        }
        if (compareResultObject.getMaternityCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, compareResultObject.getMaternityCompareParts(), "生育保险差异", "compareData", "#eb3333");
        }
        if (sourceDataObject.getSubCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, sourceDataObject.getSubCompareParts(), "保险系统数据", "sourceData", "#6894fe");
        }
        if (systemDataObject.getSubCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, systemDataObject.getSubCompareParts(), "保险劳务公司实际缴纳", "systemData", "#efa807");
        }
        if (compareResultObject.getSubCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, compareResultObject.getSubCompareParts(), "保险差异", "compareData", "#eb3333");
        }
        if (sourceDataObject.getAccumulationFundCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, sourceDataObject.getAccumulationFundCompareParts(), "公积金系统数据", "sourceData", "#6894fe");
        }
        if (systemDataObject.getAccumulationFundCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, systemDataObject.getAccumulationFundCompareParts(), "公积金劳务公司实际缴纳", "systemData", "#efa807");
        }
        if (compareResultObject.getAccumulationFundCompareParts() != null) {
            this.dealComparePartDynamicHeader(list, compareResultObject.getAccumulationFundCompareParts(), "公积金差异", "compareData", "#eb3333");
        }
        list.add(new DynamicHeadersDTO().setTitle("咨询公司支付合计").setKey("systemTotal").setColor("#efa807").setChildren(new ArrayList<>()));
        list.add(new DynamicHeadersDTO().setTitle("海尔系统支付合计").setKey("sourceTotal").setColor("#6894fe").setChildren(new ArrayList<>()));
        list.add(new DynamicHeadersDTO().setTitle("差异合计").setKey("compareTotal").setColor("#eb3333").setChildren(new ArrayList<>()));
        return list;
    }

    private void dealComparePartDynamicHeader(List<DynamicHeadersDTO> list, List<ComparePart> compareParts, String topTitle, String topKey, String color) {
        DynamicHeadersDTO dynamicHeadersDTO = new DynamicHeadersDTO();
        dynamicHeadersDTO.setTitle(topTitle);
        dynamicHeadersDTO.setKey(topKey);
        dynamicHeadersDTO.setColor(color);
        dynamicHeadersDTO.setChildren(new ArrayList<>());
        List<ComparePart> collect = compareParts.stream().filter(lst -> lst.getSortValue() != null).sorted(Comparator.comparing(ComparePart::getSortValue)).collect(Collectors.toList());
        if (collect != null && !collect.isEmpty()) {
            compareParts = collect;
        }
        compareParts.forEach(unitPart -> {
            DynamicHeadersDTO temp = new DynamicHeadersDTO();
            temp.setTitle(unitPart.getFieldName());
            temp.setKey(topKey + (unitPart.getFieldKey().contains("unit") ? "Unit" : "Personal") + unitPart.getFieldKey());
            temp.setColor(color);
            dynamicHeadersDTO.getChildren().add(temp);
        });
        list.add(dynamicHeadersDTO);
    }

    /**
     * 预检测,判断字段映射等配置是否合理
     *
     * @param hrBillCompareConfig
     */
    private void preCheckWithCompare(HrBillCompareConfigDTO hrBillCompareConfig) {
        BillFieldInfoDTO fieldInfoDTO = hrBillCompareConfig.getBillFieldInfoDTO().orElseThrow(() -> new CommonException("数据解析异常!"));
        // 如果是个税对账,限制只能配置一个个税字段
        if (BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey().equals(hrBillCompareConfig.getType())) {
            int nums = 0;
            HrExpenseManageDTO taxDTO = null;
            for (HrExpenseManageDTO expenseManageDTO : fieldInfoDTO.getMappingFields()) {
                if (DynamicFeeTypesEnum.PERSONAL_TAX.getKey().equals(expenseManageDTO.getExpenseType())) {
                    nums++;
                    taxDTO = expenseManageDTO;
                }
            }
            String errorMsg = "需要配置一个【个税】类型的字段，并且该字段应为【累计应补(退)税额】";
            if (nums == 0) {
                throw new CommonException(errorMsg);
            } else if (nums == 1 && !taxDTO.getExpenseName().startsWith("累计应补(退)税额")) {
                throw new CommonException(errorMsg);
            } else if (nums > 1) {
                throw new CommonException(errorMsg);
            }
        } else if (BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey().equals(hrBillCompareConfig.getType())) {
            List<HrExpenseManageDTO> mappingFields = fieldInfoDTO.getMappingFields();
            Map<String, List<HrExpenseManageDTO>> listMap = mappingFields.stream().collect(Collectors.groupingBy(HrExpenseManageDTO::getExpenseType));
            List<String> arrayList = new ArrayList<>();
            for (String key : listMap.keySet()) {
                List<HrExpenseManageDTO> hrExpenseManageDTOS = listMap.get(key);
                if (hrExpenseManageDTOS.size() > 1) {
                    EnumDTO enumDTO = DynamicFeeTypesEnum.getEnumEntityByKey(key);
                    arrayList.add(enumDTO.getValue());
                }
            }
            if (CollectionUtils.isNotEmpty(arrayList)) {
                throw new CommonException("解析失败," + "表单映射列[" + String.join(",", arrayList) + "]存在多个!");
            }

        }

    }

    /**
     * 处理excel数据
     *
     * @param excelDataDTOMap
     * @param hrBillCompareConfig
     */
    private void handleExcelDataMap(Map<String, BillExcelDataDTO> excelDataDTOMap, HrBillCompareConfigDTO hrBillCompareConfig, Map<String, Map<String, Object>> systemMapDatas) {
        // 处理个税导盘
        this.handleExcelWithTax(excelDataDTOMap, hrBillCompareConfig, systemMapDatas);
    }

    /**
     * 处理个税导盘
     *
     * @param excelDataDTOMap
     * @param hrBillCompareConfig
     */
    private void handleExcelWithTax(Map<String, BillExcelDataDTO> excelDataDTOMap, HrBillCompareConfigDTO hrBillCompareConfig, Map<String, Map<String, Object>> systemMapDatas) {
        if (!BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey().equals(hrBillCompareConfig.getType())) {
            return;
        }
        // 个税导盘特殊处理: excel读取到的 [累计应扣缴税额] 转换为 [本期应扣]
        HrBillDTO hrBillDTO = this.hrBillRepository.getBillInfoById(hrBillCompareConfig.getBillId());
        Integer payYear = hrBillDTO.getPayYear();
        Integer payMonth = hrBillDTO.getPayMonthly();
        // 1月份不做处理
        if (payMonth == 1) {
            return;
        }
//        // 获取上个月1日
//        LocalDate lastMonth = LocalDate.of(payYear, payMonth - 1, 1);
//
//        // 查询本期账单关联的员工所对应的上月个税信息
//        List<HrSpecialDeductionDTO> deductionDTOS = this.hrSpecialDeductionRepository.getByBillIdAndStartDate(hrBillCompareConfig.getBillId(), lastMonth);
//        if(deductionDTOS.isEmpty()) {
//            return;
//        }
//        Map<String, HrSpecialDeductionDTO> deductionDTOMap = new HashMap<>(BillParseUtils.getInitCapacity(deductionDTOS.size()));
//        deductionDTOS.forEach(d -> {
//            deductionDTOMap.put(d.getCertificateNum(), d);
//        });

        // 重置excel读取到的 [累计应扣缴税额] 转换为 [本期应缴]
        for (String idCard : excelDataDTOMap.keySet()) {
            BillExcelDataDTO billExcelDataDTO = excelDataDTOMap.get(idCard);
            Map<String, Object> mapDatas = billExcelDataDTO.getMapDatas();
            Map<String, Object> billDetailMap = systemMapDatas.get(idCard);
//            HrSpecialDeductionDTO specialDeductionDTO = deductionDTOMap.get(idCard);
//            if(specialDeductionDTO == null) {
//                mapDatas.put(EXIST_LAST_MONTH_TAX_DEDCTIOIN, "否");
//            }else {
//                mapDatas.put(EXIST_LAST_MONTH_TAX_DEDCTIOIN, "是");
//                // 获取读取到的 [累计应扣缴税额]
//                BigDecimal a1 = BillParseUtils.objectToDecimal(mapDatas.get(DynamicFeeTypesEnum.PERSONAL_TAX.getFieldName()));
//                // 本期应缴=本期累计应扣 - 上期累计应扣 + 上期补交
//                BigDecimal val = CalculateUtils.decimalAddition(
//                    CalculateUtils.decimalSubtraction(a1, specialDeductionDTO.getTaxWithholding()),
//                    specialDeductionDTO.getTaxShouldBePaidRefunded()
//                );
//                // 重置
//                mapDatas.put(DynamicFeeTypesEnum.PERSONAL_TAX.getFieldName(), val);
//            }
            //本期应缴 = [累计应补(退)税额] - 个税
            mapDatas.put(EXIST_LAST_MONTH_TAX_DEDCTIOIN, "是");
            // 获取读取到的 [累计应补(退)税额]
            BigDecimal a1 = BillParseUtils.objectToDecimal(mapDatas.get(DynamicFeeTypesEnum.PERSONAL_TAX.getFieldName()));
            /*BigDecimal personalTax = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(billDetailMap)) {
                personalTax = new BigDecimal(String.valueOf(billDetailMap.get(DynamicFeeTypesEnum.PERSONAL_TAX.getFieldName())));//个税
            }
            BigDecimal val = CalculateUtils.decimalSubtraction(a1, personalTax);*/
            mapDatas.put(DynamicFeeTypesEnum.PERSONAL_TAX.getFieldName(), a1);
        }
    }

    /**
     * 对账后保存补差
     *
     * @param billConfigId     对账配置id
     * @param currentMonthUsed
     */
    @Override
    public void saveCompareMakeUp(String billConfigId, Integer currentMonthUsed) {
        // 获取对账配置信息
        HrBillCompareConfigDTO compareConfigDTO = this.hrBillCompareConfigRepository.getById(billConfigId);
        // 第三方账单不保存补差
        if (BillEnum.GuidePlateType.THIRD_PARTY_BILLS.getKey().equals(compareConfigDTO.getType())) {
            throw new CommonException("第三方账单只记录补差结果，暂不支持补差数额的应用");
        }
        // 获取对账结果
        HrBillCompareResultDTO compareResultDTO = this.hrBillCompareResultRepository.getByBillConfigId(billConfigId);
        if (compareResultDTO == null) {
            throw new CommonException("对账结果解析失败,无法保存!");
        }
        compareConfigDTO.setBillResultId(compareResultDTO.getId());
        // 获取对账结果明细
        List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailDTOList = this.hrBillCompareResultDetailRepository.selectHrBillCompareResultDetailByBillCompareResultId(compareResultDTO.getId());
        if (hrBillCompareResultDetailDTOList.isEmpty()) {
            throw new CommonException("未查询到对账结果明细!");
        }
        HrBillDTO hrBillDTO = this.hrBillRepository.getBillInfoById(compareConfigDTO.getBillId());

        BillEnum.GuidePlateType plateType = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, compareConfigDTO.getType());
        String title = plateType.getValue() + "对账: ";
        // 遍历明细 保存对账补差明细
        hrBillCompareResultDetailDTOList.forEach(detail -> {
            if (!StringUtil.isEmpty(detail.getIdNo()) && !"null".equals(detail.getIdNo())) {
                HrWelfareCompensation hrWelfareCompensation = new HrWelfareCompensation()
                    .setIdNo(detail.getIdNo())
                    .setStaffId(detail.getStaffId())
                    .setClientId(detail.getClientId())
                    .setPayYear(hrBillDTO.getPayYear())
                    .setPayMonthly(hrBillDTO.getPayMonthly())
                    .setType(plateType.getKey() + 1)
                    .setIsUsed(BillEnum.MakeupIsUsed.NOT_USED.getKey());
                // 删除旧数据
                this.hrWelfareCompensationRepository.delByObject(hrWelfareCompensation);
                StringBuilder sb = new StringBuilder(title);
                //补差操作日志
                HrWelfareCompensationRecord record = new HrWelfareCompensationRecord();
                //处理福利补差以及补差操作日志
                this.handleWelfareCompensation(sb, detail, record, hrWelfareCompensation, plateType);

                // 保存补差日志
                if (!sb.toString().equals(title)) {
                    record.setChangeMsg(sb.toString());
                    this.welfareCompensationRecordRepository.insert(record);
                    hrWelfareCompensation.setBillResultId(compareConfigDTO.getBillResultId()).setRecordId(record.getId());
                    this.hrWelfareCompensationRepository.insert(hrWelfareCompensation);
                }
            }
        });

        // 更新对账结果的状态
        compareResultDTO.setStatus(1).setLockStatus(1).setCurrentMonthUsed(currentMonthUsed);
        this.hrBillCompareResultRepository.updateById(this.hrBillCompareResultMapper.toEntity(compareResultDTO));
    }

    private Map<String, HrBillCompareHeaderDTO.ComparePart> getComparePartCollect(HrBillCompareHeaderDTO data) {
        List<HrBillCompareHeaderDTO.ComparePart> systemTempList = new ArrayList<>();
        systemTempList.addAll(data.getUnitPart());
        systemTempList.addAll(data.getPersonalPart());
        return systemTempList.stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
    }

    /**
     * 根据账单id和导盘类型删除对账配置和结果
     *
     * @param billId 账单id
     * @param type   导盘类型 0 社保, 1 医保, 2 公积金,3 个税, 4第三方账单
     */
    @Override
    public void delByBillIdAndType(String billId, Integer type) {
        this.hrBillCompareConfigRepository.delByBillIdAndType(billId, type, 1, null);
        this.hrBillCompareResultRepository.delByBillIdAndType(billId, type, 1, null);
        this.hrWelfareCompensationRepository.delByBillIdAndIsUsed(billId, type, 1, null);
    }


    /**
     * 所有客户是否存在账单
     *
     * @param paymentDate 缴费年月
     * @param billType    账单类型 （0薪酬账单 1保障账单 2特殊客户账单/海尔）
     * @return 客户信息
     */
    @Override
    public List<HrClientDTO> existenceSecurityBill(String paymentDate, Integer billType) {
        String[] split = paymentDate.split("-");
        Integer payYear = Integer.parseInt(split[0]);
        Integer payMonthly = Integer.parseInt(split[1]);
        List<String> clientIds = new ArrayList<>();
        if (billType == 2) {//特殊客户账单/海尔
            billType = 1;
            List<HrClientDTO> hrClientDTOList = hrClientRepository.getSubordinateClient(SpecialBillClient.HAIER.getKey());
            if (CollectionUtils.isEmpty(hrClientDTOList)) {
                throw new CommonException("未查询到海尔相关客户信息！");
            }
            clientIds = hrClientDTOList.stream().map(HrClientDTO::getId).collect(Collectors.toList());
        }
        List<String> existClientIds = hrBillRepository.getSecurityBill(payYear, payMonthly, billType, clientIds);
        HrClientDTO hrClientDTO = new HrClientDTO();
        hrClientDTO.setNeIds(existClientIds);
        if (CollectionUtils.isNotEmpty(clientIds)) {
            hrClientDTO.setIdList(clientIds);
        }
        List<HrClientDTO> hrClientDTOList = hrClientService.nonFindPage(hrClientDTO);
        return hrClientDTOList;
    }

    /**
     * 导出不存在账单的客户信息
     *
     * @param paymentDate
     * @param billType
     * @return
     */
    @Override
    public String existenceSecurityBillExport(String paymentDate, Integer billType) {
        List<HrClientDTO> hrClientDTOList = this.existenceSecurityBill(paymentDate, billType);
        // 表头
        List<ExcelExportEntity> excelHeader = new ArrayList<>();
        // 数据
        List<Map<String, Object>> paramsList = new ArrayList<>();
        for (HrClientDTO hrClientDTO : hrClientDTOList) {
            Map<String, Object> param = new HashMap<>();
            excelHeader.add(new ExcelExportEntity("单位编号", "unitNumber", 20));
            param.put("unitNumber", hrClientDTO.getUnitNumber());
            excelHeader.add(new ExcelExportEntity("客户名称", "clientName", 35));
            param.put("clientName", hrClientDTO.getClientName());
            excelHeader.add(new ExcelExportEntity("当前协议编号", "agreementNumber", 25));
            param.put("agreementNumber", hrClientDTO.getAgreementNumber());
            excelHeader.add(new ExcelExportEntity("协议类型", "agreementType", 15));
            param.put("agreementType", hrClientDTO.getAgreementTypekey());
            ExcelExportEntity entity = new ExcelExportEntity("员工人数", "peopleSum", 15);
            entity.setType(10);
            entity.setNumFormat("0");
            excelHeader.add(entity);
            param.put("peopleSum", hrClientDTO.getPeoplesum());
            paramsList.add(param);
        }
        List<ExcelExportEntity> collect = excelHeader.stream().distinct().collect(Collectors.toList());
        String filePath = ExcelUtils.dynamicColumnExportLocal("不可对账客户", null, collect, paramsList, tempPath);
        String fileUrl = this.hrAppendixService.uploadLocalFile(filePath);
        return fileUrl;
    }

    /**
     * 导出对账差异数据
     *
     * @param hrBillCompareConfigDTO
     * @return
     */
    @Override
    public String exportBillCompareResult(HrBillCompareConfigDTO hrBillCompareConfigDTO) {
        List<String> ids = hrBillCompareConfigDTO.getIds();
        // 表头
        List<ExcelExportEntity> excelHeader = new ArrayList<>();
        // 数据
        List<Map<String, Object>> paramsList = new ArrayList<>();
        if (hrBillCompareConfigDTO.getType().equals(BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey())
            || hrBillCompareConfigDTO.getType().equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            List<Map<String, Object>> list = hrBillCompareConfigDTO.getHrBillCompareResultDetailDTOList();
            for (Map<String, Object> objectMap : list) {
                HrBillCompareResultDetailDTO dto = JSONObject.parseObject(JSONObject.toJSONString(objectMap), HrBillCompareResultDetailDTO.class);
                if (dto.getUsedType() == 2) {
                    dto.setReason("导盘中不存在");
                } else if (dto.getUsedType() == 3) {
                    dto.setReason("系统中不存在");
                } else if (dto.getUsedType() == 4) {
                    dto.setReason("系统中存在，未在本月账单中存在");
                }
                this.excelHeaderDetail(dto, hrBillCompareConfigDTO.getType(), excelHeader, paramsList);
            }
        } else {
            List<HrBillCompareResultDetailDTO> list = hrBillCompareResultDetailRepository.selectDiffBatchId(ids);
            list.forEach(dto -> {
                if (dto.getUsedType() == 2) {
                    dto.setReason("导盘中不存在");
                } else if (dto.getUsedType() == 3) {
                    dto.setReason("系统中不存在");
                } else if (dto.getUsedType() == 4) {
                    dto.setReason("系统中存在，未在本月账单中存在");
                }
                Integer type = hrBillCompareConfigDTO.getType();
                this.excelHeaderDetail(dto, type, excelHeader, paramsList);
            });
        }
        // 去重
        List<ExcelExportEntity> collect = excelHeader.stream().distinct().collect(Collectors.toList());
        String filePath = ExcelUtils.dynamicColumnExportLocal("差异数据明细", null, collect, paramsList, tempPath);
        String fileUrl = this.hrAppendixService.uploadLocalFile(filePath);
        return fileUrl;
    }

    private void excelHeaderDetail(HrBillCompareResultDetailDTO dto, Integer type, List<ExcelExportEntity> excelHeader, List<Map<String, Object>> paramsList) {
        Map<String, Object> param = new HashMap<>();
        excelHeader.add(new ExcelExportEntity("单位编号", "unitNumber", 20));
        param.put("unitNumber", dto.getUnitNumber());
        excelHeader.add(new ExcelExportEntity("用工单位", "clientName", 35));
        param.put("clientName", dto.getClientName());
        excelHeader.add(new ExcelExportEntity("协议编号", "agreementNumber", 20));
        param.put("agreementNumber", dto.getAgreementNumber());
        if (!type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            excelHeader.add(new ExcelExportEntity("对公缴费账户", "accountNumber", 20));
            String accountNumber = "";
            if (type.equals(BillEnum.GuidePlateType.SOCIAL_SECURITY_GUIDE.getKey())) {
                accountNumber = dto.getSocialSecurityNumber();
            } else if (type.equals(BillEnum.GuidePlateType.MEDICAL_GUIDE.getKey())) {
                accountNumber = dto.getMedicalInsuranceNumber();
            } else if (type.equals(BillEnum.GuidePlateType.ACCUMULATION_FUND_GUIDE.getKey())) {
                accountNumber = dto.getAccountFundNumber();
            }
            param.put("accountNumber", accountNumber);
        }
        excelHeader.add(new ExcelExportEntity("员工姓名", "staffName", 15));
        param.put("staffName", dto.getStaffName());
        excelHeader.add(new ExcelExportEntity("身份证号码", "idCard", 20));
        param.put("idCard", dto.getIdNo());
        excelHeader.add(new ExcelExportEntity("员工状态", "staffStatus", 15));
        param.put("staffStatus", dto.getStaffStatusStr());
        excelHeader.add(new ExcelExportEntity("原因", "reason", 20));
        param.put("reason", dto.getReason());
        paramsList.add(param);
    }

    /**
     * 创建账单不可本月使用补差的客户
     *
     * @param billConfigId
     * @return
     */
    @Override
    public List<HrBillDTO> getExitSecurityBill(String billConfigId) {
        HrBillCompareConfigDTO compareConfigDTO = this.hrBillCompareConfigRepository.getById(billConfigId);
        HrBillDTO hrBillDTO = this.hrBillRepository.getBillInfoById(compareConfigDTO.getBillId());
        HrBillDTO dto = new HrBillDTO();
        dto.setPayYear(hrBillDTO.getPayYear()).setPayMonthly(hrBillDTO.getPayMonthly()).setBillType(hrBillDTO.getBillType());
        return hrBillRepository.findList(dto);
    }

    /**
     * 对账结果明细
     *
     * @param resultDetailDTO
     * @return
     */
    @Override
    public Map<String, Object> findResultDetailList(HrBillCompareResultDetailDTO resultDetailDTO) {
        Map<String, Object> result = new HashMap<>();
        List<HrBillCompareResultDetailDTO> list = hrBillCompareResultDetailRepository.findList(resultDetailDTO);
        if (list != null && !list.isEmpty()) {
            //组装动态表头
            List<DynamicHeadersDTO> dynamicHeaders = new ArrayList<>();
            List<Map<String, Object>> dynamicData = new LinkedList<>();
            Map<String, List<HrBillCompareResultDetailDTO>> listMap = list.stream().collect(Collectors.groupingBy(ls -> ls.getIdNo() + "_" + ls.getPaymentDate()));
            for (Map.Entry<String, List<HrBillCompareResultDetailDTO>> entry : listMap.entrySet()) {
                List<HrBillCompareResultDetailDTO> resultDetailDTOS = entry.getValue().stream().sorted(Comparator.comparing(HrBillCompareResultDetailDTO::getType)).collect(Collectors.toList());
                List<String> ids = resultDetailDTOS.stream().map(HrBillCompareResultDetailDTO::getId).collect(Collectors.toList());
                //动态表头
                this.dealCompareDynamicHeader(dynamicHeaders, resultDetailDTOS);
                // 制作数据列表
                HrBillCompareResultDetailDTO detailDTO = resultDetailDTOS.get(0);
                Map<String, Object> map = new HashMap<>();
                map.put("staffName", detailDTO.getStaffName());
                map.put("idNo", detailDTO.getIdNo());
                map.put("clientName", detailDTO.getClientName());
                map.put("paymentDate", detailDTO.getPaymentDate());
                map.put("strIds", ids);
                resultDetailDTOS.forEach(resultDetail -> {
                    String compareType = "";
                    BillEnum.GuidePlateType enumByKey = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, resultDetail.getType());
                    switch (enumByKey) {
                        case SOCIAL_SECURITY_GUIDE:
                            compareType = "socialSecurity";
                            break;
                        case MEDICAL_GUIDE:
                            compareType = "medical";
                            break;
                        case ACCUMULATION_FUND_GUIDE:
                            compareType = "accumulationFund";
                            break;
                        default:
                            break;
                    }
                    if (resultDetail.getSystemDataObject() != null) {
                        this.dealDynamicDataChildren(map, resultDetail.getSystemDataObject(), compareType + "SystemData", resultDetail.getType());
                    }
                    if (resultDetail.getSourceDataObject() != null) {
                        this.dealDynamicDataChildren(map, resultDetail.getSourceDataObject(), compareType + "SourceData", resultDetail.getType());
                    }
                    if (resultDetail.getCompareResultObject() != null) {
                        this.dealDynamicDataChildren(map, resultDetail.getCompareResultObject(), compareType + "CompareData", resultDetail.getType());
                    }
                });
                dynamicData.add(map);
            }
            //去重
            List<DynamicHeadersDTO> dynamicHeadersDTOS = dynamicHeaders.stream().distinct().collect(Collectors.toList());
            result.put("dynamicHeaders", dynamicHeadersDTOS);
            result.put("hrBillCompareResultDetailDTOList", dynamicData);
        }
        return result;
    }

    /**
     * 处理明细动态表头
     *
     * @param dynamicHeaders
     * @param resultDetailDTOS
     */
    private void dealCompareDynamicHeader(List<DynamicHeadersDTO> dynamicHeaders, List<HrBillCompareResultDetailDTO> resultDetailDTOS) {
        if (dynamicHeaders == null || dynamicHeaders.isEmpty()) {
            //动态表头
            dynamicHeaders.add(new DynamicHeadersDTO().setTitle("员工姓名").setKey("staffName"));
            dynamicHeaders.add(new DynamicHeadersDTO().setTitle("身份证号").setKey("idNo"));
            dynamicHeaders.add(new DynamicHeadersDTO().setTitle("用工单位").setKey("clientName"));
            dynamicHeaders.add(new DynamicHeadersDTO().setTitle("缴费年月").setKey("paymentDate"));
        }
        for (HrBillCompareResultDetailDTO resultDetail : resultDetailDTOS) {
            String compareTypeStr = "";
            String compareType = "";
            BillEnum.GuidePlateType enumByKey = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, resultDetail.getType());
            switch (enumByKey) {
                case SOCIAL_SECURITY_GUIDE:
                    compareTypeStr = "社保";
                    compareType = "socialSecurity";
                    break;
                case MEDICAL_GUIDE:
                    compareTypeStr = "医保";
                    compareType = "medical";
                    break;
                case ACCUMULATION_FUND_GUIDE:
                    compareTypeStr = "公积金";
                    compareType = "accumulationFund";
                    break;
                default:
                    break;
            }
            if (resultDetail.getSystemDataObject() != null) {
                String topKey = compareType + "SystemData";
                boolean anyMatch = dynamicHeaders.stream().anyMatch(ls -> ls.getKey().equals(topKey));
                if (anyMatch) {
                    continue;
                }
                this.hrBillCompareResultDetailService.dealComparePartDynamicHeader(enumByKey, dynamicHeaders, resultDetail.getSystemDataObject(), compareTypeStr + "当月实际缴费数据", topKey, "#efa807");
            }
            if (resultDetail.getSourceDataObject() != null) {
                String topKey = compareType + "SourceData";
                boolean anyMatch = dynamicHeaders.stream().anyMatch(ls -> ls.getKey().equals(topKey));
                if (anyMatch) {
                    continue;
                }
                this.hrBillCompareResultDetailService.dealComparePartDynamicHeader(enumByKey, dynamicHeaders, resultDetail.getSourceDataObject(), compareTypeStr + "系统缴费数据", topKey, "#6894fe");
            }
            if (resultDetail.getCompareResultObject() != null) {
                String topKey = compareType + "CompareData";
                boolean anyMatch = dynamicHeaders.stream().anyMatch(ls -> ls.getKey().equals(topKey));
                if (anyMatch) {
                    continue;
                }
                this.hrBillCompareResultDetailService.dealComparePartDynamicHeader(enumByKey, dynamicHeaders, resultDetail.getCompareResultObject(), compareTypeStr + "差异数据", topKey, "#eb3333");
            }
        }
    }

    /**
     * 导出对账结果明细
     *
     * @param resultDetailDTO
     * @return
     */
    @Override
    public String exportResultDetail(HrBillCompareResultDetailDTO resultDetailDTO) {
        List<HrBillCompareResultDetailDTO> list = hrBillCompareResultDetailRepository.findList(resultDetailDTO);
        if (list == null || list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        List<DynamicHeadersDTO> dynamicHeaders = new ArrayList<>();
        // 处理数据列表
        List<Map<String, Object>> dynamicData = new LinkedList<>();
        Map<String, List<HrBillCompareResultDetailDTO>> listMap = list.stream().collect(Collectors.groupingBy(ls -> ls.getIdNo() + "_" + ls.getPaymentDate()));
        for (Map.Entry<String, List<HrBillCompareResultDetailDTO>> entry : listMap.entrySet()) {
            List<HrBillCompareResultDetailDTO> resultDetailDTOS = entry.getValue().stream().sorted(Comparator.comparing(HrBillCompareResultDetailDTO::getType)).collect(Collectors.toList());
            //动态表头
            this.dealCompareDynamicHeader(dynamicHeaders, resultDetailDTOS);
            // 制作数据列表
            HrBillCompareResultDetailDTO detailDTO = resultDetailDTOS.get(0);
            Map<String, Object> map = new HashMap<>();
            map.put("staffName", detailDTO.getStaffName());
            map.put("idNo", detailDTO.getIdNo());
            map.put("clientName", detailDTO.getClientName());
            map.put("paymentDate", detailDTO.getPaymentDate());
            resultDetailDTOS.forEach(resultDetail -> {
                String compareType = "";
                BillEnum.GuidePlateType enumByKey = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, resultDetail.getType());
                switch (enumByKey) {
                    case SOCIAL_SECURITY_GUIDE:
                        compareType = "socialSecurity";
                        break;
                    case MEDICAL_GUIDE:
                        compareType = "medical";
                        break;
                    case ACCUMULATION_FUND_GUIDE:
                        compareType = "accumulationFund";
                        break;
                    default:
                        break;
                }
                if (resultDetail.getSystemDataObject() != null) {
                    this.hrBillCompareResultService.dealDynamicDataChildrenForExport(map, resultDetail.getSystemDataObject(), compareType + "SystemData");
                }
                if (resultDetail.getSourceDataObject() != null) {
                    this.hrBillCompareResultService.dealDynamicDataChildrenForExport(map, resultDetail.getSourceDataObject(), compareType + "SourceData");
                }
                if (resultDetail.getCompareResultObject() != null) {
                    this.hrBillCompareResultService.dealDynamicDataChildrenForExport(map, resultDetail.getCompareResultObject(), compareType + "CompareData");
                }
            });
            dynamicData.add(map);
        }
        // 处理表头
        List<ExcelExportEntity> colList = new ArrayList<>();
        hrBillCompareResultService.handleExcelExportEntity(colList, dynamicHeaders);

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        ExcelExportService service = new ExcelExportService();
        service.createSheetForMap(workbook, new ExportParams(null, "sheet1"), colList, dynamicData);
        String excelToLocal = ExcelUtils.downLoadExcelToLocal("对账结果明细", workbook, tempPath);
        return hrAppendixService.uploadLocalFile(excelToLocal);
    }

    /**
     * 导出不可当月使用补差的客户信息
     *
     * @param hrBillDTO
     * @return
     */
    @Override
    public String exportExcludesClient(HrBillDTO hrBillDTO) {
        List<HrBillDTO> list = hrBillRepository.findList(hrBillDTO);
        // 表头
        List<ExcelExportEntity> excelHeader = new ArrayList<>();
        // 数据
        List<Map<String, Object>> paramsList = new ArrayList<>();
        list.forEach(billDTO -> {
            Map<String, Object> param = new HashMap<>();
            excelHeader.add(new ExcelExportEntity("客户名称", "clientName", 35));
            param.put("clientName", billDTO.getClientName());
            excelHeader.add(new ExcelExportEntity("客户编号", "unitNumber", 20));
            param.put("unitNumber", billDTO.getUnitNumber());
            excelHeader.add(new ExcelExportEntity("费用年月", "paymentDate", 20));
            param.put("paymentDate", billDTO.getPaymentDate());
            excelHeader.add(new ExcelExportEntity("账单类型", "billTypeStr", 20));
            param.put("billTypeStr", billDTO.getBillTypeStr());
            excelHeader.add(new ExcelExportEntity("是否锁定", "billStateStr", 20));
            param.put("billStateStr", billDTO.getBillStateStr());
            paramsList.add(param);
        });
        List<ExcelExportEntity> collect = excelHeader.stream().distinct().collect(Collectors.toList());
        String filePath = ExcelUtils.dynamicColumnExportLocal("不可当月使用补差客户信息", null, collect, paramsList, tempPath);
        String fileUrl = this.hrAppendixService.uploadLocalFile(filePath);
        return fileUrl;
    }

    /**
     * 查询薪酬账单
     *
     * @param billConfigIds
     * @return
     */
    @Override
    public List<HrBillDTO> getExitTaxBill(List<String> billConfigIds) {
        List<HrBillCompareConfig> configList = hrBillCompareConfigRepository.selectBatchIds(billConfigIds);
        List<String> billIds = configList.stream().map(HrBillCompareConfig::getBillId).collect(Collectors.toList());
        return hrBillRepository.findList(new HrBillDTO().setIds(billIds));
    }

    /**
     * 对账仅保存对账结果
     *
     * @param billConfigIds 对账配置ID
     * @param lockStatus    是否锁定 0未锁定 1已锁定
     */
    @Override
    public void saveOnlyCompareResult(List<String> billConfigIds, Integer lockStatus) {
        List<HrBillCompareConfig> configList = hrBillCompareConfigRepository.selectBatchIds(billConfigIds);
        if (configList == null || configList.isEmpty()) {
            throw new CommonException("未查询到对账配置信息！");
        }
        List<String> ids = configList.stream().map(HrBillCompareConfig::getId).collect(Collectors.toList());
        hrBillCompareResultRepository.updateConfigLockStatus(lockStatus, ids);
        // Jigsaw 2025/5/9 若已锁定，生成各个每个单位的报销申请 ，取消锁定再删除
        // 社保医保公积金且只有1项，社保医保公积金对账只能单个锁定
        if (configList.size() == 1 && BillEnum.GuidePlateType.isSocialSecurityOrMedicalOrAccumulationFoundation(configList.get(0).getType())) {
            saveOrDeleteReimbursement(configList.get(0).getId(), lockStatus);
        }
    }

    /**
     * 根据锁定状态，新增或删除对账结果
     *
     * @param billConfigId 账单配置id
     */
    private void saveOrDeleteReimbursement(String billConfigId, Integer lockStatus) {
        // 获取对账配置信息
        HrBillCompareConfigDTO compareConfigDTO = this.hrBillCompareConfigRepository.getById(billConfigId);
        // 第三方账单不保存补差
        if (BillEnum.GuidePlateType.THIRD_PARTY_BILLS.getKey().equals(compareConfigDTO.getType())) {
            throw new CommonException("第三方账单只记录补差结果，暂不支持补差数额的应用");
        }
        // 获取对账结果
        HrBillCompareResultDTO compareResultDTO = this.hrBillCompareResultRepository.getByBillConfigId(billConfigId);
        if (compareResultDTO == null) {
            throw new CommonException("对账结果解析失败,无法保存!");
        }
        if (lockStatus == 0) {
            // 删除报销申请
            hrBillReimbursementApplyService.deleteByBillCompareResultId(Collections.singletonList(compareResultDTO.getId()));
            return;
        }
        // 新增报销申请
        compareConfigDTO.setBillResultId(compareResultDTO.getId());
        // 获取对账结果明细
        List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailDTOList = this.hrBillCompareResultDetailRepository.selectHrBillCompareResultDetailByBillCompareResultId(compareResultDTO.getId());
        if (hrBillCompareResultDetailDTOList.isEmpty()) {
            throw new CommonException("未查询到对账结果明细!");
        }

        BillEnum.GuidePlateType plateType = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, compareConfigDTO.getType());
        if (plateType == null) {
            throw new CommonException("导盘类型不存在");
        }
        BillReimbApproveEnums.InvoiceTypeEnum invoiceTypeEnum = plateType.getInvoiceTypeEnum();
        if (invoiceTypeEnum == null) {
            throw new CommonException("与导盘类型对应的报销类型不存在");
        }
        // 每次对账锁定时生成一次报销申请
        // 获取每个客户的导盘总计
        Map<String, BigDecimal> clientIdSourceTotalMap = hrBillCompareResultDetailDTOList.stream()
            .filter(it -> ObjectUtil.isNotNull(it.getClientId()))
            .filter(it -> it.getSourceTotal() != null && it.getSourceTotal().compareTo(BigDecimal.ZERO) > 0)
            .collect(Collectors.groupingBy(HrBillCompareResultDetailDTO::getClientId,
                Collectors.reducing(BigDecimal.ZERO, HrBillCompareResultDetailDTO::getSourceTotal, BigDecimal::add)));
        // 获取对账年月
        int year = hrBillCompareResultDetailDTOList.get(0).getPayYear();
        int month = hrBillCompareResultDetailDTOList.get(0).getPayMonthly();

        BigDecimal totalAmount = clientIdSourceTotalMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        HrBillReimbursementApplyDTO result = new HrBillReimbursementApplyDTO();
        result
            .setBillCompareResultId(compareResultDTO.getId())
            .setTitle((month > 9 ? "" : "0") + month + "月份" + plateType.getValue() + "对账资金发放申请")
            .setPayYear(year)
            .setPayMonth(month)
            .setAmount(totalAmount)
            .setAccountType(invoiceTypeEnum.getKey())
            .setApproveStatus(BillReimbApproveEnums.NOT_LAUNCH.getKey())
            .setApplyDate(LocalDate.now())
            .setFlag(false)
            .setReimbursementState(BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey())
            .setReimbursementLockState(BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey())
            .setDetailDTOList(Collections.singletonList(new HrBillReimbursementApplyDetailDTO()
                .setInvoiceType(invoiceTypeEnum.getKey())
                .setAmount(totalAmount)));
        // 每个客户的汇总
        List<HrBillReimbursementClientDTO> hrBillReimbursementClients = clientIdSourceTotalMap.entrySet().stream().map(it -> {
            HrBillReimbursementClientDTO hrBillReimbursementClient = new HrBillReimbursementClientDTO();
            hrBillReimbursementClient.setClientId(it.getKey());
            hrBillReimbursementClient.setAmount(it.getValue());
            return hrBillReimbursementClient;
        }).collect(Collectors.toList());
        // 映射后的客户汇总
        result.setHrBillReimbursementClientDTOS(this.getMapReimbursementClient(hrBillReimbursementClients, invoiceTypeEnum));
        this.hrBillReimbursementApplyService.insertHrBillReimbursementApplyFromCompareResult(result);
    }

    /**
     * 获取自动合并后的数据
     * 根据客户关系映射对应本级或上级的客户id
     *
     * @param hrBillReimbursementClients
     * @return
     */
    private List<HrBillReimbursementClientDTO> getMapReimbursementClient(List<HrBillReimbursementClientDTO> hrBillReimbursementClients,
                                                                         BillReimbApproveEnums.InvoiceTypeEnum invoiceTypeEnum) {
        // 标记客户列表
        Set<String> markedClientIdSet = Arrays.stream(markedClientIds.split(","))
            .collect(Collectors.toSet());
        // 保存着所有客户信息 id,parentId
        Map<String, HrClient> clientMap = hrClientService.list().stream().collect(Collectors.toMap(HrClient::getId, c -> c));

        // 按归属 ID 分组累加金额
        Map<String, BigDecimal> sumByGroup = new HashMap<>();

        for (HrBillReimbursementClientDTO dto : hrBillReimbursementClients) {
            String clientId = dto.getClientId();
            String groupId = findMarkedAncestor(clientId, clientMap, markedClientIdSet, clientId);

            sumByGroup.merge(groupId, dto.getAmount(), BigDecimal::add);
        }

        // 转成结果列表
        List<HrBillReimbursementClientDTO> result = sumByGroup.entrySet().stream()
            .map(entry -> {
                HrBillReimbursementClientDTO newDto = new HrBillReimbursementClientDTO();
                newDto.setClientId(entry.getKey());
                newDto.setAmount(entry.getValue());
                String accountId = null;
                if (invoiceTypeEnum == BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY) {
                    accountId = clientMap.get(entry.getKey()).getSocialSecurityAccountId();
                } else if (invoiceTypeEnum == BillReimbApproveEnums.InvoiceTypeEnum.DF_MEDICAL_INSURANCE) {
                    accountId = clientMap.get(entry.getKey()).getMedicalInsuranceAccountId();
                } else if (invoiceTypeEnum == BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND) {
                    accountId = clientMap.get(entry.getKey()).getProvidentFundAccountId();
                }
                newDto.setAccountId(accountId);
                return newDto;
            })
            .collect(Collectors.toList());

        return result;
    }

    /**
     * 递归获取标记客户
     *
     * @param clientId         客户id
     * @param clientMap        客户Map
     * @param markedClientIds  标记客户ID集合
     * @param originalClientId 最初调用者ID
     * @return 标记客户ID或当前客户id
     */
    private String findMarkedAncestor(String clientId, Map<String, HrClient> clientMap, Set<String> markedClientIds, String originalClientId) {
        if (markedClientIds.contains(clientId)) {
            return clientId;
        }
        HrClient client = clientMap.get(clientId);
        if (client == null || client.getParentId() == null || "0".equals(client.getParentId())) {
            return originalClientId;  // 返回最初调用者ID
        }
        return findMarkedAncestor(client.getParentId(), clientMap, markedClientIds, originalClientId);
    }

    /**
     * 批量锁定对账结果或者批量保存补差
     *
     * @param hrBillCompareResultDTO
     */
    @Override
    public void batchLockOrSaveMakeUp(HrBillCompareResultDTO hrBillCompareResultDTO) {
        List<HrBillCompareResultDTO> list = hrBillCompareResultRepository.findList(hrBillCompareResultDTO);
        if (list == null || list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        List<String> configIds = list.stream().map(HrBillCompareResultDTO::getBillCompareConfigId).collect(Collectors.toList());
        if (hrBillCompareResultDTO.getOptions() == 1) {
            List<HrBillCompareResultDTO> lockList = list.stream().filter(lst -> lst.getLockStatus() == 1).collect(Collectors.toList());
            if (lockList != null && !lockList.isEmpty()) {
                throw new CommonException("相关数据中存在已锁定的数据！");
            }
            hrBillCompareResultRepository.updateConfigLockStatus(1, configIds);
        } else {
            List<HrBillCompareResultDTO> dtoList = list.stream().filter(lst -> lst.getStatus() != null && lst.getStatus() == 1).collect(Collectors.toList());
            if (dtoList != null && !dtoList.isEmpty()) {
                throw new CommonException("相关数据中存在已保存补差的数据！");
            }
            this.saveBatchCompareMakeUp(list, hrBillCompareResultDTO.getCurrentMonthUsed());
        }
    }

    /**
     * 批量保存补差
     *
     * @param list             对账结果
     * @param currentMonthUsed 是否当月使用补差
     */
    @Override
    public void saveBatchCompareMakeUp(List<HrBillCompareResultDTO> list, Integer currentMonthUsed) {
        List<String> resultIds = list.stream().map(HrBillCompareResultDTO::getId).collect(Collectors.toList());
        List<HrBillCompareResultDetailDTO> resultDetailDTOList = hrBillCompareResultDetailRepository.selectResultDetailBatchResultId(resultIds);
        if (resultDetailDTOList.isEmpty()) {
            throw new CommonException("未查询到对账结果明细!");
        }
        //旧数据集合
        List<HrWelfareCompensation> oldDataList = new ArrayList<>();
        //新数据集合
        List<HrWelfareCompensation> newDataList = new ArrayList<>();
        //补差操作日志集合
        List<HrWelfareCompensationRecord> recordList = new ArrayList<>();
        for (HrBillCompareResultDTO resultDTO : list) {
            List<HrBillCompareResultDetailDTO> resultDetailDTOS = resultDetailDTOList.stream().filter(lst -> lst.getBillCompareResultId().equals(resultDTO.getId())).collect(Collectors.toList());
            if (resultDetailDTOS.isEmpty()) {
                throw new CommonException("未查询到" + resultDTO.getTitle() + "对账结果明细!");
            }
            BillEnum.GuidePlateType plateType = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, resultDTO.getType());
            String title = plateType.getValue() + "对账: ";
            resultDetailDTOS.forEach(detail -> {
                if (!StringUtil.isEmpty(detail.getIdNo()) && !"null".equals(detail.getIdNo())) {
                    HrWelfareCompensation hrWelfareCompensation = new HrWelfareCompensation()
                        .setIdNo(detail.getIdNo())
                        .setStaffId(detail.getStaffId())
                        .setClientId(detail.getClientId())
                        .setPayYear(resultDTO.getPayYear())
                        .setPayMonthly(resultDTO.getPayMonthly())
                        .setType(plateType.getKey() + 1)
                        .setIsUsed(BillEnum.MakeupIsUsed.NOT_USED.getKey());
                    // 处理旧数据
                    oldDataList.add(hrWelfareCompensation);
                    HrWelfareCompensationRecord record = new HrWelfareCompensationRecord();
                    StringBuilder sb = new StringBuilder(title);
                    //处理福利补差以及补差操作日志
                    this.handleWelfareCompensation(sb, detail, record, hrWelfareCompensation, plateType);
                    // 保存补差日志
                    if (!sb.toString().equals(title)) {
                        record.setChangeMsg(sb.toString());
                        record.setId(RandomUtil.generateId());
                        recordList.add(record);
                        hrWelfareCompensation.setBillResultId(resultDTO.getId()).setRecordId(record.getId());
                        newDataList.add(hrWelfareCompensation);
                    }
                }
            });
        }
        if (newDataList == null || newDataList.isEmpty()) {
            throw new CommonException("未查询到对账结果明细!");
        }
        //删除旧数据
        if (oldDataList != null && !oldDataList.isEmpty()) {
            this.hrWelfareCompensationRepository.deleteBatch(oldDataList);
        }
        //添加补差操作日志
        hrWelfareCompensationRecordService.saveBatch(recordList);
        //添加补差明细
        hrWelfareCompensationService.saveBatch(newDataList);
        HrBillCompareResultDTO resultDTO = new HrBillCompareResultDTO();
        resultDTO.setStatus(1).setLockStatus(1).setCurrentMonthUsed(currentMonthUsed).setIds(resultIds);
        hrBillCompareResultRepository.updateByObject(resultDTO);
    }

    /**
     * 个税批量保存补差
     *
     * @param billConfigIds    对账配置ID
     * @param currentMonthUsed 是否当月使用补差
     */
    @Override
    public void savePersonalTaxMakeUp(List<String> billConfigIds, Integer currentMonthUsed) {
        HrBillCompareResultDTO compareResultDTO = new HrBillCompareResultDTO();
        compareResultDTO.setBillCompareConfigIds(billConfigIds);
        List<HrBillCompareResultDTO> list = hrBillCompareResultRepository.findList(compareResultDTO);
        if (list == null || list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        this.saveBatchCompareMakeUp(list, currentMonthUsed);
    }

    /**
     * 处理福利补差以及补差操作日志
     *
     * @param sb                    补差描述
     * @param detail                对账明细
     * @param record                补差操作日志对象
     * @param hrWelfareCompensation 福利补差对账
     * @param plateType             对账类型
     */
    private void handleWelfareCompensation(StringBuilder sb, HrBillCompareResultDetailDTO detail, HrWelfareCompensationRecord record, HrWelfareCompensation hrWelfareCompensation, BillEnum.GuidePlateType plateType) {
        HrBillCompareHeaderDTO systemData = detail.getSystemDataObject();
        HrBillCompareHeaderDTO sourceData = detail.getSourceDataObject();
        HrBillCompareHeaderDTO compareResult = detail.getCompareResultObject();
        Map<String, HrBillCompareHeaderDTO.ComparePart> systemCollect = this.getComparePartCollect(systemData);
        Map<String, HrBillCompareHeaderDTO.ComparePart> excelCollect = this.getComparePartCollect(sourceData);
        Map<String, HrBillCompareHeaderDTO.ComparePart> compareCollect = this.getComparePartCollect(compareResult);

        // 比较数据是否一致
        systemCollect.forEach((k, system) -> {
            HrBillCompareHeaderDTO.ComparePart excel = excelCollect.get(k);
            if (!system.getFieldValue().equals(excel.getFieldValue())) {
                sb.append("[").append(system.getFieldName()).append("] 业务数据为:").append(system.getFieldValue()).append(", Excel数据为:").append(excel.getFieldValue()).append(", 需要补差:").append(compareCollect.get(k).getFieldValue()).append("; ");
                DynamicFeeTypesEnum typesEnum = DynamicFeeTypesEnum.getByValue(system.getFieldName());
                // 设置补差对象的明细属性
                if (typesEnum != null) {
                    this.setWelfwareField(hrWelfareCompensation, new BigDecimal(compareCollect.get(k).getFieldValue()), typesEnum);
                }
            }
        });
        if (!systemData.getUnitLateFee().equals(sourceData.getUnitLateFee())) {
            sb.append("[滞纳金] 系统数据为:").append(systemData.getUnitLateFee()).append(", Excel数据为:").append(sourceData.getUnitLateFee()).append(", 需要补差:").append(compareResult.getUnitLateFee()).append("; ");
            record.setUnitLateFeeSystem(new BigDecimal(systemData.getUnitLateFee()));
            record.setUnitLateFeeSource(new BigDecimal(sourceData.getUnitLateFee()));
            this.setWelfwareField(hrWelfareCompensation, new BigDecimal(compareResult.getUnitLateFee()), DynamicFeeTypesEnum.UNIT_LATE_FEE);
        }
        if (StringUtils.isNotBlank(sourceData.getPersonalTax()) && !systemData.getPersonalTax().equals(sourceData.getPersonalTax())) {
            sb.append("[个税] 系统数据为:").append(systemData.getPersonalTax()).append(", Excel数据为:").append(sourceData.getPersonalTax()).append(", 需要补差:").append(compareResult.getPersonalTax()).append("; ");
            record.setPersonalTaxSystem(new BigDecimal(systemData.getPersonalTax()));
            record.setPersonalTaxSource(new BigDecimal(sourceData.getPersonalTax()));
            this.setWelfwareField(hrWelfareCompensation, new BigDecimal(compareResult.getPersonalTax()), DynamicFeeTypesEnum.PERSONAL_TAX);
        }
        // 设置补差对象的小计,总计等属性
        this.setWelfwareTotalField(plateType, hrWelfareCompensation);
    }

    /**
     * 海尔对账保存对账结果
     *
     * @param billConfigIds
     */
    @Override
    public void haierSaveCompareResult(List<String> billConfigIds) {
        //保存海尔对账结果删除之前数据
        List<HrBillCompareConfigDTO> list = new ArrayList<>();
        billConfigIds.forEach(id -> {
            HrBillCompareConfigDTO dto = hrBillCompareConfigRepository.findById(id);
            list.add(dto);
        });
        HrBillCompareConfigDTO hrBillCompareConfigDTO = list.get(0);
        List<String> centerName = list.stream().map(HrBillCompareConfigDTO::getCenterName).distinct().collect(Collectors.toList());
        QueryWrapper<HrBillCompareConfig> qw = new QueryWrapper<>();
        qw.eq("pay_year", hrBillCompareConfigDTO.getPayYear());
        qw.eq("pay_monthly", hrBillCompareConfigDTO.getPayMonthly());
        qw.in("center_name", centerName);
        List<HrBillCompareConfig> hrBillCompareConfigs = hrBillCompareConfigRepository.selectList(qw);
        if (CollectionUtils.isNotEmpty(hrBillCompareConfigs)) {
            //删除上一次数据
            List<String> configIds = hrBillCompareConfigs.stream().map(HrBillCompareConfig::getId).collect(Collectors.toList());
            List<HrBillCompareResult> hrBillCompareResultList = hrBillCompareResultRepository.selectList(new QueryWrapper<HrBillCompareResult>().in("bill_compare_config_id", configIds));
            List<String> resultIds = hrBillCompareResultList.stream().map(HrBillCompareResult::getId).collect(Collectors.toList());
            hrBillCompareResultDetailRepository.delByResultId(resultIds);
            hrBillCompareResultRepository.deleteBatchIds(resultIds);
            hrBillCompareConfigRepository.delByBillIdAndType(null, BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey(), 1, configIds);
        }
        //保存当前数据
        hrBillCompareConfigRepository.delByBillIdAndType(null, BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey(), 0, billConfigIds);
    }

    /**
     * 对账结果,数值不同时,设置颜色
     *
     * @param workbook
     * @param cellItems
     * @param compareResultList
     */
    private void parseWorkBookColor(Workbook workbook, List<CellItem> cellItems, List<Map<String, Object>> compareResultList) {
        Sheet sheet = workbook.getSheetAt(0);

        // 获取列的索引
        Map<String, Integer> colIndex = new HashMap<>();
        for (int i = 0; i < cellItems.size(); i++) {
            colIndex.put(cellItems.get(i).getKey(), i);
        }

        for (int x = 0; x < compareResultList.size(); x++) {
            Map<String, Object> map = compareResultList.get(x);
            // 获取数据不同项
            Object obj = map.get(FIELD_DIFF);
            if (obj == null) {
                continue;
            }
            List<String> diffFields = JSONArray.parseArray(obj.toString(), String.class);
            // 数据行从第三行开始,索引从2开始
            Row row = sheet.getRow(x + 2);
            for (String diffField : diffFields) {
                // 获取该字段的列索引
                Integer index = colIndex.get(diffField);
                Cell cell = row.getCell(index);
                // 设置红色字体
                Font font = workbook.createFont();
                font.setColor(Font.COLOR_RED);

                CellStyle cellStyle = workbook.createCellStyle();
                BeanUtils.copyProperties(cell.getCellStyle(), cellStyle);
                cellStyle.setFont(font);

                cell.setCellStyle(cellStyle);
            }
        }

    }

    /**
     * 导盘比较数据,并保存补差
     *
     * @param compareConfigDTO
     * @param dataMaps
     */
    private void compareAndGetMakeUp(HrBillCompareConfigDTO compareConfigDTO, Map<String, List<Map<String, Object>>> dataMaps) {
        // 根据导盘类型获取补差项
        List<DynamicFeeTypesEnum> makeUpItems = this.getMakeUpItemsByType(compareConfigDTO.getType());
        List<String> allMakeUpFields = new ArrayList<>();
        makeUpItems.forEach(item -> {
            allMakeUpFields.add(item.getFieldName());
        });
        for (String idCard : dataMaps.keySet()) {
            List<Map<String, Object>> vals = dataMaps.get(idCard);
            if (vals.size() != 2) {
                continue;
            }
            Map<String, Object> systemMap = null;
            Map<String, Object> excelMap = null;
            for (Map<String, Object> map : vals) {
                if (map.get(FIELD_TYPE).toString().equals(DATA_TYPE_SYSTEM)) {
                    systemMap = map;
                } else {
                    excelMap = map;
                }
            }
            Object o = systemMap.get(FIELD_DIFF);
            if (o == null) {
                continue;
            }
            List<String> diffFields = (List<String>) o;

            // 获取差异项
            List<String> mydiffs = new ArrayList<>();
            for (String str : diffFields) {
                if (allMakeUpFields.contains(str)) {
                    mydiffs.add(str);
                }
            }

            if (mydiffs.size() == 0) {
                continue;
            }

            this.saveOrUpdateWelfare(compareConfigDTO, idCard, systemMap, excelMap, mydiffs);

        }

    }

    /**
     * 数据对比获取补差对象
     *
     * @param compareConfigDTO 对账配置
     * @param idCard           身份证号码
     * @param systemMap        业务数据
     * @param excelMap         excel数据
     * @param mydiffs          补差项
     */
    private void saveOrUpdateWelfare(HrBillCompareConfigDTO compareConfigDTO, String idCard, Map<String, Object> systemMap, Map<String, Object> excelMap, List<String> mydiffs) {
        // 获取基础账单信息
        HrBillDTO hrBillDTO = this.hrBillRepository.getBillInfoById(compareConfigDTO.getBillId());
        // 根据身份证获取员工id
        HrTalentStaff talentStaff = this.hrTalentStaffRepository.findStaffInfo(idCard);
//
//        // 根据账单id和员工id获取补差配置
//        QueryWrapper<HrWelfareCompensation> qw = new QueryWrapper<>();
//        qw.eq("client_id", hrBillDTO.getClientId());
//        qw.eq("staff_id", talentStaff.getId());
//        qw.eq("bill_id", hrBillDTO.getId());
//        HrWelfareCompensation hrWelfareCompensation = this.hrWelfareCompensationRepository.selectOne(qw);
//        if(hrWelfareCompensation == null) {
//            hrWelfareCompensation = new HrWelfareCompensation();
//        }
        // 获取导盘类型
        BillEnum.GuidePlateType plateType = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, compareConfigDTO.getType());

        HrWelfareCompensation hrWelfareCompensation = new HrWelfareCompensation();
        StringBuilder sb = new StringBuilder(plateType.getValue()).append("对账.");

        for (String field : mydiffs) {
            Object systemVal = systemMap.get(field);
            Object excelVal = excelMap.get(field);
            BigDecimal systemDecimal = BillParseUtils.objectToDecimal(systemVal);
            BigDecimal excelDecimal = BillParseUtils.objectToDecimal(excelVal);
            BigDecimal makeUpVal = excelDecimal.subtract(systemDecimal);

            // 根据字段的类型为补差对象赋值
            DynamicFeeTypesEnum typesEnum = EnumUtils.getEnum(DynamicFeeTypesEnum.class, "getFieldName", field);
            sb.append("【").append(typesEnum.getValue()).append("】 业务数据为:").append(systemDecimal)
                .append(", excel数据为:").append(excelDecimal).append(", 需要补差:").append(makeUpVal).append("; ");

            // 设置补差对象的明细属性
            this.setWelfwareField(hrWelfareCompensation, makeUpVal, typesEnum);
        }
        // 设置补差对象的小计,总计等属性
        this.setWelfwareTotalField(plateType, hrWelfareCompensation);

        // 保存补差日志
        HrWelfareCompensationRecord record = new HrWelfareCompensationRecord()
            .setChangeMsg(sb.toString());
        this.welfareCompensationRecordRepository.insert(record);

        if (StringUtils.isEmpty(hrWelfareCompensation.getId())) {
            hrWelfareCompensation
                .setClientId(hrBillDTO.getClientId())
                .setBillResultId(compareConfigDTO.getBillResultId())
                .setIdNo(talentStaff.getCertificateNum())
                .setStaffId(talentStaff.getId())
                .setPayYear(hrBillDTO.getPayYear())
                .setPayMonthly(hrBillDTO.getPayMonthly())
                .setType(BillEnum.MakeupType.BILL_CHECK.getKey())
                .setIsUsed(BillEnum.MakeupIsUsed.NOT_USED.getKey())
                .setRecordId(record.getId())
            ;
            this.hrWelfareCompensationRepository.insert(hrWelfareCompensation);
        } else {
            this.hrWelfareCompensationRepository.updateById(hrWelfareCompensation);
        }
    }

    /**
     * 设置补差对象的小计,总计等属性
     *
     * @param plateType
     * @param hrWelfareCompensation
     */
    private void setWelfwareTotalField(BillEnum.GuidePlateType plateType, HrWelfareCompensation hrWelfareCompensation) {
        switch (plateType) {
            case MEDICAL_GUIDE:
            case SOCIAL_SECURITY_GUIDE:
                // 社保类型: 设置个人补差小计, 单位补差小计, 社保补差总计
                hrWelfareCompensation.setPersonalSubtotal(CalculateUtils.decimalListAddition(
                    hrWelfareCompensation.getPersonalPension(),
                    hrWelfareCompensation.getPersonalUnemployment(),
                    hrWelfareCompensation.getPersonalMedical(),
                    hrWelfareCompensation.getPersonalMaternity(),
                    hrWelfareCompensation.getPersonalLargeMedical()
                ));
                hrWelfareCompensation.setUnitSubtotal(CalculateUtils.decimalListAddition(
                    hrWelfareCompensation.getUnitPension(),
                    hrWelfareCompensation.getUnitUnemployment(),
                    hrWelfareCompensation.getUnitMedical(),
                    hrWelfareCompensation.getUnitInjury(),
                    hrWelfareCompensation.getUnitMaternity(),
                    hrWelfareCompensation.getUnitLargeMedical(),
                    hrWelfareCompensation.getReplenishWorkInjury(),
                    hrWelfareCompensation.getUnitLateFee()
                ));
                hrWelfareCompensation.setSocialSecurityTotal(CalculateUtils.decimalListAddition(
                    hrWelfareCompensation.getPersonalSubtotal(),
                    hrWelfareCompensation.getUnitSubtotal()
                ));
                break;
            case ACCUMULATION_FUND_GUIDE:
                // 公积金导盘: 设置公积金补差总计
                hrWelfareCompensation.setAccumulationFundTotal(CalculateUtils.decimalListAddition(
                    hrWelfareCompensation.getPersonalAccumulationFund(),
                    hrWelfareCompensation.getUnitAccumulationFund()
                ));
                break;
            default:
                break;
        }
    }


    /**
     * 设置补差对象的属性
     *
     * @param hrWelfareCompensation 补差对象
     * @param makeUpVal             补差值
     * @param typesEnum             费用项类型
     */
    private void setWelfwareField(HrWelfareCompensation hrWelfareCompensation, BigDecimal makeUpVal, DynamicFeeTypesEnum typesEnum) {

        switch (typesEnum) {
            case UNIT_ACCUMULATION_FUND:
                // 单位公积金补差
                hrWelfareCompensation.setUnitAccumulationFund(makeUpVal);
                break;
            case PERSONAL_ACCUMULATION_FUND:
                // 个人公积金补差
                hrWelfareCompensation.setPersonalAccumulationFund(makeUpVal);
                break;
            case UNIT_PENSION:
                // 单位养老
                hrWelfareCompensation.setUnitPension(makeUpVal);
                break;
            case UNIT_UNEMPLOYMENT:
                // 单位失业
                hrWelfareCompensation.setUnitUnemployment(makeUpVal);
                break;
            case PERSONAL_PENSION:
                // 个人养老
                hrWelfareCompensation.setPersonalPension(makeUpVal);
                break;
            case PERSONAL_UNEMPLOYMENT:
                // 个人失业
                hrWelfareCompensation.setPersonalUnemployment(makeUpVal);
                break;
            case UNIT_MEDICAL:
                // 单位医疗
                hrWelfareCompensation.setUnitMedical(makeUpVal);
                break;
            case UNIT_INJURY:
                // 单位工伤
                hrWelfareCompensation.setUnitInjury(makeUpVal);
                break;
            case PERSONAL_MEDICAL:
                // 个人医疗
                hrWelfareCompensation.setPersonalMedical(makeUpVal);
                break;
            case PERSONAL_TAX:
                // 个税
                hrWelfareCompensation.setPersonalTax(makeUpVal);
                break;
            case UNIT_MATERNITY:
                // 单位生育
                hrWelfareCompensation.setUnitMaternity(makeUpVal);
                break;
            case PERSONAL_MATERNITY:
                // 个人生育
                hrWelfareCompensation.setPersonalMaternity(makeUpVal);
                break;
            case UNIT_LATE_FEE:
                // 滞纳金
                hrWelfareCompensation.setUnitLateFee(makeUpVal);
                break;
            default:
                break;
        }
    }


    /**
     * 根据导盘类型获取补差项
     *
     * @param type 导盘类型
     * @return
     */
    private List<DynamicFeeTypesEnum> getMakeUpItemsByType(Integer type) {
        List<DynamicFeeTypesEnum> typesEnums = new ArrayList<>();
        BillEnum.GuidePlateType plateType = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, type);
        switch (plateType) {
            // 社保
            case SOCIAL_SECURITY_GUIDE:
                typesEnums = Arrays.asList(
                    DynamicFeeTypesEnum.UNIT_PENSION,
                    DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT,
                    DynamicFeeTypesEnum.PERSONAL_PENSION,
                    DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT
                );
                break;
            // 医保
            case MEDICAL_GUIDE:
                typesEnums = Arrays.asList(
                    DynamicFeeTypesEnum.UNIT_MEDICAL,
                    DynamicFeeTypesEnum.UNIT_INJURY,
                    DynamicFeeTypesEnum.PERSONAL_MEDICAL
                );
                break;
            // 公积金
            case ACCUMULATION_FUND_GUIDE:
                typesEnums = Arrays.asList(
                    DynamicFeeTypesEnum.PERSONAL_ACCUMULATION_FUND,
                    DynamicFeeTypesEnum.UNIT_ACCUMULATION_FUND
                );
                break;
            case PERSONAL_TAX_GUIDE:
                typesEnums = Arrays.asList(
                    DynamicFeeTypesEnum.PERSONAL_TAX
                );
            default:
                break;
        }
        return typesEnums;
    }

    /**
     * 海尔对账获取对账结果
     *
     * @param systemMapDatas
     * @param excelDataDTOMap
     * @param type
     * @param compareResult
     * @param paymentDate
     * @param staffCollect
     * @return
     */
    private List<HrBillCompareResultDetailDTO> getCompareResultSpecial(Map<String, Map<String, Object>> systemMapDatas,
                                                                       Map<String, BillExcelDataDTO> excelDataDTOMap, Integer type,
                                                                       HrBillCompareResult compareResult, String paymentDate,
                                                                       List<HrBillCompareResultDetailDTO> detailDTOList, Map<String, HrTalentStaffDTO> staffCollect) {
        compareResult.setId(RandomUtil.generateId());
        List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailDTOList = new ArrayList<>();
        List<HrBillCompareResultDetail> hrBillCompareResultDetailList = new ArrayList<>();
        // 根据导盘类型初始化对比项
        BillEnum.GuidePlateType plateType = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, type);
        if (plateType == null) {
            throw new CommonException("无法识别的导盘类型!");
        }
        // 根据导盘获取比较项
        List<DynamicFeeTypesEnum> compareFields = this.getDynamicFeeTypesByType(type);

        Set<String> keySet = excelDataDTOMap.keySet();
        // 查询用户所属客户信息
        List<String> idCardList = new ArrayList<>(keySet);
        int diffNums = 0;
        String[] split = paymentDate.split("-");
        Integer payYear = Integer.parseInt(split[0]);
        Integer payMonth = Integer.parseInt(split[1]);
        if (detailDTOList == null) {
            detailDTOList = new ArrayList<>();
        }
        for (String idCard : keySet) {
            HrBillCompareResultDetail hrBillCompareResultDetail = new HrBillCompareResultDetail();
            hrBillCompareResultDetail.setBillCompareResultId(compareResult.getId());
            hrBillCompareResultDetail.setIdNo(idCard);
            hrBillCompareResultDetail.setPayYear(payYear);
            hrBillCompareResultDetail.setPayMonthly(payMonth);
            HrTalentStaffDTO hrTalentStaffDTO = staffCollect.get(idCard);
            if (hrTalentStaffDTO != null) {
                hrBillCompareResultDetail.setStaffId(hrTalentStaffDTO.getId());
                hrBillCompareResultDetail.setUnitNumber(hrTalentStaffDTO.getUnitNumber());
                hrBillCompareResultDetail.setClientName(hrTalentStaffDTO.getClientName());
                hrBillCompareResultDetail.setStaffName(hrTalentStaffDTO.getName());
                hrBillCompareResultDetail.setSystemNum(hrTalentStaffDTO.getSystemNum());
                hrBillCompareResultDetail.setArea(hrTalentStaffDTO.getSocialSecurityArea());
            }

            BillExcelDataDTO excelData = excelDataDTOMap.get(idCard);
            hrBillCompareResultDetail.setStaffName(excelData.getName());
            hrBillCompareResultDetail.setCenterName(excelData.getCenterName());
            Map<String, Object> excelMap = excelData.getMapDatas();
            // 组装Excel源数据
            HrBillCompareHeaderDTO sourceData = this.dealCompareParts(excelMap);
            hrBillCompareResultDetail.setSourceTotal(new BigDecimal(sourceData.getTotalAmount()));
            hrBillCompareResultDetail.setSourceData(JSON.toJSONString(sourceData));
            Map<String, Object> systemMap = systemMapDatas.get(idCard);
            HrBillCompareHeaderDTO systemData;
            if (CollectionUtils.isNotEmpty(systemMap)) {
                hrBillCompareResultDetail.setStaffId(String.valueOf(systemMap.get("staffId")));
                hrBillCompareResultDetail.setStaffName(String.valueOf(systemMap.get(DynamicFeeTypesEnum.NAME.getFieldName())));
                hrBillCompareResultDetail.setSystemNum(String.valueOf(systemMap.get(DynamicFeeTypesEnum.SYSTEM_NUM.getFieldName())));
                hrBillCompareResultDetail.setArea(String.valueOf(systemMap.get(DynamicFeeTypesEnum.SOCIAL_SECURITY_AREA.getFieldName())));
                // 组装系统数据
                systemData = this.dealCompareParts(systemMap);
                hrBillCompareResultDetail.setSystemData(JSON.toJSONString(systemData));
                hrBillCompareResultDetail.setSystemTotal(new BigDecimal(systemData.getTotalAmount()));
                // 存在不同数据的时候,保存到结果
                if (!this.compareMap(systemMap, excelMap, compareFields)) {
                    diffNums = diffNums + 1;
                }
                hrBillCompareResultDetail.setUsedType(1);
            } else {
                diffNums = diffNums + 1;
                // 组装系统源数据(空数据)
                systemData = new HrBillCompareHeaderDTO();
                systemData.setTotal("0.00");
                systemData.setUnitLateFee("0.00");
                systemData.setPensionCompareParts(this.dealBlankData(sourceData.getPensionCompareParts()));
                systemData.setUnemploymentCompareParts(this.dealBlankData(sourceData.getUnemploymentCompareParts()));
                systemData.setInjuryCompareParts(this.dealBlankData(sourceData.getInjuryCompareParts()));
                systemData.setMedicalCompareParts(this.dealBlankData(sourceData.getMedicalCompareParts()));
                systemData.setMaternityCompareParts(this.dealBlankData(sourceData.getMaternityCompareParts()));
                systemData.setSubCompareParts(this.dealBlankData(sourceData.getSubCompareParts()));
                systemData.setAccumulationFundCompareParts(this.dealBlankData(sourceData.getAccumulationFundCompareParts()));
                hrBillCompareResultDetail.setSystemData(JSON.toJSONString(systemData));
                hrBillCompareResultDetail.setSystemTotal(new BigDecimal(systemData.getTotal()));
                //添加对账但在系统中不存在的员工
                hrBillCompareResultDetail.setUsedType(staffCollect.get(idCard) == null ? 3 : 4);
            }
            // 组装差异数据
            HrBillCompareHeaderDTO compareData = this.dealCalCompareParts(systemData, sourceData, type);
            hrBillCompareResultDetail.setCompareTotal(new BigDecimal(compareData.getTotalAmount()));
            hrBillCompareResultDetail.setCompareResult(JSON.toJSONString(compareData));
            hrBillCompareResultDetail.setId(RandomUtil.generateId());
            hrBillCompareResultDetailList.add(hrBillCompareResultDetail);
            hrBillCompareResultDetailDTOList.add(this.hrBillCompareResultDetailMapper.toDto(hrBillCompareResultDetail));
            HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO = this.hrBillCompareResultDetailMapper.toDto(hrBillCompareResultDetail);
            if (hrTalentStaffDTO != null) {
                hrBillCompareResultDetailDTO.setStaffStatus(hrTalentStaffDTO.getStaffStatus());
                hrBillCompareResultDetailDTO.setAgreementNumber(hrTalentStaffDTO.getAgreementNumber());
            }
            detailDTOList.add(hrBillCompareResultDetailDTO);
        }
        if (hrBillCompareResultDetailList != null) {
            hrBillCompareResultDetailRepository.saveBatch(hrBillCompareResultDetailList);
        }
        compareResult.setDiffDataNums(diffNums);
        return hrBillCompareResultDetailDTOList;
    }

    /**
     * 获取对账结果
     *
     * @param systemMapDatas         系统业务数据
     * @param excelDataDTOMap        导盘Excel
     * @param type                   对账类型
     * @param integer                0批量个税对账
     * @param hrBillCompareConfigDTO
     * @return
     */
    private List<HrBillCompareResultDetailDTO> getCompareResult(Map<String, Map<String, Object>> systemMapDatas, Map<String, BillExcelDataDTO> excelDataDTOMap, Integer type,
                                                                HrBillCompareResult compareResult, Integer integer, HrBillCompareConfigDTO hrBillCompareConfigDTO, Map<String, HrTalentStaffDTO> staffCollect) {
        if (compareResult.getId() == null) {
            compareResult.setId(RandomUtil.generateId());
        }
        List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailDTOList = new ArrayList<>();
        // 根据导盘类型初始化对比项
        BillEnum.GuidePlateType plateType = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, type);
        if (plateType == null) {
            throw new CommonException("无法识别的导盘类型!");
        }
        // 根据导盘获取比较项
        List<DynamicFeeTypesEnum> compareFields = this.getDynamicFeeTypesByType(type);

        Set<String> keySet = systemMapDatas.keySet();
        // 查询用户所属客户信息

        int diffNums = 0;
        List<HrBillCompareResultDetailDTO> detailDTOList = hrBillCompareResultDetailRepository.selectHrBillCompareResultDetailByBillCompareResultId(compareResult.getId());
        String[] split = hrBillCompareConfigDTO.getPaymentDate().split("-");
        int payYear = Integer.parseInt(split[0]);
        int payMonthly = Integer.parseInt(split[1]);
        List<HrBillCompareResultDetail> saveBatchList = new ArrayList<>();
        List<HrBillCompareResultDetail> updateBatchList = new ArrayList<>();
        for (String idCard : keySet) {
            Map<String, Object> systemMap = systemMapDatas.get(idCard);
            HrBillCompareResultDetail hrBillCompareResultDetail = new HrBillCompareResultDetail();
            hrBillCompareResultDetail.setBillCompareResultId(compareResult.getId());
            hrBillCompareResultDetail.setStaffId(String.valueOf(systemMap.get("staffId")));
            hrBillCompareResultDetail.setIdNo(idCard);
            hrBillCompareResultDetail.setStaffName(String.valueOf(systemMap.get(DynamicFeeTypesEnum.NAME.getFieldName())));
            hrBillCompareResultDetail.setArea(String.valueOf(systemMap.get(DynamicFeeTypesEnum.SOCIAL_SECURITY_AREA.getFieldName())));
            hrBillCompareResultDetail.setPayYear(payYear);
            hrBillCompareResultDetail.setPayMonthly(payMonthly);
            HrTalentStaffDTO hrTalentStaffDTO = staffCollect.get(idCard);
            if (hrTalentStaffDTO != null) {
                hrBillCompareResultDetail.setUnitNumber(hrTalentStaffDTO.getUnitNumber());
                hrBillCompareResultDetail.setClientName(hrTalentStaffDTO.getClientName());
                hrBillCompareResultDetail.setArea(hrTalentStaffDTO.getSocialSecurityArea());
                hrBillCompareResultDetail.setSystemNum(hrTalentStaffDTO.getSystemNum());
            }
            // 组装系统数据
            HrBillCompareHeaderDTO systemData = this.dealCompareParts(systemMap, type);
            hrBillCompareResultDetail.setSystemData(JSON.toJSONString(systemData));
            hrBillCompareResultDetail.setSystemTotal(new BigDecimal(systemData.getTotal()));
            BillExcelDataDTO excelData = excelDataDTOMap.get(idCard);
            Map<String, Object> excelMap;
            HrBillCompareHeaderDTO sourceData;
            if (excelData != null && excelData.getMapDatas() != null && excelData.getMapDatas().size() > 0) {
                hrBillCompareResultDetail.setCenterName(excelData.getCenterName());
                hrBillCompareResultDetail.setUnitNo(excelData.getUnitNo());
                excelMap = excelData.getMapDatas();
                // 组装Excel源数据
                sourceData = this.dealCompareParts(excelMap, type);
                hrBillCompareResultDetail.setSourceData(JSON.toJSONString(sourceData)).setUsedType(1);
                // 填充导盘数据合计，用于自动生成报销申请，2025/5/12
                hrBillCompareResultDetail.setSourceTotal(new BigDecimal(sourceData.getTotal()));
                // 个税导盘处理
                this.dealWithTaxGuide(systemMap, excelMap, plateType);
                // 存在不同数据的时候,保存到结果
                if (!this.compareMap(systemMap, excelMap, compareFields)) {
                    diffNums = diffNums + 1;
                }
            } else {
                //本次对账未有某个员工的信息，但上一次有并且该员工是正常对账不处理
                HrBillCompareResultDetailDTO detailDTO = detailDTOList.stream().filter(lst -> lst.getIdNo().equals(idCard)).findAny().orElse(null);
                if (detailDTO != null && detailDTO.getUsedType() != null && detailDTO.getUsedType() == 1) {
                    continue;
                }
                diffNums = diffNums + 1;
                // 组装Excel源数据(空数据)
                sourceData = new HrBillCompareHeaderDTO();
                sourceData.setTotal("0.00");
                // sourceData.setUnitLateFee("0.00");
                sourceData.setUnitPart(this.dealBlankData(systemData.getUnitPart()));
                sourceData.setPersonalPart(this.dealBlankData(systemData.getPersonalPart()));
                hrBillCompareResultDetail.setSourceData(JSON.toJSONString(sourceData)).setUsedType(2);
                // 填充导盘数据合计，用于自动生成报销申请，2025/5/12
                hrBillCompareResultDetail.setSourceTotal(new BigDecimal(sourceData.getTotal()));
            }
            // 组装差异数据
            HrBillCompareHeaderDTO compareData = this.dealCalCompareParts(systemData, sourceData, type);
            hrBillCompareResultDetail.setCompareTotal(new BigDecimal(compareData.getTotal()));
            hrBillCompareResultDetail.setCompareResult(JSON.toJSONString(compareData));
            hrBillCompareResultDetail.setId(RandomUtil.generateId());
            this.saveOrUpdateResultDetail(idCard, detailDTOList, hrBillCompareResultDetail, saveBatchList, updateBatchList);
            hrBillCompareResultDetailDTOList.add(this.hrBillCompareResultDetailMapper.toDto(hrBillCompareResultDetail));
            // 从excel数据中移除存在的系统数据
            excelDataDTOMap.remove(idCard);
        }
        diffNums = this.dealWithMissSystemData(excelDataDTOMap, hrBillCompareResultDetailDTOList, diffNums, compareResult.getId(), type, detailDTOList, staffCollect, payYear, payMonthly, saveBatchList, updateBatchList);
        if (saveBatchList != null && !saveBatchList.isEmpty()) {
            hrBillCompareResultDetailRepository.saveBatch(saveBatchList);
        }
        if (updateBatchList != null && !updateBatchList.isEmpty()) {
            hrBillCompareResultDetailRepository.updateBatch(updateBatchList);
        }
        compareResult.setDiffDataNums(diffNums);
        return hrBillCompareResultDetailDTOList;
    }

    /**
     * 添加或修改到账记录
     *
     * @param idCard                    员工身份证
     * @param detailDTOList             已经对账的信息
     * @param hrBillCompareResultDetail 此次对账信息
     * @param saveBatchList             数据集合
     * @param updateBatchList
     */
    private void saveOrUpdateResultDetail(String idCard, List<HrBillCompareResultDetailDTO> detailDTOList, HrBillCompareResultDetail hrBillCompareResultDetail,
                                          List<HrBillCompareResultDetail> saveBatchList, List<HrBillCompareResultDetail> updateBatchList) {
        //处理之前已经对账的员工信息 以最后一次对账数据为准
        if (detailDTOList != null && !detailDTOList.isEmpty()) {
            HrBillCompareResultDetailDTO detailDTO = detailDTOList.stream().filter(lst -> lst.getIdNo().equals(idCard)).findAny().orElse(null);
            if (detailDTO != null) {
                hrBillCompareResultDetail.setId(detailDTO.getId());
                updateBatchList.add(hrBillCompareResultDetail);
            } else {
                saveBatchList.add(hrBillCompareResultDetail);
            }
        } else {
            saveBatchList.add(hrBillCompareResultDetail);
        }
    }

    private HrBillCompareHeaderDTO dealCompareParts(Map<String, Object> dateMap) {
        HrBillCompareHeaderDTO systemData = new HrBillCompareHeaderDTO();
        List<HrBillCompareHeaderDTO.ComparePart> pensionCompareParts = new ArrayList<>();
        List<HrBillCompareHeaderDTO.ComparePart> unemploymentCompareParts = new ArrayList<>();
        List<HrBillCompareHeaderDTO.ComparePart> injuryCompareParts = new ArrayList<>();
        List<HrBillCompareHeaderDTO.ComparePart> medicalCompareParts = new ArrayList<>();
        List<HrBillCompareHeaderDTO.ComparePart> maternityCompareParts = new ArrayList<>();
        List<HrBillCompareHeaderDTO.ComparePart> accumulationFundCompareParts = new ArrayList<>();
        List<HrBillCompareHeaderDTO.ComparePart> subCompareParts = new ArrayList<>();
        AtomicReference<BigDecimal> unitSubtotal = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> personalSubtotal = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> subTotal = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> accumulationFundTotal = new AtomicReference<>(BigDecimal.ZERO);
        dateMap.forEach((k, v) -> {
            if (v == null) {
                v = "0.00";
            }
            HrBillCompareHeaderDTO.ComparePart comparePart = new HrBillCompareHeaderDTO.ComparePart();
            DynamicFeeTypesEnum enumByKey = DynamicFeeTypesEnum.getEnumByFieldName(k);
            int sortValue = 0;
            if (enumByKey != null) {
                switch (enumByKey) {
                    case UNIT_PENSION_CARDINAL:
                    case PERSONAL_PENSION_CARDINAL:
                    case UNIT_PENSION:
                    case PERSONAL_PENSION:
                    case UNIT_LATE_FEE_HAIER:
                        if (enumByKey.equals(DynamicFeeTypesEnum.UNIT_PENSION)) {
                            unitSubtotal.set(CalculateUtils.objectAddition(unitSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 3;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_PENSION)) {
                            personalSubtotal.set(CalculateUtils.objectAddition(personalSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 4;
                        } else {
                            sortValue = enumByKey.equals(DynamicFeeTypesEnum.UNIT_PENSION_CARDINAL) ? 1 : (enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_PENSION_CARDINAL) ? 2 : 5);
                        }
                        this.dealComparePart(comparePart, enumByKey, v, pensionCompareParts, sortValue);
                        break;
                    case MEDICAL_INSURANCE_CARDINAL:
                    case MEDICAL_INSURANCE_CARDINAL_PERSONAL:
                    case UNIT_MEDICAL:
                    case PERSONAL_MEDICAL:
                    case UNIT_LARGE_MEDICAL_EXPENSE_HAIER:
                    case PERSONAL_LARGE_MEDICAL_EXPENSE_HAIER:
                        if (enumByKey.equals(DynamicFeeTypesEnum.UNIT_MEDICAL)) {
                            unitSubtotal.set(CalculateUtils.objectAddition(unitSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 3;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_MEDICAL)) {
                            personalSubtotal.set(CalculateUtils.objectAddition(personalSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 4;
                        } else {
                            sortValue = enumByKey.equals(DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL) ? 1 : (enumByKey.equals(DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL_PERSONAL) ? 2 : (enumByKey.equals(DynamicFeeTypesEnum.UNIT_LARGE_MEDICAL_EXPENSE_HAIER) ? 5 : 6));

                        }
                        this.dealComparePart(comparePart, enumByKey, v, medicalCompareParts, sortValue);
                        break;
                    case UNIT_UNEMPLOYMENT_CARDINAL:
                    case PERSONAL_UNEMPLOYMENT_CARDINAL:
                    case UNIT_UNEMPLOYMENT:
                    case PERSONAL_UNEMPLOYMENT:
                        if (enumByKey.equals(DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT)) {
                            unitSubtotal.set(CalculateUtils.objectAddition(unitSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 3;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT)) {
                            personalSubtotal.set(CalculateUtils.objectAddition(personalSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 4;
                        } else {
                            sortValue = enumByKey.equals(DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT_CARDINAL) ? 1 : 2;
                        }
                        this.dealComparePart(comparePart, enumByKey, v, unemploymentCompareParts, sortValue);
                        break;
                    case WORK_INJURY_CARDINAL:
                    case PERSONAL_INJURY_CARDINAL:
                    case UNIT_INJURY:
                    case PERSONAL_INJURY:
                        if (enumByKey.equals(DynamicFeeTypesEnum.UNIT_INJURY)) {
                            unitSubtotal.set(CalculateUtils.objectAddition(unitSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 3;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_INJURY)) {
                            personalSubtotal.set(CalculateUtils.objectAddition(personalSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 4;
                        } else {
                            sortValue = enumByKey.equals(DynamicFeeTypesEnum.WORK_INJURY_CARDINAL) ? 1 : 2;
                        }
                        this.dealComparePart(comparePart, enumByKey, v, injuryCompareParts, sortValue);
                        break;
                    case UNIT_MATERNITY_CARDINAL:
                    case PERSONAL_MATERNITY_CARDINAL:
                    case UNIT_MATERNITY:
                    case PERSONAL_MATERNITY:
                        if (enumByKey.equals(DynamicFeeTypesEnum.UNIT_MATERNITY)) {
                            unitSubtotal.set(CalculateUtils.objectAddition(unitSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 3;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_MATERNITY)) {
                            personalSubtotal.set(CalculateUtils.objectAddition(personalSubtotal, v));
                            subTotal.set(CalculateUtils.objectAddition(subTotal, v));
                            sortValue = 4;
                        } else {
                            sortValue = enumByKey.equals(DynamicFeeTypesEnum.UNIT_MATERNITY_CARDINAL) ? 1 : 2;
                        }
                        this.dealComparePart(comparePart, enumByKey, v, maternityCompareParts, sortValue);
                        break;
                    case ACCUMULATION_FUND_BASE:
                    case UNIT_ACCUMULATION_FUND:
                    case PERSONAL_ACCUMULATION_FUND:
                        if (!enumByKey.equals(DynamicFeeTypesEnum.ACCUMULATION_FUND_BASE)) {
                            accumulationFundTotal.set(CalculateUtils.objectAddition(accumulationFundTotal, v));
                            sortValue = enumByKey.equals(DynamicFeeTypesEnum.UNIT_ACCUMULATION_FUND) ? 2 : 3;
                        } else {
                            sortValue = 1;
                        }
                        this.dealComparePart(comparePart, enumByKey, v, accumulationFundCompareParts, sortValue);
                        break;
                    default:
                        break;
                }
            }
        });
        String unitSocialTotalStr = String.valueOf(unitSubtotal);
        String personalSocialTotalStr = String.valueOf(personalSubtotal);
        BigDecimal socialTotal = CalculateUtils.decimalAddition(new BigDecimal(unitSocialTotalStr), new BigDecimal(personalSocialTotalStr));
        subCompareParts.add(new HrBillCompareHeaderDTO.ComparePart().setFieldName("企业缴纳合计").setFieldKey("unitSocialTotal").setFieldValue(unitSocialTotalStr).setSortValue(1));
        subCompareParts.add(new HrBillCompareHeaderDTO.ComparePart().setFieldName("个人缴纳合计").setFieldKey("personalSocialTotal").setFieldValue(personalSocialTotalStr).setSortValue(2));
        subCompareParts.add(new HrBillCompareHeaderDTO.ComparePart().setFieldName("企缴+个缴合计").setFieldKey("socialTotal").setFieldValue(String.valueOf(socialTotal)).setSortValue(3));
        String accumulationFundTotalStr = String.valueOf(accumulationFundTotal);
        accumulationFundCompareParts.add(new HrBillCompareHeaderDTO.ComparePart().setFieldName("公积金合计").setFieldKey("accumulationFundTotal").setFieldValue(accumulationFundTotalStr).setSortValue(4));

        systemData.setPensionCompareParts(pensionCompareParts)
            .setUnemploymentCompareParts(unemploymentCompareParts)
            .setInjuryCompareParts(injuryCompareParts)
            .setMedicalCompareParts(medicalCompareParts)
            .setMaternityCompareParts(maternityCompareParts)
            .setSubCompareParts(subCompareParts)
            .setAccumulationFundCompareParts(accumulationFundCompareParts)
            .setTotalAmount(String.valueOf(CalculateUtils.decimalAddition(socialTotal, new BigDecimal(accumulationFundTotalStr))));
        return systemData;
    }

    /**
     * 计算数据差异
     *
     * @param systemData
     * @param sourceData
     * @param type
     * @return
     */
    private HrBillCompareHeaderDTO dealCalCompareParts(HrBillCompareHeaderDTO systemData, HrBillCompareHeaderDTO sourceData, Integer type) {
        if (type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            systemData.setTotalAmount(StringUtils.object2Str(CalculateUtils.objectSubtraction(sourceData.getTotalAmount(), systemData.getTotalAmount())));
            Map<String, HrBillCompareHeaderDTO.ComparePart> systemDataPensionCollect = systemData.getPensionCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            Map<String, HrBillCompareHeaderDTO.ComparePart> sourceDataPensionCollect = sourceData.getPensionCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            systemData.setPensionCompareParts(new ArrayList<>());
            systemDataPensionCollect.forEach((k, v) -> {
                ComparePart comparePart = sourceDataPensionCollect.get(k);
                if (comparePart != null) {
                    v.setFieldValue(StringUtils.object2Str(CalculateUtils.objectSubtraction(comparePart.getFieldValue(), v.getFieldValue())));
                    systemData.getPensionCompareParts().add(v);
                }
            });
            Map<String, HrBillCompareHeaderDTO.ComparePart> systemDataMedicalCollect = systemData.getMedicalCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            Map<String, HrBillCompareHeaderDTO.ComparePart> sourceDataMedicalCollect = sourceData.getMedicalCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            systemData.setMedicalCompareParts(new ArrayList<>());
            systemDataMedicalCollect.forEach((k, v) -> {
                ComparePart comparePart = sourceDataMedicalCollect.get(k);
                if (comparePart != null) {
                    v.setFieldValue(StringUtils.object2Str(CalculateUtils.objectSubtraction(comparePart.getFieldValue(), v.getFieldValue())));
                    systemData.getMedicalCompareParts().add(v);
                }
            });
            Map<String, HrBillCompareHeaderDTO.ComparePart> systemDataUnemploymentCollect = systemData.getUnemploymentCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            Map<String, HrBillCompareHeaderDTO.ComparePart> sourceDataUnemploymentCollect = sourceData.getUnemploymentCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            systemData.setUnemploymentCompareParts(new ArrayList<>());
            systemDataUnemploymentCollect.forEach((k, v) -> {
                ComparePart comparePart = sourceDataUnemploymentCollect.get(k);
                if (comparePart != null) {
                    v.setFieldValue(StringUtils.object2Str(CalculateUtils.objectSubtraction(comparePart.getFieldValue(), v.getFieldValue())));
                    systemData.getUnemploymentCompareParts().add(v);
                }
            });
            Map<String, HrBillCompareHeaderDTO.ComparePart> systemDataInjuryCollect = systemData.getInjuryCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            Map<String, HrBillCompareHeaderDTO.ComparePart> sourceDataInjuryCollect = sourceData.getInjuryCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            systemData.setInjuryCompareParts(new ArrayList<>());
            systemDataInjuryCollect.forEach((k, v) -> {
                ComparePart comparePart = sourceDataInjuryCollect.get(k);
                if (comparePart != null) {
                    v.setFieldValue(StringUtils.object2Str(CalculateUtils.objectSubtraction(comparePart.getFieldValue(), v.getFieldValue())));
                    systemData.getInjuryCompareParts().add(v);
                }
            });
            Map<String, HrBillCompareHeaderDTO.ComparePart> systemDataMaternityCollect = systemData.getMaternityCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            Map<String, HrBillCompareHeaderDTO.ComparePart> sourceDataMaternityCollect = sourceData.getMaternityCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            systemData.setMaternityCompareParts(new ArrayList<>());
            systemDataMaternityCollect.forEach((k, v) -> {
                ComparePart comparePart = sourceDataMaternityCollect.get(k);
                if (comparePart != null) {
                    v.setFieldValue(StringUtils.object2Str(CalculateUtils.objectSubtraction(comparePart.getFieldValue(), v.getFieldValue())));
                    systemData.getMaternityCompareParts().add(v);
                }
            });
            Map<String, HrBillCompareHeaderDTO.ComparePart> systemDataSubCollect = systemData.getSubCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            Map<String, HrBillCompareHeaderDTO.ComparePart> sourceDataSubCollect = sourceData.getSubCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            systemData.setSubCompareParts(new ArrayList<>());
            systemDataSubCollect.forEach((k, v) -> {
                ComparePart comparePart = sourceDataSubCollect.get(k);
                if (comparePart != null) {
                    v.setFieldValue(StringUtils.object2Str(CalculateUtils.objectSubtraction(comparePart.getFieldValue(), v.getFieldValue())));
                    systemData.getSubCompareParts().add(v);
                }
            });
            Map<String, HrBillCompareHeaderDTO.ComparePart> systemDataAccCollect = systemData.getAccumulationFundCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            Map<String, HrBillCompareHeaderDTO.ComparePart> sourceDataAccCollect = sourceData.getAccumulationFundCompareParts().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            systemData.setAccumulationFundCompareParts(new ArrayList<>());
            systemDataAccCollect.forEach((k, v) -> {
                ComparePart comparePart = sourceDataAccCollect.get(k);
                if (comparePart != null) {
                    v.setFieldValue(StringUtils.object2Str(CalculateUtils.objectSubtraction(comparePart.getFieldValue(), v.getFieldValue())));
                    systemData.getAccumulationFundCompareParts().add(v);
                }
            });
        } else {
            systemData.setTotal(StringUtils.object2Str(CalculateUtils.objectSubtraction(sourceData.getTotal(), systemData.getTotal())));
            systemData.setUnitLateFee(StringUtils.object2Str(CalculateUtils.objectSubtraction(sourceData.getUnitLateFee(), systemData.getUnitLateFee())));
            systemData.setPersonalTax(StringUtils.object2Str(CalculateUtils.objectSubtraction(sourceData.getPersonalTax(), systemData.getPersonalTax())));
            Map<String, HrBillCompareHeaderDTO.ComparePart> systemDataUnitCollect = systemData.getUnitPart().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            Map<String, HrBillCompareHeaderDTO.ComparePart> sourceDataUnitCollect = sourceData.getUnitPart().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            systemData.setUnitPart(new ArrayList<>());

            systemDataUnitCollect.forEach((k, v) -> {
                HrBillCompareHeaderDTO.ComparePart comparePart = sourceDataUnitCollect.get(k);
                if (comparePart != null) {
                    v.setFieldValue(StringUtils.object2Str(CalculateUtils.objectSubtraction(comparePart.getFieldValue(), v.getFieldValue())));
                    systemData.getUnitPart().add(v);
                }
            });

            // Map<String, HrBillCompareHeaderDTO.ComparePart> systemDataPersonalCollect = systemData.getPersonalPart().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            // Map<String, HrBillCompareHeaderDTO.ComparePart> sourceDataPersonalCollect = sourceData.getPersonalPart().stream().collect(Collectors.groupingBy(HrBillCompareHeaderDTO.ComparePart::getFieldKey, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            LinkedHashMap<String, HrBillCompareHeaderDTO.ComparePart> systemDataPersonalCollect = new LinkedHashMap<>();
            systemData.getPersonalPart().forEach(personalPart -> systemDataPersonalCollect.put(personalPart.getFieldKey(), personalPart));

            LinkedHashMap<String, HrBillCompareHeaderDTO.ComparePart> sourceDataPersonalCollect = new LinkedHashMap<>();
            sourceData.getPersonalPart().forEach(personalPart -> sourceDataPersonalCollect.put(personalPart.getFieldKey(), personalPart));

            systemData.setPersonalPart(new ArrayList<>());

            systemDataPersonalCollect.forEach((k, v) -> {
                HrBillCompareHeaderDTO.ComparePart comparePart = sourceDataPersonalCollect.get(k);
                if (comparePart != null) {
                    v.setFieldValue(StringUtils.object2Str(CalculateUtils.objectSubtraction(comparePart.getFieldValue(), v.getFieldValue())));
                    systemData.getPersonalPart().add(v);
                }
            });
        }
        return systemData;
    }

    /**
     * 填充空数据
     *
     * @param PartList
     * @return
     */
    private List<HrBillCompareHeaderDTO.ComparePart> dealBlankData(List<HrBillCompareHeaderDTO.ComparePart> PartList) {
        String s = JSON.toJSONString(PartList);
        List<HrBillCompareHeaderDTO.ComparePart> blankData = JSON.parseArray(s, HrBillCompareHeaderDTO.ComparePart.class);
        blankData.forEach(ls -> ls.setFieldValue("0.00"));
        return blankData;
    }

    /**
     * 处理系统与原始数据
     *
     * @param dateMap
     * @param type
     * @return
     */
    private HrBillCompareHeaderDTO dealCompareParts(Map<String, Object> dateMap, Integer type) {
        HrBillCompareHeaderDTO systemData = new HrBillCompareHeaderDTO();
        List<HrBillCompareHeaderDTO.ComparePart> unitCompareParts = new ArrayList<>();
        List<HrBillCompareHeaderDTO.ComparePart> personalCompareParts = new ArrayList<>();
        AtomicReference<BigDecimal> unitSubtotal = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> personalSubtotal = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> unitLateFee = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> unitLargeMedicalExpense = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> personalLargeMedicalExpense = new AtomicReference<>(BigDecimal.ZERO);
        dateMap.forEach((k, v) -> {
            if (v == null) {
                v = "0";
            }
            HrBillCompareHeaderDTO.ComparePart comparePart = new HrBillCompareHeaderDTO.ComparePart();
            DynamicFeeTypesEnum enumByKey = DynamicFeeTypesEnum.getEnumByFieldName(k);
            int sort = 0;
            if (enumByKey != null) {
                switch (enumByKey) {
                    case UNIT_PENSION:
                    case UNIT_UNEMPLOYMENT:
                    case UNIT_INJURY:
                    case UNIT_SOCIAL_SECURITY_MAKE_UP:

                    case UNIT_PENSION_CARDINAL:
                    case MEDICAL_INSURANCE_CARDINAL:
                    case UNIT_MEDICAL:
                    case UNIT_UNEMPLOYMENT_CARDINAL:
                    case WORK_INJURY_CARDINAL:
                    case UNIT_MATERNITY_CARDINAL:
                    case UNIT_MATERNITY:
                    case ACCUMULATION_FUND_BASE:
                    case UNIT_ACCUMULATION_FUND:
                        unitSubtotal.set(CalculateUtils.objectAddition(unitSubtotal, v));
                        total.set(CalculateUtils.objectAddition(total, v));
                        unitLateFee.set(CalculateUtils.objectAddition(unitLateFee, v));
                        unitLargeMedicalExpense.set(CalculateUtils.objectAddition(unitLargeMedicalExpense, v));
                        if (enumByKey.equals(DynamicFeeTypesEnum.UNIT_PENSION) || enumByKey.equals(DynamicFeeTypesEnum.UNIT_MEDICAL) || enumByKey.equals(DynamicFeeTypesEnum.UNIT_ACCUMULATION_FUND)) {
                            sort = 1;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT) || enumByKey.equals(DynamicFeeTypesEnum.UNIT_MATERNITY)) {
                            sort = 2;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.UNIT_INJURY)) {
                            sort = 3;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.UNIT_SOCIAL_SECURITY_MAKE_UP)) {
                            sort = 4;
                        }
                        this.dealComparePart(comparePart, enumByKey, v, unitCompareParts, sort);
                        break;
                    case PERSONAL_PENSION:
                    case PERSONAL_UNEMPLOYMENT:
                    case PERSONAL_SOCIAL_SECURITY_MAKE_UP:

                    case PERSONAL_PENSION_CARDINAL:
                    case MEDICAL_INSURANCE_CARDINAL_PERSONAL:
                    case PERSONAL_MEDICAL:
                    case PERSONAL_UNEMPLOYMENT_CARDINAL:
                    case PERSONAL_INJURY_CARDINAL:
                    case PERSONAL_INJURY:
                    case PERSONAL_MATERNITY_CARDINAL:
                    case PERSONAL_MATERNITY:
                    case PERSONAL_ACCUMULATION_FUND:
                        personalSubtotal.set(CalculateUtils.objectAddition(personalSubtotal, v));
                        total.set(CalculateUtils.objectAddition(total, v));
                        personalLargeMedicalExpense.set(CalculateUtils.objectAddition(personalLargeMedicalExpense, v));
                        if (enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_PENSION) || enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_MEDICAL) || enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_ACCUMULATION_FUND)) {
                            sort = 1;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT) || enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_MATERNITY)) {
                            sort = 2;
                        } else if (enumByKey.equals(DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MAKE_UP)) {
                            sort = 3;
                        }
                        this.dealComparePart(comparePart, enumByKey, v, personalCompareParts, sort);
                        break;
                    case UNIT_LATE_FEE:
                        if (type.equals(BillEnum.GuidePlateType.MEDICAL_GUIDE.getKey())) {
                            total.set(CalculateUtils.objectAddition(total, v));
                        }
                        systemData.setUnitLateFee(String.valueOf(v));
                        break;
                    case PERSONAL_TAX:
                        systemData.setPersonalTax(String.valueOf(v));
                        break;
                    default:
                        break;

                }
            }
        });
        // 社保导盘处理小计
        if (BillEnum.GuidePlateType.SOCIAL_SECURITY_GUIDE.getKey().equals(type)) {
            unitCompareParts.add(new HrBillCompareHeaderDTO.ComparePart().setFieldName("小计").setFieldKey("unitSubtotal").setFieldValue(String.valueOf(unitSubtotal)).setSortValue(5));
            personalCompareParts.add(new HrBillCompareHeaderDTO.ComparePart().setFieldName("小计").setFieldKey("personalSubtotal").setFieldValue(String.valueOf(personalSubtotal)).setSortValue(4));
        }
        systemData.setUnitPart(unitCompareParts);
        systemData.setPersonalPart(personalCompareParts);
        systemData.setTotal(String.valueOf(total));
        return systemData;
    }

    private void dealComparePart(HrBillCompareHeaderDTO.ComparePart comparePart, DynamicFeeTypesEnum enumByKey, Object v, List<HrBillCompareHeaderDTO.ComparePart> compareParts, int sort) {
        comparePart.setFieldName(enumByKey.getValue());
        comparePart.setFieldKey(enumByKey.getFieldName());
        comparePart.setFieldValue(String.valueOf(v));
        comparePart.setSortValue(sort);
        compareParts.add(comparePart);
    }

    /**
     * 处理excel中存在,系统中不存在的数据
     *
     * @param excelDataDTOMap                  导盘数据
     * @param hrBillCompareResultDetailDTOList 对账结果数据
     * @param diffNums                         差异数量
     * @param compareResultId                  对账结果id
     * @param type                             对账类型
     * @param detailDTOList
     * @param staffCollect
     * @param payYear
     * @param payMonthly
     * @param saveBatchList
     * @param updateBatchList
     * @return
     */
    private int dealWithMissSystemData(Map<String, BillExcelDataDTO> excelDataDTOMap, List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailDTOList,
                                       int diffNums, String compareResultId, Integer type, List<HrBillCompareResultDetailDTO> detailDTOList, Map<String, HrTalentStaffDTO> staffCollect,
                                       int payYear, int payMonthly, List<HrBillCompareResultDetail> saveBatchList, List<HrBillCompareResultDetail> updateBatchList) {
        for (String idCard : excelDataDTOMap.keySet()) {
            diffNums = diffNums + 1;
            BillExcelDataDTO excelData = excelDataDTOMap.get(idCard);
            HrBillCompareResultDetail hrBillCompareResultDetail = new HrBillCompareResultDetail();
            hrBillCompareResultDetail
                .setId(RandomUtil.generateId())
                .setPayYear(payYear)
                .setPayMonthly(payMonthly)
                .setBillCompareResultId(compareResultId)
                .setStaffName(excelData.getName())
                .setIdNo(idCard)
                .setCenterName(excelData.getCenterName());
            HrTalentStaffDTO hrTalentStaffDTO = staffCollect.get(idCard);
            if (hrTalentStaffDTO != null) {
                hrBillCompareResultDetail.setStaffId(hrTalentStaffDTO.getId());
                hrBillCompareResultDetail.setUnitNumber(hrTalentStaffDTO.getUnitNumber());
                hrBillCompareResultDetail.setClientName(hrTalentStaffDTO.getClientName());
                hrBillCompareResultDetail.setArea(hrTalentStaffDTO.getSocialSecurityArea());
                hrBillCompareResultDetail.setSystemNum(hrTalentStaffDTO.getSystemNum());
            }
            hrBillCompareResultDetail.setUsedType(hrTalentStaffDTO == null ? 3 : 4);
            Map<String, Object> excelMap = excelData.getMapDatas();
            // 组装Excel源数据
            HrBillCompareHeaderDTO sourceData = this.dealCompareParts(excelMap, type);
            hrBillCompareResultDetail.setSourceData(JSON.toJSONString(sourceData));
            hrBillCompareResultDetail.setSourceTotal(new BigDecimal(sourceData.getTotal()));
            // 组装系统数据(空数据)
            HrBillCompareHeaderDTO systemData = new HrBillCompareHeaderDTO();
            systemData.setTotal("0.00");
            systemData.setUnitPart(this.dealBlankData(sourceData.getUnitPart()));
            systemData.setPersonalPart(this.dealBlankData(sourceData.getPersonalPart()));
            hrBillCompareResultDetail.setSystemData(JSON.toJSONString(systemData));
            hrBillCompareResultDetail.setSystemTotal(new BigDecimal(systemData.getTotal()));
            HrBillCompareHeaderDTO compareData = this.dealCalCompareParts(systemData, sourceData, type);
            hrBillCompareResultDetail.setCompareTotal(new BigDecimal(compareData.getTotal()));
            hrBillCompareResultDetail.setCompareResult(JSON.toJSONString(compareData));
            //添加或修改到账记录
            this.saveOrUpdateResultDetail(idCard, detailDTOList, hrBillCompareResultDetail, saveBatchList, updateBatchList);
            hrBillCompareResultDetailDTOList.add(this.hrBillCompareResultDetailMapper.toDto(hrBillCompareResultDetail));
        }
        return diffNums;
    }

    /**
     * 个税导盘处理
     *
     * @param systemMap
     * @param excelMap
     * @param plateType
     */
    private void dealWithTaxGuide(Map<String, Object> systemMap, Map<String, Object> excelMap, BillEnum.GuidePlateType plateType) {
        if (BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE != plateType) {
            return;
        }
        Object obj = excelMap.get(EXIST_LAST_MONTH_TAX_DEDCTIOIN);
        if (obj == null) {
            systemMap.put(EXIST_LAST_MONTH_TAX_DEDCTIOIN, "否");
            excelMap.put(EXIST_LAST_MONTH_TAX_DEDCTIOIN, "否");
        } else {
            String str = (String) obj;
            systemMap.put(EXIST_LAST_MONTH_TAX_DEDCTIOIN, str);
        }

    }

    /**
     * 补全一条只有身份证号的数据
     *
     * @param idCard   身份证号
     * @param dataType 数据类型 1 系统数据, 2 excel 数据
     * @return
     */
    private Map<String, Object> getCompletionMap(String idCard, String dataType) {
        Map<String, Object> map = new HashMap<>(10);
        map.put(DynamicFeeTypesEnum.ID_CARD.getFieldName(), idCard);
        map.put(FIELD_TYPE, dataType);
        map.put(FIELD_HAS_COMPARE_DATA, false);
        // 是否存在上月专项扣除,默认设置为否
        map.put(EXIST_LAST_MONTH_TAX_DEDCTIOIN, "否");
        return map;
    }

    /**
     * 根据导盘类型,比较业务数据和excel的数据
     *
     * @param systemMap     业务数据
     * @param excelMap      excel数据
     * @param compareFields 要比较的字段信息
     * @return true 不存在差异数据
     */
    private boolean compareMap(Map<String, Object> systemMap, Map<String, Object> excelMap, List<DynamicFeeTypesEnum> compareFields) {
        List<String> diffFields = new ArrayList<>();
        for (DynamicFeeTypesEnum typesEnum : compareFields) {
            Object systemVal = systemMap.get(typesEnum.getFieldName());
            Object excelVal = excelMap.get(typesEnum.getFieldName());
            // 两个对象都没有值的情况下,略过
            if (systemVal == null && excelVal == null) {
                continue;
            }
            switch (typesEnum) {
                case NAME:
                case ID_CARD:
                case CENTER_NAME:
                case UNIT_NO:
                    String systemStr = systemVal == null ? "" : systemVal.toString();
                    String excelStr = excelVal == null ? "" : excelVal.toString();
                    if (!systemStr.equals(excelStr)) {
                        diffFields.add(typesEnum.getFieldName());
                    }
                    break;
                default:
                    BigDecimal systemDecimal = BillParseUtils.objectToDecimal(systemVal);
                    BigDecimal excelDecimal = BillParseUtils.objectToDecimal(excelVal);
                    if (!BigDecimalCompare.of(systemDecimal).eq(excelDecimal)) {
                        diffFields.add(typesEnum.getFieldName());
                    }
            }
        }
        systemMap.put(FIELD_DIFF, diffFields);
        excelMap.put(FIELD_DIFF, diffFields);
        return diffFields.isEmpty();
    }
}
