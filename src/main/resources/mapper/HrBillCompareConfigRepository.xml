<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillCompareConfigRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        parent_id,
        bill_id,
        type,
        config,
        origin_file_url,
        origin_file_name,
        data_start_row,
        is_delete ,
        created_by ,
        last_modified_by ,
        created_date ,
        last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillCompareConfig">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="bill_id" property="billId"/>
        <result column="type" property="type"/>
        <result column="config" property="config"/>
        <result column="origin_file_url" property="originFileUrl"/>
        <result column="origin_file_name" property="originFileName"/>
        <result column="data_start_row" property="dataStartRow"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <update id="delByBillIdAndType">
        UPDATE hr_bill_compare_config hc SET hc.is_delete = #{isDelete}, last_modified_date = now()
        WHERE  hc.`type` = #{type}
        <if test="billId != null and billId != ''">
            AND hc.bill_id = #{billId}
        </if>
        <if test="billConfigIds != null and billConfigIds.size()>0">
            AND hc.id IN
            <foreach collection="billConfigIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
    </update>

    <update id="updateConfig">
        UPDATE hr_bill_compare_config SET config = #{config} WHERE id = #{id}
    </update>

    <update id="delBatchIds">
        UPDATE hr_bill_compare_config SET is_delete = 1, last_modified_by = #{lastModifiedBy}, last_modified_date = now()
        WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </update>

    <select id="findCompareConfig" resultType="cn.casair.dto.HrBillCompareConfigDTO">
        SELECT
            hbc.* ,hbr.`status`
        FROM
            hr_bill_compare_config hbc
            LEFT JOIN hr_bill_compare_result hbr ON hbc.id = hbr.bill_compare_config_id
        WHERE
            hbc.is_delete = 0 AND hbc.type = #{type}
            AND ( hbr.`status` IS NULL OR hbr.`status` = 0 )
            AND hbc.bill_id IN
            <foreach collection="billIds" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        ORDER BY hbc.created_date DESC
    </select>

    <select id="findLastData" resultType="cn.casair.dto.HrBillCompareConfigDTO">
        WITH temp AS (SELECT
            hbc.*,
            CONCAT( hbc.pay_year,'-',IF(hbc.pay_monthly>9,hbc.pay_monthly,CONCAT('0',hbc.pay_monthly))) AS payment_date,
            hbr.id AS compareResultId
        FROM
            hr_bill_compare_config hbc
            LEFT JOIN hr_bill_compare_result hbr ON hbc.id = hbr.bill_compare_config_id AND hbr.is_delete = 0
            WHERE hbc.is_delete = 0 AND hbc.type = #{params.type} AND hbc.center_name = #{params.centerName}
        )SELECT * FROM temp
        WHERE payment_date <![CDATA[ <= ]]> #{params.paymentDate}
        ORDER BY payment_date ASC
    </select>

    <select id="getBillCompareConfigById" resultType="cn.casair.dto.HrBillCompareConfigDTO">
        SELECT
            b.title,
            bcc.*
        FROM
            hr_bill_compare_config bcc
                LEFT JOIN hr_bill b ON b.id = bcc.bill_id
        WHERE
            bcc.id = #{id}
    </select>

    <select id="getByBillId" resultType="cn.casair.dto.HrBillCompareConfigDTO">
         SELECT
            hb.pay_year,
            hb.pay_monthly,
            hb.title,
            bcc.*
        FROM
            hr_bill_compare_config bcc
        LEFT JOIN hr_bill hb ON hb.id = bcc.bill_id
        WHERE bcc.is_delete = 0 AND bcc.bill_id = #{billId}
        ORDER BY bcc.created_date LIMIT 1
    </select>
</mapper>
