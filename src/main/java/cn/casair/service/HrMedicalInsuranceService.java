package cn.casair.service;
import cn.casair.domain.HrMedicalInsurance;
import cn.casair.dto.HrMedicalInsuranceDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 社保医保公积金账户表服务类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
public interface HrMedicalInsuranceService extends IService<HrMedicalInsurance> {


    /**
     * 创建社保医保公积金账户表
     * @param hrMedicalInsuranceDTO
     * @return
     */
    HrMedicalInsuranceDTO createHrMedicalInsurance(HrMedicalInsuranceDTO hrMedicalInsuranceDTO);

    /**
     * 修改社保医保公积金账户表
     * @param hrMedicalInsuranceDTO
     * @return
     */
    Optional<HrMedicalInsuranceDTO> updateHrMedicalInsurance(HrMedicalInsuranceDTO hrMedicalInsuranceDTO);

    /**
     * 查询社保医保公积金账户表详情
     * @param id
     * @return
     */
    HrMedicalInsuranceDTO getHrMedicalInsurance(String id);

    /**
     * 删除社保医保公积金账户表
     * @param id
     */
    void deleteHrMedicalInsurance(String id);

    /**
     * 批量删除社保医保公积金账户表
     * @param ids
     */
    void deleteHrMedicalInsurance(List<String> ids);

    /**
     * 分页查询社保医保公积金账户表
     * @param hrMedicalInsuranceDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrMedicalInsuranceDTO hrMedicalInsuranceDTO,Long pageNumber,Long pageSize);
    }
