package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.CertificateTypeEnum;
import cn.casair.common.enums.ContractEnum;
import cn.casair.common.enums.StaffEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.ValidateUtil;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrStaffEmolumentImport;
import cn.casair.dto.excel.HrStaffImport;
import cn.casair.dto.excel.HrTalentImport;
import cn.casair.mapper.HrTalentStaffMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.casair.common.utils.DateUtils.getRetirementDate;

/**
 * 员工人才信息异步处理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HrTalentStaffComponent {

    @Value("${file.temp-path}")
    private String tempPath;

    private final RedisCache redisCache;
    private final CodeTableService codeTableService;
    private final HrStationService hrStationService;
    private final HrClientRepository hrClientRepository;
    private final HrProtocolRepository hrProtocolRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrStaffEmolumentRepository hrStaffEmolumentRepository;
    private final CodeTableRepository codeTableRepository;
    private final HrStaffWorkExperienceService hrStaffWorkExperienceService;
    private final HrTalentStaffMapper hrTalentStaffMapper;
    private final HrStaffStationService hrStaffStationService;
    private final HrContractRepository hrContractRepository;
    private final HrStaffEmolumentService hrStaffEmolumentService;
    private final HrRemindConfRepository hrRemindConfRepository;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final HrRealNameAuthService hrRealNameAuthService;
    private final HrSocialSecurityService hrSocialSecurityService;
    private final CommonComponent commonComponent;

    /**
     * 员工福利导入
     *
     * @param inputStream
     * @param redisKey
     * @param user
     */
    @Async
    public void dealEmployeeWelfareImport(InputStream inputStream, String redisKey, JWTUserDTO user) {
        try {
            ExcelImportResult<HrStaffEmolumentImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrStaffEmolumentImport.class);
            List<HrStaffEmolumentImport> list = result.getList();
            int listSize = list.size();
            int scale = 0;

            // 遍历员工福利
            for (HrStaffEmolumentImport importData : list) {
                try {
                    HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectBySystemNum(importData.getSystemNum());
                    if (hrTalentStaff == null) {
                        importData.setErrorMsg("该员工系统编号无对应员工！");
                        continue;
                    }
                    List<HrSocialSecurityDTO> socialSecurityDTOList = hrSocialSecurityService.getClientSocialSecurity(Collections.singletonList(hrTalentStaff.getClientId()));
                    if (socialSecurityDTOList != null && !socialSecurityDTOList.isEmpty()){
                        StringBuilder sbError = new StringBuilder();
                        HrSocialSecurityDTO hrSocialSecurityDTO = socialSecurityDTOList.get(0);
                        Map<String, Object> staffParams = JSONObject.parseObject(JSONObject.toJSONString(importData), Map.class);
                        commonComponent.handleCardinal(hrSocialSecurityDTO, staffParams, sbError);
                        if (StringUtils.isNotBlank(sbError)){
                            importData.setErrorMsg(sbError.toString());
                            continue;
                        }
                    }

                    // 更新员工福利信息
                    HrStaffEmolument hrStaffEmolument = new HrStaffEmolument();
                    hrStaffEmolument.setStaffId(hrTalentStaff.getId());
                    hrStaffEmolument.setBasicWage(importData.getBasicWage());
                    hrStaffEmolument.setSalary(importData.getSalary());
                    if (StringUtils.isNotBlank(importData.getOwnedBank())) {
                        CodeTable codeTable = this.codeTableService.getItemByBankName(importData.getOwnedBank());
                        if (codeTable != null) {
                            hrStaffEmolument.setOwnedBank(String.valueOf(codeTable.getItemValue()));
                        } else {
                            CodeTable children = this.codeTableService.getMaxChildrenByInnerName("ownedBank");
                            CodeTable codeTableNew = new CodeTable();
                            codeTableNew.setParentId(children.getParentId());
                            codeTableNew.setItemName(importData.getOwnedBank());
                            codeTableNew.setItemValue(children.getItemValue() + 1);
                            codeTableNew.setDisplayOrder(children.getDisplayOrder() + 1);
                            this.codeTableRepository.insert(codeTableNew);
                        }
                    }
                    hrStaffEmolument.setSalaryCardNum(importData.getSalaryCardNum())
                        .setSocialSecurityNum(importData.getSocialSecurityNum())
                        .setUnitPensionCardinal(importData.getUnitPensionCardinal())
                        .setUnitUnemploymentCardinal(importData.getUnitUnemploymentCardinal())
                        .setWorkInjuryCardinal(importData.getWorkInjuryCardinal())
                        .setMedicalInsuranceNum(importData.getMedicalInsuranceNum())
                        .setMedicalInsuranceCardinal(importData.getMedicalInsuranceCardinal())
                        .setUnitMaternityCardinal(importData.getUnitMaternityCardinal())
                        .setUnitLargeMedicalExpense(importData.getUnitLargeMedicalExpense())
                        .setReplenishWorkInjuryExpense(importData.getReplenishWorkInjuryExpense())
                        .setPersonalPensionCardinal(importData.getPersonalPensionCardinal())
                        .setPersonalUnemploymentCardinal(importData.getPersonalUnemploymentCardinal())
                        .setPersonalMaternityCardinal(importData.getPersonalMaternityCardinal())
                        .setPersonalLargeMedicalExpense(importData.getPersonalLargeMedicalExpense())
                        .setAccumulationFundNum(importData.getAccumulationFundNum())
                        .setAccumulationFundCardinal(importData.getAccumulationFundCardinal())
                        .setSeniorityWageBase(importData.getSeniorityWageBase())
                        .setMedicalInsuranceCardinalPersonal(importData.getMedicalInsuranceCardinalPersonal())
                        .setUnitEnterpriseAnnuity(importData.getUnitEnterpriseAnnuity())
                        .setCommercialInsurance(importData.getCommercialInsurance())
                        .setPersonalEnterpriseAnnuity(importData.getPersonalEnterpriseAnnuity());
                    this.hrStaffEmolumentService.updateStaffEmolumentByStaffId(hrStaffEmolument);
                } catch (Exception e) {
                    log.error("批量导入员工福利发生异常：{}", e.getMessage());
                    importData.setErrorMsg("系统异常！");
                } finally {
                    // 设置进度条进度
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 10, TimeUnit.MINUTES);
                }
            }
            // 封装返回信息
            ImportResultDTO importResult = ImportResultUtils.writeErrorFile("员工福利批量导入", HrStaffEmolumentImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);

            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CONTRACT.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, user);
        } catch (Exception e) {
            log.error("员工福利异步导入处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "员工福利导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    /**
     * 员工导入
     *
     * @param inputStream 文件
     * @param redisKey    redis
     * @param jwtUserDTO  登录人
     */
    @Async
    public void dealHrStaffImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrStaffImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrStaffImport.class);
            int listSize = result.getList().size();
            int scale = 0;
            List<CodeTableDTO> monthlyPay = codeTableService.getCodeTableListByInnerName("monthlyPay");
            Map<String, Integer> nationality = codeTableService.findCodeTableByInnerValue("nationality");
            Map<String, Integer> ownedBank = codeTableService.findCodeTableByInnerValue("ownedBank");
            Map<String, Integer> certificateType = codeTableService.findCodeTableByInnerValue("certificateType");
            Map<String, Integer> sexType = codeTableService.findCodeTableByInnerValue("sexType");
            Map<String, Integer> politicalType = codeTableService.findCodeTableByInnerValue("politicalType");
            Map<String, Integer> marriageStates = codeTableService.findCodeTableByInnerValue("marriageStates");
            Map<String, Integer> educationStates = codeTableService.findCodeTableByInnerValue("educationStates");
            Map<String, Integer> residenceType = codeTableService.findCodeTableByInnerValue("residenceType");
            Map<String, Integer> staffType = codeTableService.findCodeTableByInnerValue("staffType");
            Map<String, Integer> workNature = codeTableService.findCodeTableByInnerValue("workNature");
            Map<String, String> stationMap = hrStationService.list(new QueryWrapper<HrStation>().eq("is_delete", 0))
                .stream().collect(Collectors.toMap(HrStation::getProfessionName, HrStation::getId, (key1, key2) -> key2));
            List<HrClientDTO> clientList = hrClientRepository.findInfo(null);
            List<HrProtocol> protocolList = hrProtocolRepository.selectList(new QueryWrapper<HrProtocol>().eq("is_delete", 0));
            List<HrTalentStaff> talentStaffList = hrTalentStaffRepository.selectList(new QueryWrapper<HrTalentStaff>().eq("is_delete", 0));
            List<HrStaffEmolument> staffEmolumentList = hrStaffEmolumentRepository.selectList(new QueryWrapper<HrStaffEmolument>().eq("is_delete", 0));
            Map<String, List<HrTalentStaff>> systemMap = talentStaffList.stream().filter(ls -> ls.getSystemNum() != null).collect(Collectors.groupingBy(HrTalentStaff::getSystemNum));
            Map<String, List<HrTalentStaff>> phoneMap = talentStaffList.stream().filter(ls -> ls.getPhone() != null).collect(Collectors.groupingBy(HrTalentStaff::getPhone));
            Map<String, List<HrStaffImport>> map = result.getList().stream().collect(Collectors.groupingBy(HrStaffImport::getCertificateNum));
            List<String> clientIds = clientList.stream().map(HrClientDTO::getId).collect(Collectors.toList());
            List<HrSocialSecurityDTO> socialSecurityDTOList = hrSocialSecurityService.getClientSocialSecurity(clientIds);
            StringBuilder sbError = new StringBuilder();
            for (HrStaffImport hrStaffImport : result.getList()) {
                try {
                    sbError.delete(0, sbError.length());
                    Integer integer = certificateType.get(hrStaffImport.getCertificateType());
                    if (hrStaffImport.getCertificateType() == null || integer == null) {
                        hrStaffImport.setCertificateType(CertificateTypeEnum.CertificateType.RESIDENT_IDENTIFICATION_CARD.getValue());
                    }
                    //校验格式
                    if (hrStaffImport.getCertificateNum() != null) {
                        this.checkoutCertificate(certificateType.get(hrStaffImport.getCertificateType()), hrStaffImport.getCertificateNum(), sbError);
                    }
                    if (hrStaffImport.getSystemNum() == null) {
                        hrStaffImport.setSystemNum("SY" + System.currentTimeMillis());
                    } else {
                        String regex = "^[A-Za-z0-9]+$";
                        if (!hrStaffImport.getSystemNum().matches(regex)) {
                            sbError.append("系统编号只能是数字和字母!");
                        }
                    }
                    hrStaffImport.setCertificateNum(hrStaffImport.getCertificateNum().replaceAll(" ", "").trim());
                    hrStaffImport.setPhone(hrStaffImport.getPhone().replaceAll(" ", "").trim());
                    if (hrStaffImport.getSex() == null) {
                        Integer sex = ValidateUtil.splitSex(hrStaffImport.getCertificateNum());
                        if (sex != null) {
                            hrStaffImport.setSex(sex == 1 ? "男" : "女");
                        }
                    } else {
                        if (!hrStaffImport.getSex().equals("男") && !hrStaffImport.getSex().equals("女")) {
                            Integer sex = ValidateUtil.splitSex(hrStaffImport.getCertificateNum());
                            if (sex != null) {
                                hrStaffImport.setSex(sex == 1 ? "男" : "女");
                            }
                        }
                    }
                    if (hrStaffImport.getBirthday() == null) {
                        LocalDate birthday = ValidateUtil.splitBirthday(hrStaffImport.getCertificateNum());
                        if (birthday != null) {
                            hrStaffImport.setBirthday(birthday);
                        }
                    }
                    if (hrStaffImport.getNationality() == null) {
                        hrStaffImport.setNationality("中国");
                    }

                    if (hrStaffImport.getIzMilitary() == null) {
                        hrStaffImport.setIzMilitary("否");
                    }
                    if (hrStaffImport.getPoliticsStatus() == null) {
                        hrStaffImport.setPoliticsStatus("群众");
                    }
                    if (hrStaffImport.getMaritalStatus() == null) {
                        hrStaffImport.setMaritalStatus("未婚");
                    }
                    if (hrStaffImport.getIzInsured() == null) {
                        hrStaffImport.setIzInsured("否");
                    }
                    HrTalentStaffDTO hrTalentStaff = new HrTalentStaffDTO();
                    BeanUtils.copyProperties(hrStaffImport, hrTalentStaff);
                    boolean workFlag = true;
                    ifRetire(hrTalentStaff);
                    //判断excel中是否存在重复数据
                    String certificateNum = hrTalentStaff.getCertificateNum();
                    List<HrStaffImport> staffImportList = map.get(certificateNum);
                    if (CollectionUtils.isNotEmpty(staffImportList) && staffImportList.size() > 1) {
                        throw new CommonException("导入Excel中该证件号码存在多条数据!");
                    }
                    List<HrTalentStaff> hrTalentStaffs = systemMap.get(hrStaffImport.getSystemNum());
                    if (CollectionUtils.isNotEmpty(hrTalentStaffs) && hrTalentStaffs.size() > 1) {
                        throw new CommonException("导入Excel中该系统编号存在多条数据!");
                    }
                    List<HrTalentStaff> hrTalentStaffList = phoneMap.get(hrStaffImport.getPhone());
                    if (CollectionUtils.isNotEmpty(hrTalentStaffList) && hrTalentStaffList.size() > 1) {
                        throw new CommonException("导入Excel中该手机号码存在多条数据!");
                    }
                    //校验客户是否存在
                    List<HrClientDTO> collect = clientList.stream().filter(ls -> ls.getUnitNumber() != null && ls.getUnitNumber().equals(hrStaffImport.getUnitNumber())).distinct().collect(Collectors.toList());
                    if (collect.isEmpty()) {
                        throw new CommonException("该单位编号对应的客户在系统中不存在!");
                    }
                    HrClientDTO hrClient = collect.get(0);
                    hrTalentStaff.setClientId(hrClient.getId());
                    hrTalentStaff.setSupplementaryPayment(hrStaffImport.getSupplementaryPayment());
                    //校验协议是否存在
                    List<HrProtocol> protocols = protocolList.stream().filter(ls -> ls.getAgreementNumber() != null && ls.getAgreementNumber().equals(hrStaffImport.getAgreementNumber())).distinct().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(protocols)) {
                        sbError.append("该协议编号对应的协议在系统中不存在!");
                    } else {
                        HrProtocol hrProtocol = protocols.get(0);
//                        if (hrProtocol.getStates() >= 2) {
//                            throw new CommonException("该协议编号对应的协议已到期或已终止!");
//                        }
                        hrTalentStaff.setProtocolId(hrProtocol.getId());
                    }
                    if (hrTalentStaff.getPhone() != null) {
                        this.checkoutPhone(hrTalentStaff.getPhone(), hrTalentStaff.getCertificateNum(), hrTalentStaff.getName());
                    }
                    //转换国籍信息
                    if (hrStaffImport.getNationality() != null) {
                        Integer innerValue = nationality.get(hrStaffImport.getNationality());
                        if (innerValue == null) {
                            sbError.append("国籍在系统中不存在!");
                        }
                        if (ObjectUtils.isNotEmpty(innerValue)) {
                            hrTalentStaff.setNationality(innerValue);
                        }
                    }

                    //转换银行信息
                    Integer ownedBankValue = ownedBank.get(hrStaffImport.getOwnedBank());
                    if (hrStaffImport.getOwnedBank() != null && ownedBankValue == null) {
                        sbError.append("工资发放银行在系统中不存在!");
                    }
                    if (StringUtils.isNotBlank(hrStaffImport.getSalaryCardNum()) && StringUtils.isNotBlank(hrClient.getIssuingBank())) {
                        try {
                            String cardDetail = ValidateUtil.getCardDetail(hrStaffImport.getSalaryCardNum());
                            if (!hrClient.getIssuingBank().equals(cardDetail)) {
                                sbError.append("与客户配置的工资发放银行不一致!");
                            }
                        } catch (Exception e) {
                            sbError.append(e.getMessage());
                        }
                    }
                    //转换岗位Id
                    if (hrStaffImport.getProfessionName() != null) {
                        String stationId = stationMap.get(hrStaffImport.getProfessionName());
                        if (stationId == null) {
                            sbError.append("岗位在岗位管理中不存在!");
                        }
                        if (StringUtils.isNotBlank(stationId)) {
                            hrStaffImport.setStationId(stationId);
                        }
                    }

                    //判断合同结束日期是否在合同开始日期之前
                    /*if (hrStaffImport.getContractStartDate().isAfter(LocalDate.now())) {
                        throw new CommonException("合同开始日期不能再今天之后!");
                    }*/
                    //判断合同结束日期是否在合同开始日期之前
                    if (hrStaffImport.getContractEndDate().isBefore(hrStaffImport.getContractStartDate())) {
                        sbError.append("合同结束日期填写不合理!");
                    }
                    if (hrStaffImport.getIzMilitary().equals("是")) {
                        hrTalentStaff.setIzMilitary(true);
                    } else if (hrStaffImport.getIzMilitary().equals("否")) {
                        hrTalentStaff.setIzMilitary(false);
                    }
                    if (hrTalentStaff.getIzMilitary() != null && hrTalentStaff.getIzMilitary()) {
                        if (hrStaffImport.getMilitaryEndDate() == null || hrStaffImport.getMilitaryStartDate() == null) {
                            sbError.append("服过兵役，兵役开始日期和兵役结束日期为必填项!");
                        }
                    }
                    if (hrStaffImport.getMilitaryEndDate() != null && hrStaffImport.getMilitaryStartDate() != null) {
                        if (hrStaffImport.getMilitaryEndDate().isBefore(hrStaffImport.getMilitaryStartDate())) {
                            sbError.append("兵役结束时间填写不合理!");
                        }
                    }
                    if (hrStaffImport.getPoliticsStatus().equals("党员")) {
                        if (hrStaffImport.getPartyDate() == null || hrStaffImport.getPartyBranch() == null) {
                            sbError.append("党员入党时间和所在党支部为必填项!");
                        }
                    }
                    if (hrStaffImport.getPartyDate() != null && hrStaffImport.getPartyDate().isAfter(LocalDate.now())) {
                        sbError.append("入党时间填写不合理!");
                    }
                    if (ObjectUtils.isNotNull(certificateType.get(hrStaffImport.getCertificateType()))) {
                        hrTalentStaff.setCertificateType(certificateType.get(hrStaffImport.getCertificateType()));
                    }
                    if (ObjectUtils.isNotNull(sexType.get(hrStaffImport.getSex()))) {
                        hrTalentStaff.setSex(sexType.get(hrStaffImport.getSex()));
                    }
                    if (ObjectUtils.isNotNull(politicalType.get(hrStaffImport.getPoliticsStatus()))) {
                        hrTalentStaff.setPoliticsStatus(politicalType.get(hrStaffImport.getPoliticsStatus()));
                    }
                    if (ObjectUtils.isNotNull(marriageStates.get(hrStaffImport.getMaritalStatus()))) {
                        hrTalentStaff.setMaritalStatus(marriageStates.get(hrStaffImport.getMaritalStatus()));
                    }
                    if (hrStaffImport.getHighestEducation() != null) {
                        if (ObjectUtils.isNotNull(educationStates.get(hrStaffImport.getHighestEducation()))) {
                            hrTalentStaff.setHighestEducation(educationStates.get(hrStaffImport.getHighestEducation()));
                        }
                    }
                    if (hrStaffImport.getHouseholdRegistration() != null) {
                        if (ObjectUtils.isNotNull(residenceType.get(hrStaffImport.getHouseholdRegistration()))) {
                            hrTalentStaff.setHouseholdRegistration(residenceType.get(hrStaffImport.getHouseholdRegistration()));
                        }
                    }
                    if (hrStaffImport.getPersonnelType() != null) {
                        if (ObjectUtils.isNotNull(staffType.get(hrStaffImport.getPersonnelType()))) {
                            hrTalentStaff.setPersonnelType(staffType.get(hrStaffImport.getPersonnelType()));
                        }
                    }
                    if (hrStaffImport.getWorkNature() != null) {
                        if (ObjectUtils.isNotNull(workNature.get(hrStaffImport.getWorkNature()))) {
                            hrTalentStaff.setWorkNature(workNature.get(hrStaffImport.getWorkNature()));
                        }
                    }
                    if (!hrStaffImport.getStaffStatus().equals(String.valueOf(StaffEnum.StaffStatusEnum.ON_JOB.getKey()))
                        && !hrStaffImport.getStaffStatus().equals(String.valueOf(StaffEnum.StaffStatusEnum.SEPARATION.getKey()))) {
                        sbError.append("人员状态只能选择在职和离职!");
                    }
                    if (hrStaffImport.getStaffStatus().equals(String.valueOf(StaffEnum.StaffStatusEnum.SEPARATION.getKey())) && hrStaffImport.getDepartureDate() == null) {
                        sbError.append("选择离职，离职日期为必填项!");
                    }
                    // 实名认证
                    this.hrRealNameAuthService.authNameCertificateNum(new HrRealNameAuthDTO().setName(hrTalentStaff.getName()).setCertificateNum(hrTalentStaff.getCertificateNum()));
                    //生成劳动合同
                    HrContract contract = this.addNewStaffContract(hrClient, hrTalentStaff, hrStaffImport);
                    //根据合同状态判断员工信息是待入职、在职、离职
                    switch (Integer.parseInt(hrStaffImport.getStaffStatus())) {
                        case 5://离职
                            hrTalentStaff.setStaffStatus(StaffEnum.StaffStatusEnum.SEPARATION.getKey());
                            hrTalentStaff.setIzStartEnd(StaffEnum.IzStartEndEnum.AUDIT_SUCCEEDED.getKey());//入职流程状态到最后一步
                            hrTalentStaff.setIzPreserve(StaffEnum.IzPreserveEnum.NOTARIZE_INFO.getKey());//小程序入职资料已提交
                            hrTalentStaff.setResignationDate(hrStaffImport.getDepartureDate());
                            workFlag = false;
                            break;
                        default:
                            hrTalentStaff.setStaffStatus(StaffEnum.StaffStatusEnum.ON_JOB.getKey());
                            hrTalentStaff.setIzStartEnd(StaffEnum.IzStartEndEnum.AUDIT_SUCCEEDED.getKey());//入职流程状态到最后一步
                            hrTalentStaff.setIzPreserve(StaffEnum.IzPreserveEnum.NOTARIZE_INFO.getKey());//小程序入职资料已提交
                            break;
                    }
                    //查出的员工信息是除了离职或者退休的信息
                    HrTalentStaff staff = null;
                    StringBuilder msg = new StringBuilder();
                    for (HrTalentStaff tmp : talentStaffList) {
                        if (tmp.getCertificateNum() != null && tmp.getCertificateNum().toUpperCase().equals(hrTalentStaff.getCertificateNum().toUpperCase())) {
                            staff = tmp;
                            break;
                        }
                        if (tmp.getSystemNum() != null && tmp.getSystemNum().equals(hrTalentStaff.getSystemNum())) {
                            msg.append("系统编号已在系统中存在！");
                        }
                    }
                    if (StringUtils.isNotBlank(msg.toString())) {
                        sbError.append(msg.toString());
                    }
                    //是否参保
                    if (hrStaffImport.getIzInsured().equals("是")) {
                        //校验社保基数、公积金基数、医保基数、社保类型、公积金类型这五项是否填写完整
                        if (hrClient.getSocialSecurityTypeId() == null || hrClient.getProvidentFundTypeId() == null) {
                            hrTalentStaffRepository.deleteTalentStaff(Collections.singletonList(hrTalentStaff.getId()));
                            sbError.append("单位没有配置相关的社保类型或公积金类型");
                        }
                        if (socialSecurityDTOList != null && !socialSecurityDTOList.isEmpty()){
                            HrSocialSecurityDTO hrSocialSecurityDTO = socialSecurityDTOList.stream().filter(ls -> ls.getId().equals(hrClient.getSocialSecurityTypeId())).findAny().orElse(null);
                            if (hrSocialSecurityDTO != null){
                                Map<String, Object> staffParams = JSONObject.parseObject(JSONObject.toJSONString(hrStaffImport), Map.class);
                                commonComponent.handleCardinal(hrSocialSecurityDTO, staffParams, sbError);
                            }
                        }
                        hrTalentStaff.setIzInsured(StaffEnum.InsuredStatusEnum.INSURED.getKey());
                    } else {
                        hrTalentStaff.setIzInsured(StaffEnum.InsuredStatusEnum.UNINSURED.getKey());
                    }
                    HrContract hrContract = this.hrContractRepository.selectNewestRecord(hrClient.getId(), hrTalentStaff.getId());
                    if (hrContract != null && !hrContract.getContractEndDate().plusDays(1).equals(hrTalentStaff.getContractStartDate())) {
                        // 如果已经存在劳动合同 且上一份劳动合同的结束日期与新合同的开始日期不衔接， 不可生成合同
                        sbError.append("员工新合同开始日期与旧合同结束日期不匹配！");
                    }
                    if (StringUtils.isEmpty(sbError)) {
                        if (staff != null) {//存在在职信息的员工或者在人才库中存在
                            if (staff.getIzDefault()) {
                                if (staff.getName().equals(hrTalentStaff.getName())
                                    && staff.getCertificateNum().equals(hrTalentStaff.getCertificateNum())
                                    && staff.getPhone().equals(hrTalentStaff.getPhone())) {
                                    String staffId = staff.getId();
                                    //true 是人才，将人才库中的信息更新为员工
                                    String systemNum = staff.getSystemNum();
                                    BeanUtils.copyProperties(hrTalentStaff, staff);
                                    if (systemNum != null) {
                                        staff.setSystemNum(systemNum);
                                    }
                                    staff.setIzDefault(false).setId(staffId);
                                    this.hrTalentStaffRepository.updateById(staff);
                                    hrTalentStaff = this.hrTalentStaffMapper.toDto(staff);
                                } else {
                                    if (staff.getPhone() != null && !staff.getPhone().equals(hrTalentStaff.getPhone())
                                        && staff.getName() != null && !staff.getName().equals(hrTalentStaff.getName())) {
                                        sbError.append("证件号码已经存在，导入姓名和手机号和证件号码对应的信息不一致！");
                                    }
                                    if (staff.getPhone() != null && !staff.getPhone().equals(hrTalentStaff.getPhone())) {
                                        sbError.append("证件号码对应的手机号码不一致！");
                                    }
                                    if (staff.getName() != null && !staff.getName().equals(hrTalentStaff.getName())) {
                                        sbError.append("证件号码对应的姓名不一致！");
                                    }
                                }
                            } else {
                                if (staff.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())
                                    || staff.getStaffStatus().equals(StaffEnum.StaffStatusEnum.RETIRE.getKey())) {//员工之前已经离职或者已经退休
                                    if (hrTalentStaff.getStaffStatus().equals(StaffEnum.StaffStatusEnum.ON_JOB.getKey())) {
                                        String staffId = staff.getId();
                                        String systemNum = staff.getSystemNum();
                                        BeanUtils.copyProperties(hrTalentStaff, staff);
                                        if (systemNum != null) {
                                            staff.setSystemNum(systemNum);
                                        }
                                        staff.setId(staffId);
                                        this.hrTalentStaffRepository.updateById(staff);
                                        hrTalentStaff = this.hrTalentStaffMapper.toDto(staff);
                                        //更新在职信息为工作经历
                                        List<HrStaffWorkExperience> experienceList = hrStaffWorkExperienceService.list(new QueryWrapper<HrStaffWorkExperience>().eq("staff_id", staffId).eq("iz_default", "1"));
                                        if (!experienceList.isEmpty()) {
                                            HrStaffWorkExperience workExperience = experienceList.get(0).setIzDefault(false);//更新为之前公司
                                            hrStaffWorkExperienceService.updateById(workExperience);
                                        }
                                    } else {
                                        if (staff.getStaffStatus().equals(hrTalentStaff.getStaffStatus())) {
                                            sbError.append("该员工信息已存在！");
                                        }
                                        if (staff.getClientId().equals(hrTalentStaff.getClientId())
                                            && staff.getCertificateNum().equals(hrTalentStaff.getCertificateNum())
                                            && staff.getStaffStatus().equals(hrTalentStaff.getStaffStatus())) {
                                            sbError.append("该员工信息已存在！");
                                        }
                                    }
                                } else {
                                    sbError.append("该员工信息已存在！");
                                }
                            }
                        } else {
                            //员工信息不存在，录入员工信息
                            HrTalentStaff toEntity = this.hrTalentStaffMapper.toEntity(hrTalentStaff);
                            this.hrTalentStaffRepository.insert(toEntity);
                            hrTalentStaff.setId(toEntity.getId());
                        }
                        if (StringUtils.isNotBlank(sbError)){
                            throw new CommonException(sbError.toString());
                        }
                        //生成员工劳动合同
                        if (hrContract == null){
                            // 如果不存在劳动合同
                            contract.setStaffId(hrTalentStaff.getId());
                            this.hrContractRepository.insert(contract);
                        }
                        //生成岗位信息
                        if (StringUtils.isNotBlank(hrStaffImport.getStationId())) {
                            HrStaffStation hrStaffStation = new HrStaffStation();
                            hrStaffStation.setStaffId(hrTalentStaff.getId()).setStationId(hrStaffImport.getStationId());
                            hrStaffStationService.save(hrStaffStation);
                        }
                        //录入员工在职信息
                        HrStaffWorkExperience workExperience = new HrStaffWorkExperience();
                        BeanUtils.copyProperties(hrStaffImport, workExperience);
                        if (hrStaffImport.getSalary() != null) {
                            Integer salarySection = CalculateUtils.figureBasicWage(hrStaffImport.getSalary(), monthlyPay);
                            if (salarySection != null) {
                                workExperience.setSalarySection(salarySection);
                            }
                        }
                        if (hrTalentStaff.getWorkNature() != null) {
                            workExperience.setWorkNature(hrTalentStaff.getWorkNature());
                        }
                        if (hrTalentStaff.getPersonnelType() != null) {
                            workExperience.setPersonnelType(hrTalentStaff.getPersonnelType());
                        }
                        workExperience.setIzDefault(workFlag).setStaffId(hrTalentStaff.getId()).setBoardDate(hrStaffImport.getContractStartDate()).setClientId(hrClient.getId()).setIzInsured(hrTalentStaff.getIzInsured());
                        if (!workFlag) {
                            workExperience.setDepartureDate(hrStaffImport.getDepartureDate()).setEmployerUnit(hrClient.getClientName());
                        } else {
                            workExperience.setDepartureDate(null);
                        }
                        this.hrStaffWorkExperienceService.save(workExperience);
                        //录入员工薪酬参数
                        HrStaffEmolument staffEmolument = null;
                        for (HrStaffEmolument emolument : staffEmolumentList) {
                            if (emolument.getStaffId() != null && emolument.getStaffId().equals(hrTalentStaff.getId())) {
                                staffEmolument = emolument;
                                break;
                            }
                        }
                        //缴费年月默认入职日期的次月
                        LocalDate contractStartDate = hrStaffImport.getContractStartDate();
                        if (staffEmolument == null) {
                            HrStaffEmolument emolument = new HrStaffEmolument();
                            BeanUtils.copyProperties(hrStaffImport, emolument);
                            if (ownedBankValue != null) {
                                emolument.setOwnedBank(String.valueOf(ownedBankValue));
                            }
                            if (hrStaffImport.getSalary() != null) {
                                emolument.setSalary(hrStaffImport.getSalary());
                            }
                            emolument.setStaffId(hrTalentStaff.getId()).setPayYear(contractStartDate.getYear()).setPayMonthly(contractStartDate.getMonthValue());
                            this.hrStaffEmolumentService.save(emolument);
                        } else {
                            String emolumentId = staffEmolument.getId();
                            BeanUtils.copyProperties(hrStaffImport, staffEmolument);
                            if (ownedBankValue != null) {
                                staffEmolument.setOwnedBank(String.valueOf(ownedBankValue));
                            }
                            if (hrStaffImport.getSalary() != null) {
                                staffEmolument.setSalary(hrStaffImport.getSalary());
                            }
                            staffEmolument.setStaffId(hrTalentStaff.getId()).setId(emolumentId).setPayYear(contractStartDate.getYear()).setPayMonthly(contractStartDate.getMonthValue());
                            hrStaffEmolumentRepository.updateById(staffEmolument);
                        }
                    }
                } catch (Exception e) {
                    log.error("保存员工信息异常:{},{}", hrStaffImport.getCertificateNum(), e.getMessage());
                    if (e instanceof MyBatisSystemException) {
                        sbError.append("该信息查询出多条记录!");
                    } else if (ObjectUtils.isNotNull(e.getMessage()) && e.getMessage().contains("已存在")) {
                        hrStaffImport.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    scale++;
                    hrStaffImport.setErrorMsg(sbError.toString());
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 30, TimeUnit.MINUTES);
                }
            }
            // 将返回数据返回前端
            ImportResultDTO importResult = ImportResultUtils.writeErrorFile("员工批量导入", HrStaffImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);
            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.STAFF.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("员工合同异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "员工导入发生异常：Excel表格数据读取失败！", 30, TimeUnit.MINUTES);
        }
    }

    /**
     * 计算退休按钮是否出现
     *
     * @param hrTalentStaffDTO
     */
    private final void ifRetire(HrTalentStaffDTO hrTalentStaffDTO) {
        //获取要提醒的天数
        QueryWrapper<HrRemindConf> hrRemindConfQueryWrapper = new QueryWrapper<>();
        hrRemindConfQueryWrapper.eq("remind_key", "staff_retire");
        hrRemindConfQueryWrapper.eq("status", 0);
        hrRemindConfQueryWrapper.eq("is_delete", 0);
        HrRemindConf hrRemindConf = hrRemindConfRepository.selectOne(hrRemindConfQueryWrapper);
        //获取离退休的天数
        String ruleAppend = hrRemindConf.getRuleAppend();
        JSONObject hrTalentStaff = JSON.parseObject(ruleAppend);
        int manAge = 60;
        if (hrTalentStaff != null && hrTalentStaff.getInteger("manAge") != null) {
            manAge = hrTalentStaff.getInteger("manAge");
        }
        int womanAge = 50;
        if (hrTalentStaff != null && hrTalentStaff.getInteger("womanAge") != null) {
            womanAge = hrTalentStaff.getInteger("womanAge");
        }
        Integer retirementDate = getRetirementDate(hrTalentStaffDTO.getCertificateNum(), manAge, womanAge);
        Integer ruleDay = 90;
        if (hrRemindConf != null && hrRemindConf.getRuleDay() != null) {
            ruleDay = hrRemindConf.getRuleDay();
        }
        if (retirementDate <= ruleDay) {
            //显示退休通知按钮
            hrTalentStaffDTO.setIzRetire(3);
        }
    }

    /**
     * 手机号有效性/重复性
     *
     * @param phone          手机号码
     * @param certificateNum 手机号码
     * @param name           姓名
     */
    private final void checkoutPhone(String phone, String certificateNum, String name) {
        //验证手机号码有效性
        if (!ValidateUtil.isCellPhoneNo(phone)) {
            throw new CommonException("手机号码格式填写不正确！");
        }
        List<HrTalentStaff> hrTalentStaffs = hrTalentStaffRepository.selectList(
            new QueryWrapper<HrTalentStaff>().eq("phone", phone)
                .ne(StringUtils.isNotBlank(certificateNum), "certificate_num", certificateNum)
                .ne(StringUtils.isNotBlank(name), "name", name));
        if (CollectionUtils.isNotEmpty(hrTalentStaffs)) {
            throw new CommonException("手机号码已存在！");
        }
    }

    /**
     * 正则表达式
     *
     * @param certificateType 类型
     * @param certificateNum  号码
     */
    private final void checkoutCertificate(Integer certificateType, String certificateNum) {
        if (certificateType == CertificateTypeEnum.CertificateType.RESIDENT_IDENTIFICATION_CARD.getKey()) {
            if (!ValidateUtil.isIdNumber(certificateNum)) {
                throw new CommonException("身份证不合规！");
            }
        }
        if (certificateType == CertificateTypeEnum.CertificateType.SERGEANT_CARD.getKey()) {
            String regular = "/^[\\u4E00-\\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$/";
            if (!regular.matches(certificateNum)) {
                throw new CommonException("军官证号不合规！");
            }
        }
        if (certificateType == CertificateTypeEnum.CertificateType.DRIVING_LICENCE.getKey()) {
            String regular = "/^[1-8]\\d{11}$/";
            if (!regular.matches(certificateNum)) {
                throw new CommonException("驾驶证不合规！");
            }
        }
        if (certificateType == CertificateTypeEnum.CertificateType.PROTECTION_CARD.getKey()) {
            String regular = "/^([a-zA-z]|[0-9]){5,17}$/";
            if (!regular.matches(certificateNum)) {
                throw new CommonException("护照号码不合规！");
            }
        }
        if (certificateType == CertificateTypeEnum.CertificateType.MACAU_PASS_PASSPORT.getKey()) {
            String regular = "/^([A-Z]\\d{6,10}(\\(\\w{1}\\))?)$/";
            if (!regular.matches(certificateNum)) {
                throw new CommonException("港澳居民来往内地通行证号码不合规！");
            }
            String regularExpression = "/^\\d{8}|^[a-zA-Z0-9]{10}|^\\d{18}$/";
            if (!regularExpression.matches(certificateNum)) {
                throw new CommonException("台湾居民来往大陆通行证号码不合规！");
            }
        }
    }

    private final void checkoutCertificate(Integer certificateType, String certificateNum, StringBuilder sbError) {
        if (certificateType == CertificateTypeEnum.CertificateType.RESIDENT_IDENTIFICATION_CARD.getKey()) {
            if (!ValidateUtil.isIdNumber(certificateNum)) {
                sbError.append("身份证不合规！");
            }
        }
        if (certificateType == CertificateTypeEnum.CertificateType.SERGEANT_CARD.getKey()) {
            String regular = "/^[\\u4E00-\\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$/";
            if (!regular.matches(certificateNum)) {
                sbError.append("军官证号不合规！");
            }
        }
        if (certificateType == CertificateTypeEnum.CertificateType.DRIVING_LICENCE.getKey()) {
            String regular = "/^[1-8]\\d{11}$/";
            if (!regular.matches(certificateNum)) {
                sbError.append("驾驶证不合规！");
            }
        }
        if (certificateType == CertificateTypeEnum.CertificateType.PROTECTION_CARD.getKey()) {
            String regular = "/^([a-zA-z]|[0-9]){5,17}$/";
            if (!regular.matches(certificateNum)) {
                sbError.append("护照号码不合规！");
            }
        }
        if (certificateType == CertificateTypeEnum.CertificateType.MACAU_PASS_PASSPORT.getKey()) {
            String regular = "/^([A-Z]\\d{6,10}(\\(\\w{1}\\))?)$/";
            if (!regular.matches(certificateNum)) {
                sbError.append("港澳居民来往内地通行证号码不合规！");
            }
            String regularExpression = "/^\\d{8}|^[a-zA-Z0-9]{10}|^\\d{18}$/";
            if (!regularExpression.matches(certificateNum)) {
                sbError.append("台湾居民来往大陆通行证号码不合规！");
            }
        }
    }

    /**
     * 为录入员工生成新的劳动合同
     *
     * @param hrClient
     * @param hrTalentStaff
     * @param hrStaffImport
     */
    private final HrContract addNewStaffContract(HrClientDTO hrClient, HrTalentStaffDTO hrTalentStaff, HrStaffImport hrStaffImport) {
        HrContract hrContract = new HrContract();
        hrContract.setClientId(hrClient.getId());
        hrContract.setUnitNumber(hrClient.getUnitNumber());
        hrContract.setClientName(hrClient.getClientName());
        hrContract.setStaffId(hrTalentStaff.getId());
        hrContract.setSystemNum(hrTalentStaff.getSystemNum());
        hrContract.setStaffName(hrTalentStaff.getName());
        hrContract.setIdNo(hrTalentStaff.getCertificateNum());
        hrContract.setPhone(hrTalentStaff.getPhone());
        if (hrStaffImport != null) {
            // 如果合同结束日期在当前日期之前，合同过期
            if (hrStaffImport.getContractStartDate().isAfter(LocalDate.now())) {
                hrContract.setState(ContractEnum.ContractState.IN_EFFECT.getKey());
            }
            if (hrStaffImport.getContractEndDate().isBefore(LocalDate.now())) {
                hrContract.setState(ContractEnum.ContractState.EXPIRED.getKey());
            }
            // 如果合同结束日期距离当前时间天数小于等于60天， 合同快过期
            else if ((hrStaffImport.getContractEndDate().toEpochDay() - LocalDate.now().toEpochDay()) <= 60) {
                hrContract.setState(ContractEnum.ContractState.EXPIRING_SOON.getKey());
            } else {
                hrContract.setState(ContractEnum.ContractState.IN_EFFECT.getKey());
            }
            if (hrStaffImport.getStaffStatus().equals(String.valueOf(StaffEnum.StaffStatusEnum.SEPARATION.getKey()))) {
                hrContract.setState(ContractEnum.ContractState.DISMISSED.getKey());
            }
            hrContract.setContractStartDate(hrStaffImport.getContractStartDate());
            hrContract.setContractEndDate(hrStaffImport.getContractEndDate());
            hrContract.setContractInitDate(hrStaffImport.getContractStartDate());
            hrContract.setContractSignDate(hrStaffImport.getContractStartDate());
        }
        return hrContract;
    }

    /**
     * 人才信息导入
     *
     * @param inputStream 文件
     * @param redisKey    redis
     * @param jwtUserDTO  登录人
     */
    @Async
    public void dealHrTalentImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        ExcelImportResult<HrTalentImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrTalentImport.class);
        ImportResultDTO importResult;
        try {
            //导入人才信息以及校验
            long l = System.currentTimeMillis();
            int listSize = result.getList().size();
            int scale = 0;
            Map<String, Integer> nationality = codeTableService.findCodeTableByInnerValue("nationality");
            Map<String, Integer> certificateType = codeTableService.findCodeTableByInnerValue("certificateType");
            Map<String, Integer> sexType = codeTableService.findCodeTableByInnerValue("sexType");
            Map<String, Integer> politicalType = codeTableService.findCodeTableByInnerValue("politicalType");
            Map<String, Integer> marriageStates = codeTableService.findCodeTableByInnerValue("marriageStates");
            Map<String, Integer> educationStates = codeTableService.findCodeTableByInnerValue("educationStates");
            Map<String, Integer> residenceType = codeTableService.findCodeTableByInnerValue("residenceType");
            List<HrTalentStaff> talentStaffList = hrTalentStaffRepository.selectList(new QueryWrapper<HrTalentStaff>().eq("is_delete", 0));
            Map<String, List<HrTalentImport>> map = result.getList().stream().collect(Collectors.groupingBy(HrTalentImport::getCertificateNum));
            Map<String, List<HrTalentStaff>> systemMap = talentStaffList.stream().filter(ls -> ls.getSystemNum() != null).collect(Collectors.groupingBy(HrTalentStaff::getSystemNum));
            Map<String, List<HrTalentStaff>> phoneMap = talentStaffList.stream().filter(ls -> ls.getPhone() != null).collect(Collectors.groupingBy(HrTalentStaff::getPhone));

            StringBuilder sbError = new StringBuilder();
            for (HrTalentImport hrTalentImport : result.getList()) {
                try {
                    sbError.delete(0, sbError.length());
                    //证件号码
                    Integer integer = certificateType.get(hrTalentImport.getCertificateType());
                    if (integer == null || hrTalentImport.getCertificateType() == null) {
                        hrTalentImport.setCertificateType(CertificateTypeEnum.CertificateType.RESIDENT_IDENTIFICATION_CARD.getValue());
                    }
                    if (hrTalentImport.getCertificateNum() != null) {
                        this.checkoutCertificate(certificateType.get(hrTalentImport.getCertificateType()), hrTalentImport.getCertificateNum(), sbError);
                    }
                    if (hrTalentImport.getSystemNum() == null) {
                        hrTalentImport.setSystemNum("SY" + System.currentTimeMillis());
                    } else {
                        String regex = "^[A-Za-z0-9]+$";
                        if (!hrTalentImport.getSystemNum().matches(regex)) {
                            sbError.append("系统编号只能是数字和字母!");
                        }
                    }
                    hrTalentImport.setCertificateNum(hrTalentImport.getCertificateNum().replaceAll(" ", "").trim());
                    hrTalentImport.setPhone(hrTalentImport.getPhone().replaceAll(" ", "").trim());
                    if (hrTalentImport.getSex() == null) {
                        Integer sex = ValidateUtil.splitSex(hrTalentImport.getCertificateNum());
                        hrTalentImport.setSex(sex == 1 ? "男" : "女");
                    } else {
                        if (!hrTalentImport.getSex().equals("男") && !hrTalentImport.getSex().equals("女")) {
                            Integer sex = ValidateUtil.splitSex(hrTalentImport.getCertificateNum());
                            hrTalentImport.setSex(sex == 1 ? "男" : "女");
                        }
                    }
                    if (hrTalentImport.getBirthday() == null) {
                        LocalDate birthday = ValidateUtil.splitBirthday(hrTalentImport.getCertificateNum());
                        hrTalentImport.setBirthday(birthday);
                    }
                    if (hrTalentImport.getNationality() == null) {
                        hrTalentImport.setNationality("中国");
                    }
                    if (hrTalentImport.getIzMilitary() == null) {
                        hrTalentImport.setIzMilitary("否");
                    }
                    if (hrTalentImport.getPoliticsStatus() == null) {
                        hrTalentImport.setPoliticsStatus("群众");
                    }
                    if (hrTalentImport.getMaritalStatus() == null) {
                        hrTalentImport.setMaritalStatus("未婚");
                    }
                    HrTalentStaff talentStaff = new HrTalentStaff();
                    BeanUtils.copyProperties(hrTalentImport, talentStaff);
                    if (talentStaff.getSystemNum() == null) {
                        talentStaff.setSystemNum("SY" + System.currentTimeMillis());
                    }
                    List<HrTalentStaff> hrTalentStaffs = systemMap.get(talentStaff.getSystemNum());
                    if (CollectionUtils.isNotEmpty(hrTalentStaffs) && hrTalentStaffs.size() > 1) {
                        sbError.append("导入Excel中该系统编号存在多条数据!");
                    }
                    List<HrTalentStaff> hrTalentStaffList = phoneMap.get(talentStaff.getPhone());
                    if (CollectionUtils.isNotEmpty(hrTalentStaffList) && hrTalentStaffList.size() > 1) {
                        sbError.append("导入Excel中该手机号码存在多条数据!");
                    }
                    //判断excel中是否存在重复数据
                    String certificateNum = talentStaff.getCertificateNum();
                    List<HrTalentImport> hrTalentImports = map.get(certificateNum);
                    if (CollectionUtils.isNotEmpty(hrTalentImports) && hrTalentImports.size() > 1) {
                        sbError.append("导入Excel中该证件号码存在多条数据!");
                    }
                    if (talentStaff.getBirthday() == null) {
                        LocalDate birthday = ValidateUtil.splitBirthday(talentStaff.getCertificateNum());
                        talentStaff.setBirthday(birthday);
                    }
                    Integer innerValue = nationality.get(hrTalentImport.getNationality());
                    if (hrTalentImport.getNationality() != null && innerValue == null) {
                        sbError.append("国籍在系统中不存在!");
                    }
                    if (ObjectUtils.isNotEmpty(innerValue)) {
                        talentStaff.setNationality(innerValue);
                    }
                    if (hrTalentImport.getIzMilitary().equals("是")) {
                        talentStaff.setIzMilitary(true);
                    } else if (hrTalentImport.getIzMilitary().equals("否")) {
                        talentStaff.setIzMilitary(false);
                    }
                    if (talentStaff.getIzMilitary()) {
                        if (hrTalentImport.getMilitaryEndDate() == null || hrTalentImport.getMilitaryStartDate() == null) {
                            sbError.append("服过兵役，兵役开始日期和兵役结束日期为必填项!");
                        }
                    }
                    if (hrTalentImport.getMilitaryEndDate() != null && hrTalentImport.getMilitaryStartDate() != null) {
                        if (hrTalentImport.getMilitaryStartDate() != null && hrTalentImport.getMilitaryStartDate().isAfter(LocalDate.now())) {
                            sbError.append("兵役开始时间填写不合理!");
                        }
                        if (hrTalentImport.getMilitaryEndDate() != null && hrTalentImport.getMilitaryEndDate().isAfter(LocalDate.now())) {
                            sbError.append("兵役结束时间填写不合理!");
                        }
                        if (hrTalentImport.getMilitaryEndDate().isBefore(hrTalentImport.getMilitaryStartDate())) {
                            sbError.append("兵役结束时间填写不合理!");
                        }
                    }
                    if (hrTalentImport.getPoliticsStatus().equals("党员")) {
                        if (hrTalentImport.getPartyDate() == null || hrTalentImport.getPartyBranch() == null) {
                            sbError.append("党员入党时间和所在党支部为必填项!");
                        }
                    }
                    if (hrTalentImport.getPartyDate() != null && hrTalentImport.getPartyDate().isAfter(LocalDate.now())) {
                        sbError.append("入党时间填写不合理!");
                    }
                    if (ObjectUtils.isNotNull(certificateType.get(hrTalentImport.getCertificateType()))) {
                        talentStaff.setCertificateType(certificateType.get(hrTalentImport.getCertificateType()));
                    }
                    if (ObjectUtils.isNotNull(sexType.get(hrTalentImport.getSex()))) {
                        talentStaff.setSex(sexType.get(hrTalentImport.getSex()));
                    }
                    if (ObjectUtils.isNotNull(politicalType.get(hrTalentImport.getPoliticsStatus()))) {
                        talentStaff.setPoliticsStatus(politicalType.get(hrTalentImport.getPoliticsStatus()));
                    }
                    if (ObjectUtils.isNotNull(marriageStates.get(hrTalentImport.getMaritalStatus()))) {
                        talentStaff.setMaritalStatus(marriageStates.get(hrTalentImport.getMaritalStatus()));
                    }
                    if (hrTalentImport.getHighestEducation() != null) {
                        if (ObjectUtils.isNotNull(educationStates.get(hrTalentImport.getHighestEducation()))) {
                            talentStaff.setHighestEducation(educationStates.get(hrTalentImport.getHighestEducation()));
                        }
                    }
                    if (hrTalentImport.getHouseholdRegistration() != null) {
                        if (ObjectUtils.isNotNull(residenceType.get(hrTalentImport.getHouseholdRegistration()))) {
                            talentStaff.setHouseholdRegistration(residenceType.get(hrTalentImport.getHouseholdRegistration()));
                        }
                    }
                    //手机号码：验证手机号码有效性。手机号码不可重复
                    if (!ValidateUtil.isCellPhoneNo(talentStaff.getPhone())) {
                        sbError.append("手机号码格式填写不正确！");
                    }
                    for (HrTalentStaff hrTalentStaff : talentStaffList) {
                        //校验身份证号
                        if (hrTalentStaff.getCertificateNum() != null && hrTalentStaff.getCertificateNum().equals(talentStaff.getCertificateNum())) {
                            sbError.append("证件号码已存在！");
                        }
                        if (hrTalentStaff.getPhone() != null && hrTalentStaff.getPhone().equals(talentStaff.getPhone())) {
                            sbError.append("手机号码已存在！");
                        }
                        if (hrTalentStaff.getSystemNum() != null && hrTalentStaff.getSystemNum().equals(talentStaff.getSystemNum())) {
                            sbError.append("系统编号已存在！");
                        }
                    }
                    if (StringUtils.isEmpty(sbError)) {
                        // 实名认证
                        this.hrRealNameAuthService.authNameCertificateNum(new HrRealNameAuthDTO().setName(talentStaff.getName()).setCertificateNum(talentStaff.getCertificateNum()));
                        talentStaff.setIzDefault(true);//人才标识
                        hrTalentStaffRepository.insert(talentStaff);
                    }
                } catch (Exception e) {
                    log.error("保存人才信息异常:{}", e.getMessage());
                    if (e instanceof MyBatisSystemException) {
                        sbError.append("该信息查询出多条记录!");
                    } else if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("已存在")) {
                        hrTalentImport.setDuplicate(true);
                        sbError.append(e.getMessage());
                    } else {
                        sbError.append(e.getMessage());
                    }
                } finally {
                    hrTalentImport.setErrorMsg(sbError.toString());
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 30, TimeUnit.MINUTES);
                }
            }
            long timeMillis = System.currentTimeMillis();
            log.info("逻辑结束时间：{}", timeMillis);
            log.info("逻辑耗时：{} ms", timeMillis - l);
            // 将返回数据返回前端
            importResult = ImportResultUtils.writeErrorFile("人才批量导入", HrTalentImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResult);
            redisCache.setCacheObject(redisKey, JSON.toJSONString(importResult), 30, TimeUnit.MINUTES);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.TALENTS.getValue(), BusinessTypeEnum.IMPORT.getKey(), importResult.getFailureFileUrl(), JSON.toJSONString(importResult), ImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("人才异步导出处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "人才导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

}
