package cn.casair.repository;

import cn.casair.domain.HrApplyEntryStaff;
import cn.casair.dto.HrApplyEntryStaffDTO;
import cn.casair.dto.HrStaffTurnPositiveDTO;
import cn.casair.dto.HrTalentStaffDTO;
import cn.casair.dto.excel.HrApplyEntryStaffFailExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 申请入职员工数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-08
 */
@Repository
public interface HrApplyEntryStaffRepository extends BaseMapper<HrApplyEntryStaff> {

    /**
     * 更新员工入职申请入职流程状态
     *
     * @param id
     * @param entryState
     * @return int
     * <AUTHOR>
     * @date 2021/11/2
     **/
    int updateStaffApplyEntryStateById(@Param("id") String id, @Param("entryState") Integer entryState);

    /**
     * 根据员工表apply_staff_id 获取员工入职申请信息
     *
     * @param staffId
     * @return cn.casair.domain.HrApplyEntryStaff
     * <AUTHOR>
     * @date 2021/10/28
     **/
    HrApplyEntryStaff getByStaffId(String staffId);

    /**
     * 获取入职申请信息
     *
     * @param staffId
     * @param clientId
     * @return cn.casair.domain.HrApplyEntryStaff
     * <AUTHOR>
     * @date 2021/10/21
     **/
    HrApplyEntryStaff getApplyEntryStaffByObject(@Param("staffId") String staffId, @Param("clientId") String clientId);

    /**
     * 更新申请审批状态
     *
     * @param staffId
     * @param clientId
     * @param applyState
     * @return int
     * <AUTHOR>
     * @date 2021/10/15
     **/
    int updateStaffApplyEntryApplyState(@Param("staffId") String staffId, @Param("clientId") String clientId, @Param("applyState") String applyState, @Param("applyStep") Integer applyStep);

    /**
     * 更新审批入职状态
     *
     * @param staffId     员工id
     * @param clientId    客户id
     * @param oldStateKey 上一流程状态
     * @param newStateKey 此次更新流程状态
     * @return int
     * <AUTHOR>
     * @date 2021/9/29
     **/
    int updateStaffApplyEntryState(@Param("staffId") String staffId, @Param("clientId")String clientId, @Param("oldStateKey")Integer oldStateKey, @Param("newStateKey")Integer newStateKey);

    /**
     * 获取员工入职申请信息
     *
     * @param staffId  员工id
     * @param clientId 客户id
     * @return cn.casair.dto.HrApplyEntryStaffDTO
     * <AUTHOR>
     * @date 2021/9/14
     **/
    HrApplyEntryStaff getApplyEntryStaffInfo(@Param("staffId") String staffId, @Param("clientId") String clientId);

    /**
     * 申请列表
     *
     * @param page
     * @param hrApplyEntryStaffDTO
     * @return
     */
    IPage<HrApplyEntryStaffDTO> findPage(Page<HrApplyEntryStaff> page, @Param("param") HrApplyEntryStaffDTO hrApplyEntryStaffDTO);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    HrApplyEntryStaffDTO getApplyEntryStaffById(String id);

    /**
     * 统计每月入职数量
     *
     * @param particularYear
     * @return
     */
    List<Map<String, Object>> entryCountByYear(String particularYear);

    /**
     * 查询申请信息对应的待入职员工信息
     * @param applyId 申请ID
     * @param applyStatus 待审核状态
     * @param applyStaffIds 员工ID集合
     * @return
     */
    List<HrApplyEntryStaffDTO> selectHrApplyEntryStaffList(@Param("applyId") String applyId, @Param("applyStatus") Integer applyStatus, @Param("applyStaffIds") List<String> applyStaffIds);

    /**
     * 导出失败员工列表
     * @param applyId
     * @return
     */
    List<HrApplyEntryStaffFailExport> getHrApplyEntryStaffList(String applyId);

    /**
     * 导出待入职员工列表
     * @param hrApplyEntryStaffDTO
     * @return
     */
    List<HrApplyEntryStaffFailExport> exportToHiredStaff(@Param("param") HrApplyEntryStaffDTO hrApplyEntryStaffDTO, @Param("permissionClient") List<String> clientIds);

    List<HrTalentStaffDTO> selectClientId(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO);

    void updaterStaffTurn(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO);

    void updaterStaff(String id);

    void updaterStaffStatus(String staffId);

    /**
     * 客服部长手动修改员工入职流程
     * <p>其他方法禁止使用此接口</p>
     *
     * @param hrApplyEntryStaff
     * @return
     */
    int manualUpdateStaffEntryState(@Param("params") HrApplyEntryStaff hrApplyEntryStaff);
}
