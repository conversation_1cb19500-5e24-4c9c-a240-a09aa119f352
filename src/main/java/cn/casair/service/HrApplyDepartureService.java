package cn.casair.service;

import cn.casair.domain.HrApplyDeparture;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrApplyDepartureDTO;
import cn.casair.dto.HrApplyDepartureStaffDTO;
import cn.casair.dto.JWTUserDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 离职服务服务类
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
public interface HrApplyDepartureService extends IService<HrApplyDeparture> {


    /**
     * 创建离职服务
     *
     * @param hrApplyDepartureDTO
     * @return
     */
    HrApplyDepartureDTO createHrApplyDeparture(HrApplyDepartureDTO hrApplyDepartureDTO);

    /**
     * 修改离职服务
     *
     * @param hrApplyDepartureDTO
     * @return
     */
    Optional<HrApplyDepartureDTO> updateHrApplyDeparture(HrApplyDepartureDTO hrApplyDepartureDTO);

    /**
     * 查询离职服务详情
     *
     * @param id
     * @return
     */
    HrApplyDepartureDTO getHrApplyDeparture(String id);

    /**
     * 删除离职服务
     *
     * @param id
     */
    void deleteHrApplyDeparture(String id);

    /**
     * 批量删除离职服务
     *
     * @param ids
     */
    void deleteHrApplyDeparture(List<String> ids);

    /**
     * 分页查询离职服务
     *
     * @param hrApplyDepartureDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrApplyDepartureDTO hrApplyDepartureDTO, Long pageNumber, Long pageSize);

    /**
     * 经理审核离职服务
     * @param batchOptDTO
     * @return
     */
    ResponseEntity managerReviewDeparture(BatchOptDTO batchOptDTO);

    /**
     * 待离职员工信息导入模板
     * @param response
     */
    String importDepartureStaffTemplate(HttpServletResponse response);

    /**
     * 待离职员工信息导入
     * @param file 文件
     * @param clientId 客户ID
     * @return
     */
    String importDepartureStaff(MultipartFile file, String clientId);

    /**
     * 导出失败员工列表
     * @param departureId 离职服务ID
     * @param response
     * @return
     */
    String exportDepartureStaff(String departureId, HttpServletResponse response);

    /**
     * 离职服务--查看--审核员工信息
     * @param jwtUserDTO 操作人信息
     * @param hrApplyDepartureStaffDTO 待离职员工信息
     * @param batchOptDTO 操作参数
     */
    void updateDepartureStaffStatus(JWTUserDTO jwtUserDTO, HrApplyDepartureStaffDTO hrApplyDepartureStaffDTO, BatchOptDTO batchOptDTO);
}
