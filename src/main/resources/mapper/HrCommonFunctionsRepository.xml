<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrCommonFunctionsRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , commonly_keyid, user_id,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrCommonFunctions">
        <id column="id" property="id"/>
        <result column="commonly_keyid" property="commonlyKeyid"/>
        <result column="user_id" property="userId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <update id="deleteUserId">
        UPDATE hr_common_functions
        set is_delete=1
        where user_id = #{id}
    </update>

    <select id="selectRemindKey" resultType="cn.casair.dto.HrRemindConfDTO">
        SELECT *
        FROM `hr_remind_conf`
        where role_key like concat('%', #{currentRoleKey}, '%')
          and is_delete = 0
          AND status = 0
    </select>

    <select id="selectHrRetire" resultType="cn.casair.domain.HrTalentStaff">
        SELECT
            *
        FROM
        hr_talent_staff
        WHERE is_delete = 0 AND iz_default = 0 AND iz_insured = 1 AND staff_status IN (4, 12) AND personnel_type IN (1, 2)
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            AND client_id IN
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="statusList!=null and   statusList.size() > 0">
            AND iz_retire IN
            <foreach collection="statusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>

    <select id="selectHrWorkInjury" resultType="cn.casair.domain.HrWorkInjury">
        SELECT hwi.*
        FROM `hr_work_injury` hwi
        LEFT JOIN hr_talent_staff hts on hwi.staff_id = hts.id
        where
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            hts.client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="statusList!=null and   statusList.size() > 0">
            and hwi.status in
            <foreach collection="statusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        and hwi.is_delete = 0 and hts.is_delete = 0
    </select>

    <select id="selectHrFertilityList" resultType="cn.casair.domain.HrFertility">
        SELECT
        hf.*
        FROM
        `hr_fertility` hf
        LEFT JOIN hr_talent_staff hts on hts.id=hf.staff_id
        where
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            hts.client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        and hf.is_delete = 0 and hts.is_delete = 0
    </select>

    <select id="selectHrArchivesBring" resultType="cn.casair.domain.HrArchivesBring">
        SELECT * from hr_archives_manage ham
        LEFT JOIN hr_archives_bring hab on ham.id=hab.archives_id
        where
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            ham.client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="statusList!=null and   statusList.size() > 0">
            and hab.return_state in
            <foreach collection="statusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>

    <select id="selectUserID" resultType="java.lang.String">
        SELECT
        user_id
        FROM
        hr_client
        WHERE
        is_delete=0
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            and id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>

    <select id="selectHrMessageList" resultType="cn.casair.dto.HrMessageListDTO">
        SELECT hm.title,
               hm.id,
               hm.created_date,
               hm.created_by_id,
               hm.created_by,
               hm.type,
               hm.content,
               su.real_name,
               hm.content_type
        FROM hr_message_list hm
                 LEFT JOIN hr_message_role hmr ON hmr.message_id = hm.id
                 LEFT JOIN sys_user su on hm.created_by_id=su.id
        WHERE hm.is_delete = 0
          AND hmr.is_delete = 0
          AND hmr.states = 0
          and hmr.user_id = #{userId}


        ORDER BY hm.created_date DESC
    </select>
    <select id="selectInjuryDate" resultType="java.time.LocalDateTime">
        SELECT
        injury_date
        FROM
        `hr_work_injury`
        WHERE is_delete=0

        and staff_id in
        <foreach collection="staffIdList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>
    <select id="selectClient" resultType="java.lang.String">
        SELECT id FROM hr_talent_staff where is_delete=0
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            and client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>
    <update id="getHrRemindMessageUpdate">
        UPDATE hr_message_role  set states=1 where message_id=#{id} and user_id=#{userId}
    </update>

    <select id="selectOnboarding" resultType="cn.casair.dto.HrOnboardingDTO">
        SELECT
            hswe.board_date 								 AS createdDate,
            SUBSTRING(hswe.board_date, 9, 2)                 AS `day`,
            COUNT(hts.id)   								 AS PeopleSum
        FROM
            hr_talent_staff hts
        LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id AND hswe.is_delete = 0 AND (CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END)
        WHERE hts.is_delete = 0 AND hts.iz_default = 0 AND hts.staff_status NOT IN (1,2)
            AND hswe.board_date >= #{lastDate} AND hswe.board_date <![CDATA[ <= ]]> #{thisDate}
            <if test="clientIdList!=null and   clientIdList.size() > 0">
                AND hts.client_id in
                <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
        GROUP BY hswe.board_date ORDER BY hswe.board_date
    </select>

    <select id="selectHrResign" resultType="cn.casair.dto.HrResignDTO">
        SELECT
            hts.resignation_date AS createdDate,
            SUBSTRING( hts.resignation_date, 9, 2 ) AS `day`,
            COUNT( hts.id ) AS PeopleSum
        FROM
            hr_talent_staff hts
        WHERE hts.is_delete = 0 AND hts.iz_default = 0  AND hts.staff_status = 5
        AND hts.resignation_date >= #{lastDate}  AND hts.resignation_date <![CDATA[ <= ]]> #{thisDate}
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            and client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY hts.resignation_date  ORDER BY  hts.resignation_date
    </select>

    <select id="selectHrServiceDTO" resultType="cn.casair.dto.HrServiceDTO">
        SELECT
            DATE_FORMAT(ANY_VALUE(hswe.board_date),'%Y-%m')   AS createdDate,
            COUNT(hts.id)   								  AS PeopleSum,
            '入职'                                            AS `type`
        FROM
        hr_talent_staff hts
        LEFT JOIN v_staff_work_experience hswe ON hts.id = hswe.staff_id AND hswe.is_delete = 0 AND (CASE hts.staff_status WHEN 5 THEN hswe.iz_default = 0 AND hswe.client_id IS NOT NULL AND hts.client_id = hswe.client_id ELSE hswe.iz_default = 1 END)
        WHERE hts.is_delete = 0 AND hts.iz_default = 0 AND hts.staff_status NOT IN (1,2)
            AND hswe.board_date >= #{lastDate} AND hswe.board_date <![CDATA[ <= ]]> #{thisDate}
            <if test="clientIdList!=null and   clientIdList.size() > 0">
                AND hts.client_id in
                <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
        GROUP BY createdDate ORDER BY createdDate
    </select>

    <select id="selecthrServiceDTOResign" resultType="cn.casair.dto.HrServiceDTO">
        SELECT
            DATE_FORMAT(ANY_VALUE(hts.resignation_date),'%Y-%m') AS createdDate,
            COUNT( hts.id )                                      AS PeopleSum,
            '离职'                                                AS `type`
        FROM
            hr_talent_staff hts
        WHERE hts.is_delete = 0 AND hts.iz_default = 0  AND hts.staff_status = 5
            AND hts.resignation_date >= #{lastDate}  AND hts.resignation_date <![CDATA[ <= ]]> #{thisDate}
            <if test="clientIdList!=null and   clientIdList.size() > 0">
                and client_id in
                <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
        GROUP BY createdDate ORDER BY createdDate
    </select>

    <select id="selecthrServiceDTOPositive" resultType="cn.casair.dto.HrServiceDTO">
        SELECT
            COUNT(hst.id)                                       AS PeopleSum,
            DATE_FORMAT(ANY_VALUE(hts.created_date),'%Y-%m')    AS createdDate,
            "转正"                                               AS `type`
        FROM hr_staff_turn_positive hst
        LEFT JOIN hr_talent_staff hts on hst.staff_id = hts.id
        WHERE hst.is_delete = 0 AND hst.states = 1 and hst.enterprise_states = 1
        AND hts.created_date >= #{lastDate}  AND hts.created_date <![CDATA[ <= ]]> #{thisDate}
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            and hts.client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY createdDate
    </select>

    <select id="selecthrServiceDTORetire" resultType="cn.casair.dto.HrServiceDTO">
        SELECT
            COUNT(id)                                       AS PeopleSum,
            DATE_FORMAT(ANY_VALUE(created_date),'%Y-%m')    AS createdDate,
            "退休"                                           AS `type`
        FROM
        hr_retire
        WHERE `status` = 3 AND is_delete = 0
        AND created_date >= #{lastDate} AND created_date <![CDATA[ <= ]]> #{thisDate}
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            and client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY createdDate
    </select>

    <select id="selectStaffs" resultType="java.lang.String">
        SELECT
        any_value(htf.id) id,
        any_value(htf.system_num) systemNum,
        any_value(htf.`name`) name ,
        any_value(htf.certificate_num) certificateNum,
        any_value(htf.phone) phone,
        any_value ( htf.staff_status ) staffStatus,
        any_value(haes.staff_type) staffType,
        any_value(haes.contract_start_date) contractStartDate,
        any_value(haes.internship_date) internshipDate,
        any_value(hc.client_name) clientName,
        any_value(hc.id) clientId,
        any_value(su.real_name) specializedName,
        any_value(hs.enterprise_states) enterpriseStates,
        any_value(hs.states) states,
        any_value(he.board_date) boarDate
        FROM
        hr_apply_entry_staff haes
        LEFT JOIN hr_talent_staff htf ON haes.staff_id = htf.id
        LEFT JOIN hr_client hc ON haes.client_id = hc.id
        LEFT JOIN sys_user su ON hc.specialized_id = su.id
        LEFT JOIN hr_staff_turn_positive hs ON haes.staff_id = hs.staff_id
        LEFT JOIN hr_staff_work_experience he on haes.staff_id=he.staff_id and he.is_delete=0
        WHERE
        htf.staff_status=3
        and haes.is_delete = 0
        AND htf.is_delete = 0
        <if test="staffId!=null and  staffId.size() > 0">
            and haes.staff_id in
            <foreach collection="staffId" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        GROUP BY htf.id
    </select>
    <select id="selectListtime" resultType="cn.casair.dto.HrServiceDTO">
        SELECT
            COUNT( id )                                     AS PeopleSum,
            DATE_FORMAT(ANY_VALUE(created_date),'%Y-%m')    AS createdDate,
            "生育"                                           AS `type`
        FROM
            hr_fertility
        WHERE is_delete = 0 AND maternity_leave_start_date  <![CDATA[ < ]]>  NOW()
        AND created_date >= #{lastDate} AND created_date  <![CDATA[ <= ]]> #{thisDate}
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            and client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY createdDate
    </select>

    <select id="selectListgetId" resultType="cn.casair.dto.HrServiceDTO">
        SELECT
            COUNT(id) 											    AS PeopleSum,
            DATE_FORMAT(ANY_VALUE(medical_record_date),'%Y-%m')     AS createdDate,
            "医疗"                                                   AS `type`
        FROM
         hr_talent_staff
        WHERE is_delete = 0 AND medical_record_date IS NOT NULL
            AND medical_record_date >= #{lastDate} AND medical_record_date <![CDATA[ <= ]]> #{thisDate}
            <if test="clientIdList!=null and   clientIdList.size() > 0">
                and client_id in
                <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
        GROUP BY createdDate
    </select>

    <select id="hrServiceDTOWork" resultType="cn.casair.dto.HrServiceDTO">
        SELECT
            COUNT(id) 										AS PeopleSum,
            DATE_FORMAT(ANY_VALUE(created_date),'%Y-%m')    AS createdDate,
            "工伤"                                           AS `type`
        FROM
            hr_work_injury
        WHERE is_delete = 0
        AND created_date >= #{lastDate} AND created_date <![CDATA[ <= ]]> #{thisDate}
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            and client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY createdDate
    </select>

    <select id="hrServiceDTOProve" resultType="cn.casair.dto.HrServiceDTO">
        SELECT
            COUNT(id) 						                AS PeopleSum,
            DATE_FORMAT(ANY_VALUE(created_date),'%Y-%m')    AS createdDate,
            "证明开具"                                       AS `type`
        FROM
            hr_certificate_issuance
        WHERE is_delete = 0 AND certificate_status = 5
        AND created_date >= #{lastDate} AND created_date <![CDATA[ <= ]]> #{thisDate}
        <if test="clientIdList!=null and   clientIdList.size() > 0">
            and client_id in
            <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY createdDate
    </select>

    <select id="selectHrSalaryDTO" resultType="cn.casair.dto.HrSalaryDTO">
        WITH a AS (
            SELECT
                SUM(hbt.real_salary_total) AS realSalaryTotal,
                CONCAT(ANY_VALUE(hb.pay_year),'-',IF(ANY_VALUE(hb.pay_monthly)>9,ANY_VALUE(hb.pay_monthly),CONCAT('0',ANY_VALUE(hb.pay_monthly)))) AS createdDate,
                STR_TO_DATE( CONCAT( ANY_VALUE ( hb.pay_year ), '-', ANY_VALUE ( hb.pay_monthly ), '-', 01 ), '%Y-%m-%d' ) AS paymentDate,
                IF(ANY_VALUE(hb.pay_monthly)>9,ANY_VALUE(hb.pay_monthly),CONCAT('0',ANY_VALUE(hb.pay_monthly))) AS `month`
            FROM
                hr_bill hb
            LEFT JOIN hr_bill_total hbt ON hb.id = hbt.bill_id AND hbt.is_delete = 0
            WHERE hb.is_delete = 0 AND hb.is_official = 1
            <if test="clientIdList!=null and   clientIdList.size() > 0">
                and hb.client_id in
                <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
            GROUP BY createdDate
        )SELECT
            realSalaryTotal,
            createdDate,
            month
        FROM a
        WHERE paymentDate >= #{lastDate} AND paymentDate <![CDATA[ <= ]]> #{thisDate}
    </select>

    <select id="selectHrWelfareDTO" resultType="cn.casair.dto.HrWelfareDTO">
        WITH a AS (
            SELECT
                SUM(social_security_total) + SUM(hbd.accumulation_fund_total) welfare,
                STR_TO_DATE( CONCAT( ANY_VALUE ( hb.pay_year ), '-', ANY_VALUE ( hb.pay_monthly ), '-', 01 ), '%Y-%m-%d' ) AS paymentDate,
                CONCAT(ANY_VALUE(hb.pay_year),'-',IF(ANY_VALUE(hb.pay_monthly)>9,ANY_VALUE(hb.pay_monthly),CONCAT('0',ANY_VALUE(hb.pay_monthly)))) AS createdDate,
                IF(ANY_VALUE(hb.pay_monthly)>9,ANY_VALUE(hb.pay_monthly),CONCAT('0',ANY_VALUE(hb.pay_monthly))) AS `month`
            FROM
                hr_bill_detail hbd
            LEFT JOIN hr_bill hb ON hbd.bill_id = hb.id
            WHERE hbd.is_delete = 0 AND hbd.is_used = 1 AND hb.is_official = 1 AND hb.is_delete = 0
            <if test="clientIdList!=null and   clientIdList.size() > 0">
                AND hb.client_id in
                <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
            GROUP BY createdDate
        ) SELECT
            welfare,
            createdDate,
            `month`
        FROM a
        WHERE paymentDate >= #{lastDate} AND paymentDate <![CDATA[ <= ]]> #{thisDate}
    </select>

    <update id="getHrRemindMessageUpdateReminder">
        UPDATE hr_notification_user_content
        set type="1"
        where id = #{id}
          and is_delete = 0
    </update>

    <select id="selectLists" resultType="cn.casair.domain.HrNotificationUserContent">
        SELECT hnuc.*
        FROM hr_notification_user_content hnuc
                 LEFT JOIN hr_notification_user hnu ON hnu.id = hnuc.notification_user_id
            AND hnuc.is_delete = 0
        WHERE hnu.user_id = #{id}
          and hnuc.created_by != #{userName}
          AND hnu.is_delete = 0
          AND hnuc.type = 0
    </select>
</mapper>
