package cn.casair.service;
import cn.casair.domain.HrServiceMaterial;
import cn.casair.dto.HrServiceMaterialDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务与材料的关联表服务类
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
public interface HrServiceMaterialService extends IService<HrServiceMaterial> {


    /**
     * 创建服务与材料的关联表
     * @param hrServiceMaterialDTO
     * @return
     */
    HrServiceMaterialDTO createHrServiceMaterial(HrServiceMaterialDTO hrServiceMaterialDTO);

    /**
     * 修改服务与材料的关联表
     * @param hrServiceMaterialDTO
     * @return
     */
    Optional<HrServiceMaterialDTO> updateHrServiceMaterial(HrServiceMaterialDTO hrServiceMaterialDTO);

    /**
     * 查询服务与材料的关联表详情
     * @param id
     * @return
     */
    HrServiceMaterialDTO getHrServiceMaterial(String id);

    /**
     * 删除服务与材料的关联表
     * @param id
     */
    void deleteHrServiceMaterial(String id);

    /**
     * 批量删除服务与材料的关联表
     * @param ids
     */
    void deleteHrServiceMaterial(List<String> ids);

    /**
     * 分页查询服务与材料的关联表
     * @param hrServiceMaterialDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrServiceMaterialDTO hrServiceMaterialDTO,Long pageNumber,Long pageSize);

    /**
     * 增加服务所对应的材料
     * @param materialList
     * @param serviceId
     */
    void addList(ArrayList<String> materialList, String serviceId);

    /**
     * 获取服务的对应的材料
     * @return
     */
    List<HrServiceMaterial> selectByServiceId(String serviceId);

}
