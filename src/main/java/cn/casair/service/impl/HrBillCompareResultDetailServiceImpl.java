package cn.casair.service.impl;

import cn.casair.common.enums.BillEnum;
import cn.casair.common.utils.EnumUtils;
import cn.casair.domain.HrBillCompareResultDetail;
import cn.casair.dto.DynamicHeadersDTO;
import cn.casair.dto.HrBillCompareHeaderDTO;
import cn.casair.dto.HrBillCompareResultDetailDTO;
import cn.casair.mapper.HrBillCompareResultDetailMapper;
import cn.casair.repository.HrBillCompareResultDetailRepository;
import cn.casair.service.HrBillCompareResultDetailService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 导盘对账结果明细服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrBillCompareResultDetailServiceImpl extends ServiceImpl<HrBillCompareResultDetailRepository, HrBillCompareResultDetail> implements HrBillCompareResultDetailService {

    private final HrBillCompareResultDetailRepository hrBillCompareResultDetailRepository;
    private final HrBillCompareResultDetailMapper hrBillCompareResultDetailMapper;

    /**
     * 制作动态表头
     *
     * @param hrBillCompareResultDetailList
     * @param type                          对账类型
     * @return
     */
    @Override
    public List<DynamicHeadersDTO> makeDynamicHeaders(List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailList, Integer type) {
        HrBillCompareResultDetailDTO resultDetail = hrBillCompareResultDetailList.get(0);
        List<DynamicHeadersDTO> list = new LinkedList<>();
        list.add(new DynamicHeadersDTO().setTitle("员工姓名").setKey("staffName"));
        list.add(new DynamicHeadersDTO().setTitle("身份证号").setKey("idNo"));
        list.add(new DynamicHeadersDTO().setTitle("缴费年月").setKey("paymentDate"));
        if (!type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            list.add(new DynamicHeadersDTO().setTitle("个人编号").setKey("systemNum"));
            list.add(new DynamicHeadersDTO().setTitle("单位编号").setKey("unitNumber"));
            list.add(new DynamicHeadersDTO().setTitle("用工单位").setKey("clientName"));
            if (!type.equals(BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey())){
                list.add(new DynamicHeadersDTO().setTitle("导盘单位编号").setKey("unitNo"));
            }
        }
        String compareTypeStr = "";
        BillEnum.GuidePlateType enumByKey = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, type);
        switch (enumByKey) {
            case SOCIAL_SECURITY_GUIDE:
                compareTypeStr = "社保";
                break;
            case MEDICAL_GUIDE:
                compareTypeStr = "医保";
                break;
            case ACCUMULATION_FUND_GUIDE:
                compareTypeStr = "公积金";
                break;
            case PERSONAL_TAX_GUIDE:
                compareTypeStr = "个税";
                break;
            case THIRD_PARTY_BILLS:
                compareTypeStr = "第三方";
                break;
            case SPECIAL_CLIENT_BILL:
                compareTypeStr = "海尔";
                list.add(new DynamicHeadersDTO().setTitle("中心").setKey("centerName"));
                list.add(new DynamicHeadersDTO().setTitle("缴保地").setKey("area"));
                break;
            default:
                break;
        }
        if (resultDetail.getSystemDataObject() != null) {
            this.dealComparePartDynamicHeader(enumByKey, list, resultDetail.getSystemDataObject(), "当月实际缴费数据", "systemData", "#efa807");
        }
        if (resultDetail.getSourceDataObject() != null) {
            this.dealComparePartDynamicHeader(enumByKey, list, resultDetail.getSourceDataObject(), compareTypeStr + "系统缴费数据", "sourceData", "#6894fe");
        }
        if (resultDetail.getCompareResultObject() != null) {
            this.dealComparePartDynamicHeader(enumByKey, list, resultDetail.getCompareResultObject(), compareTypeStr + "差异数据", "compareData", "#eb3333");
        }
        return list;
    }

    /**
     * 动态项中的动态项
     *
     * @param enumByKey
     * @param list
     * @param compareHeader
     * @param topTitle
     * @param topKey
     * @param color
     */
    public void dealComparePartDynamicHeader(BillEnum.GuidePlateType enumByKey, List<DynamicHeadersDTO> list, HrBillCompareHeaderDTO compareHeader, String topTitle, String topKey, String color) {
        DynamicHeadersDTO dynamicHeadersDTO = new DynamicHeadersDTO();
        dynamicHeadersDTO.setTitle(topTitle);
        dynamicHeadersDTO.setKey(topKey);

        dynamicHeadersDTO.setColor(color);
        dynamicHeadersDTO.setChildren(new ArrayList<>());

        if (!enumByKey.equals(BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE)) {
            DynamicHeadersDTO unitPartHeader = new DynamicHeadersDTO();
            unitPartHeader.setTitle("单位部分");
            unitPartHeader.setKey(topKey + "Unit");

            unitPartHeader.setColor(color);
            unitPartHeader.setChildren(new ArrayList<>());
            List<HrBillCompareHeaderDTO.ComparePart> unitPartList = compareHeader.getUnitPart();
            List<HrBillCompareHeaderDTO.ComparePart> unitPartSortList = unitPartList.stream().filter(lst -> lst.getSortValue() != null).sorted(Comparator.comparing(HrBillCompareHeaderDTO.ComparePart::getSortValue)).collect(Collectors.toList());
            if (unitPartSortList != null && !unitPartSortList.isEmpty()){
                unitPartList = unitPartSortList;
            }
            unitPartList.forEach(unitPart -> {
                DynamicHeadersDTO temp = new DynamicHeadersDTO();
                temp.setTitle(unitPart.getFieldName());
                temp.setKey(topKey + "Unit" + unitPart.getFieldKey());
                temp.setColor(color);
                unitPartHeader.getChildren().add(temp);
            });
            dynamicHeadersDTO.getChildren().add(unitPartHeader);

            DynamicHeadersDTO personalPartHeader = new DynamicHeadersDTO();
            personalPartHeader.setTitle("个人部分");
            personalPartHeader.setKey(topKey + "Personal");
            personalPartHeader.setColor(color);
            personalPartHeader.setChildren(new ArrayList<>());
            List<HrBillCompareHeaderDTO.ComparePart> personalPartList = compareHeader.getPersonalPart();
            List<HrBillCompareHeaderDTO.ComparePart> personalPartSortList = personalPartList.stream().filter(lst -> lst.getSortValue() != null).sorted(Comparator.comparing(HrBillCompareHeaderDTO.ComparePart::getSortValue)).collect(Collectors.toList());
            if (personalPartSortList != null && !personalPartSortList.isEmpty()){
                personalPartList = personalPartSortList;
            }
            personalPartList.forEach(personalPart -> {
                DynamicHeadersDTO temp = new DynamicHeadersDTO();
                temp.setTitle(personalPart.getFieldName());
                temp.setKey(topKey + "Personal" + personalPart.getFieldKey());
                temp.setColor(color);
                personalPartHeader.getChildren().add(temp);
            });
            dynamicHeadersDTO.getChildren().add(personalPartHeader);
        } else {
            DynamicHeadersDTO taxHeader = new DynamicHeadersDTO();
            taxHeader.setTitle("税额");
            taxHeader.setKey(topKey + "PersonalTax");
            taxHeader.setColor(color);
            dynamicHeadersDTO.getChildren().add(taxHeader);
        }
        // 处理差异表头
        switch (enumByKey) {
            case SOCIAL_SECURITY_GUIDE:
                dynamicHeadersDTO.getChildren().add(new DynamicHeadersDTO().setTitle("滞纳金").setKey(topKey + "UnitLateFee").setColor(color));
                break;
            case MEDICAL_GUIDE:
                dynamicHeadersDTO.getChildren().add(new DynamicHeadersDTO().setTitle("滞纳金").setKey(topKey + "UnitLateFee").setColor(color));
                dynamicHeadersDTO.getChildren().add(new DynamicHeadersDTO().setTitle("合计").setKey(topKey + "Total").setColor(color));
                break;
            case ACCUMULATION_FUND_GUIDE:
                dynamicHeadersDTO.getChildren().add(new DynamicHeadersDTO().setTitle("合计").setKey(topKey + "Total").setColor(color));
                break;
            case THIRD_PARTY_BILLS:
            case SPECIAL_CLIENT_BILL:
            case PERSONAL_TAX_GUIDE:
            default:
                break;
        }


        list.add(dynamicHeadersDTO);
    }

    @Override
    public List<HrBillCompareResultDetailDTO> selectHrBillCompareResultDetailByBillCompareResultId(String hrBillCompareResultId) {
        return this.hrBillCompareResultDetailRepository.selectHrBillCompareResultDetailByBillCompareResultId(hrBillCompareResultId);
    }

    /**
     * 创建导盘对账结果明细
     *
     * @param hrBillCompareResultDetailDTO
     * @return
     */
    @Override
    public HrBillCompareResultDetailDTO createHrBillCompareResultDetail(HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO) {
        log.info("Create new HrBillCompareResultDetail:{}", hrBillCompareResultDetailDTO);

        HrBillCompareResultDetail hrBillCompareResultDetail = this.hrBillCompareResultDetailMapper.toEntity(hrBillCompareResultDetailDTO);
        this.hrBillCompareResultDetailRepository.insert(hrBillCompareResultDetail);
        return this.hrBillCompareResultDetailMapper.toDto(hrBillCompareResultDetail);
    }

    /**
     * 修改导盘对账结果明细
     *
     * @param hrBillCompareResultDetailDTO
     * @return
     */
    @Override
    public Optional<HrBillCompareResultDetailDTO> updateHrBillCompareResultDetail(HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO) {
        return Optional.ofNullable(this.hrBillCompareResultDetailRepository.selectById(hrBillCompareResultDetailDTO.getId()))
            .map(roleTemp -> {
                HrBillCompareResultDetail hrBillCompareResultDetail = this.hrBillCompareResultDetailMapper.toEntity(hrBillCompareResultDetailDTO);
                this.hrBillCompareResultDetailRepository.updateById(hrBillCompareResultDetail);
                log.info("Update HrBillCompareResultDetail:{}", hrBillCompareResultDetailDTO);
                return hrBillCompareResultDetailDTO;
            });
    }

    /**
     * 查询导盘对账结果明细详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillCompareResultDetailDTO getHrBillCompareResultDetail(String id) {
        log.info("Get HrBillCompareResultDetail :{}", id);

        HrBillCompareResultDetail hrBillCompareResultDetail = this.hrBillCompareResultDetailRepository.selectById(id);
        return this.hrBillCompareResultDetailMapper.toDto(hrBillCompareResultDetail);
    }

    /**
     * 删除导盘对账结果明细
     *
     * @param id
     */
    @Override
    public void deleteHrBillCompareResultDetail(String id) {
        Optional.ofNullable(this.hrBillCompareResultDetailRepository.selectById(id))
            .ifPresent(hrBillCompareResultDetail -> {
                this.hrBillCompareResultDetailRepository.deleteById(id);
                log.info("Delete HrBillCompareResultDetail:{}", hrBillCompareResultDetail);
            });
    }

    /**
     * 批量删除导盘对账结果明细
     *
     * @param ids
     */
    @Override
    public void deleteHrBillCompareResultDetail(List<String> ids) {
        log.info("Delete HrBillCompareResultDetails:{}", ids);
        this.hrBillCompareResultDetailRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询导盘对账结果明细
     *
     * @param hrBillCompareResultDetailDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO, Long pageNumber, Long pageSize) {
        Page<HrBillCompareResultDetail> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrBillCompareResultDetail> qw = new QueryWrapper<>(this.hrBillCompareResultDetailMapper.toEntity(hrBillCompareResultDetailDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrBillCompareResultDetailRepository.selectPage(page, qw);
        iPage.setRecords(this.hrBillCompareResultDetailMapper.toDto(iPage.getRecords()));
        return iPage;
    }
}
