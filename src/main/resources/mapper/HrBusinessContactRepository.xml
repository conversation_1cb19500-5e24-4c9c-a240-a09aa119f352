<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBusinessContactRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business_type, contacts, contact_information, sort,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBusinessContact">
        <id column="id" property="id"/>
        <result column="business_type" property="businessType"/>
        <result column="contacts" property="contacts"/>
        <result column="contact_information" property="contactInformation"/>
        <result column="sort" property="sort"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT
            MAX( sort )
        FROM
            hr_business_contact
        WHERE
            is_delete = 0
    </select>

    <select id="getBusinessContactList" resultType="cn.casair.dto.HrBusinessContactDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_business_contact
        WHERE is_delete = 0
        ORDER BY sort
    </select>

    <select id="selectByIds" resultType="cn.casair.domain.HrBusinessContact">
        SELECT * FROM hr_business_contact WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="selectPageList" resultType="cn.casair.dto.HrBusinessContactDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_business_contact
        WHERE is_delete = 0
        <if test="params.businessType!=null and params.businessType!=''">
            AND business_type LIKE CONCAT('%', #{params.businessType}, '%')
        </if>
        <if test="params.contacts!=null and params.contacts!=''">
            AND contacts LIKE CONCAT('%', #{params.contacts}, '%')
        </if>
        <if test="params.contactInformation!=null and params.contactInformation!=''">
            AND contact_information LIKE CONCAT('%', #{params.contactInformation}, '%')
        </if>
    </select>

    <select id="selectBySort" resultType="cn.casair.domain.HrBusinessContact">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_business_contact
        WHERE is_delete = 0
          AND sort &gt;= #{sort}
        ORDER BY
          sort
    </select>
</mapper>
