package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.BigDecimalCompare;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.RandomUtil;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrSocialSecurityTemplate;
import cn.casair.mapper.HrSocialSecurityMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.asynchronous.AccumulationFoundComponent;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 社保医保表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrSocialSecurityServiceImpl extends ServiceImpl<HrSocialSecurityRepository, HrSocialSecurity> implements HrSocialSecurityService {

    private final HrStaffWelfareRecordRepository hrStaffWelfareRecordRepository;
    private final HrFeeReviewRepository hrFeeReviewRepository;
    private final HrStaffWelfareRecordService hrStaffWelfareRecordService;
    private final HrWelfareCompensationRecordService hrWelfareCompensationRecordService;
    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final HrStaffEmolumentRepository hrStaffEmolumentRepository;
    private final HrSocialSecurityRepository hrSocialSecurityRepository;
    private final HrSocialSecurityMapper hrSocialSecurityMapper;
    private final RedisCache redisCache;
    private final HrClientService hrClientService;
    private final CodeTableServiceImpl codeTableService;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final HrSocialSecurityConfigService hrSocialSecurityConfigService;
    @Value("${file.temp-path}")
    private String fileTempPath;
    @Value("${minio.excelPrefix}")
    private String excelPrefix;
    @Autowired
    private AccumulationFoundComponent accumulationFoundComponent;

    @Override
    public List<HrSocialSecurityDTO> getSocialSecurityTypeList() {
        return this.hrSocialSecurityRepository.getSocialSecurityTypeList();
    }

    /**
     * 创建社保医保表
     *
     * @param hrSocialSecurityDTO
     * @return
     */
    @Override
    public HrSocialSecurityDTO createHrSocialSecurity(HrSocialSecurityDTO hrSocialSecurityDTO) {
        log.info("Create new HrSocialSecurity:{}", hrSocialSecurityDTO);

        HrSocialSecurity hrSocialSecurity = this.hrSocialSecurityMapper.toEntity(hrSocialSecurityDTO);
        AssignmentRatio(hrSocialSecurityDTO, hrSocialSecurity);
        //社保类型名称不能重复
        List<HrSocialSecurity> list = getHrSocialSecurities(hrSocialSecurityDTO.getSocialSecurityName());
        if (CollectionUtils.isNotEmpty(list)) {
            throw new CommonException("社保类型名称不可以重复！");
        }
        this.hrSocialSecurityRepository.insert(hrSocialSecurity);
        HrSocialSecurityDTO securityDTO = this.hrSocialSecurityMapper.toDto(hrSocialSecurity);
        HrSocialSecurityConfigDTO securityConfigDTO = this.handleHrSocialSecurityConfig(hrSocialSecurityDTO, securityDTO.getId());
        hrSocialSecurityConfigService.createHrSocialSecurityConfig(securityConfigDTO);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.SOCIAL_SECURITY.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrSocialSecurityDTO),
            HrSocialSecurityDTO.class,
            null,
            JSON.toJSONString(hrSocialSecurity)
        );
        return securityDTO;
    }

    /**
     * 添加社保类型配置信息
     *
     * @param hrSocialSecurityDTO 社保配置参数
     * @param securityId          社保类型ID
     */
    private HrSocialSecurityConfigDTO handleHrSocialSecurityConfig(HrSocialSecurityDTO hrSocialSecurityDTO, String securityId) {
        HrSocialSecurityConfigDTO securityConfigDTO = new HrSocialSecurityConfigDTO();
        securityConfigDTO.setSocialSecurityId(securityId);
        // 根据fieldKey过滤重复项
        String specialField = JSONObject.toJSONString(hrSocialSecurityDTO.getDistinctSpecialFieldDTOList());
        securityConfigDTO.setSpecialField(specialField);
        String aloneCardinal = JSONObject.toJSONString(hrSocialSecurityDTO.getDistinctAloneCardinalDTOList());
        securityConfigDTO.setAloneCardinal(aloneCardinal);
        String mergeCardinal = JSONObject.toJSONString(hrSocialSecurityDTO.getDistinctMergeCardinalDTOList());
        securityConfigDTO.setMergeCardinal(mergeCardinal);
        return securityConfigDTO;
    }

    private List<HrSocialSecurity> getHrSocialSecurities(String socialSecurityName) {
        QueryWrapper<HrSocialSecurity> hrSocialSecurityQueryWrapper = new QueryWrapper<>();
        hrSocialSecurityQueryWrapper.eq("social_security_name", socialSecurityName);
        List<HrSocialSecurity> list = list(hrSocialSecurityQueryWrapper);
        return list;
    }

    /**
     * 重新赋值比例数据
     *
     * @param hrSocialSecurityDTO
     * @param hrSocialSecurity
     */
    private void AssignmentRatio(HrSocialSecurityDTO hrSocialSecurityDTO, HrSocialSecurity hrSocialSecurity) {
        if (hrSocialSecurityDTO.getUnitPension() != null) {
            BigDecimal unitPension = extracted(1, hrSocialSecurityDTO.getUnitPension());
            hrSocialSecurity.setUnitPension(unitPension);
        }

        if (hrSocialSecurityDTO.getWorkInjury() != null) {
            BigDecimal getWorkInjury = extracted(1, hrSocialSecurityDTO.getWorkInjury());
            hrSocialSecurity.setWorkInjury(getWorkInjury);
        }

        if (hrSocialSecurityDTO.getPersonalMedical() != null) {
            BigDecimal getPersonalMedical = extracted(1, hrSocialSecurityDTO.getPersonalMedical());
            hrSocialSecurity.setPersonalMedical(getPersonalMedical);
        }


        if (hrSocialSecurityDTO.getPersonalPension() != null) {
            BigDecimal getPersonalPension = extracted(1, hrSocialSecurityDTO.getPersonalPension());
            hrSocialSecurity.setPersonalPension(getPersonalPension);
        }


        if (hrSocialSecurityDTO.getPersonalUnemployment() != null) {
            BigDecimal getPersonalUnemployment = extracted(1, hrSocialSecurityDTO.getPersonalUnemployment());
            hrSocialSecurity.setPersonalUnemployment(getPersonalUnemployment);
        }


        if (hrSocialSecurityDTO.getUnitUnemployment() != null) {
            BigDecimal getUnitUnemployment = extracted(1, hrSocialSecurityDTO.getUnitUnemployment());
            hrSocialSecurity.setUnitUnemployment(getUnitUnemployment);
        }

        if (hrSocialSecurityDTO.getUnitMedical() != null) {
            BigDecimal getUnitMedical = extracted(1, hrSocialSecurityDTO.getUnitMedical());
            hrSocialSecurity.setUnitMedical(getUnitMedical);
        }
        if (hrSocialSecurityDTO.getUnitMaternity() != null) {
            BigDecimal getUnitMaternity = extracted(1, hrSocialSecurityDTO.getUnitMaternity());
            hrSocialSecurity.setUnitMaternity(getUnitMaternity);
        }
        if (hrSocialSecurityDTO.getPersonalMaternity() != null) {
            BigDecimal getPersonalMaternity = extracted(1, hrSocialSecurityDTO.getPersonalMaternity());
            hrSocialSecurity.setPersonalMaternity(getPersonalMaternity);
        }
    }

    /**
     * 修改社保医保表
     *
     * @param hrSocialSecurityDTO
     * @return
     */
    @Override
    public Optional<List<String>> updateHrSocialSecurity(HrSocialSecurityDTO hrSocialSecurityDTO) {
        return Optional.ofNullable(this.hrSocialSecurityRepository.selectById(hrSocialSecurityDTO.getId()))
            .map(roleTemp -> {
                HrSocialSecurity hrSocialSecurity = this.hrSocialSecurityMapper.toEntity(hrSocialSecurityDTO);
                //社保类型名称不能重复
                if (!roleTemp.getSocialSecurityName().equals(hrSocialSecurityDTO.getSocialSecurityName())) {
                    List<HrSocialSecurity> list = getHrSocialSecurities(hrSocialSecurityDTO.getSocialSecurityName());
                    if (CollectionUtils.isNotEmpty(list)) {
                        throw new CommonException("社保类型名称不可以重复！");
                    }
                }
                AssignmentRatio(hrSocialSecurityDTO, hrSocialSecurity);

                List<String> ids = new ArrayList<>();
                // 检查缴费年月
                if (StringUtils.isNotBlank(hrSocialSecurityDTO.getPaymentDate())) {
                    String[] split = hrSocialSecurityDTO.getPaymentDate().split("-");
                    int payYear = Integer.parseInt(split[0]);
                    int payMonthly = Integer.parseInt(split[1]);
                    if (payYear != LocalDate.now().getYear()) {
                        throw new CommonException("缴费年月只能选择当前年！");
                    }
                    if (payMonthly > LocalDate.now().getMonthValue()) {
                        throw new CommonException("缴费年月不能大于本月！");
                    }
                    // 检查社保比例是否发生变化
                    HrWelfareCompensationRecord hrWelfareCompensationRecord = this.checkScaleIsChange(roleTemp, hrSocialSecurity);
                    if (hrWelfareCompensationRecord != null) {
                        List<HrClient> clientList = this.hrSocialSecurityRepository.getClientBySocialSecurityTypeId(hrSocialSecurity.getId());
                        clientList.forEach(client -> {
                            int loopEndMonth = 0;
                            // 获取该员工公司的最近审核通过账单
                            HrFeeReview hrFeeReview = this.hrFeeReviewRepository.selectNewestRecordByClientId(client.getId());
                            if (hrFeeReview == null) {
                                loopEndMonth = LocalDate.now().getMonthValue() - 1;
                            } else {
                                // 判断出账单缴费年月是否跨年
                                if (hrFeeReview.getPayYear() == LocalDate.now().getYear()) {
                                    loopEndMonth = hrFeeReview.getPayMonthly();
                                }
                            }
                            List<HrEmployeeWelfareDTO> hrEmployeeWelfareList = this.hrStaffEmolumentRepository.selectByClientId(client.getId());
                            int finalLoopEndMonth = loopEndMonth;
                            hrEmployeeWelfareList.forEach(employeeWelfareOld -> {
                                if (employeeWelfareOld.getPayMonthly() != null && finalLoopEndMonth != 0 && this.checkWelfareIsComplete(employeeWelfareOld)) {
                                    for (int i = payMonthly; i <= employeeWelfareOld.getPayMonthly(); i++) {
                                        HrWelfareCompensation hrWelfareCompensation = this.calculateCompensation(employeeWelfareOld, hrSocialSecurity, payYear, i, hrWelfareCompensationRecord.getId());
                                        this.hrWelfareCompensationRepository.insert(hrWelfareCompensation);

                                        String redisKey = RedisKeyEnum.currencyKey.PAYMENT_DATE.getValue() + employeeWelfareOld.getId();
                                        this.redisCache.setCacheObject(redisKey, JSON.toJSONString(employeeWelfareOld), 24, TimeUnit.HOURS);

                                        // 更新员工缴费年月
                                        this.hrStaffEmolumentRepository.updateStaffPaymentDate(employeeWelfareOld.getId(), payYear, payMonthly);
                                        ids.add(hrWelfareCompensation.getId());
                                    }
                                }
                            });
                        });
                    }
                }

                String redisKey = RedisKeyEnum.currencyKey.SOCIAL_SECURITY.getValue() + roleTemp.getId();
                this.redisCache.setCacheObject(redisKey, JSON.toJSONString(roleTemp), 24, TimeUnit.HOURS);

                this.hrSocialSecurityRepository.updateHrSocialSecurity(hrSocialSecurity);
                //处理社保类型配置信息
                HrSocialSecurityConfigDTO securityConfigDTO = handleHrSocialSecurityConfig(hrSocialSecurityDTO, hrSocialSecurity.getId());
                if (hrSocialSecurityDTO.getSocialSecurityConfigId() != null) {
                    securityConfigDTO.setId(hrSocialSecurityDTO.getSocialSecurityConfigId());
                    hrSocialSecurityConfigService.updateHrSocialSecurityConfig(securityConfigDTO);
                } else {
                    hrSocialSecurityConfigService.createHrSocialSecurityConfig(securityConfigDTO);
                }
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.SOCIAL_SECURITY.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrSocialSecurityDTO),
                    HrSocialSecurityDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrSocialSecurityDTO),
                    null,
                    HrSocialSecurityDTO.class
                );
                log.info("Update HrSocialSecurity:{}", hrSocialSecurityDTO);
                return ids;
            });
    }

    private HrWelfareCompensation calculateCompensation(HrEmployeeWelfareDTO employeeWelfareOld, HrSocialSecurity hrSocialSecurity, int payYear, int payMonthly, String recordId) {
        // 查询缴费年月员工福利记录
        Map<String, Object> query = new HashMap<>();
        query.put("staffId", employeeWelfareOld.getId());
        query.put("payYear", payYear);
        query.put("payMonthly", 0);
        HrStaffWelfareRecord benchmarkRecord = this.hrStaffWelfareRecordService.selectByObject(query);
        // 检查基准数据
        if (benchmarkRecord == null) {
            benchmarkRecord = new HrStaffWelfareRecord()
                .setStaffId(employeeWelfareOld.getId())
                .setPayYear(payYear)
                .setPayMonthly(0)
                .setUnitPensionCardinalBase(employeeWelfareOld.getUnitPensionCardinal())
                .setUnitUnemploymentCardinalBase(employeeWelfareOld.getUnitUnemploymentCardinal())
                .setWorkInjuryCardinalBase(employeeWelfareOld.getWorkInjuryCardinal())
                .setUnitMaternityCardinalBase(employeeWelfareOld.getUnitMaternityCardinal())
                .setMedicalInsuranceCardinalBase(employeeWelfareOld.getMedicalInsuranceCardinal())
                .setAccumulationFundCardinalBase(employeeWelfareOld.getAccumulationFundCardinal())
                .setPersonalPensionCardinalBase(employeeWelfareOld.getPersonalPensionCardinal())
                .setPersonalUnemploymentCardinalBase(employeeWelfareOld.getPersonalUnemploymentCardinal())
                .setMedicalInsuranceCardinalBasePersonal(employeeWelfareOld.getMedicalInsuranceCardinalPersonal())

                .setUnitLargeMedicalExpenseBase(employeeWelfareOld.getUnitLargeMedicalExpense())
                .setReplenishWorkInjuryExpenseBase(employeeWelfareOld.getReplenishWorkInjuryExpense())
                .setPersonalLargeMedicalExpenseBase(employeeWelfareOld.getPersonalLargeMedicalExpense())

                .setPersonalMaternityCardinalBase(employeeWelfareOld.getPersonalMaternityCardinal())
                .setPersonalMaternityScale(employeeWelfareOld.getPersonalMaternity())

                .setUnitPensionScale(employeeWelfareOld.getUnitPension())
                .setUnitMedicalScale(employeeWelfareOld.getUnitMedical())
                .setWorkInjuryScale(employeeWelfareOld.getWorkInjury())
                .setUnitUnemploymentScale(employeeWelfareOld.getUnitUnemployment())
                .setUnitMaternityScale(employeeWelfareOld.getUnitMaternity())
                .setPersonalPensionScale(employeeWelfareOld.getPersonalPension())
                .setPersonalMedicalScale(employeeWelfareOld.getPersonalMedical())
                .setPersonalUnemploymentScale(employeeWelfareOld.getPersonalUnemployment())
                .setUnitAccumulationFundScale(employeeWelfareOld.getUnitScale())
                .setPersonalAccumulationFundScale(employeeWelfareOld.getPersonageScale());
            this.hrStaffWelfareRecordRepository.insert(benchmarkRecord);
        }
        query.put("payMonthly", payMonthly);
        HrStaffWelfareRecord hrStaffWelfareRecord = this.hrStaffWelfareRecordService.selectByObject(query);

        BigDecimal unitPensionOld;
        BigDecimal unitPensionNew;
        BigDecimal unitUnemploymentOld;
        BigDecimal unitUnemploymentNew;
        BigDecimal unitMedicalOld;
        BigDecimal unitMedicalNew;
        BigDecimal unitWorkInjuryOld;
        BigDecimal unitWorkInjuryNew;
        BigDecimal unitMaternityOld;
        BigDecimal unitMaternityNew;
        BigDecimal personagePensionOld;
        BigDecimal personagePensionNew;
        BigDecimal personageUnemploymentOld;
        BigDecimal personageUnemploymentNew;
        BigDecimal personalMedicalOld;
        BigDecimal personalMedicalNew;
        BigDecimal unitLargeMedicalOld;
        BigDecimal unitLargeMedicalNew;
        BigDecimal replenishWorkInjuryOld;
        BigDecimal replenishWorkInjuryNew;
        BigDecimal personalLargeMedicalOld;
        BigDecimal personalLargeMedicalNew;
        BigDecimal personalMaternityOld;
        BigDecimal personalMaternityNew;

        if (hrStaffWelfareRecord == null) {
            // 单位补差
            // 养老
            unitPensionOld = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitPensionCardinalBase(), benchmarkRecord.getUnitPensionScale(), 2);
            unitPensionNew = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitPensionCardinalBase(), hrSocialSecurity.getUnitPension(), 2);
            // 失业
            unitUnemploymentOld = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitUnemploymentCardinalBase(), benchmarkRecord.getUnitUnemploymentScale(), 2);
            unitUnemploymentNew = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitUnemploymentCardinalBase(), hrSocialSecurity.getUnitUnemployment(), 2);
            // 医疗
            unitMedicalOld = CalculateUtils.decimalMultiply(benchmarkRecord.getMedicalInsuranceCardinalBase(), benchmarkRecord.getUnitMedicalScale(), 2);
            unitMedicalNew = CalculateUtils.decimalMultiply(benchmarkRecord.getMedicalInsuranceCardinalBase(), hrSocialSecurity.getUnitMedical(), 2);
            // 工伤
            unitWorkInjuryOld = CalculateUtils.decimalMultiply(benchmarkRecord.getWorkInjuryCardinalBase(), benchmarkRecord.getWorkInjuryScale(), 2);
            unitWorkInjuryNew = CalculateUtils.decimalMultiply(benchmarkRecord.getWorkInjuryCardinalBase(), hrSocialSecurity.getWorkInjury(), 2);
            // 生育
            unitMaternityOld = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitMaternityCardinalBase(), benchmarkRecord.getUnitMaternityScale(), 2);
            unitMaternityNew = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitMaternityCardinalBase(), hrSocialSecurity.getUnitMaternity(), 2);
            // 大额医疗
            unitLargeMedicalOld = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitLargeMedicalExpenseBase(), new BigDecimal(1), 2);
            unitLargeMedicalNew = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitLargeMedicalExpenseBase(), new BigDecimal(1), 2);
            // 补充工伤
            replenishWorkInjuryOld = CalculateUtils.decimalMultiply(benchmarkRecord.getReplenishWorkInjuryExpenseBase(), new BigDecimal(1), 2);
            replenishWorkInjuryNew = CalculateUtils.decimalMultiply(benchmarkRecord.getReplenishWorkInjuryExpenseBase(), new BigDecimal(1), 2);

            // 个人补差
            // 养老
            personagePensionOld = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalPensionCardinalBase(), benchmarkRecord.getPersonalPensionScale(), 2);
            personagePensionNew = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalPensionCardinalBase(), hrSocialSecurity.getPersonalPension(), 2);
            // 失业
            personageUnemploymentOld = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalUnemploymentCardinalBase(), benchmarkRecord.getPersonalUnemploymentScale(), 2);
            personageUnemploymentNew = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalUnemploymentCardinalBase(), hrSocialSecurity.getPersonalUnemployment(), 2);
            // 医疗
            personalMedicalOld = CalculateUtils.decimalMultiply(benchmarkRecord.getMedicalInsuranceCardinalBase(), benchmarkRecord.getPersonalMedicalScale(), 2);
            personalMedicalNew = CalculateUtils.decimalMultiply(benchmarkRecord.getMedicalInsuranceCardinalBase(), hrSocialSecurity.getPersonalMedical(), 2);
            // 生育
            personalMaternityOld = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalMaternityCardinalBase(), benchmarkRecord.getPersonalMaternityScale(), 2);
            personalMaternityNew = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalMaternityCardinalBase(), hrSocialSecurity.getPersonalMaternity(), 2);
            // 大额医疗
            personalLargeMedicalOld = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalLargeMedicalExpenseBase(), new BigDecimal(1), 2);
            personalLargeMedicalNew = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalLargeMedicalExpenseBase(), new BigDecimal(1), 2);
        } else {
            // 单位补差
            // 养老
            unitPensionOld = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getUnitPensionCardinalBase(), hrStaffWelfareRecord.getUnitPensionScale(), 2);
            unitPensionNew = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getUnitPensionCardinalBase(), hrSocialSecurity.getUnitPension(), 2);
            // 失业
            unitUnemploymentOld = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getUnitUnemploymentCardinalBase(), hrStaffWelfareRecord.getUnitUnemploymentScale(), 2);
            unitUnemploymentNew = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getUnitUnemploymentCardinalBase(), hrSocialSecurity.getUnitUnemployment(), 2);
            // 医疗
            unitMedicalOld = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getMedicalInsuranceCardinalBase(), hrStaffWelfareRecord.getUnitMedicalScale(), 2);
            unitMedicalNew = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getMedicalInsuranceCardinalBase(), hrSocialSecurity.getUnitMedical(), 2);
            // 工伤
            unitWorkInjuryOld = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getWorkInjuryCardinalBase(), hrStaffWelfareRecord.getWorkInjuryScale(), 2);
            unitWorkInjuryNew = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getWorkInjuryCardinalBase(), hrSocialSecurity.getWorkInjury(), 2);
            // 生育
            unitMaternityOld = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitMaternityCardinalBase(), hrStaffWelfareRecord.getUnitMaternityScale(), 2);
            unitMaternityNew = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitMaternityCardinalBase(), hrSocialSecurity.getUnitMaternity(), 2);
            // 大额医疗
            unitLargeMedicalOld = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitLargeMedicalExpenseBase(), new BigDecimal(1), 2);
            unitLargeMedicalNew = CalculateUtils.decimalMultiply(benchmarkRecord.getUnitLargeMedicalExpenseBase(), new BigDecimal(1), 2);
            // 补充工伤
            replenishWorkInjuryOld = CalculateUtils.decimalMultiply(benchmarkRecord.getReplenishWorkInjuryExpenseBase(), new BigDecimal(1), 2);
            replenishWorkInjuryNew = CalculateUtils.decimalMultiply(benchmarkRecord.getReplenishWorkInjuryExpenseBase(), new BigDecimal(1), 2);


            // 个人补差
            // 养老
            personagePensionOld = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getPersonalPensionCardinalBase(), hrStaffWelfareRecord.getPersonalPensionScale(), 2);
            personagePensionNew = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getPersonalPensionCardinalBase(), hrSocialSecurity.getPersonalPension(), 2);
            // 失业
            personageUnemploymentOld = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getPersonalUnemploymentCardinalBase(), hrStaffWelfareRecord.getPersonalUnemploymentScale(), 2);
            personageUnemploymentNew = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getPersonalUnemploymentCardinalBase(), hrSocialSecurity.getPersonalUnemployment(), 2);
            // 医疗
            personalMedicalOld = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getMedicalInsuranceCardinalBase(), hrStaffWelfareRecord.getPersonalMedicalScale(), 2);
            personalMedicalNew = CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getMedicalInsuranceCardinalBase(), hrSocialSecurity.getPersonalMedical(), 2);
            // 生育
            personalMaternityOld = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalMaternityCardinalBase(), benchmarkRecord.getPersonalMaternityScale(), 2);
            personalMaternityNew = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalMaternityCardinalBase(), hrSocialSecurity.getPersonalMaternity(), 2);
            // 大额医疗
            personalLargeMedicalOld = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalLargeMedicalExpenseBase(), new BigDecimal(1), 2);
            personalLargeMedicalNew = CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalLargeMedicalExpenseBase(), new BigDecimal(1), 2);
        }

        // 更新员工每月福利配置
        // this.hrStaffWelfareRecordService.insertOrUpdateStaffWelfareRecord(employeeWelfareOld, hrStaffWelfareRecord, hrSocialSecurity, payYear, payMonthly);

        // 计算补差数据
        HrWelfareCompensation hrWelfareCompensation = new HrWelfareCompensation();
        hrWelfareCompensation.setIdNo(employeeWelfareOld.getCertificateNum());
        hrWelfareCompensation.setStaffId(employeeWelfareOld.getId());
        hrWelfareCompensation.setClientId(employeeWelfareOld.getClientId());
        hrWelfareCompensation.setPayYear(payYear);
        hrWelfareCompensation.setPayMonthly(payMonthly);
        // 单位补差
        hrWelfareCompensation.setUnitPension(CalculateUtils.decimalSubtraction(unitPensionNew, unitPensionOld));
        hrWelfareCompensation.setUnitUnemployment(CalculateUtils.decimalSubtraction(unitUnemploymentNew, unitUnemploymentOld));
        hrWelfareCompensation.setUnitMedical(CalculateUtils.decimalSubtraction(unitMedicalNew, unitMedicalOld));
        hrWelfareCompensation.setUnitInjury(CalculateUtils.decimalSubtraction(unitWorkInjuryNew, unitWorkInjuryOld));
        hrWelfareCompensation.setUnitMaternity(CalculateUtils.decimalSubtraction(unitMaternityNew, unitMaternityOld));
        hrWelfareCompensation.setUnitLargeMedical(CalculateUtils.decimalSubtraction(unitLargeMedicalNew, unitLargeMedicalOld));
        hrWelfareCompensation.setReplenishWorkInjury(CalculateUtils.decimalSubtraction(replenishWorkInjuryNew, replenishWorkInjuryOld));
        hrWelfareCompensation.setUnitSubtotal(CalculateUtils.decimalListAddition(hrWelfareCompensation.getUnitPension(), hrWelfareCompensation.getUnitUnemployment(),
            hrWelfareCompensation.getUnitMedical(), hrWelfareCompensation.getUnitInjury(), hrWelfareCompensation.getUnitMaternity()));
        // 个人补差
        hrWelfareCompensation.setPersonalPension(CalculateUtils.decimalSubtraction(personagePensionNew, personagePensionOld));
        hrWelfareCompensation.setPersonalUnemployment(CalculateUtils.decimalSubtraction(personageUnemploymentNew, personageUnemploymentOld));
        hrWelfareCompensation.setPersonalMedical(CalculateUtils.decimalSubtraction(personalMedicalNew, personalMedicalOld));
        hrWelfareCompensation.setPersonalLargeMedical(CalculateUtils.decimalSubtraction(personalLargeMedicalNew, personalLargeMedicalOld));
        hrWelfareCompensation.setPersonalMaternity(CalculateUtils.decimalSubtraction(personalMaternityNew, personalMaternityOld));
        hrWelfareCompensation.setPersonalSubtotal(CalculateUtils.decimalListAddition(hrWelfareCompensation.getPersonalPension(), hrWelfareCompensation.getPersonalUnemployment(),
            hrWelfareCompensation.getPersonalMedical(), hrWelfareCompensation.getPersonalMaternity()));
        // 社保补差总计
        hrWelfareCompensation.setSocialSecurityTotal(CalculateUtils.decimalListAddition(hrWelfareCompensation.getUnitSubtotal(), hrWelfareCompensation.getPersonalSubtotal()));

        hrWelfareCompensation.setIsUsed(-1);
        hrWelfareCompensation.setType(0);
        hrWelfareCompensation.setRecordId(recordId);

        return hrWelfareCompensation;
    }

    /**
     * 判断员工薪酬配置是否完整
     *
     * @param employeeWelfareOld
     * @return boolean
     * <AUTHOR>
     * @date 2021/11/1
     **/
    private boolean checkWelfareIsComplete(HrEmployeeWelfareDTO employeeWelfareOld) {
        /*if (employeeWelfareOld.getSocialSecurityCardinal() == null) {
            return false;
        }
        if (employeeWelfareOld.getMedicalInsuranceCardinal() == null) {
            return false;
        }*/
        if (employeeWelfareOld.getUnitPensionCardinal() == null
            || employeeWelfareOld.getUnitUnemploymentCardinal() == null
            || employeeWelfareOld.getWorkInjuryCardinal() == null
            || employeeWelfareOld.getUnitMaternityCardinal() == null
            || employeeWelfareOld.getMedicalInsuranceCardinal() == null
            || employeeWelfareOld.getPersonalPensionCardinal() == null
            || employeeWelfareOld.getPersonalUnemploymentCardinal() == null
            || employeeWelfareOld.getPersonalMaternityCardinal() == null
            || employeeWelfareOld.getMedicalInsuranceCardinalPersonal() == null
        ) {
            return false;
        }
        return true;
    }

    /**
     * 检查社保比例是否变化
     *
     * @param oldDate
     * @param newDate
     * @return boolean
     * <AUTHOR>
     * @date 2021/11/1
     **/
    private HrWelfareCompensationRecord checkScaleIsChange(HrSocialSecurity oldDate, HrSocialSecurity newDate) {
        StringBuilder sb = new StringBuilder();
        boolean result = false;

        if (!BigDecimalCompare.of(oldDate.getUnitPension()).eq(newDate.getUnitPension())) {
            sb.append("单位养老比例：修改前").append(oldDate.getUnitPension()).append("，").append("修改后").append(newDate.getUnitPension()).append("。");
            result = true;
        }
        if (!BigDecimalCompare.of(oldDate.getWorkInjury()).eq(newDate.getWorkInjury())) {
            sb.append("单位工伤比例：修改前").append(oldDate.getWorkInjury()).append("，").append("修改后").append(newDate.getWorkInjury()).append("。");
            result = true;
        }
        if (!BigDecimalCompare.of(oldDate.getUnitUnemployment()).eq(newDate.getUnitUnemployment())) {
            sb.append("单位失业比例：修改前").append(oldDate.getUnitUnemployment()).append("，").append("修改后").append(newDate.getUnitUnemployment()).append("。");
            result = true;
        }
        if (!BigDecimalCompare.of(oldDate.getUnitMedical()).eq(newDate.getUnitMedical())) {
            sb.append("单位医疗比例：修改前").append(oldDate.getUnitMedical()).append("，").append("修改后").append(newDate.getUnitMedical()).append("。");
            result = true;
        }
        if (!BigDecimalCompare.of(oldDate.getUnitMaternity()).eq(newDate.getUnitMaternity())) {
            sb.append("单位生育比例：修改前").append(oldDate.getUnitMaternity()).append("，").append("修改后").append(newDate.getUnitMaternity()).append("。");
            result = true;
        }
        if (!BigDecimalCompare.of(oldDate.getPersonalPension()).eq(newDate.getPersonalPension())) {
            sb.append("个人养老比例：修改前").append(oldDate.getPersonalPension()).append("，").append("修改后").append(newDate.getPersonalPension()).append("。");
            result = true;
        }
        if (!BigDecimalCompare.of(oldDate.getPersonalUnemployment()).eq(newDate.getPersonalUnemployment())) {
            sb.append("个人失业比例：修改前").append(oldDate.getPersonalUnemployment()).append("，").append("修改后").append(newDate.getPersonalUnemployment()).append("。");
            result = true;
        }
        if (!BigDecimalCompare.of(oldDate.getPersonalMedical()).eq(newDate.getPersonalMedical())) {
            sb.append("个人医疗比例：修改前").append(oldDate.getPersonalMedical()).append("，").append("修改后").append(newDate.getPersonalMedical()).append("。");
            result = true;
        }
        if (!BigDecimalCompare.of(oldDate.getPersonalMaternity()).eq(newDate.getPersonalMaternity())) {
            sb.append("个人生育比例：修改前").append(oldDate.getPersonalMaternity()).append("，").append("修改后").append(newDate.getPersonalMaternity()).append("。");
            result = true;
        }

        // 产生变动 添加变动日志
        if (result) {
            return this.hrWelfareCompensationRecordService.addLog(oldDate, newDate, sb.toString());
        }
        return null;
    }

    /**
     * 查询社保医保表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrSocialSecurityDTO getHrSocialSecurity(String id) {
        log.info("Get HrSocialSecurity :{}", id);

        HrSocialSecurity hrSocialSecurity = this.hrSocialSecurityRepository.selectById(id);
        if (hrSocialSecurity == null) {
            return null;
        }
        HrSocialSecurityDTO hrSocialSecurityDTO = this.hrSocialSecurityMapper.toDto(hrSocialSecurity);
        this.extractedPercentage(hrSocialSecurityDTO);
        this.getBySocialSecurityId(id, hrSocialSecurityDTO);
        return hrSocialSecurityDTO;
    }

    private void getBySocialSecurityId(String securityId, HrSocialSecurityDTO hrSocialSecurityDTO) {
        HrSocialSecurityConfigDTO configDTO = hrSocialSecurityConfigService.getBySocialSecurityId(securityId);
        if (configDTO != null) {
            hrSocialSecurityDTO.setSocialSecurityConfigId(configDTO.getId());
            if (StringUtils.isNotBlank(configDTO.getSpecialField()) && !configDTO.getSpecialField().equals("null")) {
                hrSocialSecurityDTO.setSpecialField(configDTO.getSpecialField());
            }
            if (StringUtils.isNotBlank(configDTO.getAloneCardinal()) && !configDTO.getAloneCardinal().equals("null")) {
                hrSocialSecurityDTO.setAloneCardinal(configDTO.getAloneCardinal());
            }
            if (StringUtils.isNotBlank(configDTO.getMergeCardinal()) && !configDTO.getMergeCardinal().equals("null")) {
                hrSocialSecurityDTO.setMergeCardinal(configDTO.getMergeCardinal());
            }
        }
    }

    /**
     * 删除社保医保表
     *
     * @param id
     */
    @Override
    public void deleteHrSocialSecurity(String id) {
        Optional.ofNullable(this.hrSocialSecurityRepository.selectById(id))
            .ifPresent(hrSocialSecurity -> {
                this.hrSocialSecurityRepository.deleteById(id);
                log.info("Delete HrSocialSecurity:{}", hrSocialSecurity);
            });
    }

    /**
     * 批量删除社保医保表
     *
     * @param ids
     */
    @Override
    public void deleteHrSocialSecurity(List<String> ids) {
        log.info("Delete HrSocialSecuritys:{}", ids);
        List<HrSocialSecurity> hrSocialSecurityList = hrSocialSecurityRepository.selectBatchIds(ids);
        this.hrSocialSecurityRepository.deleteBatchIds(ids);
        // 操作日志
        //this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.SOCIAL_SECURITY.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> collect = hrSocialSecurityList.stream().map(HrSocialSecurity::getSocialSecurityName).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.SOCIAL_SECURITY.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除社保类型: " + JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(hrSocialSecurityList),
            jwtUserDTO
        );
    }

    /**
     * 分页查询社保医保表
     *
     * @param hrSocialSecurityDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrSocialSecurityDTO hrSocialSecurityDTO, Long pageNumber, Long pageSize) {
        Page<HrSocialSecurity> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrSocialSecurity> qw = new QueryWrapper<>();
        qw.like(StringUtils.isNotBlank(hrSocialSecurityDTO.getSocialSecurityName()), "social_security_name", hrSocialSecurityDTO.getSocialSecurityName());
        //排序
        if (StringUtils.isNotBlank(hrSocialSecurityDTO.getOrder())) {
            if (hrSocialSecurityDTO.getOrder().equals("DESC")) {
                qw.orderBy(StringUtils.isNotBlank(hrSocialSecurityDTO.getField()), false, hrSocialSecurityDTO.getField());
            } else {
                qw.orderBy(StringUtils.isNotBlank(hrSocialSecurityDTO.getField()), true, hrSocialSecurityDTO.getField());
            }

        }
        IPage iPage = this.hrSocialSecurityRepository.selectPage(page, qw);
        List<HrSocialSecurityDTO> list = this.hrSocialSecurityMapper.toDto(iPage.getRecords());
        for (HrSocialSecurityDTO socialSecurityDTO : list) {
            QueryWrapper<HrClient> hrClientQueryWrapper = new QueryWrapper<>();
            hrClientQueryWrapper.eq("social_security_type_id", socialSecurityDTO.getId());
            int count = hrClientService.count(hrClientQueryWrapper);
            socialSecurityDTO.setClientNumber(count);
            this.extractedPercentage(socialSecurityDTO);
        }
        iPage.setRecords(list);
        return iPage;
    }

    /**
     * 转换百分比
     *
     * @param socialSecurityDTO
     */
    private void extractedPercentage(HrSocialSecurityDTO socialSecurityDTO) {
        if (socialSecurityDTO.getUnitPension() != null) {
            BigDecimal unitPension = extracted(0, socialSecurityDTO.getUnitPension());
            socialSecurityDTO.setUnitPension(unitPension);
        }
        if (socialSecurityDTO.getWorkInjury() != null) {
            BigDecimal getWorkInjury = extracted(0, socialSecurityDTO.getWorkInjury());
            socialSecurityDTO.setWorkInjury(getWorkInjury);
        }
        if (socialSecurityDTO.getPersonalMedical() != null) {
            BigDecimal getPersonalMedical = extracted(0, socialSecurityDTO.getPersonalMedical());
            socialSecurityDTO.setPersonalMedical(getPersonalMedical);
        }
        if (socialSecurityDTO.getPersonalPension() != null) {
            BigDecimal getPersonalPension = extracted(0, socialSecurityDTO.getPersonalPension());
            socialSecurityDTO.setPersonalPension(getPersonalPension);
        }
        if (socialSecurityDTO.getPersonalUnemployment() != null) {
            BigDecimal getPersonalUnemployment = extracted(0, socialSecurityDTO.getPersonalUnemployment());
            socialSecurityDTO.setPersonalUnemployment(getPersonalUnemployment);
        }
        if (socialSecurityDTO.getUnitUnemployment() != null) {
            BigDecimal getUnitUnemployment = extracted(0, socialSecurityDTO.getUnitUnemployment());
            socialSecurityDTO.setUnitUnemployment(getUnitUnemployment);
        }
        if (socialSecurityDTO.getUnitMedical() != null) {
            BigDecimal getUnitMedical = extracted(0, socialSecurityDTO.getUnitMedical());
            socialSecurityDTO.setUnitMedical(getUnitMedical);
        }
        if (socialSecurityDTO.getUnitMaternity() != null) {
            BigDecimal getUnitMaternity = extracted(0, socialSecurityDTO.getUnitMaternity());
            socialSecurityDTO.setUnitMaternity(getUnitMaternity);
        }
        if (socialSecurityDTO.getPersonalMaternity() != null) {
            BigDecimal getPersonalMaternity = extracted(0, socialSecurityDTO.getPersonalMaternity());
            socialSecurityDTO.setPersonalMaternity(getPersonalMaternity);
        }
    }

    @Override
    public HrsocialDTO getSelectHrplatform() {
        HrsocialDTO hrsocialDTO = new HrsocialDTO();
        List<HrPlatformAccountDTO> Hrplatform = this.hrSocialSecurityRepository.getSelectHrplatform();
        List<HrAccumulationFundDTO> AccumulationFund = this.hrSocialSecurityRepository.getAccumulationFund();
        List<HrSocialSecurityDTO> SocialSecurity = this.hrSocialSecurityRepository.getSocialSecurity();
        hrsocialDTO.setHrPlatformAccountDTO(Hrplatform);
        hrsocialDTO.setHrAccumulationFundDTO(AccumulationFund);
        hrsocialDTO.setHrSocialSecurityDTO(SocialSecurity);
        return hrsocialDTO;
    }

    @Override
    public String exportSocial(HrSocialSecurityDTO socialSecurityDTO, HttpServletResponse httpServletResponse) {
        QueryWrapper<HrSocialSecurity> qw = new QueryWrapper<>();
        qw.like(StringUtils.isNotBlank(socialSecurityDTO.getSocialSecurityName()), "social_security_name", socialSecurityDTO.getSocialSecurityName());
        qw.in(CollectionUtils.isNotEmpty(socialSecurityDTO.getIds()), "id", socialSecurityDTO.getIds());
        qw.eq("is_delete", 0);
        List<HrSocialSecurity> list = this.hrSocialSecurityRepository.selectList(qw);
        if (list.isEmpty()) {
            throw new CommonException("未查到数据");
        }
        ArrayList<HrSocialSecurityTemplate> templateArrayList = new ArrayList<>();
        for (HrSocialSecurity hrSocialSecurity : list) {
            HrSocialSecurityTemplate hrSocialSecurityTemplate = new HrSocialSecurityTemplate();
            BeanUtils.copyProperties(hrSocialSecurity, hrSocialSecurityTemplate);
            templateArrayList.add(hrSocialSecurityTemplate);
        }
        for (HrSocialSecurityTemplate hrSocialSecurityDTO : templateArrayList) {

            BigDecimal unitPension = extracted(0, hrSocialSecurityDTO.getUnitPension());
            hrSocialSecurityDTO.setUnitPensionExcel(getToString(unitPension));

            BigDecimal getWorkInjury = extracted(0, hrSocialSecurityDTO.getWorkInjury());
            hrSocialSecurityDTO.setWorkInjuryExcel(getToString(getWorkInjury));

            BigDecimal getPersonalMedical = extracted(0, hrSocialSecurityDTO.getPersonalMedical());
            hrSocialSecurityDTO.setPersonalMedicalExcel(getToString(getPersonalMedical));

            BigDecimal getPersonalPension = extracted(0, hrSocialSecurityDTO.getPersonalPension());
            hrSocialSecurityDTO.setPersonalPensionExcel(getToString(getPersonalPension));

            BigDecimal getPersonalUnemployment = extracted(0, hrSocialSecurityDTO.getPersonalUnemployment());
            hrSocialSecurityDTO.setPersonalUnemploymentExcel(getToString(getPersonalUnemployment));

            BigDecimal getUnitUnemployment = extracted(0, hrSocialSecurityDTO.getUnitUnemployment());
            hrSocialSecurityDTO.setUnitUnemploymentExcel(getToString(getUnitUnemployment));

            BigDecimal getUnitMedical = extracted(0, hrSocialSecurityDTO.getUnitMedical());
            hrSocialSecurityDTO.setUnitMedicalExcel(getToString(getUnitMedical));

            BigDecimal getUnitMaternity = extracted(0, hrSocialSecurityDTO.getUnitMaternity());
            hrSocialSecurityDTO.setUnitMaternityExcel(getToString(getUnitMaternity));

            BigDecimal getPersonalMaternity = extracted(0, hrSocialSecurityDTO.getPersonalMaternity());
            hrSocialSecurityDTO.setPersonalMaternityExcel(getToString(getPersonalMaternity));
        }
        int listSize = templateArrayList.size();
        List<String> ids = list.stream().map(HrSocialSecurity::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(templateArrayList, "社保类型管理", HrSocialSecurityTemplate.class);
        // ExcelUtils.exportExcel(templateArrayList, "社保类型管理", HrSocialSecurityTemplate.class, httpServletResponse);
        // 操作日志
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.SOCIAL_SECURITY.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 转化称字符串并加"%"
     *
     * @param bigDecimal
     * @return
     */
    private String getToString(BigDecimal bigDecimal) {
        return bigDecimal.toString() + "%";
    }


    public String importSocial(MultipartFile file) {

        // 设置进度条
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.SOCIAL_SECURITY.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.accumulationFoundComponent.socialSecurityImport(inputStream, redisKey, jwtUserDTO);
        return redisKey;
    }

    /**
     * 社保类型模板
     *
     * @param response
     */
    @Override
    public String importSocialTemplate(HttpServletResponse response) {
        return excelPrefix + "社保类型导入模板.xlsx";
    }

    /**
     * 获取客户社保配置
     *
     * @param clientIdList
     * @return
     */
    @Override
    public List<HrSocialSecurityDTO> getClientSocialSecurity(List<String> clientIdList) {
        List<HrSocialSecurityDTO> clientSocialSecurity = hrSocialSecurityRepository.getClientSocialSecurity(clientIdList);
        return clientSocialSecurity;
    }

    /**
     * 处理小数
     *
     * @param type                0 乘法 1除法
     * @param personageScaleExcel
     * @return
     */
    private BigDecimal extracted(int type, BigDecimal personageScaleExcel) {
        BigDecimal bignum = new BigDecimal("100");
        BigDecimal multiply = null;
        if (type == 0) {
            multiply = personageScaleExcel.multiply(bignum).setScale(2, BigDecimal.ROUND_DOWN);
        } else {
            multiply = personageScaleExcel.divide(bignum, 4, BigDecimal.ROUND_DOWN);
        }
        return multiply;
    }
}
