<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.UserRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_name, `password`, open_id, real_name, phone, work_address, work_phone,user_status, profile_img, remark, created_by, created_date, is_delete, last_modified_by ,last_modified_date, billmaker, reimburse_type
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.dto.UserDTO">
        <id column="id" property="id"/>
        <result column="user_name" property="userName"/>
        <result column="password" property="password"/>
        <result column="open_id" property="openId"/>
        <result column="real_name" property="realName"/>
        <result column="phone" property="phone"/>
        <result column="work_address" property="workAddress"/>
        <result column="work_phone" property="workPhone"/>
        <result column="user_status" property="userStatus"/>
        <result column="profile_img" property="profileImg"/>
        <result column="remark" property="remark"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="is_delete" property="isDelete"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
        <result column="billmaker" property="billmaker"/>
        <result column="reimburse_type" property="reimburseType"/>
    </resultMap>

    <resultMap id="resultWithRole" type="cn.casair.dto.UserDTO">
        <id column="id" property="id"/>
        <result column="user_name" property="userName"/>
        <result column="password" property="password"/>
        <result column="open_id" property="openId"/>
        <result column="real_name" property="realName"/>
        <result column="phone" property="phone"/>
        <result column="work_address" property="workAddress"/>
        <result column="work_phone" property="workPhone"/>
        <result column="user_status" property="userStatus"/>
        <result column="profile_img" property="profileImg"/>
        <result column="remark" property="remark"/>
        <result column="created_date" property="createdDate"/>
        <collection property="roleList" select="getRoleByUserId" javaType="java.util.ArrayList" column="id" ofType="cn.casair.dto.RoleDTO">
        </collection>
    </resultMap>

    <select id="selectByPhone" resultType="cn.casair.dto.UserDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_user
        WHERE is_delete = 0
          AND phone = #{phone}
    </select>

    <select id="selectUserByOpenId" resultType="cn.casair.dto.UserDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_user
        WHERE is_delete = 0
          AND open_id = #{openId}
    </select>

    <select id="getUserWithRole" resultType="cn.casair.dto.UserDTO">
        SELECT u.*, r.`role_key`
        FROM sys_user u
                 LEFT JOIN sys_user_role ur ON u.`id` = ur.`user_id` AND ur.`is_delete` = 0
                 LEFT JOIN sys_role r ON r.`id` = ur.`role_id` AND r.`is_delete` = 0
        WHERE u.`is_delete` = 0
    </select>

    <!-- 分页查询(带角色的)用户列表 -->
    <select id="queryUserWithRoles" resultType="cn.casair.dto.UserDTO">
        select * from
        (        SELECT
        su.id,
        su.real_name,
        su.phone,
        su.work_address,
        su.work_phone,
        su.user_status,
        su.user_name,
        su.is_remove,
        su.created_date,
        su.billmaker,
        su.reimburse_type,
        GROUP_CONCAT(sur.role_id SEPARATOR ',') role_ids,
        GROUP_CONCAT(sr.role_name SEPARATOR ',') role_names,
        GROUP_CONCAT(sr.role_key SEPARATOR ',') role_keys,
        GROUP_CONCAT(sr.dept SEPARATOR ',') role_depts,
        GROUP_CONCAT(sr.is_restrictions SEPARATOR ',') role_restrictions
        FROM
        sys_user su
        LEFT JOIN sys_user_role sur ON sur.user_id = su.id  AND sur.is_delete = 0
        LEFT JOIN sys_role sr ON sur.role_id = sr.id AND sr.is_delete = 0
        <where>
            <!--            <if test="roleKeys != null">-->
            <!--                AND sr.role_key IN-->
            <!--                <foreach collection="roleKeys" open="(" separator="," close=")" item="val">-->
            <!--                    #{val}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <if test="param.realName != null and param.realName != ''">
                AND su.real_name LIKE concat('%', #{param.realName}, '%')
            </if>
            <if test="param.phone != null and param.phone != ''">
                AND su.phone LIKE concat('%', #{param.phone}, '%')
            </if>
            <if test="param.userName != null and param.userName != ''">
                AND su.user_name LIKE concat('%', #{param.userName}, '%')
            </if>
            <if test="param.userStatusList != null and param.userStatusList.size() > 0">
<!--                AND su.user_status = #{param.userStatus}-->
                AND su.user_status in
                <foreach collection="param.userStatusList" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>

            AND su.is_delete = 0
        </where>
        GROUP BY su.id,su.real_name,
        su.phone,su.user_status,su.user_name
        ) a
        <where>
        <if test="param.roleNameList != null and param.roleNameList.size() > 0">
            and
            <foreach collection="param.roleNameList" open="(" separator="or" close=")" item="val">
                role_names like concat('%',#{val},'%')
            </foreach>
        </if>
        <if test="param.deptList != null and param.deptList.size() > 0">
<!--            AND role_depts LIKE concat('%', #{param.dept}, '%')-->
            and role_depts in
            <foreach collection="param.deptList" open="(" separator="," close=")" item="val">
            #{val}
           </foreach>
        </if>
            <if test="param.isClient==1">
                and role_keys = 'client'
            </if>
            <if test="param.isClient==0">
                and role_keys != 'client'
            </if>
        </where>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            order by  case when role_names LIKE '超级管理员' or role_names LIKE '开发者' then 0 else 1 end, ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            order by  case when role_names LIKE '超级管理员' or role_names LIKE '开发者' then 0 else 1 end, created_date DESC
        </if>
    </select>

    <select id="getByRoles" resultType="cn.casair.dto.UserDTO">
        SELECT u.*, r.`role_key`, concat('(', r.role_name, ')', u.real_name) as userRoleName
        FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.`id` = ur.`user_id` AND ur.`is_delete` = 0
        LEFT JOIN sys_role r ON r.`id` = ur.`role_id` AND r.`is_delete` = 0
        WHERE u.`is_delete` = 0
        and r.role_key in
        <foreach collection="roleKeyList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

    <select id="getSpecialized" resultMap="BaseResultMap">
        SELECT
            su.id,
            su.real_name
        FROM
            sys_user su
                LEFT JOIN sys_user_role sur ON sur.user_id = su.id
                AND sur.is_delete = 0
                LEFT JOIN sys_role sr ON sur.role_id = sr.id
                AND sr.is_delete = 0
        WHERE
            su.is_delete = 0
          AND sr.role_key = 'customer_service_staff'
    </select>

    <select id="getUserInFor" resultType="cn.casair.dto.UserDTO">
        SELECT
          su.*,sr.role_key, sr.role_name
        FROM
            sys_user su
        LEFT JOIN sys_user_role sur ON su.id = sur.user_id AND sur.is_delete = 0
        LEFT JOIN sys_role sr ON sr.id = sur.role_id AND sr.is_delete = 0
        WHERE su.is_delete = 0 AND su.user_status = 1 AND su.id = #{userId}
    </select>

    <update id="updateStatus">
        UPDATE sys_user SET user_status = 1  WHERE id = #{userId} AND is_delete = 0
    </update>

    <update id="updateStatusBatch">
        UPDATE sys_user SET user_status = 1  WHERE is_delete = 0 AND id IN
        <foreach collection="userIds" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </update>
</mapper>
