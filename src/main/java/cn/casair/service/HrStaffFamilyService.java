package cn.casair.service;

import cn.casair.domain.HrStaffFamily;
import cn.casair.dto.HrStaffFamilyDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 家庭成员服务类
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface HrStaffFamilyService extends IService<HrStaffFamily> {


    /**
     * 创建家庭成员
     * @param hrStaffFamilyDTO
     * @return
     */
    List<HrStaffFamilyDTO> createHrStaffFamily(HrStaffFamilyDTO hrStaffFamilyDTO);

    /**
     * 修改家庭成员
     * @param hrStaffFamilyDTO
     * @return
     */
    Optional<List<HrStaffFamilyDTO>> updateHrStaffFamily(HrStaffFamilyDTO hrStaffFamilyDTO);

    /**
     * 查询家庭成员详情
     * @param id
     * @return
     */
    HrStaffFamilyDTO getHrStaffFamily(String id);

    /**
     * 删除家庭成员
     * @param id
     */
    List<HrStaffFamilyDTO> deleteHrStaffFamily(String id);

    /**
     * 查询家庭成员
     * @param staffId 员工ID
     * @return
     */
    List<HrStaffFamilyDTO> findFamilyList(String staffId);
}
