package cn.casair.repository;

import cn.casair.domain.HrBillReimbursementApply;
import cn.casair.dto.HrBillReimbursementApplyDTO;
import cn.casair.dto.HrBillReimbursementClientDTO;
import cn.casair.dto.HrBillTotalDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 报销申请数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Repository
public interface HrBillReimbursementApplyRepository extends BaseMapper<HrBillReimbursementApply> {

    /**
     * 查询支出
     *
     * @param hrBillTotalDTO
     * @param clientIds
     * @return java.util.List<cn.casair.domain.HrBillReimbursementApply>
     * <AUTHOR>
     * @date 2021/11/23
     **/
    List<HrBillReimbursementApplyDTO> selectExpenditure(@Param("params") HrBillTotalDTO hrBillTotalDTO, @Param("permissionClient") List<String> clientIds);

    /**
     *  获取报销申请详情
     * @param applyId 申请id
     * @return
     */
    HrBillReimbursementApplyDTO getById(String applyId);

    IPage<HrBillReimbursementApplyDTO> queryForPage(Page<HrBillReimbursementApply> page, @Param("param") HrBillReimbursementApplyDTO param);

    /**
     * 更新报销申请状态
     * @param id 结算单Id
     * @param approveStatus 申请状态
     */
    void updateApproveStatus(@Param("id") String id, @Param("approveStatus") Integer approveStatus, @Param("lockState") Integer lockState);

    void updateApproveStatusByApplyId(@Param("applyId") String applyId, @Param("approveStatus") Integer approveStatus, @Param("lockState") Integer lockState);

    /**
     * 查询报销申请
     * @param idList 申请Id集合
     * @return
     */
    List<HrBillReimbursementApplyDTO> selectByIdList(@Param("idList") List<String> idList);

    /**
     * 修改锁定状态
     * @param id
     * @param lockState
     */
    void updateLockState(@Param("id") String id, @Param("lockState") Integer lockState);

    /**
     * 查看报销缴纳账户信息
     * @param applyId
     * @return
     */
    List<HrBillReimbursementClientDTO> findAccountByApplyId(@Param("applyId") String applyId);

    /**
     * 查看报销代发工资账单信息
     * @param applyId
     * @return
     */
    List<HrBillReimbursementClientDTO> findBillByApplyId(@Param("applyId") String applyId);

    /**
     * 查看特殊客户明细汇总
     * @param applyId
     * @return
     */
    List<HrBillReimbursementClientDTO> findSpecialClientInfo(@Param("applyId") String applyId);

    /**
     * 修改客户账单锁定状态
     * @param realSalaryLock 代发工资
     * @param socialSecurityLock 代缴社保
     * @param medicalInsuranceLock 代缴医保
     * @param accumulationFoundLock 代缴公积金
     * @param ids
     */
    void updateClientLock(@Param("realSalaryLock") Integer realSalaryLock, @Param("socialSecurityLock") Integer socialSecurityLock,
                          @Param("medicalInsuranceLock") Integer medicalInsuranceLock, @Param("accumulationFoundLock") Integer accumulationFoundLock,
                          @Param("ids") List<String> ids);

    /**
     * 查询账单报销明细
     * @param billId
     * @return
     */
    List<HrBillReimbursementApplyDTO> getByBillId(@Param("billId") String billId);

    /**
     * 创建报销的结算单Id
     * @return
     * @param payYear
     * @param payMonth
     */
    List<String> selectBillId(@Param("payYear") Integer payYear, @Param("payMonth") Integer payMonth, @Param("accountType") Integer accountType);

    /**
     * 根据账单ID查询已发起的报销金额
     * @param payYear
     * @param payMonth
     * @param billIds
     * @return
     */
    List<HrBillReimbursementClientDTO> getAccountType(@Param("payYear") Integer payYear, @Param("payMonth") Integer payMonth, @Param("billIds") List<String> billIds);

    /**
     * 获取新建发起报销的结算单ID
     * @param payYear
     * @param payMonth
     * @param accountType
     * @return
     */
    List<String> selectClientApply(@Param("payYear") Integer payYear, @Param("payMonth") Integer payMonth, @Param("accountType") Integer accountType);

    /**
     * 根据结算单id查询手动新增的报销申请id
     *
     * @param feeReviewId
     * @return
     */
    List<String> selectApplyIdByFeeReviewId(@Param("feeReviewId") String feeReviewId);

    /**
     * 删除报销申请关联客户表
     * @param applyId
     */
    void delApplyClientId(@Param("applyId") String applyId);

    /**
     * 删除凭证
     *
     * @param hrBillReimbursementApplyId
     */
    @Update("update hr_bill_reimbursement_apply set nc_voucher = null, accounting_voucher_status = 0 where id = #{hrBillReimbursementApplyId}")
    void deleteVoucher(@Param("hrBillReimbursementApplyId") String hrBillReimbursementApplyId);

    /**
     * 首页申请列表查询
     * @param billReimbursementApplyDTO
     * @return
     */
    List<HrBillReimbursementApplyDTO> findCommonApplyList(@Param("param") HrBillReimbursementApplyDTO billReimbursementApplyDTO);

}
