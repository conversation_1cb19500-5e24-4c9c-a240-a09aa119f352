package cn.casair.service.impl;

import cn.casair.common.enums.RecruitmentBrochure;
import cn.casair.domain.HrRecruitmentDrafts;
import cn.casair.dto.HrRecruitmentDraftsDTO;
import cn.casair.mapper.HrRecruitmentDraftsMapper;
import cn.casair.repository.HrRecruitmentDraftsRepository;
import cn.casair.service.HrRecruitmentDraftsService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 招聘草稿箱服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrRecruitmentDraftsServiceImpl extends ServiceImpl<HrRecruitmentDraftsRepository, HrRecruitmentDrafts> implements HrRecruitmentDraftsService {

    private final HrRecruitmentDraftsRepository hrRecruitmentDraftsRepository;
    private final HrRecruitmentDraftsMapper hrRecruitmentDraftsMapper;


    /**
     * 创建招聘草稿箱
     *
     * @param hrRecruitmentDraftsDTO
     * @return
     */
    @Override
    public HrRecruitmentDraftsDTO createHrRecruitmentDrafts(HrRecruitmentDraftsDTO hrRecruitmentDraftsDTO) {
        log.info("Create new HrRecruitmentDrafts:{}", hrRecruitmentDraftsDTO);

        HrRecruitmentDrafts hrRecruitmentDrafts = this.hrRecruitmentDraftsMapper.toEntity(hrRecruitmentDraftsDTO);
        this.hrRecruitmentDraftsRepository.insert(hrRecruitmentDrafts);
        return this.hrRecruitmentDraftsMapper.toDto(hrRecruitmentDrafts);
    }

    /**
     * 修改招聘草稿箱
     *
     * @param hrRecruitmentDraftsDTO
     * @return
     */
    @Override
    public Optional<HrRecruitmentDraftsDTO> updateHrRecruitmentDrafts(HrRecruitmentDraftsDTO hrRecruitmentDraftsDTO) {
        return Optional.ofNullable(this.hrRecruitmentDraftsRepository.selectById(hrRecruitmentDraftsDTO.getId()))
            .map(roleTemp -> {
                HrRecruitmentDrafts hrRecruitmentDrafts = this.hrRecruitmentDraftsMapper.toEntity(hrRecruitmentDraftsDTO);
                this.hrRecruitmentDraftsRepository.updateById(hrRecruitmentDrafts);
                log.info("Update HrRecruitmentDrafts:{}", hrRecruitmentDraftsDTO);
                return hrRecruitmentDraftsDTO;
            });
    }

    /**
     * 查询招聘草稿箱详情
     *
     * @param id
     * @return
     */
    @Override
    public HrRecruitmentDraftsDTO getHrRecruitmentDrafts(String id) {
        log.info("Get HrRecruitmentDrafts :{}", id);

        HrRecruitmentDrafts hrRecruitmentDrafts = this.hrRecruitmentDraftsRepository.selectById(id);
        return this.hrRecruitmentDraftsMapper.toDto(hrRecruitmentDrafts);
    }

    /**
     * 添加草稿箱
     * @param draftType 草稿类型 1考试须知 2笔试公告 3面试公告 4笔试成绩公告 5面试成绩公告 6 最终成绩公告 7考察结果公告 8体检结果公告
     * @param draftContent 草稿箱内容
     */
    @Override
    public HrRecruitmentDraftsDTO generateHrRecruitmentDrafts(Integer draftType, String draftContent) {
        QueryWrapper<HrRecruitmentDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("draft_type", draftType);
        HrRecruitmentDrafts recruitmentDrafts = hrRecruitmentDraftsRepository.selectOne(queryWrapper);
        if (recruitmentDrafts == null){
            recruitmentDrafts = new HrRecruitmentDrafts();
            recruitmentDrafts.setDraftType(draftType);
            recruitmentDrafts.setDraftContent(draftContent);
            hrRecruitmentDraftsRepository.insert(recruitmentDrafts);
        }else {
            recruitmentDrafts.setDraftContent(draftContent);
            hrRecruitmentDraftsRepository.updateById(recruitmentDrafts);
        }
        return this.hrRecruitmentDraftsMapper.toDto(recruitmentDrafts);
    }

    /**
     * 查看最新草稿箱
     * @param draftType 草稿类型
     * @return 草稿箱
     */
    @Override
    public HrRecruitmentDraftsDTO latestContentHrRecruitmentDrafts(Integer draftType) {
        QueryWrapper<HrRecruitmentDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("draft_type", draftType);
        HrRecruitmentDrafts recruitmentDrafts = hrRecruitmentDraftsRepository.selectOne(queryWrapper);
        HrRecruitmentDraftsDTO hrRecruitmentDraftsDTO = this.hrRecruitmentDraftsMapper.toDto(recruitmentDrafts);
        if (hrRecruitmentDraftsDTO != null){
            hrRecruitmentDraftsDTO.setDraftTypeLabel(RecruitmentBrochure.DraftType.getValueByKey(hrRecruitmentDraftsDTO.getDraftType()));
        }
        return hrRecruitmentDraftsDTO;
    }

}
