package cn.casair.service.impl;

import cn.casair.common.enums.StaffEnum;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.domain.HrStaffEmolument;
import cn.casair.domain.HrStaffWorkExperience;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.HrAccumulationFundDTO;
import cn.casair.dto.HrEmployeeWelfareDTO;
import cn.casair.dto.HrSocialSecurityDTO;
import cn.casair.dto.HrStaffEmolumentDTO;
import cn.casair.mapper.HrStaffEmolumentMapper;
import cn.casair.repository.HrStaffEmolumentRepository;
import cn.casair.repository.HrStaffWorkExperienceRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrStaffEmolumentService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 薪酬参数服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-04
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrStaffEmolumentServiceImpl extends ServiceImpl<HrStaffEmolumentRepository, HrStaffEmolument> implements HrStaffEmolumentService {

    private static final Logger log = LoggerFactory.getLogger(HrStaffEmolumentServiceImpl.class);

    private final HrStaffEmolumentRepository hrStaffEmolumentRepository;

    private final HrStaffEmolumentMapper hrStaffEmolumentMapper;

    private final CodeTableService codeTableService;

    private final HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;

    private final HrTalentStaffRepository hrTalentStaffRepository;

    public HrStaffEmolumentServiceImpl(HrStaffEmolumentRepository hrStaffEmolumentRepository, HrStaffEmolumentMapper hrStaffEmolumentMapper, CodeTableService codeTableService,
                                       HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository, HrTalentStaffRepository hrTalentStaffRepository) {
        this.hrStaffEmolumentRepository = hrStaffEmolumentRepository;
        this.hrStaffEmolumentMapper = hrStaffEmolumentMapper;
        this.codeTableService = codeTableService;
        this.hrStaffWorkExperienceRepository = hrStaffWorkExperienceRepository;
        this.hrTalentStaffRepository = hrTalentStaffRepository;
    }

    @Override
    public HrStaffEmolumentDTO selectStaffSalaryBankInfo(String staffId) {
        HrStaffEmolumentDTO hrStaffEmolumentDTO = this.hrStaffEmolumentMapper.toDto(this.hrStaffEmolumentRepository.getByStaffId(staffId));
        if (StringUtils.isNotBlank(hrStaffEmolumentDTO.getOwnedBank())) {
            Map<Integer, String> ownedBank = codeTableService.findCodeTableByInnerName("ownedBank");
            hrStaffEmolumentDTO.setOwnedBankLabel(ownedBank.get(Integer.parseInt(hrStaffEmolumentDTO.getOwnedBank())));
        }
        return hrStaffEmolumentDTO;
    }

    @Override
    public int updateStaffEmolumentByStaffId(HrStaffEmolument hrStaffEmolument) {
        return this.hrStaffEmolumentRepository.updateStaffEmolumentByStaffId(hrStaffEmolument);
    }

    @Override
    public List<HrStaffEmolument> getHrStaffEmolumentListByIds(List<String> staffIds) {
        return this.hrStaffEmolumentRepository.getHrStaffEmolumentListByIds(staffIds);
    }

    @Override
    public HrStaffEmolument getHrStaffEmolumentByStaffId(String staffId) {
        return this.hrStaffEmolumentRepository.getHrStaffEmolumentByStaffId(staffId);
    }

    @Override
    public int updateByStaffId(HrEmployeeWelfareDTO hrEmployeeWelfareDTO) {
        return this.hrStaffEmolumentRepository.updateByStaffId(hrEmployeeWelfareDTO);
    }

    /**
     * 创建薪酬参数
     *
     * @param hrStaffEmolumentDTO
     * @return
     */
    @Override
    public HrStaffEmolumentDTO createHrStaffEmolument(HrStaffEmolumentDTO hrStaffEmolumentDTO) {
        log.info("Create new HrStaffEmolument:{}", hrStaffEmolumentDTO);

        HrStaffEmolument hrStaffEmolument = this.hrStaffEmolumentMapper.toEntity(hrStaffEmolumentDTO);
        this.hrStaffEmolumentRepository.insert(hrStaffEmolument);
        return this.hrStaffEmolumentMapper.toDto(hrStaffEmolument);
    }

    /**
     * 计算工龄工资
     * @param params 入职时间、工资工龄基数
     * @return 工龄工资
     */
    @Override
    public Map<String, Object> calculateSenioritySalary(Map<String, Object> params) {
        Object boardDate = params.get("boardDate");
        Object seniorityWageBase = params.get("seniorityWageBase");
        JSONObject jsonObject = new JSONObject();
        if (boardDate != null && seniorityWageBase != null && seniorityWageBase != ""){
            String board = String.valueOf(boardDate);//入职时间
            BigDecimal decimal = new BigDecimal(String.valueOf(seniorityWageBase));//工资工龄基数
            BigDecimal seniorityPay = CalculateUtils.workAge(LocalDate.parse(board, DateTimeFormatter.ofPattern("yyyy-MM-dd")), decimal);
            jsonObject.put("seniorityPay",seniorityPay);
        }
        return jsonObject;
    }

    /**
     * 修改薪酬参数
     *
     * @param hrStaffEmolumentDTO
     * @return
     */
    @Override
    public Optional<HrStaffEmolumentDTO> updateHrStaffEmolument(HrStaffEmolumentDTO hrStaffEmolumentDTO) {
        return Optional.ofNullable(this.hrStaffEmolumentRepository.selectById(hrStaffEmolumentDTO.getId()))
            .map(roleTemp -> {
                if (hrStaffEmolumentDTO.getIzInsured()){// true 参保
                    //修改员工状态
                    HrTalentStaff staff = hrTalentStaffRepository.selectById(roleTemp.getStaffId());
                    staff.setIzInsured(StaffEnum.InsuredStatusEnum.INSURED.getKey());
                    hrTalentStaffRepository.updateById(staff);

                    HrStaffWorkExperience workExperience = hrStaffWorkExperienceRepository.selectStaffNewstWorkExperience(staff.getId(), staff.getClientId());
                    if (workExperience != null){
                        workExperience.setIzInsured(StaffEnum.InsuredStatusEnum.INSURED.getKey());
                        hrStaffWorkExperienceRepository.updateById(workExperience);
                    }
                }
                HrStaffEmolument hrStaffEmolument = this.hrStaffEmolumentMapper.toEntity(hrStaffEmolumentDTO);
                this.hrStaffEmolumentRepository.updateById(hrStaffEmolument);
                log.info("Update HrStaffEmolument:{}", hrStaffEmolumentDTO);
                return hrStaffEmolumentDTO;
            });
    }

    /**
     * 查询薪酬参数详情
     *
     * @param staffId 员工ID
     * @return
     */
    @Override
    public HrStaffEmolumentDTO getHrStaffEmolument(String staffId) {
        log.info("Get HrStaffEmolument :{}", staffId);

        HrStaffEmolumentDTO hrStaffEmolumentDTO = this.hrStaffEmolumentMapper.toDto(
            this.hrStaffEmolumentRepository.selectOne(new QueryWrapper<HrStaffEmolument>().eq("staff_id",staffId).eq("is_delete",0)));
        if (hrStaffEmolumentDTO == null){
            hrStaffEmolumentDTO = new HrStaffEmolumentDTO();
        }
        this.setDict(hrStaffEmolumentDTO);
        HrStaffWorkExperience workExperience = hrStaffWorkExperienceRepository.selectOne(new QueryWrapper<HrStaffWorkExperience>().eq("staff_id", staffId).eq("iz_default", 1).last("LIMIT 1"));
        if(workExperience != null){
            hrStaffEmolumentDTO.setBoardDate(workExperience.getBoardDate());
        }
        //查询对应的社保类型
        List<HrSocialSecurityDTO> socialSecurityDTOList = this.hrStaffEmolumentRepository.findSocialSecurity(staffId);
        if (CollectionUtils.isNotEmpty(socialSecurityDTOList)){
            hrStaffEmolumentDTO.setSocialSecurityDTOList(socialSecurityDTOList);
        }
        //查询对应的公积金类型
        List<HrAccumulationFundDTO> accumulationFundDTOList = this.hrStaffEmolumentRepository.findAccumulationFund(staffId);
        if (CollectionUtils.isNotEmpty(accumulationFundDTOList)){
            hrStaffEmolumentDTO.setAccumulationFundDTOList(accumulationFundDTOList);
        }
        return hrStaffEmolumentDTO;
    }

    /**
     * 赋值字典值
     * @param hrStaffEmolumentDTO
     */
    private void setDict(HrStaffEmolumentDTO hrStaffEmolumentDTO) {
        if (hrStaffEmolumentDTO.getOwnedBank()!=null){
            Map<Integer, String> ownedBank = codeTableService.findCodeTableByInnerName("ownedBank");//所属银行
            hrStaffEmolumentDTO.setOwnedBankLabel(ownedBank.get(Integer.parseInt(hrStaffEmolumentDTO.getOwnedBank())));
        }
    }

    /**
     * 删除薪酬参数
     *
     * @param id
     */
    @Override
    public void deleteHrStaffEmolument(String id) {
        Optional.ofNullable(this.hrStaffEmolumentRepository.selectById(id))
            .ifPresent(hrStaffEmolument -> {
                this.hrStaffEmolumentRepository.deleteById(id);
                log.info("Delete HrStaffEmolument:{}", hrStaffEmolument);
            });
    }

    /**
     * 批量删除薪酬参数
     *
     * @param ids
     */
    @Override
    public void deleteHrStaffEmolument(List<String> ids) {
        log.info("Delete HrStaffEmoluments:{}", ids);
        this.hrStaffEmolumentRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询薪酬参数
     *
     * @param hrStaffEmolumentDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrStaffEmolumentDTO hrStaffEmolumentDTO, Long pageNumber, Long pageSize) {
        Page<HrStaffEmolument> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrStaffEmolument> qw = new QueryWrapper<>(this.hrStaffEmolumentMapper.toEntity(hrStaffEmolumentDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrStaffEmolumentRepository.selectPage(page, qw);
        iPage.setRecords(this.hrStaffEmolumentMapper.toDto(iPage.getRecords()));
        return iPage;
    }

}
