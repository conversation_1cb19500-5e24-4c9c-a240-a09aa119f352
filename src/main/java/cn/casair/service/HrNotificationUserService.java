package cn.casair.service;

import cn.casair.domain.HrNotificationUser;
import cn.casair.dto.HrNotificationUserDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-09-30
 */
public interface HrNotificationUserService extends IService<HrNotificationUser> {


    /**
     * 创建
     * @param hrNotificationUserDTO
     * @return
     */
    HrNotificationUserDTO createHrNotificationUser(HrNotificationUserDTO hrNotificationUserDTO);

    /**
     * 修改
     * @param hrNotificationUserDTO
     * @return
     */
    Optional<HrNotificationUserDTO> updateHrNotificationUser(HrNotificationUserDTO hrNotificationUserDTO);

    /**
     * 查询详情
     * @param id
     * @return
     */
    HrNotificationUserDTO getHrNotificationUser(String id);

    /**
     * 删除
     * @param id
     */
    void deleteHrNotificationUser(String id);

    /**
     * 批量删除
     * @param ids
     */
    void deleteHrNotificationUser(List<String> ids);

    /**
     * 分页查询
     * @param hrNotificationUserDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrNotificationUserDTO hrNotificationUserDTO,Long pageNumber,Long pageSize);

    /**
     * 保存提醒内容信息
     * @param clientId 申请员工的所属客户
     * @param applyType 申请类型
     * @param launchType 提醒类型
     * @param content 提醒内容
     * @param loginId 创建人
     */
    void saveRemindContent(String clientId, Integer applyType, Integer launchType,String content, String loginId);

}
