package cn.casair.repository;

import cn.casair.domain.*;
import cn.casair.dto.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;

/**
 * 常用功能数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Repository
public interface HrCommonFunctionsRepository extends BaseMapper<HrCommonFunctions> {


    void deleteUserId(String id);


    List<HrTalentStaff> selectHrRetire(@Param("clientIdList") List<String> clientIdList, @Param("statusList") List<String> statusList);

    List<HrWorkInjury> selectHrWorkInjury(@Param("clientIdList") List<String> clientIdList, @Param("statusList") List<String> statusList);

    List<HrArchivesBring> selectHrArchivesBring(@Param("clientIdList") List<String> clientIdList, @Param("statusList") List<String> statusList);

    List<HrFertility> selectHrFertilityList(@Param("clientIdList") List<String> clientIdList);

    List<HrRemindConfDTO> selectRemindKey(Integer currentRoleId);

    List<String> selectUserID(@Param("clientIdList") List<String> clientIdList);

    List<HrMessageListDTO> selectHrMessageList(@Param("userId")  String userId);

    void getHrRemindMessageUpdate(@Param("id") String id, @Param("userId") String userId);

    LinkedList<LocalDateTime> selectInjuryDate(@Param("staffIdList") List<String> staffIdList);

    List<String> selectClient(@Param("clientIdList") List<String> clientIdList);

    List<HrOnboardingDTO> selectOnboarding(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);


    List<HrResignDTO> selectHrResign(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    List<HrServiceDTO> selectHrServiceDTO(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    List<HrServiceDTO> selecthrServiceDTOResign(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    List<HrServiceDTO> selecthrServiceDTOPositive(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    List<HrServiceDTO> selecthrServiceDTORetire(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    List<String> selectStaffs(@Param("staffId") List<String> staffId);

    List<HrServiceDTO> selectListtime(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);


    List<HrServiceDTO> selectListgetId(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    List<HrServiceDTO> hrServiceDTOWork(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    List<HrServiceDTO> hrServiceDTOProve(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    List<HrSalaryDTO> selectHrSalaryDTO(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    List<HrWelfareDTO> selectHrWelfareDTO(@Param("clientIdList") List<String> clientIdList, @Param("lastDate") LocalDate lastDate, @Param("thisDate") LocalDate thisDate);

    void getHrRemindMessageUpdateReminder(@Param("id")String id );

    List<HrNotificationUserContent> selectLists(@Param("id")String id,@Param("userName")String userName);
}
