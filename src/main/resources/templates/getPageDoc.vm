**简要描述：**

- $!{table.comment} 接口文档

**请求URL：**
- ` ${table.entityPath}/findPage/page?pageNumber=1&pageSize=10 `

**请求方式：**
- POST

**请求示例**

```
{
#foreach($field in ${table.fields})
    "${field.propertyName}":"" //${field.comment}
#end
}
```


**参数：**

|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
#foreach($field in ${table.fields})
|${field.propertyName} |是  |${field.propertyType} |${field.comment}   |
#end

**返回示例**

```
{
    "status": 200,
    "msg": "操作成功",
    "data": {
        "records": [
        {
#foreach($field in ${table.fields})
            "${field.propertyName}":"" //${field.comment}
#end
        }
    ],
    "total": 0,
    "size": 10,
    "current": 1,
    "searchCount": true,
    "pages": 0
    }
}

{
"data": {
"records": [
{
#foreach($field in ${table.fields})
"${field.propertyName}":"" //${field.comment}
#end
},
],
"total": 4,
"size": 2,
"current": 1,
"searchCount": true,
"pages": 2
},
"errorCode": 0,
"errorMessage": "SUCCESS"
}
```

**返回参数说明**

|参数名|类型|说明|
|:-----  |:-----|-----                           |


**备注**

- 更多返回错误代码请看首页的错误代码描述

