<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillInvoiceRecordRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, invoice_id, title, content, total_amount, tax_rate, tax_amount, no_tax_amount,state,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillInvoiceRecord">
                    <id column="id" property="id"/>
                    <result column="invoice_id" property="invoiceId"/>
                    <result column="title" property="title"/>
                    <result column="content" property="content"/>
                    <result column="total_amount" property="totalAmount"/>
                    <result column="tax_rate" property="taxRate"/>
                    <result column="tax_amount" property="taxAmount"/>
                    <result column="no_tax_amount" property="noTaxAmount"/>
                    <result column="state" property="state"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

    <select id="getByInvoiceId" resultType="cn.casair.dto.HrBillInvoiceRecordDTO">
        SELECT
            hbir.*,
            hbi.invoice_lock_state
        FROM
            hr_bill_invoice_record hbir
        LEFT JOIN hr_bill_invoice hbi ON hbir.invoice_id = hbi.id
        WHERE hbir.is_delete = 0 AND hbir.invoice_id = #{invoiceId}
    </select>

    <select id="getByIdBatch" resultType="cn.casair.dto.HrBillInvoiceRecordDTO">
        SELECT
            hbir.*,
            hbi.invoice_lock_state,
            hbi.approve_status
        FROM
            hr_bill_invoice_record hbir
        LEFT JOIN hr_bill_invoice hbi ON hbir.invoice_id = hbi.id
        WHERE hbir.is_delete = 0 AND hbir.id IN
        <foreach collection="invoiceRecordIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <update id="updateState">
        UPDATE hr_bill_invoice_record SET state = #{state} WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateStateByInvoiceId">
        UPDATE hr_bill_invoice_record SET state = #{state} WHERE is_delete = 0
        AND invoice_id IN
        <foreach collection="invoiceIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        <if test="invoiceTypes != null and invoiceTypes.size() > 0">
        AND
            <foreach collection="invoiceTypes" open="(" separator=" or  " close=")" item="billInvoiceRecord">
                 ( content = #{billInvoiceRecord.content} and pay_year = #{billInvoiceRecord.payYear} and pay_monthly = #{billInvoiceRecord.payMonthly})
            </foreach>
        </if>
    </update>
</mapper>
