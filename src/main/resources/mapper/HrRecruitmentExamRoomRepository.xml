<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRecruitmentExamRoomRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, recruitment_station_id, exam_room_name, contain_num, exam_room_place,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrRecruitmentExamRoom">
        <id column="id" property="id"/>
        <result column="recruitment_station_id" property="recruitmentStationId"/>
        <result column="exam_room_name" property="examRoomName"/>
        <result column="contain_num" property="containNum"/>
        <result column="exam_room_place" property="examRoomPlace"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

</mapper>
