<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.WxpayNotifyRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            wxpay_notify_id, return_code, return_msg, transaction_id, out_trade_no, appid, mch_id, device_info, nonce_str, sign, result_code, err_code, err_code_des, openid, is_subscribe, trade_type, bank_type, total_fee, fee_type, cash_fee, cash_fee_type, coupon_fee, coupon_count, attach, time_end, create_time

        </sql>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.WxpayNotify">
                    <id column="wxpay_notify_id" property="wxpayNotifyId"/>
                    <result column="return_code" property="returnCode"/>
                    <result column="return_msg" property="returnMsg"/>
                    <result column="transaction_id" property="transactionId"/>
                    <result column="out_trade_no" property="outTradeNo"/>
                    <result column="appid" property="appid"/>
                    <result column="mch_id" property="mchId"/>
                    <result column="device_info" property="deviceInfo"/>
                    <result column="nonce_str" property="nonceStr"/>
                    <result column="sign" property="sign"/>
                    <result column="result_code" property="resultCode"/>
                    <result column="err_code" property="errCode"/>
                    <result column="err_code_des" property="errCodeDes"/>
                    <result column="openid" property="openid"/>
                    <result column="is_subscribe" property="isSubscribe"/>
                    <result column="trade_type" property="tradeType"/>
                    <result column="bank_type" property="bankType"/>
                    <result column="total_fee" property="totalFee"/>
                    <result column="fee_type" property="feeType"/>
                    <result column="cash_fee" property="cashFee"/>
                    <result column="cash_fee_type" property="cashFeeType"/>
                    <result column="coupon_fee" property="couponFee"/>
                    <result column="coupon_count" property="couponCount"/>
                    <result column="attach" property="attach"/>
                    <result column="time_end" property="timeEnd"/>
                    <result column="create_time" property="createTime"/>
        </resultMap>

</mapper>
