package cn.casair.service.component.asynchronous;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.DynamicFeeTypesEnum;
import cn.casair.common.enums.StaffApplyStatusEnum;
import cn.casair.common.enums.StaffEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.BigDecimalCompare;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.common.utils.StringUtils;
import cn.casair.common.utils.ValidateUtil;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.common.utils.excel.ImportResultDTO;
import cn.casair.common.utils.excel.ImportResultUtils;
import cn.casair.domain.HrStaffWorkExperience;
import cn.casair.domain.HrStation;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.ApplyStaffImportResultDTO;
import cn.casair.dto.HrRealNameAuthDTO;
import cn.casair.dto.HrSocialSecurityDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.excel.HrApplyDepartureStaffImport;
import cn.casair.dto.excel.HrApplyEntryStaffImport;
import cn.casair.repository.HrStaffWorkExperienceRepository;
import cn.casair.repository.HrStationRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrRealNameAuthService;
import cn.casair.service.HrSocialSecurityService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 入离职服务导入
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EntryDepartureComponent {

    @Value("${file.temp-path}")
    private String tempPath;

    private final HrRealNameAuthService hrRealNameAuthService;
    private final HrStationRepository hrStationRepository;
    private final CodeTableService codeTableService;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final RedisCache redisCache;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;
    private final HrSocialSecurityService hrSocialSecurityService;
    private final CommonComponent commonComponent;

    /**
     * 待入职员工导入
     *
     * @param clientId
     * @param inputStream Excel文件
     * @param redisKey    redisKey
     * @param jwtUserDTO  当前用户登录信息
     */
    @Async
    public void dealHiredStaffImport(String clientId, InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO) {
        try {
            ExcelImportResult<HrApplyEntryStaffImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrApplyEntryStaffImport.class);
            ImportResultDTO importResultDTO;
            ApplyStaffImportResultDTO resultDTO = new ApplyStaffImportResultDTO();

            int scale = 0;
            int listSize = result.getList().size();
            List<HrApplyEntryStaffImport> hrApplyEntryStaffDTOS = new ArrayList<>();
            List<HrStation> hrStationList = this.hrStationRepository.selectList(new QueryWrapper<HrStation>().eq("is_delete", 0));
            Map<String, String> stationMap = hrStationList.stream().collect(Collectors.toMap(HrStation::getProfessionName, HrStation::getId, (key1, key2) -> key2));
            Map<String, Integer> sexType = codeTableService.findCodeTableByInnerValue("sexType");//性别
            Map<String, Integer> residenceType = codeTableService.findCodeTableByInnerValue("residenceType");//户口性质
            Map<String, Integer> staffType = codeTableService.findCodeTableByInnerValue("staffType");
            Map<String, List<HrApplyEntryStaffImport>> map = result.getList().stream().collect(Collectors.groupingBy(HrApplyEntryStaffImport::getCertificateNum));
            List<HrSocialSecurityDTO> clientSocialSecurity = hrSocialSecurityService.getClientSocialSecurity(Collections.singletonList(clientId));
            HrSocialSecurityDTO hrSocialSecurityDTO = clientSocialSecurity == null || clientSocialSecurity.isEmpty() ? null : clientSocialSecurity.get(0);
            //导入人才信息以及校验
            for (HrApplyEntryStaffImport hrApplyEntryStaffDTO : result.getList()) {
                StringBuilder msgError = new StringBuilder();
                try {
                    //校验格式
                    boolean idNumber = ValidateUtil.isIdNumber(hrApplyEntryStaffDTO.getCertificateNum());
                    if (!idNumber) {
                        msgError.append("身份证号格式不正确！");
                    }
                    if (hrApplyEntryStaffDTO.getSexLabel() == null || StringUtils.isBlank(hrApplyEntryStaffDTO.getSexLabel())) {
                        Integer sex = ValidateUtil.splitSex(hrApplyEntryStaffDTO.getCertificateNum());
                        hrApplyEntryStaffDTO.setSex(sex).setSexLabel(sex == 1 ? "男" : "女");
                    }
                    if (hrApplyEntryStaffDTO.getSexLabel() != null && !hrApplyEntryStaffDTO.getSexLabel().equals("女") && !hrApplyEntryStaffDTO.getSexLabel().equals("男")) {
                        Integer sex = ValidateUtil.splitSex(hrApplyEntryStaffDTO.getCertificateNum());
                        hrApplyEntryStaffDTO.setSex(sex).setSexLabel(sex == 1 ? "男" : "女");
                    }
                    //判断excel中是否存在重复数据
                    String certificateNum = hrApplyEntryStaffDTO.getCertificateNum();
                    List<HrApplyEntryStaffImport> hrApplyEntryStaffImports = map.get(certificateNum);
                    if (CollectionUtils.isNotEmpty(hrApplyEntryStaffImports) && hrApplyEntryStaffImports.size() > 1) {
                        throw new CommonException("导入Excel中该证件号码存在多条数据!");
                    }
                    //验证手机号码有效性
                    if (!ValidateUtil.isCellPhoneNo(hrApplyEntryStaffDTO.getPhone())) {
                        msgError.append("手机号码格式填写不正确！");
                    }
                    if (hrApplyEntryStaffDTO.getProfessionName() != null) {
                        if (stationMap.containsKey(hrApplyEntryStaffDTO.getProfessionName())) {
                            hrApplyEntryStaffDTO.setStationId(stationMap.get(hrApplyEntryStaffDTO.getProfessionName()));
                            hrApplyEntryStaffDTO.setStationIdLabel(hrApplyEntryStaffDTO.getProfessionName());
                        } else {
                            msgError.append("输入的岗位在系统中不存在!");
                        }
                    }
                    //新入职员工合同结束日期不能早于当前日期
                    LocalDate contractStartDate = hrApplyEntryStaffDTO.getContractStartDate();
                    LocalDate contractEndDate = hrApplyEntryStaffDTO.getContractEndDate();
                    LocalDate now = LocalDate.now();
//                    if (contractStartDate.isAfter(now)){
//                        msgError.append("入职合同不能晚于当前日期";
//                    }
                    if (contractEndDate.isBefore(now)) {
                        msgError.append("新入职合同结束日期不能早于当前日期！");
                    }
                    if (contractEndDate.isBefore(contractStartDate)) {
                        msgError.append("合同结束日期填写不合理!合同结束日期不能早于合同开始日期！");
                    }
                    if (hrApplyEntryStaffDTO.getPersonnelTypeLabel() != null) {
                        Integer integer = staffType.get(hrApplyEntryStaffDTO.getPersonnelTypeLabel());
                        if (integer == null) {
                            msgError.append("输入的人员类型在系统中不存在，请按照下拉框数据选择！");
                        } else {
                            hrApplyEntryStaffDTO.setPersonnelType(integer);
                        }
                    }
                    if (hrApplyEntryStaffDTO.getSexLabel() != null) {
                        hrApplyEntryStaffDTO.setSex(sexType.get(hrApplyEntryStaffDTO.getSexLabel()));
                    }
                    if (hrApplyEntryStaffDTO.getHouseholdRegistrationLabel() != null) {
                        hrApplyEntryStaffDTO.setHouseholdRegistration(residenceType.get(hrApplyEntryStaffDTO.getHouseholdRegistrationLabel()));
                    }
                    if (hrApplyEntryStaffDTO.getStaffTypeLabel() != null) {//员工类型
                        Integer integer = StaffApplyStatusEnum.StaffTypeEnum.getKeyByValue(hrApplyEntryStaffDTO.getStaffTypeLabel());
                        if (integer == null) {
                            msgError.append("输入的员工类型在系统中不存在，请按照下拉框数据选择！");
                        } else {
                            hrApplyEntryStaffDTO.setStaffType(integer);
                            if (integer.equals(StaffApplyStatusEnum.StaffTypeEnum.PROBATIONER.getKey())
                                || integer.equals(StaffApplyStatusEnum.StaffTypeEnum.PROBATION_WORKER.getKey())) {
                                if (hrApplyEntryStaffDTO.getInternshipDuration() == null) {
                                    msgError.append("员工类型为实习工或者试用工，待转时长必填！");
                                }
                            }
                        }
                    }
                    String monthRegex = "^([1-9]\\d{3}-)(0[1-9]|10|11|12)";
                    if (!Pattern.matches(monthRegex, hrApplyEntryStaffDTO.getPaymentDate())) {
                        msgError.append("缴费年月格式不正确。例2021-01;");
                    }
                    //校验客户绑定的社保类型中合并基数是否一致
                    if (hrSocialSecurityDTO != null) {
                        Map<String, Object> staffParams = JSONObject.parseObject(JSONObject.toJSONString(hrApplyEntryStaffDTO), Map.class);
                        commonComponent.handleCardinal(hrSocialSecurityDTO, staffParams, msgError);
                    }
                    if (StringUtils.isNotEmpty(msgError)) {
                        throw new CommonException(msgError.toString());
                    }
                    // 实名认证
                    this.hrRealNameAuthService.authNameCertificateNum(new HrRealNameAuthDTO().setName(hrApplyEntryStaffDTO.getName()).setCertificateNum(hrApplyEntryStaffDTO.getCertificateNum()));

                    hrApplyEntryStaffDTOS.add(hrApplyEntryStaffDTO);
                } catch (Exception e) {
                    log.error("导入待入职员工异常:{}", e.getMessage());
                    hrApplyEntryStaffDTO.setErrorMsg(e.getMessage());
                } finally {
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 30, TimeUnit.MINUTES);
                }
            }
            if (CollectionUtils.isNotEmpty(hrApplyEntryStaffDTOS)) {
                resultDTO.setSuccessList(hrApplyEntryStaffDTOS);
            }
            // 将返回数据返回前端
            importResultDTO = ImportResultUtils.writeErrorFile("待入职批量导入", HrApplyEntryStaffImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResultDTO);
            BeanUtils.copyProperties(importResultDTO, resultDTO);
            redisCache.setCacheObject(redisKey, JSON.toJSONString(resultDTO), 30, TimeUnit.MINUTES);
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.CREATE_APPLICATION.getValue(), BusinessTypeEnum.IMPORT.getKey(), resultDTO.getFailureFileUrl(), JSON.toJSONString(resultDTO), ApplyStaffImportResultDTO.class, jwtUserDTO);
        } catch (Exception e) {
            log.error("待入职员工异步导入处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "待入职员工导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }
    }

    /**
     * 待离职员工导入
     *
     * @param inputStream Excel文件
     * @param redisKey    redisKey
     * @param jwtUserDTO  当前用户登录信息
     * @param clientId
     */
    @Async
    public void dealDepartureStaffImport(InputStream inputStream, String redisKey, JWTUserDTO jwtUserDTO, String clientId) {
        try {
            ExcelImportResult<HrApplyDepartureStaffImport> result = ExcelUtils.importExcelWithInputStream(inputStream, 0, 1, true, HrApplyDepartureStaffImport.class);
            ApplyStaffImportResultDTO resultDTO = new ApplyStaffImportResultDTO();
            ImportResultDTO importResultDTO;

            int scale = 0;
            int listSize = result.getList().size();
            List<HrApplyDepartureStaffImport> hrApplyDepartureStaffImports = new ArrayList<>();
            List<HrTalentStaff> hrTalentStaffList = hrTalentStaffRepository.selectList(new QueryWrapper<HrTalentStaff>().eq("client_id", clientId).eq("iz_default", 0));
            List<HrStaffWorkExperience> hrStaffWorkExperienceList = hrStaffWorkExperienceRepository.selectList(new QueryWrapper<HrStaffWorkExperience>().eq("client_id", clientId).eq("iz_default", 1));
            Map<String, List<HrApplyDepartureStaffImport>> map = result.getList().stream().collect(Collectors.groupingBy(HrApplyDepartureStaffImport::getCertificateNum));

            for (HrApplyDepartureStaffImport hrApplyDepartureStaffImport : result.getList()) {
                try {
                    //判断excel中是否存在重复数据
                    String certificateNum = hrApplyDepartureStaffImport.getCertificateNum();
                    List<HrApplyDepartureStaffImport> departureStaffImportList = map.get(certificateNum);
                    if (CollectionUtils.isNotEmpty(departureStaffImportList) && departureStaffImportList.size() > 1) {
                        throw new CommonException("导入Excel中该数据重复!");
                    }
                    //校验格式
                    boolean idNumber = ValidateUtil.isIdNumber(certificateNum);
                    if (!idNumber) {
                        throw new CommonException("身份证号格式不正确！");
                    }
                    //判断员工是否存在
                    List<HrTalentStaff> staffList = hrTalentStaffList.stream().filter(ls -> ls.getCertificateNum().toUpperCase().equals(certificateNum.toUpperCase())).distinct().collect(Collectors.toList());
                    HrTalentStaff talentStaff = null;
                    if (CollectionUtils.isNotEmpty(staffList)) {
                        talentStaff = staffList.get(0);
                        hrApplyDepartureStaffImport.setSex(talentStaff.getSex());
                        hrApplyDepartureStaffImport.setPhone(talentStaff.getPhone());
                        hrApplyDepartureStaffImport.setPersonnelType(talentStaff.getPersonnelType());
                    }
                    if (talentStaff == null) {
                        throw new CommonException("选择的所属客户中该员工信息不存在！");
                    }
                    //获取员工的入职时间
                    HrTalentStaff finalTalentStaff = talentStaff;
                    List<HrStaffWorkExperience> hrStaffWorkExperiences = hrStaffWorkExperienceList.stream().filter(ls -> ls.getStaffId().toUpperCase().equals(finalTalentStaff.getId().toUpperCase())).distinct().collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(hrStaffWorkExperiences)) {
                        HrStaffWorkExperience hrStaffWorkExperience = hrStaffWorkExperiences.get(0);
                        //判断实际工作期限--开始时间是否早于入职日期
                        if (hrApplyDepartureStaffImport.getWorkDurationStartDate() != null) {
                            if (hrApplyDepartureStaffImport.getWorkDurationStartDate().isBefore(hrStaffWorkExperience.getBoardDate())) {
                                throw new CommonException("实际工作期限--开始时间不可早于入职日期！入职日期-" + hrStaffWorkExperience.getBoardDate() + "。");
                            }
                        }
                    }
                    String monthRegex = "^([1-9]\\d{3}-)(0[1-9]|10|11|12)";
                    if (!Pattern.matches(monthRegex, hrApplyDepartureStaffImport.getStopPaymentDate())) {
                        throw new CommonException("停止缴费年月格式不正确。例2021-01");
                    }
                    hrApplyDepartureStaffImport.setSexLabel(talentStaff.getSex() == 1 ? "男" : "女");
                    hrApplyDepartureStaffImport.setPersonnelTypeLabel(StaffEnum.PersonnelTypeEnum.getValueByKey(talentStaff.getPersonnelType()));
                    hrApplyDepartureStaffImport.setStaffId(talentStaff.getId());
                    hrApplyDepartureStaffImports.add(hrApplyDepartureStaffImport);
                } catch (CommonException e) {
                    log.error("导入待入职员工异常:{}", e.getMessage());
                    hrApplyDepartureStaffImport.setErrorMsg(e.getMessage());
                } finally {
                    scale++;
                    int i = CalculateUtils.calculationProgress(scale, listSize);
                    redisCache.setCacheObject(redisKey, i, 30, TimeUnit.MINUTES);
                }
            }
            resultDTO.setDepartureStaffSuccessList(hrApplyDepartureStaffImports);
            // 将返回数据返回前端
            importResultDTO = ImportResultUtils.writeErrorFile("待离职批量导入", HrApplyDepartureStaffImport.class, result, tempPath);
            // 判断是否需要上传错误文件
            this.hrAppendixService.uploadErrorImportFile(importResultDTO);
            BeanUtils.copyProperties(importResultDTO, resultDTO);
            redisCache.setCacheObject(redisKey, JSON.toJSONString(resultDTO), 30, TimeUnit.MINUTES);
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.RESIGNATION_APPLICATION.getValue(), BusinessTypeEnum.IMPORT.getKey(), resultDTO.getFailureFileUrl(), JSON.toJSONString(resultDTO), ApplyStaffImportResultDTO.class, jwtUserDTO);

        } catch (Exception e) {
            log.error("待离职员工异步导入处理发生异常：{}", e.getMessage());
            redisCache.setCacheObject(redisKey, "待离职员工导入发生异常：" + e.getMessage(), 30, TimeUnit.MINUTES);
        }

    }
}
