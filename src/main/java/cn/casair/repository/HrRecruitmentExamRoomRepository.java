package cn.casair.repository;

import cn.casair.domain.HrRecruitmentExamRoom;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 招聘岗位考场数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Repository
public interface HrRecruitmentExamRoomRepository extends BaseMapper<HrRecruitmentExamRoom> {

    /**
     * 删除考场信息
     * @param recruitmentStationId 招聘岗位Id
     */
    @Delete("DELETE FROM hr_recruitment_exam_room WHERE recruitment_station_id = #{recruitmentStationId}")
    void deleteRecruitmentExamRoom(@Param("recruitmentStationId") String recruitmentStationId);

}
