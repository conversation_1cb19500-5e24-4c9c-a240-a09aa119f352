package cn.casair.repository;

import cn.casair.domain.HrFeeReview;
import cn.casair.domain.HrFeeReviewConfig;
import cn.casair.dto.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 费用审核数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Repository
public interface HrFeeReviewRepository extends BaseMapper<HrFeeReview> {

    /**
     * 查询审核信息
     *
     * @param clientId
     * @param billId
     * @return int
     * <AUTHOR>
     * @date 2021/12/16
     **/
    int selectByClientIdAndBillId(@Param("clientId") String clientId, @Param("billId")String billId);

    /**
     * 查询客户最新的出账账单
     *
     * @param clientId
     * @return cn.casair.domain.HrFeeReview
     * <AUTHOR>
     * @date 2021/11/26
     **/
    HrFeeReview selectNewestRecordByClientId(String clientId);

    /**
     * 获取客户Id的下级客户
     * @param id
     * @return
     */
    List<String> selectChlientId(String id);


    HrFeeReviewDTO selectClientname(String id);


    List<HrBillTotalDTO> selectBillTotal(@Param("param1")HrFeeReviewDTO hrFeeReviewDTO);

    List<HrBillDTO> selectBill(@Param("param1")HrFeeReviewDTO hrFeeReviewDTO);


    HrFeeReviewDTO selectClient(@Param("id")String id);

    List<HrBillDTO> selectBillId(@Param("id")List<String> id);

    IPage<HrFeeReviewDTO> selectPages(Page<HrFeeReview> page, @Param("param1")HrFeeReviewDTO hrFeeReviewDTO);


    List<HrClientsDTO> selectChlient(@Param("clientIdList")List<String> clientIdList);


    List<HrBillTotalDTO> selectBillTotals(@Param("id")List<String> id);

    void updateStatus(@Param("id")List<String> id);

    List<HrBillDetailDTO> selectBillDet(@Param("id")List<String>  id);


    List<HrBillDetailItemsDTO> selectHrBillDetailItems(String id);

    void updateHrBill(@Param("id")String id, @Param("type")Integer type);

    String selecttotal(@Param("billId")List<String> billId);

    List<String> selectChlientIds(List<String> clientIdList);

    HrFeeReviewDTO selectTile(String id);


    List<HrBillTotalDTO> selectBillTotalas(@Param("param1")HrFeeReviewDTO hrFeeReviewDTO);

    List<HrBillDetailDTO> selectBillDetail(@Param("billId")List<String> billId, @Param("clientIdLista")List<String> clientIdLista);

     ArrayList<HrBillDetailDTO> selectBillDetails(@Param("billId")List<String> billId);

    /**
     * 查询结算单基本信息
     * @param id
     * @return
     */
    HrSettleAccountDTO findHrFeeReviewById(@Param("id") String id);

    List<HrBillDetailItemsDTO> getHrBillDetailItems(@Param("billIds") List<String> billIds);

    /**
     * 审核通过未创建报销记录的结算单
     * @return
     * @param paymentDate
     * @param ids
     */
    List<HrFeeReviewDTO> selectFeeReview(@Param("paymentDate") String paymentDate, @Param("ids") List<String> ids);

    /**
     * 查询客户所有审核通过的结算单信息
     * @param clientId
     * @return
     */
    List<HrFeeReviewDTO> selectFeeReviewByClientId(@Param("clientId") String clientId);

    /**
     * 根据开票ID查询结算单信息
     * @param invoiceId 开票ID
     * @return
     */
    List<HrFeeReview> findFeeReviewByInvoiceId(@Param("invoiceId") String invoiceId);

    /**
     * 查看客户配置的计算单表头
     * @param clientId
     * @return
     */
    HrFeeReviewConfig selectReviewConfigByClientId(@Param("clientId") String clientId, @Param("feeReviewId") String feeReviewId);

}
