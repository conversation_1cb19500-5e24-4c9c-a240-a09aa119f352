package cn.casair.service;

import cn.casair.common.enums.BillEnum;
import cn.casair.domain.HrBillCompareResultDetail;
import cn.casair.dto.DynamicHeadersDTO;
import cn.casair.dto.HrBillCompareHeaderDTO;
import cn.casair.dto.HrBillCompareResultDetailDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 导盘对账结果明细服务类
 *
 * <AUTHOR>
 * @since 2022-09-22
 */
public interface HrBillCompareResultDetailService extends IService<HrBillCompareResultDetail> {


    /**
     * 制作动态表头
     *
     * @param hrBillCompareResultDetailList
     * @param type
     * @return
     */
    List<DynamicHeadersDTO> makeDynamicHeaders(List<HrBillCompareResultDetailDTO> hrBillCompareResultDetailList, Integer type);

    /**
     * 根据对账结果id查询对账明细
     *
     * @param hrBillCompareResultId
     * @return
     */
    List<HrBillCompareResultDetailDTO> selectHrBillCompareResultDetailByBillCompareResultId(String hrBillCompareResultId);

    /**
     * 创建导盘对账结果明细
     *
     * @param hrBillCompareResultDetailDTO
     * @return
     */
    HrBillCompareResultDetailDTO createHrBillCompareResultDetail(HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO);

    /**
     * 修改导盘对账结果明细
     *
     * @param hrBillCompareResultDetailDTO
     * @return
     */
    Optional<HrBillCompareResultDetailDTO> updateHrBillCompareResultDetail(HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO);

    /**
     * 查询导盘对账结果明细详情
     *
     * @param id
     * @return
     */
    HrBillCompareResultDetailDTO getHrBillCompareResultDetail(String id);

    /**
     * 删除导盘对账结果明细
     *
     * @param id
     */
    void deleteHrBillCompareResultDetail(String id);

    /**
     * 批量删除导盘对账结果明细
     *
     * @param ids
     */
    void deleteHrBillCompareResultDetail(List<String> ids);

    /**
     * 分页查询导盘对账结果明细
     *
     * @param hrBillCompareResultDetailDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillCompareResultDetailDTO hrBillCompareResultDetailDTO, Long pageNumber, Long pageSize);

    /**
     * 动态项中动态项
     *
     * @param enumByKey 导盘枚举
     * @param list 动态表头
     * @param compareHeader 对账表头
     * @param topTitle 标题
     * @param topKey key
     * @param color 颜色
     */
    void dealComparePartDynamicHeader(BillEnum.GuidePlateType enumByKey, List<DynamicHeadersDTO> list, HrBillCompareHeaderDTO compareHeader, String topTitle, String topKey, String color);

}
