package cn.casair.service;
import cn.casair.domain.HrWorkInjury;
import cn.casair.dto.HrWorkInjuryDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;

/**
 * 工伤服务表服务类
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
public interface HrWorkInjuryService extends IService<HrWorkInjury> {


    /**
     * 创建工伤服务表
     * @param hrWorkInjuryDTO
     * @param requestFromWx 是否小程序请求
     * @return
     */
    HrWorkInjuryDTO createHrWorkInjury(HrWorkInjuryDTO hrWorkInjuryDTO, Boolean requestFromWx);

    /**
     * 修改工伤服务表
     * @param hrWorkInjuryDTO
     * @return
     */
    Optional<HrWorkInjuryDTO> updateHrWorkInjury(HrWorkInjuryDTO hrWorkInjuryDTO);

    /**
     * 查询工伤服务表详情
     * @param id
     * @return
     */
    HrWorkInjuryDTO getHrWorkInjury(String id);

    /**
     * 删除工伤服务表
     * @param id
     */
    void deleteHrWorkInjury(String id);

    /**
     * 批量删除工伤服务表
     * @param ids
     */
    void deleteHrWorkInjury(List<String> ids);

    /**
     * 分页查询工伤服务表
     * @param hrWorkInjuryDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrWorkInjuryDTO hrWorkInjuryDTO,Long pageNumber,Long pageSize);

    /**
     * 查看工伤详情
     * @param hrWorkInjuryDTO
     * @return
     */
    HrWorkInjuryDTO getDetail(HrWorkInjuryDTO hrWorkInjuryDTO);

    /**
     * 批量进行修改
     * @param hrWorkInjuryDTOList
     * @param status
     * @return
     */
    ResponseEntity<?> updateStates(List<HrWorkInjuryDTO> hrWorkInjuryDTOList, Integer status);

    /**
     * 微信 工伤的进度详情
     * @return
     */
    List<HrWorkInjuryDTO> getWorkInjury();

    /**
     * 导出工伤信息
     * @param hrWorkInjuryDTO
     * @param httpServletResponse
     * @return
     */
    String exportWorkInjuries(HrWorkInjuryDTO hrWorkInjuryDTO, HttpServletResponse httpServletResponse);

    /**
     * 跟新鉴定结果
     */
    void identificationResult(HrWorkInjuryDTO hrWorkInjuryDTO);

    /**
     * 检查工伤是否发送返岗通知
     */
    void updateWorkInjury();

    /**
     * 微信 工伤单条的进度详情
     * @return
     */
    HrWorkInjuryDTO getWorkInjuryOne(String serviceId);
}
