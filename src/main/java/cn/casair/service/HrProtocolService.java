package cn.casair.service;

import cn.casair.domain.HrProtocol;
import cn.casair.dto.HrAppendixDTO;
import cn.casair.dto.HrProtocolDTO;
import cn.casair.dto.HrRemindConfDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
public interface HrProtocolService extends IService<HrProtocol> {


    /**
     * 创建
     * @param hrProtocolDTO
     * @return
     */
    HrProtocolDTO createHrProtocol(HrProtocolDTO hrProtocolDTO);

    /**
     * 修改
     * @param hrProtocolDTO
     * @return
     */
    ResponseEntity  updateHrProtocol(HrProtocolDTO hrProtocolDTO);

    /**
     * 查询详情
     * @param id
     * @return
     */
    HrProtocolDTO getHrProtocol(String id);

    /**
     * 删除
     * @param id
     */
    void deleteHrProtocol(Integer id);

    /**
     * 批量删除
     * @param ids
     */
    void deleteHrProtocol(List<String> ids);
    /**
     * 定时任务
     */
    void configureTasks();


    /**
     * 定时任务
     */
    void updateProtocolRenewType();

    /**
     * 分页查询
     * @param hrProtocolDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrProtocolDTO hrProtocolDTO,Long pageNumber,Long pageSize);


    HrProtocolDTO getappendix(String id);

    HrAppendixDTO uploadMultipleFileAppendix(String staffId, MultipartFile file);

    void deleteMultipleFileappendix(String staffId, String appendixId);

    String importHrStaff(MultipartFile file );

    ResponseEntity updateHrDocking(HrProtocolDTO hrProtocolDTO, MultipartFile file);

    String importHrProtocolsTemplate(HttpServletResponse response);

    List<HrProtocolDTO> getHrProtocolAppendix(HrProtocolDTO protocolDTO);

   List<HrProtocolDTO>  HrProtocolsselectProtocols(HrProtocolDTO hrProtocolDTO);

    IPage<HrProtocolDTO> historypage(HrProtocolDTO hrProtocolDTO, Long pageNumber, Long pageSize);

    HrProtocolDTO createUpdateHrProtocol(HrProtocolDTO hrProtocolDTO);

    HrRemindConfDTO getProtocolTime();

    HrProtocolDTO getClientProtocolList(String clientId);

    void updateStaffLoginStatus();

    /**
     * 不分页查询客户协议列表
     * @param hrProtocolDTO
     * @return
     */
    List<HrProtocolDTO> findList(HrProtocolDTO hrProtocolDTO);

    /**
     * 检查并更改客户协议状态
     */
    void scheduleTaskToUpdateProtocolStates();
}
