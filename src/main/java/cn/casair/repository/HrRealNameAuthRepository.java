package cn.casair.repository;

import cn.casair.domain.HrRealNameAuth;
import cn.casair.dto.HrRealNameAuthDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 实名认证数据库操作类
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Repository
public interface HrRealNameAuthRepository extends BaseMapper<HrRealNameAuth> {

    /**
     * 获取实名认证信息
     *
     * @param hrRealNameAuthDTO
     * @return cn.casair.domain.HrRealNameAuth
     * <AUTHOR>
     * @date 2021/12/2
     **/
    HrRealNameAuth selectAuthInfo(@Param("params") HrRealNameAuthDTO hrRealNameAuthDTO);
}
