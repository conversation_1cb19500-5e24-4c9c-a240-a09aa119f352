<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillDynamicFieldsRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        bill_id,
        content,
        data_start_rows,
        origin_bill_url,
        origin_bill_name,
        is_delete ,
        created_by ,
        last_modified_by ,
        created_date ,
        last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillDynamicFields">
        <id column="id" property="id"/>
        <result column="bill_id" property="billId"/>
        <result column="content" property="content"/>
        <result column="data_start_rows" property="dataStartRows"/>
        <result column="origin_bill_url" property="originBillUrl"/>
        <result column="origin_bill_name" property="originBillName"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getBatchBillId" resultType="cn.casair.dto.HrBillDynamicFieldsDTO">
        SELECT * FROM hr_bill_dynamic_fields bf WHERE bf.is_delete = 0 AND bf.bill_id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

</mapper>
