package cn.casair.repository;

import cn.casair.domain.HrRecruitmentStation;
import cn.casair.dto.HrRecruitmentStationDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 招聘岗位表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Repository
public interface HrRecruitmentStationRepository extends BaseMapper<HrRecruitmentStation> {


    List<HrRecruitmentStationDTO> selectByServiceId(@Param("serviceId") String serviceId);

    List<HrRecruitmentStationDTO> listByServiceId(String serviceId);

    List<HrRecruitmentStationDTO> selsecthrRecruitmentStationRepository(String id);

    List<HrRecruitmentStationDTO> findList(String serviceId);
}
