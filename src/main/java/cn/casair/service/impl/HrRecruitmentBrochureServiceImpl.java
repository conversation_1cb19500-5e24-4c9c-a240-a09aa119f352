package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.CompanyInfoEnum;
import cn.casair.common.enums.MessageTemplateEnum;
import cn.casair.common.enums.RecruitmentBrochure;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.FileUtil;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.mapper.*;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.asynchronous.HrRecruitmentBrochureComponent;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 招聘简章服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrRecruitmentBrochureServiceImpl extends ServiceImpl<HrRecruitmentBrochureRepository, HrRecruitmentBrochure> implements HrRecruitmentBrochureService {

    private final HrRecruitmentBrochureComponent hrRecruitmentBrochureComponent;
    private final HrRecruitmentBrochureRepository hrRecruitmentBrochureRepository;
    private final HrRecruitmentBrochureMapper hrRecruitmentBrochureMapper;
    private final HrRecruitmentStationService hrRecruitmentStationService;
    private final HrRecruitmentStationMapper hrRecruitmentStationMapper;
    private final HrClientService hrClientService;
    private final HrRecruitmentStationRepository hrRecruitmentStationRepository;
    private final HrAppendixRepository hrAppendixRepository;
    private final HrTemplateRepository hrTemplateRepository;
    private final HrTemplateMapper hrTemplateMapper;
    private final HrContentTemplateRepository hrContentTemplateRepository;
    private final HrContentTemplateMapper hrContentTemplateMapper;
    private final HrCertificateRepository hrCertificateRepository;
    private final HrClientRepository hrClientRepository;
    private final HrCertificateMapper hrCertificateMapper;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrAppendixService hrAppendixService;
    private final CodeTableService codeTableService;
    private final HrRegistrationInfoRepository hrRegistrationInfoRepository;
    private final HrRegistrationDetailsRepository hrRegistrationDetailsRepository;
    private final HrRecruitmentBulletinRepository hrRecruitmentBulletinRepository;
    private final HrRecruitmentBulletinMapper hrRecruitmentBulletinMapper;
    private final HrRegistrationDetailsService hrRegistrationDetailsService;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrExamService hrExamService;
    private final SysOperLogService sysOperLogService;
    private final HrPaperManagementRepository hrPaperManagementRepository;
    private final HrAppletMessageService hrAppletMessageService;
    private final HrSmsTemplateService hrSmsTemplateService;
    private final HrRecruitmentBulletinService hrRecruitmentBulletinService;

    @Value("${file.temp-path}")
    private String tempPath;
    @Value("${minio.serverUrl}")
    private String serverUrl;
    @Value("${mini.appletName}")
    private String appletSite;

    /**
     * 创建招聘简章
     *
     * @param hrRecruitmentBrochureDTO
     * @return
     */
    @Override
    public HrRecruitmentBrochureDTO createHrRecruitmentBrochure(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO) {
        log.info("Create new HrRecruitmentBrochure:{}", hrRecruitmentBrochureDTO);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrRecruitmentBrochure hrRecruitmentBrochure = this.hrRecruitmentBrochureMapper.toEntity(hrRecruitmentBrochureDTO);
        if (hrRecruitmentBrochureDTO.getIdentification().equals(RecruitmentBrochure.Identification.PRESERVATION.getKey())) {
            hrRecruitmentBrochure.setRegisterState(RecruitmentBrochure.RegisterState.SUBMIT_FOR_REVIEW.getKey());
        }
        if (hrRecruitmentBrochureDTO.getIdentification().equals(RecruitmentBrochure.Identification.SUBMIT.getKey())) {
            hrRecruitmentBrochure.setRegisterState(RecruitmentBrochure.RegisterState.TO_BE_REVIEWED.getKey());
        }
        if (CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getAppendixIdList())) {
            hrRecruitmentBrochure.setAppendixIds(String.join(",", hrRecruitmentBrochureDTO.getAppendixIdList()));
        }
        this.hrRecruitmentBrochureRepository.insert(hrRecruitmentBrochure);

        HrRecruitmentBrochureDTO toDto = this.hrRecruitmentBrochureMapper.toDto(hrRecruitmentBrochure);
        for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentBrochureDTO.getHrRecruitmentStation()) {
            this.saveHrRecruitmentStation(toDto, hrRecruitmentStationDTO,null);
        }
        //添加操作信息
        hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(toDto.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "新建了招聘简章", null, ServiceCenterEnum.RECRUITMENT_BROCHURES.getKey());
        HrRecruitmentBrochureDTO recruitmentBrochureDTO = this.hrRecruitmentBrochureRepository.findRecruitmentBrochureById(toDto.getId());
        recruitmentBrochureDTO.setPostName(String.join(",", hrRecruitmentBrochureDTO.getHrRecruitmentStation().stream().map(HrRecruitmentStationDTO::getRecruitmentStationName).collect(Collectors.toList())));
        if (CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getAppendixIdList())) {
            List<HrAppendix> hrAppendices = hrAppendixRepository.selectBatchIds(hrRecruitmentBrochureDTO.getAppendixIdList());
            hrRecruitmentBrochureDTO.setOriginNameList(String.join(",", hrAppendices.stream().map(HrAppendix::getOriginName).collect(Collectors.toList())));
        }
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.RECRUITMENT_BROCHURES.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(recruitmentBrochureDTO),
            HrRecruitmentBrochureDTO.class,
            null,
            JSON.toJSONString(toDto)
        );
        return toDto;
    }

    private void saveHrRecruitmentStation(HrRecruitmentBrochureDTO toDto, HrRecruitmentStationDTO hrRecruitmentStationDTO,List<HrRecruitmentStation> collect) {
        HrRecruitmentStation hrRecruitmentStation = collect == null || collect.isEmpty() ? null : collect.get(0);
        if (hrRecruitmentStationDTO.getId() == null){
            hrRecruitmentStationDTO.setClientId(toDto.getClientId());
            hrRecruitmentStationDTO.setServiceId(toDto.getId());
            if (hrRecruitmentStationDTO.getRecruitmentFee().equals(BigDecimal.ZERO)) {
                hrRecruitmentStationDTO.setIsNeedPay(false);
            } else {
                hrRecruitmentStationDTO.setIsNeedPay(true);
            }
            hrRecruitmentStationService.save(hrRecruitmentStationMapper.toEntity(hrRecruitmentStationDTO));
        }
        //获取报名模板Id
        if (hrRecruitmentStation == null){
            HrTemplate hrTemplate = this.hrTemplateRepository.selectById(hrRecruitmentStationDTO.getRegisterTemplateId());
            hrTemplate.setFrequency(hrTemplate.getFrequency() + 1);
            hrTemplateRepository.updateById(hrTemplate);
        }else {
            if (!hrRecruitmentStationDTO.getRegisterTemplateId().equals(hrRecruitmentStation.getRegisterTemplateId())){
                HrTemplate hrTemplate = this.hrTemplateRepository.selectById(hrRecruitmentStationDTO.getRegisterTemplateId());
                hrTemplate.setFrequency(hrTemplate.getFrequency() + 1);
                hrTemplateRepository.updateById(hrTemplate);
            }
        }
        //增加试卷使用次数
        if (hrRecruitmentStationDTO.getPaperId() != null) {
            if (hrRecruitmentStation == null){
                this.updatePaperUsageSum(hrRecruitmentStationDTO.getPaperId());
            }else {
                if (hrRecruitmentStation.getPaperId() == null || !hrRecruitmentStation.getPaperId().equals(hrRecruitmentStationDTO.getPaperId())){
                    this.updatePaperUsageSum(hrRecruitmentStationDTO.getPaperId());
                }
            }
        }
        if (hrRecruitmentStationDTO.getInterviewPaperId() != null) {
            if (hrRecruitmentStation == null){
                this.updatePaperUsageSum(hrRecruitmentStationDTO.getInterviewPaperId());
            }else {
                if (hrRecruitmentStation.getInterviewPaperId() == null || !hrRecruitmentStation.getInterviewPaperId().equals(hrRecruitmentStationDTO.getInterviewPaperId())){
                    this.updatePaperUsageSum(hrRecruitmentStationDTO.getInterviewPaperId());
                }
            }
        }
        if (StringUtils.isNotBlank(hrRecruitmentStationDTO.getPaperId())
            || hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())) {
            String paperId = hrRecruitmentStationDTO.getPaperId();
            if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())){
                paperId = hrRecruitmentStationDTO.getInterviewPaperId();
            }
            //根据招聘名称以及岗位名称搜索之前是否存在考试结果管理
            HrExam hrExam = hrExamService.getOne(new QueryWrapper<HrExam>()
                .eq("exam_name", toDto.getRecruitBrochureName()).eq("profession_name", hrRecruitmentStationDTO.getRecruitmentStationName()));
            //如果考试是 先笔后面 或先面后笔 获取面试试卷名称
            String interviewPaper = "";
            if (hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())||hrRecruitmentStationDTO.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())){

                HrPaperManagement hrPaperManagement = hrPaperManagementRepository.selectById(hrRecruitmentStationDTO.getInterviewPaperId());
                if (hrPaperManagement!=null&& cn.casair.common.utils.StringUtils.isNotBlank(hrPaperManagement.getPaperName())){
                    interviewPaper = hrPaperManagement.getPaperName();
                }
            }
            if (hrExam == null) {
                HrExamDTO hrExamDTO = new HrExamDTO();
                hrExamDTO.setPaperId(paperId);
                hrExamDTO.setProfessionName(hrRecruitmentStationDTO.getRecruitmentStationName());
                hrExamDTO.setExamName(toDto.getRecruitBrochureName());
                //属于简章
                hrExamDTO.setExamsType(1);
                hrExamDTO.setBrochurePaper(interviewPaper);
                hrExamService.saveHrExam(hrExamDTO);
            } else {
                hrExam.setPaperId(paperId);
                hrExam.setBrochurePaper(interviewPaper);
                hrExamService.updateById(hrExam);
            }
        }
    }

    /**
     * 增加试卷使用次数
     * @param paperId 试卷Id
     */
    private void updatePaperUsageSum(String paperId) {
        HrPaperManagement hrPaperManagement = hrPaperManagementRepository.selectById(paperId);
        if (hrPaperManagement != null) {
            int usageSum = hrPaperManagement.getUsageSum() == null ? 0 : hrPaperManagement.getUsageSum();
            hrPaperManagement.setUsageSum(usageSum + 1);
            hrPaperManagementRepository.updateById(hrPaperManagement);
        }
    }

    /**
     * 修改招聘简章
     *
     * @param hrRecruitmentBrochureDTO
     * @return
     */
    @Override
    public Optional<HrRecruitmentBrochureDTO> updateHrRecruitmentBrochure(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        return Optional.ofNullable(this.hrRecruitmentBrochureRepository.selectById(hrRecruitmentBrochureDTO.getId()))
            .map(roleTemp -> {
                String message;
                HrRecruitmentBrochure hrRecruitmentBrochure = this.hrRecruitmentBrochureMapper.toEntity(hrRecruitmentBrochureDTO);
                if (hrRecruitmentBrochureDTO.getIdentification().equals(RecruitmentBrochure.Identification.PRESERVATION.getKey())) {
                    hrRecruitmentBrochure.setRegisterState(RecruitmentBrochure.RegisterState.SUBMIT_FOR_REVIEW.getKey());
                    message = jwtUserDTO.getRealName() + "重新保存了招聘简章";
                } else if (hrRecruitmentBrochureDTO.getIdentification().equals(RecruitmentBrochure.Identification.SUBMIT.getKey())) {
                    hrRecruitmentBrochure.setRegisterState(RecruitmentBrochure.RegisterState.TO_BE_REVIEWED.getKey());
                    message = jwtUserDTO.getRealName() + "重新提交了招聘简章";
                }else {
                    message = jwtUserDTO.getRealName() + "调整了招聘简章";
                }
                if (CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getAppendixIdList())) {
                    if (hrRecruitmentBrochure.getAppendixIds() != null){
                        List<HrAppendix> hrAppendices = hrAppendixRepository.selectBatchIds(Arrays.asList(hrRecruitmentBrochure.getAppendixIds().split(",")));
                        List<String> collect = hrAppendices.stream().map(HrAppendix::getOriginName).collect(Collectors.toList());
                        roleTemp.setAppendixIds(String.join(",",collect));
                    }else {
                        roleTemp.setAppendixIds(null);
                    }
                    hrRecruitmentBrochure.setAppendixIds(String.join(",", hrRecruitmentBrochureDTO.getAppendixIdList()));
                    List<HrAppendix> hrAppendices = hrAppendixRepository.selectBatchIds(hrRecruitmentBrochureDTO.getAppendixIdList());
                    List<String> collect = hrAppendices.stream().map(HrAppendix::getOriginName).collect(Collectors.toList());
                    hrRecruitmentBrochureDTO.setAppendixIds(String.join(",",collect));
                }else {
                    hrRecruitmentBrochure.setAppendixIds(null);
                }
                //若删除掉之前已添加的岗位，报名系统中该岗位消失，已报名用户的信息删除
                List<HrRecruitmentStationDTO> hrRecruitmentStation = hrRecruitmentBrochureDTO.getHrRecruitmentStation();
                List<HrRecruitmentStationDTO> hrRecruitmentStationDTOList = hrRecruitmentStationRepository.findList(hrRecruitmentBrochure.getId());
                List<HrRecruitmentStation> hrRecruitmentStations = hrRecruitmentStationRepository.selectList(new QueryWrapper<HrRecruitmentStation>().eq("service_id", hrRecruitmentBrochure.getId()));
                boolean flag = false;
                for (HrRecruitmentStationDTO ls : hrRecruitmentStation) {
                    boolean anyMatch = hrRecruitmentStations.stream().anyMatch(tmp -> tmp.getRecruitmentStationId().equals(ls.getRecruitmentStationId())
                        && tmp.getRecruitmentStationName().equals(ls.getRecruitmentStationName()) && tmp.getRecruitmentPeopleNumber().equals(ls.getRecruitmentPeopleNumber())
                        && tmp.getRecruitmentFee().equals(ls.getRecruitmentFee()) && tmp.getExamFormat().equals(ls.getExamFormat()) && tmp.getExamFormat().equals(ls.getExamFormat())
                        && tmp.getRegisterTemplateId().equals(ls.getRegisterTemplateId()) && tmp.getPaperId().equals(ls.getPaperId()) && tmp.getInterviewPaperId().equals(ls.getInterviewPaperId()));
                    if (!anyMatch) {
                        flag = true;
                    }
                    List<HrRecruitmentStation> collect = hrRecruitmentStations.stream().filter(tmp -> tmp.getId().equals(ls.getId())).distinct().collect(Collectors.toList());
                    if (ls.getId() != null && !ls.getIsDelete()) {
                        if (ls.getRecruitmentFee().equals(BigDecimal.ZERO)) {
                            ls.setIsNeedPay(false);
                        } else {
                            ls.setIsNeedPay(true);
                        }
                        if (CollectionUtils.isNotEmpty(collect)){
                            HrRecruitmentStation station = collect.get(0);
                            if (!ls.getRegisterTemplateId().equals(station.getRegisterTemplateId())){
                                ls.setPaymentNum(0).setRecruitmentNum(0).setPassNum(0);
                                List<String> recruitmentStationIds = Arrays.asList(station.getId());
                                this.deleteEnrollmentInFor(hrRecruitmentBrochure.getId(), recruitmentStationIds,true);
                            }
                            if (!ls.getRecruitmentStationName().equals(station.getRecruitmentStationName())){
                                ls.setPaymentNum(0).setRecruitmentNum(0).setPassNum(0);
                                List<String> recruitmentStationIds = Arrays.asList(station.getId());
                                this.deleteEnrollmentInFor(hrRecruitmentBrochure.getId(), recruitmentStationIds,false);
                            }
                        }
                        hrRecruitmentStationService.updateById(hrRecruitmentStationMapper.toEntity(ls));
                    }
                    this.saveHrRecruitmentStation(hrRecruitmentBrochureDTO, ls,collect);
                }
                List<String> recruitmentStationIds = hrRecruitmentStation.stream().filter(ls -> ls.getIsDelete())
                    .map(HrRecruitmentStationDTO::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(recruitmentStationIds)) {
                    //删除岗位并且删除报名信息
                    List<String> recruitmentStationNames = hrRecruitmentStation.stream().filter(ls -> ls.getIsDelete())
                        .map(HrRecruitmentStationDTO::getRecruitmentStationName).collect(Collectors.toList());
                    this.deleteEnrollmentInFor(hrRecruitmentBrochure.getId(), recruitmentStationIds,false);
                    hrRecruitmentStationRepository.deleteBatchIds(recruitmentStationIds);
                }

                this.hrRecruitmentBrochureRepository.updateBrochure(hrRecruitmentBrochure);
                hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRecruitmentBrochure.getId(), null, jwtUserDTO.getId(), message, null, ServiceCenterEnum.RECRUITMENT_BROCHURES.getKey());
                log.info("Update HrRecruitmentBrochure:{}", hrRecruitmentBrochureDTO);
                // 操作日志
                HrRecruitmentBrochureDTO dto = hrRecruitmentBrochureMapper.toDto(roleTemp);
                if (flag){
                    List<HrRecruitmentStationDTO> collect = hrRecruitmentStation.stream().filter(ls -> !ls.getIsDelete()).collect(Collectors.toList());
                    this.statisticsPostName(hrRecruitmentBrochureDTO,dto,collect,hrRecruitmentStationDTOList);
                }
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.RECRUITMENT_BROCHURES.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrRecruitmentBrochureDTO),
                    HrRecruitmentBrochureDTO.class,
                    null,
                    JSON.toJSONString(dto),
                    JSON.toJSONString(hrRecruitmentBrochureDTO),
                    null,
                    HrRecruitmentBrochureDTO.class
                );
                return hrRecruitmentBrochureDTO;
            });
    }

    /**
     *
     * @param hrRecruitmentBrochureDTO 前端传值
     * @param dto 数据库值
     * @param hrRecruitmentStation 前端传值
     * @param hrRecruitmentStations 数据库值
     */
    private void statisticsPostName(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO, HrRecruitmentBrochureDTO dto, List<HrRecruitmentStationDTO> hrRecruitmentStation, List<HrRecruitmentStationDTO> hrRecruitmentStations) {
        List<String> strings = new ArrayList<>();
        for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentStation) {
            JSONObject jsonObject = new JSONObject();
            setRecruitmentStationDict(hrRecruitmentStationDTO);
            jsonObject.put("岗位名称",hrRecruitmentStationDTO.getRecruitmentStationName());
            jsonObject.put("招聘人数",hrRecruitmentStationDTO.getRecruitmentPeopleNumber());
            jsonObject.put("报名费",hrRecruitmentStationDTO.getRecruitmentFee().setScale(3,BigDecimal.ROUND_HALF_DOWN));
            jsonObject.put("考试形式",hrRecruitmentStationDTO.getExamFormatName());
            jsonObject.put("报名模板",hrRecruitmentStationDTO.getTemplateName());
            if(hrRecruitmentStationDTO.getPaperId() != null){
                jsonObject.put("笔试试卷",hrPaperManagementRepository.selectById(hrRecruitmentStationDTO.getPaperId()).getPaperName());
            }
            if (hrRecruitmentStationDTO.getInterviewPaperId() != null){
                jsonObject.put("面试试卷",hrPaperManagementRepository.selectById(hrRecruitmentStationDTO.getInterviewPaperId()).getPaperName());
            }
            strings.add(jsonObject.toString());
        }
        hrRecruitmentBrochureDTO.setPostName(String.join(",",strings));
        List<String> string = new ArrayList<>();
        for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentStations) {
            JSONObject jsonObject = new JSONObject();
            setRecruitmentStationDict(hrRecruitmentStationDTO);
            jsonObject.put("岗位名称",hrRecruitmentStationDTO.getRecruitmentStationName());
            jsonObject.put("招聘人数",hrRecruitmentStationDTO.getRecruitmentPeopleNumber());
            jsonObject.put("报名费",hrRecruitmentStationDTO.getRecruitmentFee().setScale(3,BigDecimal.ROUND_HALF_DOWN));
            jsonObject.put("考试形式",hrRecruitmentStationDTO.getExamFormatName());
            jsonObject.put("报名模板",hrRecruitmentStationDTO.getTemplateName());
            if(hrRecruitmentStationDTO.getPaperId() != null){
                jsonObject.put("笔试试卷",hrPaperManagementRepository.selectById(hrRecruitmentStationDTO.getPaperId()).getPaperName());
            }
            if (hrRecruitmentStationDTO.getInterviewPaperId() != null){
                jsonObject.put("面试试卷",hrPaperManagementRepository.selectById(hrRecruitmentStationDTO.getInterviewPaperId()).getPaperName());
            }
            string.add(jsonObject.toString());
        }
        dto.setPostName(String.join(",",string));
    }

    /**
     * 删除报名信息
     *  @param brochureId            报名简章ID
     * @param recruitmentStationIds 招聘岗位ID集合
     * @param flag
     */
    private void deleteEnrollmentInFor(String brochureId, List<String> recruitmentStationIds, Boolean flag) {
        QueryWrapper<HrRegistrationDetails> qw = new QueryWrapper<>();
        qw.eq(StringUtils.isNotBlank(brochureId), "brochure_id", brochureId);
        qw.in(CollectionUtils.isNotEmpty(recruitmentStationIds), "station_id", recruitmentStationIds);
        List<HrRegistrationDetailsDTO> hrRegistrationDetailsList = hrRegistrationDetailsRepository.findList(brochureId,recruitmentStationIds);
        if (CollectionUtils.isNotEmpty(hrRegistrationDetailsList)){
            hrRegistrationDetailsList.forEach(ls->{
                String message = "";
                HashMap<Integer, String> params = new HashMap<>();
                params.put(1, ls.getName());
                params.put(2, ls.getRecruitBrochureName());
                params.put(3, ls.getStationName());
                params.put(4, appletSite);
                params.put(5, CompanyInfoEnum.FIRST_PART_PHONE_FOUR.getValue());
                if (flag){
                    message = "您报名的岗位（"+ls.getStationName()+"）的报名模板被更改。如果需要请重新报名";
                    if (ls.getPhone() != null){
                        this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.REGISTRATION_TEMPLATE_MODIFICATION.getTemplateCode(), ls.getPhone());
                    }
                }else {
                    message = "您报名的招聘简章中岗位（"+ls.getStationName()+"）已被删除。如有问题请联系黄岛人力资源";
                    if (ls.getPhone() != null) {
                        this.hrSmsTemplateService.hrSmsTemplateSendSms(params, MessageTemplateEnum.RECRUITMENT_POSITION_DELETION.getTemplateCode(), ls.getPhone());
                    }
                }
                hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.SIGN_UP.getKey(), ls.getId(), ls.getStaffId(), message, false, ServiceCenterEnum.SIGN_UP.getValue());
            });
        }
        List<String> staffIdList = hrRegistrationDetailsList.stream().map(HrRegistrationDetailsDTO::getStaffId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(staffIdList)) {
            List<HrTalentStaff> hrTalentStaffs = hrTalentStaffRepository.selectBatchIds(staffIdList);
            for (HrTalentStaff hrTalentStaff : hrTalentStaffs) {
                if (hrTalentStaff.getOpenId() != null) {
                    hrTalentStaff.setOpenId(null);
                    hrTalentStaffRepository.updateById(hrTalentStaff);
                }
            }
        }
        List<String> infoIdList = hrRegistrationDetailsList.stream().map(HrRegistrationDetailsDTO::getInfoId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(infoIdList)) {
            hrRegistrationInfoRepository.deleteBatchIds(infoIdList);
        }
        List<String> detailsIdList = hrRegistrationDetailsList.stream().map(HrRegistrationDetailsDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(detailsIdList)) {
            hrRegistrationDetailsRepository.deleteBatchIds(detailsIdList);
        }

    }

    /**
     * 查询招聘简章详情
     *
     * @param id
     * @return
     */
    @Override
    public HrRecruitmentBrochureDTO getHrRecruitmentBrochure(String id) {
        log.info("Get HrRecruitmentBrochure :{}", id);
        HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO = this.hrRecruitmentBrochureRepository.findRecruitmentBrochureById(id);
        List<HrRecruitmentStationDTO> recruitmentStationDTOList = hrRecruitmentStationRepository.findList(hrRecruitmentBrochureDTO.getId());
        if (CollectionUtils.isNotEmpty(recruitmentStationDTOList)) {
            recruitmentStationDTOList.forEach(this::setRecruitmentStationDict);
            hrRecruitmentBrochureDTO.setHrRecruitmentStation(recruitmentStationDTOList);
        }
        //返回附件信息
        if (StringUtils.isNotBlank(hrRecruitmentBrochureDTO.getAppendixIds())) {
            List<HrAppendixDTO> hrAppendixDTOS = hrAppendixService.getHrAppendixListByIds(Arrays.asList(hrRecruitmentBrochureDTO.getAppendixIds().split(",")));
            if (CollectionUtils.isNotEmpty(hrAppendixDTOS)) {
                hrRecruitmentBrochureDTO.setHrAppendixDTOS(hrAppendixDTOS);
            }
        }
        //返回审核信息
        List<HrApplyOpLogsDTO> hrApplyOpLogsDTOS = hrApplyOpLogsService.findApplyOpLogsList(hrRecruitmentBrochureDTO.getId(), null);
        if (CollectionUtils.isNotEmpty(hrApplyOpLogsDTOS)) {
            hrRecruitmentBrochureDTO.setApplyOpLogsList(hrApplyOpLogsDTOS);
        }
        return hrRecruitmentBrochureDTO;

    }

    /**
     * 批量删除招聘简章
     *
     * @param ids
     */
    @Override
    public void deleteHrRecruitmentBrochure(List<String> ids) {
        log.info("Delete HrRecruitmentBrochures:{}", ids);
        List<HrRecruitmentBrochure> hrRecruitmentBrochureList = hrRecruitmentBrochureRepository.selectBatchIds(ids);
        List<HrRecruitmentBrochure> recruitmentBrochureList = hrRecruitmentBrochureList.stream().filter(ls ->
                ls.getRegisterState().equals(RecruitmentBrochure.RegisterState.TO_BE_REVIEWED.getKey())
                    || ls.getRegisterState().equals(RecruitmentBrochure.RegisterState.END_OF_RECRUITMENT.getKey()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(recruitmentBrochureList)) {
            throw new CommonException("只有“待提交审核、审核未通过、待发布”这三个状态下的招聘简章可删除！");
        } else {
            hrRecruitmentBrochureRepository.deleteBatchIds(ids);
            JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
            List<String> collect = hrRecruitmentBrochureList.stream().map(HrRecruitmentBrochure::getRecruitBrochureName).collect(Collectors.toList());
            this.sysOperLogService.insertSysOper(
                ModuleTypeEnum.RECRUITMENT_BROCHURES.getValue(),
                BusinessTypeEnum.DELETE.getKey(),
                "删除招聘简章: " + JSON.toJSONString(collect),
                null,
                null,
                null,
                JSON.toJSONString(hrRecruitmentBrochureList),
                jwtUserDTO
            );
        }
    }

    /**
     * 分页查询招聘简章
     *
     * @param hrRecruitmentBrochureDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO, Long pageNumber, Long pageSize) {
        Page<HrRecruitmentBrochure> page = new Page<>(pageNumber, pageSize);
        this.setAssignment(hrRecruitmentBrochureDTO);
        IPage<HrRecruitmentBrochureDTO> iPage = this.hrRecruitmentBrochureRepository.findPage(page, hrRecruitmentBrochureDTO);
        for (HrRecruitmentBrochureDTO hrRecruitmentBrochure : iPage.getRecords()) {
            this.setRecruitmentBrochureDict(hrRecruitmentBrochure);
            Integer count = hrRecruitmentBulletinRepository.selectCount(new QueryWrapper<HrRecruitmentBulletin>().eq("recruitment_brochure_id", hrRecruitmentBrochure.getId()));
            if (count > 0) {
                hrRecruitmentBrochure.setIsChapterAdjustment(0);
            }
            List<HrRecruitmentStationDTO> hrRecruitmentStationDTOS = hrRecruitmentStationRepository.findList(hrRecruitmentBrochure.getId());
            hrRecruitmentStationDTOS.forEach(this::setRecruitmentStationDict);
            hrRecruitmentBrochure.setHrRecruitmentStation(hrRecruitmentStationDTOS);
        }
        return iPage;
    }

    private void setAssignment(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        if (CollectionUtils.isEmpty(hrRecruitmentBrochureDTO.getClientIds())) {
            //获取当前登录人的数据权限
            List<String> clientIds = hrClientService.selectClientIdByUserId();
            if (CollectionUtils.isEmpty(clientIds)) {
                clientIds.add("");
            }
            hrRecruitmentBrochureDTO.setClientIds(clientIds);
        }
        //先根据岗位进行查找
        QueryWrapper<HrRecruitmentStation> qw = new QueryWrapper<>();
        qw.between(CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getRecruitmentPeopleNumberQuery()), "recruitment_people_number", hrRecruitmentBrochureDTO.getRecruitmentPeopleNumberStart(), hrRecruitmentBrochureDTO.getRecruitmentPeopleNumberEnd());
        qw.between(CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getRecruitmentNumQuery()), "recruitment_num", hrRecruitmentBrochureDTO.getRecruitmentNumStart(), hrRecruitmentBrochureDTO.getRecruitmentNumEnd());
        qw.between(CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getPassNumQuery()), "pass_num", hrRecruitmentBrochureDTO.getPassNumStart(), hrRecruitmentBrochureDTO.getPassNumEnd());
        qw.between(CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getPaymentNumQuery()), "payment_num", hrRecruitmentBrochureDTO.getPaymentNumStart(), hrRecruitmentBrochureDTO.getPaymentNumEnd());
        qw.between(CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getRecruitmentFeeQuery()), "recruitment_fee", hrRecruitmentBrochureDTO.getRecruitmentFeeStart(), hrRecruitmentBrochureDTO.getRecruitmentFeeEnd());
        qw.in(CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getRecruitmentStationIdList()), "recruitment_station_id", hrRecruitmentBrochureDTO.getRecruitmentStationIdList());
        qw.in(CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getExamFormatList()), "exam_format", hrRecruitmentBrochureDTO.getExamFormatList());
        qw.in(CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getClientIds()), "client_id", hrRecruitmentBrochureDTO.getClientIds());
        qw.in(CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getTemplateIdList()), "register_template_id", hrRecruitmentBrochureDTO.getTemplateIdList());
        List<HrRecruitmentStation> hrRecruitmentStationList = hrRecruitmentStationRepository.selectList(qw);
        if (CollectionUtils.isEmpty(hrRecruitmentStationList)) {
            List<String> list = new ArrayList<>();
            list.add("");
            hrRecruitmentBrochureDTO.setRecruitmentBrochureIdList(list);
        }else {
            List<String> recruitmentBrochureIdList = hrRecruitmentStationList.stream().map(HrRecruitmentStation::getServiceId).distinct().collect(Collectors.toList());
            hrRecruitmentBrochureDTO.setRecruitmentBrochureIdList(recruitmentBrochureIdList);
        }
        //判断当前登录人是否是客户角色
        if (jwtUserDTO.getCurrentRoleKey().equalsIgnoreCase("client")) {
            hrRecruitmentBrochureDTO.setFlag(1);
        }
    }

    /**
     * 赋值字典值--招聘
     *
     * @param hrRecruitmentBrochureDTO
     */
    private void setRecruitmentBrochureDict(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO) {
        if (hrRecruitmentBrochureDTO.getRegisterState() != null) {
            hrRecruitmentBrochureDTO.setRegisterStateLabel(RecruitmentBrochure.RegisterState.getValueByKey(hrRecruitmentBrochureDTO.getRegisterState()));
        }
    }

    /**
     * 赋值字典值--岗位
     *
     * @param hrRecruitmentStationDTO
     */
    private void setRecruitmentStationDict(HrRecruitmentStationDTO hrRecruitmentStationDTO) {
        if (hrRecruitmentStationDTO.getExamFormat() != null) {
            hrRecruitmentStationDTO.setExamFormatName(RecruitmentBrochure.ExamFormat.getValueByKey(hrRecruitmentStationDTO.getExamFormat()));
        }
    }

    @Override
    public IPage<HrRecruitmentBrochureDTO> findPageSelectWXSignUp(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO, Long pageNumber, Long pageSize) {
        Page<HrRecruitmentBrochure> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrRegistrationDetails> wq = new QueryWrapper<>();
        QueryWrapper<HrAppendix> qw = new QueryWrapper<>();

        wq.eq("staff_id", hrRecruitmentBrochureDTO.getStaffId());
        List<HrRegistrationDetails> hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectList(wq);
        List<String> brochureId = hrRegistrationDetails.stream().map(HrRegistrationDetails::getBrochureId).distinct().collect(Collectors.toList());
        QueryWrapper<HrRecruitmentBrochure> qwa = new QueryWrapper<>();
        IPage iPages = null;
        if (CollectionUtils.isNotEmpty(brochureId)) {
            qwa.in("id", brochureId);
            iPages = this.hrRecruitmentBrochureRepository.selectPage(page, qwa);
            List<HrRecruitmentBrochureDTO> lists = this.hrRecruitmentBrochureMapper.toDto(iPages.getRecords());
            Map<Integer, String> recruitBrochureState = codeTableService.findCodeTableByInnerName("recruitBrochureState");
            Map<Integer, String> enterprisemap = codeTableService.findCodeTableByInnerName("examFormat");
            for (HrRecruitmentBrochureDTO record : lists) {
                HrClient hrClient = this.hrClientRepository.selectById(record.getClientId());
                record.setClientName(hrClient.getClientName());
                //获取岗位信息
                List<HrRecruitmentStationDTO> hrRecruitmentStationDTOList = this.hrRecruitmentBrochureRepository.getRecruitment(record.getId());
                record.setHrRecruitmentStation(hrRecruitmentStationDTOList);
                //获取数据字典
                record.setExamFormat(record.getExamFormat());
                record.setExamFormatName(enterprisemap.get(record.getExamFormat()));
                record.setRegisterState(record.getRegisterState());
                record.setRegisterStateLabel(recruitBrochureState.get(record.getRegisterState()));
                if (StringUtils.isNotEmpty(record.getAppendixIds())) {
                    //获取附件路径
                    List<String> AppendiId = new ArrayList<>();
                    AppendiId = Arrays.asList(record.getAppendixIds().split(","));
                    qw.in("id", AppendiId);
                    qw.eq("is_delete", 0);
                    qw.select("file_url");
                    List<HrAppendix> fileUrl = this.hrAppendixRepository.selectList(qw);
                    List<String> list = new ArrayList<>();
                    for (HrAppendix appendix : fileUrl) {
                        list.add(appendix.getFileUrl());
                    }
                    if (CollectionUtils.isNotEmpty(list)) {
                        record.setAppendixIdList(list);
                    }
                }
            }
            iPages.setRecords(lists);
        }

        return iPages;
    }

    /**
     * 查询是否存在未入职员工
     * @param id 招聘简章Id
     * @return
     */
    @Override
    public Boolean queryHrRecruitmentBrochure(String id) {
        List<Integer> statusList = new ArrayList<>();
        statusList.add(RecruitmentBrochure.RegistrationStatus.AUDIT_FAILED.getKey());
        statusList.add(RecruitmentBrochure.RegistrationStatus.USER_DOES_NOT_PAY.getKey());
        statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
        statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
        statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INSPECTION_RESULTS.getKey());
        statusList.add(RecruitmentBrochure.RegistrationStatus.FAILED_PHYSICAL_EXAM.getKey());
        statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
        statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INTERVIEW_SCOPE.getKey());
        statusList.add(RecruitmentBrochure.RegistrationStatus.NO_WRITTEN_EXAM_SCOPE.getKey());

        QueryWrapper<HrRegistrationDetails> wrapper = new QueryWrapper<>();
        wrapper.eq("brochure_id",id);
        wrapper.notIn("status",statusList);
        List<HrRegistrationDetails> hrRegistrationDetailsList = hrRegistrationDetailsRepository.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(hrRegistrationDetailsList)){
            return true;
        }else {
            return false;
        }
    }

    /**
     * 修改招聘状态
     * @param batchOptDTO 招聘简章Id
     * @return
     */
    @Override
    public void updateRecruitmentEnd(BatchOptDTO batchOptDTO) {
        Integer registerState;
        if (batchOptDTO.getOpt()){
            registerState = RecruitmentBrochure.RegisterState.RECRUITMENT_MANUAL_END.getKey();
        }else {
            registerState = RecruitmentBrochure.RegisterState.END_OF_RECRUITMENT.getKey();
        }
        hrRecruitmentBrochureRepository.updateRecruitmentEnd(batchOptDTO.getApplyId(), registerState);
    }

    /**
     * 官网查询招聘信息
     * @param hrRecruitmentBrochureDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrRecruitmentBrochureDTO> findPageSelectOW(HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO, Long pageNumber, Long pageSize) {
        Page<HrRecruitmentBrochure> page = new Page<>(pageNumber, pageSize);
        IPage<HrRecruitmentBrochureDTO> iPage = this.hrRecruitmentBrochureRepository.findPageSelectOW(page,hrRecruitmentBrochureDTO);
        return iPage;
    }

    /**
     * 官网招聘信息详情
     * @param recruitmentId
     * @return
     */
    @Override
    public HrRecruitmentBrochureDTO findRecruitmentInfo(String recruitmentId) {
        HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO = this.hrRecruitmentBrochureRepository.findRecruitmentBrochureById(recruitmentId);
        //返回公告信息
        List<HrRecruitmentBulletinDTO> hrRecruitmentBulletinDTOList = hrRecruitmentBulletinService.findPage(new HrRecruitmentBulletinDTO().setRecruitmentBrochureId(recruitmentId));
        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletinDTOList)){
            hrRecruitmentBrochureDTO.setHrRecruitmentBulletinDTOList(hrRecruitmentBulletinDTOList);
        }
        //返回附件信息
        if (hrRecruitmentBrochureDTO != null && StringUtils.isNotBlank(hrRecruitmentBrochureDTO.getAppendixIds())) {
            List<HrAppendixDTO> hrAppendixDTOS = hrAppendixService.getHrAppendixListByIds(Arrays.asList(hrRecruitmentBrochureDTO.getAppendixIds().split(",")));
            if (CollectionUtils.isNotEmpty(hrAppendixDTOS)) {
                hrRecruitmentBrochureDTO.setHrAppendixDTOS(hrAppendixDTOS);
            }
        }
        return hrRecruitmentBrochureDTO;
    }

    /**
     * 小程序分页查询简章
     *
     * @return
     */
    @Override
    public IPage<HrRecruitmentBrochureDTO> findPageSelectWX(Long pageNumber, Long pageSize) {
        Page<HrRecruitmentBrochure> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrAppendix> qw = new QueryWrapper<>();
        IPage<HrRecruitmentBrochureDTO> iPage = this.hrRecruitmentBrochureRepository.findPageSelectWX(page);
        Map<Integer, String> recruitBrochureState = codeTableService.findCodeTableByInnerName("recruitBrochureState");
        Map<Integer, String> enterprisemap = codeTableService.findCodeTableByInnerName("examFormat");
        HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO = null;
        if (CollectionUtils.isNotEmpty(iPage.getRecords())){
            hrRecruitmentBrochureDTO = iPage.getRecords().get(0);
        }
        HrRecruitmentBrochureDTO brochureDTO = hrRecruitmentBrochureRepository.findEnterpriseWechatUrl();
        for (HrRecruitmentBrochureDTO record : iPage.getRecords()) {
            if (hrRecruitmentBrochureDTO != null && hrRecruitmentBrochureDTO.getId().equals(record.getId()) && StringUtils.isBlank(hrRecruitmentBrochureDTO.getEnterpriseWechatUrl())){
                record.setEnterpriseWechatUrl(brochureDTO.getEnterpriseWechatUrl());
            }
            //获取岗位信息
            List<HrRecruitmentStationDTO> hrRecruitmentStationDTOList = this.hrRecruitmentBrochureRepository.getRecruitment(record.getId());
            record.setHrRecruitmentStation(hrRecruitmentStationDTOList);
            //获取数据字典
            record.setExamFormat(record.getExamFormat());
            record.setExamFormatName(enterprisemap.get(record.getExamFormat()));
            record.setRegisterState(record.getRegisterState());
            record.setRegisterStateLabel(recruitBrochureState.get(record.getRegisterState()));
            if (StringUtils.isNotEmpty(record.getAppendixIds())) {
                //获取附件路径
                List<String> AppendiId = new ArrayList<>();
                AppendiId = Arrays.asList(record.getAppendixIds().split(","));
                qw.in("id", AppendiId);
                qw.eq("is_delete", 0);
                qw.select("file_url");
                List<HrAppendix> fileUrl = this.hrAppendixRepository.selectList(qw);
                List<String> list = new ArrayList<>();
                for (HrAppendix appendix : fileUrl) {
                    list.add(appendix.getFileUrl());
                }
                if (CollectionUtils.isNotEmpty(list)) {
                    record.setAppendixIdList(list);
                }
            }
        }
        return iPage;
    }

    /**
     * 小程序查询简章详情
     *
     * @param
     * @param
     * @return
     */
    @Override
    public HrRecruitmentBrochureDTO getHrrecruitmenSelect(String id, String staffId) {
        QueryWrapper<HrClient> ew = new QueryWrapper<>();
        QueryWrapper<HrAppendix> ews = new QueryWrapper<>();
        HrRecruitmentBrochure hrRecruitmentBrochure = this.hrRecruitmentBrochureRepository.selectById(id);
        if (hrRecruitmentBrochure == null){
            throw new CommonException("未查询到相关数据！");
        }
        ew.eq("id", hrRecruitmentBrochure.getClientId());
        HrClient hrClient = this.hrClientRepository.selectOne(ew);
        HrRecruitmentBrochureDTO hrRecruitmentBrochureDTO = this.hrRecruitmentBrochureMapper.toDto(hrRecruitmentBrochure);
        hrRecruitmentBrochureDTO.setClientName(hrClient.getClientName());
        hrRecruitmentBrochureDTO.setHrRecruitmentStation(this.hrRecruitmentStationRepository.selsecthrRecruitmentStationRepository(hrRecruitmentBrochureDTO.getId()));

        //是否报名岗位添加标记
        if (StringUtils.isNotBlank(staffId)) {
            if (CollectionUtils.isNotEmpty(hrRecruitmentBrochureDTO.getHrRecruitmentStation())) {
                QueryWrapper<HrRegistrationDetails> qwe = new QueryWrapper<>();
                qwe.eq(StringUtils.isNotBlank(staffId), "staff_id", staffId);
                List<HrRegistrationDetails> hrRegistrationDetails = this.hrRegistrationDetailsRepository.selectList(qwe);
                for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentBrochureDTO.getHrRecruitmentStation()) {
                    for (HrRegistrationDetails hrRegistrationDetail : hrRegistrationDetails) {
                        if (hrRecruitmentStationDTO.getId().equals(hrRegistrationDetail.getStationId())) {
                            hrRecruitmentStationDTO.setMark(true);
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(hrRecruitmentBrochureDTO.getAppendixIds())) {
            List<String> aIdX = Arrays.asList(hrRecruitmentBrochureDTO.getAppendixIds().split(","));
            ews.in("id", aIdX);
            hrRecruitmentBrochureDTO.setAppendixIdDTOList(this.hrAppendixRepository.selectList(ews));
        }


        Map<Integer, String> enterprisemap = codeTableService.findCodeTableByInnerName("examFormat");
        for (HrRecruitmentStationDTO hrRecruitmentStationDTO : hrRecruitmentBrochureDTO.getHrRecruitmentStation()) {
            hrRecruitmentStationDTO.setExamFormat(hrRecruitmentStationDTO.getExamFormat());
            hrRecruitmentStationDTO.setExamFormatName(enterprisemap.get(hrRecruitmentStationDTO.getExamFormat()));
        }
        return hrRecruitmentBrochureDTO;
    }

    /**
     * 小程序查询简章模板 todo
     *
     * @param
     * @param
     * @return
     */
    @Override
    public HrTemplateDTO getHrrecruitmenTemplateselect(String id) {
        HrRecruitmentStation hrRecruitmentStation = this.hrRecruitmentStationRepository.selectById(id);

        QueryWrapper<HrContentTemplate> qw = new QueryWrapper<>();
        HrTemplate hrTemplate = this.hrTemplateRepository.selectById(hrRecruitmentStation.getRegisterTemplateId());
        HrTemplateDTO hrTemplateDTO = this.hrTemplateMapper.toDto(hrTemplate);
        if (StringUtils.isNotBlank(hrTemplateDTO.getContentId())) {
            List<String> hrContentTemplateIdList = Arrays.asList(hrTemplateDTO.getContentId().split(","));
            qw.in("id", hrContentTemplateIdList);
        }
        qw.eq("is_delete", 0);
        qw.orderByAsc("orders");
        List<HrContentTemplate> hrContentTemplateList = this.hrContentTemplateRepository.selectList(qw);
        List<HrContentTemplateDTO> hrContentTemplateDTOList = this.hrContentTemplateMapper.toDto(hrContentTemplateList);
        List<HrContentTemplateDTO>hrContentTemplateDTOLists=new ArrayList<>();

        for (HrContentTemplateDTO hrContentTemplateDTO : hrContentTemplateDTOList) {
            if (StringUtils.isNotBlank(hrTemplateDTO.getRequiredFieldsJson())){
                JSONObject obj = JSONObject.parseObject(hrTemplateDTO.getRequiredFieldsJson());
                String hrContentTemplateJson=obj.getString( hrContentTemplateDTO.getId() );
                HrContentTemplateDTO hrContentTemplateDTOs = JSON.parseObject(hrContentTemplateJson, HrContentTemplateDTO.class);
                hrContentTemplateDTOLists.add(hrContentTemplateDTOs);
                if (StringUtils.isNotBlank(hrContentTemplateDTOs.getLabel())){
                    hrContentTemplateDTO.setLabel(hrContentTemplateDTOs.getLabel());
                }
                if (StringUtils.isNotBlank(hrContentTemplateDTOs.getName())){
                    hrContentTemplateDTO.setName(hrContentTemplateDTOs.getName());
                }
                if ( hrContentTemplateDTOs.getRequired()!=null){
                    hrContentTemplateDTO.setRequired(hrContentTemplateDTOs.getRequired());
                }
                if (StringUtils.isNotBlank(hrContentTemplateDTOs.getDefaults())){
                    hrContentTemplateDTO.setDefaults(hrContentTemplateDTOs.getDefaults());
                }
                if (StringUtils.isNotBlank(hrContentTemplateDTOs.getType())){
                    hrContentTemplateDTO.setType(hrContentTemplateDTOs.getType());
                }
                if (StringUtils.isNotBlank(hrContentTemplateDTOs.getOptionsName())){
                    hrContentTemplateDTO.setOptionsName(hrContentTemplateDTOs.getOptionsName());
                }
                if (StringUtils.isNotBlank(hrContentTemplateDTOs.getWidth())){
                    hrContentTemplateDTO.setWidth(hrContentTemplateDTOs.getWidth());
                }
                if ( hrContentTemplateDTOs.getOrders()!=null ){
                    hrContentTemplateDTO.setOrders(hrContentTemplateDTOs.getOrders());
                }

                if (StringUtils.isNotBlank(hrContentTemplateDTOs.getRuleType())){
                    hrContentTemplateDTO.setRuleType(hrContentTemplateDTOs.getRuleType());
                }
                if (StringUtils.isNotBlank(hrContentTemplateDTOs.getGroupName())){
                    hrContentTemplateDTO.setGroupName(hrContentTemplateDTOs.getGroupName());
                }
            }
            if (StringUtils.isNotBlank(hrContentTemplateDTO.getType())) {
                if (hrContentTemplateDTO.getType().equals("appendix")) {
                    HrCertificate hrCertificate = this.hrCertificateRepository.selectById(hrContentTemplateDTO.getName());
                    HrCertificateDTO hrCertificateDTO = this.hrCertificateMapper.toDto(hrCertificate);
                    hrContentTemplateDTO.setHrCertificateDTO(hrCertificateDTO);
                }
            }
        }
        hrTemplateDTO.setHrContentTemplateDTOList(hrContentTemplateDTOList);
        return hrTemplateDTO;

    }


    /**
     * 客户审核招聘简章
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity<?> examineApprove(BatchOptDTO batchOptDTO) {
        String userName = "";
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        if (jwtUserDTO.getRealName() != null) {
            userName = jwtUserDTO.getRealName();
        }
        List<String> errorList = new ArrayList<>();//状态不匹配集合
        List<String> successList = new ArrayList<>();//状态匹配集合
        List<HrRecruitmentBrochure> hrRecruitmentBrochureList = hrRecruitmentBrochureRepository.selectBatchIds(batchOptDTO.getApplyIdList());
        for (HrRecruitmentBrochure hrRecruitmentBrochure : hrRecruitmentBrochureList) {
            String message = "";
            if (!hrRecruitmentBrochure.getRegisterState().equals(RecruitmentBrochure.RegisterState.TO_BE_REVIEWED.getKey())) {
                errorList.add(hrRecruitmentBrochure.getRecruitBrochureName());
                continue;
            }

            if (batchOptDTO.getOpt()) {
                message = userName + "审核通过了招聘简章";
                hrRecruitmentBrochure.setRegisterState(RecruitmentBrochure.RegisterState.TO_BE_RELEASED.getKey());
            } else {
                message = userName + "审核拒绝了招聘简章。拒绝理由：" + batchOptDTO.getCheckerReason();
                hrRecruitmentBrochure.setRegisterState(RecruitmentBrochure.RegisterState.AUDIT_FAILED.getKey());
            }
            hrRecruitmentBrochureRepository.updateById(hrRecruitmentBrochure);
            successList.add(hrRecruitmentBrochure.getRecruitBrochureName());
            hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRecruitmentBrochure.getId(), null, jwtUserDTO.getId(), message, null, ServiceCenterEnum.RECRUITMENT_BROCHURES.getKey());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (CollectionUtils.isNotEmpty(errorList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorList) + "的状态不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 提交招聘简章
     *
     * @param id 招聘简章ID
     */
    @Override
    public void brochureSubmit(String id) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureRepository.selectById(id);
        hrRecruitmentBrochure.setRegisterState(RecruitmentBrochure.RegisterState.TO_BE_REVIEWED.getKey());
        hrRecruitmentBrochureRepository.updateById(hrRecruitmentBrochure);
        hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRecruitmentBrochure.getId(), null, jwtUserDTO.getId(), jwtUserDTO.getRealName() + "提交了招聘简章", null, ServiceCenterEnum.RECRUITMENT_BROCHURES.getKey());
    }

    /**
     * 导出招聘简章
     *
     * @param recruitmentBrochureDTO
     * @param response
     * @return
     */
    @Override
    public String brochureExport(HrRecruitmentBrochureDTO recruitmentBrochureDTO, HttpServletResponse response) {

        this.setAssignment(recruitmentBrochureDTO);
        List<HrRecruitmentBrochureDTO> hrRecruitmentBrochureDTOS = hrRecruitmentBrochureRepository.findList(recruitmentBrochureDTO);
        if (hrRecruitmentBrochureDTOS.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        // 临时文件夹
        String zipFileName = "招聘简章" + "_" + DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
        String tempRootPath = tempPath + zipFileName + File.separator;
        cn.hutool.core.io.FileUtil.mkdir(tempRootPath);
        try {
            List<Future<String>> futureList = new ArrayList<>();
            hrRecruitmentBrochureDTOS.forEach(hrRecruitmentBrochureDTO -> {
                Future<String> result = this.hrRecruitmentBrochureComponent.dealHrRecruitmentBrochureExport(hrRecruitmentBrochureDTO, tempRootPath);
                futureList.add(result);
            });

            // 等待异步线程执行完毕
            for (Future<String> future : futureList) {
                try {
                    future.get(5, TimeUnit.MINUTES);
                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                    log.error("招聘导出异步线程获取异常:{}", e.getMessage());
                    e.printStackTrace();
                }
            }

            // 导出信息招聘简章
            ExcelUtils.exportLocal("招聘简章", HrRecruitmentBrochureDTO.class, hrRecruitmentBrochureDTOS, tempRootPath);

            // 压缩文件夹
            String zipFilePath = tempPath + zipFileName + ".zip";
            ZipUtil.zip(tempRootPath, CharsetUtil.CHARSET_UTF_8);

            return this.hrAppendixService.uploadLocalFile(zipFilePath);
        } finally {
            // 删除临时文件
            cn.hutool.core.io.FileUtil.del(tempRootPath);
        }
    }

    /**
     * 导出公告概览
     *  @param hrRecruitmentBulletinDTO
     * @param response
     */
    @Override
    public String bulletinsExport(HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO, HttpServletResponse response) {
        List<File> fileList = new ArrayList<>();
        List<HrRecruitmentBulletinDTO> list = hrRecruitmentBulletinService.findPage(hrRecruitmentBulletinDTO);
        List<String> ids = list.stream().map(HrRecruitmentBulletinDTO::getId).collect(Collectors.toList());
        this.bulletinsExportInFor(fileList, list);
        try {
            String fileUrl = this.hrAppendixService.zipAndUploadFile(fileList, "公告概览");
            this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.ANNOUNCEMENT_OVERVIEW.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), ids.size(), fileUrl);
            return fileUrl;
        } catch (Exception e) {
            throw new CommonException("导出出现错误");
        }
    }


    /**
     * 招聘公告导出信息
     *
     * @param hrRecruitmentBulletinDTOS
     * @return
     */
    private void bulletinsExportInFor(List<File> fileList, List<HrRecruitmentBulletinDTO> hrRecruitmentBulletinDTOS) {
        for (HrRecruitmentBulletinDTO hrRecruitmentBulletinDTO : hrRecruitmentBulletinDTOS) {
            //查询对应的附件信息
            if (StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getAppendixIds())) {
                List<String> appendixIds = Arrays.asList(hrRecruitmentBulletinDTO.getAppendixIds().split(","));
                List<HrAppendixDTO> hrAppendixDTOList = hrAppendixService.getHrAppendixListByIds(appendixIds);
                List<String> appendixNames = hrAppendixDTOList.stream().map(HrAppendixDTO::getOriginName).collect(Collectors.toList());
                hrRecruitmentBulletinDTO.setAppendixNames(String.join(",", appendixNames));
                hrAppendixDTOList.forEach(ls -> {
                    File file = FileUtil.getFile(ls.getFileUrl(), ls.getOriginName());
                    if (file != null) {
                        fileList.add(file);
                    }
                });
            }
            if (StringUtils.isNotBlank(hrRecruitmentBulletinDTO.getNoticeContent())) {
                String valueByKey = RecruitmentBrochure.NoticeType.getValueByKey(hrRecruitmentBulletinDTO.getNoticeType());
                String wordName = valueByKey + "-" + (hrRecruitmentBulletinDTO.getRecruitmentStationName() == null ? "" : hrRecruitmentBulletinDTO.getRecruitmentStationName()) + "公告内容";
                String wordFile = hrAppendixService.generateWordFile(hrRecruitmentBulletinDTO.getNoticeContent(), wordName, ".doc");
                fileList.add(FileUtil.getFile(wordFile, wordName));
            }
        }
        fileList.add(new File(Objects.requireNonNull(ExcelUtils.exportLocal("公告概览", HrRecruitmentBulletinDTO.class, hrRecruitmentBulletinDTOS, tempPath))));
    }
}
