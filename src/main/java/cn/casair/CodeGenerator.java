package cn.casair;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.IColumnType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import com.google.common.base.CaseFormat;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class CodeGenerator {

    public static String GeneratorModuleName = "code-generator";
    public static String AdminModuleName = "web-admin";
    public static String DomainModuleName = "cas-domain";
    public static String DtoModuleName = "cas-dto";
    public static String RepositoryModuleName = "cas-repository";
    public static String ServiceModuleName = "cas-service";
    public static String MapperModuleName = "cas-mapper";

    private static final String DATABASE_URL = "*******************************************************************************************************************************************************";
    private static final String DATABASE_USERNAME = "root";
    private static final String DATABASE_PASSWORD = "cas@!23";


    public static void main(String[] args) {

        Scanner scanner = new Scanner(System.in);
        int step = 0;

        String tableName = null;
        String moduleName = null;
        String tablePrefix = null;
        IdType idType = null;
        boolean generateDomain = false;
        boolean generateDTO = false;
        boolean generateRepository = false;
        boolean generateRepositoryXml = false;
        boolean generateService = false;
        boolean generateServiceImpl = false;
        boolean generateMapper = false;
        boolean generateController = false;
        boolean overwrite = false;
        boolean extendAbstractAuditingEntity = true;

        System.out.print("请输入表名：\n");
        while (step != -1 && scanner.hasNextLine()) {
            if (step == 0) {
                tableName = scanner.nextLine();

                if (Strings.isNullOrEmpty(tableName)) {
                    System.out.print("请输入正确表名：\n");
                } else {
                    System.out.println("请输入表前缀[默认为空]：");
                    step++;
                }
            } else if (step == 1) {
                tablePrefix = scanner.nextLine();
                System.out.println("请输入模块名[默认表注释]：");
                step++;
            } else if (step == 2) {
                moduleName = scanner.nextLine();
                step++;
                System.out.println("请输入主键策略：0).AUTO[默认] 1).NONE 2).INPUT 3).ID_WORKER 4).UUID 5).ID_WORKER_STR :");
            } else if (step == 3) {
                String stId = scanner.nextLine();
                if (Strings.isNullOrEmpty(stId)) {
                    idType = IdType.AUTO;
                } else {
                    int sId = Integer.parseInt(stId);
                    if (sId == 0) {
                        idType = IdType.AUTO;
                    } else if (sId == 1) {
                        idType = IdType.NONE;
                    } else if (sId == 2) {
                        idType = IdType.INPUT;
                    } else if (sId == 3) {
                        idType = IdType.ID_WORKER;
                    } else if (sId == 4) {
                        idType = IdType.ASSIGN_UUID;
                    } else if (sId == 5) {
                        idType = IdType.ID_WORKER_STR;
                    } else {
                        idType = IdType.AUTO;
                    }
                }

                step++;

                System.out.println("a).全部生成[默认]");
                System.out.println("do).生成Domain");
                System.out.println("dt).生成DTO");
                System.out.println("s).生成Service");
                System.out.println("si).生成ServiceImpl");
                System.out.println("r).生成Repository");
                System.out.println("rx).生成Repository xml");
                System.out.println("m).生成Mapper");
                System.out.println("c).生成Controller");
                System.out.print("请输入生成文件：\n");
            } else if (step == 4) {
                String filesStr = scanner.nextLine();

                List<String> files;
                if (Strings.isNullOrEmpty(filesStr)) {
                    files = Collections.singletonList("a");
                } else {
                    files = Splitter.on(" ").trimResults().omitEmptyStrings().splitToList(filesStr);
                }

                if (files.contains("a")) {
                    generateDomain = true;
                    generateDTO = true;
                    generateRepository = true;
                    generateRepositoryXml = true;
                    generateService = true;
                    generateServiceImpl = true;
                    generateMapper = true;
                    generateController = true;
                } else {
                    if (files.contains("do")) {
                        generateDomain = true;
                    }
                    if (files.contains("dt")) {
                        generateDTO = true;
                    }
                    if (files.contains("s")) {
                        generateService = true;
                    }
                    if (files.contains("si")) {
                        generateServiceImpl = true;
                    }
                    if (files.contains("r")) {
                        generateRepository = true;
                    }
                    if (files.contains("rx")) {
                        generateRepositoryXml = true;
                    }
                    if (files.contains("m")) {
                        generateMapper = true;
                    }
                    if (files.contains("c")) {
                        generateController = true;
                    }
                }

                step++;
                System.out.print("是否继承AbstractAuditingEntity y).是[默认] n).否 :\n");

            } else if (step == 5) {
                String writeRule = scanner.nextLine();
                if (!Strings.isNullOrEmpty(writeRule) && "n".equals(writeRule)) {
                    extendAbstractAuditingEntity = false;
                }

                step++;
                System.out.print("生成规则 a).不覆盖[默认] b).覆盖 :\n");
            } else {
                String writeRule = scanner.nextLine();
                if (!Strings.isNullOrEmpty(writeRule) && "b".equals(writeRule)) {
                    overwrite = true;
                }
                step = -1;
            }

        }

        generateCode(tableName, moduleName, tablePrefix, idType, generateDomain, generateDTO,
                generateRepository, generateRepositoryXml, generateService,
                generateServiceImpl, generateMapper, generateController, extendAbstractAuditingEntity, overwrite);
    }

    private static void generateCode(String tableName, String moduleName, String tablePrefix, IdType idType, boolean generateDomain, boolean generateDTO, boolean generateRepository, boolean generateRepositoryXml, boolean generateService, boolean generateServiceImpl, boolean generateMapper, boolean generateController, boolean extendAbstractAuditingEntity, boolean overwrite) {
        AutoGenerator generator = new AutoGenerator();
        String projectPath = setGlobalConfig(generator, idType, overwrite);
        setDataSourceConfig(generator);
        PackageConfig pc = setPackageConfig(generator);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                this.getConfig().getTableInfoList().forEach(tableInfo -> {
                    // 此处限制一次只能生成一个模块
                    this.getMap().put("entityProperty", CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_CAMEL, tableInfo.getEntityName()));
                    this.getMap().put("repositoryProperty", CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_CAMEL, tableInfo.getMapperName()));
                    this.getMap().put("serviceProperty", CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_CAMEL, tableInfo.getServiceName()));

                    this.getMap().put("restControllerPath", CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_HYPHEN, convertPlural(tableInfo.getEntityPath())));
                    if (this.getMap().get("tableComment") != null && !Strings.isNullOrEmpty(String.valueOf(this.getMap().get("tableComment")))) {
                        tableInfo.setComment((String) this.getMap().get("tableComment"));
                    }
                });
            }
        };

        // 自定义package信息
        Map<String, String> extendPackages = Maps.newHashMap();
        extendPackages.put("Dto", pc.getParent().concat(".").concat("dto"));
        extendPackages.put("Mapper", pc.getParent().concat(".").concat("mapper"));

        Map<String, Object> customConfigs = Maps.newHashMap();
        // 设置是否生成字段注释
        customConfigs.put("showFiledCommon", true);
        // 是否启用guava,生成guava风格的toString()
        customConfigs.put("enableGuava", true);
        // 是否生成guava风格的equals()和HashCode()
        customConfigs.put("equalsAndHashCodeFields", Collections.singletonList("id"));
        customConfigs.put("tableComment", moduleName);
        customConfigs.put("extPackage", extendPackages);
        customConfigs.put("superMapStructClassPackage", "cn.casair.service.mapper");
        customConfigs.put("superMapStructClassName", "EntityMapper");

        cfg.setMap(customConfigs);
        generator.setCfg(cfg);


        // 自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        if (generateRepositoryXml) {
            // 自定义Mapper xml输出
            String templatePath = "templates/mapper.xml.vm";
            focList.add(new FileOutConfig(templatePath) {
                @Override
                public String outputFile(TableInfo tableInfo) {
                    // 自定义输出文件名
                    return projectPath + "/src/main/resources/mapper/" + tableInfo.getEntityName() + "Repository" + StringPool.DOT_XML;
                }
            });
        }

        if (generateDTO) {
            // 自定义DTO输出
            String dtoTemplatePath = "templates/dto.java.vm";
            focList.add(new FileOutConfig(dtoTemplatePath) {
                @Override
                public String outputFile(TableInfo tableInfo) {
                    String dtoPath = extendPackages.get("Dto").replace(".", "/");

                    // 自定义输出文件名
                    return projectPath + "/src/main/java/" + dtoPath + "/" + tableInfo.getEntityName() + "DTO.java";
                }
            });
        }

        if (generateMapper) {
            // 自定义MapStruct 输出
            String mapStructTemplatePath = "templates/mapstruct.java.vm";
            focList.add(new FileOutConfig(mapStructTemplatePath) {
                @Override
                public String outputFile(TableInfo tableInfo) {
                    String mapperPath = extendPackages.get("Mapper").replace(".", "/");

                    // 自定义输出文件名
                    return projectPath + "/src/main/java/" + mapperPath + "/" + tableInfo.getEntityName() + "Mapper.java";
                }
            });
        }

        cfg.setFileOutConfigList(focList);

        // 自定义配置模板
        setTemplateConfig(generator, generateDomain, generateRepository, generateService, generateServiceImpl, generateController);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel);
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        if (extendAbstractAuditingEntity) {
            strategy.setSuperEntityClass("cn.casair.domain.AbstractAuditingEntity");
            strategy.setSuperEntityColumns("created_by", "created_date", "last_modified_by", "last_modified_date", "is_delete");
        }
        // 使用lombok模式
        strategy.setEntityLombokModel(true);
        strategy.setRestControllerStyle(true);
        strategy.setInclude(tableName);
        strategy.setControllerMappingHyphenStyle(true);
        strategy.setEntityBooleanColumnRemoveIsPrefix(false);
        if (!Strings.isNullOrEmpty(tablePrefix)) {
            strategy.setTablePrefix(tablePrefix);
        }
        generator.setStrategy(strategy);
        generator.setTemplateEngine(new VelocityTemplateEngine());
        generator.execute();


        String sourceDomainPath = projectPath + "/" + GeneratorModuleName + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" + generator.getPackageInfo().getEntity() + "/";

        String targetDomainPath = projectPath + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" + generator.getPackageInfo().getEntity() + "/";
        mvdir(new File(sourceDomainPath), new File(targetDomainPath), overwrite);

        String sourceMapperPath = projectPath + "/" + GeneratorModuleName + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" + generator.getPackageInfo().getMapper() + "/";

        String targetMapperPath = projectPath + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" + generator.getPackageInfo().getMapper() + "/";
        mvdir(new File(sourceMapperPath), new File(targetMapperPath), overwrite);

        String sourceService = projectPath + "/" + GeneratorModuleName + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" + generator.getPackageInfo().getService() + "/";

        String targetService = projectPath + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" + generator.getPackageInfo().getService() + "/";
        mvdir(new File(sourceService), new File(targetService), overwrite);

        String sourceServiceImplPath = projectPath + "/" + GeneratorModuleName + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getServiceImpl())) + "/";

        String targetServiceImplPath = projectPath + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getServiceImpl())) + "/";

        mvdir(new File(sourceServiceImplPath), new File(targetServiceImplPath), overwrite);

        String sourceWebPath = projectPath + "/" + GeneratorModuleName + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getController())) + "/";

        String targetWebPath = projectPath + "/src/main/java/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getParent())) + "/" +
                String.join("/", Splitter.on(".").splitToList(generator.getPackageInfo().getController())) + "/";
        mvdir(new File(sourceWebPath), new File(targetWebPath), overwrite);
    }

    private static void mvdir(File sourceDir, File targetDir, boolean overwrite) {
        if (sourceDir.exists()) {
            if (!targetDir.exists()) {
                targetDir.mkdirs();
            }

            String[] targetFiles = sourceDir.list();

            Arrays.stream(targetFiles).forEach(fileName -> {
                try {
                    File sourceFile = new File(sourceDir.getPath() + "/" + fileName);
                    File targetFile = new File(targetDir.getPath() + "/" + fileName);

                    if (!targetFile.isDirectory()) {
                        if (targetFile.exists()) {
                            if (overwrite) {
                                //需要重写就尝试删除旧的文件,然后拷贝新文件
                                FileUtils.deleteQuietly(targetFile);
                                FileUtils.moveFile(sourceFile, targetFile);
                            }
                        } else {
                            FileUtils.moveFile(sourceFile, targetFile);
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
            Arrays.stream(targetFiles).forEach(fileInfo -> {
                File file = new File(sourceDir + File.separator + fileInfo);
                file.delete();
            });

            sourceDir.delete();
        }
    }


    /**
     * 生成器全局配置
     *
     * @param generator
     * @return
     */
    private static String setGlobalConfig(AutoGenerator generator, IdType idType, boolean overwrite) {
        String projectPath = System.getProperty("user.dir");

        GlobalConfig gc = new GlobalConfig();
        //设置生成文件的输出目录
        gc.setOutputDir(projectPath + "/src/main/java");
        //设置开发者信息
        gc.setAuthor("InternalCodeGenerator");
        //设置是否打开输出目录
        gc.setOpen(false);
        //设置是否开启baseResultMap
        gc.setBaseResultMap(true);
        //设置是否开启baseColumnList
        gc.setBaseColumnList(true);
        //设置是否开启 ActiveRecord 模式
        gc.setActiveRecord(false);
        //设置Mapper Class命名方式
        gc.setMapperName("%sRepository");
        //设置Mapper xml命名方式
        gc.setXmlName("%sRepository");
        //设置Service Class命名方式
        gc.setServiceName("%sService");
        //设置ServiceImpl Class命名方式
        gc.setServiceImplName("%sServiceImpl");
        //设置Controller Class命名方式
        gc.setControllerName("%sResource");
        gc.setIdType(idType);
        gc.setSwagger2(true);
        gc.setFileOverride(overwrite);
        generator.setGlobalConfig(gc);

        return projectPath;
    }

    /**
     * 数据源配置
     *
     * @param generator
     */
    private static void setDataSourceConfig(AutoGenerator generator) {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(DATABASE_URL);
        dsc.setDriverName("com.mysql.jdbc.Driver");
        dsc.setUsername(DATABASE_USERNAME);
        dsc.setPassword(DATABASE_PASSWORD);
        //自定义时间类型转换
        dsc.setTypeConvert(new MySqlTypeConvert() {
            @Override
            public IColumnType processTypeConvert(GlobalConfig globalConfig, String fieldType) {
                if (fieldType.contains("timestamp")) {
                    return new IColumnType() {
                        @Override
                        public String getType() {
                            return "LocalDateTime";
                        }

                        @Override
                        public String getPkg() {
                            return "java.time.LocalDateTime";
                        }
                    };
                } else if (fieldType.contains("datetime")) {
                    return new IColumnType() {
                        @Override
                        public String getType() {
                            return "LocalDateTime";
                        }

                        @Override
                        public String getPkg() {
                            return "java.time.LocalDateTime";
                        }
                    };
                } else {
                    return super.processTypeConvert(globalConfig, fieldType);
                }
            }
        });
        generator.setDataSource(dsc);
    }

    /**
     * 包名配置
     *
     * @param generator
     * @return
     */
    private static PackageConfig setPackageConfig(AutoGenerator generator) {
        PackageConfig pc = new PackageConfig();
        //设置父包模块名
        pc.setParent("cn.casair");
        //设置Controller包名
        pc.setController("web.rest");
        //设置Service包名
        pc.setService("service");
        //设置Service Impl包名
        pc.setServiceImpl("service.impl");
        //设置Mapper Class包名
        pc.setMapper("repository");
        //设置Entity包名
        pc.setEntity("domain");
        generator.setPackageInfo(pc);
        return pc;
    }

    /**
     * 自定义配置模板
     *
     * @param generator
     */
    private static void setTemplateConfig(AutoGenerator generator, boolean generateDomain, boolean generateRepository, boolean generateService, boolean generateServiceImpl, boolean generateController) {
        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();
        // 配置自定义输出模板
        //指定自定义模板路径，注意不要带上.ftl/.vm, 会根据使用的模板引擎自动识别
        if (generateDomain) {
            templateConfig.setEntity("templates/domain.java");
        } else {
            templateConfig.setEntity(null);
        }
        //templateConfig.setXml("templates/mapper.xml");
        templateConfig.setXml(null);

        if (generateRepository) {
            templateConfig.setMapper("templates/mapper.java");
        } else {
            templateConfig.setMapper(null);
        }

        if (generateService) {
            templateConfig.setService("templates/service.java");
        } else {
            templateConfig.setService(null);
        }

        if (generateServiceImpl) {
            templateConfig.setServiceImpl("templates/serviceImpl.java");
        } else {
            templateConfig.setServiceImpl(null);
        }

        if (generateController) {
            templateConfig.setController("templates/controller.java");
        } else {
            templateConfig.setController(null);
        }
        generator.setTemplate(templateConfig);
    }

    public static String convertPlural(String word) {

        if (word == null) {
            return null;
        }

        if (word.trim().length() == 0) {
            return "";
        }

        if (word.endsWith("y")) {
            return word.substring(0, word.length() - 1) + "ies";
        }

        if (word.endsWith("o") || word.endsWith("s") || word.endsWith("x") || word.endsWith("sh") || word.endsWith("ch")) {
            return word + "es";
        }

        return word + "s";
    }
}
