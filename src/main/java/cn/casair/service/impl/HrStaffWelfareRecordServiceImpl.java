package cn.casair.service.impl;

import cn.casair.domain.HrAccumulationFund;
import cn.casair.domain.HrSocialSecurity;
import cn.casair.domain.HrStaffWelfareRecord;
import cn.casair.domain.HrWelfareCompensation;
import cn.casair.dto.HrEmployeeWelfareDTO;
import cn.casair.dto.HrStaffWelfareRecordDTO;
import cn.casair.mapper.HrStaffWelfareRecordMapper;
import cn.casair.repository.HrStaffEmolumentRepository;
import cn.casair.repository.HrStaffWelfareRecordRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.HrStaffWelfareRecordService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 员工每月福利配置服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrStaffWelfareRecordServiceImpl extends ServiceImpl<HrStaffWelfareRecordRepository, HrStaffWelfareRecord> implements HrStaffWelfareRecordService {

    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrStaffEmolumentRepository hrStaffEmolumentRepository;
    private final HrStaffWelfareRecordRepository hrStaffWelfareRecordRepository;
    private final HrStaffWelfareRecordMapper hrStaffWelfareRecordMapper;

    /**
     * 创建员工每月福利配置
     *
     * @param hrStaffWelfareRecordDTO
     * @return
     */
    @Override
    public HrStaffWelfareRecordDTO createHrStaffWelfareRecord(HrStaffWelfareRecordDTO hrStaffWelfareRecordDTO) {
        log.info("Create new HrStaffWelfareRecord:{}", hrStaffWelfareRecordDTO);

        HrStaffWelfareRecord hrStaffWelfareRecord = this.hrStaffWelfareRecordMapper.toEntity(hrStaffWelfareRecordDTO);
        this.hrStaffWelfareRecordRepository.insert(hrStaffWelfareRecord);
        return this.hrStaffWelfareRecordMapper.toDto(hrStaffWelfareRecord);
    }

    /**
     * 修改员工每月福利配置
     *
     * @param hrStaffWelfareRecordDTO
     * @return
     */
    @Override
    public Optional<HrStaffWelfareRecordDTO> updateHrStaffWelfareRecord(HrStaffWelfareRecordDTO hrStaffWelfareRecordDTO) {
        return Optional.ofNullable(this.hrStaffWelfareRecordRepository.selectById(hrStaffWelfareRecordDTO.getId()))
                .map(roleTemp -> {
                    HrStaffWelfareRecord hrStaffWelfareRecord = this.hrStaffWelfareRecordMapper.toEntity(hrStaffWelfareRecordDTO);
                    this.hrStaffWelfareRecordRepository.updateById(hrStaffWelfareRecord);
                    log.info("Update HrStaffWelfareRecord:{}", hrStaffWelfareRecordDTO);
                    return hrStaffWelfareRecordDTO;
                });
    }

    /**
     * 查询员工每月福利配置详情
     *
     * @param id
     * @return
     */
    @Override
    public HrStaffWelfareRecordDTO getHrStaffWelfareRecord(String id) {
        log.info("Get HrStaffWelfareRecord :{}", id);

        HrStaffWelfareRecord hrStaffWelfareRecord = this.hrStaffWelfareRecordRepository.selectById(id);
        return this.hrStaffWelfareRecordMapper.toDto(hrStaffWelfareRecord);
    }

    /**
     * 删除员工每月福利配置
     *
     * @param id
     */
    @Override
    public void deleteHrStaffWelfareRecord(String id) {
        Optional.ofNullable(this.hrStaffWelfareRecordRepository.selectById(id))
                .ifPresent(hrStaffWelfareRecord -> {
                    this.hrStaffWelfareRecordRepository.deleteById(id);
                    log.info("Delete HrStaffWelfareRecord:{}", hrStaffWelfareRecord);
                });
    }

    /**
     * 批量删除员工每月福利配置
     *
     * @param ids
     */
    @Override
    public void deleteHrStaffWelfareRecord(List<String> ids) {
        log.info("Delete HrStaffWelfareRecords:{}", ids);
        this.hrStaffWelfareRecordRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询员工每月福利配置
     *
     * @param hrStaffWelfareRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrStaffWelfareRecordDTO hrStaffWelfareRecordDTO, Long pageNumber, Long pageSize) {
        Page<HrStaffWelfareRecord> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrStaffWelfareRecord> qw = new QueryWrapper<>(this.hrStaffWelfareRecordMapper.toEntity(hrStaffWelfareRecordDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrStaffWelfareRecordRepository.selectPage(page, qw);
        iPage.setRecords(this.hrStaffWelfareRecordMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    @Override
    public void checkAndInsertStaffWelfareRecord() {
        int payYear = LocalDate.now().getYear();
        int payMonth = LocalDate.now().getMonthValue();

        this.dealStaffWelfareRecord(payYear, payMonth, null);
    }

    /**
     * 处理员工每月福利参数配置
     *
     * @param payYear
     * @param payMonth
     * @param staffId
     * @return void
     * <AUTHOR>
     * @date 2021/12/6
     **/
    private void dealStaffWelfareRecord(int payYear, int payMonth, String staffId) {
        List<HrEmployeeWelfareDTO> hrEmployeeWelfareList = this.hrStaffEmolumentRepository.selectStaffWelfare(staffId);
        if (!hrEmployeeWelfareList.isEmpty()) {
            for (HrEmployeeWelfareDTO ls : hrEmployeeWelfareList) {
                Map<String, Object> query = new HashMap<>();
                query.put("staffId", ls.getId());
                query.put("payYear", payYear);
                query.put("payMonthly", payMonth);
                HrStaffWelfareRecord hrStaffWelfareRecord = this.hrStaffWelfareRecordRepository.selectByObject(query);
                // 配置信息已经存在 更新
                if (hrStaffWelfareRecord != null) {
//                    hrStaffWelfareRecord.setSocialSecurityCardinalBase(ls.getSocialSecurityCardinal());
                    hrStaffWelfareRecord.setUnitPensionCardinalBase(ls.getUnitPensionCardinal());
                    hrStaffWelfareRecord.setUnitUnemploymentCardinalBase(ls.getUnitUnemploymentCardinal());
                    hrStaffWelfareRecord.setWorkInjuryCardinalBase(ls.getWorkInjuryCardinal());
                    hrStaffWelfareRecord.setUnitMaternityCardinalBase(ls.getUnitMaternityCardinal());
                    hrStaffWelfareRecord.setMedicalInsuranceCardinalBase(ls.getMedicalInsuranceCardinal());
                    hrStaffWelfareRecord.setAccumulationFundCardinalBase(ls.getAccumulationFundCardinal());
                    hrStaffWelfareRecord.setPersonalPensionCardinalBase(ls.getPersonalPensionCardinal());
                    hrStaffWelfareRecord.setPersonalUnemploymentCardinalBase(ls.getPersonalUnemploymentCardinal());
                    hrStaffWelfareRecord.setMedicalInsuranceCardinalBasePersonal(ls.getMedicalInsuranceCardinalPersonal());
                    hrStaffWelfareRecord.setUnitLargeMedicalExpenseBase(ls.getUnitLargeMedicalExpense());
                    hrStaffWelfareRecord.setReplenishWorkInjuryExpenseBase(ls.getReplenishWorkInjuryExpense());
                    hrStaffWelfareRecord.setPersonalLargeMedicalExpenseBase(ls.getPersonalLargeMedicalExpense());
                    hrStaffWelfareRecord.setPersonalMaternityCardinalBase(ls.getPersonalMaternityCardinal());
                    hrStaffWelfareRecord.setPersonalMaternityScale(ls.getPersonalMaternity());
                    hrStaffWelfareRecord.setUnitPensionScale(ls.getUnitPension());
                    hrStaffWelfareRecord.setUnitMedicalScale(ls.getUnitMedical());
                    hrStaffWelfareRecord.setWorkInjuryScale(ls.getWorkInjury());
                    hrStaffWelfareRecord.setUnitUnemploymentScale(ls.getUnitUnemployment());
                    hrStaffWelfareRecord.setUnitMaternityScale(ls.getUnitMaternity());
                    hrStaffWelfareRecord.setPersonalPensionScale(ls.getPersonalPension());
                    hrStaffWelfareRecord.setPersonalMedicalScale(ls.getPersonalMedical());
                    hrStaffWelfareRecord.setPersonalUnemploymentScale(ls.getPersonalUnemployment());
                    hrStaffWelfareRecord.setUnitAccumulationFundScale(ls.getUnitScale());
                    hrStaffWelfareRecord.setPersonalAccumulationFundScale(ls.getPersonageScale());
                    this.hrStaffWelfareRecordRepository.updateById(hrStaffWelfareRecord);
                } else {
                    // 获取员工福利配置
                    HrEmployeeWelfareDTO hrEmployeeWelfare = this.hrTalentStaffRepository.findEmployeeWelfareByStaffId(ls.getId());
                    hrStaffWelfareRecord = new HrStaffWelfareRecord()
                        .setStaffId(ls.getId())
                        .setPayYear(payYear)
                        .setPayMonthly(payMonth)
//                        .setSocialSecurityCardinalBase(hrEmployeeWelfare.getSocialSecurityCardinal())
                        .setUnitPensionCardinalBase(hrEmployeeWelfare.getUnitPensionCardinal())
                        .setUnitUnemploymentCardinalBase(hrEmployeeWelfare.getUnitUnemploymentCardinal())
                        .setWorkInjuryCardinalBase(hrEmployeeWelfare.getWorkInjuryCardinal())
                        .setUnitMaternityCardinalBase(hrEmployeeWelfare.getUnitMaternityCardinal())
                        .setMedicalInsuranceCardinalBase(hrEmployeeWelfare.getMedicalInsuranceCardinal())
                        .setAccumulationFundCardinalBase(hrEmployeeWelfare.getAccumulationFundCardinal())
                        .setPersonalPensionCardinalBase(hrEmployeeWelfare.getPersonalPensionCardinal())
                        .setPersonalUnemploymentCardinalBase(hrEmployeeWelfare.getPersonalUnemploymentCardinal())
                        .setMedicalInsuranceCardinalBasePersonal(hrEmployeeWelfare.getMedicalInsuranceCardinalPersonal())
                        .setUnitLargeMedicalExpenseBase(hrEmployeeWelfare.getUnitLargeMedicalExpense())
                        .setReplenishWorkInjuryExpenseBase(hrEmployeeWelfare.getReplenishWorkInjuryExpense())
                        .setPersonalLargeMedicalExpenseBase(hrEmployeeWelfare.getPersonalLargeMedicalExpense())
                        .setPersonalMaternityCardinalBase(hrEmployeeWelfare.getPersonalMaternityCardinal())
                        .setPersonalMaternityScale(hrEmployeeWelfare.getPersonalMaternity())
                        .setUnitPensionScale(hrEmployeeWelfare.getUnitPension())
                        .setUnitMedicalScale(hrEmployeeWelfare.getUnitMedical())
                        .setWorkInjuryScale(hrEmployeeWelfare.getWorkInjury())
                        .setUnitUnemploymentScale(hrEmployeeWelfare.getUnitUnemployment())
                        .setUnitMaternityScale(hrEmployeeWelfare.getUnitMaternity())
                        .setPersonalPensionScale(hrEmployeeWelfare.getPersonalPension())
                        .setPersonalMedicalScale(hrEmployeeWelfare.getPersonalMedical())
                        .setPersonalUnemploymentScale(hrEmployeeWelfare.getPersonalUnemployment())
                        .setUnitAccumulationFundScale(hrEmployeeWelfare.getUnitScale())
                        .setPersonalAccumulationFundScale(hrEmployeeWelfare.getPersonageScale());
                    this.hrStaffWelfareRecordRepository.insert(hrStaffWelfareRecord);
                }
            }
        }
    }

    @Override
    public HrStaffWelfareRecord selectByObject(Map<String, Object> query) {
        return this.hrStaffWelfareRecordRepository.selectByObject(query);
    }

    @Override
    public void insertOrUpdateStaffWelfareRecord(HrStaffWelfareRecord hrStaffWelfareRecord, HrEmployeeWelfareDTO hrEmployeeWelfareNew, int payYear, int payMonthly) {
        HrStaffWelfareRecord hrStaffWelfareRecordNew = new HrStaffWelfareRecord();
        hrStaffWelfareRecordNew.setStaffId(hrEmployeeWelfareNew.getId());
        hrStaffWelfareRecordNew.setPayYear(payYear);
        hrStaffWelfareRecordNew.setPayMonthly(payMonthly);
//        hrStaffWelfareRecordNew.setSocialSecurityCardinalBase(hrEmployeeWelfareNew.getSocialSecurityCardinal());
        hrStaffWelfareRecordNew.setUnitPensionCardinalBase(hrEmployeeWelfareNew.getUnitPensionCardinal());
        hrStaffWelfareRecordNew.setUnitUnemploymentCardinalBase(hrEmployeeWelfareNew.getUnitUnemploymentCardinal());
        hrStaffWelfareRecordNew.setWorkInjuryCardinalBase(hrEmployeeWelfareNew.getWorkInjuryCardinal());
        hrStaffWelfareRecordNew.setUnitMaternityCardinalBase(hrEmployeeWelfareNew.getUnitMaternityCardinal());
        hrStaffWelfareRecordNew.setMedicalInsuranceCardinalBase(hrEmployeeWelfareNew.getMedicalInsuranceCardinal());
        hrStaffWelfareRecordNew.setAccumulationFundCardinalBase(hrEmployeeWelfareNew.getAccumulationFundCardinal());
        hrStaffWelfareRecordNew.setPersonalPensionCardinalBase(hrEmployeeWelfareNew.getPersonalPensionCardinal());
        hrStaffWelfareRecordNew.setPersonalUnemploymentCardinalBase(hrEmployeeWelfareNew.getPersonalUnemploymentCardinal());
        hrStaffWelfareRecordNew.setMedicalInsuranceCardinalBasePersonal(hrEmployeeWelfareNew.getMedicalInsuranceCardinalPersonal());
        hrStaffWelfareRecordNew.setUnitLargeMedicalExpenseBase(hrEmployeeWelfareNew.getUnitLargeMedicalExpense());
        hrStaffWelfareRecordNew.setReplenishWorkInjuryExpenseBase(hrEmployeeWelfareNew.getReplenishWorkInjuryExpense());
        hrStaffWelfareRecordNew.setPersonalLargeMedicalExpenseBase(hrEmployeeWelfareNew.getPersonalLargeMedicalExpense());

        hrStaffWelfareRecordNew.setPersonalMaternityCardinalBase(hrEmployeeWelfareNew.getPersonalMaternityCardinal());
        hrStaffWelfareRecordNew.setPersonalMaternityScale(hrEmployeeWelfareNew.getPersonalMaternity());

        hrStaffWelfareRecordNew.setUnitPensionScale(hrEmployeeWelfareNew.getUnitPension());
        hrStaffWelfareRecordNew.setUnitMedicalScale(hrEmployeeWelfareNew.getUnitMedical());
        hrStaffWelfareRecordNew.setWorkInjuryScale(hrEmployeeWelfareNew.getWorkInjury());
        hrStaffWelfareRecordNew.setUnitUnemploymentScale(hrEmployeeWelfareNew.getUnitUnemployment());
        hrStaffWelfareRecordNew.setUnitMaternityScale(hrEmployeeWelfareNew.getUnitMaternity());
        hrStaffWelfareRecordNew.setPersonalPensionScale(hrEmployeeWelfareNew.getPersonalPension());
        hrStaffWelfareRecordNew.setPersonalUnemploymentScale(hrEmployeeWelfareNew.getPersonalUnemployment());
        hrStaffWelfareRecordNew.setUnitAccumulationFundScale(hrEmployeeWelfareNew.getUnitScale());
        hrStaffWelfareRecordNew.setPersonalAccumulationFundScale(hrEmployeeWelfareNew.getPersonageScale());

        if (hrStaffWelfareRecord == null) {
            this.hrStaffWelfareRecordRepository.insert(hrStaffWelfareRecordNew);
        } else {
            hrStaffWelfareRecordNew.setId(hrStaffWelfareRecord.getId());
            this.hrStaffWelfareRecordRepository.updateById(hrStaffWelfareRecordNew);
        }
    }

    @Override
    public void insertOrUpdateStaffWelfareRecord(HrEmployeeWelfareDTO employeeWelfareOld, HrStaffWelfareRecord hrStaffWelfareRecord, HrSocialSecurity hrSocialSecurity, int payYear, int payMonthly) {
        HrStaffWelfareRecord hrStaffWelfareRecordNew = new HrStaffWelfareRecord();
        hrStaffWelfareRecordNew.setStaffId(employeeWelfareOld.getId());
        hrStaffWelfareRecordNew.setPayYear(payYear);
        hrStaffWelfareRecordNew.setPayMonthly(payMonthly);
//        hrStaffWelfareRecordNew.setSocialSecurityCardinalBase(employeeWelfareOld.getSocialSecurityCardinal());
        hrStaffWelfareRecordNew.setUnitPensionCardinalBase(employeeWelfareOld.getUnitPensionCardinal());
        hrStaffWelfareRecordNew.setUnitUnemploymentCardinalBase(employeeWelfareOld.getUnitUnemploymentCardinal());
        hrStaffWelfareRecordNew.setWorkInjuryCardinalBase(employeeWelfareOld.getWorkInjuryCardinal());
        hrStaffWelfareRecordNew.setUnitMaternityCardinalBase(employeeWelfareOld.getUnitMaternityCardinal());
        hrStaffWelfareRecordNew.setMedicalInsuranceCardinalBase(employeeWelfareOld.getMedicalInsuranceCardinal());

        hrStaffWelfareRecordNew.setAccumulationFundCardinalBase(employeeWelfareOld.getAccumulationFundCardinal());

        hrStaffWelfareRecordNew.setPersonalPensionCardinalBase(employeeWelfareOld.getPersonalPensionCardinal());
        hrStaffWelfareRecordNew.setPersonalUnemploymentCardinalBase(employeeWelfareOld.getPersonalUnemploymentCardinal());
        hrStaffWelfareRecordNew.setMedicalInsuranceCardinalBasePersonal(employeeWelfareOld.getMedicalInsuranceCardinalPersonal());

        hrStaffWelfareRecordNew.setUnitLargeMedicalExpenseBase(employeeWelfareOld.getUnitLargeMedicalExpense());
        hrStaffWelfareRecordNew.setReplenishWorkInjuryExpenseBase(employeeWelfareOld.getReplenishWorkInjuryExpense());
        hrStaffWelfareRecordNew.setPersonalLargeMedicalExpenseBase(employeeWelfareOld.getPersonalLargeMedicalExpense());

        hrStaffWelfareRecordNew.setPersonalMaternityCardinalBase(employeeWelfareOld.getPersonalMaternityCardinal());
        hrStaffWelfareRecordNew.setPersonalMaternityScale(employeeWelfareOld.getPersonalMaternity());

        hrStaffWelfareRecordNew.setUnitPensionScale(hrSocialSecurity.getUnitPension());
        hrStaffWelfareRecordNew.setUnitMedicalScale(hrSocialSecurity.getUnitMedical());
        hrStaffWelfareRecordNew.setWorkInjuryScale(hrSocialSecurity.getWorkInjury());
        hrStaffWelfareRecordNew.setUnitUnemploymentScale(hrSocialSecurity.getUnitUnemployment());
        hrStaffWelfareRecordNew.setUnitMaternityScale(hrSocialSecurity.getUnitMaternity());
        hrStaffWelfareRecordNew.setPersonalPensionScale(hrSocialSecurity.getPersonalPension());
        hrStaffWelfareRecordNew.setPersonalUnemploymentScale(hrSocialSecurity.getPersonalUnemployment());
        hrStaffWelfareRecordNew.setUnitAccumulationFundScale(employeeWelfareOld.getUnitScale());
        hrStaffWelfareRecordNew.setPersonalAccumulationFundScale(employeeWelfareOld.getPersonageScale());

        if (hrStaffWelfareRecord == null) {
            this.hrStaffWelfareRecordRepository.insert(hrStaffWelfareRecordNew);
        } else {
            hrStaffWelfareRecordNew.setId(hrStaffWelfareRecord.getId());
            this.hrStaffWelfareRecordRepository.updateById(hrStaffWelfareRecordNew);
        }
    }

    @Override
    public void insertOrUpdateStaffWelfareRecord(HrEmployeeWelfareDTO employeeWelfareOld, HrStaffWelfareRecord hrStaffWelfareRecord, HrAccumulationFund hrAccumulationFund, int payYear, int payMonthly) {
        HrStaffWelfareRecord hrStaffWelfareRecordNew = new HrStaffWelfareRecord();
        hrStaffWelfareRecordNew.setStaffId(employeeWelfareOld.getId());
        hrStaffWelfareRecordNew.setPayYear(payYear);
        hrStaffWelfareRecordNew.setPayMonthly(payMonthly);
//        hrStaffWelfareRecordNew.setSocialSecurityCardinalBase(employeeWelfareOld.getSocialSecurityCardinal());
        hrStaffWelfareRecordNew.setUnitPensionCardinalBase(employeeWelfareOld.getUnitPensionCardinal());
        hrStaffWelfareRecordNew.setUnitUnemploymentCardinalBase(employeeWelfareOld.getUnitUnemploymentCardinal());
        hrStaffWelfareRecordNew.setWorkInjuryCardinalBase(employeeWelfareOld.getWorkInjuryCardinal());
        hrStaffWelfareRecordNew.setUnitMaternityCardinalBase(employeeWelfareOld.getUnitMaternityCardinal());
        hrStaffWelfareRecordNew.setMedicalInsuranceCardinalBase(employeeWelfareOld.getMedicalInsuranceCardinal());

        hrStaffWelfareRecordNew.setAccumulationFundCardinalBase(employeeWelfareOld.getAccumulationFundCardinal());

        hrStaffWelfareRecordNew.setPersonalPensionCardinalBase(employeeWelfareOld.getPersonalPensionCardinal());
        hrStaffWelfareRecordNew.setPersonalUnemploymentCardinalBase(employeeWelfareOld.getPersonalUnemploymentCardinal());
        hrStaffWelfareRecordNew.setMedicalInsuranceCardinalBasePersonal(employeeWelfareOld.getMedicalInsuranceCardinalPersonal());
        hrStaffWelfareRecordNew.setUnitLargeMedicalExpenseBase(employeeWelfareOld.getUnitLargeMedicalExpense());
        hrStaffWelfareRecordNew.setReplenishWorkInjuryExpenseBase(employeeWelfareOld.getReplenishWorkInjuryExpense());
        hrStaffWelfareRecordNew.setPersonalLargeMedicalExpenseBase(employeeWelfareOld.getPersonalLargeMedicalExpense());

        hrStaffWelfareRecordNew.setPersonalMaternityCardinalBase(employeeWelfareOld.getPersonalMaternityCardinal());
        hrStaffWelfareRecordNew.setPersonalMaternityScale(employeeWelfareOld.getPersonalMaternity());

        hrStaffWelfareRecordNew.setUnitPensionScale(employeeWelfareOld.getUnitPension());
        hrStaffWelfareRecordNew.setUnitMedicalScale(employeeWelfareOld.getUnitMedical());
        hrStaffWelfareRecordNew.setWorkInjuryScale(employeeWelfareOld.getWorkInjury());
        hrStaffWelfareRecordNew.setUnitUnemploymentScale(employeeWelfareOld.getUnitUnemployment());
        hrStaffWelfareRecordNew.setUnitMaternityScale(employeeWelfareOld.getUnitMaternity());
        hrStaffWelfareRecordNew.setPersonalPensionScale(employeeWelfareOld.getPersonalPension());
        hrStaffWelfareRecordNew.setPersonalUnemploymentScale(employeeWelfareOld.getPersonalUnemployment());
        hrStaffWelfareRecordNew.setUnitAccumulationFundScale(hrAccumulationFund.getUnitScale());
        hrStaffWelfareRecordNew.setPersonalAccumulationFundScale(hrAccumulationFund.getPersonageScale());

        if (hrStaffWelfareRecord == null) {
            this.hrStaffWelfareRecordRepository.insert(hrStaffWelfareRecordNew);
        } else {
            hrStaffWelfareRecordNew.setId(hrStaffWelfareRecord.getId());
            this.hrStaffWelfareRecordRepository.updateById(hrStaffWelfareRecordNew);
        }
    }

    @Override
    public void insertOrUpdateStaffWelfareRecord(List<HrWelfareCompensation> hrWelfareCompensationList) {
        hrWelfareCompensationList.forEach(ls -> this.dealStaffWelfareRecord(ls.getPayYear(), ls.getPayMonthly(), ls.getStaffId()));
    }
}
