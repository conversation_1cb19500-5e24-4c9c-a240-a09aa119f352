<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrMiniUserRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, open_id, mp_open_id, union_id, nick_name, gender, `language`, city, province, country, avatar_url, phone,
        is_delete, created_by, created_date, last_modified_by ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrMiniUser">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="open_id" property="openId"/>
        <result column="mp_open_id" property="mpOpenId"/>
        <result column="union_id" property="unionId"/>
        <result column="nick_name" property="nickName"/>
        <result column="gender" property="gender"/>
        <result column="language" property="language"/>
        <result column="city" property="city"/>
        <result column="province" property="province"/>
        <result column="country" property="country"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="phone" property="phone"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectByOpenId" resultType="cn.casair.domain.HrMiniUser">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_mini_user
        WHERE is_delete = 0
          AND open_id = #{openId}
    </select>

    <select id="selectByUserId" resultType="cn.casair.domain.HrMiniUser">

    </select>

</mapper>
