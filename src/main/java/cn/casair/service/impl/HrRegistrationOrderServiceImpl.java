package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.domain.HrRegistrationOrder;
import cn.casair.dto.HrRegistrationOrderDTO;
import cn.casair.mapper.HrRegistrationOrderMapper;
import cn.casair.repository.HrRegistrationOrderRepository;
import cn.casair.service.HrRegistrationOrderService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;

/**
 * 报名订单表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrRegistrationOrderServiceImpl extends ServiceImpl<HrRegistrationOrderRepository, HrRegistrationOrder> implements HrRegistrationOrderService {


    private final HrRegistrationOrderRepository hrRegistrationOrderRepository;
    private final HrRegistrationOrderMapper hrRegistrationOrderMapper;
    private final SysOperLogService sysOperLogService;
    @Autowired
    private WxPayService wxService;

    @Value("${wx.pay.notifyUrl}")
    private String notifyUrl;

    public HrRegistrationOrderServiceImpl(HrRegistrationOrderRepository hrRegistrationOrderRepository, HrRegistrationOrderMapper hrRegistrationOrderMapper, SysOperLogService sysOperLogService) {
        this.hrRegistrationOrderRepository = hrRegistrationOrderRepository;
        this.hrRegistrationOrderMapper = hrRegistrationOrderMapper;
        this.sysOperLogService = sysOperLogService;
    }

    /**
     * 创建报名订单表
     *
     * @param hrRegistrationOrderDTO
     * @return
     */
    @Override
    public HrRegistrationOrderDTO createHrRegistrationOrder(HrRegistrationOrderDTO hrRegistrationOrderDTO) {
        log.info("Create new HrRegistrationOrder:{}", hrRegistrationOrderDTO);
        //hrRegistrationOrderDTO.setPayTime(LocalDateTime.now());
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddkkmmss");
        String format = sdf.format(d);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        int number = (int) ((Math.random() * 9 + 1) * 100000);
        hrRegistrationOrderDTO.setOutTradeNo("H" + format + number);
        HrRegistrationOrder hrRegistrationOrder = this.hrRegistrationOrderMapper.toEntity(hrRegistrationOrderDTO);
        this.hrRegistrationOrderRepository.insert(hrRegistrationOrder);
        HrRegistrationOrderDTO hrRegistrationOrderDTO1 = this.hrRegistrationOrderMapper.toDto(hrRegistrationOrder);
        WxPayMpOrderResult result = generateOrder(hrRegistrationOrderDTO);
        //获取时间戳
        long timeMillis = System.currentTimeMillis();
        hrRegistrationOrderDTO1.setTimeStamp(timeMillis);
        hrRegistrationOrderDTO1.setWxPayUnifiedOrderResult(result);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.REGISTRATION_ORDER.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrRegistrationOrderDTO),
            HrRegistrationOrderDTO.class,
            null,
            JSON.toJSONString(hrRegistrationOrderDTO)
        );
        return hrRegistrationOrderDTO1;
    }

    /**
     * 修改报名订单表
     *
     * @param hrRegistrationOrderDTO
     * @return
     */
    @Override
    public Optional<HrRegistrationOrderDTO> updateHrRegistrationOrder(HrRegistrationOrderDTO hrRegistrationOrderDTO) {
        return Optional.ofNullable(this.hrRegistrationOrderRepository.selectById(hrRegistrationOrderDTO.getId()))
            .map(roleTemp -> {
                HrRegistrationOrder hrRegistrationOrder = this.hrRegistrationOrderMapper.toEntity(hrRegistrationOrderDTO);
                this.hrRegistrationOrderRepository.updateById(hrRegistrationOrder);
                log.info("Update HrRegistrationOrder:{}", hrRegistrationOrderDTO);
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.REGISTRATION_ORDER.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrRegistrationOrderDTO),
                    HrRegistrationOrderDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrRegistrationOrderDTO),
                    null,
                    HrRegistrationOrderDTO.class
                );
                return hrRegistrationOrderDTO;
            });
    }

    /**
     * 查询报名订单表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrRegistrationOrderDTO getHrRegistrationOrder(String id) {
        log.info("Get HrRegistrationOrder :{}", id);

        HrRegistrationOrder hrRegistrationOrder = this.hrRegistrationOrderRepository.selectById(id);
        return this.hrRegistrationOrderMapper.toDto(hrRegistrationOrder);
    }

    /**
     * 删除报名订单表
     *
     * @param id
     */
    @Override
    public void deleteHrRegistrationOrder(String id) {
        Optional.ofNullable(this.hrRegistrationOrderRepository.selectById(id))
            .ifPresent(hrRegistrationOrder -> {
                this.hrRegistrationOrderRepository.deleteById(id);
                log.info("Delete HrRegistrationOrder:{}", hrRegistrationOrder);
            });
    }

    /**
     * 批量删除报名订单表
     *
     * @param ids
     */
    @Override
    public void deleteHrRegistrationOrder(List<String> ids) {
        log.info("Delete HrRegistrationOrders:{}", ids);
        this.hrRegistrationOrderRepository.deleteBatchIds(ids);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.REGISTRATION_ORDER.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
    }

    /**
     * 分页查询报名订单表
     *
     * @param hrRegistrationOrderDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrRegistrationOrderDTO hrRegistrationOrderDTO, Long pageNumber, Long pageSize) {
        Page<HrRegistrationOrder> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrRegistrationOrder> qw = new QueryWrapper<>(this.hrRegistrationOrderMapper.toEntity(hrRegistrationOrderDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrRegistrationOrderRepository.selectPage(page, qw);
        iPage.setRecords(this.hrRegistrationOrderMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    /**
     * 远程调用微信接口生成订单
     *
     * @return
     */
    @Override
    public WxPayMpOrderResult generateOrder(HrRegistrationOrderDTO hrRegistrationOrderDTO) {
        //调接口传参
        BigDecimal payMoney = hrRegistrationOrderDTO.getPayMoney().multiply(new BigDecimal("100"));
        WxPayUnifiedOrderRequest request = WxPayUnifiedOrderRequest.newBuilder().body("hr").outTradeNo(hrRegistrationOrderDTO.getOutTradeNo())
            .totalFee((int) (payMoney.intValue()))
            .notifyUrl(notifyUrl).spbillCreateIp("*************").openid(hrRegistrationOrderDTO.getOpenId()).tradeType("JSAPI").build();
        try {
//            WxPayUnifiedOrderResult result = this.wxService.unifiedOrder(request);
            WxPayMpOrderResult result = this.wxService.createOrder(request);
            return result;
        } catch (WxPayException e) {
            e.printStackTrace();
            throw new CommonException("微信付款失败，请重试");
        }
    }
}
