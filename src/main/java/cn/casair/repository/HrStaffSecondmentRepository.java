package cn.casair.repository;

import cn.casair.domain.HrStaffSecondment;
import cn.casair.dto.HrStaffSecondmentDTO;
import cn.casair.dto.HrTalentStaffDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 员工借调服务数据库操作类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Repository
public interface HrStaffSecondmentRepository extends BaseMapper<HrStaffSecondment> {

    IPage<HrStaffSecondmentDTO> findPage(Page<HrStaffSecondment> page, @Param("param") HrStaffSecondmentDTO hrStaffSecondmentDTO, @Param("permissionClient")List<String> clientIds);

    List<HrStaffSecondmentDTO> findList(@Param("param") HrStaffSecondmentDTO hrStaffSecondmentDTO, @Param("permissionClient") List<String> clientIds);

    List<HrStaffSecondmentDTO> findBatch(@Param("ids") List<String> ids);

    void updateStepAndStates(@Param("param") HrStaffSecondmentDTO hrStaffSecondmentDTO);

    List<HrTalentStaffDTO> findStaff();

    List<HrStaffSecondmentDTO> findSecondmentList(@Param("staffId") String id);

    List<HrStaffSecondment> findByApply(@Param("param") HrStaffSecondmentDTO hrStaffSecondmentDTO, @Param("permissionClient") List<String> clientId);

}
