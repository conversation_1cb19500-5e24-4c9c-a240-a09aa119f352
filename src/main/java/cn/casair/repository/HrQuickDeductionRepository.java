package cn.casair.repository;
import cn.casair.domain.HrQuickDeduction;
import cn.casair.dto.excel.HrQuickDeductionExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 速算扣除数表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface HrQuickDeductionRepository extends BaseMapper<HrQuickDeduction> {

    List<HrQuickDeductionExport> selectBatch(@Param("ids")List<String> ids);

    /**
     * 获取速算扣除数列表
     *
     * @return java.util.List<cn.casair.domain.HrQuickDeduction>
     * <AUTHOR>
     * @date 2021/11/7
     **/
    List<HrQuickDeduction> selectAll();
}
