package cn.casair.service;

import cn.casair.domain.SysOperLog;
import cn.casair.dto.JWTUserDTO;
import cn.casair.dto.SysOperLogDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 操作日志记录服务类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
public interface SysOperLogService extends IService<SysOperLog> {

    /**
     * 操作日志
     * <p>下载</p>
     *
     * @param title
     * @param businessType
     * @param operDetail
     * @param requestParam
     * @param fileUrl
     * @return void
     * <AUTHOR>
     * @date 2022/1/4
     **/
    void insertDownloadSysOper(String title, Integer businessType, String operDetail, String requestParam, String fileUrl);

    /**
     * 操作日志
     * <p>手动处理操作明细</p>
     *
     * @param title            操作内容
     * @param businessType     操作类型
     * @param operDetail       操作明细
     * @param requestParam     请求参数
     * @param fileUrl          文件下载地址
     * @param beforeUpdateData 更新前数据
     * @param jsonResult       返回参数
     * @param user             用户登录信息
     * @return void
     * <AUTHOR>
     * @date 2021/12/18
     **/
    void insertSysOper(String title, Integer businessType, String operDetail, String requestParam, String fileUrl, String beforeUpdateData, String jsonResult, Object user);

    /**
     * 操作日志
     * <p>导入</p>
     * @param title
     * @param businessType
     * @param file
     * @param requestParam
     * <AUTHOR>
     * @return void
     * @date 2021/12/17
     **/
    void insertSysOperLog(String title, Integer businessType, MultipartFile file, String requestParam, Class<?> requestClazz);

    /**
     * 操作日志
     * <p>导入-异步</p>
     * @param title
     * @param businessType
     * @param fileUrl
     * @param requestParam
     * <AUTHOR>
     * @return void
     * @date 2021/12/17
     **/
    void insertSysOperLog(String title, Integer businessType, String fileUrl, String requestParam, Class<?> requestClazz, JWTUserDTO user);

    /**
     * 导出日志
     *
     * @param title
     * @param businessType
     * @param requestParam
     * @param fileUrl
     */
    void insertExportSysOperLog(String title, Integer businessType, String requestParam, Integer resultSize, String fileUrl);

    /**
     * 操作日志
     * <p>删除</p>
     *
     * @param title
     * @param businessType
     * @param requestParam
     * @return void
     * <AUTHOR>
     * @date 2021/12/4
     **/
    void insertSysOperLog(String title, Integer businessType, String requestParam);

    /**
     * 新增操作日志
     *
     * @param title 操作内容
     * @param businessType 操作类型 使用枚举类
     * @param requestParam 请求参数
     * @param requestClazz 请求参数class
     * @param fileUrl 文件下载地址（多文件英文逗号拼接）
     * @param jsonResult 返回参数
     * @return void
     * <AUTHOR>
     * @date 2021/12/4
     **/
    void insertSysOperLog(String title, Integer businessType, String requestParam, Class<?> requestClazz, String fileUrl, String jsonResult);

    /**
     * 添加系统日志
     * <p>
     * 若参数为空 设置为null即可
     * </p>
     * 只记录成功日志
     *
     * @param title            操作内容
     * @param businessType     操作类型 使用枚举类
     * @param requestParam     请求参数
     * @param requestClazz     请求参数class
     * @param fileUrl          文件下载地址（多文件英文逗号拼接）
     * @param beforeUpdateData 更新前数据
     * @param afterUpdateData  更新后数据
     * @param jsonResult       返回参数
     * @param operClazz        操作数据class
     * @return void
     * <AUTHOR>
     * @date 2021/11/8
     **/
    void insertSysOperLog(String title, Integer businessType, String requestParam, Class<?> requestClazz, String fileUrl, String beforeUpdateData, String afterUpdateData, String jsonResult, Class<?> operClazz);

    /**
     * 创建系统操作日志
     *
     * @param sysOperLog
     * @return void
     * <AUTHOR>
     * @date 2021/8/27
     **/
    void addSysOperLog(SysOperLog sysOperLog);

    /**
     * 分页查询操作日志记录
     *
     * @param sysOperLogDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<SysOperLogDTO> findPage(SysOperLogDTO sysOperLogDTO, Long pageNumber, Long pageSize);

    /**
     * 系统操作日志导出
     *
     * @param ids 导出日志id
     * @return void
     * <AUTHOR>
     * @date 2021/8/31
     **/
    void exportSysOperLog(List<String> ids, HttpServletResponse response);

    /**
     * 获取查询下拉列表
     *
     * @return org.springframework.http.ResponseEntity<?>
     * <AUTHOR>
     * @date 2021/11/17
     **/
    List<String> getTitleDropDownList();

    void insertDownloadLog(SysOperLogDTO sysOperLogDTO);
}
