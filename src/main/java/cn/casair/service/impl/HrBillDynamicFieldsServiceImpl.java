package cn.casair.service.impl;

import cn.casair.common.enums.BillEnum;
import cn.casair.common.enums.DynamicFeeTypesEnum;
import cn.casair.common.enums.SpecialBillClient;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.BillParseUtils;
import cn.casair.common.utils.EnumUtils;
import cn.casair.common.utils.FileUtil;
import cn.casair.common.utils.StringUtils;
import cn.casair.common.utils.excel.CellItem;
import cn.casair.common.utils.excel.ExcelReadUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.billl.BillFieldInfoDTO;
import cn.casair.dto.billl.BillTableHeaderDTO;
import cn.casair.dto.billl.DynamicFeeFieldDTO;
import cn.casair.mapper.HrBillCompareConfigMapper;
import cn.casair.mapper.HrBillDynamicFieldsMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 薪酬账单动态字段服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class HrBillDynamicFieldsServiceImpl extends ServiceImpl<HrBillDynamicFieldsRepository, HrBillDynamicFields> implements HrBillDynamicFieldsService {


    private final HrBillDynamicFieldsRepository hrBillDynamicFieldsRepository;
    private final HrBillDynamicFieldsMapper hrBillDynamicFieldsMapper;
    private final HrExpenseManageService hrExpenseManageService;
    private final HrAppendixService hrAppendixService;
    private final HrBillDetailItemsRepository hrBillDetailItemsRepository;
    private final HrBillDetailRepository hrBillDetailRepository;
    private final HrOriginalSalaryRepository hrOriginalSalaryRepository;
    private final HrBillCompareConfigService hrBillCompareConfigService;
    private final HrBillCompareConfigRepository hrBillCompareConfigRepository;
    private final HrBillTotalRepository hrBillTotalRepository;
    private final HrBillRepository hrBillRepository;
    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final HrExpenseManageRepository hrExpenseManageRepository;
    private final HrBillCompareResultRepository hrBillCompareResultRepository;
    private final CodeTableRepository codeTableRepository;
    private final CodeTableService codeTableService;
    private final HrClientRepository hrClientRepository;
    private final HrBillCompareConfigMapper hrBillCompareConfigMapper;

    private static final String PENSION = "养老";
    private static final String UNEMPLOYMENT = "失业";
    private static final String INJURY = "工伤";
    private static final String MEDICAL = "医疗";
    private static final String MATERNITY = "生育";
    private static final String ACCUMULATION_FUND = "公积金";

    /**
     * 创建薪酬账单动态字段
     *
     * @param hrBillDynamicFieldsDTO
     * @return
     */
    @Override
    public HrBillDynamicFieldsDTO createHrBillDynamicFields(HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO) {
        log.info("Create new HrBillDynamicFields:{}", hrBillDynamicFieldsDTO);

        HrBillDynamicFields hrBillDynamicFields = this.hrBillDynamicFieldsMapper.toEntity(hrBillDynamicFieldsDTO);
        this.hrBillDynamicFieldsRepository.insert(hrBillDynamicFields);
        return this.hrBillDynamicFieldsMapper.toDto(hrBillDynamicFields);
    }

    /**
     * 修改薪酬账单动态字段
     *
     * @param hrBillDynamicFieldsDTO
     * @return
     */
    @Override
    public Optional<HrBillDynamicFieldsDTO> updateHrBillDynamicFields(HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO) {
        return Optional.ofNullable(this.hrBillDynamicFieldsRepository.selectById(hrBillDynamicFieldsDTO.getId()))
            .map(roleTemp -> {
                HrBillDynamicFields hrBillDynamicFields = this.hrBillDynamicFieldsMapper.toEntity(hrBillDynamicFieldsDTO);
                this.hrBillDynamicFieldsRepository.updateById(hrBillDynamicFields);
                log.info("Update HrBillDynamicFields:{}", hrBillDynamicFieldsDTO);
                return hrBillDynamicFieldsDTO;
            });
    }

    /**
     * 查询薪酬账单动态字段详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillDynamicFieldsDTO getHrBillDynamicFields(String id) {
        log.info("Get HrBillDynamicFields :{}", id);

        HrBillDynamicFields hrBillDynamicFields = this.hrBillDynamicFieldsRepository.selectById(id);
        return this.hrBillDynamicFieldsMapper.toDto(hrBillDynamicFields);
    }

    /**
     * 删除薪酬账单动态字段
     *
     * @param id
     */
    @Override
    public void deleteHrBillDynamicFields(String id) {
        Optional.ofNullable(this.hrBillDynamicFieldsRepository.selectById(id))
            .ifPresent(hrBillDynamicFields -> {
                this.hrBillDynamicFieldsRepository.deleteById(id);
                log.info("Delete HrBillDynamicFields:{}", hrBillDynamicFields);
            });
    }

    /**
     * 批量删除薪酬账单动态字段
     *
     * @param ids
     */
    @Override
    public void deleteHrBillDynamicFields(List<String> ids) {
        log.info("Delete HrBillDynamicFieldss:{}", ids);
        this.hrBillDynamicFieldsRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询薪酬账单动态字段
     *
     * @param hrBillDynamicFieldsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO, Long pageNumber, Long pageSize) {
        Page<HrBillDynamicFields> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrBillDynamicFields> qw = new QueryWrapper<>(this.hrBillDynamicFieldsMapper.toEntity(hrBillDynamicFieldsDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrBillDynamicFieldsRepository.selectPage(page, qw);
        iPage.setRecords(this.hrBillDynamicFieldsMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    /**
     * 导入工资明细后,获取表头
     *
     * @param file     excel文件
     * @param billId   账单id
     * @param clientId 客户ID
     */
    @Override
    public BillTableHeaderDTO getOriginTableHeader(MultipartFile file, String billId, String originSalaryId, String clientId) {

        // 判断是否使用了系统事先创建好的薪酬原单,如果是,则替换file
        HrOriginalSalary originalSalary = new HrOriginalSalary();
        if (StringUtils.isNotEmpty(originSalaryId)) {
            originalSalary = this.hrOriginalSalaryRepository.selectById(originSalaryId);
            if (originalSalary == null) {
                throw new CommonException("薪酬原单查询异常,请重新选择后提交");
            }
            String originBillUrl = originalSalary.getSalaryUrl();
            try {
                File f = FileUtil.getFileByUrl(originBillUrl, FileUtil.getFileExt(originBillUrl));
                file = FileUtil.fileToMultipartFile(f);
            } catch (Exception e) {
                e.printStackTrace();
                throw new CommonException("薪酬原单解析失败!");
            }
        }
        List<HrBill> hrBillList = hrBillRepository.selectByIdOrBillNo(billId);
        if (hrBillList == null || hrBillList.isEmpty()) {
            throw new CommonException("未查询到原始账单！");
        }
        // 删除原先的账单配置
        this.deleteOldDynamicBill(hrBillList);


        // 解析excel文件,获取返回对象
        List<String> expenseTypes = Arrays.asList(
            DynamicFeeTypesEnum.NAME.getKey(),
            DynamicFeeTypesEnum.ID_CARD.getKey(),
            DynamicFeeTypesEnum.FEE_TYPE_ADD.getKey(),
            DynamicFeeTypesEnum.FEE_TYPE_REDUCE.getKey(),
            DynamicFeeTypesEnum.FEE_TYPE_OTHER.getKey(),
            DynamicFeeTypesEnum.TAX_EXEMPT_INCOME.getKey()
        );
        HrBill hrBill = hrBillList.get(0);
        List<String> idStrs = new ArrayList<>();
        Integer flag = 0;
        //中石化费用统计账单
        if (hrBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            expenseTypes = Arrays.asList(
                DynamicFeeTypesEnum.CURRENT_YEAR.getKey(), DynamicFeeTypesEnum.PROJECT_CODE.getKey(), DynamicFeeTypesEnum.PROJECT_NAME.getKey(),
                DynamicFeeTypesEnum.PROJECT_TYPE.getKey(), DynamicFeeTypesEnum.CONTRACT_NO.getKey(),
                DynamicFeeTypesEnum.PEOPLE_NUM.getKey(), DynamicFeeTypesEnum.EMPLOYEE_COMPENSATION.getKey(), DynamicFeeTypesEnum.UNIT_HOUSING_FUND.getKey(),
                DynamicFeeTypesEnum.UNIT_INSURANCE.getKey(), DynamicFeeTypesEnum.OTHER_EXPENSE.getKey(), DynamicFeeTypesEnum.REIMBURSEMENT_EXPENSE.getKey(),
                DynamicFeeTypesEnum.MANAGEMENT_EXPENSE.getKey(), DynamicFeeTypesEnum.TAXES.getKey(), DynamicFeeTypesEnum.INDIVIDUAL_INSURANCE.getKey(),
                DynamicFeeTypesEnum.INDIVIDUAL_HOUSING_FUND.getKey(), DynamicFeeTypesEnum.INDIVIDUAL_INCOME_TAX.getKey(), DynamicFeeTypesEnum.TOTAL_EXPENSES.getKey()
            );
            idStrs.add("项目名称");
            flag = 1;
        } else {
            idStrs.add("身份证");
            idStrs.add("证件号");
            idStrs.add("证照号");
            if (hrBill.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey())) {
                flag = 2;
                idStrs.add("客户名称");
            }
        }
        if (StringUtils.isEmpty(clientId) || clientId.equals("null")) {
            clientId = hrBill.getOptClientId() == null ? hrBill.getClientId() : hrBill.getOptClientId();
        }
        HrClient rootParentClient = hrClientRepository.getRootParentClient(clientId);
        if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
            List<String> expenseTypeList = new ArrayList<>(expenseTypes);
            expenseTypeList.add(DynamicFeeTypesEnum.SERVICE_CHARGE.getKey());
            expenseTypes = expenseTypeList;
        }
        BillTableHeaderDTO result = this.getTableHeaderResult(file, expenseTypes, clientId, idStrs, flag, null);
        // 薪酬原单上传到私有云
        HrAppendixDTO hrAppendixDTO;
        if (StringUtils.isEmpty(originSalaryId)) {
            hrAppendixDTO = this.hrAppendixService.uploadSingleFile(file);
        } else {
            hrAppendixDTO = new HrAppendixDTO()
                .setFileUrl(originalSalary.getSalaryUrl())
                .setOriginName(originalSalary.getTitle());
        }

        // 保存薪酬账单字段信息
        HrBillDynamicFields billDynamicFields = new HrBillDynamicFields()
            .setBillId(hrBill.getId())
            .setContent(JSON.toJSONString(result.getBillFieldInfoDTO(), SerializerFeature.DisableCircularReferenceDetect))
            .setDataStartRows(result.getDataStartRow())
            .setOriginBillUrl(hrAppendixDTO.getFileUrl())
            .setOriginBillName(hrAppendixDTO.getOriginName());
        this.save(billDynamicFields);

        return result;
    }

    /**
     * 添加中石化账单数据字典
     *
     * @param cellItemList excel表头
     * @param expenseTypes 中石化账单费用类型
     */
    private void createSinopecExpenseManage(List<CellItem> cellItemList, List<String> expenseTypes, String clientId) {
        List<HrExpenseManageDTO> hrExpenseManageDTOS = hrExpenseManageRepository.getListByExpenseType(expenseTypes, clientId);
        List<HrExpenseManage> list = new ArrayList<>();
        for (CellItem cellItem : cellItemList) {
            if (StringUtils.isNotEmpty(cellItem.getValue())) {
                List<CellItem> childrenItemList = cellItem.getChildren();
                if (CollectionUtils.isNotEmpty(childrenItemList)) {
                    this.handleChildrenItem(cellItem, childrenItemList, clientId, hrExpenseManageDTOS, list);
                } else {
                    List<HrExpenseManageDTO> collect = hrExpenseManageDTOS.stream().filter(lst -> lst.getExpenseName().equals(cellItem.getValue())).collect(Collectors.toList());
                    if (!collect.isEmpty()) {
                        HrExpenseManage hrExpenseManage = new HrExpenseManage();
                        hrExpenseManage.setClientId(clientId)
                            .setExpenseName(cellItem.getValue())
                            .setExpenseType(Objects.requireNonNull(DynamicFeeTypesEnum.getEnumEntityByValue(cellItem.getValue())).getKey());
                        list.add(hrExpenseManage);
                    }
                }
            }
        }
        hrExpenseManageService.saveBatch(list);
    }

    /**
     * 处理子集表头
     *
     * @param cellItem
     * @param childrenItemList
     * @param clientId
     * @param hrExpenseManageDTOS
     * @return
     */
    private void handleChildrenItem(CellItem cellItem, List<CellItem> childrenItemList, String clientId, List<HrExpenseManageDTO> hrExpenseManageDTOS, List<HrExpenseManage> list) {
        for (CellItem item : childrenItemList) {
            List<CellItem> childrenList = item.getChildren();
            if (CollectionUtils.isNotEmpty(childrenList)) {
                handleChildrenItem(cellItem, childrenList, clientId, hrExpenseManageDTOS, list);
            } else {
                List<HrExpenseManageDTO> collect = hrExpenseManageDTOS.stream().filter(lst -> lst.getExpenseName().equals(item.getValue())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    HrExpenseManage hrExpenseManage = new HrExpenseManage();
                    hrExpenseManage.setClientId(clientId)
                        .setExpenseName(item.getValue())
                        .setExpenseType(DynamicFeeTypesEnum.getEnumEntityByValue(cellItem.getValue()).getKey());
                    list.add(hrExpenseManage);
                }
            }
        }
    }

    /**
     * excel导入后,获取展示对象
     *
     * @param file         excel文件
     * @param expenseTypes 费用项类型
     * @param clientId     费用项类型
     * @param idStrs       身份标识集合
     * @param flag         0-薪酬账单 1-中石化账单 2 其他账单
     * @param type         对账类型
     * @return
     */
    @Override
    public BillTableHeaderDTO getTableHeaderResult(MultipartFile file, List<String> expenseTypes, String clientId, List<String> idStrs, Integer flag, Integer type) {
        // 读取【身份证】所在行作为费用列起始行
        // TODO: 未读取到身份证的时候应该读取其映射字段
        CellItem idCellItem = ExcelReadUtils.readCellByValue(file, 0, idStrs);
        if (idCellItem == null) {
            throw new CommonException("该文档无可用的身份标识列");
        }

        // 解析原始表头
        List<CellItem> list = ExcelReadUtils.readExcel(file, 0, idCellItem.getFirstRow(), idCellItem.getLastRow(), flag);
        if (list.size() == 0) {
            throw new CommonException("未解析到表头数据!");
        }
        if (flag == 1) {
            // createSinopecExpenseManage(list,expenseTypes,clientId);
        } else if (flag == 2) {
            int num = 0;
            for (CellItem cellItem : list) {
                for (String idStr : idStrs) {
                    if (cellItem.getValue().contains(idStr)) {
                        num++;
                    }
                }
            }
            if (num > 1) {
                throw new CommonException("该文档存在多个身份标识列");
            }
            expenseTypes = new ArrayList<>();
            expenseTypes.add(DynamicFeeTypesEnum.CHARGE_ITEM.getKey());
            expenseTypes.add(DynamicFeeTypesEnum.REFUND_ITEM.getKey());
            if (idCellItem.getValue().equals("客户名称")) {
                expenseTypes.add(DynamicFeeTypesEnum.CUSTOMER_NAME.getKey());
            } else {
                expenseTypes.add(DynamicFeeTypesEnum.NAME.getKey());
                expenseTypes.add(DynamicFeeTypesEnum.ID_CARD.getKey());
            }
        }
        // 定义方法返回对象
        BillTableHeaderDTO result = new BillTableHeaderDTO();
        result.setStartRow(idCellItem.getFirstRow()).setEndRow(idCellItem.getLastRow()).setDataStartRow(idCellItem.getLastRow() + 1);

        // 解析cells,获取字段映射等信息
        BillFieldInfoDTO billFieldInfoDTO = this.getFieldMapping(list, expenseTypes, clientId, type);
        result.setBillFieldInfoDTO(billFieldInfoDTO);

        // 获取前端可展示的表单映射数据
        List<List<CellItem>> tableMappingList = this.getMappingRelationData(billFieldInfoDTO.getMappingFields());
        if (type != null && type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            list = list.stream().sorted(Comparator.comparing(CellItem::getOrder)).collect(Collectors.toList());
        }
        result.setCellItemList(list).setTableMappingList(tableMappingList);

        return result;
    }

    /**
     * 导盘文件导入
     *
     * @param file   导盘文件
     * @param billId 账单id
     * @param type   导盘类型 0 社保, 1 医保, 2 公积金,3 个税, 4第三方账单
     * @return
     */
    @Override
    public BillTableHeaderDTO importGuidePlate(MultipartFile file, String billId, Integer type) {
        // 判断该账单是否可以对账
        this.checkBillStatus(billId, type);
        return this.handleGuidePlate(file, billId, type, null, null, null);
    }

    /**
     * 批量对账导盘导入
     *
     * @param paymentDate 缴费年月
     * @param type        导盘类型 0 社保, 1 医保, 2 公积金, 3 个税 5海尔对账单
     * @return
     */
    @Override
    public BillTableHeaderDTO batchReconciliationBill(MultipartFile file, String paymentDate, Integer type) {
        String[] split = paymentDate.split("-");
        Integer payYear = Integer.parseInt(split[0]);
        Integer payMonthly = Integer.parseInt(split[1]);
        // 海尔对账单处理
        if (type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            //获取可以对账的海尔保障账单
            List<HrClientDTO> hrClientDTOList = hrClientRepository.getSubordinateClient(SpecialBillClient.HAIER.getKey());
            if (CollectionUtils.isEmpty(hrClientDTOList)) {
                throw new CommonException("没有可以对账的客户！");
            }
            List<String> clientIds = hrClientDTOList.stream().map(HrClientDTO::getId).collect(Collectors.toList());
            QueryWrapper<HrBill> qw = new QueryWrapper<>();
            qw.eq("is_delete", 0);
            qw.eq("is_official", 1);
            qw.eq("bill_type", BillEnum.BillType.SECURITY_BILL.getKey());
            qw.eq("pay_year", payYear);
            qw.eq("pay_monthly", payMonthly);
            qw.in("client_id", clientIds);
            List<HrBill> hrBillList = hrBillRepository.selectList(qw);
            if (hrBillList == null || hrBillList.isEmpty()) {
                throw new CommonException("选择对账的费用年月中没有可以对账的保障账单！");
            }
            List<String> billIds = hrBillList.stream().map(HrBill::getId).collect(Collectors.toList());
            return this.handleGuidePlate(file, null, type, billIds, payYear, payMonthly);
        } else {
            if (!BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey().equals(type)) {
                HrBillCompareResultDTO hrBillCompareResultDTO = new HrBillCompareResultDTO();
                hrBillCompareResultDTO.setTitle("全部公司");
                hrBillCompareResultDTO.setPayYear(payYear);
                hrBillCompareResultDTO.setPayMonthly(payMonthly);
                hrBillCompareResultDTO.setType(type);
                List<HrBillCompareResultDTO> billCompareResultDTOS = hrBillCompareResultRepository.findList(hrBillCompareResultDTO);
                if (CollectionUtils.isNotEmpty(billCompareResultDTOS)) {
                    List<HrBillCompareResultDTO> collect = billCompareResultDTOS.stream().filter(lst -> lst.getLockStatus() == 1).collect(Collectors.toList());
                    if (collect != null && !collect.isEmpty()){
                        throw new CommonException("不同类型的批量对账单每月仅可保存一个!");
                    }
                }
                HrBill hrBill = new HrBill();
                hrBill.setPayYear(payYear);
                hrBill.setPayMonthly(payMonthly);
                if (type.equals(BillEnum.GuidePlateType.SOCIAL_SECURITY_GUIDE.getKey())
                    || type.equals(BillEnum.GuidePlateType.MEDICAL_GUIDE.getKey())
                    || type.equals(BillEnum.GuidePlateType.ACCUMULATION_FUND_GUIDE.getKey())) {
                    hrBill.setBillType(BillEnum.BillType.SECURITY_BILL.getKey());
                }
                hrBill.setTitle("全部公司" + split[1] + "月份" + Objects.requireNonNull(BillEnum.GuidePlateType.getValueByKey(type)).substring(0, 2) + "对账单");
                HrBill bill = hrBillRepository.selectOne(new QueryWrapper<HrBill>().eq("pay_year", payYear).eq("pay_monthly", payMonthly).eq("title", hrBill.getTitle()).last("LIMIT 1"));
                if (bill == null){
                    hrBillRepository.insert(hrBill);
                }else {
                    hrBill = bill;
                }
                /*if (!BillEnum.GuidePlateType.THIRD_PARTY_BILLS.getKey().equals(type)) {
                    this.hrBillCompareConfigService.delByBillIdAndType(hrBill.getId(), type);
                }*/
                return this.handleGuidePlate(file, hrBill.getId(), type, null, null, null);
            } else {
                QueryWrapper<HrBill> eq = new QueryWrapper<>();
                eq.eq("is_delete", 0);
                eq.eq("bill_type", BillEnum.BillType.SALARY_BILL.getKey());
                eq.eq("is_official", 1);
                eq.eq("pay_year", payYear);
                eq.eq("pay_monthly", payMonthly);
                eq.eq("bill_state", BillEnum.BillState.LOCKED.getKey());

                //1. 查询已经存在已应用补差的账单
                List<Integer> status = Arrays.asList(
                    BillEnum.MakeupIsUsed.USED.getKey(),
                    BillEnum.MakeupIsUsed.NOT_EFFECTIVE.getKey()
                );
                //查询已经保存补差或者已经锁定的账单
                 List<HrBillCompareConfigDTO> resultDTOS = hrBillCompareResultRepository.getIsUsedBillId(payYear, payMonthly, BillEnum.BillType.SALARY_BILL.getKey());
                if (resultDTOS != null && !resultDTOS.isEmpty()) {
                    List<String> billIds = resultDTOS.stream().map(HrBillCompareConfigDTO::getBillId).distinct().collect(Collectors.toList());
                    eq.notIn("id", billIds);
                }
                List<HrBill> hrBills = hrBillRepository.selectList(eq);
                if (hrBills == null || hrBills.isEmpty()) {
                    throw new CommonException("选择对账的费用年月中没有可以对账的薪酬账单！");
                }
                List<String> billIds = hrBills.stream().map(HrBill::getId).collect(Collectors.toList());
                return this.handleGuidePlate(file, null, type, billIds, null, null);
            }
        }
    }


    /**
     * 处理导盘文件信息
     *
     * @param file       导盘文件
     * @param billId     账单id
     * @param type       导盘类型 0 社保, 1 医保, 2 公积金,3 个税, 4第三方账单
     * @param payYear
     * @param payMonthly
     * @return
     */
    public BillTableHeaderDTO handleGuidePlate(MultipartFile file, String billId, Integer type, List<String> billIds, Integer payYear, Integer payMonthly) {
        // 获取不同导盘的比较类型
        List<EnumDTO> enumDTOS = this.hrBillCompareConfigService.getExpenseTypeItem(type);
        List<String> expenseTypes = new ArrayList<>();
        enumDTOS.forEach(e -> expenseTypes.add(e.getKey()));
        List<String> idStrs = new ArrayList<>();
        idStrs.add("身份证");
        idStrs.add("证件号");
        idStrs.add("证照号");
        BillTableHeaderDTO result = this.getTableHeaderResult(file, expenseTypes, null, idStrs, 0, type);
        // 根据导盘类型,判断传入的excel是否符合格式,同时设置一些必须的信息
        this.checkAndSetExcelFieldInfo(result, type);

        // 文件上传到私有云
        HrAppendixDTO hrAppendixDTO = this.hrAppendixService.uploadSingleFile(file);
        if (billId != null) {
            HrBillCompareConfigDTO compareConfigDTO = hrBillCompareConfigRepository.getByBillId(billId);
            // 保存导盘文件信息
            HrBillCompareConfig compareConfig = compareConfigDTO == null ? new HrBillCompareConfig() : hrBillCompareConfigMapper.toEntity(compareConfigDTO);
            compareConfig.setBillId(billId)
                .setType(type)
                .setOriginFileUrl(hrAppendixDTO.getFileUrl())
                .setOriginFileName(hrAppendixDTO.getOriginName())
                .setDataStartRow(result.getDataStartRow())
                .setConfig(JSON.toJSONString(result.getBillFieldInfoDTO(), SerializerFeature.DisableCircularReferenceDetect));
            if (compareConfigDTO == null){
                this.hrBillCompareConfigRepository.insert(compareConfig);
            }else {
                this.hrBillCompareConfigRepository.updateById(compareConfig);
            }
            result.setBillConfigId(compareConfig.getId());
        }
        // 批量对账
        else {
            if (type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
                // 创建一条总配置信息
                HrBillCompareConfig compareConfig = new HrBillCompareConfig()
                    .setBillId(billIds.get(0))
                    .setClientId(SpecialBillClient.HAIER.getKey())
                    .setType(type)
                    .setPayYear(payYear)
                    .setPayMonthly(payMonthly)
                    .setOriginFileUrl(hrAppendixDTO.getFileUrl())
                    .setOriginFileName(hrAppendixDTO.getOriginName())
                    .setDataStartRow(result.getDataStartRow())
                    .setConfig(JSON.toJSONString(result.getBillFieldInfoDTO(), SerializerFeature.DisableCircularReferenceDetect));
                compareConfig.setIsDelete(true);
                this.hrBillCompareConfigRepository.insert(compareConfig);

                result.setBillConfigId(compareConfig.getId());
            }
            // 批量个税对账
            else {
                List<HrBillCompareConfigDTO> billFieldInfoDTOList = hrBillCompareConfigRepository.findCompareConfig(billIds, BillEnum.GuidePlateType.PERSONAL_TAX_GUIDE.getKey());
                List<HrBillCompareConfig> list = new ArrayList<>();
                for (String id : billIds) {
                    // 保存导盘文件信息
                    HrBillCompareConfig compareConfig = new HrBillCompareConfig()
                        .setBillId(id)
                        .setType(type)
                        .setOriginFileUrl(hrAppendixDTO.getFileUrl())
                        .setOriginFileName(hrAppendixDTO.getOriginName())
                        .setDataStartRow(result.getDataStartRow())
                        .setConfig(JSON.toJSONString(result.getBillFieldInfoDTO(), SerializerFeature.DisableCircularReferenceDetect));
                    billFieldInfoDTOList.stream().filter(lst -> lst.getBillId().equals(id)).findFirst().ifPresent(hrBillCompareConfigDTO -> compareConfig.setParentId(hrBillCompareConfigDTO.getId()));
                    list.add(compareConfig);
                }
                this.hrBillCompareConfigService.saveBatch(list);
                List<String> billConfigIds = list.stream().map(HrBillCompareConfig::getId).collect(Collectors.toList());
                result.setBillConfigIds(billConfigIds);
            }
        }
        return result;
    }

    /**
     * 根据导盘类型,校验excel是否正确,同时处理映射等信息
     *
     * @param result
     * @param type   导盘类型
     */
    private void checkAndSetExcelFieldInfo(BillTableHeaderDTO result, Integer type) {
        BillFieldInfoDTO billFieldInfoDTO = result.getBillFieldInfoDTO();
        BillEnum.GuidePlateType plateType = EnumUtils.getEnumByKey(BillEnum.GuidePlateType.class, type);
        List<String> needFields = null;

        switch (plateType) {
            // 社保
            case SOCIAL_SECURITY_GUIDE:
                needFields = Arrays.asList(
                    "社保编号",
                    "证件号码",
                    "缴费类型"
                );
                break;
            // 医保
            case MEDICAL_GUIDE:
                needFields = Arrays.asList(
                    "社保编号",
                    "身份证号",
                    "缴费类型"
                );
                break;
            // 公积金
            case ACCUMULATION_FUND_GUIDE:
                needFields = Arrays.asList(
                    "单位金额",
                    "个人金额",
                    "合计"
                );
                // 手动创建映射关系
                this.setParamWithAccu(needFields, result);
                break;
            case PERSONAL_TAX_GUIDE:
                needFields = Arrays.asList(
                    "累计应扣缴税额"
                );
                break;
            case SPECIAL_CLIENT_BILL:
                needFields = Arrays.asList(
                    "中心",
                    "身份证号",
                    "预算体名称"
                );
                break;
            default:
                break;
        }

        if (needFields != null) {
            // 原始的表头中必须要包含所需要的字段
            List<String> allFields = this.getAllHeaderFields(billFieldInfoDTO.getCellItems());
            if (type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
                if (!allFields.contains("身份证号")) {
                    throw new CommonException("解析失败," + plateType.getValue() + "的表头应当含有列[身份证号]");
                }
                if (!allFields.contains("中心") && !allFields.contains("预算体名称")) {
                    throw new CommonException("解析失败," + plateType.getValue() + "的表头应当含有列[中心/预算体名称]");
                }
            } else {
                for (String str : needFields) {
                    if (!allFields.contains(str)) {
                        throw new CommonException("解析失败," + plateType.getValue() + "的表头应当含有列[" + str + "]");
                    }
                }
            }
        }
    }

    /**
     * 公积金类型的导盘,设置映射关系
     *
     * @param needFields
     * @param result
     */
    private void setParamWithAccu(List<String> needFields, BillTableHeaderDTO result) {
        BillFieldInfoDTO billFieldInfoDTO = result.getBillFieldInfoDTO();

        // 获取未被映射的字段
        List<CellItem> unUsedCols = billFieldInfoDTO.getUnUsedCols();
        // 已映射的字段
        List<HrExpenseManageDTO> mappingFields = billFieldInfoDTO.getMappingFields();

        for (String field : needFields) {
            CellItem cellItem = BillParseUtils.getCellItemByName(field, unUsedCols);
            if (cellItem == null) {
                continue;
            }
            unUsedCols.remove(cellItem);
            HrExpenseManageDTO manageDTO = new HrExpenseManageDTO();
            manageDTO.setCellOriginName(field);
            manageDTO.setExpenseName(field);
            manageDTO.setColIndex(cellItem.getFirstCol());
            manageDTO.setSortValue(cellItem.getOrder());
            switch (field) {
                case "单位金额":
                    manageDTO.setExpenseType(DynamicFeeTypesEnum.UNIT_ACCUMULATION_FUND.getKey())
                        .setExpenseTypeName(DynamicFeeTypesEnum.UNIT_ACCUMULATION_FUND.getValue());
                    break;
                case "个人金额":
                    manageDTO.setExpenseType(DynamicFeeTypesEnum.PERSONAL_ACCUMULATION_FUND.getKey())
                        .setExpenseTypeName(DynamicFeeTypesEnum.PERSONAL_ACCUMULATION_FUND.getValue());
                    break;
                case "合计":
                    manageDTO.setExpenseType(DynamicFeeTypesEnum.TOTAL_ACCUMULATION_FUND.getKey())
                        .setExpenseTypeName(DynamicFeeTypesEnum.TOTAL_ACCUMULATION_FUND.getValue());
                    break;
                default:
                    break;
            }
            mappingFields.add(manageDTO);
        }

        // 重新获取前端需要的映射表单
        result.setTableMappingList(this.getMappingRelationData(mappingFields));
    }

    /**
     * 获取所有的表头名称
     *
     * @param cellItems
     * @return
     */
    private List<String> getAllHeaderFields(List<CellItem> cellItems) {
        List<String> names = new ArrayList<>();
        for (CellItem cellItem : cellItems) {
            getNames(cellItem, names);
        }
        return names;
    }

    /**
     * 递归获取表头名称
     *
     * @param cellItem
     * @param list
     */
    private void getNames(CellItem cellItem, List<String> list) {
        list.add(cellItem.getValue());
        List<CellItem> children = cellItem.getChildren();
        if (children == null) {
            return;
        }
        for (CellItem c : children) {
            getNames(c, list);
        }
    }

    /**
     * 判断是有审核通过的账单可以对账
     *
     * @param billId 账单id
     * @param type   导盘类型
     */
    private void checkBillStatus(String billId, Integer type) {
        // 1.审核通过的账单可以对账
        HrBill bill = hrBillRepository.selectById(billId);
        if (bill == null || bill.getIsDelete()) {
            throw new CommonException("账单不存在!");
        }
        Integer reviewState = bill.getReviewState();
        if (reviewState == null || reviewState != 1) {
            throw new CommonException("只有审核通过的账单可以对账!");
        }

        // 2.如果本期账单存在已应用的补差,不允许重复导入
        List<Integer> status = Arrays.asList(
            BillEnum.MakeupIsUsed.USED.getKey(),
            BillEnum.MakeupIsUsed.NOT_EFFECTIVE.getKey()
        );
        List<HrWelfareCompensationDTO> hrWelfareCompensationDTOS = this.hrWelfareCompensationRepository.getByBillIdAndIsUsed(billId, type, status);
        if (hrWelfareCompensationDTOS != null && hrWelfareCompensationDTOS.size() > 0) {
            throw new CommonException("本期账单已经导入过该类型的导盘,并且补差结果已被使用,禁止操作!");
        }

        // 3.除第三方账单,如果本期账单存在同类型的导盘,则删除原本导盘的数据
        if (!BillEnum.GuidePlateType.THIRD_PARTY_BILLS.getKey().equals(type)) {
            this.hrBillCompareConfigService.delByBillIdAndType(billId, type);
        }
    }

    /**
     * 导盘对账设置字段映射
     *
     * @param id    对账配置id
     * @param param 费用项映射
     * @return List<List < CellItem>>
     */
    @Override
    public List<List<CellItem>> setFieldMrByGuidePlate(String id, HrExpenseManageDTO param) {
        this.checkExpenseManage(param);

        // 获取对账配置
        HrBillCompareConfigDTO configDTO = this.hrBillCompareConfigRepository.findById(id);
        BillFieldInfoDTO billFieldInfoDTO = configDTO.getBillFieldInfoDTO().orElseThrow(() -> new CommonException("解析配置信息异常!"));

        // TODO:根据导盘类型,只能设置指定类型的费用项,并且不能重复

        // 保存费用类型,然后获取新的对账配置
        this.saveAndGetBillFieldInfo(param, billFieldInfoDTO, false, configDTO);

        // 更新对账配置
        configDTO.setConfig(JSON.toJSONString(billFieldInfoDTO, SerializerFeature.DisableCircularReferenceDetect));
        if (configDTO.getType().equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            this.hrBillCompareConfigRepository.updateConfig(configDTO.getConfig(), configDTO.getId());
        } else {
            this.hrBillCompareConfigService.updateHrBillCompareConfig(configDTO);
        }

        // 返回前端需要的表单映射数据
        return this.getMappingRelationData(billFieldInfoDTO.getMappingFields());
    }

    private void checkExpenseManage(HrExpenseManageDTO param) {
        // 必填参数校验
        if (StringUtils.isEmpty(param.getExpenseName())) {
            throw new CommonException("费用类型名称不能为空!");
        }
        if (StringUtils.isEmpty(param.getExpenseType())) {
            throw new CommonException("费用类型项不能为空!");
        }
        if (param.getSortValue() == null) {
            throw new CommonException("字段排序值缺失!");
        }
    }

    /**
     * 删除字段映射关系
     *
     * @param billId 账单id
     * @param param
     * @return
     */
    @Override
    public List<List<CellItem>> delMappingRelationWithBill(String billId, HrExpenseManageDTO param) {
        List<HrBill> hrBillList = hrBillRepository.selectByIdOrBillNo(billId);
        if (hrBillList == null || hrBillList.isEmpty()) {
            throw new CommonException("未查询到账单信息！");
        }
        HrBillDynamicFieldsDTO dynamicFieldsDTO = this.hrBillDynamicFieldsRepository.getByBillId(hrBillList.get(0).getId());
        List<List<CellItem>> cellItemList = this.handleDelMappingRelation(dynamicFieldsDTO, param);
        return cellItemList;
    }

    /**
     * 删除字段映射
     *
     * @param dynamicFieldsDTO
     * @param param
     */
    @Override
    public List<List<CellItem>> handleDelMappingRelation(HrBillDynamicFieldsDTO dynamicFieldsDTO, HrExpenseManageDTO param) {
        BillFieldInfoDTO fieldInfoDTO = dynamicFieldsDTO.getBillFieldInfoDTO();
        if (fieldInfoDTO == null) {
            throw new CommonException("未获取到薪酬原单解析信息,操作失败!");
        }

        this.delFieldMappingRelationCommon(fieldInfoDTO, param, true);

        // 更新
        dynamicFieldsDTO.setContent(JSON.toJSONString(fieldInfoDTO, SerializerFeature.DisableCircularReferenceDetect));
        this.updateById(this.hrBillDynamicFieldsMapper.toEntity(dynamicFieldsDTO));

        return this.getMappingRelationData(fieldInfoDTO.getMappingFields());
    }

    /**
     * 通用删除已映射字段的方法
     *
     * @param fieldInfoDTO
     * @param param
     * @param flag         true---薪酬账单:删除字段映射关系  false--对账:删除字段映射关系
     */
    private void delFieldMappingRelationCommon(BillFieldInfoDTO fieldInfoDTO, HrExpenseManageDTO param, Boolean flag) {
        List<CellItem> cellItems = fieldInfoDTO.getCellItems();

        // 设置该字段为未映射
        CellItem cellItem = BillParseUtils.getCellItemByNameAndOrder(param.getExpenseName(), param.getSortValue(), cellItems);
        cellItem.setHasMapping(false);

        // 从已映射字段中移除
        List<HrExpenseManageDTO> mappingFields = fieldInfoDTO.getMappingFields();
        HrExpenseManageDTO expenseManageDTO = null;
        for (HrExpenseManageDTO f : mappingFields) {
            if (f.getExpenseName().equals(param.getExpenseName()) && f.getSortValue().equals(param.getSortValue())) {
                expenseManageDTO = f;
                hrExpenseManageRepository.deleteById(f.getId());
                mappingFields.remove(f);
                break;
            }
        }
        if (expenseManageDTO == null) {
            throw new CommonException("获取excel字段[" + param.getExpenseName() + "]异常");
        }

        // 转CellItem
        CellItem newCellItem = this.expenseManage2CellItem(expenseManageDTO);

        // 添加到未映射字段
        List<CellItem> unUsedCols = fieldInfoDTO.getUnUsedCols();
        unUsedCols.add(newCellItem);

        // 已映射字段-1
        Integer mappingFieldTotal = fieldInfoDTO.getMappingFieldTotal();
        fieldInfoDTO.setMappingFieldTotal(mappingFieldTotal - 1);
    }


    /**
     * 薪酬账单获取费用项类型
     *
     * @param billType 账单类型 0薪酬账单 1保障账单 3其他账单
     * @param clientId 用于薪酬账单
     * @return
     */
    @Override
    public List<EnumDTO> getExpenseManage(Integer billType, String clientId) {
        List<DynamicFeeTypesEnum> dynamicFeeTypesEnums = new ArrayList<>();
        if (billType.equals(BillEnum.BillType.SALARY_BILL.getKey())) {
            dynamicFeeTypesEnums = Arrays.asList(
                DynamicFeeTypesEnum.NAME,
                DynamicFeeTypesEnum.ID_CARD,
                DynamicFeeTypesEnum.PROJECT_NAME,
                DynamicFeeTypesEnum.FEE_TYPE_ADD,
                DynamicFeeTypesEnum.FEE_TYPE_REDUCE,
                DynamicFeeTypesEnum.FEE_TYPE_OTHER,
                DynamicFeeTypesEnum.TAX_EXEMPT_INCOME
            );
            if (StringUtils.isNotEmpty(clientId)) {
                //海尔集团费用项添加服务费
                HrClient rootParentClient = hrClientRepository.getRootParentClient(clientId);
                if (rootParentClient != null && rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
                    List<DynamicFeeTypesEnum> arrayList = new ArrayList<>(dynamicFeeTypesEnums);
                    arrayList.add(DynamicFeeTypesEnum.SERVICE_CHARGE);
                    dynamicFeeTypesEnums = arrayList;
                }
            }
        } else if (billType.equals(BillEnum.BillType.OTHER_BILL.getKey())) {
            dynamicFeeTypesEnums = Arrays.asList(
                DynamicFeeTypesEnum.NAME,
                DynamicFeeTypesEnum.ID_CARD,
                DynamicFeeTypesEnum.CUSTOMER_NAME,
                DynamicFeeTypesEnum.CHARGE_ITEM,
                DynamicFeeTypesEnum.REFUND_ITEM
            );
        } else if (billType.equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            dynamicFeeTypesEnums = Arrays.asList(
                DynamicFeeTypesEnum.CURRENT_YEAR, DynamicFeeTypesEnum.PROJECT_CODE, DynamicFeeTypesEnum.PROJECT_NAME,
                DynamicFeeTypesEnum.PEOPLE_NUM, DynamicFeeTypesEnum.PROJECT_TYPE, DynamicFeeTypesEnum.CONTRACT_NO,
                DynamicFeeTypesEnum.EMPLOYEE_COMPENSATION, DynamicFeeTypesEnum.UNIT_HOUSING_FUND,
                DynamicFeeTypesEnum.UNIT_INSURANCE, DynamicFeeTypesEnum.OTHER_EXPENSE, DynamicFeeTypesEnum.REIMBURSEMENT_EXPENSE,
                DynamicFeeTypesEnum.MANAGEMENT_EXPENSE, DynamicFeeTypesEnum.TAXES, DynamicFeeTypesEnum.INDIVIDUAL_INSURANCE,
                DynamicFeeTypesEnum.INDIVIDUAL_HOUSING_FUND, DynamicFeeTypesEnum.INDIVIDUAL_INCOME_TAX, DynamicFeeTypesEnum.TOTAL_EXPENSES
            );
        } else if (billType.equals(BillEnum.BillType.SPECIAL_REVIEW.getKey())) {
            dynamicFeeTypesEnums = Arrays.asList(
                DynamicFeeTypesEnum.NAME,
                DynamicFeeTypesEnum.ID_CARD,
                DynamicFeeTypesEnum.UNIT_PENSION_CARDINAL,
                DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT_CARDINAL,
                DynamicFeeTypesEnum.WORK_INJURY_CARDINAL,
                DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL,
                DynamicFeeTypesEnum.UNIT_MATERNITY_CARDINAL,
                DynamicFeeTypesEnum.PERSONAL_PENSION_CARDINAL,
                DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT_CARDINAL,
                DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL_PERSONAL,
                DynamicFeeTypesEnum.PERSONAL_MATERNITY_CARDINAL,
                DynamicFeeTypesEnum.UNIT_PENSION,
                DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT,
                DynamicFeeTypesEnum.UNIT_MEDICAL,
                DynamicFeeTypesEnum.UNIT_INJURY,
                DynamicFeeTypesEnum.UNIT_MATERNITY,
                DynamicFeeTypesEnum.UNIT_LARGE_MEDICAL_EXPENSE,
                DynamicFeeTypesEnum.REPLENISH_WORK_INJURY_EXPENSE,
                DynamicFeeTypesEnum.COMMERCIAL_INSURANCE,
                DynamicFeeTypesEnum.UNIT_ENTERPRISE_ANNUITY,
                DynamicFeeTypesEnum.UNIT_SOCIAL_SECURITY_MAKE_UP,
                DynamicFeeTypesEnum.SUBTOTAL_UNIT_SOCIAL_SECURITY,
                DynamicFeeTypesEnum.PERSONAL_PENSION,
                DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT,
                DynamicFeeTypesEnum.PERSONAL_MEDICAL,
                DynamicFeeTypesEnum.PERSONAL_MATERNITY,
                DynamicFeeTypesEnum.PERSONAL_LARGE_MEDICAL_EXPENSE,
                DynamicFeeTypesEnum.PERSONAL_ENTERPRISE_ANNUITY,
                DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MAKE_UP,
                DynamicFeeTypesEnum.SUBTOTAL_PERSONAL_SOCIAL_SECURITY,
                DynamicFeeTypesEnum.TOTAL_SOCIAL_SECURITY,
                DynamicFeeTypesEnum.ACCUMULATION_FUND_BASE,
                DynamicFeeTypesEnum.UNIT_ACCUMULATION_FUND_HAIER,
                DynamicFeeTypesEnum.PERSONAL_ACCUMULATION_FUND_HAIER,
                DynamicFeeTypesEnum.TOTAL_ACCUMULATION_FUND,
                DynamicFeeTypesEnum.SALARY,
                DynamicFeeTypesEnum.PRE_TAX_SALARY,
                DynamicFeeTypesEnum.PERSONAL_TAX,
                DynamicFeeTypesEnum.REAL_SALARY,
                DynamicFeeTypesEnum.SERVICE_CHARGE,
                DynamicFeeTypesEnum.TOTAL_AMOUNT,
                DynamicFeeTypesEnum.ENTERPRISE_TAX,
                DynamicFeeTypesEnum.SPECIAL_OTHER,
                DynamicFeeTypesEnum.MEMBERSHIP_FEE
            );
        }
        List<EnumDTO> result = new ArrayList<>();
        for (DynamicFeeTypesEnum df : dynamicFeeTypesEnums) {
            EnumDTO enumDTO = new EnumDTO()
                .setKey(df.getKey())
                .setValue(df.getValue());
            result.add(enumDTO);
        }
        return result;
    }

    /**
     * 对账删除字段映射
     *
     * @param billConfigId 对账配置id
     * @param param
     * @return
     */
    @Override
    public List<List<CellItem>> delMappingRelationWithCompare(String billConfigId, HrExpenseManageDTO param) {
        // 获取对账配置
        HrBillCompareConfigDTO configDTO = this.hrBillCompareConfigRepository.findById(billConfigId);
        BillFieldInfoDTO billFieldInfoDTO = configDTO.getBillFieldInfoDTO().orElseThrow(() -> new CommonException("解析配置信息异常!"));

        // 删除字段映射
        this.delFieldMappingRelationCommon(billFieldInfoDTO, param, false);

        // 更新删除后的配置
        configDTO.setConfig(JSON.toJSONString(billFieldInfoDTO, SerializerFeature.DisableCircularReferenceDetect));
        if (configDTO.getType().equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            this.hrBillCompareConfigRepository.updateConfig(configDTO.getConfig(), configDTO.getId());
        } else {
            this.hrBillCompareConfigService.updateHrBillCompareConfig(configDTO);
        }
        return this.getMappingRelationData(billFieldInfoDTO.getMappingFields());
    }

    /**
     * HrExpenseManageDTO 转 CellItem
     *
     * @param expenseManageDTO
     * @return
     */
    private CellItem expenseManage2CellItem(HrExpenseManageDTO expenseManageDTO) {
        CellItem cellItem = new CellItem()
            .setFirstCol(expenseManageDTO.getColIndex())
            .setOrder(expenseManageDTO.getSortValue())
            .setColumnLabel(expenseManageDTO.getColumnLabel())
            .setValue(expenseManageDTO.getCellOriginName())
            .setHasMapping(false);
        return cellItem;
    }


    /**
     * 删除旧的薪酬账单的配置项等
     *
     * @param hrBillList 基础账单
     */
    private void deleteOldDynamicBill(List<HrBill> hrBillList) {
        hrBillList.forEach(hrBill -> {
            String billId = hrBill.getId();
            this.hrBillDynamicFieldsRepository.deleteByBillId(billId);
            this.hrBillDetailItemsRepository.deleteByBillId(billId);
            this.hrBillDetailRepository.deleteByBillId(billId);
            this.hrBillTotalRepository.deleteByBillId(billId);
        });
    }

    /**
     * 获取表单映射数据
     *
     * @param mappingFields
     * @return
     */
    private List<List<CellItem>> getMappingRelationData(List<HrExpenseManageDTO> mappingFields) {
        if (mappingFields == null || mappingFields.isEmpty()) {
            return null;
        }

        mappingFields.sort(Comparator.comparingInt(HrExpenseManageDTO::getSortValue));
        Map<String, List<HrExpenseManageDTO>> map = new HashMap<>();
        List<String> typeNames = new ArrayList<>();
        for (HrExpenseManageDTO expenseManageDTO : mappingFields) {
            List<HrExpenseManageDTO> cellItems = map.get(expenseManageDTO.getExpenseTypeName());
            if (cellItems != null) {
                cellItems.add(expenseManageDTO);
            } else {
                cellItems = new ArrayList<>();
                cellItems.add(expenseManageDTO);
                map.put(expenseManageDTO.getExpenseTypeName(), cellItems);
                typeNames.add(expenseManageDTO.getExpenseTypeName());
            }
        }

        List<CellItem> col1 = new ArrayList<>();
        List<CellItem> col2 = new ArrayList<>();
        List<CellItem> col3 = new ArrayList<>();

        int startCols = 1;

        for (String key : typeNames) {
            List<HrExpenseManageDTO> hrBillDynamicFieldsDTOS = map.get(key);
            int size = hrBillDynamicFieldsDTOS.size();
            CellItem cellItem1 = new CellItem().setValue(key).setFirstCol(startCols).setLastCol(startCols + size - 1);
            col1.add(cellItem1);
            for (HrExpenseManageDTO expenseManageDTO : hrBillDynamicFieldsDTOS) {
                CellItem cellItem2 = new CellItem().setFirstCol(startCols).setLastCol(startCols).setValue(expenseManageDTO.getColumnLabel());
                col2.add(cellItem2);
                CellItem cellItem3 = new CellItem().setFirstCol(startCols).setLastCol(startCols).setValue(expenseManageDTO.getExpenseName()).setOrder(expenseManageDTO.getSortValue());
                col3.add(cellItem3);
                startCols++;
            }
        }

        return Arrays.asList(col1, col2, col3);
    }

    /**
     * 工资账单设置字段映射
     *
     * @param billId 账单id
     * @param param
     */
    @Override
    public List<List<CellItem>> setFieldMappingRelation(String billId, HrExpenseManageDTO param) {
        // 校验参数
        this.checkExpenseManage(param);
        List<HrBill> hrBillList = hrBillRepository.selectByIdOrBillNo(billId);
        if (hrBillList == null || hrBillList.isEmpty()) {
            throw new CommonException("未查询到账单信息！");
        }
        // 获取账单字段数据
        HrBillDynamicFieldsDTO dynamicFieldsDTO = this.hrBillDynamicFieldsRepository.getByBillId(hrBillList.get(0).getId());
        List<List<CellItem>> cellItemList = this.handleFieldMappingRelation(dynamicFieldsDTO, param);
        return cellItemList;
    }

    /**
     * 处理账单字段映射
     *
     * @param dynamicFieldsDTO
     * @param param
     */
    @Override
    public List<List<CellItem>> handleFieldMappingRelation(HrBillDynamicFieldsDTO dynamicFieldsDTO, HrExpenseManageDTO param) {
        BillFieldInfoDTO billFieldInfoDTO = dynamicFieldsDTO.getBillFieldInfoDTO();

        // 保存新的字段映射,同时返回新的数据解析对象
        this.saveAndGetBillFieldInfo(param, billFieldInfoDTO, true, null);

        // 更新账单数据
        dynamicFieldsDTO.setContent(JSON.toJSONString(billFieldInfoDTO, SerializerFeature.DisableCircularReferenceDetect));
        this.updateById(this.hrBillDynamicFieldsMapper.toEntity(dynamicFieldsDTO));

        // 返回前端需要的表单映射数据
        return this.getMappingRelationData(billFieldInfoDTO.getMappingFields());
    }

    /**
     * 保存新的字段映射,同时返回新的数据解析对象
     *
     * @param param
     * @param billFieldInfoDTO
     * @param flag             true--工资账单设置字段映射 false--导盘对账设置字段映射
     * @param configDTO        获取对账配置
     * @return
     */
    private BillFieldInfoDTO saveAndGetBillFieldInfo(HrExpenseManageDTO param, BillFieldInfoDTO billFieldInfoDTO, Boolean flag, HrBillCompareConfigDTO configDTO) {
        List<CellItem> unUsedCols = billFieldInfoDTO.getUnUsedCols();

        // 判断是否已经存在该名称的费用类型,如果没有则新增,如果已存在则修改
        Integer isDefault = null;
        if (configDTO != null && configDTO.getType().equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            isDefault = 1;
            param.setIsDefault(isDefault);
            CellItem item = unUsedCols.stream().filter(cellItem -> param.getExpenseName().equals(cellItem.getValue()) && param.getSortValue().equals(cellItem.getOrder())).findFirst().orElse(null);
            if (item != null){
                param.setParentExpenseName(item.getParentName());
            }
        }

        HrExpenseManageDTO hrExpenseManageDTO = this.hrExpenseManageRepository.getByExpenseName(param, flag ? param.getClientId() : null, isDefault);
        if (hrExpenseManageDTO == null) {
            // 新增字段映射数据
            hrExpenseManageDTO = this.hrExpenseManageService.createHrExpenseManage(param);
        } else {
            // 修改
            if (!hrExpenseManageDTO.getExpenseType().equals(param.getExpenseType())) {
                hrExpenseManageDTO.setExpenseType(param.getExpenseType());
                this.hrExpenseManageService.updateHrExpenseManage(hrExpenseManageDTO);
            }
        }
        // 从未被映射的字段中移除
        // 须确保未映射字段中存在该字段
        boolean exists = false;
        for (CellItem cellItem : unUsedCols) {
            String extName = cellItem.getParentName() + ":" + cellItem.getValue();
            if ((param.getExpenseName().equals(cellItem.getValue()) || param.getExpenseName().equals(extName)) &&
                param.getSortValue().equals(cellItem.getOrder())) {
                exists = true;
                // 设置被添加字段的排序值,列名
                hrExpenseManageDTO.setSortValue(cellItem.getOrder());
                hrExpenseManageDTO.setColumnLabel(cellItem.getColumnLabel());
                hrExpenseManageDTO.setColIndex(cellItem.getFirstCol());
                hrExpenseManageDTO.setCellOriginName(cellItem.getValue());
                // 移除元素
                unUsedCols.remove(cellItem);
                break;
            }
        }
        if (!exists) {
            throw new CommonException("未映射字段中不存在字段[" + param.getExpenseName() + "]" + ",请核对后重新操作!");
        }
        billFieldInfoDTO.setUnUsedCols(unUsedCols);

        // 设置原始表头,该字段为已映射
        List<CellItem> cellItems = billFieldInfoDTO.getCellItems();
        CellItem c = BillParseUtils.getCellItemByNameAndOrder(param.getExpenseName(), param.getSortValue(), cellItems);
        if (c == null) {
            throw new CommonException("原始表头中未获取到列名:" + param.getExpenseName());
        }
        c.setHasMapping(true);

        // 添加新的被映射的字段
        List<HrExpenseManageDTO> mappingFields = billFieldInfoDTO.getMappingFields();
        mappingFields.add(hrExpenseManageDTO);

        billFieldInfoDTO.setMappingFieldTotal(billFieldInfoDTO.getMappingFieldTotal() + 1);
        return billFieldInfoDTO;
    }

    /**
     * 功能描述: 获取未映射的字段列表
     *
     * @param billId 账单id
     * <AUTHOR>
     * @date 2021/11/2 10:35
     */
    @Override
    public List<CellItem> getUnUsedFieldList(String billId) {
        List<HrBill> hrBillList = hrBillRepository.selectByIdOrBillNo(billId);
        if (hrBillList == null || hrBillList.isEmpty()) {
            throw new CommonException("未查询到账单信息！");
        }
        HrBill hrBill = hrBillList.get(0);
        HrBillDynamicFieldsDTO dynamicFieldsDTO = this.hrBillDynamicFieldsRepository.getByBillId(hrBill.getId());
        if (dynamicFieldsDTO == null) {
            throw new CommonException("未查询到薪酬原单！");
        }
        List<CellItem> unUsedFields = dynamicFieldsDTO.getBillFieldInfoDTO().getUnUsedCols();
        if (hrBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            List<CellItem> cellItemList = unUsedFields.stream().filter(lst ->
                lst.getValue().equals(DynamicFeeTypesEnum.EMPLOYEE_COMPENSATION.getValue())
                    || lst.getValue().equals(DynamicFeeTypesEnum.OTHER_EXPENSE.getValue())
                    || lst.getValue().equals("其中")
            ).collect(Collectors.toList());
            unUsedFields.removeAll(cellItemList);
        }
        // 按照order排序
        unUsedFields.sort((x, y) -> Integer.compare(x.getOrder(), y.getOrder()));
        return unUsedFields;
    }

    /**
     * 更新数据开始行
     *
     * @param billId   账单id
     * @param startRow 数据开始行
     */
    @Override
    public void updateStartRowByBillId(String billId, Integer startRow) {
        HrBillDynamicFieldsDTO hrBillDynamicFieldsDTO = this.hrBillDynamicFieldsRepository.getByBillId(billId);
        hrBillDynamicFieldsDTO.setDataStartRows(startRow);
        this.updateById(this.hrBillDynamicFieldsMapper.toEntity(hrBillDynamicFieldsDTO));
    }

    /**
     * 薪酬账单详情页===前端获取动态表头
     *
     * @param billIds 账单id
     * @return
     */
    @Override
    public List<CellItem> getTableDynamicHeader(List<String> billIds) {
        // 获取账单字段配置
        List<HrBillDynamicFieldsDTO> list = this.hrBillDynamicFieldsRepository.getBatchBillId(billIds);
        if (list == null || list.isEmpty()) {
            throw new CommonException("未查询到原始账单明细！");
        }
        HrBillDynamicFieldsDTO billDynamicFieldsDTO = list.get(0);
        // 获取字段信息对象
        BillFieldInfoDTO billFieldInfoDTO = billDynamicFieldsDTO.getBillFieldInfoDTO();

        // 获取动态费用项列表
        DynamicFeeFieldDTO dynamicFeeFieldDTO = BillFieldInfoDTO.getDynamicFeeFieldDTO(billFieldInfoDTO);

        List<HrExpenseManageDTO> addList = dynamicFeeFieldDTO.getAddItems();
        List<HrExpenseManageDTO> reduceList = dynamicFeeFieldDTO.getReduceItems();
        List<HrExpenseManageDTO> otherList = dynamicFeeFieldDTO.getOtherItems();
        List<HrExpenseManageDTO> taxExemptList = dynamicFeeFieldDTO.getTaxExemptItems();
        List<HrExpenseManageDTO> chargeList = dynamicFeeFieldDTO.getChargeItems();
        List<HrExpenseManageDTO> refundList = dynamicFeeFieldDTO.getRefundItems();
        List<HrExpenseManageDTO> employeeCompensationList = dynamicFeeFieldDTO.getEmployeeCompensationList();
        List<HrExpenseManageDTO> unitInsuranceList = dynamicFeeFieldDTO.getUnitInsuranceList();
        List<HrExpenseManageDTO> unitHousingFundList = dynamicFeeFieldDTO.getIndividualHousingFundList();
        List<HrExpenseManageDTO> otherExpenseList = dynamicFeeFieldDTO.getOtherExpenseList();
        List<HrExpenseManageDTO> reimbursementExpenseList = dynamicFeeFieldDTO.getReimbursementExpenseList();
        List<HrExpenseManageDTO> managementExpenseList = dynamicFeeFieldDTO.getManagementExpenseList();
        List<HrExpenseManageDTO> taxesList = dynamicFeeFieldDTO.getTaxesList();
        List<HrExpenseManageDTO> individualInsuranceList = dynamicFeeFieldDTO.getIndividualInsuranceList();
        List<HrExpenseManageDTO> individualHousingFundList = dynamicFeeFieldDTO.getIndividualHousingFundList();
        List<HrExpenseManageDTO> individualIncomeTaxList = dynamicFeeFieldDTO.getIndividualIncomeTaxList();
        List<HrExpenseManageDTO> totalExpensesList = dynamicFeeFieldDTO.getTotalExpensesList();
        List<HrExpenseManageDTO> serviceFeeList = dynamicFeeFieldDTO.getServiceFeeList();

        List<CellItem> cellItems = new ArrayList<>();
        if (addList != null && addList.size() > 0) {
            CellItem cellItem = new CellItem();
            cellItem.setValue(DynamicFeeTypesEnum.FEE_TYPE_ADD.getValue()).setKey(DynamicFeeTypesEnum.FEE_TYPE_ADD.getValue());
            cellItem.setChildren(this.getChildren(addList));
            cellItems.add(cellItem);
        }

        if (reduceList != null && reduceList.size() > 0) {
            CellItem cellItem = new CellItem();
            cellItem.setValue(DynamicFeeTypesEnum.FEE_TYPE_REDUCE.getValue()).setKey(DynamicFeeTypesEnum.FEE_TYPE_REDUCE.getValue());
            cellItem.setChildren(this.getChildren(reduceList));
            cellItems.add(cellItem);
        }

        if (otherList != null && otherList.size() > 0) {
            CellItem cellItem = new CellItem();
            cellItem.setValue(DynamicFeeTypesEnum.FEE_TYPE_OTHER.getValue()).setKey(DynamicFeeTypesEnum.FEE_TYPE_OTHER.getValue());
            cellItem.setChildren(this.getChildren(otherList));
            cellItems.add(cellItem);
        }

        if (taxExemptList != null && taxExemptList.size() > 0) {
            CellItem cellItem = new CellItem();
            cellItem.setValue(DynamicFeeTypesEnum.TAX_EXEMPT_INCOME.getValue()).setKey(DynamicFeeTypesEnum.TAX_EXEMPT_INCOME.getValue());
            cellItem.setChildren(this.getChildren(taxExemptList));
            cellItems.add(cellItem);
        }
        if (chargeList != null && chargeList.size() > 0) {
            CellItem cellItem = new CellItem();
            cellItem.setValue(DynamicFeeTypesEnum.CHARGE_ITEM.getValue()).setKey(DynamicFeeTypesEnum.CHARGE_ITEM.getValue());
            cellItem.setChildren(this.getChildren(chargeList));
            cellItems.add(cellItem);
        }
        if (refundList != null && refundList.size() > 0) {
            CellItem cellItem = new CellItem();
            cellItem.setValue(DynamicFeeTypesEnum.REFUND_ITEM.getValue()).setKey(DynamicFeeTypesEnum.REFUND_ITEM.getValue());
            cellItem.setChildren(this.getChildren(refundList));
            cellItems.add(cellItem);
        }
        if (serviceFeeList != null && serviceFeeList.size() > 0) {
            CellItem cellItem = new CellItem();
            cellItem.setValue(DynamicFeeTypesEnum.SERVICE_CHARGE.getValue()).setKey(DynamicFeeTypesEnum.SERVICE_CHARGE.getValue());
            cellItem.setChildren(this.getChildren(serviceFeeList));
            cellItems.add(cellItem);
        }
        this.handleCellItems(employeeCompensationList, DynamicFeeTypesEnum.EMPLOYEE_COMPENSATION.getValue(), cellItems);
        this.handleCellItems(unitInsuranceList, DynamicFeeTypesEnum.UNIT_INSURANCE.getValue(), cellItems);
        this.handleCellItems(unitHousingFundList, DynamicFeeTypesEnum.UNIT_HOUSING_FUND.getValue(), cellItems);
        this.handleCellItems(otherExpenseList, DynamicFeeTypesEnum.OTHER_EXPENSE.getValue(), cellItems);
        this.handleCellItems(reimbursementExpenseList, DynamicFeeTypesEnum.REIMBURSEMENT_EXPENSE.getValue(), cellItems);
        this.handleCellItems(managementExpenseList, DynamicFeeTypesEnum.MANAGEMENT_EXPENSE.getValue(), cellItems);
        this.handleCellItems(taxesList, DynamicFeeTypesEnum.TAXES.getValue(), cellItems);
        this.handleCellItems(individualInsuranceList, DynamicFeeTypesEnum.INDIVIDUAL_INSURANCE.getValue(), cellItems);
        this.handleCellItems(individualHousingFundList, DynamicFeeTypesEnum.INDIVIDUAL_HOUSING_FUND.getValue(), cellItems);
        this.handleCellItems(individualIncomeTaxList, DynamicFeeTypesEnum.INDIVIDUAL_INCOME_TAX.getValue(), cellItems);
        this.handleCellItems(totalExpensesList, DynamicFeeTypesEnum.TOTAL_EXPENSES.getValue(), cellItems);
        return cellItems;
    }

    private void handleCellItems(List<HrExpenseManageDTO> list, String value, List<CellItem> cellItems) {
        if (list != null && list.size() > 0) {
            CellItem cellItem = new CellItem();
            cellItem.setValue(value).setKey(value);
            cellItem.setChildren(this.getChildren(list));
            cellItems.add(cellItem);
        }
    }

    /**
     * 获取子类
     *
     * @param list
     * @return
     */
    private List<CellItem> getChildren(List<HrExpenseManageDTO> list) {
        List<CellItem> childrens = new ArrayList<>();
        for (HrExpenseManageDTO param : list) {
            CellItem cellItem = new CellItem();
            cellItem.setKey(param.getExpenseName()).setValue(param.getCellOriginName());
            childrens.add(cellItem);
        }
        return childrens;
    }

    /**
     * 解析cells,获取字段映射等信息
     *
     * @param list
     * @param expenseTypes 费用项类型
     * @param clientId     客户ID
     * @param type
     * @return
     */
    private BillFieldInfoDTO getFieldMapping(List<CellItem> list, List<String> expenseTypes, String clientId, Integer type) {

        // 获取所有的映射信息
        List<HrExpenseManageDTO> expenseManageDTOS = new ArrayList<>();
        if (type != null && type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            HrExpenseManageDTO hrExpenseManageDTO = new HrExpenseManageDTO().setExpenseTypeList(expenseTypes).setIsDefault(1);
            expenseManageDTOS = hrExpenseManageRepository.getList(hrExpenseManageDTO);
        } else {
            expenseManageDTOS = this.hrExpenseManageRepository.getListByExpenseType(expenseTypes, clientId);
        }
        int capacity = BillParseUtils.getInitCapacity(expenseManageDTOS.size());
        Map<String, HrExpenseManageDTO> expenseManageMap = new HashMap<>(capacity);
        expenseManageDTOS.forEach(em -> {
            if (type != null && type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
                String topKey = "";
                DynamicFeeTypesEnum enumByKey = EnumUtils.getEnumByKey(DynamicFeeTypesEnum.class, em.getExpenseType());
                switch (enumByKey) {
                    case UNIT_PENSION_CARDINAL:
                    case PERSONAL_PENSION_CARDINAL:
                    case UNIT_PENSION:
                    case PERSONAL_PENSION:
                        topKey = PENSION;
                        break;
                    case MEDICAL_INSURANCE_CARDINAL:
                    case MEDICAL_INSURANCE_CARDINAL_PERSONAL:
                    case UNIT_MEDICAL:
                    case PERSONAL_MEDICAL:
                        topKey = MEDICAL;
                        break;
                    case UNIT_UNEMPLOYMENT_CARDINAL:
                    case PERSONAL_UNEMPLOYMENT_CARDINAL:
                    case UNIT_UNEMPLOYMENT:
                    case PERSONAL_UNEMPLOYMENT:
                        topKey = UNEMPLOYMENT;
                        break;
                    case WORK_INJURY_CARDINAL:
                    case PERSONAL_INJURY_CARDINAL:
                    case UNIT_INJURY:
                    case PERSONAL_INJURY:
                        topKey = INJURY;
                        break;
                    case UNIT_MATERNITY_CARDINAL:
                    case PERSONAL_MATERNITY_CARDINAL:
                    case UNIT_MATERNITY:
                    case PERSONAL_MATERNITY:
                        topKey = MATERNITY;
                        break;
                    case ACCUMULATION_FUND_BASE:
                    case UNIT_ACCUMULATION_FUND:
                    case PERSONAL_ACCUMULATION_FUND:
                        topKey = ACCUMULATION_FUND;
                        break;
                    default:
                        break;
                }
                expenseManageMap.put(em.getParentExpenseName() + "_" +topKey + "_" + em.getExpenseName(), em);
            } else {
                expenseManageMap.put(em.getExpenseName(), em);
            }
        });
        // 记录字段数量和被映射的数量等信息
        BillFieldInfoDTO fieldInfoDTO = new BillFieldInfoDTO();
        AtomicInteger order = new AtomicInteger(0);

        // 解析表头字段
        for (CellItem item : list) {
            this.parseCell(item, expenseManageMap, fieldInfoDTO, order, type);
        }
        fieldInfoDTO.setCellItems(list);

        return fieldInfoDTO;
    }

    /**
     * 单个cell解析
     *
     * @param item             cellItem
     * @param expenseManageMap
     * @param fieldInfoDTO
     * @param order            排序值
     * @param type             对账类型
     */
    private void parseCell(CellItem item, Map<String, HrExpenseManageDTO> expenseManageMap, BillFieldInfoDTO fieldInfoDTO, AtomicInteger order, Integer type) {
        int fieldTotal = fieldInfoDTO.getFieldTotal();
        int mappingFieldTotal = fieldInfoDTO.getMappingFieldTotal();
        List<HrExpenseManageDTO> mappingFields = fieldInfoDTO.getMappingFields();
        List<CellItem> unUsedFields = fieldInfoDTO.getUnUsedCols();
        // 设置字段总数
        fieldInfoDTO.setFieldTotal(fieldTotal + 1);
        // 设置排序值;
        order.incrementAndGet();
        if (type != null && !type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            item.setOrder(order.get());
        }
        HrExpenseManageDTO expenseManageDTO;
        //特殊处理海尔对账表头映射
        if (type != null && type.equals(BillEnum.GuidePlateType.SPECIAL_CLIENT_BILL.getKey())) {
            String topKey = "";
            if ((item.getParentName() != null && item.getParentName().contains(PENSION)) || item.getValue().contains(PENSION)) {
                topKey = PENSION;
            } else if ((item.getParentName() != null && item.getParentName().contains(UNEMPLOYMENT)) || item.getValue().contains(UNEMPLOYMENT)) {
                topKey = UNEMPLOYMENT;
            } else if ((item.getParentName() != null && item.getParentName().contains(INJURY)) || item.getValue().contains(INJURY)) {
                topKey = INJURY;
            } else if ((item.getParentName() != null && item.getParentName().contains(MEDICAL)) || item.getValue().contains(MEDICAL)) {
                topKey = MEDICAL;
            } else if ((item.getParentName() != null && item.getParentName().contains(MATERNITY)) || item.getValue().contains(MATERNITY)) {
                topKey = MATERNITY;
            } else if ((item.getParentName() != null && item.getParentName().contains(ACCUMULATION_FUND)) || item.getValue().contains(ACCUMULATION_FUND)) {
                topKey = ACCUMULATION_FUND;
            }
            expenseManageDTO = expenseManageMap.get(item.getParentName() + "_" +topKey + "_" + item.getValue());
        } else {

            expenseManageDTO = expenseManageMap.get(item.getValue());
        }
        // 注意: 需要同时适配单表格名称和(父级名称+子集名称)的映射
        if (expenseManageDTO == null && StringUtils.isNotEmpty(item.getParentName())) {
            expenseManageDTO = expenseManageMap.get(CellItem.getExtName(item));
        }

        // 判断是否为最底层的表头
        boolean isBottomItem = item.getChildren() == null || item.getChildren().isEmpty();
        if (expenseManageDTO != null && isBottomItem) {
            // 如果存在表头名称完全一致的excel,并且该字段有映射配置,那么此时获取到的可能是上一个cell的值,需要做判断,否则会导致多个列使用了相同的对象
            if (StringUtils.isNotEmpty(expenseManageDTO.getColumnLabel())) {
                expenseManageDTO = new HrExpenseManageDTO()
                    .setExpenseName(expenseManageDTO.getExpenseName())
                    .setExpenseType(expenseManageDTO.getExpenseType())
                    .setExpenseTypeName(expenseManageDTO.getExpenseTypeName());
            }
            // 设置已映射字段总数
            fieldInfoDTO.setMappingFieldTotal(mappingFieldTotal + 1);
            // 设置类型为已映射
            item.setHasMapping(true);
            // CellItem转HrExpenseDTO
            this.cellItem2ExpenseManage(expenseManageDTO, item);

            mappingFields.add(expenseManageDTO);
        } else {
            item.setHasMapping(false);
            unUsedFields.add(item);
        }
        fieldInfoDTO.setMappingFields(mappingFields)
            .setUnUsedCols(unUsedFields);
        // 设置其子类
        if (item.getChildren() != null && item.getChildren().size() > 0) {
            List<CellItem> children = item.getChildren();
            for (CellItem cellItem : children) {
                cellItem.setParentName(item.getValue());
                this.parseCell(cellItem, expenseManageMap, fieldInfoDTO, order, type);
            }
        }
    }

    /**
     * CellItem转HrExpenseDTO
     *
     * @param expenseManageDTO
     * @param item
     */
    private void cellItem2ExpenseManage(HrExpenseManageDTO expenseManageDTO, CellItem item) {
        // 注意: item的value(excel原始的值=cellOriginName)不能直接赋值给expenseManage的expenseName(可能是excel原始值,也可能是父级+子集)
        expenseManageDTO.setSortValue(item.getOrder());
        expenseManageDTO.setColumnLabel(item.getColumnLabel());
        expenseManageDTO.setColIndex(item.getFirstCol());
        expenseManageDTO.setCellOriginName(item.getValue());
        expenseManageDTO.setNextValue(item.getNextValue());
    }


}
