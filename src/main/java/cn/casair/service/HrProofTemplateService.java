package cn.casair.service;

import cn.casair.domain.HrProofTemplate;
import cn.casair.dto.HrProofTemplateDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 证明模板服务类
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
public interface HrProofTemplateService extends IService<HrProofTemplate> {


    /**
     * 创建证明模板
     *
     * @param hrProofTemplateDTO
     * @return
     */
    HrProofTemplateDTO createHrProofTemplate(HrProofTemplateDTO hrProofTemplateDTO);

    /**
     * 修改证明模板
     *
     * @param hrProofTemplateDTO
     * @return
     */
    Optional<HrProofTemplateDTO> updateHrProofTemplate(HrProofTemplateDTO hrProofTemplateDTO);

    /**
     * 查询证明模板详情
     *
     * @param id
     * @return
     */
    HrProofTemplateDTO getHrProofTemplate(String id);

    /**
     * 删除证明模板
     *
     * @param id
     */
    void deleteHrProofTemplate(String id);

    /**
     * 批量删除证明模板
     *
     * @param ids
     */
    void deleteHrProofTemplate(List<String> ids);

    /**
     * 分页查询证明模板
     *
     * @param hrProofTemplateDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrProofTemplateDTO hrProofTemplateDTO, Long pageNumber, Long pageSize);

    /**
     * 批量下载
     * @param hrProofTemplateDTO
     * @return
     */
    String batchDownload(HrProofTemplateDTO hrProofTemplateDTO);

    /**
     * 查询所有证明模板
     * @return
     */
    List<HrProofTemplateDTO> getHrProofTemplateList();

    /**
     * 处理证明模板
     * @param hrProofTemplateDTO 证明模板
     * @param staffId 员工信息
     * @return
     */
    Map<String, Object> proofTemplatePretreatment(HrProofTemplateDTO hrProofTemplateDTO, String staffId);

}
