<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrPlatformAccountRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, account_type, Issuing_bank, account_number, `password`,
        is_delete"utf-8",created_by"utf-8",last_modified_by"utf-8",created_date"utf-8",last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrPlatformAccount">
        <id column="id" property="id"/>
        <result column="account_type" property="accountType"/>
        <result column="Issuing_bank" property="issuingBank"/>
        <result column="account_number" property="accountNumber"/>
        <result column="password" property="password"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectClientPlatformAccount" resultType="cn.casair.domain.HrPlatformAccount">
        SELECT
            hpa.*
        FROM
            hr_platform_account hpa
            LEFT JOIN hr_client hc ON hc.payroll_account_id = hpa.id
        WHERE
            hc.id = #{clientId}
            AND hpa.platform_type = 4
    </select>
    <select id="findListByPlatformType" resultType="cn.casair.dto.HrPlatformAccountDTO">
        SELECT
        hpa.*
        FROM
        hr_platform_account hpa
        WHERE
        hpa.is_delete = 0
        <if test="platformType != null and platformType != '' ">
            AND hpa.platform_type = #{platformType}
        </if>
        AND !ISNULL(hpa.account_number)
        AND !ISNULL(hpa.Issuing_bank)
    </select>
</mapper>
