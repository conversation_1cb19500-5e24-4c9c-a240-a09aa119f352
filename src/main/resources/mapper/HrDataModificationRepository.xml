<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrDataModificationRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, apply_content, apply_status, apply_type,modification_content,see_status,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrDataModification">
        <id column="id" property="id"/>
        <result column="apply_content" property="applyContent"/>
        <result column="apply_status" property="applyStatus"/>
        <result column="apply_type" property="applyType"/>
        <result column="modification_content" property="modificationContent"/>
        <result column="see_status" property="seeStatus"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="findSQL">
        SELECT
            hdm.*, hc.unit_number,
            hc.client_name,
            hts.system_num,
            hts.`name`,
            hts.staff_status staffStatus,
            hts.certificate_num,
            hts.phone
        FROM
            hr_data_modification hdm
        LEFT JOIN hr_talent_staff hts ON hdm.staff_id = hts.id
        LEFT JOIN hr_client hc ON hdm.client_id = hc.id
    </sql>

    <select id="findById" resultType="cn.casair.dto.HrDataModificationDTO">
        <include refid="findSQL"/>
        WHERE hdm.is_delete = 0 AND hts.is_delete = 0 AND hdm.id = #{id}
    </select>

    <sql id="Select_Sql">
        <include refid="findSQL"/>
        WHERE hdm.is_delete = 0 AND hts.is_delete = 0
        <if test="param.ids != null and param.ids.size() > 0">
            and hdm.id in
            <foreach collection="param.ids" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param.name != null and param.name != ''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND hts.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.systemNum != null and param.systemNum != ''">
            AND hts.system_num LIKE concat('%', #{param.systemNum}, '%')
        </if>
        <if test="param.clientId != null">
            AND hdm.client_id = #{param.clientId}
        </if>
        <if test="param.createdDateStart!=null">
            AND hdm.created_date &gt;= #{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd!=null">
            AND hdm.created_date &lt; date_add(#{param.createdDateEnd}, interval 1 day)
        </if>
        <if test="param.applyContent != null and param.applyContent != ''">
            AND hdm.apply_content LIKE concat('%', #{param.applyContent}, '%')
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hc.id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.applyStatusList!=null and param.applyStatusList.size() > 0">
            AND hdm.apply_status IN
            <foreach collection="param.applyStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.applyStatusList!=null and param.applyStatusList.size() > 0">
            AND hdm.apply_status IN
            <foreach collection="param.applyStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.applyTypeList!=null and param.applyTypeList.size() > 0">
            AND hdm.apply_type IN
            <foreach collection="param.applyTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.staffStatusList!=null and param.staffStatusList.size() > 0">
            AND hts.staff_status IN
            <foreach collection="param.staffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            ORDER BY ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY hdm.created_date DESC
        </if>
    </sql>
    <select id="findPage" resultType="cn.casair.dto.HrDataModificationDTO">
        <include refid="Select_Sql"></include>
    </select>

    <select id="findList" resultType="cn.casair.dto.HrDataModificationDTO">
        <include refid="Select_Sql"></include>
    </select>

</mapper>
