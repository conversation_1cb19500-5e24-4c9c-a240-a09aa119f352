package cn.casair.repository;

import cn.casair.domain.HrWelfareCompensation;
import cn.casair.dto.HrWelfareAdditionDTO;
import cn.casair.dto.HrWelfareCompensationDTO;
import cn.casair.dto.HrWelfareCompensationStatistics;
import cn.casair.dto.excel.HrWelfareCompensationExport;
import cn.casair.dto.report.ReportQueryParam;
import cn.casair.dto.report.TaxSummaryStaffDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 员工福利补差统计数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
@Repository
public interface HrWelfareCompensationRepository extends BaseMapper<HrWelfareCompensation> {


    /**
     * 查询员工个税
     *
     * @param reportQueryParam
     * @return
     */
    List<TaxSummaryStaffDTO> selectTaxSummaryStaff(@Param("params") ReportQueryParam reportQueryParam);

    /**
     * 删除数据
     *
     * @param hrWelfareCompensation
     * @return
     */
    int delByObject(@Param("params") HrWelfareCompensation hrWelfareCompensation);

    /**
     * export
     *
     * @param staffId
     * @return
     */
    List<HrWelfareCompensationExport> exportWelfareCompensation(@Param("staffId") String staffId);

    /**
     * 获取员工福利补差列表
     *
     * @param hrWelfareCompensationDTO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrWelfareCompensationDTO>
     * <AUTHOR>
     * @date 2021/12/18
     **/
    List<HrWelfareCompensationDTO> getStaffWelfareCompensation(@Param("params") HrWelfareCompensationDTO hrWelfareCompensationDTO);

    /**
     * 获取补差社保
     *
     * @param ids
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021/12/13
     **/
    List<String> selectSocialSecurityIdListByIds(@Param("ids") List<String> ids);

    /**
     * 获取补差公积金
     *
     * @param ids
     * <AUTHOR>
     * @return java.util.List<java.lang.String>
     * @date 2021/12/10
     **/
    List<String> selectAccumulationFundIdListByIds(@Param("ids") List<String> ids);

    /**
     * 获取员工ids
     *
     * @param ids
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021/12/10
     **/
    List<String> selectStaffIdListByIds(@Param("ids") List<String> ids);

    /**
     * 获取补差统计详情
     *
     * @param ids
     * @return cn.casair.domain.HrWelfareCompensation
     * <AUTHOR>
     * @date 2021/12/6
     **/
    List<HrWelfareCompensation> selectByIds(@Param("ids") List<String> ids);

    /**
     * 更新补差使用状态
     *
     * @param billList
     * @param isUsed
     * @return void
     * <AUTHOR>
     * @date 2021/11/10
     **/
    void updateIsUsed(@Param("billList") List<String> billList, @Param("isUsed") int isUsed);

    /**
     * 补差确认
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @date 2021/11/10
     **/
    int updateConfirmByIds(@Param("ids") List<String> ids);

    /**
     * 分页查询员工福利补差
     *
     * @param page
     * @param hrWelfareCompensationDTO
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrWelfareCompensationDTO>
     * <AUTHOR>
     * @date 2021/10/25
     **/
    IPage<HrWelfareCompensationDTO> findPage(Page<HrWelfareCompensation> page, @Param("params") HrWelfareCompensationDTO hrWelfareCompensationDTO);

    /**
     * 获取本月未使用补差记录
     *
     * @param hrWelfareCompensation
     * @return cn.casair.domain.HrWelfareCompensation
     * <AUTHOR>
     * @date 2021/10/28
     **/
    List<HrWelfareCompensation> selectListByObject(HrWelfareCompensation hrWelfareCompensation);

    /**
     * 获取员工缴费年月补差列表
     *
     * @param idNo
     * @return java.util.List<cn.casair.domain.HrWelfareCompensation>
     * <AUTHOR>
     * @date 2021/11/2
     **/
    List<HrWelfareCompensation> getStaffWelfareCompensationByPaymentDate(@Param("billType") Integer billType, @Param("idNo") String idNo);

    /**
     * 获取员工福利补差统计
     *
     * @param clientId
     * @param staffId
     * @return cn.casair.dto.HrWelfareCompensationStatistics
     * <AUTHOR>
     * @date 2021/11/2
     **/
    HrWelfareCompensationStatistics getStaffMakeUpStatistics(@Param("clientId") String clientId, @Param("staffId") String staffId);

    /**
     * 分页查询薪金税差
     *
     * @param page
     * @param hrWelfareCompensationDTO
     * @param clientIds
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrWelfareCompensationDTO>
     * <AUTHOR>
     * @date 2021/11/10
     **/
    IPage<HrWelfareCompensationDTO> findSalaryTaxPage(Page<HrWelfareCompensationDTO> page, @Param("params") HrWelfareCompensationDTO hrWelfareCompensationDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 福利补差详情
     *
     * @param id
     * @return cn.casair.dto.HrWelfareAdditionDTO
     * <AUTHOR>
     * @date 2021/11/11
     **/
    HrWelfareAdditionDTO getSalaryTaxDetail(String id);

    /**
     * 获取薪金税差列表
     *
     * @param hrWelfareCompensationDTO
     * @return java.util.List<cn.casair.dto.HrWelfareCompensationDTO>
     * <AUTHOR>
     * @date 2021/11/11
     **/
    List<HrWelfareCompensationDTO> selectSalaryTaxList(@Param("params") HrWelfareCompensationDTO hrWelfareCompensationDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 查询账单对应的指定使用状态的补差数据
     * @param billId 账单id
     * @param useStatus 使用状态列表
     * @return
     */
    List<HrWelfareCompensationDTO> getByBillIdAndIsUsed(@Param("billId") String billId,@Param("type") Integer type, @Param("useStatus") List<Integer> useStatus);

    /**
     *  根据账单和导盘类型删除未使用的补差
     * @param billId 账单id
     * @param type 导盘类型
     * @param isDelete 删除标识
     * @param billConfigIds 对账配置ID
     */
    void delByBillIdAndIsUsed(@Param("billId") String billId, @Param("type") Integer type, @Param("isDelete") Integer isDelete,@Param("billConfigIds") List<String> billConfigIds);

    void delByResultId(@Param("billResultIds") List<String> billResultIds, @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 获取所有未使用的补差
     * @param billType
     * @param paymentDate
     * @return
     */
    List<HrWelfareCompensation> getUnUsedWelfareCompensation(@Param("billType") Integer billType, @Param("paymentDate") String paymentDate);

    /**
     * 删除数据
     * @param dataList
     */
    void deleteBatch(@Param("list") List<HrWelfareCompensation> dataList);

    /**
     * 根据对账结果ID查询补差列表
     * @param billResultIds 对账结果ID
     * @param type 对账类型
     * @param isUsed 使用状态 0未使用 1已使用
     * @return 补差列表
     */
    List<HrWelfareCompensationDTO> getUsedMakeUpByResultId(@Param("billResultIds") List<String> billResultIds, @Param("type") Integer type, @Param("isUsed") Integer isUsed);

}
