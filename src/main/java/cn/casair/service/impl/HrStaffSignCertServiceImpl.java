package cn.casair.service.impl;

import cn.casair.common.Constants;
import cn.casair.common.enums.CompanyInfoEnum;
import cn.casair.domain.HrStaffSignCert;
import cn.casair.dto.HrStaffSignCertDTO;
import cn.casair.mapper.HrStaffSignCertMapper;
import cn.casair.repository.HrStaffSignCertRepository;
import cn.casair.service.HrStaffSignCertService;
import cn.casair.service.component.ecloud.ECloudComponent;
import com.agile.ecloud.sdk.bean.ECloudDomain;
import com.agile.ecloud.sdk.http.EcloudClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * 入职员工签名证书（易云章）服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrStaffSignCertServiceImpl extends ServiceImpl<HrStaffSignCertRepository, HrStaffSignCert> implements HrStaffSignCertService {

    private final ECloudComponent eCloudComponent;
    private final HrStaffSignCertRepository hrStaffSignCertRepository;
    private final HrStaffSignCertMapper hrStaffSignCertMapper;

    @Override
    public void checkAndGetStaffSignCert(String staffId, String clientId, String staffName, String idNo, String phone) {
        // 检查企业用户是否创建
        ECloudDomain companyReturnData = eCloudComponent.getCertInfo("2", "4", CompanyInfoEnum.BUSINESS_NUM.getValue());
        if (!Constants.SUCCESS_CODE.equals(companyReturnData.getCode())) {
            EcloudClient.applyCert("2", "4", CompanyInfoEnum.BUSINESS_NUM.getValue(), CompanyInfoEnum.FIRST_PART_NAME.getValue(), CompanyInfoEnum.FIRST_PART_PHONE.getValue());
        }

        // 检查易云章是否存在用户已注册信息
        ECloudDomain returnData = eCloudComponent.getCertInfo("1", "0", idNo);
        log.info("易云章用户证书信息：{}", JSON.toJSONString(returnData));

        JSONObject jsonObject;
        String userName;
        String userNum;
        String serialNumber;
        String issuer;
        String certNotAfter;
        String certNotBefore;
        if (!Constants.SUCCESS_CODE.equals(returnData.getCode())) {
            // 未注册
            // this.eCloudComponent.internalThreeElementsCheck(idNo, phone, staffName);
            jsonObject = this.eCloudComponent.applyCert(idNo, phone, staffName);
        } else {
            // 已注册
            jsonObject = JSONObject.parseObject(JSONObject.toJSONString(returnData.getData()));
        }

        String userNumCheck = jsonObject.getString("userNum");
        if (!userNumCheck.equals(phone)) {
            jsonObject = this.eCloudComponent.updateCertUserPhone(idNo, staffName, phone);
        }
        String userNameCheck = jsonObject.getString("userName");
        if (!userNameCheck.equals(staffName)) {
            jsonObject = this.eCloudComponent.updateCertUserName(idNo, staffName, phone);
        }

        userName = jsonObject.getString("userName");
        userNum = jsonObject.getString("userNum");
        serialNumber = jsonObject.getString("serialNumber");
        issuer = jsonObject.getString("issuer");
        certNotAfter = jsonObject.getString("certNotAfter");
        certNotBefore = jsonObject.getString("certNotBefore");

        HrStaffSignCert hrStaffSignCert = new HrStaffSignCert();
        hrStaffSignCert.setStaffId(staffId);
        hrStaffSignCert.setType(1);
        hrStaffSignCert.setCardType(0);
        hrStaffSignCert.setIdCardNum(idNo);
        hrStaffSignCert.setName(staffName);
        hrStaffSignCert.setMobilePhone(phone);
        hrStaffSignCert.setState(1);
        hrStaffSignCert.setIssuer(issuer);
        hrStaffSignCert.setSerialNumber(serialNumber);
        hrStaffSignCert.setCertNotBefore(LocalDate.parse(certNotBefore, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        hrStaffSignCert.setCertNotAfter(LocalDate.parse(certNotAfter, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        hrStaffSignCert.setUserName(userName);
        hrStaffSignCert.setUserNum(userNum);

        HrStaffSignCert hrStaffSignCertOld = this.hrStaffSignCertRepository.getByStaffId(staffId);
        if (hrStaffSignCertOld == null) {
            this.hrStaffSignCertRepository.insert(hrStaffSignCert);
        } else {
            hrStaffSignCert.setId(hrStaffSignCertOld.getId());
            this.hrStaffSignCertRepository.updateById(hrStaffSignCert);
        }
    }

    /**
     * 创建入职员工签名证书（易云章）
     *
     * @param hrStaffSignCertDTO
     * @return
     */
    @Override
    public HrStaffSignCertDTO createHrStaffSignCert(HrStaffSignCertDTO hrStaffSignCertDTO) {
        log.info("Create new HrStaffSignCert:{}", hrStaffSignCertDTO);

        HrStaffSignCert hrStaffSignCert = this.hrStaffSignCertMapper.toEntity(hrStaffSignCertDTO);
        this.hrStaffSignCertRepository.insert(hrStaffSignCert);
        return this.hrStaffSignCertMapper.toDto(hrStaffSignCert);
    }

    /**
     * 修改入职员工签名证书（易云章）
     *
     * @param hrStaffSignCertDTO
     * @return
     */
    @Override
    public Optional<HrStaffSignCertDTO> updateHrStaffSignCert(HrStaffSignCertDTO hrStaffSignCertDTO) {
        return Optional.ofNullable(this.hrStaffSignCertRepository.selectById(hrStaffSignCertDTO.getId()))
                .map(roleTemp -> {
                    HrStaffSignCert hrStaffSignCert = this.hrStaffSignCertMapper.toEntity(hrStaffSignCertDTO);
                    this.hrStaffSignCertRepository.updateById(hrStaffSignCert);
                    log.info("Update HrStaffSignCert:{}", hrStaffSignCertDTO);
                    return hrStaffSignCertDTO;
                });
    }

    /**
     * 查询入职员工签名证书（易云章）详情
     *
     * @param id
     * @return
     */
    @Override
    public HrStaffSignCertDTO getHrStaffSignCert(String id) {
        log.info("Get HrStaffSignCert :{}", id);

        HrStaffSignCert hrStaffSignCert = this.hrStaffSignCertRepository.selectById(id);
        return this.hrStaffSignCertMapper.toDto(hrStaffSignCert);
    }

    /**
     * 删除入职员工签名证书（易云章）
     *
     * @param id
     */
    @Override
    public void deleteHrStaffSignCert(String id) {
        Optional.ofNullable(this.hrStaffSignCertRepository.selectById(id))
                .ifPresent(hrStaffSignCert -> {
                    this.hrStaffSignCertRepository.deleteById(id);
                    log.info("Delete HrStaffSignCert:{}", hrStaffSignCert);
                });
    }

    /**
     * 批量删除入职员工签名证书（易云章）
     *
     * @param ids
     */
    @Override
    public void deleteHrStaffSignCert(List<String> ids) {
        log.info("Delete HrStaffSignCerts:{}", ids);
        this.hrStaffSignCertRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询入职员工签名证书（易云章）
     *
     * @param hrStaffSignCertDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrStaffSignCertDTO hrStaffSignCertDTO, Long pageNumber, Long pageSize) {
        Page<HrStaffSignCert> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrStaffSignCert> qw = new QueryWrapper<>(this.hrStaffSignCertMapper.toEntity(hrStaffSignCertDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrStaffSignCertRepository.selectPage(page, qw);
        iPage.setRecords(this.hrStaffSignCertMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    private void updateHrStaffSignCertByEcloud(String name, JSONObject jsonObject, HrStaffSignCert hrStaffSignCert) {
        if (jsonObject != null) {
            hrStaffSignCert.setName(name);
            hrStaffSignCert.setIssuer(jsonObject.getString("issuer"));
            hrStaffSignCert.setSerialNumber(jsonObject.getString("serialNumber"));
            hrStaffSignCert.setCertNotBefore(LocalDate.parse(jsonObject.getString("certNotBefore"), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            hrStaffSignCert.setCertNotAfter(LocalDate.parse(jsonObject.getString("certNotAfter"), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            hrStaffSignCert.setUserName(jsonObject.getString("userName"));
            hrStaffSignCert.setUserNum(jsonObject.getString("userNum"));
            this.hrStaffSignCertRepository.updateById(hrStaffSignCert);
        }
    }

    @Override
    public void updateCertUserName(String idNo, String name, String phone, HrStaffSignCert hrStaffSignCert) {
        JSONObject jsonObject = this.eCloudComponent.updateCertUserName(idNo, name, phone);
        this.updateHrStaffSignCertByEcloud(name, jsonObject, hrStaffSignCert);
    }

    @Override
    public void updateCertUserPhone(String idNo, String name, String phone, HrStaffSignCert hrStaffSignCert) {
        this.eCloudComponent.updateCertUserPhone(idNo, name, phone);
        hrStaffSignCert.setMobilePhone(phone);
        hrStaffSignCert.setUserNum(phone);
        this.hrStaffSignCertRepository.updateById(hrStaffSignCert);
    }

    @Override
    public HrStaffSignCert getByStaffId(String staffId) {
        return this.hrStaffSignCertRepository.getByStaffId(staffId);
    }

}
