<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.CodeTableRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, inner_name, item_name, item_value, display_order, item_status, item_level, item_type, value_type, item_json_value, iz_default,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.CodeTable">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="inner_name" property="innerName"/>
        <result column="item_name" property="itemName"/>
        <result column="item_value" property="itemValue"/>
        <result column="display_order" property="displayOrder"/>
        <result column="item_status" property="itemStatus"/>
        <result column="item_level" property="itemLevel"/>
        <result column="item_type" property="itemType"/>
        <result column="value_type" property="valueType"/>
        <result column="item_json_value" property="itemJsonValue"/>
        <result column="iz_default" property="izDefault"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getItemByBankName" resultType="cn.casair.domain.CodeTable">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            code_table
        WHERE
            is_delete = 0
            AND parent_id = ( SELECT id FROM code_table WHERE inner_name = 'ownedBank' )
            AND item_name = #{bankName}
    </select>

    <select id="getCodeTableListByInnerName" resultType="cn.casair.dto.CodeTableDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        code_table ct
        WHERE
        ct.parent_id IN ( SELECT id FROM code_table WHERE inner_name = #{innerName} )
        AND ct.is_delete = 0
        ORDER BY
        display_order
    </select>

    <select id="getCodeTableSpecialByInnerName" resultType="cn.casair.dto.CodeTableDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            code_table ct
        WHERE
            ct.parent_id IN ( SELECT id FROM code_table WHERE inner_name = #{innerName} )
          AND ct.is_delete = 0
        ORDER BY
            display_order
    </select>

    <select id="getMaxChildren" resultType="cn.casair.dto.CodeTableDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            code_table
        WHERE
            parent_id = #{parentId}
          AND is_delete = 0
        ORDER BY
            display_order DESC,
            created_date DESC
        LIMIT 1
    </select>

    <select id="findPage" resultType="cn.casair.dto.CodeTableDTO">
        SELECT
        any_value ( cta.id ) id,
        any_value ( cta.parent_id ) parent_id,
        any_value ( cta.inner_name ) inner_name,
        any_value ( cta.item_name ) item_name,
        any_value ( cta.display_order ) display_order,
        any_value ( cta.item_status ) item_status,
        any_value ( cta.iz_default ) iz_default,
        count( cta.id ) count_num
        FROM
        code_table cta
        LEFT JOIN code_table ctb ON ctb.parent_id = cta.id AND ctb.is_delete = 0
        WHERE
        cta.is_delete = 0
        AND cta.parent_id = 0
        <if test="params.itemName!=null and params.itemName!=''">
            AND cta.item_name LIKE CONCAT('%', #{params.itemName}, '%')
        </if>
        GROUP BY
        ctb.parent_id
        <choose>
            <when test="params.field!=null and params.field!=''">
                ORDER BY ${params.field} ${params.order}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
    </select>

    <select id="getChildrenByParentId" resultType="cn.casair.dto.CodeTableDTO">
        SELECT any_value(cta.id)            id,
               any_value(cta.parent_id)     parent_id,
               any_value(cta.item_name)     item_name,
               any_value(cta.item_value)    item_value,
               any_value(cta.display_order) display_order,
               any_value(cta.item_status)   item_status,
               any_value(cta.iz_default)    iz_default,
               count(ctb.id)                countNum
        FROM code_table cta
                 LEFT JOIN code_table ctb ON ctb.parent_id = cta.id AND ctb.is_delete = 0
        WHERE cta.is_delete = 0
          AND cta.parent_id = #{parentId}
        GROUP BY cta.id
        ORDER BY display_order,
                 id
    </select>

    <select id="getListById" resultType="cn.casair.dto.CodeTableDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        code_table
        WHERE
        is_delete = 0
        AND (id = #{id} OR parent_id = #{id})
    </select>

    <select id="getChildrenListByParentId" resultType="cn.casair.dto.CodeTableDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        code_table
        WHERE
        is_delete = 0
        AND parent_id = #{parentId}
        ORDER BY
        display_order
    </select>

    <select id="getItemByParentIdAndItemName" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM code_table
        WHERE is_delete = 0
          AND parent_id = #{parentId}
          AND item_name = #{itemName}
    </select>

    <select id="uniqueVerification" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            code_table
        WHERE
            is_delete = 0
          AND parent_id = #{parentId}
          AND item_name = #{itemName}
          AND id != #{id}
    </select>

    <select id="getValueByInnerName" resultType="java.lang.Integer">
        SELECT item_value
        FROM code_table
        WHERE is_delete = 0
          AND inner_name = #{innerName}
        ORDER BY created_date DESC
        LIMIT 1
    </select>

    <select id="getMaxChildrenByInnerName" resultType="cn.casair.domain.CodeTable">
        SELECT
            *
        FROM
            code_table
        WHERE
            parent_id = ( SELECT id FROM code_table WHERE inner_name = 'ownedBank' )
        ORDER BY
            display_order DESC
        LIMIT 1
    </select>

    <select id="findSinopecBillCode" resultType="cn.casair.dto.CodeTableDTO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            code_table ct
        WHERE
            ct.is_delete = 0
            AND ct.item_value > #{itemValue}
            AND ct.parent_id IN ( SELECT id FROM code_table WHERE inner_name = #{expensetype} )
        ORDER BY
            display_order
    </select>

</mapper>
