package cn.casair.service;

import cn.casair.domain.HrStation;
import cn.casair.dto.HrStationDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 岗位服务类
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface HrStationService extends IService<HrStation> {


    /**
     * 创建岗位
     *
     * @param hrStationDTO
     * @return
     */
    HrStationDTO createHrStation(HrStationDTO hrStationDTO);

    /**
     * 修改岗位
     *
     * @param hrStationDTO
     * @return
     */
    Optional<HrStationDTO> updateHrStation(HrStationDTO hrStationDTO);

    /**
     * 查询岗位详情
     *
     * @param id
     * @return
     */
    HrStationDTO getHrStation(String id);

    /**
     * 删除岗位
     *
     * @param id
     */
    void deleteHrStation(String id);

    /**
     * 批量删除岗位
     *
     * @param ids
     */
    void deleteHrStation(List<String> ids);

    /**
     * 分页查询岗位
     *
     * @param hrStationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrStationDTO hrStationDTO, Long pageNumber, Long pageSize);

    /**
     * 查询所有岗位
     * @return
     */
    List<HrStationDTO> selectList();

    /**
     * 岗位导入信息
     * @param file
     * @return
     */
    String importStation(MultipartFile file);

    /**
     * 岗位导入模板
     * @param response
     */
    String importStationTemplate(HttpServletResponse response);

    /**
     * 导出岗位管理
     * @param hrStationDTO
     * @param httpServletResponse
     * @return
     */
    String exportStation(HrStationDTO hrStationDTO, HttpServletResponse httpServletResponse);
}
