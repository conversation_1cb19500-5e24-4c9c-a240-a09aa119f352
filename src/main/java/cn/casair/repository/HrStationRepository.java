package cn.casair.repository;
import cn.casair.domain.HrLaborAppraisal;
import cn.casair.domain.HrStation;
import cn.casair.dto.excel.HrStationTemplate;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 岗位数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Repository
public interface HrStationRepository extends BaseMapper<HrStation> {

    /**
     * 导出
     * @param qw
     * @return
     */
    List<HrStationTemplate> findList(@Param(Constants.WRAPPER) QueryWrapper<HrStation> qw);

    /**
     * 查询
     *
     * @param professionName
     * @return
     */
    HrStation selectByName(@Param("professionName") String professionName);
}
