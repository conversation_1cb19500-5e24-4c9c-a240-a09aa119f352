package cn.casair.service.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrMedicalInsurance;
import cn.casair.dto.HrMedicalInsuranceDTO;
import cn.casair.repository.HrMedicalInsuranceRepository;
import cn.casair.mapper.HrMedicalInsuranceMapper;
import cn.casair.service.HrMedicalInsuranceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;
/**
 * 社保医保公积金账户表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrMedicalInsuranceServiceImpl extends ServiceImpl<HrMedicalInsuranceRepository, HrMedicalInsurance>implements HrMedicalInsuranceService {

    private static final Logger log=LoggerFactory.getLogger(HrMedicalInsuranceServiceImpl.class);

    private final HrMedicalInsuranceRepository hrMedicalInsuranceRepository;

    private final HrMedicalInsuranceMapper hrMedicalInsuranceMapper;

    public HrMedicalInsuranceServiceImpl(HrMedicalInsuranceRepository hrMedicalInsuranceRepository, HrMedicalInsuranceMapper hrMedicalInsuranceMapper){
    this.hrMedicalInsuranceRepository = hrMedicalInsuranceRepository;
    this.hrMedicalInsuranceMapper= hrMedicalInsuranceMapper;
    }

    /**
     * 创建社保医保公积金账户表
     * @param hrMedicalInsuranceDTO
     * @return
     */
    @Override
    public HrMedicalInsuranceDTO createHrMedicalInsurance(HrMedicalInsuranceDTO hrMedicalInsuranceDTO){
    log.info("Create new HrMedicalInsurance:{}", hrMedicalInsuranceDTO);

    HrMedicalInsurance hrMedicalInsurance =this.hrMedicalInsuranceMapper.toEntity(hrMedicalInsuranceDTO);
    this.hrMedicalInsuranceRepository.insert(hrMedicalInsurance);
    return this.hrMedicalInsuranceMapper.toDto(hrMedicalInsurance);
    }

    /**
     * 修改社保医保公积金账户表
     * @param hrMedicalInsuranceDTO
     * @return
     */
    @Override
    public Optional<HrMedicalInsuranceDTO>updateHrMedicalInsurance(HrMedicalInsuranceDTO hrMedicalInsuranceDTO){
    return Optional.ofNullable(this.hrMedicalInsuranceRepository.selectById(hrMedicalInsuranceDTO.getId()))
    .map(roleTemp->{
    HrMedicalInsurance hrMedicalInsurance =this.hrMedicalInsuranceMapper.toEntity(hrMedicalInsuranceDTO);
    this.hrMedicalInsuranceRepository.updateById(hrMedicalInsurance);
    log.info("Update HrMedicalInsurance:{}", hrMedicalInsuranceDTO);
    return hrMedicalInsuranceDTO;
    });
    }

    /**
     * 查询社保医保公积金账户表详情
     * @param id
     * @return
     */
    @Override
    public HrMedicalInsuranceDTO getHrMedicalInsurance(String id){
    log.info("Get HrMedicalInsurance :{}",id);

    HrMedicalInsurance hrMedicalInsurance =this.hrMedicalInsuranceRepository.selectById(id);
    return this.hrMedicalInsuranceMapper.toDto(hrMedicalInsurance);
    }

    /**
     * 删除社保医保公积金账户表
     * @param id
     */
    @Override
    public void deleteHrMedicalInsurance(String id){
    Optional.ofNullable(this.hrMedicalInsuranceRepository.selectById(id))
    .ifPresent(hrMedicalInsurance ->{
    this.hrMedicalInsuranceRepository.deleteById(id);
    log.info("Delete HrMedicalInsurance:{}", hrMedicalInsurance);
    });
    }

    /**
     * 批量删除社保医保公积金账户表
     * @param ids
     */
    @Override
    public void deleteHrMedicalInsurance(List<String>ids){
    log.info("Delete HrMedicalInsurances:{}",ids);
    this.hrMedicalInsuranceRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询社保医保公积金账户表
     * @param hrMedicalInsuranceDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrMedicalInsuranceDTO hrMedicalInsuranceDTO,Long pageNumber,Long pageSize){
    Page<HrMedicalInsurance>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrMedicalInsurance>qw=new QueryWrapper<>(this.hrMedicalInsuranceMapper.toEntity(hrMedicalInsuranceDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrMedicalInsuranceRepository.selectPage(page,qw);
    iPage.setRecords(this.hrMedicalInsuranceMapper.toDto(iPage.getRecords()));
    return iPage;
    }
}
