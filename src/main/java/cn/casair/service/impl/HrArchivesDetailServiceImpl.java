package cn.casair.service.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrArchivesDetail;
import cn.casair.dto.HrArchivesDetailDTO;
import cn.casair.repository.HrArchivesDetailRepository;
import cn.casair.mapper.HrArchivesDetailMapper;
import cn.casair.service.HrArchivesDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;
/**
 * 档案明细服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrArchivesDetailServiceImpl extends ServiceImpl<HrArchivesDetailRepository, HrArchivesDetail>implements HrArchivesDetailService {

    private static final Logger log=LoggerFactory.getLogger(HrArchivesDetailServiceImpl.class);

    private final HrArchivesDetailRepository hrArchivesDetailRepository;

    private final HrArchivesDetailMapper hrArchivesDetailMapper;

    public HrArchivesDetailServiceImpl(HrArchivesDetailRepository hrArchivesDetailRepository, HrArchivesDetailMapper hrArchivesDetailMapper){
    this.hrArchivesDetailRepository = hrArchivesDetailRepository;
    this.hrArchivesDetailMapper= hrArchivesDetailMapper;
    }

    /**
     * 创建档案明细
     * @param hrArchivesDetailDTO
     * @return
     */
    @Override
    public HrArchivesDetailDTO createHrArchivesDetail(HrArchivesDetailDTO hrArchivesDetailDTO){
    log.info("Create new HrArchivesDetail:{}", hrArchivesDetailDTO);

    HrArchivesDetail hrArchivesDetail =this.hrArchivesDetailMapper.toEntity(hrArchivesDetailDTO);
    this.hrArchivesDetailRepository.insert(hrArchivesDetail);
    return this.hrArchivesDetailMapper.toDto(hrArchivesDetail);
    }

    /**
     * 修改档案明细
     * @param hrArchivesDetailDTO
     * @return
     */
    @Override
    public Optional<HrArchivesDetailDTO>updateHrArchivesDetail(HrArchivesDetailDTO hrArchivesDetailDTO){
    return Optional.ofNullable(this.hrArchivesDetailRepository.selectById(hrArchivesDetailDTO.getId()))
    .map(roleTemp->{
    HrArchivesDetail hrArchivesDetail =this.hrArchivesDetailMapper.toEntity(hrArchivesDetailDTO);
    this.hrArchivesDetailRepository.updateById(hrArchivesDetail);
    log.info("Update HrArchivesDetail:{}", hrArchivesDetailDTO);
    return hrArchivesDetailDTO;
    });
    }

    /**
     * 查询档案明细详情
     * @param id
     * @return
     */
    @Override
    public HrArchivesDetailDTO getHrArchivesDetail(String id){
    log.info("Get HrArchivesDetail :{}",id);

    HrArchivesDetail hrArchivesDetail =this.hrArchivesDetailRepository.selectById(id);
    return this.hrArchivesDetailMapper.toDto(hrArchivesDetail);
    }

    /**
     * 删除档案明细
     * @param id
     */
    @Override
    public void deleteHrArchivesDetail(String id){
    Optional.ofNullable(this.hrArchivesDetailRepository.selectById(id))
    .ifPresent(hrArchivesDetail ->{
    this.hrArchivesDetailRepository.deleteById(id);
    log.info("Delete HrArchivesDetail:{}", hrArchivesDetail);
    });
    }

    /**
     * 批量删除档案明细
     * @param ids
     */
    @Override
    public void deleteHrArchivesDetail(List<String>ids){
    log.info("Delete HrArchivesDetails:{}",ids);
    this.hrArchivesDetailRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询档案明细
     * @param hrArchivesDetailDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrArchivesDetailDTO hrArchivesDetailDTO,Long pageNumber,Long pageSize){
    Page<HrArchivesDetail>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrArchivesDetail>qw=new QueryWrapper<>(this.hrArchivesDetailMapper.toEntity(hrArchivesDetailDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrArchivesDetailRepository.selectPage(page,qw);
    iPage.setRecords(this.hrArchivesDetailMapper.toDto(iPage.getRecords()));
    return iPage;
    }
}
