package cn.casair.service;
import cn.casair.domain.NcAccount;
import cn.casair.dto.NcAccountDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用友NC会计科目及对应的辅助服务类
 *
 * <AUTHOR>
 * @since 2022-10-19
 */
public interface NcAccountService extends IService<NcAccount> {


    /**
     * 创建用友NC会计科目及对应的辅助
     * @param ncAccountDTO
     * @return
     */
    NcAccountDTO createNcAccount(NcAccountDTO ncAccountDTO);

    /**
     * 修改用友NC会计科目及对应的辅助
     * @param ncAccountDTO
     * @return
     */
    Optional<NcAccountDTO> updateNcAccount(NcAccountDTO ncAccountDTO);

    /**
     * 查询用友NC会计科目及对应的辅助详情
     * @param id
     * @return
     */
    NcAccountDTO getNcAccount(String id);

    /**
     * 删除用友NC会计科目及对应的辅助
     * @param id
     */
    void deleteNcAccount(String id);

    /**
     * 批量删除用友NC会计科目及对应的辅助
     * @param ids
     */
    void deleteNcAccount(List<String> ids);

    /**
     * 分页查询用友NC会计科目及对应的辅助
     * @param ncAccountDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(NcAccountDTO ncAccountDTO,Long pageNumber,Long pageSize);
    }
