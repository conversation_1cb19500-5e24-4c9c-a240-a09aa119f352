package cn.casair.service;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.casair.domain.HrBillCompareResult;
import cn.casair.dto.DynamicHeadersDTO;
import cn.casair.dto.HrBillCompareHeaderDTO;
import cn.casair.dto.HrBillCompareResultDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 对账结果服务类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
public interface HrBillCompareResultService extends IService<HrBillCompareResult> {


    /**
     * 导出对账结果
     *
     * @param hrBillCompareResultDTO
     * @return
     */
    String exportCompareResult(HrBillCompareResultDTO hrBillCompareResultDTO);

    /**
     * 批量导出对账结果
     *
     * @param hrBillCompareResultDTO
     * @return
     */
    String batchExportCompareResult(HrBillCompareResultDTO hrBillCompareResultDTO);

    /**
     * 创建对账结果
     *
     * @param hrBillCompareResultDTO
     * @return
     */
    HrBillCompareResultDTO createHrBillCompareResult(HrBillCompareResultDTO hrBillCompareResultDTO);

    /**
     * 修改对账结果
     *
     * @param hrBillCompareResultDTO
     * @return
     */
    Optional<HrBillCompareResultDTO> updateHrBillCompareResult(HrBillCompareResultDTO hrBillCompareResultDTO);

    /**
     * 查询对账结果详情
     *
     * @param id
     * @return
     */
    HrBillCompareResultDTO getHrBillCompareResult(String id);

    /**
     * 删除对账结果
     *
     * @param id
     */
    void deleteHrBillCompareResult(String id);

    /**
     * 批量删除对账结果
     *
     * @param ids
     */
    void deleteHrBillCompareResult(List<String> ids);

    /**
     * 分页查询对账结果
     *
     * @param hrBillCompareResultDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrBillCompareResultDTO> findPage(HrBillCompareResultDTO hrBillCompareResultDTO, Long pageNumber, Long pageSize);

    /**
     * 导出海尔对账汇总
     * @param configId
     * @return
     */
    String exportHaierReconciliation(String configId);

    /**
     * 批量下载海尔对账
     * @param hrBillCompareResultDTO
     * @return
     */
    String batchExportHaier(HrBillCompareResultDTO hrBillCompareResultDTO);

    /**
     * 处理导出excel表头
     * @param colList
     * @param dynamicHeaders
     */
    void handleExcelExportEntity(List<ExcelExportEntity> colList, List<DynamicHeadersDTO> dynamicHeaders);

    /**
     * 处理导出动态数据列
     *
     * @param valueMap
     * @param dataObject
     * @param topKey
     */
    void dealDynamicDataChildrenForExport(Map<String, Object> valueMap, HrBillCompareHeaderDTO dataObject, String topKey);

}
