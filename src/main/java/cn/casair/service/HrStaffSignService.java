package cn.casair.service;

import cn.casair.domain.HrStaffSign;
import cn.casair.dto.HrStaffSignDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * 员工签名服务类
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
public interface HrStaffSignService extends IService<HrStaffSign> {

    /**
     * 获取员工签名列表
     *
     * @return
     */
    List<HrStaffSignDTO> getHrStaffSignList();

    /**
     * 创建员工签名
     *
     * @param file
     * @return
     */
    HrStaffSignDTO createHrStaffSign(MultipartFile file);

    /**
     * 修改员工签名
     *
     * @param hrStaffSignDTO
     * @return
     */
    Optional<HrStaffSignDTO> updateHrStaffSign(HrStaffSignDTO hrStaffSignDTO);

    /**
     * 查询员工签名详情
     *
     * @param id
     * @return
     */
    HrStaffSignDTO getHrStaffSign(String id);

    /**
     * 删除员工签名
     *
     * @param id
     */
    void deleteHrStaffSign(String id);

    /**
     * 批量删除员工签名
     *
     * @param ids
     */
    void deleteHrStaffSign(List<String> ids);

    /**
     * 分页查询员工签名
     *
     * @param hrStaffSignDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrStaffSignDTO hrStaffSignDTO, Long pageNumber, Long pageSize);
}
