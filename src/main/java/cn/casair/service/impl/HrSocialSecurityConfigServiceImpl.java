package cn.casair.service.impl;

import cn.casair.domain.HrSocialSecurityConfig;
import cn.casair.dto.HrSocialSecurityConfigDTO;
import cn.casair.mapper.HrSocialSecurityConfigMapper;
import cn.casair.repository.HrSocialSecurityConfigRepository;
import cn.casair.service.HrSocialSecurityConfigService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 社保类型模板服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrSocialSecurityConfigServiceImpl extends ServiceImpl<HrSocialSecurityConfigRepository, HrSocialSecurityConfig> implements HrSocialSecurityConfigService {

    private final HrSocialSecurityConfigRepository hrSocialSecurityConfigRepository;
    private final HrSocialSecurityConfigMapper hrSocialSecurityConfigMapper;


    /**
     * 创建社保类型模板
     *
     * @param hrSocialSecurityConfigDTO
     * @return
     */
    @Override
    public HrSocialSecurityConfigDTO createHrSocialSecurityConfig(HrSocialSecurityConfigDTO hrSocialSecurityConfigDTO) {
        log.info("Create new HrSocialSecurityConfig:{}", hrSocialSecurityConfigDTO);

        HrSocialSecurityConfig hrSocialSecurityConfig = this.hrSocialSecurityConfigMapper.toEntity(hrSocialSecurityConfigDTO);
        this.hrSocialSecurityConfigRepository.insert(hrSocialSecurityConfig);
        return this.hrSocialSecurityConfigMapper.toDto(hrSocialSecurityConfig);
    }

    /**
     * 修改社保类型模板
     *
     * @param hrSocialSecurityConfigDTO
     * @return
     */
    @Override
    public Optional<HrSocialSecurityConfigDTO> updateHrSocialSecurityConfig(HrSocialSecurityConfigDTO hrSocialSecurityConfigDTO) {
        return Optional.ofNullable(this.hrSocialSecurityConfigRepository.selectById(hrSocialSecurityConfigDTO.getId()))
            .map(roleTemp -> {
                HrSocialSecurityConfig hrSocialSecurityConfig = this.hrSocialSecurityConfigMapper.toEntity(hrSocialSecurityConfigDTO);
                this.hrSocialSecurityConfigRepository.updateById(hrSocialSecurityConfig);
                log.info("Update HrSocialSecurityConfig:{}", hrSocialSecurityConfigDTO);
                return hrSocialSecurityConfigDTO;
            });
    }

    /**
     * 查询社保类型模板详情
     *
     * @param id
     * @return
     */
    @Override
    public HrSocialSecurityConfigDTO getHrSocialSecurityConfig(String id) {
        log.info("Get HrSocialSecurityConfig :{}", id);

        HrSocialSecurityConfig hrSocialSecurityConfig = this.hrSocialSecurityConfigRepository.selectById(id);
        return this.hrSocialSecurityConfigMapper.toDto(hrSocialSecurityConfig);
    }

    /**
     * 删除社保类型模板
     *
     * @param id
     */
    @Override
    public void deleteHrSocialSecurityConfig(String id) {
        Optional.ofNullable(this.hrSocialSecurityConfigRepository.selectById(id))
            .ifPresent(hrSocialSecurityConfig -> {
                this.hrSocialSecurityConfigRepository.deleteById(id);
                log.info("Delete HrSocialSecurityConfig:{}", hrSocialSecurityConfig);
            });
    }

    /**
     * 批量删除社保类型模板
     *
     * @param ids
     */
    @Override
    public void deleteHrSocialSecurityConfig(List<String> ids) {
        log.info("Delete HrSocialSecurityConfigs:{}", ids);
        this.hrSocialSecurityConfigRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询社保类型模板
     *
     * @param hrSocialSecurityConfigDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrSocialSecurityConfigDTO hrSocialSecurityConfigDTO, Long pageNumber, Long pageSize) {
        Page<HrSocialSecurityConfig> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrSocialSecurityConfig> qw = new QueryWrapper<>(this.hrSocialSecurityConfigMapper.toEntity(hrSocialSecurityConfigDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrSocialSecurityConfigRepository.selectPage(page, qw);
        iPage.setRecords(this.hrSocialSecurityConfigMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    /**
     * 根据社保类型ID查询
     * @param socialSecurityId 社保类型ID
     * @return 模板配置信息
     */
    @Override
    public HrSocialSecurityConfigDTO getBySocialSecurityId(String socialSecurityId) {
        HrSocialSecurityConfig securityConfig = hrSocialSecurityConfigRepository.selectOne(new QueryWrapper<HrSocialSecurityConfig>().eq("social_security_id", socialSecurityId));
        return hrSocialSecurityConfigMapper.toDto(securityConfig);
    }
}
