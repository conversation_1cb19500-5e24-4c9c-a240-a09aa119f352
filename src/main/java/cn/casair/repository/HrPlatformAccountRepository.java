package cn.casair.repository;
import cn.casair.domain.HrPlatformAccount;
import cn.casair.dto.HrPlatformAccountDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 平台账户表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Repository
public interface HrPlatformAccountRepository extends BaseMapper<HrPlatformAccount> {

    /**
     * 查询公司设置的工资发放银行
     *
     * @param clientId
     * @return
     */
    HrPlatformAccount selectClientPlatformAccount(@Param("clientId") String clientId);


    /**
     * 查询平台账户表
     *
     * @param platformType
     * @return
     */
    List<HrPlatformAccountDTO> findListByPlatformType(@Param("platformType") String platformType);
}
