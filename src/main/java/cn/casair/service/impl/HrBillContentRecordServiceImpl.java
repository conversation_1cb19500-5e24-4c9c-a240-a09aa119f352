package cn.casair.service.impl;

import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.CommonUtils;
import cn.casair.common.utils.FileUtil;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.domain.HrBill;
import cn.casair.domain.HrBillContentRecord;
import cn.casair.dto.*;
import cn.casair.mapper.HrBillContentRecordMapper;
import cn.casair.repository.HrBillContentRecordRepository;
import cn.casair.service.HrBillContentRecordService;
import cn.casair.service.HrBillService;
import cn.casair.service.HrClientService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class HrBillContentRecordServiceImpl extends ServiceImpl<HrBillContentRecordRepository, HrBillContentRecord> implements HrBillContentRecordService {


    private final HrBillContentRecordRepository hrBillContentRecordRepository;
    private final HrBillContentRecordMapper hrBillContentRecordMapper;
    private final HrClientService hrClientService;
    private final HrBillService hrBillService;
    @Value("${file.temp-path}")
    private String fileTempPath;

    /**
     * 创建
     *
     * @param hrBillContentRecordDTO
     * @return
     */
    @Override
    public HrBillContentRecordDTO createHrBillContentRecord(HrBillContentRecordDTO hrBillContentRecordDTO) {
        log.info("Create new HrBillContentRecord:{}", hrBillContentRecordDTO);

        HrBillContentRecord hrBillContentRecord = this.hrBillContentRecordMapper.toEntity(hrBillContentRecordDTO);
        this.hrBillContentRecordRepository.insert(hrBillContentRecord);
        return this.hrBillContentRecordMapper.toDto(hrBillContentRecord);
    }

    /**
     * 修改
     *
     * @param hrBillContentRecordDTO
     * @return
     */
    @Override
    public Optional<HrBillContentRecordDTO> updateHrBillContentRecord(HrBillContentRecordDTO hrBillContentRecordDTO) {
        return Optional.ofNullable(this.hrBillContentRecordRepository.selectById(hrBillContentRecordDTO.getId()))
            .map(roleTemp -> {
                HrBillContentRecord hrBillContentRecord = this.hrBillContentRecordMapper.toEntity(hrBillContentRecordDTO);
                this.hrBillContentRecordRepository.updateById(hrBillContentRecord);
                log.info("Update HrBillContentRecord:{}", hrBillContentRecordDTO);
                return hrBillContentRecordDTO;
            });
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillContentRecordDTO getHrBillContentRecord(String id) {
        log.info("Get HrBillContentRecord :{}", id);

        HrBillContentRecord hrBillContentRecord = this.hrBillContentRecordRepository.selectById(id);
        return this.hrBillContentRecordMapper.toDto(hrBillContentRecord);
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleteHrBillContentRecord(String id) {
        Optional.ofNullable(this.hrBillContentRecordRepository.selectById(id))
            .ifPresent(hrBillContentRecord -> {
                this.hrBillContentRecordRepository.deleteById(id);
                log.info("Delete HrBillContentRecord:{}", hrBillContentRecord);
            });
    }

    /**
     * 批量删除
     *
     * @param ids
     */
    @Override
    public void deleteHrBillContentRecord(List<String> ids) {
        log.info("Delete HrBillContentRecords:{}", ids);
        this.hrBillContentRecordRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询
     *
     * @param hrBillContentRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillContentRecordDTO hrBillContentRecordDTO, Long pageNumber, Long pageSize) {
        // 查询权限
        List<String> clientIdList = hrClientService.selectClientIdByUserId();
        if (clientIdList == null || clientIdList.isEmpty()) {
            return new Page<>();
        }
        hrBillContentRecordDTO.setClientIdList(clientIdList);
        Page<HrBillContentRecord> page = new Page<>(pageNumber, pageSize);
        Map<String, String> mappingMap = new HashMap<>();
        mappingMap.put("payment_date","pay_year,pay_monthly");
        mappingMap.put("client_name","client_name");
        mappingMap.put("bill_type","bill_type");
        mappingMap.put("title","title");
        page.setOrders(CommonUtils.getOrderItems("pay_year,pay_monthly",false,hrBillContentRecordDTO.getField(),hrBillContentRecordDTO.getOrder(),mappingMap));
        IPage<HrBillContentRecordDTO> iPage = this.hrBillContentRecordRepository.findPage(page, hrBillContentRecordDTO);
        return iPage;
    }


    /**
     * 分页查询
     *
     * @param hrBillContentRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findRecordPage(HrBillContentRecordDTO hrBillContentRecordDTO, Long pageNumber, Long pageSize) {
        String billId = hrBillContentRecordDTO.getBillId();
        if(StringUtils.isBlank(billId)){
            throw new CommonException("请选择要查看的账单！");
        }
        Page<HrBillContentRecord> page = new Page<>(pageNumber, pageSize);
        IPage<HrBillContentRecordDTO> iPage = this.hrBillContentRecordRepository.findRecordPage(page, hrBillContentRecordDTO);
        return iPage;
    }

    @Override
    public void downloadBillBatch(HrBillContentRecordDTO hrBillContentRecordDTO, HttpServletResponse response) {
        // 查询权限
        List<String> clientIdList = hrClientService.selectClientIdByUserId();
        if (clientIdList == null || clientIdList.isEmpty()) {
            clientIdList.add("");
        }
        hrBillContentRecordDTO.setClientIdList(clientIdList);
        List<HrBillContentRecordDTO> list = this.hrBillContentRecordRepository.selectListBatch(hrBillContentRecordDTO);
        List<String> ids = list.stream().map(HrBillContentRecordDTO::getBillId).collect(Collectors.toList());
        String suffix = "_" + DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
        List<File> fileList = new ArrayList<>();
        ids.forEach(id -> {
            HrBill hrBill = hrBillService.getById(id);
            if(hrBill != null){
                List<HrBillContentRecordDTO> recordList = hrBillContentRecordRepository.findList(new HrBillContentRecordDTO().setBillId(id));
                fileList.add(new File(Objects.requireNonNull(ExcelUtils.exportLocal(hrBill.getTitle()+"_账单日志明细", HrBillContentRecordDTO.class, recordList, fileTempPath))));
            }
        });
        if(fileList.isEmpty()){
            throw new CommonException("选择的账单没有对应的数据！");
        }
        try {
            FileUtil.downloadZip(response, fileList, "账单日志明细" + suffix);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
