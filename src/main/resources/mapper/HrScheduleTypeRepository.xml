<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrScheduleTypeRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, user_id, schedule_type_id, schedule_type_name,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrScheduleType">
                    <id column="id" property="id"/>
                    <result column="user_id" property="userId"/>
                    <result column="schedule_type_id" property="scheduleTypeId"/>
                    <result column="schedule_type_name" property="scheduleTypeName"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

</mapper>
