<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillDetailItemsRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        bill_detail_id,
        expense_manage_id,
        expense_name,
        expense_type,
        amount,
        calculation_num,
        is_default,
        is_delete ,
        created_by ,
        last_modified_by ,
        created_date ,
        last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillDetailItems">
        <id column="id" property="id"/>
        <result column="bill_detail_id" property="billDetailId"/>
        <result column="expense_manage_id" property="expenseManageId"/>
        <result column="expense_name" property="expenseName"/>
        <result column="expense_type" property="expenseType"/>
        <result column="amount" property="amount"/>
        <result column="calculation_num" property="calculationNum"/>
        <result column="is_default" property="isDefault"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>


    <select id="getByBillDetailId" resultType="cn.casair.dto.HrBillDetailItemsDTO">
        SELECT
            id,
            bill_detail_id,
            expense_name,
            expense_type,
            amount,
            calculation_num
        FROM
            `hr_bill_detail_items`
        WHERE
            is_delete = 0
          AND bill_detail_id IN
        <foreach collection="billDetailIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        ORDER BY expense_name
    </select>

    <update id="deleteByBillId">
        update hr_bill_detail_items bi
            left join hr_bill_detail hd on bi.bill_detail_id = hd.id
        set bi.is_delete = 1
        where hd.bill_id = #{billId}
    </update>


    <update id="deleteByBillIdBatch">
        UPDATE hr_bill_detail_items bi
        LEFT JOIN hr_bill_detail hd ON bi.bill_detail_id = hd.id
        SET bi.is_delete = 1
        WHERE hd.bill_id IN
        <foreach collection="billIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <select id="getBillDynamicItemsData" resultType="java.util.HashMap">
        SELECT
            hi.bill_detail_id,
            <foreach collection="dynamicFees" open="" separator="," close="" item="val">
                MAX(if(hi.expense_name = #{val}, hi.amount, 0.0)) AS ${val}
            </foreach>
        FROM hr_bill_detail_items hi
                 LEFT JOIN hr_bill_detail bd ON hi.bill_detail_id = bd.id
        WHERE hi.is_delete = 0
          AND bd.bill_id = #{billId}
        GROUP BY hi.bill_detail_id
    </select>

    <select id="getBillDynamicItemsByBillDetailId" resultType="java.lang.String">
        SELECT DISTINCT expense_name FROM hr_bill_detail_items
        WHERE
            is_delete = 0
        AND bill_detail_id IN
        <foreach collection="billDetailIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <delete id="deleteBatch">
        DELETE  FROM hr_bill_detail_items  WHERE expense_type = #{expenseType} AND expense_name = #{expenseName} AND bill_detail_id IN
        <foreach collection="billDetailIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </delete>

    <insert id="batchSave">
        INSERT INTO `hr_bill_detail_items`
        ( `id`, `bill_detail_id`, `expense_manage_id`, `expense_name`, `expense_type`, `amount`, `calculation_num`, `created_by`, `created_date` )
        VALUES
        <foreach collection ="list" item="item" index= "index" separator =",">
            (
            #{item.id}, #{item.billDetailId}, #{item.expenseManageId},
            #{item.expenseName}, #{item.expenseType}, #{item.amount},
            #{item.calculationNum}, #{item.createdBy}, #{item.createdDate}
            )
        </foreach>
    </insert>

</mapper>
