<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffSecondmentRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, staff_id, new_client_id, old_client_id, start_date, end_date, states, step, reason, remark, is_default,staff_status,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffSecondment">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="new_client_id" property="newClientId"/>
        <result column="old_client_id" property="oldClientId"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="states" property="states"/>
        <result column="step" property="step"/>
        <result column="reason" property="reason"/>
        <result column="remark" property="remark"/>
        <result column="is_default" property="isDefault"/>
        <result column="staff_status" property="staffStatus"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Select_SQL">
        SELECT
        hss.*,
        hc1.client_name old_client_name,
        hc2.client_name new_client_name,
        hts.`name`,
        hts.staff_status as staffStatus,
        hts.sex,
        IF(hts.sex = 1, '男', '女') AS sexLabel,
        hts.certificate_num,
        hts.phone,
        hs.profession_name,
        hts.personnel_type
        FROM
        hr_staff_secondment hss
        LEFT JOIN hr_talent_staff hts ON hts.id = hss.staff_id
        LEFT JOIN hr_staff_work_experience hswe ON hts.id = hswe.staff_id AND hswe.iz_default = 1 AND hts.client_id = hswe.client_id AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hs.id = hswe.station_id
        LEFT JOIN hr_client hc1 ON hc1.id = hss.old_client_id
        LEFT JOIN hr_client hc2 ON hc2.id = hss.new_client_id
        WHERE hss.is_delete = 0
        <if test="permissionClient!=null and permissionClient.size>0">
            AND ( hss.old_client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
            OR hss.new_client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
            )
        </if>
        <if test="param.ids!=null and param.ids.size() > 0">
            AND hss.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.name != null and param.name != ''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.sex != null">
            AND hts.sex = #{param.sex}
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND hts.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.professionIdList!=null and param.professionIdList.size() > 0">
            AND hs.id IN
            <foreach collection="param.professionIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.personnelTypeList!=null and param.personnelTypeList.size() > 0">
            AND hts.personnel_type IN
            <foreach collection="param.personnelTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.staffStatusList!=null and param.staffStatusList.size() > 0">
            AND hts.staff_status IN
            <foreach collection="param.staffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.statesList!=null and param.statesList.size() > 0">
            AND hss.states IN
            <foreach collection="param.statesList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.stepList!=null and param.stepList.size() > 0">
            AND hss.step NOT IN
            <foreach collection="param.stepList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.newClientIds!=null and param.newClientIds.size() > 0">
            AND hss.new_client_id IN
            <foreach collection="param.newClientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.oldClientIds!=null and param.oldClientIds.size() > 0">
            AND hss.old_client_id IN
            <foreach collection="param.oldClientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.startDateStart!=null">
            AND hss.start_date >= #{param.startDateStart}
        </if>
        <if test="param.startDateEnd!=null">
            AND hss.start_date  &lt;=  #{param.startDateEnd}
        </if>
        <if test="param.endDateStart!=null">
            AND hss.end_date >= #{param.endDateStart}
        </if>
        <if test="param.endDateEnd!=null">
            AND hss.end_date  &lt;=  #{param.endDateEnd}
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            ORDER BY ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY hss.created_date DESC
        </if>
    </sql>

    <select id="findPage" resultType="cn.casair.dto.HrStaffSecondmentDTO">
       <include refid="Select_SQL"/>
    </select>

    <select id="findList" resultType="cn.casair.dto.HrStaffSecondmentDTO">
        <include refid="Select_SQL"/>
    </select>

    <select id="findBatch" resultType="cn.casair.dto.HrStaffSecondmentDTO">
        SELECT
            hss.*,
            hc1.client_name old_client_name,
            hc1.user_id old_user_id,
            hc2.client_name new_client_name,
            hc2.user_id new_user_id,
            hts.`name`,
            hts.sex,
            IF(hts.sex = 1, '男', '女') AS sexLabel,
            hts.certificate_num,
            hts.phone,
            hs.profession_name,
            hts.personnel_type,
            hts.staff_status AS oldStaffStatus
        FROM
            hr_staff_secondment hss
        LEFT JOIN hr_talent_staff hts ON hts.id = hss.staff_id
        LEFT JOIN hr_staff_work_experience hswe ON hts.id = hswe.staff_id AND hts.client_id = hswe.client_id AND hswe.iz_default = 1 AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hs.id = hswe.station_id
        LEFT JOIN hr_client hc1 ON hc1.id = hss.old_client_id
        LEFT JOIN hr_client hc2 ON hc2.id = hss.new_client_id
        WHERE hss.is_delete = 0 AND hss.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="findStaff" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
            hts.*,
            hss.new_client_id,
            hss.old_client_id,
            hss.start_date secondmentStartDate,
	        hss.end_date secondmentEndDate,
            hss.is_default secondmentIsDefault,
            hss.staff_status secondmentStaffStatus,
            hss.states secondmentStates
        FROM
            hr_talent_staff hts
        LEFT JOIN hr_staff_secondment hss ON hts.secondment_id = hss.id AND hss.is_delete = 0
        WHERE hts.is_delete = 0 AND hts.secondment_id IS NOT NULL AND hts.staff_status NOT IN (5,10,11)
        AND hss.states in (1,2)
    </select>

    <select id="findSecondmentList" resultType="cn.casair.dto.HrStaffSecondmentDTO">
        SELECT
            hc.client_name new_client_name,
            hss.*
        FROM
            hr_staff_secondment hss
            LEFT JOIN hr_talent_staff hts ON hts.id = hss.staff_id
            LEFT JOIN hr_client hc ON hc.id = hss.new_client_id
        WHERE
            hss.is_delete = 0 AND hss.staff_id = #{staffId}
    </select>

    <select id="findByApply" resultType="cn.casair.domain.HrStaffSecondment">
        SELECT
            *
        FROM
            hr_staff_secondment hss
        WHERE hss.is_delete = 0
        <if test="permissionClient!=null and permissionClient.size>0">
            AND ( hss.old_client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
            OR hss.new_client_id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
            )
        </if>
        <if test="param.statesList!=null and param.statesList.size() > 0">
            AND hss.states IN
            <foreach collection="param.statesList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.stepList!=null and param.stepList.size() > 0">
            AND hss.step IN
            <foreach collection="param.stepList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        ORDER BY created_date DESC LIMIT 5
    </select>


    <update id="updateStepAndStates">
        UPDATE hr_staff_secondment
        <set>
            <if test="param.states != null"> states = #{param.states},</if>
            <if test="param.step != null"> step = #{param.step},</if>
            <if test="param.isDefault != null"> is_default = #{param.isDefault},</if>
        </set>
        WHERE id = #{param.id}
    </update>
</mapper>
