package cn.casair.service;
import cn.casair.domain.HrPlatformAccount;
import cn.casair.dto.HrPlatformAccountDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 平台账户表服务类
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
public interface HrPlatformAccountService extends IService<HrPlatformAccount> {


    /**
     * 创建平台账户表
     * @param hrPlatformAccountDTO
     * @return
     */
    HrPlatformAccountDTO createHrPlatformAccount(HrPlatformAccountDTO hrPlatformAccountDTO);

    /**
     * 修改平台账户表
     * @param hrPlatformAccountDTO
     * @return
     */
    Optional<HrPlatformAccountDTO> updateHrPlatformAccount(HrPlatformAccountDTO hrPlatformAccountDTO);

    /**
     * 查询平台账户表详情
     * @param id
     * @return
     */
    HrPlatformAccountDTO getHrPlatformAccount(String id);

    /**
     * 删除平台账户表
     * @param id
     */
    void deleteHrPlatformAccount(String id);

    /**
     * 批量删除平台账户表
     * @param ids
     */
    void deleteHrPlatformAccount(List<String> ids);

    /**
     * 分页查询平台账户表
     * @param hrPlatformAccountDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrPlatformAccountDTO hrPlatformAccountDTO,Long pageNumber,Long pageSize);

    String exportPlatform(HrPlatformAccountDTO hrPlatformAccountDTO, HttpServletResponse httpServletResponse);

    String importPlatform(MultipartFile file);

    /**
     * 平台账号模板
     * @param response
     */
    String importPlatformTemplate(HttpServletResponse response);

    /**
     * 查询平台账户表
     *
     * @param platformType
     * @return
     */
    List<HrPlatformAccountDTO> findListByPlatformType(String platformType);
}
