package cn.casair.service;

import cn.casair.domain.HrCommonFunctions;
import cn.casair.dto.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.HttpHeaders;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;

/**
 * 常用功能服务类
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
public interface HrCommonFunctionsService extends IService<HrCommonFunctions> {


    /**
     * 创建常用功能
     * @param hrCommonFunctionsDTO
     * @return
     */
    HrCommonFunctionsDTO createHrCommonFunctions(HrCommonFunctionsDTO hrCommonFunctionsDTO);


    /**
     * 查询常用功能详情
     * @param
     * @return
     */
    HrCommonFunctionsDTO getHrCommonFunctions( );


    List<HrRemindDTO> getHrRemindFege() throws ParseException;

    List<HrMessageListDTO> getHrRemindMessage();

    void getHrRemindMessageUpdate(String id);

    HrBusinessIndicatorsDTO getBusinessIndicators();

    /**
     * 申请列表
     * @return
     */
    List<ApplicationListDTO> getApplicationList();

    List<HrNotificationUserContentDTO> getApplicationListReminder();

    void getHrRemindMessageUpdateReminder(String id);

    /**
     * 获取小程序 scheme 码
     * @param envVersion 打开的小程序版本
     * @return scheme 码
     */
    JSONObject getAppletSchemeCode(String envVersion);


}
