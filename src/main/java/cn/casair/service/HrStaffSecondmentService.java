package cn.casair.service;

import cn.casair.domain.HrStaffSecondment;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrStaffSecondmentDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 员工借调服务服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface HrStaffSecondmentService extends IService<HrStaffSecondment> {


    /**
     * 创建员工借调服务
     *
     * @param hrStaffSecondmentDTO
     * @return
     */
    HrStaffSecondmentDTO createHrStaffSecondment(HrStaffSecondmentDTO hrStaffSecondmentDTO);

    /**
     * 修改员工借调服务
     *
     * @param hrStaffSecondmentDTO
     * @return
     */
    Optional<HrStaffSecondmentDTO> updateHrStaffSecondment(HrStaffSecondmentDTO hrStaffSecondmentDTO);

    /**
     * 查询员工借调服务详情
     *
     * @param id
     * @return
     */
    HrStaffSecondmentDTO getHrStaffSecondment(String id);

    /**
     * 删除员工借调服务
     *
     * @param id
     */
    void deleteHrStaffSecondment(String id);

    /**
     * 批量删除员工借调服务
     *
     * @param ids
     */
    void deleteHrStaffSecondment(List<String> ids);

    /**
     * 分页查询员工借调服务
     *
     * @param hrStaffSecondmentDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrStaffSecondmentDTO> findPage(HrStaffSecondmentDTO hrStaffSecondmentDTO, Long pageNumber, Long pageSize);

    /**
     * 导出员工借调服务
     * @param hrStaffSecondmentDTO
     * @return
     */
    String exportHrStaffSecondment(HrStaffSecondmentDTO hrStaffSecondmentDTO);

    /**
     * 员工借调-->批量确认
     * @param batchOptDTO
     * @return
     */
    Map<String, Object> confirmationBatch(BatchOptDTO batchOptDTO);

    /**
     * 员工借调-->业务员批量审核
     * @param batchOptDTO
     * @return
     */
    Map<String, Object> salesmanApproval(BatchOptDTO batchOptDTO);

    /**
     * 每天0点检测员工借调状态
     */
    void updateStaffStatusSecondment();


}
