**简要描述：**

- $!{table.comment} 接口文档

**请求URL：**
- `POST    app/${table.entityPath}/findPage/page?pageNumber=1&pageSize=10 `  //分页查询
- `GET     app/${table.entityPath}/get${entity}/{id}`    //获取详情
- `POST    app/${table.entityPath}/create${entity}`      //新增数据
- `PUT     app/${table.entityPath}/update${entity} `     //更新数据
- `DELETE  app/${table.entityPath}/delete${entity}/{id}` //数据删除
- `GET     app/${table.entityPath}/get${entity}Count`    //获取近期统计

**请求示例**

```
{
#foreach($field in ${table.fields})
"${field.propertyName}":"" //${field.comment}
#end
}
```


**参数：**

|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
#foreach($field in ${table.fields})
|${field.propertyName} |是  |${field.propertyType} |${field.comment}   |
#end

**返回示例**

```
//详情返回
{
    "status": 200,
    "msg": "操作成功",
    "data": {
    #foreach($field in ${table.fields})
        "${field.propertyName}":"" //${field.comment}
    #end
    }
}
//分页返回
{
"data": {
"records": [
{
#foreach($field in ${table.fields})
"${field.propertyName}":"" //${field.comment}
#end
},
],
"total": 4,
"size": 2,
"current": 1,
"searchCount": true,
"pages": 2
},
"errorCode": 0,
"errorMessage": "SUCCESS"
}
```

**返回参数说明**

|参数名|类型|说明|
|:-----  |:-----|-----                           |


**备注**

- 更多返回错误代码请看首页的错误代码描述

