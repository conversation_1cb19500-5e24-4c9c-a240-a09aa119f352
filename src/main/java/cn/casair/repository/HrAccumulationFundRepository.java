package cn.casair.repository;
import cn.casair.domain.HrAccumulationFund;
import cn.casair.domain.HrClient;
import cn.casair.dto.HrAccumulationFundDTO;
import cn.casair.dto.HrClientDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 公积金类型管理数据库操作类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Repository
public interface HrAccumulationFundRepository extends BaseMapper<HrAccumulationFund> {

    /**
     * 获取客户公积金配置
     *
     * @param clientIds
     * @return cn.casair.domain.HrAccumulationFund
     * <AUTHOR>
     * @date 2021/11/2
     **/
    List<HrAccumulationFund> getClientAccumulationFund(@Param("clientIds") List<String> clientIds);

    /**
     * 获取此公积金类型下的客户列表
     *
     * @param id
     * @return java.util.List<cn.casair.domain.HrClient>
     * <AUTHOR>
     * @date 2021/11/1
     **/
    List<HrClient> getClientByProvidentFundTypeId(String id);

    /**
     * 公积金类型下拉列表数据
     *
     * @return java.util.List<cn.casair.dto.HrAccumulationFundDTO>
     * <AUTHOR>
     * @date 2021/10/18
     **/
    List<HrAccumulationFundDTO> getAccumulationFundTypeList();

    @Select(" SELECT hc.*,hp.agreement_start_date AS agreementStartDate,hp.agreement_end_date AS agreementEndDate FROM hr_client hc LEFT JOIN hr_protocol hp ON hc.id = hp.client_id ${ew.customSqlSegment}")
    IPage<HrClientDTO> selectPageClient(Page page, @Param(Constants.WRAPPER)QueryWrapper<HrClient> qw);
}
