package cn.casair.repository;

import cn.casair.domain.HrPaperQuestion;
import cn.casair.domain.HrQuestion;
import cn.casair.dto.HrQuestionDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 题库表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
@Repository
public interface HrQuestionRepository extends BaseMapper<HrQuestion> {

    void deleteList(@Param("applicablePostList")List<String> applicablePostList);

    void deleteId(String id);


    List<String> getlistsQuestion(@Param("param1")HrQuestionDTO hrQuestionDTOS);

    IPage<HrQuestionDTO> selectPages(Page<HrQuestion> page, @Param("param1")HrQuestionDTO hrQuestionDTO);

    List<HrQuestionDTO> selectQuesionid(@Param("qid")List<String> questionIdNew,  @Param("totalscoreSum") Integer totalscoreSum);


}
