package cn.casair.service;

import cn.casair.domain.HrNotificationMessage;
import cn.casair.dto.HrNotificationMessageDTO;
import cn.casair.dto.HrNotificationUserDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 通知消息服务类
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
public interface HrNotificationMessageService extends IService<HrNotificationMessage> {


    /**
     * 创建通知消息
     * @param
     * @return
     */
    HrNotificationUserDTO createHrNotificationMessage(HrNotificationUserDTO hrNotificationUserDTO);

    /**
     * 修改通知消息
     * @param
     * @return
     */
    Optional<HrNotificationUserDTO> updateHrNotificationMessage(HrNotificationUserDTO hrNotificationUserDTO);

    /**
     * 查询通知消息详情
     * @param id
     * @return
     */
    HrNotificationMessageDTO getHrNotificationMessage(String id);

    /**
     * 删除通知消息
     * @param id
     */
    void deleteHrNotificationMessage(String id);

    /**
     * 批量删除通知消息
     * @param ids
     */
    void deleteHrNotificationMessage(List<String> ids);

    /**
     * 分页查询通知消息
     * @param hrNotificationMessageDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrNotificationMessageDTO hrNotificationMessageDTO,Long pageNumber,Long pageSize);

    void updeteHrNotificationMessage(List<HrNotificationMessageDTO> ids);

    List<HrNotificationMessageDTO> getHrNotificationMessageSelectContent(String id);
}
