package cn.casair.service;

import cn.casair.domain.HrArchivesBring;
import cn.casair.dto.HrArchivesBringDTO;
import cn.casair.dto.HrArchivesDetailDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * 档案调入调出表服务类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface HrArchivesBringService extends IService<HrArchivesBring> {


    /**
     * 创建档案调入调出表
     *
     * @param hrArchivesBringDTO
     * @return
     */
    HrArchivesBringDTO createHrArchivesBring(HrArchivesBringDTO hrArchivesBringDTO);

    /**
     * 修改档案调入调出表
     *
     * @param hrArchivesBringDTO
     * @return
     */
    Optional<HrArchivesBringDTO> updateHrArchivesBring(HrArchivesBringDTO hrArchivesBringDTO);

    /**
     * 查询档案调入调出表详情
     *
     * @param id
     * @return
     */
    HrArchivesBringDTO getHrArchivesBring(String id);

    /**
     * 删除档案调入调出表
     *
     * @param id
     */
    void deleteHrArchivesBring(String id);

    /**
     * 批量删除档案调入调出表
     *
     * @param ids
     */
    void deleteHrArchivesBring(List<String> ids);

    /**
     * 分页查询档案变更记录
     *
     * @param hrArchivesBringDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrArchivesBringDTO> findPage(HrArchivesBringDTO hrArchivesBringDTO, Long pageNumber, Long pageSize);

    /**
     * 根据档案id获取在档档案明细列表
     *
     * @param archivesId
     * @return java.util.List<cn.casair.dto.HrArchivesDetailDTO>
     * <AUTHOR>
     * @date 2021/10/14
     **/
    List<HrArchivesDetailDTO> getArchivesDetailByArchivesId(String archivesId);

    /**
     * 档案归还
     *
     * @param hrArchivesBringDTO
     * @return void
     * <AUTHOR>
     * @date 2021/10/14
     **/
    void archivesReturn(HrArchivesBringDTO hrArchivesBringDTO);

    /**
     * 档案变更记录列表导出
     *
     * @param hrArchivesBringDTO
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2021/10/14
     **/
    String archivesBringExport(HrArchivesBringDTO hrArchivesBringDTO, HttpServletResponse response);

    /**
     * 录入劳动合同专用接口
     *
     * @param hrArchivesBringDTO
     * @return cn.casair.dto.HrArchivesBringDTO
     * <AUTHOR>
     * @date 2021/10/27
     **/
    HrArchivesBringDTO enterLaborContract(HrArchivesBringDTO hrArchivesBringDTO);

}
