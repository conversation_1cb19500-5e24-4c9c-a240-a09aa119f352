<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrApplyCheckerRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, checker_id, apply_id, checker_name, checker_result, checker_reason,checker_remark,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrApplyChecker">
        <id column="id" property="id"/>
        <result column="checker_id" property="checkerId"/>
        <result column="apply_id" property="applyId"/>
        <result column="checker_name" property="checkerName"/>
        <result column="checker_result" property="checkerResult"/>
        <result column="checker_reason" property="checkerReason"/>
        <result column="checker_remark" property="checkerRemark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findApplyCheckList" resultType="cn.casair.dto.HrApplyCheckerDTO">
        SELECT
            hac.*,su.real_name
        FROM
            hr_apply_checker hac
        LEFT JOIN hr_data_modification hdm ON hac.apply_id = hdm.id
        LEFT JOIN sys_user su ON hac.checker_id = su.id
        where hac.is_delete = 0 AND hdm.id = #{id}
    </select>

    <select id="findApplyChecker" resultType="cn.casair.dto.HrApplyCheckerDTO">
        SELECT
            hac.*, concat(concat('(',sr.role_name,') '),su.real_name) real_name
        FROM
            hr_apply_checker hac
        LEFT JOIN sys_user su ON hac.checker_id = su.id
        LEFT JOIN sys_user_role sur ON su.id = sur.user_id AND sur.is_delete = 0
        LEFT JOIN sys_role sr ON sur.role_id = sr.id AND sr.is_delete = 0
        WHERE hac.is_delete = 0 AND hac.apply_id = #{applyId}
    </select>
</mapper>
