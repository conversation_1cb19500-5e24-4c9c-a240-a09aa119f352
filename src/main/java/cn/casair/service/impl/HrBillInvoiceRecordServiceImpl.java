package cn.casair.service.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.domain.HrBillInvoiceRecord;
import cn.casair.dto.HrBillInvoiceRecordDTO;
import cn.casair.repository.HrBillInvoiceRecordRepository;
import cn.casair.mapper.HrBillInvoiceRecordMapper;
import cn.casair.service.HrBillInvoiceRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Optional;
import java.util.List;
/**
 * 开票明细服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrBillInvoiceRecordServiceImpl extends ServiceImpl<HrBillInvoiceRecordRepository, HrBillInvoiceRecord>implements HrBillInvoiceRecordService {


    private final HrBillInvoiceRecordRepository hrBillInvoiceRecordRepository;
    private final HrBillInvoiceRecordMapper hrBillInvoiceRecordMapper;

    public HrBillInvoiceRecordServiceImpl(HrBillInvoiceRecordRepository hrBillInvoiceRecordRepository, HrBillInvoiceRecordMapper hrBillInvoiceRecordMapper){
    this.hrBillInvoiceRecordRepository = hrBillInvoiceRecordRepository;
    this.hrBillInvoiceRecordMapper= hrBillInvoiceRecordMapper;
    }

    /**
     * 创建开票明细
     * @param hrBillInvoiceRecordDTO
     * @return
     */
    @Override
    public HrBillInvoiceRecordDTO createHrBillInvoiceRecord(HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO){
    log.info("Create new HrBillInvoiceRecord:{}", hrBillInvoiceRecordDTO);

    HrBillInvoiceRecord hrBillInvoiceRecord =this.hrBillInvoiceRecordMapper.toEntity(hrBillInvoiceRecordDTO);
    this.hrBillInvoiceRecordRepository.insert(hrBillInvoiceRecord);
    return this.hrBillInvoiceRecordMapper.toDto(hrBillInvoiceRecord);
    }

    /**
     * 修改开票明细
     * @param hrBillInvoiceRecordDTO
     * @return
     */
    @Override
    public Optional<HrBillInvoiceRecordDTO>updateHrBillInvoiceRecord(HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO){
    return Optional.ofNullable(this.hrBillInvoiceRecordRepository.selectById(hrBillInvoiceRecordDTO.getId()))
    .map(roleTemp->{
    HrBillInvoiceRecord hrBillInvoiceRecord =this.hrBillInvoiceRecordMapper.toEntity(hrBillInvoiceRecordDTO);
    this.hrBillInvoiceRecordRepository.updateById(hrBillInvoiceRecord);
    log.info("Update HrBillInvoiceRecord:{}", hrBillInvoiceRecordDTO);
    return hrBillInvoiceRecordDTO;
    });
    }

    /**
     * 查询开票明细详情
     * @param id
     * @return
     */
    @Override
    public HrBillInvoiceRecordDTO getHrBillInvoiceRecord(String id){
    log.info("Get HrBillInvoiceRecord :{}",id);

    HrBillInvoiceRecord hrBillInvoiceRecord =this.hrBillInvoiceRecordRepository.selectById(id);
    return this.hrBillInvoiceRecordMapper.toDto(hrBillInvoiceRecord);
    }

    /**
     * 删除开票明细
     * @param id
     */
    @Override
    public void deleteHrBillInvoiceRecord(String id){
    Optional.ofNullable(this.hrBillInvoiceRecordRepository.selectById(id))
    .ifPresent(hrBillInvoiceRecord ->{
    this.hrBillInvoiceRecordRepository.deleteById(id);
    log.info("Delete HrBillInvoiceRecord:{}", hrBillInvoiceRecord);
    });
    }

    /**
     * 批量删除开票明细
     * @param ids
     */
    @Override
    public void deleteHrBillInvoiceRecord(List<String>ids){
    log.info("Delete HrBillInvoiceRecords:{}",ids);
    this.hrBillInvoiceRecordRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询开票明细
     * @param hrBillInvoiceRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO,Long pageNumber,Long pageSize){
    Page<HrBillInvoiceRecord>page=new Page<>(pageNumber,pageSize);
    QueryWrapper<HrBillInvoiceRecord>qw=new QueryWrapper<>(this.hrBillInvoiceRecordMapper.toEntity(hrBillInvoiceRecordDTO));
    qw.orderByDesc("id");

    IPage iPage=this.hrBillInvoiceRecordRepository.selectPage(page,qw);
    iPage.setRecords(this.hrBillInvoiceRecordMapper.toDto(iPage.getRecords()));
    return iPage;
    }

    /**
     * 根据发票id获取发票明细
     *
     * @param id
     * @return
     */
    @Override
    public List<HrBillInvoiceRecordDTO> getByInvoiceId(String id) {

        return hrBillInvoiceRecordRepository.getByInvoiceId(id);
    }

    /**
     * 根据申请发票id删除发票明细
     *
     * @param id
     */
    @Override
    public void deleteByInvoiceId(String id) {
        hrBillInvoiceRecordRepository.deleteByInvoiceId(id);
    }

    /**
     * 修改开票明细状态
     * @param ids 明细Dis
     * @param state 锁定状态
     */
    @Override
    public void updateState(List<String> ids, Integer state) {
        hrBillInvoiceRecordRepository.updateState(ids,state);
    }

    /**
     * 根据开票ID修改开票明细状态
     * @param invoiceIds 开票IDS
     * @param invoiceTypes 开票类型
     * @param state 锁定状态
     */
    @Override
    public int updateStateByInvoiceId(List<String> invoiceIds,List<HrBillInvoiceRecordDTO> invoiceTypes, Integer state) {
        return hrBillInvoiceRecordRepository.updateStateByInvoiceId(invoiceIds, invoiceTypes, state);
    }

    /**
     * 根据明细ID查询
     * @param invoiceRecordIds
     * @return
     */
    @Override
    public List<HrBillInvoiceRecordDTO> getByIdBatch(List<String> invoiceRecordIds) {
        return hrBillInvoiceRecordRepository.getByIdBatch(invoiceRecordIds);
    }
}
