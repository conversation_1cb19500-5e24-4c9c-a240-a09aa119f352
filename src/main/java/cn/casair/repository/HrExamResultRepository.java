package cn.casair.repository;

import cn.casair.domain.HrExamResult;
import cn.casair.domain.HrQuestion;
import cn.casair.dto.HrExamResultDTO;
import cn.casair.dto.HrQuestionDTO;
import cn.casair.dto.excel.HrExamResultTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

/**
 * 考试结果表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Repository
public interface HrExamResultRepository extends BaseMapper<HrExamResult> {

    /**
     * 获取员工入职考试结果
     *
     * @param staffId
     * @param clientId
     * @return int
     * <AUTHOR>
     * @date 2021/10/26
     **/
    int getStaffEntranceExaminationState(@Param("staffId") String staffId, @Param("clientId") String clientId);

    IPage<HrQuestionDTO> examResultDetail(Page<HrQuestion> page, @Param("param") HrExamResultDTO hrExamResultDTO);

    List<HrExamResultTemplate> exportExamResults(@Param("ids")List<String> ids);











    void updatetype(@Param("staffId")String staffId, @Param("type")Integer type);

    void updateExamResult(@Param("staffId")String staffId, @Param("type")String type,@Param("stationName")String stationName, @Param("brochureId")String brochureId);

    void writtenwUpdateTypes(@Param("writtenID")List<String> writtenID);

    void interviewUpdateTypes(@Param("staffIdList")List<String> staffIdList);

    void writtenwUpdateType(@Param("professionName")String professionName,@Param("staffId") String staffId, @Param("brochureId")String brochureId);

    void updateHrExamResult(@Param("param1")HrExamResult hrExamResult);

    void interviewUpdateType(@Param("staffIdList")List<String> staffIdList, @Param("stationName")String stationName, @Param("brochureId")String brochureId);

    List<HrExamResult> selectWrittenPassLine(@Param("staffIdList")List<String> staffIdList, @Param("peoplesum")int peoplesum, @Param("stationName")String stationName, @Param("brochureName")String brochureName);

    void writtenwUpdateTypeS(@Param("professionName")String professionName,@Param("staffId") String staffId, @Param("brochureId")String brochureId);

    void interviewUpdateTypeS(@Param("staffIdList")List<String> staffIdList, @Param("stationName")String stationName, @Param("brochureId")String brochureId);

    List<String> selectLists(@Param("stationName")String stationName, @Param("brochureId")String brochureId);


    List<HrExamResult> selectWrittenPassLines(@Param("numberSum")Long numberSum, @Param("stationName")String stationName, @Param("brochureName")String brochureName);

    void updateExamResulter(@Param("staffId")String staffId, @Param("stationName")String stationName, @Param("id")String id, @Param("type")String type);

    void updateExamResults(@Param("sId")String sId, @Param("stationName")String stationName, @Param("bid")String bid, @Param("type")String type);

    void noWrittenUpdateType(@Param("noStaffIdList")List<String> noStaffIdList);
    void noInterviewUpdateType(@Param("noStaffIdList")String noStaffIdList, @Param("stationName")String stationName, @Param("id")String id, @Param("type")String type);

    List<HrExamResult> selectWrittenPassLiness(@Param("numberSum")Long numberSum, @Param("stationName")String stationName, @Param("brochureName")String brochureName);

    List<HrExamResult> selectWrittenPassLinesss(Long peoplesums, String stationName, String brochureName);
    void noInterviewUpdateType(@Param("noStaffIdList")List<String> noStaffIdList);
}
