package cn.casair.service;

import cn.casair.domain.HrWelfareCompensation;
import cn.casair.dto.HrEmployeeWelfareDTO;
import cn.casair.dto.HrWelfareAdditionDTO;
import cn.casair.dto.HrWelfareCompensationDTO;
import cn.casair.dto.HrWelfareCompensationStatistics;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 员工福利补差统计服务类
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
public interface HrWelfareCompensationService extends IService<HrWelfareCompensation> {

    /**
     * 员工福利补差结果导出
     *
     * @return void
     * <AUTHOR>
     * @date 2021/12/30
     **/
    String exportWelfareCompensation(Map<String, String> params);

    /**
     * 获取员工福利补差列表
     *
     * @param hrWelfareCompensationDTO
     * @return list
     * <AUTHOR>
     * @date 2021/12/18
     **/
    List<HrWelfareCompensationDTO> getStaffWelfareCompensation(HrWelfareCompensationDTO hrWelfareCompensationDTO);

    /**
     * 获取员工福利补差统计
     *
     * @param clientId
     * @param staffId
     * @return cn.casair.dto.HrWelfareCompensationStatistics
     * <AUTHOR>
     * @date 2021/11/2
     **/
    HrWelfareCompensationStatistics getStaffMakeUpStatistics(String clientId, String staffId);

    /**
     * 获取员工缴费年月未使用补差列表
     *
     * @param clientId
     * @param staffId
     * @return void
     * <AUTHOR>
     * @date 2021/11/2
     **/
    List<HrWelfareCompensation> getStaffWelfareCompensationByPaymentDate(String clientId, String staffId);

    /**
     * 创建员工福利补差统计
     *
     * @param hrWelfareCompensationDTO
     * @return
     */
    HrWelfareCompensationDTO createHrWelfareCompensation(HrWelfareCompensationDTO hrWelfareCompensationDTO);

    /**
     * 修改员工福利补差统计
     *
     * @param hrWelfareCompensationDTO
     * @return
     */
    Optional<HrWelfareCompensationDTO> updateHrWelfareCompensation(HrWelfareCompensationDTO hrWelfareCompensationDTO);

    /**
     * 查询员工福利补差统计详情
     *
     * @param id
     * @return
     */
    HrWelfareCompensationDTO getHrWelfareCompensation(String id);

    /**
     * 删除员工福利补差统计
     *
     * @param id
     */
    void deleteHrWelfareCompensation(String id);

    /**
     * 批量删除员工福利补差统计
     *
     * @param ids
     */
    void deleteHrWelfareCompensation(List<String> ids);

    /**
     * 确认福利补差数据
     *
     * @param ids
     * <AUTHOR>
     * @date 2021/11/10
     **/
    void confirmHrWelfareCompensation(List<String> ids);


    /**
     * 分页查询员工福利补差统计
     *
     * @param hrWelfareCompensationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrWelfareCompensationDTO> findPage(HrWelfareCompensationDTO hrWelfareCompensationDTO, Long pageNumber, Long pageSize);

    /**
     * 编辑员工福利
     * 计算补差
     *
     * @param hrEmployeeWelfareDTO
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021/10/29
     **/
    List<String> updateEmployeeWelfare(HrEmployeeWelfareDTO hrEmployeeWelfareDTO);

    /**
     * 批量修改员工福利
     *
     * @param hrEmployeeWelfareDTO
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021/10/29
     **/
    List<String> batchUpdateEmployeeWelfare(HrEmployeeWelfareDTO hrEmployeeWelfareDTO);

    /**
     * 分页查询薪金税差
     *
     * @param hrWelfareCompensationDTO
     * @param pageNumber
     * @param pageSize
     * @return org.springframework.http.ResponseEntity<?>
     * <AUTHOR>
     * @date 2021/11/10
     **/
    IPage<HrWelfareCompensationDTO> findSalaryTaxPage(HrWelfareCompensationDTO hrWelfareCompensationDTO, Long pageNumber, Long pageSize);

    /**
     * 薪金税差
     *
     * @param id
     * @return cn.casair.dto.HrWelfareCompensationDTO
     * <AUTHOR>
     * @date 2021/11/11
     **/
    HrWelfareAdditionDTO getSalaryTaxDetail(String id);

    /**
     * 薪金税差导出
     *
     * @param hrWelfareCompensationDTO
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2021/11/11
     **/
    String salaryTaxExport(HrWelfareCompensationDTO hrWelfareCompensationDTO, HttpServletResponse response);
}
