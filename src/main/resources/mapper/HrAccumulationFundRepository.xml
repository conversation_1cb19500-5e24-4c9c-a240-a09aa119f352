<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrAccumulationFundRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, type_name, area, payee_name, payee_account, opening_bank, unit_scale, personage_scale,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>
    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrAccumulationFund">
                    <id column="id" property="id"/>
                    <result column="type_name" property="typeName"/>
                    <result column="area" property="area"/>
                    <result column="payee_name" property="payeeName"/>
                    <result column="payee_account" property="payeeAccount"/>
                    <result column="unit_scale" property="unitScale"/>
                    <result column="personage_scale" property="personageScale"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

    <select id="getAccumulationFundTypeList" resultType="cn.casair.dto.HrAccumulationFundDTO">
        SELECT
            id,
            type_name
        FROM
            hr_accumulation_fund
        WHERE
            is_delete = 0
        ORDER BY
            created_date DESC
    </select>

    <select id="getClientByProvidentFundTypeId" resultType="cn.casair.domain.HrClient">
        SELECT hc.*
        FROM hr_client hc
                 LEFT JOIN hr_accumulation_fund haf ON haf.id = hc.provident_fund_type_id
            AND haf.is_delete = 0
        WHERE hc.is_delete = 0
          AND haf.id = #{id}
    </select>

    <select id="getClientAccumulationFund" resultType="cn.casair.domain.HrAccumulationFund">
        SELECT haf.*
        FROM hr_accumulation_fund haf
                 LEFT JOIN hr_client hc ON hc.provident_fund_type_id = haf.id
            AND hc.is_delete = 0
        WHERE haf.is_delete = 0
          AND hc.id IN
        <foreach collection="clientIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

</mapper>
