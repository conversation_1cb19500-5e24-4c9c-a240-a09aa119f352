<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrContractGroupRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, contract_group_name, contract_required_accessories, contract_optional_accessories, contract_template, contract_frequency, contract_update_date,
        is_delete,created_by,created_date,last_modified_by,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrContractGroup">
        <id column="id" property="id"/>
        <result column="contract_group_name" property="contractGroupName"/>
        <result column="contract_required_accessories" property="contractRequiredAccessories"/>
        <result column="contract_optional_accessories" property="contractOptionalAccessories"/>
        <result column="contract_template" property="contractTemplate"/>
        <result column="contract_frequency" property="contractFrequency"/>
        <result column="contract_update_date" property="contractUpdateDate"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <update id="frequencyPlus">
        UPDATE hr_contract_group
        SET contract_frequency = contract_frequency + 1
        WHERE id = #{contractGroupId}
    </update>

    <select id="selectPageContractGroup" resultType="cn.casair.domain.HrContractGroup">
        SELECT * FROM hr_contract_group
        WHERE is_delete=0
        <if test="param1.contractGroupName!=null and param1.contractGroupName!=''">
            and contract_group_name like concat('%', #{param1.contractGroupName},'%')
        </if>
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </select>

    <select id="selectName" resultType="cn.casair.dto.HrContractGroupDTO">
        SELECT *
        FROM `hr_contract_group`
        where contract_group_name = #{contractGroupName}
          and is_delete = 0
    </select>

    <select id="selectContractGroupList" resultType="cn.casair.dto.HrContractGroupDTO">
        SELECT
            hcg.*
        FROM
            hr_contract_group hcg
        WHERE
            hcg.is_delete = 0
        ORDER BY
            hcg.contract_frequency DESC,
            hcg.created_date DESC
    </select>
</mapper>
