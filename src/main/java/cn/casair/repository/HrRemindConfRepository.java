package cn.casair.repository;
import cn.casair.domain.HrRemindConf;
import cn.casair.dto.HrRemindConfDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 提醒配置表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Repository
public interface HrRemindConfRepository extends BaseMapper<HrRemindConf> {

    /**
     * 根据提醒key获取配置
     *
     * @param remindKey
     * @return cn.casair.domain.HrRemindConf
     * <AUTHOR>
     * @date 2021/11/19
     **/
    HrRemindConf selectByRemindKey(@Param("remindKey") String remindKey);

    /**
     * 获取指定提醒配置规则
     * @param currentRoleId 角色
     * @param remindKey
     * @return
     */
    HrRemindConfDTO getRemindConfByRoleAndKey(@Param("currentRoleId") Integer currentRoleId, @Param("remindKey") String remindKey);

}
