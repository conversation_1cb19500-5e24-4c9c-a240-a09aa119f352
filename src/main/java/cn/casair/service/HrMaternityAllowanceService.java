package cn.casair.service;

import cn.casair.domain.HrMaternityAllowance;
import cn.casair.dto.HrMaternityAllowanceDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 生育津贴服务类
 *
 * <AUTHOR>
 * @since 2022-08-13
 */
public interface HrMaternityAllowanceService extends IService<HrMaternityAllowance> {


    /**
     * 创建生育津贴
     *
     * @param hrMaternityAllowanceDTO
     * @return
     */
    HrMaternityAllowanceDTO createHrMaternityAllowance(HrMaternityAllowanceDTO hrMaternityAllowanceDTO);

    /**
     * 修改生育津贴
     *
     * @param hrMaternityAllowanceDTO
     * @return
     */
    Optional<HrMaternityAllowanceDTO> updateHrMaternityAllowance(HrMaternityAllowanceDTO hrMaternityAllowanceDTO);

    /**
     * 查询生育津贴详情
     *
     * @param id
     * @return
     */
    HrMaternityAllowanceDTO getHrMaternityAllowance(String id);

    /**
     * 删除生育津贴
     *
     * @param id
     */
    void deleteHrMaternityAllowance(String id);

    /**
     * 批量删除生育津贴
     *
     * @param ids
     */
    void deleteHrMaternityAllowance(List<String> ids);

    /**
     * 分页查询生育津贴
     *
     * @param hrMaternityAllowanceDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrMaternityAllowanceDTO hrMaternityAllowanceDTO, Long pageNumber, Long pageSize);

    /**
     * 导出
     *
     * @param hrMaternityAllowanceDTO
     * @return
     */
    String export(HrMaternityAllowanceDTO hrMaternityAllowanceDTO);

    /**
     * 批量审核
     *
     * @param hrMaternityAllowanceDTO
     */
    void batchApproval(HrMaternityAllowanceDTO hrMaternityAllowanceDTO);

    /**
     * 确认生育津贴
     *
     * @param hrMaternityAllowanceDTO
     */
    void confirmMaternityAllowance(HrMaternityAllowanceDTO hrMaternityAllowanceDTO);

}
