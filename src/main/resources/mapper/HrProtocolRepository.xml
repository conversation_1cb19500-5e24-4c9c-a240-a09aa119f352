<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrProtocolRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, agreement_number, agreement_title, agreement_end_date, settlement_method, service_charge, agreement_start_date, states, client_id, reason_termination_agreement, remark, agreement_termination_date,
        service_fee_type, service_fee_data, calculation_formula,invoice_type,
        archive_status, archive_time, archive_address,
last_modified_date                    ,is_delete                    ,created_by                    ,last_modified_by                    ,created_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrProtocol">
        <id column="id" property="id"/>
        <result column="agreement_number" property="agreementNumber"/>
        <result column="agreement_title" property="agreementTitle"/>
        <result column="agreement_end_date" property="agreementEndDate"/>
        <result column="settlement_method" property="settlementMethod"/>
        <result column="service_charge" property="serviceCharge"/>
        <result column="agreement_start_date" property="agreementStartDate"/>
        <result column="states" property="states"/>
        <result column="client_id" property="clientId"/>
        <result column="reason_termination_agreement" property="reasonTerminationAgreement"/>
        <result column="remark" property="remark"/>
        <result column="agreement_termination_date" property="agreementTerminationDate"/>
        <result column="service_fee_type" property="serviceFeeType"/>
        <result column="service_fee_data" property="serviceFeeData"/>
        <result column="calculation_formula" property="calculationFormula"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="archive_status" property="archiveStatus"/>
        <result column="archive_time" property="archiveTime"/>
        <result column="archive_address" property="archiveAddress"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
    </resultMap>
    <sql id="findSQl">
        SELECT
        any_value ( hp.id ) id,
        any_value ( hp.agreement_number ) agreement_number,
        any_value ( hp.agreement_title ) agreement_title,
        any_value ( hp.agreement_end_date ) agreement_end_date,
        any_value ( hp.settlement_method ) settlement_method,
        any_value ( hp.renew_type ) renew_type,
        any_value ( hp.agreement_owned_customer ) agreement_owned_customer,
        any_value ( hp.service_charge_type ) service_charge_type,
        any_value ( hp.service_charge ) service_charge,
        any_value ( hp.agreement_type ) agreement_type,
        any_value ( hp.agreement_start_date ) agreement_start_date,
        any_value ( hp.protocol_id ) protocol_id,
        any_value ( hp.type) type,
        any_value ( hp.types) types,
        any_value ( hp.states ) states,
        any_value ( hp.last_modified_date ) last_modified_date,
        any_value ( hp.client_id ) client_id,
        any_value ( hp.reason_termination_agreement ) reason_termination_agreement,
        any_value ( hp.appendix_id ) appendix_id,
        any_value ( hp.remark ) remark,
        any_value ( hp.agreement_termination_date ) agreement_termination_date,
        any_value ( hp.archive_status) archive_status,
        any_value ( hp.archive_time) archive_time,
        any_value ( hp.archive_address) archive_address,
        any_value ( hc.client_name ) client_name,
        any_value ( hc.unit_number ) unitNumber,
        any_value ( hc.business_type ) business_type
        FROM hr_client hc
        LEFT JOIN hr_protocol hp on hc.id = hp.client_id
        WHERE hp.is_delete = 0 AND hc.is_delete = 0 and hp.type=1
        <if test="param1.clientIdList!=null and param1.clientIdList.size() > 0">
            AND hp.client_id IN
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.ids!=null and param1.ids.size() > 0">
            AND hp.id IN
            <foreach collection="param1.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.statesList!=null and param1.statesList.size() > 0">
            AND hp.states IN
            <foreach collection="param1.statesList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.typesQuery != null">
            <choose>
                <when test="param1.typesQuery == 1">
                    AND hp.types = 1
                </when>
                <when test="param1.typesQuery == 2">
                    AND hp.types IS NULL
                </when>
                <otherwise>
                    AND (hp.types = 1 OR hp.types IS NULL)
                </otherwise>
            </choose>
        </if>
        <if test="param1.agreementTypeList!=null and param1.agreementTypeList.size() > 0">
            AND hp.agreement_type IN
            <foreach collection="param1.agreementTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.settlementMethodList!=null and param1.settlementMethodList.size() > 0">
            AND hp.settlement_method IN
            <foreach collection="param1.settlementMethodList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.agreementNumber!=null and param1.agreementNumber!=''">
            and hp.agreement_number like concat('%',#{param1.agreementNumber},'%')
        </if>
        <if test="param1.agreementStartDate!=null  ">
            AND agreement_start_date <![CDATA[ >= ]]> #{param1.agreementStartDate}
        </if>
        <if test="param1.agreementStartDateend!=null ">
            AND agreement_start_date <![CDATA[ <= ]]> #{param1.agreementStartDateend}
        </if>
        <if test="param1.agreementEndDate!=null  ">
            AND agreement_end_date <![CDATA[ >= ]]> #{param1.agreementEndDate}
        </if>
        <if test="param1.agreementEndDateend!=null  ">
            AND agreement_end_date <![CDATA[ <= ]]> #{param1.agreementEndDateend}
        </if>
        <if test="param1.archiveStatusList != null and param1.archiveStatusList.size() > 0">
            AND hp.archive_status IN
            <foreach collection="param1.archiveStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.archiveTimeStart != null">
            AND hp.archive_time  <![CDATA[ >= ]]>#{param1.archiveTimeStart}
        </if>
        <if test="param1.archiveTimeEnd != null">
            AND hp.archive_time  <![CDATA[ <= ]]>#{param1.archiveTimeEnd}
        </if>
        <if test="param1.archiveAddress != null and param1.archiveAddress != ''">
            AND hp.archive_address like concat('%',#{param1.archiveAddress},'%')
        </if>
        GROUP BY hp.agreement_number, hp.client_id
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </sql>
    <update id="updateClient">
        UPDATE hr_client
        SET business_type=#{param.businessType}
        WHERE id = #{param.clientId}
          and is_delete = 0
          and status = 0
    </update>

    <select id="selectUseingProtocolByClientId" resultType="cn.casair.domain.HrProtocol">
        SELECT
            *
        FROM hr_protocol
        WHERE
            is_delete = 0
            AND client_id = #{clientId}
            AND use_status = 1
            AND states &lt; 2
    </select>

    <select id="selectUserid" resultType="java.lang.String">
        select user_id
        from hr_client
        WHERE id = #{clientId}
          and is_delete = 0
    </select>

    <update id="updateUser">
        UPDATE sys_user
        SET user_status=0
        WHERE id = #{userId}
          and is_delete = 0
    </update>

    <update id="updateProtocols">
        UPDATE hr_protocol
        SET states                      = #{states},
        use_status                      = #{useStatus},
        agreement_termination_date      = #{agreementTerminationDate},
        reason_termination_agreement    = #{reasonTerminationAgreement}
        WHERE agreement_number = #{agreementNumber} AND is_delete = 0
    </update>


    <select id="selectPageProtocol" resultType="cn.casair.dto.HrProtocolDTO">
        <include refid="findSQl"/>
    </select>

    <select id="selectappendixId" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT appendix_id ,id,agreement_title
        FROM hr_protocol
        WHERE id = #{id}
          AND is_delete = 0
    </select>

    <select id="selectnumber" resultType="cn.casair.domain.HrProtocol">
        SELECT *
        from hr_protocol
        where agreement_number = #{unitNumber}
          and is_delete = 0
    </select>

    <select id="selectstatrsum" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT *
        from hr_protocol
        WHERE states   <![CDATA[ < ]]> 2
          and client_id = #{clientId}
    </select>

    <select id="selectHrDocking" resultType="cn.casair.domain.HrDocking">
        SELECT *
        from hr_docking
        WHERE protocol_id = #{id}
          and is_delete = 0
    </select>

    <select id="selectClientId" resultMap="BaseResultMap">
        SELECT *
        FROM hr_protocol
        WHERE client_id = #{id}
          and is_delete = 0
        ORDER BY agreement_start_date DESC
    </select>
    <select id="selectAgreementNumber" resultType="java.lang.String">
        SELECT id
        from hr_protocol
        where agreement_number = #{agreementNumber}
          and is_delete = 0
    </select>
    <select id="getSelectProtocolId" resultType="java.lang.String">
        SELECT *
        from hr_protocol
        where client_id = #{clientId}
          and is_delete = 0
        ORDER BY created_date DESC LIMIT 1
    </select>

    <update id="deleteClient">
        UPDATE hr_protocol
        set `is_delete`=1,
            states=3
        where client_id = #{id}
    </update>
    <update id="updateAppendixId">
        UPDATE hr_protocol
        set `appendix_id`=#{param1.appendixId}
        where id = #{param1.id}
    </update>

    <update id="updateProtocolstates">
        UPDATE
            hr_protocol
        SET states = #{states}
        WHERE agreement_number = #{agreementNumber}
          AND is_delete = 0
    </update>
    <update id="updateProtocolstatess">
        UPDATE hr_protocol
        SET states=#{states}
        WHERE agreement_number = #{agreementNumber}
          AND is_delete = 0
    </update>

    <update id="updateProtocolsStatus">
       UPDATE hr_protocol SET use_status = 2 WHERE is_delete = 0 AND client_id = #{clientId}
    </update>

    <update id="updateProtocolRenewType">
       UPDATE hr_protocol SET use_status = 1 WHERE is_delete = 0 AND id = #{id}
    </update>

    <update id="upDateProtocol">
        UPDATE hr_protocol SET types = 1 WHERE is_delete = 0 AND id = #{protocolId}
    </update>

    <update id="updateProtocolUseStatus">
        UPDATE hr_protocol SET use_status = 2 WHERE client_id = #{clientId} AND agreement_number =#{agreementNumber} AND is_delete = 0
    </update>

    <update id="updateFirstSignTime">

        UPDATE hr_protocol
        set first_sign_time=#{firstSignTime}
        where id = #{id}
          and is_delete = 0
    </update>

    <select id="selectListClentId" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT
        any_value(hp.id) id,
        any_value(hc.client_name)clientName,
        any_value(hp.agreement_type)agreementType,
        any_value(hp.agreement_number)agreementNumber,
        any_value(hp.agreement_title)agreementTitle,
        any_value(hp.settlement_method)settlementMethod,
        any_value(hp.agreement_start_date)agreementStartDate,
        any_value(hp.agreement_end_date)agreementEndDate,
        any_value(hp.states)states
        FROM
        hr_protocol hp
        LEFT JOIN hr_client hc on hp.client_id=hc.id  and hc.is_delete=0
        where
        hp.client_id IN
        <foreach collection=" clientIdLista" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        and hp.is_delete = 0
        and hp.use_status <![CDATA[ < ]]>2
        GROUP BY hp.agreement_number
    </select>

    <select id="selecrClientName" resultType="java.lang.String">
        SELECT client_name
        FROM hr_client
        where id = #{agreementOwnedCustomer}
          and is_delete = 0
    </select>

    <select id="selecttime" resultType="java.lang.Integer">
        SELECT rule_day
        FROM `hr_remind_conf`
        where remind_key = #{customer_agreement}
          and is_delete = 0
    </select>


    <select id="selecthrProtocolsum" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT *
        from hr_protocol
        where agreement_number = #{agreementNumber}
          and use_status = 1
          and is_delete = 0
    </select>

    <!--
     查询 [指定客户id] 的 所有父级客户List (包含指定客户)
     【按照层级深度依次排序,  父级排在子集后面】

     ！！！！不建议直接使用!!!!!!     e 不建议使用
           系统内客户层级可以整体移动 sql 并不是总能满足预期功能
       -->
    <select id="selectAllParentClientById" resultType="cn.casair.domain.HrClient">
        SELECT t3.id, t3.parent_id
        FROM (SELECT t1.*,
                     IF(FIND_IN_SET(id, @ids) > 0, @ids := CONCAT( parent_id,',', @ids), '0') AS isparent
              FROM (SELECT t.id, t.parent_id, t.is_delete
                    FROM hr_client AS t
                    ORDER BY t.id DESC) t1,
                   (SELECT @ids := #{id}) t2) t3
        WHERE t3.isparent != '0'  and t3.is_delete=0
    </select>

    <update id="updateRemark">
        UPDATE hr_protocol
        set remark=#{remark}
        where id = #{id}
          and is_delete = 0
    </update>

    <update id="updateUserStatus">
        UPDATE sys_user
        SET user_status=1
        WHERE id = #{userId}
          and is_delete = 0
    </update>

    <update id="updateStall">
        UPDATE hr_talent_staff
        SET protocol_id = #{pid},  `status` = 0
        WHERE client_id = #{cid} AND staff_status != #{staffStatus} AND is_delete = 0
    </update>

    <update id="updateTalentStaff">
        UPDATE hr_talent_staff
        SET `status` = 1
        WHERE protocol_id = #{id} AND is_delete = 0
    </update>

    <update id="updateDelect">
        UPDATE hr_protocol
        set is_delete=1
        where agreement_number = #{agreementNumber}

    </update>
    <update id="updateus">
        UPDATE hr_protocol
        SET types=1
        WHERE client_id = #{userId}
          and is_delete = 0
          and use_status = 1
    </update>

    <update id="upDateProtocols">
        UPDATE hr_protocol
        SET use_status = 2
        WHERE id = #{id}
          and is_delete = 0
          and use_status = 1
    </update>

    <select id="selecttimes" resultType="cn.casair.dto.HrRemindConfDTO">
        SELECT *
        FROM `hr_remind_conf`
        where remind_key = #{customer_agreement}
          and is_delete = 0
    </select>

    <select id="getClientProtocolList" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT
            ANY_VALUE(id) id,
            ANY_VALUE(agreement_number)agreement_number,
            ANY_VALUE(agreement_title)agreement_title,
            ANY_VALUE(agreement_type)agreement_type,
            ANY_VALUE(agreement_end_date)agreement_end_date,
            ANY_VALUE(agreement_owned_customer)agreement_owned_customer,
            ANY_VALUE(types)types,
            ANY_VALUE(type)type,
            ANY_VALUE(protocol_id)protocol_id,
            ANY_VALUE(settlement_method)settlement_method,
            ANY_VALUE(use_status)use_status,
            ANY_VALUE(renew_type)renew_type,
            ANY_VALUE(service_charge)service_charge,
            ANY_VALUE(service_charge_type)service_charge_type,
            ANY_VALUE(appendix_id)appendix_id,
            ANY_VALUE(agreement_start_date)agreement_start_date,
            ANY_VALUE(states)states,
            ANY_VALUE(client_id)client_id,
            ANY_VALUE(reason_termination_agreement)reason_termination_agreement,
            ANY_VALUE(remark)remark,
            ANY_VALUE(agreement_termination_date)agreement_termination_date,
            ANY_VALUE(archive_status) archive_status,
            ANY_VALUE(archive_time) archive_time,
            ANY_VALUE(archive_address) archive_address,
            ANY_VALUE(created_by)created_by,
            ANY_VALUE(created_date)created_date,
            ANY_VALUE(last_modified_by)last_modified_by,
            ANY_VALUE(last_modified_date)last_modified_date,
            ANY_VALUE(is_delete)is_delete
        FROM
            hr_protocol
        WHERE
            is_delete = 0 AND client_id = #{clientId}
            AND states IN (0,1) AND use_status = 1
        GROUP BY
            agreement_number
    </select>

    <select id="findList" resultType="cn.casair.dto.HrProtocolDTO">
        <include refid="findSQl"/>
    </select>

    <select id="getIsUsedProtocol" resultType="cn.casair.domain.HrProtocol">
        SELECT
            *
        FROM hr_protocol
        WHERE
            is_delete = 0
            AND client_id = #{clientId}
            AND use_status = 1
    </select>

    <select id="selectProtocolByStates" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT
            any_value ( hp.id ) id,
            any_value ( hp.client_id ) client_id,
            any_value ( hp.states ) states,
            any_value ( hp.agreement_number ) agreement_number,
            any_value ( hp.agreement_end_date ) agreement_end_date,
            any_value ( hp.agreement_start_date ) agreement_start_date,
            any_value ( hp.agreement_termination_date ) agreement_termination_date
        FROM
            hr_protocol hp
        WHERE
            hp.is_delete = 0
            AND states IN
            <foreach collection=" states" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        GROUP BY
            hp.agreement_number
    </select>

    <update id="updateArchiveInfo">
        UPDATE hr_protocol
        <set>
            <if test="archiveStatus != null">
                archive_status = #{archiveStatus},
            </if>
            <if test="archiveTime != null">
                archive_time = #{archiveTime},
            </if>
            <if test="archiveAddress != null and archiveAddress != ''">
                archive_address = #{archiveAddress},
            </if>
        </set>
        where id = #{id}
        and is_delete = 0
    </update>

    <update id="updateOriginalProtocol">
        UPDATE hr_protocol
        SET types = 1,
            use_status = 2
        WHERE client_id = #{clientId}
          and agreement_number = #{agreementNumber}
          and is_delete = 0
          and use_status = 1
    </update>
</mapper>
