package cn.casair.service;

import cn.casair.domain.Menu;
import cn.casair.dto.MenuDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 菜单表服务类
 *
 * <AUTHOR>
 * @since 2020-01-13
 */
public interface MenuService extends IService<Menu> {

    /**
     * 创建菜单表
     *
     * @param menuDTO
     * @return
     */
    MenuDTO createMenu(MenuDTO menuDTO);

    /**
     * 修改菜单表
     *
     * @param menuDTO
     * @return
     */
    Optional<MenuDTO> updateMenu(MenuDTO menuDTO);

    /**
     * 查询菜单表详情
     *
     * @param id
     * @return
     */
    MenuDTO getMenu(Integer id);

    /**
     * 删除菜单表
     *
     * @param id
     */
    void deleteMenu(Integer id);

    /**
     * 批量删除菜单表
     *
     * @param ids
     */
    void deleteMenu(List<Integer> ids);

    /**
     * 分页查询菜单表
     *
     * @param menuDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(MenuDTO menuDTO, Long pageNumber, Long pageSize);

    /**
     * @param userId 用户ID
     * @return List<EsMenuDTO>
     * @Description: 根据用户id查询菜单列表
     * @version: 1.0
     * @author: liuchenghao
     * @createDate: 2020/1/9 16:13
     */
    List<MenuDTO> findListByAdminUserId(String userId);

    /**
     * @param roleId 角色ID
     * @return List<EsMenuDTO>
     * @Description: 根据角色id查询菜单列表
     * @version: 1.0
     * @author: yanglei
     * @createDate: 2020/5/12 16:15
     */
    List<MenuDTO> findListByRoleId(Integer roleId);

    /**
     * @param menuList
     * @param parent
     * @return
     * @Description: 递归每个父级赋值子列表
     * @version: 1.0
     * @author: liuchenghao
     * @createDate: 2020/1/10 9:10
     */
    List<MenuDTO> sort(List<Menu> menuList, MenuDTO parent);

    Menu getByTableName(String tableName);

    List<MenuDTO> initTree(List<MenuDTO> list);

    /**
     * 根据当前用户查询他所拥有的按钮权限list
     *
     * @param userId
     * @return java.util.List<cn.casair.dto.MenuDTO>
     * <AUTHOR> yanglei 2020/4/29 11:12
     */
    List<String> findButtonListByAdminUserId(String userId);

    List<MenuDTO> listAll();
}
