<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrPersonalRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , client_id, protocol_id, system_num, name, picture_path, nationality, nation, certificate_type, certificate_num, birthday, sex, phone, native_place, height, email, marital_status,
            highest_education, household_registration, domicile_place, census_register_address, census_register_postcode, politics_status, party_date, party_branch, contact_address, contact_postcode,
            iz_military, military_start_date, military_end_date, rewards_punishment, remark, staff_status, personnel_type, work_status, nick_name, status, iz_default,appendix_ids,
            is_delete,created_by,created_date,last_modified_by,last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrTalentStaff">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="protocol_id" property="protocolId"/>
        <result column="system_num" property="systemNum"/>
        <result column="name" property="name"/>
        <result column="picture_path" property="picturePath"/>
        <result column="nationality" property="nationality"/>
        <result column="nation" property="nation"/>
        <result column="certificate_type" property="certificateType"/>
        <result column="certificate_num" property="certificateNum"/>
        <result column="birthday" property="birthday"/>
        <result column="sex" property="sex"/>
        <result column="phone" property="phone"/>
        <result column="native_place" property="nativePlace"/>
        <result column="height" property="height"/>
        <result column="email" property="email"/>
        <result column="marital_status" property="maritalStatus"/>
        <result column="highest_education" property="highestEducation"/>
        <result column="household_registration" property="householdRegistration"/>
        <result column="domicile_place" property="domicilePlace"/>
        <result column="census_register_address" property="censusRegisterAddress"/>
        <result column="census_register_postcode" property="censusRegisterPostcode"/>
        <result column="politics_status" property="politicsStatus"/>
        <result column="party_date" property="partyDate"/>
        <result column="party_branch" property="partyBranch"/>
        <result column="contact_address" property="contactAddress"/>
        <result column="contact_postcode" property="contactPostcode"/>
        <result column="iz_military" property="izMilitary"/>
        <result column="military_start_date" property="militaryStartDate"/>
        <result column="military_end_date" property="militaryEndDate"/>
        <result column="rewards_punishment" property="rewardsPunishment"/>
        <result column="remark" property="remark"/>
        <result column="staff_status" property="staffStatus"/>
        <result column="personnel_type" property="personnelType"/>
        <result column="work_status" property="workStatus"/>
        <result column="nick_name" property="nickName"/>
        <result column="status" property="status"/>
        <result column="iz_default" property="izDefault"/>
        <result column="appendix_ids" property="appendixIds"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectTalentStaffId" resultType="cn.casair.domain.HrTalentStaff">
        SELECT *
        from hr_talent_staff
        where id = #{uid}
          and is_delete = 0
    </select>
</mapper>
