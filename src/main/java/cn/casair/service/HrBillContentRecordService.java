package cn.casair.service;

import cn.casair.domain.HrBillContentRecord;
import cn.casair.dto.HrBillContentRecordDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface HrBillContentRecordService extends IService<HrBillContentRecord> {


    /**
     * 创建
     *
     * @param hrBillContentRecordDTO
     * @return
     */
    HrBillContentRecordDTO createHrBillContentRecord(HrBillContentRecordDTO hrBillContentRecordDTO);

    /**
     * 修改
     *
     * @param hrBillContentRecordDTO
     * @return
     */
    Optional<HrBillContentRecordDTO> updateHrBillContentRecord(HrBillContentRecordDTO hrBillContentRecordDTO);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    HrBillContentRecordDTO getHrBillContentRecord(String id);

    /**
     * 删除
     *
     * @param id
     */
    void deleteHrBillContentRecord(String id);

    /**
     * 批量删除
     *
     * @param ids
     */
    void deleteHrBillContentRecord(List<String> ids);

    /**
     * 分页查询
     *
     * @param hrBillContentRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillContentRecordDTO hrBillContentRecordDTO, Long pageNumber, Long pageSize);

    /**
     * 分页查询账单日志明细
     *
     * @param hrBillContentRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findRecordPage(HrBillContentRecordDTO hrBillContentRecordDTO, Long pageNumber, Long pageSize);

    /**
     * 账单日志批量下载
     *
     * @param hrBillContentRecordDTO
     * @return void
     * <AUTHOR>
     * @date 2021/11/24
     **/
    void downloadBillBatch(HrBillContentRecordDTO hrBillContentRecordDTO, HttpServletResponse response);
}
