<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrLendingApplyRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_id, client_id, title, detail, start_date, end_date, status,is_delete,created_by,last_modified_by ,created_date                    ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrLendingApply">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="title" property="title"/>
        <result column="detail" property="detail"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="status" property="status"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Select_Sql">
        SELECT
        b.client_name AS clientName,
        c.name AS staffName,
        c.staff_status,
        c.system_num AS systemNum,
        c.certificate_num AS certificateNum,
        su.real_name AS specialized,
        a.id,
        a.title,
        a.detail,
        a.status,
        a.type_list_str,
        a.start_date,
        a.type,
        ham.archives_num archives_num
        FROM
        hr_lending_apply a
        LEFT JOIN hr_client b ON a.client_id = b.id and a.is_delete=0
        LEFT JOIN hr_talent_staff c ON a.staff_id = c.id and c.is_delete=0
        LEFT JOIN hr_archives_manage ham ON c.id = ham.staff_id AND ham.is_delete = 0
        LEFT JOIN sys_user su ON su.id = b.specialized_id AND su.is_delete = 0
        where 1=1
        and b.is_delete=0 AND c.is_delete = 0
        <if test="param.ids!=null and param.ids.size() > 0">
            AND a.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND a.client_id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.findStateList!=null and param.findStateList.size() > 0">
            AND a.status IN
            <foreach collection="param.findStateList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientId != null and param.clientId != ''">
            AND b.id = #{param.clientId}
        </if>
        <if test="param.clientIdList!=null and param.clientIdList.size() > 0">
            AND a.client_id IN
            <foreach collection="param.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientName != null and param.clientName != ''">
            AND b.client_name LIKE CONCAT('%',#{param.clientName},'%')
        </if>
        <if test="param.systemNum != null and param.systemNum != ''">
            AND c.system_num LIKE CONCAT('%',#{param.systemNum},'%')
        </if>
        <if test="param.staffName != null and param.staffName != ''">
            AND c.name LIKE CONCAT('%',#{param.staffName},'%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND c.certificate_num LIKE CONCAT('%',#{param.certificateNum},'%')
        </if>
        <if test="param.staffStatusList!=null and param.staffStatusList.size() > 0">
            AND c.staff_status IN
            <foreach collection="param.staffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.title != null and param.title != ''">
            AND a.title LIKE CONCAT('%',#{param.title},'%')
            OR a.detail LIKE CONCAT('%',#{param.title},'%')
        </if>
        <if test="param.typeListStr != null and param.typeListStr != ''">
            AND a.type_list_str LIKE CONCAT('%',#{param.typeListStr},'%')
        </if>
        <if test="param.createdStartDate != null">
            AND a.created_date &gt;= #{param.createdStartDate}
        </if>
        <if test="param.createdEndDate!=null">
            AND a.created_date &lt; date_add(#{param.createdEndDate}, interval 1 day)
        </if>
        <choose>
            <when test="param.field!=null and param.field!=''">
                <choose>
                    <when test="param.field=='clientName'">
                        ORDER BY b.client_name ${param.order}
                    </when>
                    <when test="param.field=='systemNum'">
                        ORDER BY c.system_num ${param.order}
                    </when>
                    <when test="param.field=='staffName'">
                        ORDER BY c.name ${param.order}
                    </when>
                    <when test="param.field=='certificateNum'">
                        ORDER BY c.certificate_num ${param.order}
                    </when>
                    <when test="param.field=='title'">
                        ORDER BY a.title ${param.order}
                    </when>
                    <when test="param.field=='createdDate'">
                        ORDER BY a.created_date ${param.order}
                    </when>
                    <when test="param.field=='specialized'">
                        ORDER BY su.real_name ${param.order}
                    </when>
                    <otherwise>
                        ORDER BY a.status ${param.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY a.created_date desc
            </otherwise>
        </choose>
    </sql>
    <select id="selectLendingApplyPage" resultType="cn.casair.dto.HrLendingApplyDTO">
        <include refid="Select_Sql"></include>
    </select>
    <select id="findList" resultType="cn.casair.dto.HrLendingApplyDTO">
        <include refid="Select_Sql"></include>
    </select>
    <select id="getLendingApplyById" resultType="cn.casair.dto.HrLendingApplyDTO">
        select
        a.id,
        a.title,
        a.detail,
        a.status,
        a.type_list_str,
        a.start_date,
        a.end_date,
        a.created_date,
        a.return_on_time,
        a.callout_place,
        a.staff_id,
        a.type,
        b.client_name AS clientName,
        c.name AS staffName,
        c.staff_status,
        c.system_num AS systemNum,
        c.certificate_num AS certificateNum,
        c.phone,
        d.id AS manageId,
        d.archives_name AS archivesName,
        d.archives_type AS archivesType
        FROM
        hr_lending_apply a
        LEFT JOIN hr_client b ON a.client_id = b.id
        LEFT JOIN hr_talent_staff c ON a.staff_id = c.id
        LEFT JOIN hr_archives_manage d on d.staff_id = a.staff_id
        WHERE a.is_delete = 0 AND c.is_delete = 0 AND a.id = #{id}
    </select>


</mapper>
