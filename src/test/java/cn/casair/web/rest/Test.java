package cn.casair.web.rest;

import cn.casair.common.utils.BillParseUtils;
import cn.casair.common.utils.excel.CellItem;
import cn.casair.common.utils.excel.ExcelReadUtils;
import cn.hutool.core.io.FileUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.TimeZone;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/12/03 14:43
 */
@Slf4j
public class Test {

    public static void main(String[] args) {
        System.out.println(TimeZone.getDefault()); // 检查输出
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8")); // 强制覆盖
        System.out.println(TimeZone.getDefault()); // 检查输出

//        test("C:\\Users\\<USER>\\Desktop\\cba综合平台\\test\\230", "C:\\Users\\<USER>\\Desktop\\cba综合平台\\test\\liu");
    }

    @SneakyThrows
    public static void test(String oldDir, String newDir) {
        List<String> fileNames = FileUtil.listFileNames(oldDir);
        for (String fileName : fileNames) {
            if (fileName.contains(".xls")) {
                Workbook workbook = ExcelReadUtils.createWorkBook(new File(oldDir + File.separator + fileName));
                Sheet sheet = ExcelReadUtils.getNotHiddenSheetAt(workbook, 0);
                CellItem cellItem = test1(workbook, 0, Collections.singletonList("身份证"));
                if (cellItem == null) {
                    log.error("读取身份证失败=========>:{}", fileName);
                    continue;
                }
                Integer startCol = cellItem.getFirstCol();
                int i = 0;
                int j = 0;
                while (i < 8) {
                    Row row = sheet.getRow(j);
                    j++;
                    Cell cell = null;
                    try {
                        cell = row.getCell(startCol);
                        Object objectValue = null;
                        try {
                            objectValue = BillParseUtils.getCellObjectValue(row, startCol);
                        } catch (IllegalStateException ex) {
                            // 处理身份证X的问题
                            cell.setCellType(CellType.STRING);
                            System.out.println(cell.getStringCellValue());
                            objectValue = BillParseUtils.getCellObjectValue(row, startCol);
                        }
                        String cellValue = objectValue.toString();
                        DataFormat format = workbook.createDataFormat();
                        if (cellValue.length() >= 9) {
                            CellStyle cellStyle = cell.getCellStyle();
                            if (cellValue.contains(".")) {
                                cell.setCellType(CellType.STRING);
                                cellValue = cell.getStringCellValue() + "";
                                cell.setCellValue(cellValue);
                                System.out.println(cellValue);
                            } else {
                                cellStyle.setDataFormat(format.getFormat("@"));
                                cell.setCellStyle(cellStyle);
                                i = 0;
                            }

                        } else {
                            i++;
                        }
                    } catch (Exception e) {
//                        e.printStackTrace();
                        i++;
                    }

                }

                // 生成新的xls
                File newFile = new File(newDir + File.separator + fileName);
                try {
                    FileOutputStream outputStream = new FileOutputStream(newFile);
                    workbook.write(outputStream);
                } catch (FileNotFoundException e) {
                    log.error("File not found : fileName {}  Exception details:{} ", fileName, e);

                } catch (IOException e) {
                    log.error("IO exception  : fileName {}  Exception details:{} ", fileName, e);
                }
            }

        }
    }

    public static CellItem test1(Workbook workbook, Integer sheetIndex, List<String> idStrs) {
        try {
            // 读取指定的sheet页
            Sheet sheet = ExcelReadUtils.getNotHiddenSheetAt(workbook, sheetIndex);
            // 获取该sheet页里所有的合并单元格
            List<CellRangeAddress> cellRangeAddressList = sheet.getMergedRegions();

            for (int i = 0; i <= 10; i++) {

                // 获取某一行
                Row row = sheet.getRow(i);
                int colNum = row.getLastCellNum();

                for (int j = 0; j < colNum; j++) {
                    Cell cell = row.getCell(j);

                    // 如果值不为空，则进行逻辑判断处理
                    String cellValue = ExcelReadUtils.getCellValue(cell);
                    if (StringUtils.isNotBlank(cellValue)) {
                        for (String value : idStrs) {
                            if ("身份证".equals(cellValue.trim()) || cellValue.trim().contains(value)) {
                                CellItem cellItem = new CellItem();
                                cellItem.setValue(value);
                                cellItem.setChildren(new ArrayList<>());

                                CellRangeAddress originCellRangeAddress = ExcelReadUtils.isMerge(cellRangeAddressList, cell);

                                // 如果是合并单元格
                                if (originCellRangeAddress != null) {
                                    cellItem.setFirstRow(originCellRangeAddress.getFirstRow());
                                    cellItem.setLastRow(originCellRangeAddress.getLastRow());
                                    cellItem.setFirstCol(originCellRangeAddress.getFirstColumn());
                                    cellItem.setLastCol(originCellRangeAddress.getLastColumn());
                                } else {
                                    cellItem.setFirstCol(cell.getColumnIndex());
                                    cellItem.setLastCol(cell.getColumnIndex());
                                    cellItem.setFirstRow(cell.getRowIndex());
                                    cellItem.setLastRow(cell.getRowIndex());
                                }
                                return cellItem;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
