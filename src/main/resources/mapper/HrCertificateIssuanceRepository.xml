<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrCertificateIssuanceRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, staff_id, client_id, certificate_name, certificate_purpose, certificate_status, contract_num, appendix_ids,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrCertificateIssuance">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="certificate_name" property="certificateName"/>
        <result column="certificate_purpose" property="certificatePurpose"/>
        <result column="certificate_status" property="certificateStatus"/>
        <result column="contract_num" property="contractNum"/>
        <result column="appendix_ids" property="appendixIds"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="findSql">
        SELECT
            hc.client_name,
            hc.user_id,
            hts.`name`,
            hts.staff_status,
            hts.certificate_num,
            hts.sex,
            IF(hts.sex = 1, '男', '女') AS sexLabel,
            hts.personnel_type,
            hts.phone,
            hswe.station_id,
            hs.profession_name,
            hci.*, hse.basic_wage,
            hse.accumulation_fund_cardinal,
            hse.medical_insurance_cardinal,
            hse.social_security_cardinal,
            hcr.contract_start_date,
            hcr.contract_end_date,
            su.real_name AS specialized,
            ha.origin_name,
            ha.file_url,
            IF(hci.is_default = 0,'暂无所需模板',hpt.title) title
        FROM
            hr_certificate_issuance hci
        LEFT JOIN hr_talent_staff hts ON hci.staff_id = hts.id
        LEFT JOIN hr_client hc ON hc.id = hci.client_id AND hc.is_delete = 0
        LEFT JOIN hr_staff_work_experience hswe ON hswe.staff_id = hts.id  AND hts.client_id = hswe.client_id AND hswe.iz_default = 1 AND hswe.is_delete = 0
        LEFT JOIN hr_station hs ON hswe.station_id = hs.id AND hs.is_delete = 0
        LEFT JOIN hr_staff_emolument hse ON hse.staff_id = hts.id AND hs.is_delete = 0 AND hse.is_delete = 0
        LEFT JOIN sys_user su ON su.id = hc.specialized_id AND su.is_delete = 0
        LEFT JOIN (SELECT hc.staff_id, any_value (hc.id) id, any_value(hc.state) state,any_value(hc.contract_start_date) contract_start_date, any_value(hc.contract_end_date) contract_end_date,any_value (hc.is_delete) is_delete
                   FROM (SELECT * FROM hr_contract WHERE is_delete = 0 AND state in (1,2,3) ORDER BY contract_start_date DESC limit 999999999)as hc GROUP BY hc.staff_id)
        hcr ON hcr.staff_id = hts.id AND hcr.is_delete = 0
        LEFT JOIN hr_proof_template hpt ON hpt.id = hci.appendix_ids AND hpt.is_delete = 0
        LEFT JOIN hr_appendix ha ON hpt.appendix_id = ha.id AND ha.is_delete = 0
    </sql>

    <select id="findPage" resultType="cn.casair.dto.HrCertificateIssuanceDTO">
        <include refid="findSql"/>
        WHERE hci.is_delete = 0 AND hts.is_delete = 0
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hci.client_id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.name != null and param.name != ''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND hts.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.personnelType != null">
            AND hts.personnel_type = #{param.personnelType}
        </if>
        <if test="param.professionName != null and param.professionName != ''">
            AND hs.profession_name LIKE concat('%', #{param.professionName}, '%')
        </if>
        <if test="param.clientId != null">
            AND hc.id = #{param.clientId}
        </if>
        <if test="param.clientName != null and param.clientName != ''">
            AND hc.client_name LIKE concat('%', #{param.clientName}, '%')
        </if>
        <if test="param.certificateName != null and param.certificateName != ''">
            AND hci.certificate_name LIKE concat('%', #{param.certificateName}, '%')
        </if>
        <if test="param.certificateStatus != null">
            AND hci.certificate_status = #{param.certificateStatus}
        </if>
        <if test="param.createdStartDate != null">
            AND hci.created_date &gt;= #{param.createdStartDate}
        </if>
        <if test="param.createdEndDate!=null">
            AND hci.created_date &lt; date_add(#{param.createdEndDate}, interval 1 day)
        </if>
        <if test="param.certificateStatusList!=null and param.certificateStatusList.size() > 0">
            AND hci.certificate_status IN
            <foreach collection="param.certificateStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.sexList!=null and param.sexList.size() > 0">
            AND hts.sex IN
            <foreach collection="param.sexList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.personnelTypeList!=null and param.personnelTypeList.size() > 0">
            AND hts.personnel_type IN
            <foreach collection="param.personnelTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.staffStatusList!=null and param.staffStatusList.size() > 0">
            AND hts.staff_status IN
            <foreach collection="param.staffStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.userIdList!=null and param.userIdList.size() > 0">
            AND su.id IN
            <foreach collection="param.userIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.stationIdList!=null and param.stationIdList.size() > 0">
            AND hs.id IN
            <foreach collection="param.stationIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.roleKey != null">
            AND (hci.is_default = 0 OR hpt.title REGEXP #{param.certificateTitle})
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            ORDER BY  ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY hci.created_date DESC
        </if>
    </select>

    <select id="findById" resultType="cn.casair.dto.HrCertificateIssuanceDTO">
        <include refid="findSql"/>
        WHERE hci.is_delete = 0  AND hts.is_delete = 0 AND hci.id = #{id}
    </select>

    <select id="getCertificateIssuanceList" resultType="cn.casair.dto.HrCertificateIssuanceDTO">
        <include refid="findSql"/>
        WHERE hci.is_delete = 0  AND hts.is_delete = 0
        <if test="param.ids!=null and param.ids.size() > 0">
            AND hci.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hci.client_id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.name != null and param.name != ''">
            AND hts.name LIKE concat('%', #{param.name}, '%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            AND hts.phone LIKE concat('%', #{param.phone}, '%')
        </if>
        <if test="param.certificateNum != null and param.certificateNum != ''">
            AND hts.certificate_num LIKE concat('%', #{param.certificateNum}, '%')
        </if>
        <if test="param.personnelType != null">
            AND hts.personnel_type = #{param.personnelType}
        </if>
        <if test="param.professionName != null and param.professionName != ''">
            AND hs.profession_name LIKE concat('%', #{param.professionName}, '%')
        </if>
        <if test="param.clientId != null">
            AND hc.id = #{param.clientId}
        </if>
        <if test="param.clientName != null and param.clientName != ''">
            AND hc.client_name LIKE concat('%', #{param.clientName}, '%')
        </if>
        <if test="param.certificateName != null and param.certificateName != ''">
            AND hci.certificate_name LIKE concat('%', #{param.certificateName}, '%')
        </if>
        <if test="param.certificateStatus != null">
            AND hci.certificate_status = #{param.certificateStatus}
        </if>
        <if test="param.createdStartDate != null">
            AND hci.created_date &gt;= #{param.createdStartDate}
        </if>
        <if test="param.createdEndDate!=null">
            AND hci.created_date &lt; date_add(#{param.createdEndDate}, interval 1 day)
        </if>
        <if test="param.certificateStatusList!=null and param.certificateStatusList.size() > 0">
            AND hci.certificate_status IN
            <foreach collection="param.certificateStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.sexList!=null and param.sexList.size() > 0">
            AND hts.sex IN
            <foreach collection="param.sexList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.personnelTypeList!=null and param.personnelTypeList.size() > 0">
            AND hts.personnel_type IN
            <foreach collection="param.personnelTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.userIdList!=null and param.userIdList.size() > 0">
            AND su.id IN
            <foreach collection="param.userIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.stationIdList!=null and param.stationIdList.size() > 0">
            AND hs.id IN
            <foreach collection="param.stationIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.roleKey != null">
            <choose>
                <when test="param.roleKey == 1">
                    AND (hci.is_default = 0 OR hpt.title REGEXP #{param.certificateTitle})
                </when>
                <otherwise>
                    AND hpt.title REGEXP #{param.certificateTitle}
                </otherwise>
            </choose>
        </if>
        <if test="param.field != null and param.field != '' and param.order != null and param.order != ''">
            ORDER BY  ${param.field} ${param.order}
        </if>
        <if test="param.field == null or param.field == ''">
            ORDER BY hci.created_date DESC
        </if>
    </select>

    <select id="certificateIssuanceListByStaffId" resultType="cn.casair.dto.HrCertificateIssuanceDTO">
        SELECT
            hc.client_name,
            hci.certificate_name,
            hci.created_date,
            haol.appendix_ids
        FROM
            hr_certificate_issuance hci
        LEFT JOIN hr_talent_staff hts ON hci.staff_id = hts.id
        LEFT JOIN hr_client hc ON hc.id = hci.client_id
        LEFT JOIN hr_apply_op_logs haol ON hci.id = haol.apply_id AND haol.is_delete = 0 AND haol.message LIKE '%线上签章%'
        WHERE
            hci.is_delete = 0 AND hts.id = #{staffId}
        ORDER BY
            hci.created_date
    </select>

    <select id="selectApplyListByRole" resultType="cn.casair.domain.HrCertificateIssuance">
        SELECT
            hci.*
        FROM
            hr_certificate_issuance hci
        LEFT JOIN hr_proof_template hpt ON hci.appendix_ids = hpt.id AND hpt.is_delete = 0
        WHERE hci.is_delete = 0 AND hci.client_id IN
        <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        AND hci.certificate_status IN
        <foreach collection="param.certificateStatusList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        <if test="param.roleKey != null">
            AND (hci.is_default = 0 OR hpt.title REGEXP #{param.title})
        </if>
        ORDER BY created_date DESC LIMIT 5
    </select>

</mapper>
