package cn.casair.ncc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * NCC Token传输对象（DTO）
 * 用于接收 NCC Token获取接口返回的数据
 */
@Data
public class NccTokenDTO {
    
    /**
     * 访问令牌
     * 示例值：154caed9c96dd8ce4938b3cf7e820f7b
     * 是否必传：是
     */
    @JsonProperty("access_token")
    private String accessToken;
    
    /**
     * 令牌过期时间（秒）
     * 示例值：1000000
     * 是否必传：是
     */
    @JsonProperty("expires_in")
    private Long expiresIn;
    
    /**
     * 刷新令牌
     * 示例值：154caed9c96dd8ce4938b3cf7e820f7b
     * 是否必传：是
     */
    @JsonProperty("refresh_token")
    private String refreshToken;
    
    /**
     * 安全密钥
     * 示例值：rXyMJPwIRQZaapbTV+IOBdLRCKVHM3mbKw+s1Ve9jPo=
     * 是否必传：是
     */
    @JsonProperty("security_key")
    private String securityKey;
    
    /**
     * 时间戳
     * 示例值：1756345951246
     * 是否必传：是
     */
    @JsonProperty("ts")
    private Long ts;
    
    /**
     * 授权类型
     * 示例值：client_credentials
     * 是否必传：是
     */
    @JsonProperty("grant_type")
    private String grantType;
}