package cn.casair.repository;

import cn.casair.domain.HrBill;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrStaffEntryQuitExport;
import cn.casair.dto.excel.HrStaffExport;
import cn.casair.dto.excel.HrTalentExport;
import cn.casair.dto.pdf.ContractInfoForPdf;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 员工管理数据库操作类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Repository
public interface HrTalentStaffRepository extends BaseMapper<HrTalentStaff> {

    /**
     * 批量更新员工计算补缴状态
     *
     * @param hrTalentStaffDTO
     * @param jwtUserDTO
     */
    int batchUpdateStaffSupplementaryPayment(@Param("params") HrTalentStaffDTO hrTalentStaffDTO, @Param("user") JWTUserDTO jwtUserDTO);

    /**
     * 更新员工密码
     *
     * @param hrTalentStaffdto
     */
    void updatePassword(@Param("params") HrTalentStaffDTO hrTalentStaffdto);

    /**
     * 根据身份证列表查询员工信息
     *
     * @param idCardList
     * @return
     */
    List<HrTalentStaffDTO> selectByIdCards(@Param("idCardList") List<String> idCardList);

    /**
     * 更新员工第一次登录标识
     *
     * @param staffId
     * @return void
     * <AUTHOR>
     * @date 2022/1/17
     **/
    void updateStaffFirstLoginSign(@Param("staffId") String staffId);

    /**
     * 更新员工为入职完成状态
     *
     * @param staffId
     * @param staffStatus
     * @param izStartEnd
     * @return void
     * <AUTHOR>
     * @date 2021/10/27
     **/
    void staffInductionCompletion(@Param("staffId") String staffId, @Param("staffStatus") Integer staffStatus, @Param("izStartEnd") Integer izStartEnd);

    /**
     * 获取员工基础信息
     *
     * @param clientId
     * @param staffId
     * @return cn.casair.dto.HrTalentStaffDTO
     * <AUTHOR>
     * @date 2021/10/27
     **/
    HrTalentStaffDTO getStaffInfoById(@Param("clientId") String clientId, @Param("staffId") String staffId);

    /**
     * 获取客户员工（账单用）
     *
     * @param clientIds
     * @return cn.casair.domain.HrTalentStaff
     * <AUTHOR>
     * @date 2021/10/26
     **/
    List<HrTalentStaffDTO> selectByClientId(@Param("clientIds") List<String> clientIds);

    /**
     * 根据员工id获取员工福利信息
     *
     * @param id
     * @return cn.casair.dto.HrEmployeeWelfareDTO
     * <AUTHOR>
     * @date 2021/10/24
     **/
    HrEmployeeWelfareDTO findEmployeeWelfareByStaffId(String id);

    /**
     * 查询员工福利导出列表
     *
     * @param hrEmployeeWelfareDTO
     * @param clientIds
     * @return java.util.List<cn.casair.dto.HrEmployeeWelfareDTO>
     * <AUTHOR>
     * @date 2021/10/20
     **/
    List<HrEmployeeWelfareDTO> selectExportListByIds(@Param("params") HrEmployeeWelfareDTO hrEmployeeWelfareDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 分页查询员工福利列表
     *
     * @param page
     * @param hrEmployeeWelfareDTO
     * @param clientIds
     * @return org.springframework.http.ResponseEntity<?>
     * <AUTHOR>
     * @date 2021/10/15
     **/
    IPage<HrEmployeeWelfareDTO> findEmployeeWelfarePage(Page<HrEmployeeWelfareDTO> page, @Param("params") HrEmployeeWelfareDTO hrEmployeeWelfareDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 更新员工状态
     *
     * @param staffId
     * @param staffStatus
     * @return int
     * <AUTHOR>
     * @date 2021/10/15
     **/
    int updateStaffStatus(@Param("staffId") String staffId, @Param("staffStatus") Integer staffStatus);

    /**
     * 更新员工入职流程状态
     *
     * @param staffId
     * @param state
     * @return int
     * <AUTHOR>
     * @date 2021/9/27
     **/
    int updateStaffIzStartEnd(@Param("staffId") String staffId, @Param("state") Integer state);

    /**
     * 获取员工户口所在地地址
     *
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021/9/16
     **/
    List<String> domicilePlaceList();

    /**
     * 获取员工电签所需信息
     *
     * @param staffId 员工id
     * @return cn.casair.dto.pdf.ContractInfoForPdf
     * <AUTHOR>
     * @date 2021/9/14
     **/
    ContractInfoForPdf selectSignInfoByStaffId(@Param("staffId") String staffId);

    /**
     * 根据员工编号获取员工信息
     *
     * @param systemNum 员工编号
     * @return cn.casair.domain.HrTalentStaff
     * <AUTHOR>
     * @date 2021/9/12
     **/
    HrTalentStaff selectBySystemNum(@Param("systemNum") String systemNum);

    /**
     * 获取员工姓名列表
     *
     * @return java.util.List<cn.casair.domain.HrTalentStaff>
     * <AUTHOR>
     * @date 2021/9/12
     **/
    List<HrTalentStaff> getStaffNameList();

    /**
     * 列表查询
     *
     * @param page
     * @param hrTalentStaffDTO
     * @return
     */
    IPage<HrTalentStaffDTO> findPage(Page<HrTalentStaff> page, @Param("param") HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 员工入离职列表
     * @param page
     * @param hrTalentStaffDTO
     * @return
     */
    IPage<HrTalentStaffDTO> entryResignationPage(Page<HrTalentStaff> page, @Param("param") HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 导出员工信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    List<HrStaffExport> findHrStaff(@Param("param") HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 导出人才信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    List<HrTalentExport> findHrTalent(@Param("param") HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 入职资料-基本信息
     *
     * @param id
     * @return
     */
    HrTalentStaffDTO getStaffBasicInfo(String id);

    /**
     * 根据身份证号码查询员工信息
     * @param certificateNum
     * @return
     */
    HrTalentStaff findStaffInfo(String certificateNum);

    /**
     * 导出员工入离职信息
     * @param hrTalentStaffDTO 员工
     * @return
     */
    List<HrStaffEntryQuitExport> findStaffEntryQuit(@Param("param") HrTalentStaffDTO hrTalentStaffDTO);

    List<HrQuestionDTO> selectQuestion(String id);

    /**
     * 服务中心--根据名称以及身份证查询数据
     * @param hrTalentStaffDTO 名称/身份证
     * @return
     */
    List<HrTalentStaffDTO> searchDataHrStaff(@Param("param") HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 医疗备案页面数据
     * @param staffId 员工ID
     * @return
     */
    HrTalentStaffDTO obtainMedicalRecordInfo(@Param("staffId") String staffId);

    List<String> findArchivesBringDetail(@Param("detailIds") List<String> detailIds);

    void deleteArchivesBringDetail(@Param("detailIds") List<String> detailIds);

    /**
     * 修改员工renewalProcess状态
     * @param staffId 员工ID
     * @param clientId 客户ID
     * @param renewalProcess 续签流程状态
     */
    void updateStaffRenewalProcess(@Param("staffId") String staffId, @Param("clientId") String clientId, @Param("renewalProcess") Integer renewalProcess);

    void updateStatus(@Param("clientId")String clientId, @Param("id")String id);

    /**
     * 修改员工信息
     * @param staff
     */
    void updateTalentStaff(@Param("param") HrTalentStaff staff);

    /**
     * 修改员工--入职Id
     * @param staffId
     * @param applyStaffId
     */
    void updateApplyStaffId(@Param("staffId") String staffId, @Param("applyStaffId") String applyStaffId);

    /**
     * 修改员工工作状态
     * @param staffId
     * @param workStatus
     */
    void updateWorkStatus(@Param("staffId") String staffId, @Param("workStatus") Integer workStatus);

    /**
     * 批量修改员工信息
     * @param ids
     * @param lastModifiedBy
     * @param lastModifiedDate
     */
    void updateBatchIds(@Param("ids") List<String> ids, @Param("lastModifiedBy") String lastModifiedBy, @Param("lastModifiedDate") LocalDateTime lastModifiedDate);


    /**
     * 通过手机号查询员工
     * @param phone
     */
    HrTalentStaffDTO selectByPhone(@Param("phone")String phone);

    List<String> findStaffStation(@Param("professionName") String professionName);

    /**
     * 查询人才的适配岗位
     * @param ids
     * @return
     */
    List<HrTalentStaffDTO> findStationByStaffId(@Param("ids")List<String> ids);

    /**
     * 修改员工参保状态
     * @param staffId 员工Id
     * @param izInsured 参保状态
     */
    void updateIzInsured(@Param("staffId") String staffId, @Param("izInsured") Integer izInsured);


    void updateStaffWorkExperience(@Param("ids") List<String> staffWorkExperienceIds, @Param("now") LocalDate now);

    void updateContract(@Param("ids") List<String> contractIds);

    void updateApplyDepartureStaff(@Param("ids") List<String> applyDepartureStaffIds);

    ContractInfoForPdf findSignInfoByStaffId(@Param("id") String staffId);
    /**
     * 删除人才信息
     * @param ids
     */
    void deleteTalentStaff(@Param("ids") List<String> ids);

    /**
     * 不分页查询员工信息
     * @param hrTalentStaffDTO
     * @return
     */
    List<HrTalentStaffDTO> findNotPageHrTalentStaff(@Param("param") HrTalentStaffDTO hrTalentStaffDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 查询离职员工的离职日期
     * @param hrTalentStaffDTO
     * @return
     */
    List<HrTalentStaff> findResignationStaff(@Param("param") HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 续签服务列表查询
     * @param page
     * @param hrTalentStaffDTO
     * @param clientIds
     * @return
     */
    IPage<HrTalentStaffDTO> findPageRenewal(Page<HrBill> page, @Param("param") HrTalentStaffDTO hrTalentStaffDTO, @Param("clientIds") List<String> clientIds);

    /**
     * 不分页查询续签信息
     * @param hrTalentStaffDTO
     * @param clientIds
     * @return
     */
    List<HrTalentStaffDTO> findListRenewal(@Param("param") HrTalentStaffDTO hrTalentStaffDTO, @Param("clientIds")List<String> clientIds);

    /**
     * 赋值员工续签服务ID
     * @param renewalServiceId
     * @param staffId
     */
    void updateRenewalServiceId(@Param("renewalServiceId") String renewalServiceId, @Param("staffId") String staffId);

    /**
     * 修改员工续签
     * @param isRenewalContract
     * @param staffIds
     */
    void updateRenewalContract(@Param("isRenewalContract") Integer isRenewalContract, @Param("staffIds") List<String> staffIds);

    /**
     * 员工基本信息
     *
     * @param staffIds
     * @return
     */
    List<HrTalentStaffDTO> findStaffBatchIds(@Param("staffIds") List<String> staffIds);

    /**
     * 不分页查询员工福利列表
     * @param hrEmployeeWelfareDTO
     * @param clientIds
     * @return
     */
    List<HrEmployeeWelfareDTO> findEmployeeWelfareList(@Param("params") HrEmployeeWelfareDTO hrEmployeeWelfareDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 修改员工微信openid
     * @param staffIds 员工ID
     * @param openId
     */
    void updateOpenIdByStaffId(@Param("staffIds") List<String> staffIds, @Param("openId") String openId);

    /**
     * 离职修改员工信息
     * @param hrTalentStaff
     */
    void updateHrTalentStaff(@Param("param") HrTalentStaff hrTalentStaff);

    /**
     * 更改员工标识-人才
     *
     * @param ids            员工ID
     * @param lastModifiedBy 修改人
     */
    void updateIzDefault(@Param("ids") List<String> ids, @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 修改账单员工缴费年月计算倍数状态
     *
     * @param staffIdList
     * @param supplementaryPayment
     * @return
     */
    int updateSupplementaryPayment(@Param("staffIdList") List<String> staffIdList, @Param("supplementaryPayment") int supplementaryPayment);

    /**
     * 客服部长手动修改员工入职流程
     * <p>其他方法禁止使用此接口</p>
     *
     * @param hrTalentStaff
     * @return
     */
    @Deprecated
    int manualUpdateStaffInductionProcess(@Param("params") HrTalentStaff hrTalentStaff);

    /**
     * 修改客户员工的登录状态
     * @param clientIds
     */
    void updateStaffLoginStatus(@Param("clientIds") List<String> clientIds);

    /**
     * 不分页查询员工信息列表
     * @param hrTalentStaffDTO
     * @return
     */
    List<HrTalentStaffDTO> findPage(@Param("param") HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 根据客户id修改员工的登录状态
     * @param clientIds
     * @param status
     */
    void updateStatusByClient(@Param("clientIds") List<String> clientIds, @Param("status") Integer status);

    /**
     * 添加认证跳过名单
     * @param distinguishDTO
     */
    void insertDistinguish(@Param("param") HrDistinguishDTO distinguishDTO);

    /**
     * 查看名单
     * @param idCard 员工身份证
     * @return
     */
    List<HrDistinguishDTO> selectDistinguish(@Param("idCard") String idCard, @Param("type") Integer type);

    /**
     * 重置 即将过期的续签成功 -> 可续签
     * @param staffIds
     */
    void resetSuccessRenewalProcess(@Param("staffIds") List<String> staffIds);

    /**
     * 获取员工基本信息  id, 系统编号， 姓名， 手机号
     *
     * @param certificateNum
     * @return
     */
    HrTalentStaffDTO getStaffInfoByCertificateNum(@Param("certificateNum") String certificateNum);
}
