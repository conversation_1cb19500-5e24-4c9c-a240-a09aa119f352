package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.domain.HrStaffCertificate;
import cn.casair.dto.HrStaffCertificateDTO;
import cn.casair.mapper.HrStaffCertificateMapper;
import cn.casair.repository.HrStaffCertificateRepository;
import cn.casair.service.HrStaffCertificateService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 证书服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrStaffCertificateServiceImpl extends ServiceImpl<HrStaffCertificateRepository, HrStaffCertificate> implements HrStaffCertificateService {

    private static final Logger log = LoggerFactory.getLogger(HrStaffCertificateServiceImpl.class);

    private final HrStaffCertificateRepository hrStaffCertificateRepository;

    private final HrStaffCertificateMapper hrStaffCertificateMapper;

    private final SysOperLogService sysOperLogService;

    public HrStaffCertificateServiceImpl(HrStaffCertificateRepository hrStaffCertificateRepository, HrStaffCertificateMapper hrStaffCertificateMapper, SysOperLogService sysOperLogService) {
        this.hrStaffCertificateRepository = hrStaffCertificateRepository;
        this.hrStaffCertificateMapper = hrStaffCertificateMapper;
        this.sysOperLogService = sysOperLogService;
    }

    /**
     * 创建证书
     *
     * @param hrStaffCertificateDTO
     * @return
     */
    @Override
    public List<HrStaffCertificateDTO> createHrStaffCertificate(HrStaffCertificateDTO hrStaffCertificateDTO) {
        log.info("Create new HrStaffCertificate:{}", hrStaffCertificateDTO);

        HrStaffCertificate hrStaffCertificate = this.hrStaffCertificateMapper.toEntity(hrStaffCertificateDTO);
        this.hrStaffCertificateRepository.insert(hrStaffCertificate);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_CERT.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffCertificateDTO),
            HrStaffCertificateDTO.class,
            null,
            JSON.toJSONString(hrStaffCertificate)
        );
        return this.findCertificateList(hrStaffCertificateDTO.getStaffId());
    }

    /**
     * 修改证书
     *
     * @param hrStaffCertificateDTO
     * @return
     */
    @Override
    public Optional<List<HrStaffCertificateDTO>> updateHrStaffCertificate(HrStaffCertificateDTO hrStaffCertificateDTO) {
        return Optional.ofNullable(this.hrStaffCertificateRepository.selectById(hrStaffCertificateDTO.getId()))
            .map(roleTemp -> {
                HrStaffCertificate hrStaffCertificate = this.hrStaffCertificateMapper.toEntity(hrStaffCertificateDTO);
                this.hrStaffCertificateRepository.updateById(hrStaffCertificate);
                log.info("Update HrStaffCertificate:{}", hrStaffCertificateDTO);
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.STAFF_CERT.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrStaffCertificateDTO),
                    HrStaffCertificateDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrStaffCertificateDTO),
                    null,
                    HrStaffCertificateDTO.class
                );
                return this.findCertificateList(hrStaffCertificateDTO.getStaffId());
            });
    }

    /**
     * 查询证书详情
     *
     * @param id
     * @return
     */
    @Override
    public HrStaffCertificateDTO getHrStaffCertificate(String id) {
        log.info("Get HrStaffCertificate :{}", id);

        HrStaffCertificate hrStaffCertificate = this.hrStaffCertificateRepository.selectById(id);
        return this.hrStaffCertificateMapper.toDto(hrStaffCertificate);
    }

    /**
     * 删除证书
     *
     * @param id
     */
    @Override
    public List<HrStaffCertificateDTO> deleteHrStaffCertificate(String id) {
        HrStaffCertificate hrStaffCertificate = this.hrStaffCertificateRepository.selectById(id);
        this.hrStaffCertificateRepository.deleteById(id);
        log.info("Delete HrStaffCertificate:{}", hrStaffCertificate);
        List<String> ids = new ArrayList<>();
        ids.add(id);
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.STAFF_CERT.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
        return this.findCertificateList(hrStaffCertificate.getStaffId());
    }

    /**
     * 查询证书
     * @param staffId 员工ID
     * @return
     */
    @Override
    public List<HrStaffCertificateDTO> findCertificateList(String staffId) {
        List<HrStaffCertificateDTO> hrStaffCertificateDTOS = this.hrStaffCertificateMapper.toDto(
            hrStaffCertificateRepository.selectList(new QueryWrapper<HrStaffCertificate>().eq("staff_id", staffId).eq("is_delete", 0)));
        return hrStaffCertificateDTOS;
    }
}
