# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
    level:
        ROOT: DEBUG
        io.github.jhipster: DEBUG
        cn.casair: DEBUG

spring:
    profiles:
        active: dev
        include:
            - swagger
            # Uncomment to activate TLS for the dev profile
            #- tls
    flyway:
        #对于开发环境, 可能是多人协作开发, 很可能先 apply 了自己本地的最新 SQL 代码, 然后发现其他同事早先时候提交的 SQL 代码还没有 apply,
        #所以开发环境应该设置 spring.flyway.outOfOrder=true, 这样 flyway 将能加载漏掉的老版本 SQL 文件;
        out-of-order: true
    devtools:
        restart:
            enabled: true
        livereload:
            enabled: false # we use Webpack dev server + BrowserSync for livereload
    jackson:
        serialization:
            indent-output: true
    servlet:
        multipart:
            max-file-size: 1024MB
            max-request-size: 1024MB
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        platform: mysql
        druid:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: *************************************************************************************************************************************************************
            username: rootai
            password: casair@2022#2023
            initial-size: 5
            min-idle: 5
            max-active: 20
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT1FROMDUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            filter:
                stat:
                    log-slow-sql: true
                    # Druid WebStatFilter配置，说明请参考Druid Wiki，配置_配置WebStatFilter
            web-stat-filter:
                enabled: true
                url-pattern: /*
                exclusions: "*.gif,*.png,*.jpg,*.html,*.js,*.css,*.ico,/druid/*"
            # Druid StatViewServlet配置，说明请参考Druid Wiki，配置_StatViewServlet配置
            stat-view-servlet:
                enabled: true
                url-pattern: druid/*
                reset-enable: true
                login-username: admin
                login-password: admin
                allow:
                deny:
    #  mail:
    #    host: localhost
    #    port: 25
    #    username:
    #    password:
    messages:
        cache-duration: PT1S # 1 second, see the ISO 8601 standard
    redis:
        host: *************  # IP
        port: 16379  # 端口号
        password: asdf.1234 # 密码
        lettuce:
            pool:
                max-active: 8 # 连接池最大连接数
                max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
                min-idle: 0 # 连接池中的最小空闲连接
                max-idle: 8 # 连接池中的最大空闲连接
        database: 6
server:
    port: 10023
    tomcat:
        remote-ip-header: X-Real-IP
        protocol-header: X-Forwarded-Proto

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
    # CORS is only enabled by default with the "dev" profile, so BrowserSync can access the API
    cors:
        allowed-origins: '*'
        allowed-methods: '*'
        allowed-headers: '*'
        exposed-headers: 'Authorization,Link,X-Total-Count'
        allow-credentials: true
        max-age: 1800
    security:
        authentication:
            jwt:
                # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
                base64-secret: ZDMzN2MxNzk3MDZkN2MyNmU0YTczNmJkMDkxMTM0OTM1NWE0MmQ2Y2M0NTg1Yzk1OWVjYjZmNDkxNWQ5MjhmMjFlOThhOTU2MGZlMzYzOGE5MTlhMTI1M2FmZmI3Y2M3ODM4Zjc1YWU2NDBmMjVjODYzYjg1M2QxYjM2YWE4YzE=
                # Token is valid 24 hours
                token-validity-in-seconds: 864000
                token-validity-in-seconds-for-remember-me: 2592000
    mail: # specific JHipster mail property, for standard properties see MailProperties
        from: casairwebbase@localhost
        base-url: http://127.0.0.1:8080
    metrics:
        logs: # Reports metrics in the logs
            enabled: false
            report-frequency: 60 # in seconds
    logging:
        use-json-format: false # By default, logs are not in Json format
        logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
            enabled: false
            host: localhost
            port: 5000
            queue-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================
#minio:
#    access-key: admin
#    secretKey: asdf.1234
#    url: http://************:39000
#    region: casair-region1 # 默认区域1
#    bucketName: common
# application:

mybatis-plus:
    mapper-locations: classpath*:mapper/*.xml
    # 配置扫描枚举类的路径 支持统配符 * 或者 ; 分割
    typeEnumsPackage: cn.casair.common.enums
    configuration:
        log-impl:

# @Swagger配置
swagger:
    production: false #是否生产环境
    basic:
        enable: true #是否开启登录权限
        username: admin
        password: asdf.1234

file:
    file-root: /opt/ai/tmp/
    temp-path: /Users/<USER>/temp
    image-path: /root/tmp/ # image图片存储根路径

# 常量开关
constant:
    loginCheck: false
    fontPath: /opt/ai/tmp/fonts/simsun.ttc

# minio配置
minio:
    serverUrl: https://hr-minio.cas-air.cn
    endpoint: http://************:9000
    accessKey: admin
    secretKey: Casair@2023
    defaultBucket: hrresources
    pdfTempBucket: pdf-temp  #pdf临时桶
    zipBucket: zip-temp  #zip临时桶
    excelImportErrorBucket: excel-import-error #excel导入错误信息桶
    archivesManageBucket: archives-manage # 档案上传
    contractBucket: contract #电签签合同
    wordBucket: word-template #word模板
    excelPrefix: https://hr-minio.cas-air.cn/excel-template/


#SMS短信配置
sms:
    serverIp: app.cloopen.com_
    serverPort: 8883
    accountSId: 8a216da8621834ef01622337a91707da_
    accountToken: 1e523561ac2b416cb4bf5321d549e6f0
    appId: 8aaf070862181ad5016223a3874b0825

ecloud:
    appKey: yyz4x9swf5azly66l9
    secret: ddcd51cab72dd3acd664b0fb7a02e8ab
    aesKey: p3pb3764q920mn51
    v: 1.0
    url: https://testapi.ecloudsign.cn
    h5url: https://fat-yss-h5.myrrx.com/signFilePage
    realNameAuth: false
    smsCode: false
    bankVerify: false

#腾讯云
tencent:
    appId: **********
    secretId: AKIDGL9hW5OQChxwe4D5g03phMkrhMoztWsx
    secretKey: pOffV4MN7amhLiodBTUDfqlPhjW1FCtJ
    merchantId: 0NSJ2201131646081527

# 短信跳转小程序地址配置
mini:
    appId: wxba7e8509bb4ca5d4
    secret: 62d3ec3615c368e9a314371d68337a95
    getAccessToken: 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=SECRET'
    generateLinkURL: 'https://api.weixin.qq.com/wxa/generatescheme?access_token=ACCESS_TOKEN'
    site: 'https://cba-web.cas-air.cn/hr-admin/openScheme/index.html'
    appletSite: 'https://cba-web.cas-air.cn/hr-admin-v3/openScheme/signUp.html'
    appletName: '【黄岛HR云管家】'

# 微信
weixin:
    appId: wxba7e8509bb4ca5d4
    secret: 62d3ec3615c368e9a314371d68337a95
    accessUrl: https://api.weixin.qq.com/sns/jscode2session
    accessTokenUrl: https://api.weixin.qq.com/cgi-bin/token
    pushMsgUrl: https://api.weixin.qq.com/cgi-bin/message/subscribe/send
    templateId: FrP_WMo2JnXIO3d-YIRE68WaARBNiVoG0Oyq0JeFYfw
    page: pages/public/myNews/myNews
    miniProgramState: trial
    lang: zh_CN

wx:
    # 审批小程序
    miniapp:
        configs:
            - appid: wx60f909f587ed40d6 #微信小程序的appid
              secret: aabd64d4c2e0a426301c11b41d8886aa #微信小程序的Secret
              token: #微信小程序消息服务器配置的token
              aesKey: #微信小程序消息服务器配置的EncodingAESKey
              msgDataFormat: JSON
    # 公众号
    mp:
        useRedis: false
        redisConfig:
            host: 127.0.0.1
            port: 6379
        configs:
            - appId: wxedb097b080231054 # 第一个公众号的appid
              secret: 1df4bf072bcc6c60a94dc5947dfc5065 # 公众号的appsecret
              token: 111 # 接口配置里的Token值
              aesKey: 111 # 接口配置里的EncodingAESKey值
            - appId: 2222 # 第二个公众号的appid，以下同上
              secret: 1111
              token: 111
              aesKey: 111
    # 支付
    pay:
        appId: wx51d54f0c5bb8005c #微信公众号或者小程序等的appidwx51d54f0c5bb8005c
        mchId: 1544006891 #微信支付商户号
        mchKey: d0a9ac09501dcc672487e80b2c635299 #微信支付商户密钥
        subAppId: #服务商模式下的子商户公众账号ID
        subMchId: #服务商模式下的子商户号
        keyPath: # p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
        notifyUrl: https://hr-server.cas-air.cn/dev-2.0/api/notify/order #回调的接口
    robot:
        url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3c28cd0a-eb32-47c5-85e4-00a10b0cc144

#nc接口域名
nc:
    domain: http://*************:9098
    accountbook: 0901-0001
    # 教体局 0c2fa9ec8afffb95731873a1dfab6fe4 ，政法委 dd8210f1e4f02bda4a442ad120d3b6ee ，1幼儿园 3a97ab40a56f5f171a2046101756f705
    markedClientIds: 0c2fa9ec8afffb95731873a1dfab6fe4,dd8210f1e4f02bda4a442ad120d3b6ee-,3a97ab40a56f5f171a2046101756f705-
ncc:
    ip: ************
    port: 38090
    busiCenter: NCCloud
    appId: rlzy
    appSecret: 0953eccabd93406687cf
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCsnZkl/3/TK/udfT2+Z/thG1ofOhY1fi5BNSnmitZk9U/DZGOUtkDU7A27alNPfu6dYMqH7YTDtiQTjYYiDjw3Bz+S6vv1M8N9CkNYvRVmPFYQ9c+IB6h+vI8vV+3ORK7nFuEeI4KeUPGNzZbL2y9JrBv6HdHB/x6VfP31cBDD7QIDAQAB
    accountbook: ht0901-0001
    # 教体局 0c2fa9ec8afffb95731873a1dfab6fe4 ，政法委 dd8210f1e4f02bda4a442ad120d3b6ee ，1幼儿园 3a97ab40a56f5f171a2046101756f705
    markedClientIds: 0c2fa9ec8afffb95731873a1dfab6fe4,dd8210f1e4f02bda4a442ad120d3b6ee-,3a97ab40a56f5f171a2046101756f705-
