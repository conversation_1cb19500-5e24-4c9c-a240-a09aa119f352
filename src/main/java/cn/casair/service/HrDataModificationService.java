package cn.casair.service;

import cn.casair.domain.HrDataModification;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrDataModificationDTO;
import cn.casair.dto.HrExamResultDTO;
import cn.casair.dto.HrStaffCertificateDTO;
import cn.casair.dto.HrStaffContactsDTO;
import cn.casair.dto.HrStaffEducationDTO;
import cn.casair.dto.HrStaffEmolumentDTO;
import cn.casair.dto.HrStaffFamilyDTO;
import cn.casair.dto.HrStaffLanguageDTO;
import cn.casair.dto.HrStaffProfessionDTO;
import cn.casair.dto.HrStaffQualificationDTO;
import cn.casair.dto.HrStaffTechniqueDTO;
import cn.casair.dto.HrStaffWorkExperienceDTO;
import cn.casair.dto.HrTalentStaffDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

/**
 * 资料修改服务类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
public interface HrDataModificationService extends IService<HrDataModification> {

    /**
     * 资料修改--工资卡
     *
     * @param hrStaffEmolumentDTO
     */
    void salaryBankModification(HrStaffEmolumentDTO hrStaffEmolumentDTO);

    /**
     * 创建资料修改
     *
     * @param hrDataModificationDTO
     * @return
     */
    HrDataModificationDTO createHrDataModification(HrDataModificationDTO hrDataModificationDTO);

    /**
     * 修改资料修改
     *
     * @param hrDataModificationDTO
     * @return
     */
    Optional<HrDataModificationDTO> updateHrDataModification(HrDataModificationDTO hrDataModificationDTO);

    /**
     * 查询资料修改详情
     *
     * @param id
     * @return
     */
    HrDataModificationDTO getHrDataModification(String id);

    /**
     * 删除资料修改
     *
     * @param id
     */
    void deleteHrDataModification(String id);

    /**
     * 批量删除资料修改
     *
     * @param ids
     */
    void deleteHrDataModification(List<String> ids);

    /**
     * 分页查询资料修改
     *
     * @param hrDataModificationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrDataModificationDTO hrDataModificationDTO, Long pageNumber, Long pageSize);

    /**
     * 导出
     *
     * @param hrDataModificationDTO
     * @return
     */
    String export(HrDataModificationDTO hrDataModificationDTO);

    /**
     * 资料修改--基本信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    ResponseEntity basicInformationModification(HrTalentStaffDTO hrTalentStaffDTO);

    /**
     * 资料修改--附加信息--工作经历
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity workExperienceModification(List<HrStaffWorkExperienceDTO> dtoList);

    /**
     * 资料修改--附加信息--教育经历
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity educationalExperienceModification(List<HrStaffEducationDTO> dtoList);

    /**
     * 资料修改--附加信息--应试经历
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity interviewExperiencdtoseModification(List<HrExamResultDTO> dtoList);

    /**
     * 资料修改--附加信息--家庭成员
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity familyModification(List<HrStaffFamilyDTO> dtoList);

    /**
     * 资料修改--附加信息--紧急联系人
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity contactsModification(List<HrStaffContactsDTO> dtoList);

    /**
     * 资料修改--附加信息--语言能力
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity languageModification(List<HrStaffLanguageDTO> dtoList);

    /**
     * 资料修改--附加信息--专业能力
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity professionModification(List<HrStaffProfessionDTO> dtoList);

    /**
     * 资料修改--附加信息--职业(工种)资格
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity qualificationModification(List<HrStaffQualificationDTO> dtoList);

    /**
     * 资料修改--附加信息--职业技术能力
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity techniqueModification(List<HrStaffTechniqueDTO> dtoList);

    /**
     * 资料修改--附加信息--证书
     *
     * @param dtoList 信息
     * @return
     */
    ResponseEntity certificateModification(List<HrStaffCertificateDTO> dtoList);

    /**
     * 批量审核拒绝
     *
     * @param batchOptDTO
     * @return
     */
    ResponseEntity batchRejectModification(BatchOptDTO batchOptDTO);

    /**
     * 批量审核通过
     *
     * @param batchOptDTO
     * @return
     */
    ResponseEntity passModification(BatchOptDTO batchOptDTO);

    /**
     * 资料修改申请记录
     *
     * @return
     */
    List<HrDataModificationDTO> dataApplicationRecord();

    /**
     * 资料申请记录--詳情
     *
     * @param id 记录详情
     * @return
     */
    ResponseEntity dataApplicationRecordDetails(String id);

    /**
     * 资料修改申请记录未读数量
     *
     * @return 未读数量
     */
    int dataApplicationUnreadNumber();


}
