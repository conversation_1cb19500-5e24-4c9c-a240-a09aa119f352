package cn.casair.service;

import cn.casair.domain.HrSpecialDeduction;
import cn.casair.dto.HrSpecialDeductionDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-09-28
 */
public interface HrSpecialDeductionService extends IService<HrSpecialDeduction> {


    /**
     * 创建
     * @param hrSpecialDeductionDTO
     * @return
     */
    HrSpecialDeductionDTO createHrSpecialDeduction(HrSpecialDeductionDTO hrSpecialDeductionDTO);

    /**
     * 修改
     * @param hrSpecialDeductionDTO
     * @return
     */
    Optional<HrSpecialDeductionDTO> updateHrSpecialDeduction(HrSpecialDeductionDTO hrSpecialDeductionDTO);

    /**
     * 查询详情
     * @param id
     * @return
     */
    HrSpecialDeductionDTO getHrSpecialDeduction(String id);

    /**
     * 删除
     * @param id
     */
    void deleteHrSpecialDeduction(String id);

    /**
     * 批量删除
     * @param ids
     */
    void deleteHrSpecialDeduction(List<String> ids);

    /**
     * 分页查询
     * @param hrSpecialDeductionDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrSpecialDeductionDTO hrSpecialDeductionDTO,Long pageNumber,Long pageSize);

    String exportHrStaff(HrSpecialDeductionDTO hrSpecialDeductionDTO, HttpServletResponse response);

    String importHrStaff(MultipartFile file );

    void importHrClientsTemplate(HttpServletResponse response);
}
