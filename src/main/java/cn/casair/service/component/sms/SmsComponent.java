package cn.casair.service.component.sms;

import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.DateUtils;
import cn.casair.domain.HrSmsTemplate;
import cn.casair.repository.HrSmsTemplateRepository;
import cn.casair.service.HrSmsTemplateService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 异步短信
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/17 9:34
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmsComponent {

    private final HrSmsTemplateRepository hrSmsTemplateRepository;
    private final HrSmsTemplateService hrSmsTemplateService;


    /**
     * 异步短信 单号码
     *
     * @param phone
     * @param templateId
     * @param operator 操作人
     */
    // @Async
    public void sendSmsSingle(String phone, String templateId, HashMap<Integer, String> params, String operator) {
        HrSmsTemplate hrSmsTemplate = this.hrSmsTemplateRepository.selectByObject(new HrSmsTemplate().setTemplateId(templateId).setStatus(0));
        if (hrSmsTemplate == null) {
            throw new CommonException("未查询到该短信模板!");
        }
        String parameterContent = hrSmsTemplate.getParameterContent();
        Integer dateIndex = hrSmsTemplate.getDateIndex();
        List arrayList = JSON.parseObject(parameterContent, ArrayList.class);
        if (CollectionUtils.isEmpty(arrayList)) {
            throw new CommonException("请先到页面配置短信模板！");
        }
        if (dateIndex != null) {
            int dateNumber = hrSmsTemplate.getDateNumber();
            //获取当前时间+dateNumber
            String afterDate = DateUtils.selectBeforeDate(LocalDateTime.now().toString(), -dateNumber);
            arrayList.set(dateIndex - 1, afterDate);
        }
        //如果短信配置了参数，以短信配置的参数为主
        for (int i = 0; i < arrayList.size(); i++) {
            String value = String.valueOf(arrayList.get(i));
            if (StringUtils.isBlank(value) || "null".equals(value)) {
                arrayList.set(i, params.get(i + 1));
            }
        }
        String[] array = (String[]) arrayList.toArray(new String[0]);
        //将内容进行拼接
        String sendContent = hrSmsTemplate.getSendContent();
        for (int i = 1; i <= array.length; i++) {
            sendContent = sendContent.replace("{" + i + "}", array[i - 1]);
        }
        this.hrSmsTemplateService.sendMessage(operator, phone, templateId, array);
    }
}
