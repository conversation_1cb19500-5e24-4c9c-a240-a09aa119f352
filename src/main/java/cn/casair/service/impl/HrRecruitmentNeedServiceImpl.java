package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.DateUtils;
import cn.casair.common.utils.StringUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.mapper.HrRecruitmentStationMapper;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import cn.casair.repository.HrRecruitmentNeedRepository;
import cn.casair.mapper.HrRecruitmentNeedMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 招聘需求表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrRecruitmentNeedServiceImpl extends ServiceImpl<HrRecruitmentNeedRepository, HrRecruitmentNeed> implements HrRecruitmentNeedService {


    private final HrRecruitmentNeedRepository hrRecruitmentNeedRepository;
    private final HrRecruitmentNeedMapper hrRecruitmentNeedMapper;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final SysOperLogService sysOperLogService;
    private final CodeTableService codeTableService;
    @Autowired
    private HrRecruitmentStationService hrRecruitmentStationService;

    @Autowired
    private HrRecruitmentStationMapper hrRecruitmentStationMapper;
    @Autowired
    private  HrClientService hrClientService;

    public HrRecruitmentNeedServiceImpl(HrRecruitmentNeedRepository hrRecruitmentNeedRepository, HrRecruitmentNeedMapper hrRecruitmentNeedMapper, HrApplyOpLogsService hrApplyOpLogsService, SysOperLogService sysOperLogService, CodeTableService codeTableService) {
        this.hrRecruitmentNeedRepository = hrRecruitmentNeedRepository;
        this.hrRecruitmentNeedMapper = hrRecruitmentNeedMapper;
        this.hrApplyOpLogsService = hrApplyOpLogsService;
        this.sysOperLogService = sysOperLogService;
        this.codeTableService = codeTableService;
    }

    /**
     * 创建招聘需求表
     *
     * @param hrRecruitmentNeedDTO
     * @return
     */
    @Override
    public HrRecruitmentNeedDTO createHrRecruitmentNeed(HrRecruitmentNeedDTO hrRecruitmentNeedDTO) {
        log.info("Create new HrRecruitmentNeed:{}", hrRecruitmentNeedDTO);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrRecruitmentNeed hrRecruitmentNeed = this.hrRecruitmentNeedMapper.toEntity(hrRecruitmentNeedDTO);
        //招聘岗位的集合
        List<HrRecruitmentStationDTO> hrRecruitmentStationList = hrRecruitmentNeedDTO.getHrRecruitmentStationDTOList();
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddkkmmss");
        String format = sdf.format(d);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        hrRecruitmentNeed.setRecruitmentNumber(format);
        hrRecruitmentNeed.setStatus(0);
        this.hrRecruitmentNeedRepository.insert(hrRecruitmentNeed);
        String enterpriseMessage =jwtUserDTO.getRealName()+"新建了招聘需求";
        this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRecruitmentNeed.getId(), null, jwtUserDTO.getId(), enterpriseMessage, null, ServiceCenterEnum.RECRUITMENT_NOTICES.getKey());
        for (HrRecruitmentStationDTO hrRecruitmentStation : hrRecruitmentStationList) {
            hrRecruitmentStation.setClientId(hrRecruitmentNeedDTO.getClientId());
            hrRecruitmentStation.setServiceId(hrRecruitmentNeed.getId());
            hrRecruitmentStationService.save(hrRecruitmentStationMapper.toEntity(hrRecruitmentStation));
        }
        // 操作日志
        HrClient hrClient = hrClientService.getById(hrRecruitmentNeedDTO.getClientId());
        hrRecruitmentNeedDTO.setClientName(hrClient.getClientName());
        hrRecruitmentNeedDTO.setRecruitmentNumber(format);
        List<String> collect = hrRecruitmentStationList.stream().map(HrRecruitmentStationDTO::getRecruitmentStationName).collect(Collectors.toList());
        hrRecruitmentNeedDTO.setRecruitmentStation(String.join(",", collect));
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.RECRUITMENT_NEED.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrRecruitmentNeedDTO),
            HrRecruitmentNeedDTO.class,
            null,
            JSON.toJSONString(hrRecruitmentNeed)
        );
        return this.hrRecruitmentNeedMapper.toDto(hrRecruitmentNeed);
    }

    /**
     * 修改招聘需求表
     *
     * @param hrRecruitmentNeedDTO
     * @return
     */
    @Override
    public Optional<HrRecruitmentNeedDTO> updateHrRecruitmentNeed(HrRecruitmentNeedDTO hrRecruitmentNeedDTO) {
        return Optional.ofNullable(this.hrRecruitmentNeedRepository.selectById(hrRecruitmentNeedDTO.getId()))
            .map(roleTemp -> {
                JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
                //如果修改了招聘岗位先删除后添加
//                if (CollectionUtils.isNotEmpty(hrRecruitmentNeedDTO.getHrRecruitmentStationList())){
//                    QueryWrapper<HrRecruitmentStation> queryWrapper = new QueryWrapper<>();
//                    queryWrapper.eq("service_id",hrRecruitmentNeedDTO.getId());
//                    hrRecruitmentStationService.remove(queryWrapper);
//                    //重新进行添加
//                    for (HrRecruitmentStation hrRecruitmentStation : hrRecruitmentNeedDTO.getHrRecruitmentStationList()) {
//                        hrRecruitmentStation.setClientId(hrRecruitmentNeedDTO.getClientId());
//                        hrRecruitmentStation.setServiceId(hrRecruitmentNeedDTO.getId());
//                        hrRecruitmentStationService.save(hrRecruitmentStation);
//                    }
//                }
                if (hrRecruitmentNeedDTO.getStatus()==1){
                    //添加日志 附件
                    String appendixId = "";
                    if (CollectionUtils.isNotEmpty(hrRecruitmentNeedDTO.getAppendixIds())) {
                        appendixId = String.join(",", hrRecruitmentNeedDTO.getAppendixIds());
                    }
                    String enterpriseMessage =jwtUserDTO.getRealName()+"确认了招聘需求";

                    if (StringUtils.isNotBlank(hrRecruitmentNeedDTO.getRemark())) {
                        enterpriseMessage = enterpriseMessage + "  备注："+hrRecruitmentNeedDTO.getRemark();
                    }
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRecruitmentNeedDTO.getId(), null, jwtUserDTO.getId(), enterpriseMessage, appendixId, ServiceCenterEnum.RECRUITMENT_NOTICES.getKey());
                }
                if (hrRecruitmentNeedDTO.getStatus()==2){
                    //添加日志 附件
                    String appendixId = "";
                    if (CollectionUtils.isNotEmpty(hrRecruitmentNeedDTO.getAppendixIds())) {
                        appendixId = String.join(",", hrRecruitmentNeedDTO.getAppendixIds());
                    }
                    String enterpriseMessage =jwtUserDTO.getRealName()+"上传了附件";
                    if (StringUtils.isNotBlank(hrRecruitmentNeedDTO.getRemark())) {
                        enterpriseMessage = enterpriseMessage + " 备注："+hrRecruitmentNeedDTO.getRemark();
                    }
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRecruitmentNeedDTO.getId(), null, jwtUserDTO.getId(), enterpriseMessage, appendixId, ServiceCenterEnum.RECRUITMENT_NOTICES.getKey());
                }
                HrRecruitmentNeed hrRecruitmentNeed = this.hrRecruitmentNeedMapper.toEntity(hrRecruitmentNeedDTO);
                this.hrRecruitmentNeedRepository.updateById(hrRecruitmentNeed);
                log.info("Update HrRecruitmentNeed:{}", hrRecruitmentNeedDTO);
//                // 操作日志
//                this.sysOperLogService.insertSysOperLog(
//                    ModuleTypeEnum.RECRUITMENT_NEED.getValue(),
//                    BusinessTypeEnum.UPDATE.getKey(),
//                    JSON.toJSONString(hrRecruitmentNeedDTO),
//                    HrRecruitmentNeedDTO.class,
//                    null,
//                    JSON.toJSONString(roleTemp),
//                    JSON.toJSONString(hrRecruitmentNeedDTO),
//                    null,
//                    HrRecruitmentNeedDTO.class
//                );
                return hrRecruitmentNeedDTO;
            });
    }

    /**
     * 查询招聘需求表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrRecruitmentNeedDTO getHrRecruitmentNeed(String id) {
        log.info("Get HrRecruitmentNeed :{}", id);

        HrRecruitmentNeed hrRecruitmentNeed = this.hrRecruitmentNeedRepository.selectById(id);
        return this.hrRecruitmentNeedMapper.toDto(hrRecruitmentNeed);
    }

    /**
     * 删除招聘需求表
     *
     * @param id
     */
    @Override
    public void deleteHrRecruitmentNeed(String id) {
        Optional.ofNullable(this.hrRecruitmentNeedRepository.selectById(id))
            .ifPresent(hrRecruitmentNeed -> {
                this.hrRecruitmentNeedRepository.deleteById(id);
                log.info("Delete HrRecruitmentNeed:{}", hrRecruitmentNeed);
            });
    }

    /**
     * 批量删除招聘需求表
     *
     * @param ids
     */
    @Override
    public void deleteHrRecruitmentNeed(List<String> ids) {
        log.info("Delete HrRecruitmentNeeds:{}", ids);
        List<HrRecruitmentNeed> hrRecruitmentNeeds = hrRecruitmentNeedRepository.selectBatchIds(ids);
        this.hrRecruitmentNeedRepository.deleteBatchIds(ids);
        // 操作日志
        //this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.RECRUITMENT_NEED.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> collect = hrRecruitmentNeeds.stream().map(HrRecruitmentNeed::getRecruitmentNumber).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.RECRUITMENT_NEED.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除招聘需求，需求编号: "+JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(hrRecruitmentNeeds),
            jwtUserDTO
        );
    }

    /**
     * 分页查询招聘需求表
     *
     * @param hrRecruitmentNeedDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrRecruitmentNeedDTO hrRecruitmentNeedDTO, Long pageNumber, Long pageSize) {
        //查询数据权限
        List<String> clientId = hrClientService.selectClientIdByUserId();
        if (CollectionUtils.isEmpty(clientId)) {
            return new Page<>();
        }
        Page<HrRecruitmentNeed> page = new Page<>(pageNumber, pageSize);
        //先根据岗位进行查找
        QueryWrapper<HrRecruitmentStation> hrRecruitmentStationQueryWrapper = new QueryWrapper<>();
        hrRecruitmentStationQueryWrapper.like(StringUtils.isNotBlank(hrRecruitmentNeedDTO.getRecruitmentTerm()),"recruitment_term",hrRecruitmentNeedDTO.getRecruitmentTerm());
        hrRecruitmentStationQueryWrapper.eq(hrRecruitmentNeedDTO.getRecruitmentPeopleNumber()!=null,"recruitment_people_number",hrRecruitmentNeedDTO.getRecruitmentPeopleNumber());
        hrRecruitmentStationQueryWrapper.in(CollectionUtils.isNotEmpty(hrRecruitmentNeedDTO.getRecruitmentStationIdList()),"recruitment_station_id",hrRecruitmentNeedDTO.getRecruitmentStationIdList());
        hrRecruitmentStationQueryWrapper.in(CollectionUtils.isNotEmpty(hrRecruitmentNeedDTO.getExamFormatList()),"exam_format",hrRecruitmentNeedDTO.getExamFormatList());
        if (CollectionUtils.isEmpty(hrRecruitmentNeedDTO.getClientIdList())) {
            hrRecruitmentStationQueryWrapper.in("client_id", clientId);
        }
        hrRecruitmentStationQueryWrapper.in(CollectionUtils.isNotEmpty(hrRecruitmentNeedDTO.getClientIdList()), "client_id", hrRecruitmentNeedDTO.getClientIdList());
        //hrRecruitmentStationQueryWrapper.in("client_id",hrRecruitmentNeedDTO.getClientId());
        List<HrRecruitmentStation> list = hrRecruitmentStationService.list(hrRecruitmentStationQueryWrapper);
        //通过岗位取所有招聘Id
        List<String> recruitmentNeedIdList = new ArrayList<String>();
        if (CollectionUtils.isNotEmpty(list)){
            for (HrRecruitmentStation hrRecruitmentStation : list) {
                recruitmentNeedIdList.add(hrRecruitmentStation.getServiceId());
            }
        }
        else {
            return new Page();
        }
        QueryWrapper<HrRecruitmentNeed> qw = new QueryWrapper<>();
        //数据权限
        //qw.in("client_id",hrRecruitmentNeedDTO.getClientId());
        qw.in(CollectionUtils.isNotEmpty(hrRecruitmentNeedDTO.getClientIdList()), "rn.client_id", hrRecruitmentNeedDTO.getClientIdList());
        if (CollectionUtils.isEmpty(hrRecruitmentNeedDTO.getClientIdList())) {
            qw.in("rn.client_id ", clientId);
        }
        qw.ge(StringUtils.isNotBlank(hrRecruitmentNeedDTO.getCreateStartDate()), "rn.created_date", hrRecruitmentNeedDTO.getCreateStartDate());
        if (StringUtils.isNotBlank(hrRecruitmentNeedDTO.getCreateEndDate())){
            String afterDate = DateUtils.selectBeforeDate(hrRecruitmentNeedDTO.getCreateEndDate(), -1);
            qw.le( "rn.created_date", afterDate);
        }

        qw.in(CollectionUtils.isNotEmpty(hrRecruitmentNeedDTO.getStatusList()), "rn.status", hrRecruitmentNeedDTO.getStatusList());
        qw.in(CollectionUtils.isNotEmpty(recruitmentNeedIdList), "rn.id", recruitmentNeedIdList);
        qw.like(StringUtils.isNotBlank(hrRecruitmentNeedDTO.getRecruitmentNumber()), "rn.recruitment_number", hrRecruitmentNeedDTO.getRecruitmentNumber());
        qw.eq("rn.is_delete ", 0);
        //排序
        if (StringUtils.isNotBlank(hrRecruitmentNeedDTO.getOrder())) {
            if (hrRecruitmentNeedDTO.getOrder().equals("DESC")) {
                qw.orderBy(StringUtils.isNotBlank(hrRecruitmentNeedDTO.getField()), false, hrRecruitmentNeedDTO.getField());
            } else {
                qw.orderBy(StringUtils.isNotBlank(hrRecruitmentNeedDTO.getField()), true, hrRecruitmentNeedDTO.getField());
            }

        }
        IPage<HrRecruitmentNeedDTO> iPage = this.hrRecruitmentNeedRepository.page(page, qw);
        for (HrRecruitmentNeedDTO record : iPage.getRecords()) {
//            QueryWrapper<HrRecruitmentStation> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("service_id",record.getId());
            List<HrRecruitmentStationDTO> hrRecruitmentStationList = hrRecruitmentStationService.listByServiceId(record.getId());
            record.setHrRecruitmentStationDTOList(hrRecruitmentStationList);
        }
        return iPage;
    }

    /**
     * 查看招聘需求详情
     * @param hrRecruitmentNeedDTO
     * @return
     */
    @Override
    public HrRecruitmentNeedDTO getDetail(HrRecruitmentNeedDTO hrRecruitmentNeedDTO) {
        QueryWrapper<HrRecruitmentStation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("service_id",hrRecruitmentNeedDTO.getId());
        List<HrRecruitmentStationDTO> list = hrRecruitmentStationService.listByServiceId(hrRecruitmentNeedDTO.getId());
        hrRecruitmentNeedDTO.setHrRecruitmentStationDTOList(list);
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(hrRecruitmentNeedDTO.getId(), null);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)) {
            hrRecruitmentNeedDTO.setApplyOpLogsList(applyOpLogsList);
        }
        return hrRecruitmentNeedDTO;
    }

    @Override
    public ResponseEntity<?> updateStates(List<HrRecruitmentNeedDTO> hrRecruitmentNeedDTOList) {
        if (CollectionUtils.isEmpty(hrRecruitmentNeedDTOList)) {
            throw new CommonException("请先选择员工");
        }
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> successList = new ArrayList<>();//成功集合
        List<String> errorStatusList = new ArrayList<>();//失败集合
        for (HrRecruitmentNeedDTO hrRecruitmentNeedDTO : hrRecruitmentNeedDTOList) {
            if (hrRecruitmentNeedDTO.getStatus() == 0) {
//                    HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(hrFertility.getStaffId());
                hrRecruitmentNeedDTO.setStatus(1);
                updateById(hrRecruitmentNeedMapper.toEntity(hrRecruitmentNeedDTO));
                //添加日志 附件
                String appendixId = "";
                if (CollectionUtils.isNotEmpty(hrRecruitmentNeedDTO.getAppendixIds())) {
                    appendixId = String.join(",", hrRecruitmentNeedDTO.getAppendixIds());
                }
                String enterpriseMessage =jwtUserDTO.getRealName()+"确认了招聘需求";

                if (StringUtils.isNotBlank(hrRecruitmentNeedDTO.getRemark())) {
                    enterpriseMessage = enterpriseMessage + "  备注："+hrRecruitmentNeedDTO.getRemark();
                }
                this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRecruitmentNeedDTO.getId(), null, jwtUserDTO.getId(), enterpriseMessage, appendixId, ServiceCenterEnum.RECRUITMENT_NOTICES.getKey());
                successList.add(hrRecruitmentNeedDTO.getClientName());
            } else {
                errorStatusList.add(hrRecruitmentNeedDTO.getClientName());
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态不支持该操作");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 获取招聘单位下面的招聘需求
     * @param clientId
     * @return
     */
    @Override
    public List<HrRecruitmentNeedDTO> unitRrecruitmentDemand(String clientId) {
        List<HrRecruitmentNeed> hrRecruitmentNeeds = hrRecruitmentNeedRepository.selectList(new QueryWrapper<HrRecruitmentNeed>().eq("client_id", clientId).eq("status", 1));
        List<HrRecruitmentNeedDTO> hrRecruitmentNeedDTOS = hrRecruitmentNeedMapper.toDto(hrRecruitmentNeeds);
        for (HrRecruitmentNeedDTO hrRecruitmentNeedDTO : hrRecruitmentNeedDTOS) {
            List<HrRecruitmentStation> recruitmentStationList = hrRecruitmentStationService.list(new QueryWrapper<HrRecruitmentStation>().eq("service_id", hrRecruitmentNeedDTO.getId()));
            if (CollectionUtils.isNotEmpty(recruitmentStationList)){
                hrRecruitmentNeedDTO.setHrRecruitmentStationDTOList(hrRecruitmentStationMapper.toDto(recruitmentStationList));
            }
        }
        return hrRecruitmentNeedDTOS;
    }
}
