package cn.casair.web.rest;

import cn.casair.CasairwebbaseApp;
import cn.casair.common.enums.ContractEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.utils.PdfUtils;
import cn.casair.domain.*;
import cn.casair.dto.HrBillDetailDTO;
import cn.casair.dto.HrBillTotalDTO;
import cn.casair.dto.HrFeeReviewDTO;
import cn.casair.dto.formdata.SignRegionDTO;
import cn.casair.mapper.HrBillDetailMapper;
import cn.casair.mapper.HrBillTotalMapper;
import cn.casair.mapper.HrFeeReviewMapper;
import cn.casair.repository.*;
import cn.casair.service.HrApplyOpLogsService;
import cn.casair.service.HrBillDetailService;
import cn.casair.service.HrContractService;
import cn.casair.service.component.ecloud.ECloudComponent;
import com.agile.ecloud.sdk.bean.ECloudDomain;
import com.agile.ecloud.sdk.http.EcloudClient;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/12/03 14:45
 */
@Slf4j
@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CasairwebbaseApp.class)
public class SpringbootTest {

    @Autowired
    private ECloudComponent eCloudComponent;
    @Autowired
    private HrContractService hrContractService;
    @Autowired
    private HrProtocolRepository hrProtocolRepository;
    @Autowired
    private HrFeeReviewRepository hrFeeReviewRepository;
    @Autowired
    private HrFeeReviewMapper hrFeeReviewMapper;
    @Autowired
    private HrBillDetailRepository hrBillDetailRepository;
    @Autowired
    private HrBillDetailService hrBillDetailService;
    @Autowired
    private HrBillDetailMapper hrBillDetailMapper;
    @Autowired
    private HrBillTotalRepository hrBillTotalRepository;
    @Autowired
    private HrBillTotalMapper hrBillTotalMapper;

    @Test
    public void getEClouldUserInfo() throws Exception {
        // ECloudDomain eCloudDomain = eCloudComponent.getCertInfo("1", "0", "23030219720830602X");
        // ECloudDomain eCloudDomain = EcloudClient.applyCert("2", "4", "91370211750412645W", "青岛市黄岛区人力资源有限公司", "0532-80981915");
        // ECloudDomain eCloudDomain = EcloudClient.applyCert("1", "0", "", "", "");
        // ECloudDomain eCloudDomain=EcloudClient.updateCert("2","4","511902200001131221","已废弃","0000-00000000");
        // ECloudDomain eCloudDomain = EcloudClient.changeMobile("1", "0", "", "","");

        // ECloudDomain returnData = EcloudClient.getContractList("0532-80981915", "1", "100");
        // ECloudDomain returnData1 = EcloudClient.getContractList("***********", "1", "10");
        // JSONObject jsonObject = eCloudComponent.revokeUser("", "", "");
        // ECloudDomain eCloudDomain = EcloudClient.getContractDetail("","CB26945CDAAA0E73");
        // ECloudDomain eCloudDomain = EcloudClient.downloadContractBase64("30EC7AAECFE21DD2");
        ECloudDomain eCloudDomain = EcloudClient.threeElementsIdentification("王敬美", "***********", "23030219720830602x");
        // ECloudDomain eCloudDomain = EcloudClient.bankVerify("王泓博","***********","6210812390012539070","37021120000325201x",4);
        log.info("易云章返回信息：{}", JSON.toJSONString(eCloudDomain));
        /*JSONObject o2 = JSONObject.parseObject(eCloudDomain.getData().toString());
        Base64Util.decoderBase64File(String.valueOf(o2.get("bytes")),"D:\\tmp\\hetong.pdf");*/
    }

    /**
     * 甲方签章到合同
     */
    @Test
    public void firstPartSign() {
        String signId = "********";
        String templatePath = "https://hr-server.hdqhr.com:10443/minio/hrresources/61d53ce1f2a746babf6180b08334cb49.pdf";
        String contractNum = "HT25C8WTQ43GT4Y7";
        SignRegionDTO signRegionDTO = PdfUtils.getPdfSignRegion(templatePath, ContractEnum.FormField.firstPartSign.getKey());
        if (signRegionDTO != null) {
            this.eCloudComponent.firstPartSealToContract(contractNum, signId, signRegionDTO);
            // HrAppendix hrAppendix = this.eCloudComponent.downloadContractFile(contractNum);
            // log.info("下载合同信息:{}", hrAppendix);
        }
    }

    /**
     * 作废合同
     */
    @Test
    public void contractCancel() {
        List<String> contractNum = new ArrayList<>();
        contractNum.add("HTZ26P72O5IT8F61");
        contractNum.forEach(ls -> this.eCloudComponent.contractCancel(ls));
    }

    @Test
    public void testHrContract() {
        this.hrContractService.scheduleTaskToUpdateContractState();
    }

    @Test
    public void testLoopClient() {
        String clientId = "07e5014154f51f6f1fa89bcd3d723663";

        List<HrClient> clientIds = this.hrProtocolRepository.selectAllParentClientById(clientId);
        Set<String> clientIdList = new LinkedHashSet<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIds)) {
            for (HrClient client : clientIds) {
                clientIdList.add(client.getId());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIds)) {
                    List<HrClient> clientIdLists = this.hrProtocolRepository.selectAllParentClientById(client.getParentId());
                    for (HrClient idList : clientIdLists) {
                        clientIdList.add(idList.getId());
                    }
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(clientIdLists)) {
                        for (HrClient idLisst : clientIdLists) {
                            List<HrClient> clientIdListss = this.hrProtocolRepository.selectAllParentClientById(idLisst.getParentId());
                            for (HrClient idListss : clientIdListss) {
                                clientIdList.add(idListss.getId());
                            }
                        }
                    }

                }
            }
        }
        clientIdList.forEach(log::debug);
    }

    @Test
    public void getFeeInfo(){
        List<HrFeeReview> hrFeeReviews = hrFeeReviewRepository.selectList(new QueryWrapper<HrFeeReview>().eq("is_delete", 0));
        List<HrFeeReviewDTO> hrFeeReviewDTOList = hrFeeReviewMapper.toDto(hrFeeReviews);
        for (HrFeeReviewDTO hrFeeReviewDTO : hrFeeReviewDTOList) {
            List<String> billIds = Arrays.asList(hrFeeReviewDTO.getBillId().split(","));
            hrFeeReviewDTO.setBillIdList(billIds);
            List<HrBillDetailDTO> listByBillIdBatch = hrBillDetailRepository.getListByBillIdBatch(hrFeeReviewDTO.getBillIdList(), 1);
            if (CollectionUtils.isNotEmpty(listByBillIdBatch)){
                for (HrBillDetailDTO byBillIdBatch : listByBillIdBatch) {
                    String id = byBillIdBatch.getId();
                    byBillIdBatch.setId(null).setBillId(hrFeeReviewDTO.getId()).setIsUsed(true).setReason(id);
                }
                List<HrBillDetail> hrBillDetailList = hrBillDetailMapper.toEntity(listByBillIdBatch);
                hrBillDetailService.saveBatch(hrBillDetailList);
            }

            HrBillTotalDTO billTotalByBatchBill = hrBillTotalRepository.getBillTotalByBatchBill(billIds);
            if (billTotalByBatchBill!=null){
                billTotalByBatchBill.setBillId(hrFeeReviewDTO.getId()).setPayYear(hrFeeReviewDTO.getPayYear()).setPayMonthly(hrFeeReviewDTO.getPayMonthly());
                HrBillTotal hrBillTotal = hrBillTotalMapper.toEntity(billTotalByBatchBill);
                hrBillTotalRepository.insert(hrBillTotal);
            }
        }
    }
    @Autowired
    private HrTalentStaffRepository hrTalentStaffRepository;
    @Autowired
    private HrApplyDepartureStaffRepository hrApplyDepartureStaffRepository;
    @Autowired
    private HrApplyOpLogsService hrApplyOpLogsService;
    @Autowired
    private HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;
    @Autowired
    private HrContractRepository hrContractRepository;

    /**
     * 批量设置员工离职
     */
    @Test
    public void updateStaff(){
        List<HrTalentStaff> hrTalentStaffs = hrTalentStaffRepository.selectList(new QueryWrapper<HrTalentStaff>().in("certificate_num","469021199002269764","469007197602091318"));
        List<String> collect = hrTalentStaffs.stream().map(HrTalentStaff::getId).collect(Collectors.toList());

        List<HrStaffWorkExperience> hrStaffWorkExperiences = hrStaffWorkExperienceRepository.selectList(new QueryWrapper<HrStaffWorkExperience>().in("staff_id", collect).eq("iz_default", 1).eq("is_delete", 0));

        List<HrContract> hrContracts = hrContractRepository.selectList(new QueryWrapper<HrContract>().in("staff_id", collect).eq("is_delete", 0));

        List<HrApplyDepartureStaff> hrApplyDepartureStaffs = hrApplyDepartureStaffRepository.selectList(new QueryWrapper<HrApplyDepartureStaff>()
            .le("departure_staff_status",5)
            .in("staff_id", collect).eq("is_delete", 0));

        List<String> staffWorkExperienceIds = new ArrayList<>();
        List<String> contractIds = new ArrayList<>();
        List<String> applyDepartureStaffIds = new ArrayList<>();
        for (HrTalentStaff hrTalentStaff : hrTalentStaffs) {
            hrTalentStaff.setStaffStatus(5).setResignationDate(LocalDate.now()).setIzInsured(2);
            hrTalentStaffRepository.updateById(hrTalentStaff);
            List<HrStaffWorkExperience> staffWorkExperiences = hrStaffWorkExperiences.stream().filter(ls -> ls.getStaffId().equals(hrTalentStaff.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(staffWorkExperiences)){
                staffWorkExperienceIds.add(staffWorkExperiences.get(0).getId());
            }
            List<HrContract> contracts = hrContracts.stream().filter(lst -> lst.getStaffId().equals(hrTalentStaff.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(contracts)){
                List<String> contractId = contracts.stream().map(HrContract::getId).collect(Collectors.toList());
                contractIds.addAll(contractId);
            }
            List<HrApplyDepartureStaff> applyDepartureStaffs = hrApplyDepartureStaffs.stream().filter(lst -> lst.getStaffId().equals(hrTalentStaff.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(applyDepartureStaffs)){
                for (HrApplyDepartureStaff applyDepartureStaff : applyDepartureStaffs) {
                    applyDepartureStaffIds.add(applyDepartureStaff.getId());
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(applyDepartureStaff.getApplyDepartureId(),applyDepartureStaff.getId(),"","由其他途径直接完成离职流程！",null, ServiceCenterEnum.DEPARTURE_APPLICATIONS.getKey());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(staffWorkExperienceIds)){
            hrTalentStaffRepository.updateStaffWorkExperience(staffWorkExperienceIds,LocalDate.now());
        }
        if (CollectionUtils.isNotEmpty(contractIds)){
            hrTalentStaffRepository.updateContract(contractIds);
        }
        if (CollectionUtils.isNotEmpty(applyDepartureStaffIds)){
            hrTalentStaffRepository.updateApplyDepartureStaff(applyDepartureStaffIds);
        }
    }
}
