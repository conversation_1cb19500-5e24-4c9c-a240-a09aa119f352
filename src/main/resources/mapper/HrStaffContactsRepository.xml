<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStaffContactsRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, staff_id, family_name, relation, contact_way, contact_address,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrStaffContacts">
                    <id column="id" property="id"/>
                    <result column="staff_id" property="staffId"/>
                    <result column="family_name" property="familyName"/>
                    <result column="relation" property="relation"/>
                    <result column="contact_way" property="contactWay"/>
                    <result column="contact_address" property="contactAddress"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

</mapper>
