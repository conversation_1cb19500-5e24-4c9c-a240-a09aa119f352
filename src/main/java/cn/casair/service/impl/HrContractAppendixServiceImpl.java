package cn.casair.service.impl;

import cn.casair.cache.RedisCache;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.PdfUtils;
import cn.casair.domain.*;
import cn.casair.dto.HrContractAppendixDTO;
import cn.casair.dto.JWTMiniDTO;
import cn.casair.dto.formdata.InputLabel;
import cn.casair.dto.formdata.SignRegionDTO;
import cn.casair.dto.verify.VerifyUserInfo;
import cn.casair.mapper.HrContractAppendixMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.ecloud.ECloudComponent;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 员工合同-电签附件服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrContractAppendixServiceImpl extends ServiceImpl<HrContractAppendixRepository, HrContractAppendix> implements HrContractAppendixService {

    @Value("${file.temp-path}")
    private String tempPath;

    private final RedisCache redisCache;
    private final HrContractTemplateService hrContractTemplateService;
    private final DistinguishOcrService distinguishOcrService;
    private final CodeTableService codeTableService;
    private final CodeTableRepository codeTableRepository;
    private final HrContractRepository hrContractRepository;
    private final HrStaffEmolumentRepository hrStaffEmolumentRepository;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrApplyEntryStaffRepository hrApplyEntryStaffRepository;
    private final ECloudComponent eCloudComponent;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrAppendixService hrAppendixService;
    private final HrContractTemplateRepository hrContractTemplateRepository;
    private final HrContractAppendixRepository hrContractAppendixRepository;
    private final HrContractAppendixMapper hrContractAppendixMapper;
    private final HrUpcomingService hrUpcomingService;
    private final HrPlatformAccountRepository hrPlatformAccountRepository;

    @Override
    public void checkStaffBankInfo(JWTMiniDTO user, String bankName, String bankNo) {
        if (StringUtils.isNotBlank(bankName) && StringUtils.isNotBlank(bankNo)) {
            // 检查用户上传银行卡是否与入职公司
            HrPlatformAccount hrPlatformAccount = this.hrPlatformAccountRepository.selectClientPlatformAccount(user.getClientId());
            if (hrPlatformAccount == null) {
                throw new CommonException("您的入职公司暂未设置工资发放银行,请联系相关人员.");
            }
            if (bankName.length() < 4) {
                throw new CommonException("请输入正确的银行名称!");
            }
            if (!hrPlatformAccount.getIssuingBank().contains(bankName)) {
                throw new CommonException("请上传您入职公司要求的工资发放银行卡!");
            }
            VerifyUserInfo verifyUserInfo = new VerifyUserInfo();
            verifyUserInfo.setRealName(user.getName());
            // verifyUserInfo.setPhone(user.getPhone());
            verifyUserInfo.setBankNumber(bankNo);
            verifyUserInfo.setIdCardNumber(user.getCertificateNum());
            Map<String, Object> map = this.distinguishOcrService.bankVerify(verifyUserInfo);
            String code = String.valueOf(map.get("code"));
            if (!"200".equals(code)) {
                log.error("个人银行卡认证失败:{}", map.get("msg"));
                throw new CommonException(map.get("msg") + ",可能存在:[银行卡非本人名下/新办银行卡/银行卡过旧/对比库未收录此银行]");
            }
        } else {
            throw new CommonException("银行名称或银行卡号信息未识别成功,请重新上传银行卡图片进行识别");
        }
    }

    @Override
    public HrContractAppendixDTO staffConfirmSign(HrContractAppendixDTO hrContractAppendixDTO) {
        if (StringUtils.isBlank(hrContractAppendixDTO.getId())) {
            throw new CommonException("合同项id不能为空!");
        }
        if (hrContractAppendixDTO.getSignId() == null) {
            throw new CommonException("签名id不能为空!");
        }

        JWTMiniDTO user = SecurityUtils.getCurrentMini().get();
        HrContractAppendix hrContractAppendix = this.hrContractAppendixRepository.selectById(hrContractAppendixDTO.getId());
        HrContract hrContract = this.hrContractRepository.selectById(hrContractAppendix.getContractId());
        if (!user.getId().equals(hrContract.getStaffId())) {
            throw new CommonException("用户信息校验异常!");
        }
        // 甲乙方信息填入PDF模板
        HrAppendix hrAppendix = this.hrContractTemplateService.makeTemplate(hrContractAppendix);
        // 上传合同
        try {
            this.eCloudComponent.uploadContractUrl(hrContractAppendix.getContractNum(), hrContractAppendix.getName(), hrAppendix.getFileUrl());
        } catch (Exception e) {
            log.info("上传合同异常:{}", e.getMessage());
        }
        // 乙方签章到合同
        SignRegionDTO signRegionDTO;
        String redisKey = RedisKeyEnum.currencyKey.PDF_SIGN_REGION.getValue() + hrContractAppendix.getTemplateId() + ":" + ContractEnum.FormField.secondPartSign.getKey();
        if (this.redisCache.hasKey(redisKey)) {
            signRegionDTO = JSONObject.parseObject(this.redisCache.getCacheObject(redisKey), SignRegionDTO.class);
        } else {
            signRegionDTO = PdfUtils.getPdfSignRegion(hrContractAppendix.getOriginalPath(), ContractEnum.FormField.secondPartSign.getKey());
            if (signRegionDTO == null) {
                throw new CommonException("乙方签章区域获取异常!");
            }
            this.redisCache.setCacheObject(redisKey, JSON.toJSONString(signRegionDTO));
        }
        // 劳动合同需要甲方签章 所以合同签署状态须设为未完成状态
        int isFinish = 0;
        HrContractTemplate hrContractTemplate = this.hrContractTemplateRepository.selectById(hrContractAppendix.getTemplateId());
        if (ContractEnum.TemplateType.LABOR_CONTRACT.getKey().equals(hrContractTemplate.getType())) {
            isFinish = 1;
        }
        JSONObject jsonObject = this.eCloudComponent.secondPartSealToContract(
            hrContractAppendix.getContractNum(), hrContract.getIdNo(), hrContract.getStaffName(), hrContract.getPhone(), hrContractAppendixDTO.getSignId(), signRegionDTO, isFinish
        );
        log.info("乙方签名到合同:{}", jsonObject);
        // 下载合同文件并上传到文件服务器
        HrAppendix contract = this.eCloudComponent.downloadContractFile(hrContractAppendix.getContractNum());
        hrContractAppendix.setSignId(hrContractAppendixDTO.getSignId());
        hrContractAppendix.setState(ContractEnum.ExamineState.SIGNED.getKey());
        hrContractAppendix.setAppendixId(contract.getId());
        hrContractAppendix.setAppendixPath(contract.getFileUrl());
        this.hrContractAppendixRepository.updateById(hrContractAppendix);

        // 清理临时文件
        this.hrAppendixService.deleteHrAppendix(hrAppendix.getId());
        return this.hrContractAppendixMapper.toDto(hrContractAppendix);
    }

    @Override
    public HrContractAppendixDTO staffConfirmInformation(HrContractAppendixDTO hrContractAppendixDTO) {
        JWTMiniDTO user = SecurityUtils.getCurrentMini().get();
        HrContractAppendix hrContractAppendix = hrContractAppendixRepository.selectById(hrContractAppendixDTO.getId());
        HrContractTemplate hrContractTemplate = hrContractTemplateRepository.selectById(hrContractAppendix.getTemplateId());

        // 当合同为劳动合同时 更新员工信息
        if (ContractEnum.TemplateType.LABOR_CONTRACT.getKey().equals(hrContractTemplate.getType())) {
            HrTalentStaff hrTalentStaff = this.hrTalentStaffRepository.selectById(user.getId());
            List<InputLabel> inputLabelList = JSON.parseArray(hrContractAppendixDTO.getSecondsPartInfo(), InputLabel.class);
            Map<String, Object> secondsPartInfo = new HashMap<>();
            inputLabelList.forEach(l -> secondsPartInfo.put(l.getName(), l.getValue()));
            String secondPartHometown = String.valueOf(secondsPartInfo.get(ContractEnum.FormField.secondPartHometown.getKey()));
            String secondPartZipCode = String.valueOf(secondsPartInfo.get(ContractEnum.FormField.secondPartZipCode.getKey()));
            // 通信地址
            String secondPartAddress = String.valueOf(secondsPartInfo.get(ContractEnum.FormField.secondPartAddress.getKey()));
            if (StringUtils.isNotBlank(secondPartHometown) && !"null".equals(secondPartHometown)) {
                hrTalentStaff.setCensusRegisterAddress(secondPartHometown);
            }
            if (StringUtils.isNotBlank(secondPartAddress)) {
                hrTalentStaff.setContactAddress(secondPartAddress);
            }
            if (StringUtils.isNotBlank(secondPartZipCode) && !"null".equals(secondPartZipCode)) {
                hrTalentStaff.setContactPostcode(secondPartZipCode);
            }
            this.hrTalentStaffRepository.updateById(hrTalentStaff);
        }

        // 更新合同确认信息
        hrContractAppendix
            .setId(hrContractAppendixDTO.getId())
            .setState(ContractEnum.ExamineState.CONFIRMED.getKey())
            .setSecondsPartInfo(hrContractAppendixDTO.getSecondsPartInfo());
        this.hrContractAppendixRepository.updateById(hrContractAppendix);
        return this.hrContractAppendixMapper.toDto(hrContractAppendix);
    }

    @Override
    public Map<String, Object> checkContractItemState(Map<String, Object> params) {
        String id = String.valueOf(params.get("id"));
        if (StringUtils.isBlank(id)) {
            throw new CommonException("查询合同项id不能为空！");
        }
        HrContractAppendix hrContractAppendix = this.hrContractAppendixRepository.selectById(id);
        if (hrContractAppendix == null) {
            throw new CommonException("未查询到相关合同项！");
        }
        if (!hrContractAppendix.getType().equals(ContractEnum.AppendixType.TEMPLATE_CONTRACT.getKey())) {
            throw new CommonException("仅可查询签署合同项目签署状态！");
        }
        // 当合同状态为已签订、通过、未通过 直接返回
        if (hrContractAppendix.getState().equals(ContractEnum.ExamineState.SIGNED.getKey())
            || hrContractAppendix.getState().equals(ContractEnum.ExamineState.PASS.getKey())
            || hrContractAppendix.getState().equals(ContractEnum.ExamineState.FAIL.getKey())) {
            params.put("status", 4);
            // params.put("evidenceUrl", hrContractAppendix.getAdditionalInfo());
        } else if (hrContractAppendix.getState().equals(ContractEnum.ExamineState.TO_BE_SIGNED.getKey()) || hrContractAppendix.getState().equals(ContractEnum.ExamineState.COUNTERSIGN.getKey())) {
            // 待签订状态=》已签订
            JWTMiniDTO user = SecurityUtils.getCurrentMini().get();
            // JSONObject jsonObject = this.eCloudComponent.getContractDetail(user.getPhone(), hrContractAppendix.getContractNum());
            // Map<String, Object> result = JSON.parseObject(JSON.toJSONString(jsonObject.get("contractInfo")), Map.class);
            // Integer status = Integer.valueOf(String.valueOf(result.get("status")));
            params.put("status", 2);
            // 判断合同状态是否为已签订状态
            /*if (status.equals(4)) {
                // 获取易云章合同文件
                HrAppendix hrAppendix = this.eCloudComponent.downloadContract(hrContractAppendix.getContractNum(), user.getPhone());
                hrContractAppendix.setAppendixId(hrAppendix.getId());
                hrContractAppendix.setAppendixPath(hrAppendix.getFileUrl());

                // 合同存证下载 易云章合同存根查看地址特殊处理
                // 易云章接口暂时注释
                // JSONObject evidenceData = this.eCloudComponent.getEvidenceData(user.getPhone(), hrContractAppendix.getContractNum());
                // hrContractAppendix.setAdditionalInfo(evidenceData.getString("ossUrl"));
                this.hrContractAppendixRepository.updateById(hrContractAppendix);
                // 更新合同项状态为已签订
                this.hrContractAppendixRepository.setContractAppendixSigned(id);
                // 更新合同签署日期
                this.hrContractRepository.updateContractSignDate(hrContractAppendix.getContractId());

                params.put("contractUrl", hrAppendix.getFileUrl());
                // params.put("evidenceUrl", hrContractAppendix.getAdditionalInfo());
            }*/
        }
        params.put("contractUrl", hrContractAppendix.getAppendixPath());
        return params;
    }

    @Override
    public void staffUploadContract(String contractId) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        // 获取合同信息
        List<HrContractAppendixDTO> list = this.hrContractAppendixRepository.getStaffContractListByObject(jwtMiniDTO.getId(), jwtMiniDTO.getClientId(), contractId);
        list.forEach(ls -> {
            if (!ls.getState().equals(ContractEnum.ExamineState.SIGNED.getKey()) && !ls.getState().equals(ContractEnum.ExamineState.PASS.getKey())) {
                throw new CommonException(ls.getName() + "未完成！");
            }
        });
        // 员工izStartEnd状态设置为待审核
        this.hrTalentStaffRepository.updateStaffIzStartEnd(jwtMiniDTO.getId(), StaffEnum.IzStartEndEnum.TO_BE_REVIEWED.getKey());

        // 添加入职操作日志
        HrApplyEntryStaff hrApplyEntryStaff = this.hrApplyEntryStaffRepository.getApplyEntryStaffInfo(jwtMiniDTO.getId(), jwtMiniDTO.getClientId());
        if (hrApplyEntryStaff == null) {
            throw new CommonException("未查询到员工入职申请信息！");
        }
        String message = jwtMiniDTO.getName() + " 签署了劳动合同。";
        hrApplyOpLogsService.saveHrApplyOpLogsMiniPhaseTWO(hrApplyEntryStaff.getApplyId(), hrApplyEntryStaff.getId(), jwtMiniDTO.getId(), message, true, ServiceCenterEnum.ENTRY_APPLICATIONS.getKey());
        hrUpcomingService.createServiceUpcoming(hrApplyEntryStaff.getId(),hrApplyEntryStaff.getStaffId(), "入职服务-为" + jwtMiniDTO.getName() + "进行合同确认",LocalDate.now(),0);
        // 更新员工入职申请状态为发起劳动合同
        this.hrApplyEntryStaffRepository.updateStaffApplyEntryState(jwtMiniDTO.getId(), jwtMiniDTO.getClientId(), ApprovalEntryStatusEnum.EntryStatus.SIGN_CONTRACT.getKey(), ApprovalEntryStatusEnum.EntryStatus.CONFIRMATION_CONTRACT.getKey());
    }

    @Override
    public HrAppendix contractSign(HrContractAppendixDTO hrContractAppendixDTO) {
        // 获取模板信息
        HrContractTemplate hrContractTemplate = this.hrContractTemplateRepository.selectById(hrContractAppendixDTO.getTemplateId());
        if (hrContractTemplate == null) {
            throw new CommonException("未查询到相关模板信息！");
        }

        Map<String, Object> formFieldList = new HashMap<>();
        LocalDate localDate = LocalDate.now();

        // 判断模板类型
        ContractEnum.TemplateType enumByKey = ContractEnum.TemplateType.getEnumByKey(hrContractTemplate.getType());
        switch (Objects.requireNonNull(enumByKey)) {
            // 劳动合同
            case LABOR_CONTRACT:
                formFieldList.put("secondPartSignature", hrContractAppendixDTO.getAutographUrl());
                formFieldList.put("secondPartSignatureYear", localDate.getYear());
                formFieldList.put("secondPartSignatureMonth", localDate.getMonthValue());
                formFieldList.put("secondPartSignatureDay", localDate.getDayOfMonth());
                break;
            // 承诺书
            case COMMITMENT:
                formFieldList.put("promiseSignature", hrContractAppendixDTO.getAutographUrl());
                formFieldList.put("promiseSignatureDate", localDate.format(DateTimeFormatter.ofPattern("yyyy.MM.dd")));
                break;
            // 规章制度
            case RULES_REGULATIONS:
                formFieldList.put("staffSignature", hrContractAppendixDTO.getAutographUrl());
                formFieldList.put("signatureYear", localDate.getYear());
                formFieldList.put("signatureMonth", localDate.getMonthValue());
                formFieldList.put("signatureDay", localDate.getDayOfMonth());
                break;
            // 确认函
            case CONFIRMATION:
                formFieldList.put("staffSignature", hrContractAppendixDTO.getAutographUrl());
                formFieldList.put("signatureYear", localDate.getYear());
                formFieldList.put("signatureMonth", localDate.getMonthValue());
                formFieldList.put("signatureDay", localDate.getDayOfMonth());
                break;
            // 一票否决、员工手册
            case ONE_VOTE_VETO:
            case EMPLOYEE_HANDBOOK:
                break;
            default:
                break;
        }

        String tempPdfPath = PdfUtils.fillTextOrImageToPdf(formFieldList, hrContractTemplate.getTemplatePath(), tempPath, true);
        if (StringUtils.isBlank(tempPdfPath)) {
            throw new CommonException("PDF文件制作异常！");
        }
        // 上传到文件服务器
        HrAppendix hrAppendix = this.hrAppendixService.uploadTempPdf(tempPdfPath);

        // 更新合同附件
        hrContractAppendixDTO.setAppendixId(hrAppendix.getId());
        hrContractAppendixDTO.setAppendixPath(hrAppendix.getFileUrl());
        hrContractAppendixDTO.setState(ContractEnum.ExamineState.SIGNED.getKey());
        this.hrContractAppendixRepository.updateById(this.hrContractAppendixMapper.toEntity(hrContractAppendixDTO));
        return hrAppendix;
    }

    @Override
    public void contractAppendixUpload(HrContractAppendixDTO hrContractAppendixDTO) {
        hrContractAppendixDTO.setState(ContractEnum.ExamineState.SIGNED.getKey());
        // 新增自定义附件
        if (hrContractAppendixDTO.getType().equals(ContractEnum.AppendixType.CUSTOMIZE_ATTACHMENT.getKey())) {
            if (StringUtils.isBlank(hrContractAppendixDTO.getName())) {
                throw new CommonException("请输入自定义附件名称！");
            }
            this.hrContractAppendixRepository.insert(this.hrContractAppendixMapper.toEntity(hrContractAppendixDTO));
        } else {
            JWTMiniDTO user = SecurityUtils.getCurrentMini().get();
            // 身份证
            if (hrContractAppendixDTO.getCertificateAttribute().equals(1)) {
                if (StringUtils.isNotBlank(hrContractAppendixDTO.getFillInfo())) {
                    JSONObject jsonObject = JSON.parseObject(hrContractAppendixDTO.getFillInfo());
                    String idNo = String.valueOf(jsonObject.get("NUM"));
                    String realName = String.valueOf(jsonObject.get("NAME"));
                    if (!realName.equals(user.getName())) {
                        throw new CommonException("上传身份证姓名与当前用户信息不匹配！");
                    }
                    if (!idNo.equals(user.getCertificateNum())) {
                        throw new CommonException("上传身份证号码与当前用户信息不匹配！");
                    }
                }
            }
            // 如果附件为银行卡 通过银行卡校验后更新用户银行卡信息
            if (hrContractAppendixDTO.getCertificateAttribute().equals(2)) {
                this.checkStaffBankInfo(user, hrContractAppendixDTO.getBankName(), hrContractAppendixDTO.getBankNo());
                // 查银行字典 没有则添加
                CodeTable codeTable = this.codeTableService.getItemByBankName(hrContractAppendixDTO.getBankName());
                if (codeTable != null) {
                    this.hrStaffEmolumentRepository.updateStaffBankInfo(user.getId(), codeTable.getItemValue(), hrContractAppendixDTO.getBankNo());
                } else {
                    CodeTable children = this.codeTableService.getMaxChildrenByInnerName("ownedBank");
                    CodeTable codeTableNew = new CodeTable();
                    codeTableNew.setParentId(children.getParentId());
                    codeTableNew.setItemName(hrContractAppendixDTO.getBankName());
                    codeTableNew.setItemValue(children.getItemValue() + 1);
                    codeTableNew.setDisplayOrder(children.getDisplayOrder() + 1);
                    this.codeTableRepository.insert(codeTableNew);
                    this.hrStaffEmolumentRepository.updateStaffBankInfo(user.getId(), codeTableNew.getItemValue(), hrContractAppendixDTO.getBankNo());
                }
            }


            // 更新附件
            this.hrContractAppendixRepository.updateById(this.hrContractAppendixMapper.toEntity(hrContractAppendixDTO));
        }
    }

    /**
     * 创建员工合同-电签附件
     *
     * @param hrContractAppendixDTO
     * @return
     */
    @Override
    public HrContractAppendixDTO createHrContractAppendix(HrContractAppendixDTO hrContractAppendixDTO) {
        log.info("Create new HrContractAppendix:{}", hrContractAppendixDTO);

        HrContractAppendix hrContractAppendix = this.hrContractAppendixMapper.toEntity(hrContractAppendixDTO);
        this.hrContractAppendixRepository.insert(hrContractAppendix);
        return this.hrContractAppendixMapper.toDto(hrContractAppendix);
    }

    /**
     * 修改员工合同-电签附件
     *
     * @param hrContractAppendixDTO
     * @return
     */
    @Override
    public Optional<HrContractAppendixDTO> updateHrContractAppendix(HrContractAppendixDTO hrContractAppendixDTO) {
        return Optional.ofNullable(this.hrContractAppendixRepository.selectById(hrContractAppendixDTO.getId()))
                .map(roleTemp -> {
                    HrContractAppendix hrContractAppendix = this.hrContractAppendixMapper.toEntity(hrContractAppendixDTO);
                    this.hrContractAppendixRepository.updateById(hrContractAppendix);
                    log.info("Update HrContractAppendix:{}", hrContractAppendixDTO);
                    return hrContractAppendixDTO;
                });
    }

    /**
     * 查询员工合同-电签附件详情
     *
     * @param id
     * @return
     */
    @Override
    public HrContractAppendixDTO getHrContractAppendix(String id) {
        log.info("Get HrContractAppendix :{}", id);

        HrContractAppendix hrContractAppendix = this.hrContractAppendixRepository.selectById(id);
        return this.hrContractAppendixMapper.toDto(hrContractAppendix);
    }

    /**
     * 删除员工合同-电签附件
     *
     * @param id
     */
    @Override
    public void deleteHrContractAppendix(String id) {
        Optional.ofNullable(this.hrContractAppendixRepository.selectById(id))
                .ifPresent(hrContractAppendix -> {
                    this.hrContractAppendixRepository.deleteById(id);
                    log.info("Delete HrContractAppendix:{}", hrContractAppendix);
                });
    }

    /**
     * 批量删除员工合同-电签附件
     *
     * @param ids
     */
    @Override
    public void deleteHrContractAppendix(List<String> ids) {
        log.info("Delete HrContractAppendixs:{}", ids);
        this.hrContractAppendixRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询员工合同-电签附件
     *
     * @param hrContractAppendixDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrContractAppendixDTO hrContractAppendixDTO, Long pageNumber, Long pageSize) {
        Page<HrContractAppendix> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrContractAppendix> qw = new QueryWrapper<>(this.hrContractAppendixMapper.toEntity(hrContractAppendixDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrContractAppendixRepository.selectPage(page, qw);
        iPage.setRecords(this.hrContractAppendixMapper.toDto(iPage.getRecords()));
        return iPage;
    }
}
