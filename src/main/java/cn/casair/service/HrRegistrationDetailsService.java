package cn.casair.service;

import cn.casair.domain.HrRegistrationDetails;
import cn.casair.dto.HrExamResultDTO;
import cn.casair.dto.HrRegistrationDetailsDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;

/**
 * 报名情况服务类
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
public interface HrRegistrationDetailsService extends IService<HrRegistrationDetails> {

    /**
     * 创建报名情况
     *
     * @param hrRegistrationDetailsDTO
     * @return
     */
    HrRegistrationDetailsDTO createHrRegistrationDetails(HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    /**
     * 拟聘用公示
     * @param
     * @return
     */
    ResponseEntity<?> updateHrRegistrationDetails(HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    /**
     * 查询报名情况详情
     * @param id
     * @return
     */
    HrRegistrationDetailsDTO getHrRegistrationDetails(String id);

    /**
     * 删除报名情况
     * @param id
     */
    void deleteHrRegistrationDetails(String id);

    /**
     * 批量删除报名情况
     * @param ids
     */
    void deleteHrRegistrationDetails(List<String> ids);

    /**
     * 分页查询报名情况
     * @param hrRegistrationDetailsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRegistrationDetailsDTO hrRegistrationDetailsDTO,Long pageNumber,Long pageSize);

    void updateHrRegistration(HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    ResponseEntity<?> updateHrRegistrationEvaluation(HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    /**
     * 抽签号模板
     * @param response
     * @param brochureId
     */
    void importDrawLotsNumberTemplate(HttpServletResponse response, String brochureId);

    /**
     * 导入抽签号
     * @param file
     * @param redisKey
     * @return
     */
    ResponseEntity importDrawLotsNumber(MultipartFile file, String redisKey,String brochureId);

    ResponseEntity<?> selectHrRegistrationDetails(HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    /**
     * 导出报名情况
     * @param hrRegistrationDetailsDTO
     * @param httpServletResponse
     */
    String detailsExport(HrRegistrationDetailsDTO hrRegistrationDetailsDTO, HttpServletResponse httpServletResponse);

    /**
     * 导出时导出人员报名信息
     * @param fileList
     * @param hrRegistrationDetailsList
     * @return
     */
    List<String> exportSignUp(List<File> fileList, List<HrRegistrationDetails> hrRegistrationDetailsList);

    /**
     * 考察通知
     * @param hrRegistrationDetailsDTO
     * @return
     */
    ResponseEntity<?> noticeInvestigation(List<HrRegistrationDetailsDTO> hrRegistrationDetailsDTO);

    ResponseEntity<?> getHrRegistrationDetailsCheckup(List<HrRegistrationDetailsDTO> hrRegistrationDetailsDTO);


    ResponseEntity<?> getHrRegistrationInformation(List<String> id);

    ResponseEntity<?>  updateHrRegistrationEvaluationExel(List<String> ids) ;

    /**
     * 查询该员工所有的报名信息
     * @param staffId 员工Id
     * @return
     */
    List<HrRegistrationDetails> findRegistrationDetailsByStaffId(String staffId);

    List<HrRegistrationDetails> getHrRegistrationDetailsStation(String id);

    ResponseEntity<?> getHrRegistrationDetailsReview(HrRegistrationDetailsDTO hrRegistrationDetailsDTO);

    ResponseEntity<?> getHrRegistrationDetailsReviewCheckup(HrExamResultDTO hrExamResultDTO);
}
