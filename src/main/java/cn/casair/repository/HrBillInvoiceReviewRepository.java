package cn.casair.repository;

import cn.casair.domain.HrBillInvoiceReview;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 结算单开票中间表数据库操作类
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@Repository
public interface HrBillInvoiceReviewRepository extends BaseMapper<HrBillInvoiceReview> {

    @Update("UPDATE hr_bill_invoice_review SET is_delete = 1,last_modified_date = NOW() WHERE invoice_id = #{invoiceId}")
    void delByInvoiceId(@Param("invoiceId") String invoiceId);

}
