package cn.casair.security;

import cn.casair.common.errors.CommonException;
import cn.casair.security.data.IdentityHolder;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 自定义注解OnlyMe的实现
 *
 * <AUTHOR>
 * @date 2020/2/14
 */
@Aspect
@Component
public class IdentityAspect {
    private static final Logger logger = LoggerFactory.getLogger(IdentityAspect.class);

    @Before("within(@org.springframework.web.bind.annotation.RestController *) && @annotation(cn.casair.security.Identity)")
    public void identity(final JoinPoint joinPoint) throws CommonException {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        IdentityHolder.setContext(methodSignature.getMethod());

//        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
//        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
//        HttpServletRequest httpServletRequest = sra.getRequest();
//        String headerUserId = httpServletRequest.getHeader("cuId");
//        if (headerUserId == null) {
//            throw new CommonException("无效的请求");
//        } else {
//            try {
//                Long hUserId = Long.parseLong(headerUserId);
//                Long tokenUserId = SecurityUtils.getCurrentUser().get().getId();
//                if (!tokenUserId.equals(hUserId)) {
//                    throw new CommonException("无效的资源");
//                }
//            } catch (NumberFormatException e) {
//                throw new CommonException("无效的请求");
//            }
//
//        }
    }
}
