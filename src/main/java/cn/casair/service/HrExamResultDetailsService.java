package cn.casair.service;
import cn.casair.domain.HrExamResultDetails;
import cn.casair.dto.HrExamResultDetailsDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 考生答案表服务类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface HrExamResultDetailsService extends IService<HrExamResultDetails> {


    /**
     * 创建考生答案表
     * @param hrExamResultDetailsDTO
     * @return
     */
    HrExamResultDetailsDTO createHrExamResultDetails(HrExamResultDetailsDTO hrExamResultDetailsDTO);

    /**
     * 修改考生答案表
     * @param hrExamResultDetailsDTO
     * @return
     */
    Optional<HrExamResultDetailsDTO> updateHrExamResultDetails(HrExamResultDetailsDTO hrExamResultDetailsDTO);

    /**
     * 查询考生答案表详情
     * @param id
     * @return
     */
    HrExamResultDetailsDTO getHrExamResultDetails(String id);

    /**
     * 删除考生答案表
     * @param id
     */
    void deleteHrExamResultDetails(String id);

    /**
     * 批量删除考生答案表
     * @param ids
     */
    void deleteHrExamResultDetails(List<String> ids);

    /**
     * 分页查询考生答案表
     * @param hrExamResultDetailsDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrExamResultDetailsDTO hrExamResultDetailsDTO,Long pageNumber,Long pageSize);
    }
