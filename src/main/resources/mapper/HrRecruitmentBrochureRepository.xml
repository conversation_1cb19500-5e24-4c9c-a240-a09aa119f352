<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrRecruitmentBrochureRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, recruit_brochure_name, client_id, recruitment_need_id, is_overstate, register_start_date, register_end_date, audit_start_date, audit_end_date, payment_start_date, payment_end_date, enterprise_wechat_url, detail_content, appendix_ids, register_state,
is_delete                    ,created_by                    ,last_modified_by                    ,created_date                    ,last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrRecruitmentBrochure">
            <id column="id" property="id"/>
            <result column="recruit_brochure_name" property="recruitBrochureName"/>
            <result column="client_id" property="clientId"/>
            <result column="recruitment_need_id" property="recruitmentNeedId"/>
            <result column="is_overstate" property="isOverstate"/>
            <result column="register_start_date" property="registerStartDate"/>
            <result column="register_end_date" property="registerEndDate"/>
            <result column="audit_start_date" property="auditStartDate"/>
            <result column="audit_end_date" property="auditEndDate"/>
            <result column="payment_start_date" property="paymentStartDate"/>
            <result column="payment_end_date" property="paymentEndDate"/>
            <result column="enterprise_wechat_url" property="enterpriseWechatUrl"/>
            <result column="detail_content" property="detailContent"/>
            <result column="appendix_ids" property="appendixIds"/>
            <result column="register_state" property="registerState"/>
            <result column="is_delete" property="isDelete"/>
            <result column="created_by" property="createdBy"/>
            <result column="last_modified_by" property="lastModifiedBy"/>
            <result column="created_date" property="createdDate"/>
            <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Find_SQL">
        SELECT
            hrn.recruitment_number,
            hrd.recruitment_num enrollment,
            hrd.pass_num passerNum,
            hrb.*,
            rs.exam_format,
            rs.recruitment_people_number,
            rs.recruitment_num,
            rs.pass_num,
            rs.payment_num,
            rs.recruitment_fee,
            hc.client_name,
            hs.profession_name,
            rs.total,
            rs.register_template_id
        FROM
        hr_recruitment_brochure hrb
        LEFT JOIN ( SELECT brochure_id,count(hrd.id) recruitment_num, count(hrd.`status` > 2 or null) pass_num FROM hr_registration_details hrd LEFT JOIN hr_talent_staff hts ON hrd.staff_id = hts.id WHERE hrd.is_delete = 0 AND hts.is_delete = 0 GROUP BY hrd.brochure_id
        ) hrd ON hrb.id = hrd.brochure_id
        LEFT JOIN ( SELECT sum( recruitment_people_number ) total, service_id, any_value ( recruitment_station_id )recruitment_station_id , any_value ( exam_format ) exam_format, any_value(recruitment_people_number) recruitment_people_number,
        any_value(recruitment_num) recruitment_num,any_value(pass_num) pass_num,any_value(payment_num) payment_num,any_value(recruitment_fee) recruitment_fee,any_value (register_template_id) register_template_id FROM hr_recruitment_station GROUP BY service_id ) AS rs ON hrb.id = rs.service_id
        LEFT JOIN hr_client hc ON hc.id = hrb.client_id AND hc.is_delete = 0
        LEFT JOIN hr_station hs ON hs.id = rs.recruitment_station_id AND hs.is_delete = 0
        LEFT JOIN hr_recruitment_need hrn ON hrb.recruitment_need_id = hrn.id AND hrn.is_delete = 0
        WHERE hrb.is_delete = 0
        <if test="param.clientId != null">
            AND hc.id = #{param.clientId}
        </if>
        <if test="param.flag != null and param.flag == 1">
            AND hrb.register_state > 1
        </if>
        <if test="param.recruitmentNumber != null and param.recruitmentNumber != ''">
            AND hrn.recruitment_number LIKE concat('%', #{param.recruitmentNumber}, '%')
        </if>
        <if test="param.recruitBrochureName != null and param.recruitBrochureName != ''">
            AND hrb.recruit_brochure_name LIKE concat('%', #{param.recruitBrochureName}, '%')
        </if>
        <if test="param.registerStateList!=null and param.registerStateList.size() > 0">
            AND hrb.register_state IN
            <foreach collection="param.registerStateList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.clientIds!=null and param.clientIds.size() > 0">
            AND hc.id IN
            <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.ids!=null and param.ids.size() > 0">
            AND hrb.id IN
            <foreach collection="param.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.recruitmentBrochureIdList!=null and param.recruitmentBrochureIdList.size() > 0">
            AND hrb.id IN
            <foreach collection="param.recruitmentBrochureIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <choose>
            <when test="param.field != null and param.field != '' and param.order != null and param.order != ''">
                <choose>
                    <when test="param.field=='pass_num'">
                        ORDER BY hrd.pass_num ${param.order}
                    </when>
                    <when test="param.field=='recruitment_num'">
                        ORDER BY hrd.recruitment_num ${param.order}
                    </when>
                    <otherwise>
                        ORDER BY ${param.field} ${param.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY hrb.created_date DESC
            </otherwise>
        </choose>
    </sql>

    <select id="findPage" resultType="cn.casair.dto.HrRecruitmentBrochureDTO">
        <include refid="Find_SQL"/>
    </select>

    <sql id="Select_SQL">
        SELECT
            hrb.id,
            hrb.recruit_brochure_name,
            hrb.detail_content,
            hrb.client_id,
            hrb.recruitment_need_id,
            hrb.is_overstate,
            hrb.register_start_date,
            hrb.register_end_date,
            hrb.audit_start_date,
            hrb.audit_end_date,
            hrb.payment_start_date,
            hrb.payment_end_date,
--             hrb.register_template_id,
            hrb.enterprise_wechat_url,
            hrb.appendix_ids,
            hrb.register_state,
            hrb.created_date,
            hc.client_name
        FROM
            `hr_recruitment_brochure` hrb
                LEFT JOIN hr_client hc ON hc.id = hrb.client_id
    </sql>

    <select id="findPageSelectWX" resultType="cn.casair.dto.HrRecruitmentBrochureDTO">
        <include refid="Select_SQL"/>
        WHERE
            hrb.is_delete = 0
          AND hrb.register_state IN (
            "5",
            "6")
        ORDER BY

       case when  hrb.register_state = 5 then 0 else 1 end,      hrb.release_time DESC ,  hrb.created_date DESC

    </select>
    <select id="getRecruitment" resultType="cn.casair.dto.HrRecruitmentStationDTO">
            SELECT * FROM `hr_recruitment_station` where service_id=#{id} and is_delete=0
    </select>

    <select id="findRecruitmentBrochureById" resultType="cn.casair.dto.HrRecruitmentBrochureDTO">
        SELECT
            hc.client_name,
            hrn.recruitment_number,
            hrb.*
        FROM
            hr_recruitment_brochure hrb
        LEFT JOIN hr_client hc ON hc.id = hrb.client_id AND hc.is_delete = 0
        LEFT JOIN hr_recruitment_need hrn ON hrb.recruitment_need_id = hrn.id AND hrn.is_delete = 0
        WHERE hrb.id = #{id}
    </select>

    <select id="findList" resultType="cn.casair.dto.HrRecruitmentBrochureDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="findPageSelectOW" resultType="cn.casair.dto.HrRecruitmentBrochureDTO">
        <include refid="Select_SQL"/>
        WHERE
        hrb.is_delete = 0 AND hrb.register_state IN (5,6)
        <if test="param.releaseDate != null">
            AND hrb.created_date >= #{param.releaseDate}
        </if>
        ORDER BY hrb.created_date DESC
    </select>

    <update id="updateBrochure">
        UPDATE hr_recruitment_brochure
        SET recruit_brochure_name = #{param.recruitBrochureName},
        client_id = #{param.clientId},
        recruitment_need_id = #{param.recruitmentNeedId},
        is_overstate = #{param.isOverstate},
        register_start_date = #{param.registerStartDate},
        register_end_date = #{param.registerEndDate},
        audit_start_date = #{param.auditStartDate},
        audit_end_date = #{param.auditEndDate},
        payment_start_date = #{param.paymentStartDate},
        payment_end_date = #{param.paymentEndDate},
        enterprise_wechat_url = #{param.enterpriseWechatUrl},
        detail_content = #{param.detailContent},
        appendix_ids = #{param.appendixIds},
        register_state = #{param.registerState},
        release_time = #{param.releaseTime},
        last_modified_date = NOW()
        WHERE
            id = #{param.id}
    </update>
</mapper>
