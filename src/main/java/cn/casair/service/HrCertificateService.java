package cn.casair.service;
import cn.casair.domain.HrCertificate;
import cn.casair.dto.HrCertificateDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 证件管理服务类
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface HrCertificateService extends IService<HrCertificate> {


    /**
     * 创建证件管理
     * @param hrCertificateDTO
     * @return
     */
    HrCertificateDTO createHrCertificate(HrCertificateDTO hrCertificateDTO);

    /**
     * 修改证件管理
     * @param hrCertificateDTO
     * @return
     */
    Optional<HrCertificateDTO> updateHrCertificate(HrCertificateDTO hrCertificateDTO);

    /**
     * 查询证件管理详情
     * @param id
     * @return
     */
    HrCertificateDTO getHrCertificate(String id);

    /**
     * 删除证件管理
     * @param id
     */
    void deleteHrCertificate(String id);

    /**
     * 批量删除证件管理
     * @param ids
     */
    void deleteHrCertificate(List<String> ids);

    /**
     * 分页查询证件管理
     * @param hrCertificateDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrCertificateDTO hrCertificateDTO,Long pageNumber,Long pageSize);
    }
