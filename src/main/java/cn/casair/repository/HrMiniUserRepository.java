package cn.casair.repository;

import cn.casair.domain.HrMiniUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 微信小程序用户数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-20
 */
@Repository
public interface HrMiniUserRepository extends BaseMapper<HrMiniUser> {

    /**
     * 根据openId获取小程序用户信息
     *
     * @param openId
     * @return cn.casair.domain.HrMiniUser
     * <AUTHOR>
     * @date 2021/11/21
     **/
    HrMiniUser selectByOpenId(@Param("openId") String openId);

    /**
     * 根据用户id获取用户微信信息
     *
     * @param userId
     * @return cn.casair.domain.HrMiniUser
     * <AUTHOR>
     * @date 2021/11/22
     **/
    HrMiniUser selectByUserId(String userId);
}
