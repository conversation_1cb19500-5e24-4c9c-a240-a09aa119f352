package cn.casair.service;

import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.HrTalentStaffDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

public interface HrPersonalServer extends IService<HrTalentStaff> {
    HrTalentStaff getHrProtocolAvatar();

    ResponseEntity updateHrClient(HrTalentStaff hrTalentStaff);

    /**
     * 员工修改密码
     *
     * @param hrTalentStaffdto
     * @return
     */
    void staffUpdatePassword(HrTalentStaffDTO hrTalentStaffdto);
}
