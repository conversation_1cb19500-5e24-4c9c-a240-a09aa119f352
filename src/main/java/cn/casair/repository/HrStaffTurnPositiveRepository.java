package cn.casair.repository;

import cn.casair.domain.HrAppendix;
import cn.casair.domain.HrStaffTurnPositive;
import cn.casair.dto.HrStaffTurnPositiveDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 员工转正（客户pc）数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Repository
public interface HrStaffTurnPositiveRepository extends BaseMapper<HrStaffTurnPositive> {

    IPage<HrStaffTurnPositiveDTO> selectStaff(Page<HrStaffTurnPositive> page, @Param("param1") HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO);

    List<HrStaffTurnPositiveDTO> findList(@Param("param1") HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO);

    void updateHrStaffTurnPositive(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTOs);

    HrStaffTurnPositiveDTO selectTurn(String id);

    List<HrAppendix> selectAppendixId(@Param("param1") List<String> AppendixIds);

    Integer selecttime(String staff_turn);

    String selectClientId(String clientUserId);

    String selectStaffName(String staffId);

    String selectGetClientId(String staffId);

    @Select(" SELECT hp.*,htf.client_id FROM hr_staff_turn_positive hp  LEFT JOIN hr_talent_staff htf ON hp.staff_id = htf.id ${ew.customSqlSegment}")
    List<HrStaffTurnPositive> select(@Param(Constants.WRAPPER) QueryWrapper<HrStaffTurnPositive> queryWrapper);

    void updateSalary(@Param("staffId") String staffId, @Param("salary") BigDecimal salary);
}
