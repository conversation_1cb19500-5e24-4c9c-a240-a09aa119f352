package cn.casair.repository;

import cn.casair.domain.HrContractAppendix;
import cn.casair.dto.HrContractAppendixDTO;
import cn.casair.dto.HrContractDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 员工合同-电签附件数据库操作类
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Repository
public interface HrContractAppendixRepository extends BaseMapper<HrContractAppendix> {

    /**
     * 删除合同项
     *
     * @param hrContractAppendix
     * @return
     */
    int deleteByObject(@Param("params") HrContractAppendix hrContractAppendix);

    /**
     * 获取审核未通过的合同附件
     *
     * @param contractId
     * @return java.util.List<cn.casair.domain.HrContractAppendix>
     * <AUTHOR>
     * @date 2021/11/19
     **/
    List<HrContractAppendix> getFailAppendix(@Param("contractId") String contractId);

    /**
     * 获取员工电签劳动合同
     *
     * @param staffId
     * @return cn.casair.domain.HrContractAppendix
     * <AUTHOR>
     * @date 2021/11/3
     **/
    List<HrContractAppendix> getStaffElectronicContractByStaffId(String staffId);

    /**
     * 根据档案id删除旧档案可选附件
     *
     * @param contractId
     * @return void
     * <AUTHOR>
     * @date 2021/9/30
     **/
    void deleteContractAppendix(@Param("contractId") String contractId);

    /**
     * 设置合同项为已签订
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @date 2021/9/29
     **/
    int setContractAppendixSigned(@Param("id") String id);

    /**
     * 获取合同项中合同列表
     *
     * @param contractId
     * @return java.util.List<cn.casair.domain.HrContractAppendix>
     * <AUTHOR>
     * @date 2021/9/24
     **/
    List<HrContractAppendix> getContractTemplateByContractId(@Param("contractId") String contractId);

    /**
     * 合同项 删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @date 2021/9/18
     **/
    int deleteAppendix(@Param("ids") List<String> ids);

    /**
     * 合同项确定按钮
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @date 2021/9/18
     **/
    int changeContractAppendixState(@Param("ids") List<String> ids);

    /**
     * 填充易云章合同编号到合同项
     *
     * @param id
     * @return void
     * <AUTHOR>
     * @date 2021/9/18
     **/
    void setContractAppendixContractNum(@Param("id") String id, @Param("contractNum") String contractNum);

    /**
     * 删除数据库中历史预处理数据
     *
     * @param contractId
     * @return void
     * <AUTHOR>
     * @date 2021/9/17
     **/
    int deleteTemplatePretreatment(@Param("contractId") String contractId);

    /**
     * 根据合同id获取合同项列表
     *
     * @param contractId 合同id
     * @return java.util.List<cn.casair.domain.HrContractAppendix>
     * <AUTHOR>
     * @date 2021/9/16
     **/
    List<HrContractAppendix> getContractListByContractId(@Param("contractId") String contractId);

    /**
     * 获取员工确认电签合同列表
     *
     * @param clientId 客户id
     * @param staffId  员工id
     * @return java.util.List<cn.casair.dto.HrContractAppendixDTO>
     * <AUTHOR>
     * @date 2021/9/14
     **/
    List<HrContractAppendixDTO> getConfirmationTelegramList(@Param("clientId") String clientId, @Param("staffId") String staffId);

    /**
     * 获取员工劳动合同待签约附件
     *
     * @param contractId 合同id
     * @return java.util.List<cn.casair.dto.HrContractAppendixDTO>
     * <AUTHOR>
     * @date 2021/9/15
     **/
    List<HrContractAppendixDTO> getStaffContractList(@Param("contractId") String contractId);

    /**
     * 根据员工id 客户id获取合同项列表
     *
     * @param staffId
     * @param clientId
     * @return java.util.List<cn.casair.dto.HrContractAppendixDTO>
     * <AUTHOR>
     * @date 2021/9/16
     **/
    List<HrContractAppendixDTO> getStaffContractListByObject(@Param("staffId") String staffId, @Param("clientId") String clientId, @Param("contractId")String contractId);

    /**
     * 更新模板合同
     *
     * @param hrContractAppendixDTO
     * @return int
     * <AUTHOR>
     * @date 2021/9/24
     **/
    int updateContractAppendix(@Param("param") HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 关联模板表
     *
     * @param hrContractAppendixDTO
     * @return java.util.List<cn.casair.dto.HrContractAppendixDTO>
     * <AUTHOR>
     * @date 2021/9/14
     **/
    List<HrContractAppendixDTO> getStaffContractListTemplate(@Param("params") HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 获取最新的劳动合同项
     *
     * @param contractId
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/9/30
     **/
    String getNewestLaborContract(@Param("contractId") String contractId);

    /**
     * 根据合同id获取合同相关合同项附件(含有有效附件的合同项)
     *
     * @param contractId
     * @return java.util.List<cn.casair.dto.HrContractAppendixDTO>
     * <AUTHOR>
     * @date 2021/10/8
     **/
    List<HrContractAppendixDTO> getStaffContractListByContract(@Param("contractId") String contractId);

    /**
     * 获取合同相关合同项附件(含有有效附件的合同项)
     * @param hrContractDTO
     * @param clientIds
     * @return
     */
    List<HrContractAppendixDTO> getStaffContractListBatch(@Param("params") HrContractDTO hrContractDTO, @Param("permissionClient") List<String> clientIds);

    /**
     * 更新模板合同为生效
     * @param contractId 合同ID
     * @param templateId 模板ID
     */
    void updateByContractId(@Param("contractId") String contractId, @Param("templateId") String templateId);


}
