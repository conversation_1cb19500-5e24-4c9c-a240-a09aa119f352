<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillInvoiceRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, client_id, invoice_type, apply_id, total_amount, total_amount_cn, tax_amount, apply_date, approve_status, invoice_status, accounting_voucher_status, remark, notice_roles,
            parent_id,invoice_state,invoice_lock_state,is_show,nc_voucher,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>

    <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillInvoice">
                <id column="id" property="id"/>
                <result column="client_id" property="clientId"/>
                <result column="invoice_type" property="invoiceType"/>
                <result column="apply_id" property="applyId"/>
                <result column="total_amount" property="totalAmount"/>
                <result column="total_amount_cn" property="totalAmountCn"/>
                <result column="tax_amount" property="taxAmount"/>
                <result column="apply_date" property="applyDate"/>
                <result column="approve_status" property="approveStatus"/>
                <result column="invoice_status" property="invoiceStatus"/>
                <result column="accounting_voucher_status" property="accountingVoucherStatus"/>
                <result column="remark" property="remark"/>
                <result column="notice_roles" property="noticeRoles"/>
                <result column="parent_id" property="parentId"/>
                <result column="invoice_state" property="invoiceState"/>
                <result column="invoice_lock_state" property="invoiceLockState"/>
                <result column="is_show" property="isShow"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
                <result column="nc_voucher" property="ncVoucher"/>

        </resultMap>

    <update id="updateApproveStatus">
        UPDATE hr_bill_invoice hbi LEFT JOIN hr_bill_invoice_review hbir ON hbi.id = hbir.invoice_id
        SET hbi.approve_status = #{approveStatus}, hbi.invoice_lock_state = #{invoiceLockState}
        WHERE hbi.is_delete = 0 AND hbir.fee_review_id = #{feeReviewId}
    </update>

    <update id="updateInvoiceLockState">
        UPDATE hr_bill_invoice SET invoice_lock_state = #{invoiceLockState}  WHERE id = #{id}
    </update>

    <update id="updateBeforeInvoice">
        UPDATE hr_bill_invoice SET invoice_lock_state = #{invoiceLockState}, is_show = #{isShow}  WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <select id="findPage" resultType="cn.casair.dto.HrBillInvoiceDTO">
        SELECT
            *
        FROM (
            SELECT
                hbi.id,
                ANY_VALUE( hbi.parent_id ) AS parent_id,
                ANY_VALUE( hbi.client_id ) AS client_id,
                ANY_VALUE( hbi.bill_id ) AS bill_id,
                ANY_VALUE( hbi.invoice_type ) AS invoice_type,
                ANY_VALUE( hbi.apply_id ) AS apply_id,
                ANY_VALUE( hbi.total_amount ) AS total_amount,
                ANY_VALUE( hbi.total_amount_cn ) AS total_amount_cn,
                ANY_VALUE( hbi.tax_amount ) AS tax_amount,
                ANY_VALUE( hbi.apply_date ) AS apply_date,
                ANY_VALUE( hbi.approve_status ) AS approve_status,
                ANY_VALUE( hbi.invoice_status ) AS invoice_status,
                ANY_VALUE( hbi.invoice_filed_ids ) AS invoice_filed_ids,
                ANY_VALUE( hbi.accounting_voucher_status ) AS accounting_voucher_status,
                ANY_VALUE( hbi.invoice_state ) AS invoice_state,
                ANY_VALUE( hbi.invoice_lock_state ) AS invoice_lock_state,
                ANY_VALUE( hbi.remark ) AS remark,
                ANY_VALUE( hbi.invoice_remark ) AS invoice_remark,
                ANY_VALUE( hbi.notice_roles ) AS notice_roles,
                ANY_VALUE( hbi.is_default ) AS is_default,
                ANY_VALUE( hbi.is_delete ) AS is_delete,
                ANY_VALUE( hbi.is_show ) AS is_show,
                ANY_VALUE( hbi.created_by ) AS created_by,
                ANY_VALUE( hbi.created_date ) AS created_date,
                ANY_VALUE( hbi.last_modified_by ) AS last_modified_by,
                ANY_VALUE( hbi.last_modified_date ) AS last_modified_date,
                ANY_VALUE( u.real_name ) AS apply_name,
                ANY_VALUE( hc.`client_name` ) AS client_name,
                ANY_VALUE( har.total_arrival_amount ) AS total_arrival_amount,
                GROUP_CONCAT(DISTINCT hbir.payment_date) payment_date
            FROM
                `hr_bill_invoice` hbi
            LEFT JOIN sys_user u ON hbi.`apply_id` = u.`id` AND u.`is_delete` = 0
            LEFT JOIN hr_client hc ON hc.`id` = hbi.`client_id` AND hc.`is_delete` = 0
            LEFT JOIN hr_arrival_record har ON har.bill_invoice_id = hbi.id AND hbi.is_delete = 0
            LEFT JOIN hr_bill_invoice_record hbir ON hbi.id = hbir.invoice_id AND hbir.is_delete = 0
            WHERE hbi.`is_delete` = 0 AND hbi.is_show = 0
            <if test="clientIds != null and clientIds.size() > 0">
                and hbi.client_id in
                <foreach collection="clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.curUserStartStatus != null">
                and hbi.approve_status >= #{params.curUserStartStatus}
            </if>
            <if test="params.curUserId != null and params.curUserId != ''">
                or hbi.apply_id = #{params.curUserId}
            </if>
            GROUP BY hbi.id
        ) AS hbi
        WHERE hbi.is_delete = 0 AND hbi.is_show = 0
        <choose>
            <when test="params.invoiceState == 1">
                and hbi.invoice_state = #{params.invoiceState} AND hbi.approve_status > 0
            </when>
            <otherwise>
                and (hbi.invoice_state = #{params.invoiceState} OR hbi.approve_status = 0)
            </otherwise>
        </choose>
        <if test="params.clientId != null and params.clientId != ''">
            and hbi.client_id = #{params.clientId}
        </if>
        <if test="params.applyName != null and params.applyName != ''">
            and hbi.apply_name like concat ('%', #{params.applyName}, '%')
        </if>
        <if test="params.applyDate != null">
            and hbi.apply_date = #{params.applyDate}
        </if>
        <if test="params.approveStatus != null">
            and hbi.approve_status = #{params.approveStatus}
        </if>
        <if test="params.approveStatusList != null and params.approveStatusList.size() > 0">
            and hbi.approve_status in
            <foreach collection="params.approveStatusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="params.invoiceStatus != null">
            and hbi.invoice_status = #{params.invoiceStatus}
        </if>
        <if test="params.accountingVoucherStatus != null">
            and hbi.accounting_voucher_status = #{params.accountingVoucherStatus}
        </if>
        <if test="params.totalAmountQuery != null and params.totalAmountQuery.size() > 0">
            AND hbi.total_amount BETWEEN #{params.totalAmountStart} AND #{params.totalAmountEnd}
        </if>
        <if test="params.taxAmountQuery != null and params.taxAmountQuery.size() > 0">
            AND hbi.tax_amount BETWEEN #{params.taxAmountStart} AND #{params.taxAmountEnd}
        </if>
        <if test="params.paymentDate!=null">
            AND FIND_IN_SET(#{params.paymentDate}, payment_date)
        </if>
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY
                        hbi.pay_year ${params.order},
                        hbi.pay_monthly ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY
                        ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY
                hbi.created_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="getHrBillInvoice" resultType="cn.casair.dto.HrBillInvoiceDTO">
        SELECT
            hbi.*,
            u.`real_name` applyName,
            hc.`client_name`,
            har.total_arrival_amount
        FROM
            `hr_bill_invoice` hbi
        LEFT JOIN sys_user u ON hbi.`apply_id` = u.`id` AND u.`is_delete` = 0
        LEFT JOIN hr_client hc ON hc.`id` = hbi.`client_id` AND hc.`is_delete` = 0
        LEFT JOIN hr_arrival_record har ON har.bill_invoice_id = hbi.id AND hbi.is_delete = 0
        WHERE hbi.id = #{id}
    </select>

    <select id="getBillInvoiceByIds" resultType="cn.casair.dto.HrBillInvoiceDTO">
        SELECT
            hbi.*,
            u.`real_name` applyName,
            hc.`client_name`
        FROM
            `hr_bill_invoice` hbi
            LEFT JOIN sys_user u ON hbi.`apply_id` = u.`id`
            AND u.`is_delete` = 0
            LEFT JOIN hr_client hc ON hc.`id` = hbi.`client_id`
            AND hc.`is_delete` = 0
        WHERE hbi.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

    <select id="selectInfoList" resultType="cn.casair.dto.HrBillInvoiceDTO">
        SELECT
            hbi.*
        FROM
            hr_bill_invoice hbi
        WHERE hbi.is_delete = 0
        <if test="parentId != null and parentId != ''">
            AND hbi.parent_id = #{parentId}
        </if>
        <if test="neApproveStatusList != null and neApproveStatusList.size() > 0">
            AND hbi.approve_status NOT IN
            <foreach collection="neApproveStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
    </select>

    <select id="findByBillId" resultType="cn.casair.dto.HrBillInvoiceDTO">
        SELECT
            hbi.*
        FROM
            hr_bill_invoice_record hbir
        LEFT JOIN hr_bill_invoice hbi ON hbir.invoice_id = hbi.id
        WHERE hbir.is_delete = 0  AND hbi.invoice_state = 1
        AND hbi.approve_status = #{approveStatus}
        AND hbir.content = #{content}
        AND hbi.bill_id IN
        <foreach collection="feeReviewIds" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

    <select id="findByReviewId" resultType="cn.casair.dto.HrBillInvoiceDTO">
        SELECT
            hbir.invoice_id,
            hbir.fee_review_id,
            hbi.*
        FROM
            hr_bill_invoice_review hbir
        LEFT JOIN hr_bill_invoice hbi ON hbi.id = hbir.invoice_id
        WHERE hbir.is_delete = 0 AND hbi.is_delete = 0 AND hbi.is_show = 0 AND hbir.fee_review_id IN
        <foreach collection="feeReviewIds" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
        <if test="isDefault != null">
            AND hbi.is_default = #{isDefault}
         </if>
    </select>

</mapper>
