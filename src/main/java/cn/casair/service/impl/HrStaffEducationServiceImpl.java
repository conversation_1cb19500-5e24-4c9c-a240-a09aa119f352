package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.DateUtils;
import cn.casair.domain.HrStaffEducation;
import cn.casair.dto.HrStaffEducationDTO;
import cn.casair.mapper.HrStaffEducationMapper;
import cn.casair.repository.HrStaffEducationRepository;
import cn.casair.service.CodeTableService;
import cn.casair.service.HrStaffEducationService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
/**
 * 教育经历服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrStaffEducationServiceImpl extends ServiceImpl<HrStaffEducationRepository, HrStaffEducation>implements HrStaffEducationService {

    private static final Logger log=LoggerFactory.getLogger(HrStaffEducationServiceImpl.class);

    private final HrStaffEducationRepository hrStaffEducationRepository;

    private final HrStaffEducationMapper hrStaffEducationMapper;

    private final CodeTableService codeTableService;

    private final SysOperLogService sysOperLogService;

    public HrStaffEducationServiceImpl(HrStaffEducationRepository hrStaffEducationRepository, HrStaffEducationMapper hrStaffEducationMapper, CodeTableService codeTableService, SysOperLogService sysOperLogService){
        this.hrStaffEducationRepository = hrStaffEducationRepository;
        this.hrStaffEducationMapper= hrStaffEducationMapper;
        this.codeTableService= codeTableService;
        this.sysOperLogService = sysOperLogService;
    }

    /**
     * 创建教育经历
     * @param hrStaffEducationDTO
     * @return
     */
    @Override
    public List<HrStaffEducationDTO> createHrStaffEducation(HrStaffEducationDTO hrStaffEducationDTO){
        log.info("Create new HrStaffEducation:{}", hrStaffEducationDTO);
        this.dateRangeVerification(hrStaffEducationDTO);
        HrStaffEducation hrStaffEducation =this.hrStaffEducationMapper.toEntity(hrStaffEducationDTO);
        this.hrStaffEducationRepository.insert(hrStaffEducation);
        // 操作日志
        setDict(hrStaffEducationDTO);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_EDUCATION.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffEducationDTO),
            HrStaffEducationDTO.class,
            null,
            JSON.toJSONString(hrStaffEducation)
        );
        return this.findEducationList(hrStaffEducationDTO.getStaffId());
    }

    private void dateRangeVerification(HrStaffEducationDTO hrStaffEducationDTO) {
        List<HrStaffEducation> hrStaffEducationList = hrStaffEducationRepository.selectList(new QueryWrapper<HrStaffEducation>().eq("staff_id", hrStaffEducationDTO.getStaffId()));
        if (CollectionUtils.isNotEmpty(hrStaffEducationList)){
            for (HrStaffEducation hrStaffEducation : hrStaffEducationList) {
                if (DateUtils.isWithin(hrStaffEducation.getEducationStartDate(),hrStaffEducation.getEducationEndDate(),hrStaffEducationDTO.getEducationStartDate(),hrStaffEducationDTO.getEducationEndDate())){
                    throw new CommonException("同一时间段内应该只有一条教育经历");
                }
            }
        }
    }

    /**
     * 修改教育经历
     * @param hrStaffEducationDTO
     * @return
     */
    @Override
    public Optional<List<HrStaffEducationDTO>>updateHrStaffEducation(HrStaffEducationDTO hrStaffEducationDTO){
        return Optional.ofNullable(this.hrStaffEducationRepository.selectById(hrStaffEducationDTO.getId()))
        .map(roleTemp->{
            HrStaffEducation hrStaffEducation =this.hrStaffEducationMapper.toEntity(hrStaffEducationDTO);
            this.hrStaffEducationRepository.updateById(hrStaffEducation);
            log.info("Update HrStaffEducation:{}", hrStaffEducationDTO);
            // 操作日志
            setDict(hrStaffEducationDTO);
            HrStaffEducationDTO educationDTO = hrStaffEducationMapper.toDto(roleTemp);
            setDict(educationDTO);
            this.sysOperLogService.insertSysOperLog(
                ModuleTypeEnum.STAFF_EDUCATION.getValue(),
                BusinessTypeEnum.UPDATE.getKey(),
                JSON.toJSONString(hrStaffEducationDTO),
                HrStaffEducationDTO.class,
                null,
                JSON.toJSONString(educationDTO),
                JSON.toJSONString(hrStaffEducationDTO),
                null,
                HrStaffEducationDTO.class
            );
            return this.findEducationList(hrStaffEducationDTO.getStaffId());
        });
    }

    /**
     * 查询全部教育经历
     * @param staffId 员工ID
     * @return
     */
    @Override
    public List<HrStaffEducationDTO> findEducationList(String staffId) {
        List<HrStaffEducationDTO> dtoList = hrStaffEducationMapper.toDto(
            hrStaffEducationRepository.selectList(new QueryWrapper<HrStaffEducation>().eq("staff_id", staffId).eq("is_delete", 0)));
        if (CollectionUtils.isNotEmpty(dtoList)){
            dtoList.forEach(this::setDict);
        }
        //根据员工ID查询全部教育经历
        return dtoList;
    }

    /**
     * 查询教育经历详情
     * @param id
     * @return
     */
    @Override
    public HrStaffEducationDTO getHrStaffEducation(String id){
        log.info("Get HrStaffEducation :{}",id);

        HrStaffEducation hrStaffEducation =this.hrStaffEducationRepository.selectById(id);
        HrStaffEducationDTO staffEducationDTO = this.hrStaffEducationMapper.toDto(hrStaffEducation);
        //赋值字典值
        this.setDict(staffEducationDTO);
        return staffEducationDTO;
    }

    /**
     * 赋值字典值
     * @param staffEducationDTO
     */
    public void setDict(HrStaffEducationDTO staffEducationDTO) {
        if (staffEducationDTO.getHighestDegree()!=null){
            Map<Integer, String> educationStates = codeTableService.findCodeTableByInnerName("educationStates");//最高学历
            staffEducationDTO.setHighestDegreeLabel(educationStates.get(staffEducationDTO.getHighestDegree()));
        }
        if (staffEducationDTO.getDegree()!=null){
            Map<Integer, String> degree = codeTableService.findCodeTableByInnerName("degree");//学位
            staffEducationDTO.setDegreeLabel(degree.get(staffEducationDTO.getDegree()));
        }
        if (staffEducationDTO.getStudyModality()!=null){
            Map<Integer, String> studyModality = codeTableService.findCodeTableByInnerName("studyModality");//学习形式
            staffEducationDTO.setStudyModalityLabel(studyModality.get(staffEducationDTO.getStudyModality()));
        }
    }

    /**
     * 删除教育经历
     * @param id
     */
    @Override
    public List<HrStaffEducationDTO> deleteHrStaffEducation(String id){
        HrStaffEducation hrStaffEducation = this.hrStaffEducationRepository.selectById(id);
        this.hrStaffEducationRepository.deleteById(id);
        log.info("Delete HrStaffEducation:{}", hrStaffEducation);
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.STAFF_EDUCATION.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除" + ModuleTypeEnum.STAFF_EDUCATION.getValue()+": "+ hrStaffEducation.getEducation(),
            null,
            null,
            null,
            JSON.toJSONString(hrStaffEducation),
            null
        );
        return this.findEducationList(hrStaffEducation.getStaffId());
    }
}
