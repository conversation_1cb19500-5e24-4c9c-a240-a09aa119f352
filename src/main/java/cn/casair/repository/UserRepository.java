package cn.casair.repository;

import cn.casair.domain.User;
import cn.casair.dto.UserDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据库操作类
 *
 * <AUTHOR>
 * @since 2020-05-08
 */
@Repository
public interface UserRepository extends BaseMapper<User> {

    /**
     * 根据手机号获取有效用户信息
     *
     * @param phone
     * @return cn.casair.dto.UserDTO
     * <AUTHOR>
     * @date 2021/11/22
     **/
    UserDTO selectByPhone(@Param("phone") String phone);

    /**
     * 根据openId获取用户
     *
     * @param openId
     * @return cn.casair.dto.UserDTO
     * <AUTHOR>
     * @date 2021/11/20
     **/
    UserDTO selectUserByOpenId(@Param("openId") String openId);

    @Select("select * from sys_user u where u.user_name = #{loginName} and u.is_delete = 0")
    User getByLoginName(String loginName);

    /**
     * 查询所有赛区负责人包含角色
     *
     * @param
     * @return java.util.List<cn.casair.dto.UserDTO>
     * <AUTHOR> yanglei 2020/5/30 14:41
     */
    List<UserDTO> getUserWithRole();

    /**
     * 分页查询(带角色的)用户列表
     *
     * @param page
     * @param userDTO
     * @param roleKeys
     * <AUTHOR> Lyric.Lin 2020/6/12 12:28
     */
    IPage<UserDTO> queryUserWithRoles(Page page, @Param("param") UserDTO userDTO, @Param("roleKeys") List<String> roleKeys);

    List<UserDTO> getByRoles(@Param("roleKeyList") List<String> roleKeyList);

    /**
     * 根据手机号查询是否有存在的用户
     *
     * @param phone
     * @return
     */
    @Select("SELECT * FROM sys_user WHERE phone = #{phone}  AND is_delete = 0")
    List<User> selectForPhone(@Param("phone") String phone);


    List<UserDTO> getSpecialized();

    /**
     * 获取用户的roleKey
     *
     * @param userId
     * @return
     */
    UserDTO getUserInFor(@Param("userId") String userId);

    @Select("SELECT c.*  FROM hr_talent_staff a LEFT JOIN hr_client b ON a.client_id = b.id and b.is_delete=0 LEFT JOIN sys_user c ON b.specialized_id = c.id and c.is_delete=0 and c.user_status=1 where a.id=#{id}")
    User getAuditorInfo(@Param("id") String id);

    /**
     * 修改用户状态为启用
     * @param userId 用户ID
     */
    void updateStatus(String userId);

    /**
     * 批量修改用户状态为启用
     * @param userIds
     */
    void updateStatusBatch(@Param("userIds") List<String> userIds);

}
