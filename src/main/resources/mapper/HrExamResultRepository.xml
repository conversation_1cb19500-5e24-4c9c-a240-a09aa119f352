<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrExamResultRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , paper_id, client_id, students_name, card, phone, exam_date, score, appendix_url, evaluation,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrExamResult">
        <id column="id" property="id"/>
        <result column="paper_id" property="paperId"/>
        <result column="client_id" property="clientId"/>
        <result column="students_name" property="studentsName"/>
        <result column="card" property="card"/>
        <result column="phone" property="phone"/>
        <result column="exam_date" property="examDate"/>
        <result column="score" property="score"/>
        <result column="appendix_url" property="appendixUrl"/>
        <result column="evaluation" property="evaluation"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getStaffEntranceExaminationState" resultType="java.lang.Integer">
        SELECT count(her.id)
        FROM hr_exam_result her
                 LEFT JOIN hr_paper_management hpm ON her.paper_id = hpm.id
        WHERE her.is_delete = 0
          AND hpm.is_delete = 0
          AND hpm.is_preset = 1
          AND her.staff_id = #{staffId}
          AND her.client_id = #{clientId}
    </select>

    <select id="examResultDetail" resultType="cn.casair.dto.HrQuestionDTO">
        SELECT hpq.id,
               hq.title,
               hq.question_type,
               hq.question_pro,
               hq.score,
               he.student_answer,
               hq.correct,
               hq.answer_analysis,
               hq.question_cont
        FROM hr_paper_question hpq
                 LEFT JOIN hr_exam_result_details he ON he.question_id = hpq.question_id
                 LEFT JOIN hr_question hq ON hpq.question_id = hq.id
        WHERE hpq.paper_id = #{param.paperId}
          and he.exam_result_id = #{param.examResultId}
        ORDER BY hpq.ordersum ASC
    </select>

    <select id="exportExamResults" resultType="cn.casair.dto.excel.HrExamResultTemplate">
        select * from hr_exam_result where id in
        <foreach collection="ids" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>
    <select id="selectWrittenPassLine" resultType="cn.casair.domain.HrExamResult">
        SELECT
        *
        FROM
        hr_exam_result
        WHERE
        is_delete=0
        and exam_name=#{brochureName} and profession_name=#{stationName}

    </select>


    <update id="interviewUpdateType">
        UPDATE hr_registration_details set status=9 where is_delete=0
        and staff_id in
        <foreach collection="staffIdList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
        and station_name=#{stationName} and brochure_id=#{brochureId}
    </update>
    <update id="noInterviewUpdateType">
        UPDATE hr_registration_details set status=#{type} where is_delete=0 and station_name = #{stationName}
        and brochure_id = #{id}
        and staff_id  =#{noStaffIdList}


    </update>
    <update id="noWrittenUpdateType">
        UPDATE hr_registration_details set status=8 where is_delete=0 and station_name = #{stationName}
        and brochure_id = #{id}
        and staff_id in
        <foreach collection="noStaffIdList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>

    </update>
    <update id="writtenwUpdateType">
        UPDATE hr_registration_details
        set status=9
        where is_delete = 0
          and staff_id = #{staffId}
          and station_name = #{professionName}
          and brochure_id = #{brochureId}
    </update>
    <update id="updatetype">
        UPDATE hr_registration_details
        set status=#{type}
        where is_delete = 0
          and staff_id = #{staffId}
    </update>
    <update id="updateExamResult">
        UPDATE hr_registration_details
        set status=#{type}
        where is_delete = 0
          and staff_id = #{staffId}
          and station_name = #{stationName}
          and brochure_id = #{brochureId}
    </update>

    <update id="writtenwUpdateTypes">
        UPDATE hr_registration_details set status=17 where is_delete=0
        and staff_id in
        <foreach collection="writtenID" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </update>

    <update id="interviewUpdateTypes">
        UPDATE hr_registration_details set status=9 where is_delete=0
        and staff_id in
        <foreach collection="staffIdList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </update>

    <update id="updateHrExamResult">
        UPDATE hr_exam_result
        set final_result =#{param1.finalResult}
        where id = #{param1.id}
          and is_delete = 0
    </update>
    <update id="writtenwUpdateTypeS">
        UPDATE hr_registration_details
        set status=17
        where is_delete = 0
          and staff_id = #{staffId}
          and station_name = #{professionName}
          and brochure_id = #{brochureId}
    </update>
    <update id="interviewUpdateTypeS">
        UPDATE hr_registration_details set status=6 where is_delete=0
        and staff_id in
        <foreach collection="staffIdList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
        and station_name=#{stationName} and brochure_id=#{brochureId}
    </update>
    <update id="updateExamResulter">
        UPDATE hr_registration_details
        set status=#{type}
        where is_delete = 0
          and staff_id = #{staffId}
          and station_name = #{stationName}
          and brochure_id = #{id}
    </update>
    <update id="updateExamResults">
        UPDATE hr_registration_details
        set status=#{type}
        where is_delete = 0
          and staff_id = #{sId}
          and station_name = #{stationName}
          and brochure_id = #{bid}
    </update>

    <select id="selectLists" resultType="java.lang.String">
        SELECT staff_id
        from hr_registration_details
        where status = 9
          and station_name = #{stationName}
          and brochure_id = #{brochureId}
          and is_delete = 0
    </select>
    <select id="selectWrittenPassLines" resultType="cn.casair.domain.HrExamResult">
        SELECT *
        FROM hr_exam_result
        WHERE is_delete = 0
          and exam_name = #{brochureName}
          and profession_name = #{stationName}
        ORDER BY final_result DESC
            LIMIT #{numberSum}
    </select>

    <select id="selectWrittenPassLiness" resultMap="BaseResultMap">
        SELECT *
        FROM hr_exam_result
        WHERE is_delete = 0
          and exam_name = #{brochureName}
          and profession_name = #{stationName}
        ORDER BY score DESC
            LIMIT #{numberSum}
    </select>

    <select id="selectWrittenPassLinesss" resultMap="BaseResultMap">
        SELECT *
        FROM hr_exam_result
        WHERE is_delete = 0
          and exam_name = #{brochureName}
          and profession_name = #{stationName}
        ORDER BY interview_score_result DESC
            LIMIT #{numberSum}
    </select>
</mapper>
