<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrPaperManagementRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , paper_name, usage_sum, test_duration, time_limit, pass_line, full_marks, preview,is_delete,is_preset,created_by,created_date,last_modified_by,last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrPaperManagement">
        <id column="id" property="id"/>
        <result column="paper_name" property="paperName"/>
        <result column="usage_sum" property="usageSum"/>
        <result column="test_duration" property="testDuration"/>
        <result column="time_limit" property="timeLimit"/>
        <result column="pass_line" property="passLine"/>
        <result column="full_marks" property="fullMarks"/>
        <result column="preview" property="preview"/>
        <result column="is_delete" property="isDelete"/>

        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="getHrPaperManagementClient" resultType="cn.casair.dto.HrPaperManagementDTO">
        SELECT any_value(hc.id)          clientId,
               any_value(hc.client_name) clientName
        FROM hr_paper_client hpc
                 LEFT JOIN hr_client hc on hc.id = hpc.client_id
        WHERE hpc.is_delete = 0
        GROUP BY hpc.client_id
    </select>

    <select id="getHrPaperManagementStation" resultType="cn.casair.dto.HrPaperManagementDTO">
        SELECT any_value(hs.id)                 stationId,
               any_value(hs.profession_name) as stationName
        FROM hr_paper_station hps
                 LEFT JOIN hr_station hs on hs.id = hps.station_id
        WHERE hps.is_delete = 0
        GROUP BY hps.station_id
    </select>

    <sql id="Find_SQL">
        SELECT
        *
        FROM
        (
        SELECT
        a.id id,
        a.clientId clientId,
        group_concat( hps.station_id SEPARATOR ',' ) stationId,
        a.usageSum usageSum,
        a.passLine passLine,
        a.paperName paperName,
        a.preview,
        a.isPreset isPreset,
        a.paperStatus paperStatus,
        a.createdDate createdDate
        FROM
        (
        SELECT
        any_value ( hp.id ) id,
        group_concat( hpc.client_id SEPARATOR ',' ) clientId,
        any_value ( hp.created_date ) createdDate,
        any_value ( hp.usage_sum ) usageSum,
        any_value ( hp.pass_line ) passLine,
        any_value ( hp.paper_name ) paperName,
        any_value (hp.preview) preview,
        any_value ( hp.is_preset ) isPreset,
        any_value ( hp.paper_status ) paperStatus
        FROM
        hr_paper_management hp
        LEFT JOIN hr_paper_client hpc ON hpc.paper_id = hp.id
        WHERE
        hp.is_delete = 0
        AND hpc.is_delete = 0
        <if test="param1.contractStartDateStart!=null  ">
            AND hp.created_date <![CDATA[ >= ]]> #{param1.contractStartDateStart}
        </if>
        <if test="param1.contractStartDateEnd!=null ">
            AND hp.created_date <![CDATA[ <=]]> #{param1.contractStartDateEnd}
        </if>

        <if test="param1.clientIdList!=null and param1.clientIdList.size() > 0">
            AND hpc.client_id IN
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.paperStatusList!=null and param1.paperStatusList.size() > 0">
            AND hp.paper_status IN
            <foreach collection="param1.paperStatusList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        GROUP BY
        id
        ) a
        LEFT JOIN hr_paper_station hps ON hps.paper_id = a.id
        GROUP BY
        id
        ) b
        <where>
            <if test="param1.stationIdList!=null and param1.stationIdList.size() > 0">
                and
                <foreach collection="param1.stationIdList" open="(" separator="or" close=")" item="val">
                    stationId like concat('%',#{val},'%')
                </foreach>
            </if>
            <if test="param1.ids!=null and param1.ids.size() > 0">
                and id in
                <foreach collection="param1.ids" open="(" separator="," close=")" item="val">
                    #{val}
                </foreach>
            </if>
            <if test="param1.paperName!=null and param1.paperName!=''">
                and paperName like concat('%',#{param1.paperName},'%')
            </if>
        </where>
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order} ,  isPreset desc
        </if>
        <if test="param1.field == null  and param1.order == null ">
            order by  isPreset desc
        </if>
    </sql>

    <select id="pageselect" resultType="cn.casair.dto.HrPaperManagementDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="getHrPaperManagementIndexClient" resultType="cn.casair.dto.HrPaperManagementDTO">
        SELECT id as clientId, client_name as clientName
        from hr_client
        where is_delete = 0
    </select>

    <select id="getHrPaperManagementIndexStation" resultType="cn.casair.dto.HrPaperManagementDTO">
        SELECT id as stationId, profession_name as stationName
        from hr_station
        where is_delete = 0
    </select>
    <select id="selectQuestionPage" resultType="cn.casair.dto.HrQuestionDTO">
        SELECT
        hq.*,
        any_value(hqs.station_id)stationId
        FROM hr_question hq
        LEFT JOIN hr_question_station hqs on hq.id = hqs.question_id
        LEFT JOIN hr_station hs on hqs.station_id = hs.id
        where
          hqs.is_delete = 0
        and hs.is_delete = 0
        and hq.is_delete = 0
        and  hqs.station_id IN
        <foreach collection="param1.stationIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>

        <if test="param1.title!=null and param1.title!=''">
            and hq.title like concat('%',#{param1.title},'%')
        </if>

        <if test="param1.questionTypeList==null  ">
            and  hq.question_type !=7

        </if>

        <if test="param1.questionTypeList!=null and param1.questionTypeList.size() > 0">
            and  hq.question_type in
            <foreach collection="param1.questionTypeList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test="param1.questionProList!=null and param1.questionProList.size() > 0">
            and    hq.question_pro in
            <foreach collection="param1.questionProList" open="(" separator="," close=")" item="val">
               #{val}
            </foreach>
        </if>

        <if test="param1.scoreList!=null and param1.scoreList.size() > 0">
            and hq.score in
            <foreach collection="param1.scoreList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY hq.id
        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
    </select>
    <select id="getCopyHrPaperManagement" resultType="cn.casair.dto.HrPaperManagementDTO">
        SELECT any_value(hpm.paper_name)                  paperName,
               any_value(hpm.usage_sum)                   usageSum,
               any_value(hpm.test_duration)               testDuration,
               any_value(hpm.time_limit)                  timeLimit,
               any_value(hpm.pass_line)                   passLine,
               any_value(hpm.full_marks)                  fullMarks,
               any_value(hpm.preview)                     preview,
               group_concat(hc.client_id SEPARATOR ',')   clientId,
               group_concat(hq.question_id SEPARATOR ',') questionId,
               group_concat(hs.station_id SEPARATOR ',')  stationId
        FROM hr_paper_management hpm
                 LEFT JOIN hr_paper_client hc ON hpm.id = hc.paper_id
                 LEFT JOIN hr_paper_station hs ON hpm.id = hs.paper_id
                 LEFT JOIN hr_paper_question hq ON hpm.id = hq.paper_id
        WHERE hpm.is_delete = 0
          AND hpm.id = #{id}
        GROUP BY hpm.id
    </select>
    <select id="getHrPaperManagementIndexScore" resultType="java.lang.String">
        SELECT any_value(question_id)question_id
        from hr_question_station
        where is_delete = 0

        <if test="param1.stationIdList!=null and param1.stationIdList.size() > 0">
            and station_id in
            <foreach collection="param1.stationIdList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY question_id
    </select>
    <select id="getHrPaperManagementScore" resultType="cn.casair.dto.HrQuestionDTO">
        SELECT any_value(score) score
        from hr_question
        where is_delete = 0
        <if test="questionId!=null and  questionId.size() > 0">
            and id in
            <foreach collection="questionId" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>


        GROUP BY score   ORDER BY score
    </select>
    <select id="getClientName" resultType="java.lang.String">
        SELECT  client_name
        from hr_client
        where id = #{clientId}
          and is_delete = 0
    </select>
    <select id="getStationName" resultType="java.lang.String">
        SELECT group_concat(profession_name SEPARATOR ',')
        from hr_station
        where id = #{stationId}
          and is_delete = 0
    </select>
    <select id="findPageRandom" resultType="cn.casair.dto.HrQuestionDTO">
        SELECT *
        from hr_question
        where is_delete = 0
        <if test=" param1.questionTypeList!=null and  param1.questionTypeList.size() > 0">
            and question_type in
            <foreach collection="param1.questionTypeList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>

        <if test=" param1.scoreList!=null and  param1.scoreList.size() > 0">
            and score in
            <foreach collection="param1.scoreList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>
    <select id="getHrQuestionDTOId" resultType="cn.casair.dto.HrQuestionDTO">
        SELECT *
        from hr_question
        where is_delete = 0
        <if test=" list!=null and  list.size() > 0">
            and id in
            <foreach collection="list" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
    </select>

    <select id="getSelectQuestion" resultType="cn.casair.dto.HrPaperQuestionDTO">
        SELECT *
        from hr_paper_question
        where question_id = #{id}
          and is_delete = 0
    </select>

    <select id="getQuestionId" resultType="java.lang.String">
        SELECT
        hq. id
        FROM
        hr_question hq
        LEFT JOIN hr_question_station hs on hq.id=hs.question_id
        WHERE
        hq.score = #{param1.score}


        <if test=" param1.applicablePostList!=null and  param1.applicablePostList.size() > 0">
            and hs.station_id in
            <foreach collection="param1.applicablePostList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test=" param1.questionType!=null  ">
            AND hq.question_type = #{param1.questionType}
        </if>
        <if test=" param1.questionPro!=null and  param1.questionPro !=''">
            AND hq.question_pro = #{param1.questionPro}
        </if>
        AND hq.is_delete = 0
        GROUP BY 	hq.id
        ORDER BY hq.question_type DESC
        LIMIT #{param1.scoreSum}
    </select>

    <select id="selectmanId" resultType="cn.casair.dto.HrPaperManagementDTO">
        SELECT *
        FROM (
                 SELECT a.id                                       id,
                        a.clientId                                 clientId,
                        group_concat(hps.station_id SEPARATOR ',') stationId,
                        a.usageSum                                 usageSum,
                        a.passLine                                 passLine,
                        a.paperName                                paperName,
                        a.createdDate                              createdDate,
                        a.preview                                  preview,
                        a.testDuration                             testDuration,
                        a.timeLimit                                timeLimit,
                        a.isPreset                                 isPreset,
                        a.paperStatus                              paperStatus
                 FROM (
                          SELECT any_value(hp.id)                          id,
                                 group_concat(hpc.client_id SEPARATOR ',') clientId,
                                 any_value(hp.created_date)                createdDate,
                                 any_value(hp.usage_sum)                   usageSum,
                                 any_value(hp.pass_line)                   passLine,
                                 any_value(hp.paper_name)                  paperName,
                                 any_value(hp.preview)                     preview,
                                 any_value(hp.test_duration)               testDuration,
                                 any_value(hp.time_limit)                  timeLimit,
                                 any_value(hp.is_preset)                   isPreset,
                                 any_value(hp.paper_status)                paperStatus
                          FROM hr_paper_management hp
                                   LEFT JOIN hr_paper_client hpc ON hpc.paper_id = hp.id

                          WHERE hp.is_delete = 0
                            AND hpc.is_delete = 0
                            AND hp.id = #{id}
                          GROUP BY id
                          ) a
                          LEFT JOIN hr_paper_station hps ON hps.paper_id = a.id
                 GROUP BY id
                 ) b
    </select>

    <select id="selectHrQuestion" resultType="cn.casair.dto.HrQuestionDTO">
        SELECT hq.*
        FROM hr_paper_question hpq
                 LEFT JOIN hr_question hq on hq.id = hpq.question_id
        where hpq.paper_id = #{id}
          and hq.is_delete = 0
          and hpq.is_delete = 0
        ORDER BY hpq.ordersum
    </select>
    <select id="selectQuestionssSum" resultType="cn.casair.dto.HrQuestionDTO">
        SELECT
        hq.id
        FROM
            hr_question hq
                LEFT JOIN hr_question_station hqs ON hq.id = hqs.question_id
                AND hq.is_delete = 0
        WHERE
            hqs.is_delete = 0
        <if test=" param1. applicablePostList!=null and  param1. applicablePostList.size() > 0">
            and hqs.station_id in
            <foreach collection="param1. applicablePostList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
            and hq.question_type=#{param1.questionType}
        and hq.score=#{param1.score}
        GROUP BY 	hq.id
    </select>

    <select id="selectClientid" resultType="java.lang.String">
        SELECT client_id from hr_paper_client where paper_id=#{id} and is_delete=0
    </select>

    <select id="findList" resultType="cn.casair.dto.HrPaperManagementDTO">
        <include refid="Find_SQL"/>
    </select>
</mapper>
