# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

#management:
#  endpoints:
#    web:
#      base-path: /actuator
#      exposure:
#        include: ['configprops', 'env', 'health', 'info', 'jhimetrics', 'logfile', 'loggers', 'prometheus', 'threaddump']
#  endpoint:
#    health:
#      show-details: when-authorized
#    jhimetrics:
#      enabled: true
#  info:
#    git:
#      mode: full
#  health:
#    mail:
#      enabled: false # When using the MailService, configure an SMTP server and set this to true
#  metrics:
#    export:
#      # Prometheus is the default metrics backend
#      prometheus:
#        enabled: true
#        step: 60
#    enable:
#      http: true
#      jvm: true
#      logback: true
#      process: true
#      system: true
#    distribution:
#      percentiles-histogram:
#        all: true
#      percentiles:
#        all: 0, 0.5, 0.75, 0.95, 0.99, 1.0
#    tags:
#      application: ${spring.application.name}
#    web:
#      server:
#        auto-time-requests: true

spring:
  application:
    name: hr-server
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the JAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: dev
  jmx:
    enabled: false

  messages:
    basename: i18n/messages
  main:
    allow-bean-definition-overriding: true
  mvc:
    favicon:
      enabled: false
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${file.imagePath},file:${file.tempPath}

  task:
    execution:
      thread-name-prefix: casairwebbase-task-
      pool:
        core-size: 2
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: casairwebbase-scheduling-
      pool:
        size: 2
  thymeleaf:
    mode: HTML
  servlet:
    multipart:
      max-file-size: 100MB
  flyway:
    # flyway 的 clean 命令会删除指定 schema 下的所有 table, 杀伤力太大了, 应该禁掉.
    clean-disabled: true
    #启用flyway
    enabled: true
    # 设定 SQL 脚本的目录,多个路径使用逗号分隔, 比如取值为 classpath:db/migration,filesystem:/sql-migrations
    locations: classpath:db/migration
    # 如果指定 schema 包含了其他表,但没有 flyway schema history 表的话, 在执行 flyway migrate 命令之前, 必须先执行 flyway baseline 命令.
    # 设置 spring.flyway.baseline-on-migrate 为 true 后, flyway 将在需要 baseline 的时候, 自动执行一次 baseline.
    baseline-on-migrate: true
    encoding: UTF-8

server:
  tomcat:
    uri-encoding: utf-8
  servlet:
    session:
      cookie:
        http-only: true

# Properties to be exposed on the /info management endpoint
info:
  # Comma separated list of profiles that will trigger the ribbon to show
  display-ribbon-on-profiles: 'dev'

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  clientApp:
    name: 'hr-service-platform'
  # By default CORS is disabled. Uncomment to enable.
  # cors:
  #     allowed-origins: "*"
  #     allowed-methods: "*"
  #     allowed-headers: "*"
  #     exposed-headers: "Authorization,Link,X-Total-Count"
  #     allow-credentials: true
  #     max-age: 1800
  mail:
    from: casairwebbase@localhost
  swagger:
    default-include-pattern: /api/.*
    title: casairwebbase API
    description: casairwebbase API documentation
    version: 0.0.1
    terms-of-service-url:
    contact-name:
    contact-url:
    contact-email:
    license:
    license-url:
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
mybatis-plus:
    mapper-locations: classpath*:mapper/*.xml
    configuration:
        #配置sql打印
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
