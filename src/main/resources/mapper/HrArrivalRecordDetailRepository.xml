<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrArrivalRecordDetailRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , arrival_id, serial_no, arrival_amount, arrival_date, account_number,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrArrivalRecordDetail">
        <id column="id" property="id"/>
        <result column="arrival_id" property="arrivalId"/>
        <result column="serial_no" property="serialNo"/>
        <result column="arrival_amount" property="arrivalAmount"/>
        <result column="arrival_date" property="arrivalDate"/>
        <result column="account_number" property="accountNumber"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <update id="deleteByArrivalIds">
        UPDATE hr_arrival_record_detail
        SET is_delete = 1
        WHERE arrival_id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="deleteByArrivalDetailIds">
        UPDATE hr_arrival_record_detail
        SET is_delete = 1
        WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="deleteByArrivalId">
        UPDATE hr_arrival_record_detail
        SET is_delete = 1
        WHERE arrival_id = #{arrivalId}
    </update>

    <select id="getByArrivalId" resultType="cn.casair.dto.HrArrivalRecordDetailDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_arrival_record_detail
        WHERE is_delete = 0
        AND arrival_id = #{arrivalId}
        ORDER BY arrival_date
    </select>

    <select id="selectByIds" resultType="cn.casair.domain.HrArrivalRecordDetail">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_arrival_record_detail
        WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>
    <select id="getListByBillInvoiceId" resultType="cn.casair.dto.HrArrivalRecordDetailDTO">
        select ard.*
        from hr_arrival_record_detail ard
                 left join hr_arrival_record ar on ard.arrival_id = ar.id
        where ard.is_delete = 0
          and ar.is_delete = 0
          and ar.bill_invoice_id = #{billInvoiceId}
        order by ard.created_date asc
    </select>

</mapper>
