package cn.casair.service;

import cn.casair.domain.HrBillTotal;
import cn.casair.dto.HrBillTotalDTO;
import cn.casair.dto.HrRevenueExpenditureDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * 账单汇总服务类
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
public interface HrBillTotalService extends IService<HrBillTotal> {


    /**
     * 报表管理-收支统计
     *
     * @param hrBillTotalDTO
     * @return java.util.List<cn.casair.dto.HrRevenueExpenditureDTO>
     * <AUTHOR>
     * @date 2021/11/23
     **/
    List<HrRevenueExpenditureDTO> getRevenueExpenditure(HrBillTotalDTO hrBillTotalDTO);

    /**
     * 统计报表导出
     *
     * @param hrBillTotalDTO
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2021/11/22
     **/
    String export(HrBillTotalDTO hrBillTotalDTO, HttpServletResponse response);

    /**
     * 获取客户年度曲线
     *
     * @param hrBillTotalDTO
     * @return java.util.List<cn.casair.dto.HrBillTotalDTO>
     * <AUTHOR>
     * @date 2021/11/17
     **/
    List<HrBillTotalDTO> getClientAnnualCurve(HrBillTotalDTO hrBillTotalDTO);

    /**
     * 创建账单汇总
     *
     * @param hrBillTotalDTO
     * @return
     */
    HrBillTotalDTO createHrBillTotal(HrBillTotalDTO hrBillTotalDTO);

    /**
     * 修改账单汇总
     *
     * @param hrBillTotalDTO
     * @return
     */
    Optional<HrBillTotalDTO> updateHrBillTotal(HrBillTotalDTO hrBillTotalDTO);

    /**
     * 查询账单汇总详情
     *
     * @param id
     * @return
     */
    HrBillTotalDTO getHrBillTotal(String id);

    /**
     * 删除账单汇总
     *
     * @param id
     */
    void deleteHrBillTotal(String id);

    /**
     * 批量删除账单汇总
     *
     * @param ids
     */
    void deleteHrBillTotal(List<String> ids);

    /**
     * 分页查询账单汇总
     *
     * @param hrBillTotalDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrBillTotalDTO> findPage(HrBillTotalDTO hrBillTotalDTO, Long pageNumber, Long pageSize);

    /**
     * 获取数据统计
     * @param billTotalDTO
     * @return
     */
    List<HrBillTotalDTO> getBillStatistics(HrBillTotalDTO billTotalDTO);

    /**
     * 根据客户id查询客户的账单，分页
     * @param clientId
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrBillTotalDTO> getBillTotalPageByClient(String clientId, Long pageNumber, Long pageSize);

    /**
     * 统计报表 --> 应收账款统计
     *
     * @param billTotalDTO
     * @return
     */
    List<HrBillTotalDTO> accountsReceivableStatistics(HrBillTotalDTO billTotalDTO);

    /**
     * 统计报表 --> 导出应收账款统计
     * @param billTotalDTO
     * @return
     */
    String accountsReceivableExport(HrBillTotalDTO billTotalDTO);

    /**
     * 查询账单汇总数据合计
     * @param hrBillTotalDTO
     * @return
     */
    HrBillTotalDTO getTotalData(HrBillTotalDTO hrBillTotalDTO);

}
