package cn.casair.repository;

import cn.casair.domain.HrLendingApply;
import cn.casair.dto.HrLendingApplyDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 借阅申请表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
@Repository
public interface HrLendingApplyRepository extends BaseMapper<HrLendingApply> {

    IPage<HrLendingApplyDTO> selectLendingApplyPage(Page<HrLendingApply> page, @Param("param") HrLendingApplyDTO hrLendingApplyDTO);

    List<HrLendingApplyDTO> findList(@Param("param") HrLendingApplyDTO hrLendingApplyDTO);

    HrLendingApplyDTO getLendingApplyById(String id);
}
