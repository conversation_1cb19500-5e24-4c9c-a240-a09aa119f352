package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.domain.HrBusinessContact;
import cn.casair.dto.HrBusinessContactDTO;
import cn.casair.mapper.HrBusinessContactMapper;
import cn.casair.repository.HrBusinessContactRepository;
import cn.casair.service.HrBusinessContactService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 业务联系人服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrBusinessContactServiceImpl extends ServiceImpl<HrBusinessContactRepository, HrBusinessContact> implements HrBusinessContactService {

    private final SysOperLogService sysOperLogService;
    private final HrBusinessContactRepository hrBusinessContactRepository;
    private final HrBusinessContactMapper hrBusinessContactMapper;


    @Override
    public int getMaxSort() {
        Integer maxSort = this.hrBusinessContactRepository.getMaxSort();
        if (maxSort == null) {
            maxSort = 0;
        }
        return maxSort;
    }

    @Override
    public List<HrBusinessContactDTO> homePageBusinessContact() {
        return this.hrBusinessContactRepository.getBusinessContactList();
    }

    /**
     * 创建业务联系人
     *
     * @param hrBusinessContactDTO
     * @return
     */
    @Override
    public HrBusinessContactDTO createHrBusinessContact(HrBusinessContactDTO hrBusinessContactDTO) {
        log.info("Create new HrBusinessContact:{}", hrBusinessContactDTO);

        // 检查序号
        this.checkAndUpdateSort(hrBusinessContactDTO.getSort());
        HrBusinessContact hrBusinessContact = this.hrBusinessContactMapper.toEntity(hrBusinessContactDTO);
        this.hrBusinessContactRepository.insert(hrBusinessContact);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
                ModuleTypeEnum.BUSINESS_CONTACT.getValue(),
                BusinessTypeEnum.INSERT.getKey(),
                JSON.toJSONString(hrBusinessContactDTO),
                HrBusinessContactDTO.class,
                null,
                JSON.toJSONString(hrBusinessContact)
        );
        return this.hrBusinessContactMapper.toDto(hrBusinessContact);
    }

    /**
     * 更新序号
     *
     * @param sort
     */
    private void checkAndUpdateSort(Integer sort) {
        List<HrBusinessContact> oldBusinessContactList = this.hrBusinessContactRepository.selectBySort(sort);
        List<Integer> collect = oldBusinessContactList.stream().map(HrBusinessContact::getSort).collect(Collectors.toList());
        if (collect.contains(sort)) {
            oldBusinessContactList.forEach(ls -> {
                ls.setSort(ls.getSort() + 1);
                this.hrBusinessContactRepository.updateById(ls);
            });
        }
    }

    /**
     * 修改业务联系人
     *
     * @param hrBusinessContactDTO
     * @return
     */
    @Override
    public Optional<HrBusinessContactDTO> updateHrBusinessContact(HrBusinessContactDTO hrBusinessContactDTO) {
        return Optional.ofNullable(this.hrBusinessContactRepository.selectById(hrBusinessContactDTO.getId()))
                .map(roleTemp -> {
                    if (hrBusinessContactDTO.getSort() != null && !roleTemp.getSort().equals(hrBusinessContactDTO.getSort())) {
                        // 检查序号
                        this.checkAndUpdateSort(hrBusinessContactDTO.getSort());
                    }
                    HrBusinessContact hrBusinessContact = this.hrBusinessContactMapper.toEntity(hrBusinessContactDTO);
                    this.hrBusinessContactRepository.updateById(hrBusinessContact);
                    log.info("Update HrBusinessContact:{}", hrBusinessContactDTO);
                    // 操作日志
                    this.sysOperLogService.insertSysOperLog(
                            ModuleTypeEnum.BUSINESS_CONTACT.getValue(),
                            BusinessTypeEnum.UPDATE.getKey(),
                            JSON.toJSONString(hrBusinessContactDTO),
                            HrBusinessContactDTO.class,
                            null,
                            JSON.toJSONString(roleTemp),
                            JSON.toJSONString(hrBusinessContactDTO),
                            null,
                            HrBusinessContactDTO.class
                    );
                    return hrBusinessContactDTO;
                });
    }

    /**
     * 查询业务联系人详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBusinessContactDTO getHrBusinessContact(String id) {
        log.info("Get HrBusinessContact :{}", id);

        HrBusinessContact hrBusinessContact = this.hrBusinessContactRepository.selectById(id);
        return this.hrBusinessContactMapper.toDto(hrBusinessContact);
    }

    /**
     * 批量删除业务联系人
     *
     * @param ids
     */
    @Override
    public void deleteHrBusinessContact(List<String> ids) {
        log.info("Delete HrBusinessContacts:{}", ids);
        this.hrBusinessContactRepository.deleteBatchIds(ids);
        // 操作日志
        StringBuilder sb = new StringBuilder("删除业务联系人：");
        List<HrBusinessContact> hrBusinessContacts = this.hrBusinessContactRepository.selectByIds(ids);
        hrBusinessContacts.forEach(ls -> sb.append("[业务类型：")
            .append(ls.getBusinessType()).append("，联系人姓名：")
            .append(ls.getContacts()).append("，联系方式：")
            .append(ls.getContactInformation()).append("]；"));
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.BUSINESS_CONTACT.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            sb.toString(),
            JSON.toJSONString(ids),
            null,
            null,
            null,
            null
        );
    }

    /**
     * 分页查询业务联系人
     *
     * @param hrBusinessContactDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrBusinessContactDTO> findPage(HrBusinessContactDTO hrBusinessContactDTO, Long pageNumber, Long pageSize) {
        Page<HrBusinessContact> page = new Page<>(pageNumber, pageSize);
        page.setOrders(hrBusinessContactDTO.orderItems());
        return this.hrBusinessContactRepository.selectPageList(page, hrBusinessContactDTO);
    }
}
