<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrStationRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
            , profession_type, industry_type, profession_name, label_name, remark,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrStation">
        <id column="id" property="id"/>
        <result column="profession_type" property="professionType"/>
        <result column="industry_type" property="industryType"/>
        <result column="profession_name" property="professionName"/>
        <result column="label_name" property="labelName"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="findList" resultType="cn.casair.dto.excel.HrStationTemplate">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_station
        ${ew.customSqlSegment}
    </select>

    <select id="selectByName" resultType="cn.casair.domain.HrStation">
        SELECT
            <include refid="Base_Column_List"/>
        FROM hr_station
        WHERE is_delete = 0
          AND profession_name = #{professionName}
        ORDER By created_date DESC
        LIMIT 1
    </select>
</mapper>
