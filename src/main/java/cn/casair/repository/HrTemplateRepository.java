package cn.casair.repository;

import cn.casair.domain.HrTemplate;
import cn.casair.dto.HrTemplateDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模板管理数据库操作类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Repository
public interface HrTemplateRepository extends BaseMapper<HrTemplate> {

    IPage<HrTemplateDTO> selectPages(Page<HrTemplate> page, @Param("param1")HrTemplateDTO hrTemplateDTO);

    List<String> selectFrequency();

    List<HrTemplateDTO> findList(@Param("param1") HrTemplateDTO templateDTO);

}
