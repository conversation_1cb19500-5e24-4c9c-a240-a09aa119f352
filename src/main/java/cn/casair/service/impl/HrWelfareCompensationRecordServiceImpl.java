package cn.casair.service.impl;

import cn.casair.domain.HrAccumulationFund;
import cn.casair.domain.HrSocialSecurity;
import cn.casair.domain.HrWelfareCompensationRecord;
import cn.casair.dto.HrEmployeeWelfareDTO;
import cn.casair.dto.HrWelfareCompensationRecordDTO;
import cn.casair.mapper.HrWelfareCompensationRecordMapper;
import cn.casair.repository.HrWelfareCompensationRecordRepository;
import cn.casair.service.HrWelfareCompensationRecordService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 福利补差计算操作日志服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrWelfareCompensationRecordServiceImpl extends ServiceImpl<HrWelfareCompensationRecordRepository, HrWelfareCompensationRecord> implements HrWelfareCompensationRecordService {

    private final HrWelfareCompensationRecordRepository hrWelfareCompensationRecordRepository;
    private final HrWelfareCompensationRecordMapper hrWelfareCompensationRecordMapper;

    @Override
    public int deleteByWelfareCompensationIds(List<String> welfareCompensationIds) {
        return this.hrWelfareCompensationRecordRepository.deleteByWelfareCompensationIds(welfareCompensationIds);
    }

    /**
     * 创建福利补差计算操作日志
     *
     * @param hrWelfareCompensationRecordDTO
     * @return
     */
    @Override
    public HrWelfareCompensationRecordDTO createHrWelfareCompensationLog(HrWelfareCompensationRecordDTO hrWelfareCompensationRecordDTO) {
        log.info("Create new HrWelfareCompensationLog:{}", hrWelfareCompensationRecordDTO);

        HrWelfareCompensationRecord hrWelfareCompensationRecord = this.hrWelfareCompensationRecordMapper.toEntity(hrWelfareCompensationRecordDTO);
        this.hrWelfareCompensationRecordRepository.insert(hrWelfareCompensationRecord);
        return this.hrWelfareCompensationRecordMapper.toDto(hrWelfareCompensationRecord);
    }

    /**
     * 修改福利补差计算操作日志
     *
     * @param hrWelfareCompensationRecordDTO
     * @return
     */
    @Override
    public Optional<HrWelfareCompensationRecordDTO> updateHrWelfareCompensationLog(HrWelfareCompensationRecordDTO hrWelfareCompensationRecordDTO) {
        return Optional.ofNullable(this.hrWelfareCompensationRecordRepository.selectById(hrWelfareCompensationRecordDTO.getId()))
                .map(roleTemp -> {
                    HrWelfareCompensationRecord hrWelfareCompensationRecord = this.hrWelfareCompensationRecordMapper.toEntity(hrWelfareCompensationRecordDTO);
                    this.hrWelfareCompensationRecordRepository.updateById(hrWelfareCompensationRecord);
                    log.info("Update HrWelfareCompensationLog:{}", hrWelfareCompensationRecordDTO);
                    return hrWelfareCompensationRecordDTO;
                });
    }

    /**
     * 查询福利补差计算操作日志详情
     *
     * @param id
     * @return
     */
    @Override
    public HrWelfareCompensationRecordDTO getHrWelfareCompensationLog(String id) {
        log.info("Get HrWelfareCompensationLog :{}", id);

        HrWelfareCompensationRecord hrWelfareCompensationRecord = this.hrWelfareCompensationRecordRepository.selectById(id);
        return this.hrWelfareCompensationRecordMapper.toDto(hrWelfareCompensationRecord);
    }

    /**
     * 删除福利补差计算操作日志
     *
     * @param id
     */
    @Override
    public void deleteHrWelfareCompensationLog(String id) {
        Optional.ofNullable(this.hrWelfareCompensationRecordRepository.selectById(id))
                .ifPresent(hrWelfareCompensationLog -> {
                    this.hrWelfareCompensationRecordRepository.deleteById(id);
                    log.info("Delete HrWelfareCompensationLog:{}", hrWelfareCompensationLog);
                });
    }

    /**
     * 批量删除福利补差计算操作日志
     *
     * @param ids
     */
    @Override
    public void deleteHrWelfareCompensationLog(List<String> ids) {
        log.info("Delete HrWelfareCompensationLogs:{}", ids);
        this.hrWelfareCompensationRecordRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询福利补差计算操作日志
     *
     * @param hrWelfareCompensationRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrWelfareCompensationRecordDTO hrWelfareCompensationRecordDTO, Long pageNumber, Long pageSize) {
        Page<HrWelfareCompensationRecord> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrWelfareCompensationRecord> qw = new QueryWrapper<>(this.hrWelfareCompensationRecordMapper.toEntity(hrWelfareCompensationRecordDTO));
        qw.orderByDesc("id");

        IPage iPage = this.hrWelfareCompensationRecordRepository.selectPage(page, qw);
        iPage.setRecords(this.hrWelfareCompensationRecordMapper.toDto(iPage.getRecords()));
        return iPage;
    }

    @Override
    public HrWelfareCompensationRecord addLog(HrEmployeeWelfareDTO hrEmployeeWelfareOld, HrEmployeeWelfareDTO hrEmployeeWelfareNew, String changeMsg) {
        HrWelfareCompensationRecord hrWelfareCompensationRecord = new HrWelfareCompensationRecord();

        hrWelfareCompensationRecord
//            .setSocialSecurityCardinalBaseOld(hrEmployeeWelfareOld.getSocialSecurityCardinal())
            .setUnitPensionCardinalBaseOld(hrEmployeeWelfareOld.getUnitPensionCardinal())
            .setUnitUnemploymentCardinalBaseOld(hrEmployeeWelfareOld.getUnitUnemploymentCardinal())
            .setWorkInjuryCardinalBaseOld(hrEmployeeWelfareOld.getWorkInjuryCardinal())
            .setUnitMaternityCardinalBaseOld(hrEmployeeWelfareOld.getUnitMaternityCardinal())
            .setMedicalInsuranceCardinalBaseOld(hrEmployeeWelfareOld.getMedicalInsuranceCardinal())
            .setAccumulationFundCardinalBaseOld(hrEmployeeWelfareOld.getAccumulationFundCardinal())
//            .setSocialSecurityCardinalPersonalBaseOld(hrEmployeeWelfareOld.getSocialSecurityCardinalPersonal())
            .setPersonalPensionCardinalBaseOld(hrEmployeeWelfareOld.getPersonalPensionCardinal())
            .setPersonalUnemploymentCardinalBaseOld(hrEmployeeWelfareOld.getPersonalUnemploymentCardinal())
            .setMedicalInsuranceCardinalPersonalBaseOld(hrEmployeeWelfareOld.getMedicalInsuranceCardinalPersonal())
            .setUnitLargeMedicalExpenseBaseOld(hrEmployeeWelfareOld.getUnitLargeMedicalExpense())
            .setReplenishWorkInjuryExpenseBaseOld(hrEmployeeWelfareOld.getReplenishWorkInjuryExpense())
            .setPersonalLargeMedicalExpenseBaseOld(hrEmployeeWelfareOld.getPersonalLargeMedicalExpense())

            .setPersonalMaternityCardinalBaseOld(hrEmployeeWelfareOld.getPersonalMaternityCardinal())
            .setPersonalMaternityScaleOld(hrEmployeeWelfareOld.getPersonalMaternity())
            .setPersonalMaternityCardinalBaseNew(hrEmployeeWelfareNew.getPersonalMaternityCardinal())
            .setPersonalMaternityScaleNew(hrEmployeeWelfareNew.getPersonalMaternity())

//            .setSocialSecurityCardinalBaseNew(hrEmployeeWelfareNew.getSocialSecurityCardinal())
            .setUnitPensionCardinalBaseNew(hrEmployeeWelfareNew.getUnitPensionCardinal())
            .setUnitUnemploymentCardinalBaseNew(hrEmployeeWelfareNew.getUnitUnemploymentCardinal())
            .setWorkInjuryCardinalBaseNew(hrEmployeeWelfareNew.getWorkInjuryCardinal())
            .setUnitMaternityCardinalBaseNew(hrEmployeeWelfareNew.getUnitMaternityCardinal())
            .setMedicalInsuranceCardinalBaseNew(hrEmployeeWelfareNew.getMedicalInsuranceCardinal())
            .setAccumulationFundCardinalBaseNew(hrEmployeeWelfareNew.getAccumulationFundCardinal())
//            .setSocialSecurityCardinalPersonalBaseNew(hrEmployeeWelfareNew.getSocialSecurityCardinalPersonal())
            .setPersonalPensionCardinalBaseNew(hrEmployeeWelfareNew.getPersonalPensionCardinal())
            .setPersonalUnemploymentCardinalBaseNew(hrEmployeeWelfareNew.getPersonalUnemploymentCardinal())
            .setMedicalInsuranceCardinalPersonalBaseNew(hrEmployeeWelfareNew.getMedicalInsuranceCardinalPersonal())
            .setUnitLargeMedicalExpenseBaseNew(hrEmployeeWelfareNew.getUnitLargeMedicalExpense())
            .setReplenishWorkInjuryExpenseBaseNew(hrEmployeeWelfareNew.getReplenishWorkInjuryExpense())
            .setPersonalLargeMedicalExpenseBaseNew(hrEmployeeWelfareNew.getPersonalLargeMedicalExpense())
            .setChangeMsg(changeMsg);

        this.hrWelfareCompensationRecordRepository.insert(hrWelfareCompensationRecord);
        return hrWelfareCompensationRecord;
    }

    @Override
    public HrWelfareCompensationRecord addLog(HrSocialSecurity hrSocialSecurityOld, HrSocialSecurity hrSocialSecurityNew, String changeMsg) {
        HrWelfareCompensationRecord hrWelfareCompensationRecord = new HrWelfareCompensationRecord();

        hrWelfareCompensationRecord.setUnitPensionScaleOld(hrSocialSecurityOld.getUnitPension());
        hrWelfareCompensationRecord.setUnitMedicalScaleOld(hrSocialSecurityOld.getUnitMedical());
        hrWelfareCompensationRecord.setWorkInjuryScaleOld(hrSocialSecurityOld.getWorkInjury());
        hrWelfareCompensationRecord.setUnitUnemploymentScaleOld(hrSocialSecurityOld.getUnitUnemployment());
        hrWelfareCompensationRecord.setUnitMaternityScaleOld(hrSocialSecurityOld.getUnitMaternity());
        hrWelfareCompensationRecord.setPersonalPensionScaleOld(hrSocialSecurityOld.getPersonalPension());
        hrWelfareCompensationRecord.setPersonalMedicalScaleOld(hrSocialSecurityOld.getPersonalMedical());
        hrWelfareCompensationRecord.setPersonalUnemploymentScaleOld(hrSocialSecurityOld.getPersonalUnemployment());

        hrWelfareCompensationRecord.setPersonalMaternityScaleOld(hrSocialSecurityOld.getPersonalMaternity());
        hrWelfareCompensationRecord.setPersonalMaternityScaleNew(hrSocialSecurityNew.getPersonalMaternity());

        hrWelfareCompensationRecord.setUnitPensionScaleNew(hrSocialSecurityNew.getUnitPension());
        hrWelfareCompensationRecord.setUnitMedicalScaleNew(hrSocialSecurityNew.getUnitMedical());
        hrWelfareCompensationRecord.setWorkInjuryScaleNew(hrSocialSecurityNew.getWorkInjury());
        hrWelfareCompensationRecord.setUnitUnemploymentScaleNew(hrSocialSecurityNew.getUnitUnemployment());
        hrWelfareCompensationRecord.setUnitMaternityScaleNew(hrSocialSecurityNew.getUnitMaternity());
        hrWelfareCompensationRecord.setPersonalPensionScaleNew(hrSocialSecurityNew.getPersonalPension());
        hrWelfareCompensationRecord.setPersonalMedicalScaleNew(hrSocialSecurityNew.getPersonalMedical());
        hrWelfareCompensationRecord.setPersonalUnemploymentScaleNew(hrSocialSecurityNew.getPersonalUnemployment());

        hrWelfareCompensationRecord.setChangeMsg(changeMsg);

        this.hrWelfareCompensationRecordRepository.insert(hrWelfareCompensationRecord);
        return hrWelfareCompensationRecord;
    }

    @Override
    public HrWelfareCompensationRecord addLog(HrAccumulationFund hrAccumulationFundOld, HrAccumulationFund hrAccumulationFundNew, String changeMsg) {
        HrWelfareCompensationRecord hrWelfareCompensationRecord = new HrWelfareCompensationRecord();

        hrWelfareCompensationRecord.setUnitAccumulationFundScaleOld(hrAccumulationFundOld.getUnitScale());
        hrWelfareCompensationRecord.setPersonalAccumulationFundScaleOld(hrAccumulationFundOld.getPersonageScale());

        hrWelfareCompensationRecord.setUnitAccumulationFundScaleNew(hrAccumulationFundNew.getUnitScale());
        hrWelfareCompensationRecord.setPersonalAccumulationFundScaleNew(hrAccumulationFundNew.getPersonageScale());

        hrWelfareCompensationRecord.setChangeMsg(changeMsg);

        this.hrWelfareCompensationRecordRepository.insert(hrWelfareCompensationRecord);
        return hrWelfareCompensationRecord;
    }

    @Override
    public HrWelfareCompensationRecord selectById(String recordId) {
        return this.hrWelfareCompensationRecordRepository.selectById(recordId);
    }

    @Override
    public List<HrWelfareCompensationRecordDTO> selectByBillId(String billId) {
        return this.hrWelfareCompensationRecordRepository.selectByBillId(billId);
    }
}
