package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.AuditInForEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.enums.StaffEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.StringUtil;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.mapper.HrDataModificationMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 资料修改服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrDataModificationServiceImpl extends ServiceImpl<HrDataModificationRepository, HrDataModification> implements HrDataModificationService {


    private final HrDataModificationRepository hrDataModificationRepository;
    private final HrDataModificationMapper hrDataModificationMapper;
    private final HrStaffEmolumentService hrStaffEmolumentService;
    private final CodeTableService codeTableService;
    private final HrContractAppendixService hrContractAppendixService;
    private final CodeTableRepository codeTableRepository;
    @Resource
    private HrTalentStaffService hrTalentStaffService;
    @Resource
    private HrModificationContentRepository hrModificationContentRepository;
    @Resource
    private HrStaffWorkExperienceService hrStaffWorkExperienceService;
    @Resource
    private HrStaffEducationService hrStaffEducationService;
    @Resource
    private HrStaffCertificateService hrStaffCertificateService;
    @Resource
    private HrStaffContactsService hrStaffContactsService;
    @Resource
    private HrStaffFamilyService hrStaffFamilyService;
    @Resource
    private HrStaffInterviewService hrStaffInterviewService;
    @Resource
    private HrStaffLanguageService hrStaffLanguageService;
    @Resource
    private HrStaffProfessionService hrStaffProfessionService;
    @Resource
    private HrStaffQualificationService hrStaffQualificationService;
    @Resource
    private HrStaffTechniqueService hrStaffTechniqueService;
    @Resource
    private HrApplyCheckerService hrApplyCheckerService;
    @Resource
    private HrNotificationUserService hrNotificationUserService;
    @Resource
    private HrApplyCheckerRepository hrApplyCheckerRepository;
    @Resource
    private HrAppletMessageService hrAppletMessageService;
    @Resource
    private HrTalentStaffRepository hrTalentStaffRepository;
    @Resource
    private HrStaffSignCertService hrStaffSignCertService;
    @Resource
    private HrUpcomingService hrUpcomingService;
    @Resource
    private SysOperLogService sysOperLogService;

    @Resource
    private HrAppendixService hrAppendixService;

    @Override
    public void salaryBankModification(HrStaffEmolumentDTO hrStaffEmolumentDTO) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        if (org.apache.commons.lang3.StringUtils.isBlank(hrStaffEmolumentDTO.getOwnedBankLabel())) {
            throw new CommonException("银行卡名称不能为空!");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(hrStaffEmolumentDTO.getSalaryCardNum())) {
            throw new CommonException("银行卡号码不能为空!");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(hrStaffEmolumentDTO.getBankCardUrl())) {
            throw new CommonException("银行卡图片不能为空!");
        }

        // 检查银行卡信息
        this.hrContractAppendixService.checkStaffBankInfo(jwtMiniDTO, hrStaffEmolumentDTO.getOwnedBankLabel(), hrStaffEmolumentDTO.getSalaryCardNum());
        // 查银行字典 没有则添加
        CodeTable codeTable = this.codeTableService.getItemByBankName(hrStaffEmolumentDTO.getOwnedBankLabel());
        if (codeTable == null) {
            CodeTable children = this.codeTableService.getMaxChildrenByInnerName("ownedBank");
            codeTable = new CodeTable();
            codeTable.setParentId(children.getParentId());
            codeTable.setItemName(hrStaffEmolumentDTO.getOwnedBankLabel());
            codeTable.setItemValue(children.getItemValue() + 1);
            codeTable.setDisplayOrder(children.getDisplayOrder() + 1);
            this.codeTableRepository.insert(codeTable);
        }
        hrStaffEmolumentDTO.setOwnedBankLabel(codeTable.getItemName());
        HrStaffEmolumentDTO oldData = this.hrStaffEmolumentService.selectStaffSalaryBankInfo(jwtMiniDTO.getId());

        HrDataModification hrDataModification = new HrDataModification();
        hrDataModification.setClientId(jwtMiniDTO.getClientId());
        hrDataModification.setStaffId(jwtMiniDTO.getId());
        hrDataModification.setApplyType(AuditInForEnum.ApplyType.SALARY_BANK.getKey());
        hrDataModification.setApplyContent(JSON.toJSONString(hrStaffEmolumentDTO, SerializerFeature.WriteMapNullValue));
        hrDataModification.setModificationContent(JSON.toJSONString(hrStaffEmolumentDTO, SerializerFeature.WriteMapNullValue));
        hrDataModification.setModificationBefore(JSON.toJSONString(oldData));
        hrDataModification.setLastModifiedDate(LocalDateTime.now());
        this.hrDataModificationRepository.insert(hrDataModification);

        Map<String, String> oldDataMap = new HashMap<>();
        oldDataMap.put("ownedBank", oldData.getOwnedBank());
        oldDataMap.put("ownedLabel", oldData.getOwnedBankLabel());
        oldDataMap.put("salaryCardNum", oldData.getSalaryCardNum());
        oldDataMap.put("bankCardUrl", oldData.getBankCardUrl());
        Map<String, String> newDataMap = new HashMap<>();
        newDataMap.put("ownedBank", hrStaffEmolumentDTO.getOwnedBank());
        newDataMap.put("ownedLabel", hrStaffEmolumentDTO.getOwnedBankLabel());
        newDataMap.put("salaryCardNum", hrStaffEmolumentDTO.getSalaryCardNum());
        newDataMap.put("bankCardUrl", hrStaffEmolumentDTO.getBankCardUrl());
        // 插入明细
        newDataMap.forEach((k, v) -> {
            HrModificationContent hrModificationContent = new HrModificationContent();
            hrModificationContent.setInfoId(oldData.getId());
            hrModificationContent.setModificationId(hrDataModification.getId());
            hrModificationContent.setModificationTitle("工资卡信息--" + k);
            hrModificationContent.setModificationType(AuditInForEnum.ModificationType.UPDATE.getKey());
            Map<String, String> before = new HashMap<>();
            before.put(k, oldDataMap.get(k));
            hrModificationContent.setModificationBefore(JSON.toJSONString(before));
            Map<String, String> after = new HashMap<>();
            after.put(k, v);
            hrModificationContent.setModificationAfter(JSON.toJSONString(after));
            hrModificationContent.setIzSingle(AuditInForEnum.IzSingleEnum.MULTIPLE.getKey());
            this.hrModificationContentRepository.insert(hrModificationContent);
        });

        this.hrNotificationUserService.saveRemindContent(jwtMiniDTO.getClientId(), ServiceCenterEnum.DATA_MODIFICATION.getKey(), 1, jwtMiniDTO.getName() + "通过小程序发起了工资卡修改申请", jwtMiniDTO.getId());
        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.DATA_MODIFICATION.getKey(), hrDataModification.getId(), hrDataModification.getStaffId(), "您已发起了资料修改服务", false, null);
        hrUpcomingService.createServiceUpcoming(hrDataModification.getId(), jwtMiniDTO.getId(), "资料修改-" + jwtMiniDTO.getName() + "申请的" + AuditInForEnum.ApplyType.SALARY_BANK.getValue() + "资料修改申请待审核", LocalDate.now(), 0);
        HrDataModificationDTO modificationDTO = hrDataModificationRepository.findById(hrDataModification.getId());
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.DATA_MODIFICATION.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(modificationDTO),
            HrDataModification.class,
            hrStaffEmolumentDTO.getBankCardUrl(),
            JSON.toJSONString(modificationDTO)
        );
    }

    /**
     * 创建资料修改
     *
     * @param hrDataModificationDTO
     * @return
     */
    @Override
    public HrDataModificationDTO createHrDataModification(HrDataModificationDTO hrDataModificationDTO) {
        log.info("Create new HrDataModification:{}", hrDataModificationDTO);

        HrDataModification hrDataModification = this.hrDataModificationMapper.toEntity(hrDataModificationDTO);
        this.hrDataModificationRepository.insert(hrDataModification);
        return this.hrDataModificationMapper.toDto(hrDataModification);
    }

    /**
     * 修改资料修改
     *
     * @param hrDataModificationDTO
     * @return
     */
    @Override
    public Optional<HrDataModificationDTO> updateHrDataModification(HrDataModificationDTO hrDataModificationDTO) {
        return Optional.ofNullable(this.hrDataModificationRepository.selectById(hrDataModificationDTO.getId()))
            .map(roleTemp -> {
                HrDataModification hrDataModification = this.hrDataModificationMapper.toEntity(hrDataModificationDTO);
                this.hrDataModificationRepository.updateById(hrDataModification);
                log.info("Update HrDataModification:{}", hrDataModificationDTO);
                return hrDataModificationDTO;
            });
    }

    /**
     * 查询资料修改详情
     *
     * @param id
     * @return
     */
    @Override
    public HrDataModificationDTO getHrDataModification(String id) {
        log.info("Get HrDataModification :{}", id);

        HrDataModificationDTO modificationDTO = this.hrDataModificationRepository.findById(id);
        this.setDict(modificationDTO);
        //查找申请修改内容
        List<HrModificationContent> modificationContentList = hrModificationContentRepository.selectList(new QueryWrapper<HrModificationContent>()
            .eq("modification_id", modificationDTO.getId()));
        if (CollectionUtils.isNotEmpty(modificationContentList)) {
            modificationContentList.forEach(ls -> {
                ls.setModificationTypeLabel(AuditInForEnum.ModificationType.getValueByKey(ls.getModificationType()));
            });
            modificationDTO.setModificationContentList(modificationContentList);
        }
        //查找操作信息
        List<HrApplyCheckerDTO> applyCheckerList = hrApplyCheckerService.findApplyChecker(modificationDTO.getId());
        if (CollectionUtils.isNotEmpty(applyCheckerList)) {
            modificationDTO.setApplyCheckerList(applyCheckerList);
        }
        return modificationDTO;
    }

    /**
     * 删除资料修改
     *
     * @param id
     */
    @Override
    public void deleteHrDataModification(String id) {
        Optional.ofNullable(this.hrDataModificationRepository.selectById(id))
            .ifPresent(hrDataModification -> {
                this.hrDataModificationRepository.deleteById(id);
                log.info("Delete HrDataModification:{}", hrDataModification);
            });
    }

    /**
     * 批量删除资料修改
     *
     * @param ids
     */
    @Override
    public void deleteHrDataModification(List<String> ids) {
        log.info("Delete HrDataModifications:{}", ids);
        this.hrDataModificationRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询资料修改
     *
     * @param hrDataModificationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrDataModificationDTO hrDataModificationDTO, Long pageNumber, Long pageSize) {
        Page<HrDataModification> page = new Page<>(pageNumber, pageSize);

        IPage<HrDataModificationDTO> iPage = this.hrDataModificationRepository.findPage(page, hrDataModificationDTO);
        iPage.getRecords().forEach(this::setDict);
        return iPage;
    }

    @Override
    public String export(HrDataModificationDTO hrDataModificationDTO) {
        List<HrDataModificationDTO> list = this.hrDataModificationRepository.findList(hrDataModificationDTO);
        List<String> ids = list.stream().map(HrDataModificationDTO::getId).collect(Collectors.toList());
        list.forEach(this::setDict);
        String fileUrl = this.hrAppendixService.uploadExportFile(list, ModuleTypeEnum.DATA_MODIFICATION.getValue(), HrDataModificationDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.DATA_MODIFICATION.getValue(), BusinessTypeEnum.EXPORT.getKey(),
            JSON.toJSONString(ids), ids.size(), fileUrl);
        return fileUrl;
    }

    /**
     * 赋值字典值
     *
     * @param modificationDTO
     */
    private void setDict(HrDataModificationDTO modificationDTO) {
        if (modificationDTO.getApplyStatus() != null) {
            modificationDTO.setApplyStatusLabel(AuditInForEnum.ApplyStatus.getValueByKey(modificationDTO.getApplyStatus()));
        }
        if (modificationDTO.getApplyType() != null) {
            modificationDTO.setApplyTypeLabel(AuditInForEnum.ApplyType.getValueByKey(modificationDTO.getApplyType()));
        }
    }

    /**
     * 资料修改--基本信息
     *
     * @param hrTalentStaffDTO
     * @return
     */
    @Override
    public ResponseEntity basicInformationModification(HrTalentStaffDTO hrTalentStaffDTO) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        hrTalentStaffService.setDict(hrTalentStaffDTO);
        HrDataModification hrDataModification = new HrDataModification();
        //查询基本信息数据
        HrTalentStaffDTO staffBasicInfo = hrTalentStaffService.getStaffBasicInfo(jwtMiniDTO.getId());
        hrTalentStaffService.setDict(staffBasicInfo);
        Map<String, Object> oldDataMap = this.objectToMap(staffBasicInfo);//旧数据
        Map<String, Object> newDataMap = this.objectToMap(hrTalentStaffDTO);//新数据
        //判断所有字段
        hrDataModification.setStaffId(jwtMiniDTO.getId()).setClientId(jwtMiniDTO.getClientId())
            .setApplyContent(AuditInForEnum.ApplyType.ESSENTIAL_INFOR.getValue())
            .setApplyType(AuditInForEnum.ApplyType.ESSENTIAL_INFOR.getKey())
            .setModificationContent(JSON.toJSONString(hrTalentStaffDTO, SerializerFeature.WriteMapNullValue))
            .setApplyContent(JSON.toJSONString(hrTalentStaffDTO, SerializerFeature.WriteMapNullValue))
            .setModificationBefore(JSON.toJSONString(staffBasicInfo, SerializerFeature.WriteMapNullValue))
            .setLastModifiedDate(LocalDateTime.now());
        hrDataModificationRepository.insert(hrDataModification);
        this.handleDataMap(oldDataMap, newDataMap, hrDataModification.getId(), "基本", staffBasicInfo.getId());
        hrDataModificationRepository.updateById(hrDataModification);
        this.hrNotificationUserService.saveRemindContent(staffBasicInfo.getClientId(), ServiceCenterEnum.DATA_MODIFICATION.getKey(), 1, jwtMiniDTO.getName() + "通过小程序发起了基本信息的资料修改申请", jwtMiniDTO.getId());
        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.DATA_MODIFICATION.getKey(), hrDataModification.getId(), hrDataModification.getStaffId(), "您已发起了资料修改服务", false, null);
        hrUpcomingService.createServiceUpcoming(hrDataModification.getId(), jwtMiniDTO.getId(), "资料修改-" + jwtMiniDTO.getName() + "申请的" + AuditInForEnum.ApplyType.ESSENTIAL_INFOR.getValue() + "资料修改申请待审核", LocalDate.now(), 0);
        HrDataModificationDTO modificationDTO = hrDataModificationRepository.findById(hrDataModification.getId());
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.DATA_MODIFICATION.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(modificationDTO),
            HrDataModification.class,
            null,
            JSON.toJSONString(modificationDTO)
        );
        // 处理省市区
        this.dealContactAddress(staffBasicInfo);
        return ResponseUtil.buildSuccess(staffBasicInfo);
    }

    /**
     * 处理通信地址省市区
     *
     * @param staffBasicInfo
     */
    private void dealContactAddress(HrTalentStaffDTO staffBasicInfo) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(staffBasicInfo.getContactAddress())) {
            Map<String, String> addressMap = StringUtil.addressResolution(staffBasicInfo.getContactAddress());
            if (addressMap != null && addressMap.size() > 0) {
                // 省市区
                String province = addressMap.get("province");
                String city = addressMap.get("city");
                String county = addressMap.get("county");
                String addressPrefix = (province == null ? "" : province) + "/" + (city == null ? "" : city) + "/" + (county == null ? "" : county);
                // 详细地址
                String town = addressMap.get("town");
                String village = addressMap.get("village");
                String houseNumber = addressMap.get("houseNumber");
                String addressDetail = (town == null ? "" : town) + (village == null ? "" : village) + (houseNumber == null ? "" : houseNumber);

                staffBasicInfo.setContactAddressPrefix(addressPrefix);
                staffBasicInfo.setContactAddressDetail(addressDetail);
            } else {
                staffBasicInfo.setContactAddressDetail(staffBasicInfo.getContactAddress());
            }
        }
    }

    /**
     * 资料修改--附加信息--工作经历
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity workExperienceModification(List<HrStaffWorkExperienceDTO> dtoList) {
        //查询该员工之前的工作经历
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.WORK_EXPERIENCE.getKey());
        //之前数据
        List<HrStaffWorkExperienceDTO> hrStaffWorkExperienceDTOList = hrStaffWorkExperienceService.findWorkExperienceList(jwtMiniDTO.getId(), false);
        Map<String, HrStaffWorkExperienceDTO> workExperienceDTOMap = hrStaffWorkExperienceDTOList.stream().collect(Collectors.toMap(HrStaffWorkExperienceDTO::getId, Function.identity()));
        List<String> list = new ArrayList<>();
        List<String> listNew = new ArrayList<>();//修改后
        List<String> listOld = new ArrayList<>();//修改前
        for (HrStaffWorkExperienceDTO hrStaffWorkExperienceDTO : dtoList) {
            this.hrStaffWorkExperienceService.setDict(hrStaffWorkExperienceDTO);
            Map<String, Object> objectMap = this.objectToMap(hrStaffWorkExperienceDTO);
            if (hrStaffWorkExperienceDTO.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增工作经历--" + hrStaffWorkExperienceDTO.getEmployerUnit(), objectMap);
            } else if (hrStaffWorkExperienceDTO.getIsDelete()) {//删除
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(workExperienceDTOMap.get(hrStaffWorkExperienceDTO.getId()));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除工作经历--" + hrStaffWorkExperienceDTO.getEmployerUnit(), objectMap);
            } else {//修改
                //通过ID取出之前的信息
                list.add(JSON.toJSONString(hrStaffWorkExperienceDTO, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(workExperienceDTOMap.get(hrStaffWorkExperienceDTO.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), hrStaffWorkExperienceDTO.getEmployerUnit(), hrStaffWorkExperienceDTO.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(list, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.WORK_EXPERIENCE.getValue());
        return ResponseUtil.buildSuccess(hrStaffWorkExperienceDTOList);
    }

    /**
     * 资料修改--附加信息--教育经历
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity educationalExperienceModification(List<HrStaffEducationDTO> dtoList) {
        //查询该员工之前的工作经历
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.EDUCATIONAL_EXPERIENCE.getKey());
        //之前数据
        List<HrStaffEducationDTO> educationList = hrStaffEducationService.findEducationList(jwtMiniDTO.getId());
        Map<String, HrStaffEducationDTO> dtoMap = educationList.stream().collect(Collectors.toMap(HrStaffEducationDTO::getId, Function.identity()));
        List<String> list = new ArrayList<>();
        List<String> listNew = new ArrayList<>();//修改后
        List<String> listOld = new ArrayList<>();//修改前
        for (HrStaffEducationDTO dto : dtoList) {
            this.hrStaffEducationService.setDict(dto);
            Map<String, Object> objectMap = this.objectToMap(dto);
            if (dto.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增教育经历--" + dto.getEducation(), objectMap);
            } else if (dto.getIsDelete()) {//删除
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除教育经历--" + dto.getEducation(), objectMap);
            } else {//修改
                list.add(JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), dto.getEducation(), dto.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(list, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.EDUCATIONAL_EXPERIENCE.getValue());
        return ResponseUtil.buildSuccess(educationList);
    }

    /**
     * 资料修改--附加信息--应试经历
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity interviewExperiencdtoseModification(List<HrExamResultDTO> dtoList) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.INTERVIEW_EXPERIENCE.getKey());
        //之前数据
        List<HrExamResultDTO> list = hrStaffInterviewService.findInterviewList(jwtMiniDTO.getId());
        Map<String, HrExamResultDTO> dtoMap = list.stream().collect(Collectors.toMap(HrExamResultDTO::getId, Function.identity()));
        List<String> lists = new ArrayList<>();
        List<String> listNew = new ArrayList<>();//修改后
        List<String> listOld = new ArrayList<>();//修改前
        for (HrExamResultDTO dto : dtoList) {
            this.hrStaffInterviewService.setDict(dto);
            Map<String, Object> objectMap = this.objectToMap(dto);
            if (dto.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增应试经历--" + dto.getClientName(), objectMap);
            } else if (dto.getIsDelete()) {//删除
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除应试经历--" + dto.getClientName(), objectMap);
            } else {//修改
                lists.add(JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), dto.getClientName(), dto.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(lists, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.INTERVIEW_EXPERIENCE.getValue());
        return ResponseUtil.buildSuccess(list);
    }

    /**
     * 资料修改--附加信息--家庭成员
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity familyModification(List<HrStaffFamilyDTO> dtoList) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.FAMILY.getKey());
        //之前数据
        List<HrStaffFamilyDTO> list = hrStaffFamilyService.findFamilyList(jwtMiniDTO.getId());
        Map<String, HrStaffFamilyDTO> dtoMap = list.stream().collect(Collectors.toMap(HrStaffFamilyDTO::getId, Function.identity()));
        List<String> lists = new ArrayList<>();
        List<String> listNew = new ArrayList<>();//修改后
        List<String> listOld = new ArrayList<>();//修改前
        for (HrStaffFamilyDTO dto : dtoList) {
            Map<String, Object> objectMap = this.objectToMap(dto);
            if (dto.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增家庭成员--" + dto.getFamilyName(), objectMap);
            } else if (dto.getIsDelete()) {//删除
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除家庭成员--" + dto.getFamilyName(), objectMap);
            } else {//修改
                lists.add(JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), dto.getFamilyName(), dto.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(lists, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.FAMILY.getValue());
        return ResponseUtil.buildSuccess(list);
    }

    /**
     * 资料修改--附加信息--紧急联系人
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity contactsModification(List<HrStaffContactsDTO> dtoList) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.CONTACTS.getKey());
        //之前数据
        List<HrStaffContactsDTO> list = hrStaffContactsService.findContactsList(jwtMiniDTO.getId());
        Map<String, HrStaffContactsDTO> dtoMap = list.stream().collect(Collectors.toMap(HrStaffContactsDTO::getId, Function.identity()));
        List<String> lists = new ArrayList<>();
        List<String> listNew = new ArrayList<>();//修改后
        List<String> listOld = new ArrayList<>();//修改前
        for (HrStaffContactsDTO dto : dtoList) {
            Map<String, Object> objectMap = this.objectToMap(dto);
            if (dto.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增紧急联系人--" + dto.getFamilyName(), objectMap);
            } else if (dto.getIsDelete()) {//删除
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除紧急联系人--" + dto.getFamilyName(), objectMap);
            } else {//修改
                lists.add(JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), dto.getFamilyName(), dto.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(lists, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.CONTACTS.getValue());
        return ResponseUtil.buildSuccess(list);
    }

    /**
     * 资料修改--附加信息--语言能力
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity languageModification(List<HrStaffLanguageDTO> dtoList) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.LANGUAGE.getKey());
        //之前数据
        List<HrStaffLanguageDTO> list = hrStaffLanguageService.findLanguageList(jwtMiniDTO.getId());
        Map<String, HrStaffLanguageDTO> dtoMap = list.stream().collect(Collectors.toMap(HrStaffLanguageDTO::getId, Function.identity()));
        List<String> lists = new ArrayList<>();
        List<String> listOld = new ArrayList<>();//修改前
        List<String> listNew = new ArrayList<>();//修改后
        for (HrStaffLanguageDTO dto : dtoList) {
            hrStaffLanguageService.setDict(dto);
            Map<String, Object> objectMap = this.objectToMap(dto);
            if (dto.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增语言能力--" + dto.getLanguageLabel(), objectMap);
            } else if (dto.getIsDelete()) {//删除
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除语言能力--" + dto.getLanguageLabel(), objectMap);
            } else {//修改
                lists.add(JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), dto.getLanguageLabel(), dto.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(lists, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.LANGUAGE.getValue());
        return ResponseUtil.buildSuccess(list);
    }

    /**
     * 资料修改--附加信息--专业能力
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity professionModification(List<HrStaffProfessionDTO> dtoList) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.PROFESSION.getKey());
        //之前数据
        List<HrStaffProfessionDTO> list = hrStaffProfessionService.findProfessionList(jwtMiniDTO.getId());
        Map<String, HrStaffProfessionDTO> dtoMap = list.stream().collect(Collectors.toMap(HrStaffProfessionDTO::getId, Function.identity()));
        List<String> lists = new ArrayList<>();
        List<String> listNew = new ArrayList<>();//修改后
        List<String> listOld = new ArrayList<>();//修改前
        for (HrStaffProfessionDTO dto : dtoList) {
            this.hrStaffProfessionService.setDict(dto);
            Map<String, Object> objectMap = this.objectToMap(dto);
            if (dto.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增专业技能--" + dto.getSkillName(), objectMap);
            } else if (dto.getIsDelete()) {//删除
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除专业技能--" + dto.getSkillName(), objectMap);
            } else {//修改
                lists.add(JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), dto.getSkillName(), dto.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(lists, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.PROFESSION.getValue());
        return ResponseUtil.buildSuccess(list);
    }

    /**
     * 资料修改--附加信息--职业(工种)资格
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity qualificationModification(List<HrStaffQualificationDTO> dtoList) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.QUALIFICATION.getKey());
        //之前数据
        List<HrStaffQualificationDTO> list = hrStaffQualificationService.findQualificationList(jwtMiniDTO.getId());
        Map<String, HrStaffQualificationDTO> dtoMap = list.stream().collect(Collectors.toMap(HrStaffQualificationDTO::getId, Function.identity()));
        List<String> lists = new ArrayList<>();
        List<String> listNew = new ArrayList<>();//修改后
        List<String> listOld = new ArrayList<>();//修改前
        for (HrStaffQualificationDTO dto : dtoList) {
            this.hrStaffQualificationService.setDict(dto);
            Map<String, Object> objectMap = this.objectToMap(dto);
            if (dto.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增职业(工种)资格--" + dto.getQualificationName(), objectMap);
            } else if (dto.getIsDelete()) {//删除
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除职业(工种)资格--" + dto.getQualificationName(), objectMap);
            } else {//修改
                lists.add(JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), dto.getQualificationName(), dto.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(lists, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.QUALIFICATION.getValue());
        return ResponseUtil.buildSuccess(list);
    }

    /**
     * 资料修改--附加信息--职业技术能力
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity techniqueModification(List<HrStaffTechniqueDTO> dtoList) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.TECHNIQUE.getKey());
        //之前数据
        List<HrStaffTechniqueDTO> list = hrStaffTechniqueService.findTechniqueList(jwtMiniDTO.getId());
        Map<String, HrStaffTechniqueDTO> dtoMap = list.stream().collect(Collectors.toMap(HrStaffTechniqueDTO::getId, Function.identity()));
        List<String> lists = new ArrayList<>();
        List<String> listNew = new ArrayList<>();//修改后
        List<String> listOld = new ArrayList<>();//修改前
        for (HrStaffTechniqueDTO dto : dtoList) {
            this.hrStaffTechniqueService.setDict(dto);
            Map<String, Object> objectMap = this.objectToMap(dto);
            if (dto.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增职业技术能力--" + dto.getTechniqueName(), objectMap);
            } else if (dto.getIsDelete()) {//删除
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除职业技术能力--" + dto.getTechniqueName(), objectMap);
            } else {//修改
                lists.add(JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), dto.getTechniqueName(), dto.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(lists, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.TECHNIQUE.getValue());
        return ResponseUtil.buildSuccess(list);
    }

    /**
     * 资料修改--附加信息--证书
     *
     * @param dtoList 信息
     * @return
     */
    @Override
    public ResponseEntity certificateModification(List<HrStaffCertificateDTO> dtoList) {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        HrDataModificationDTO hrDataModificationDTO = this.saveHrDataModification(jwtMiniDTO, AuditInForEnum.ApplyType.CERTIFICATE.getKey());
        //之前数据
        List<HrStaffCertificateDTO> list = hrStaffCertificateService.findCertificateList(jwtMiniDTO.getId());
        Map<String, HrStaffCertificateDTO> dtoMap = list.stream().collect(Collectors.toMap(HrStaffCertificateDTO::getId, Function.identity()));
        List<String> lists = new ArrayList<>();
        //全部修改内容
        List<String> listNew = new ArrayList<>();//修改后
        List<String> listOld = new ArrayList<>();//修改前
        for (HrStaffCertificateDTO dto : dtoList) {
            Map<String, Object> objectMap = this.objectToMap(dto);
            if (dto.getId() == null) {//新增
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.INSERT.getKey(), "新增证书--" + dto.getCertificateName(), objectMap);
            } else if (dto.getIsDelete()) {//删除
                listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                this.saveHrModificationContent(hrDataModificationDTO.getId(), AuditInForEnum.ModificationType.DELETE.getKey(), "删除证书--" + dto.getCertificateName(), objectMap);
            } else {//修改
                lists.add(JSON.toJSONString(dto, SerializerFeature.WriteMapNullValue));
                Map<String, Object> oldDataMap = this.objectToMap(dtoMap.get(dto.getId()));
                Boolean aBoolean = this.handleDataMap(oldDataMap, objectMap, hrDataModificationDTO.getId(), dto.getCertificateName(), dto.getId());
                if (aBoolean) {//true 数据有修改
                    listNew.add(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue));
                    listOld.add(JSON.toJSONString(oldDataMap, SerializerFeature.WriteMapNullValue));
                }
            }
        }
        this.saveNotification(lists, hrDataModificationDTO, jwtMiniDTO, listNew, listOld, AuditInForEnum.ApplyType.CERTIFICATE.getValue());
        return ResponseUtil.buildSuccess(list);
    }

    /**
     * 资料修改
     *
     * @param list
     * @param hrDataModificationDTO
     * @param jwtMiniDTO
     */
    private void saveNotification(List<String> list, HrDataModificationDTO hrDataModificationDTO, JWTMiniDTO jwtMiniDTO, List<String> listNew, List<String> listOld, String applyTypeName) {
        if (!list.isEmpty()) {
            hrDataModificationDTO.setModificationContent(list.toString());
        }
        if (!listOld.isEmpty()) {
            hrDataModificationDTO.setModificationBefore(listOld.toString());
        }
        if (!listNew.isEmpty()) {
            hrDataModificationDTO.setApplyContent(listNew.toString());
        }
        hrDataModificationRepository.updateById(hrDataModificationMapper.toEntity(hrDataModificationDTO));
        this.hrNotificationUserService.saveRemindContent(hrDataModificationDTO.getClientId(), ServiceCenterEnum.DATA_MODIFICATION.getKey(), 1, jwtMiniDTO.getName() + "通过小程序发起了" + applyTypeName + "的资料修改申请。等待客户审核", jwtMiniDTO.getId());
        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.DATA_MODIFICATION.getKey(), hrDataModificationDTO.getId(), hrDataModificationDTO.getStaffId(), "您已发起了资料修改服务", false, null);
        hrUpcomingService.createServiceUpcoming(hrDataModificationDTO.getId(), jwtMiniDTO.getId(), "资料修改-" + jwtMiniDTO.getName() + "申请的" + applyTypeName + "资料修改申请待审核", LocalDate.now(), 0);

    }

    /**
     * 处理修改数据内容
     *
     * @param oldDataMap     数据库旧数据
     * @param newDataMap     员工修改新数据
     * @param modificationId 资料申请ID
     * @param prefix         附加信息标题前缀
     */
    private Boolean handleDataMap(Map<String, Object> oldDataMap, Map<String, Object> newDataMap, String modificationId, String prefix, String id) {
        boolean flag = false;
        for (String newMap : newDataMap.keySet()) {
            Object newValue = newDataMap.get(newMap);//前端传值
            Object oldValue = oldDataMap.get(newMap);//数据库数据
            //判断是新增还是修改
            if (newValue != null && oldValue == null) {//新值不为空---新增
                flag = true;
                this.createHrModificationContent(modificationId, AuditInForEnum.ModificationType.INSERT.getKey(), prefix + "信息--" + newMap, newMap, newValue, oldValue, id);
            } else if (oldValue != null && newValue == null) {//删除
                flag = true;
                this.createHrModificationContent(modificationId, AuditInForEnum.ModificationType.DELETE.getKey(), prefix + "信息--" + newMap, newMap, newValue, oldValue, id);
            } else if (oldValue != null) {//修改
                String oldData = oldValue.toString();
                String newData = newValue.toString();
                if (!oldData.equals(newData)) {
                    Integer key = AuditInForEnum.ModificationType.UPDATE.getKey();
                    if (StringUtils.isBlank(oldData) && StringUtils.isNotBlank(newData)) {
                        key = AuditInForEnum.ModificationType.INSERT.getKey();
                    }
                    if (StringUtils.isNotBlank(oldData) && StringUtils.isBlank(newData)) {
                        key = AuditInForEnum.ModificationType.DELETE.getKey();
                    }
                    flag = true;
                    this.createHrModificationContent(modificationId, key, prefix + "信息--" + newMap, newMap, newValue, oldValue, id);
                }
            }
        }
        return flag;
    }

    /**
     * 创建资料修改
     *
     * @param jwtMiniDTO 小程序申请员工ID
     * @param applyType  类型
     * @return
     */
    private HrDataModificationDTO saveHrDataModification(JWTMiniDTO jwtMiniDTO, Integer applyType) {
        HrDataModificationDTO hrDataModification = new HrDataModificationDTO();
        HrTalentStaff hrTalentStaff = hrTalentStaffService.getById(jwtMiniDTO.getId());
        if (hrTalentStaff.getStaffStatus().equals(StaffEnum.StaffStatusEnum.SEPARATION.getKey())) {
            throw new CommonException("离职完成后不可在进行操作");
        }
        //判断所有字段
        hrDataModification.setStaffId(jwtMiniDTO.getId());
        hrDataModification.setClientId(jwtMiniDTO.getClientId());
        hrDataModification.setApplyType(applyType);
        hrDataModification.setLastModifiedDate(LocalDateTime.now());
        return this.createHrDataModification(hrDataModification);
    }

    /**
     * 申请修改内容--修改
     *
     * @param modificationId    资料申请ID
     * @param modificationType  申请修改内容类型
     * @param modificationTitle 修改标题
     * @param newValue          新修改值
     * @param oldValue          旧修改值
     */
    private void createHrModificationContent(String modificationId, Integer modificationType, String modificationTitle, String newMap, Object newValue, Object oldValue, String id) {
        HrModificationContent hrModificationContent = new HrModificationContent();
        hrModificationContent.setInfoId(id);
        hrModificationContent.setModificationId(modificationId);
        hrModificationContent.setModificationTitle(modificationTitle);
        hrModificationContent.setModificationType(modificationType);
        JSONObject beforeObject = new JSONObject();
        beforeObject.put(newMap, oldValue);
        hrModificationContent.setModificationBefore(beforeObject.toString());

        JSONObject afterObject = new JSONObject();
        afterObject.put(newMap, newValue);
        hrModificationContent.setModificationAfter(afterObject.toString());
        hrModificationContent.setIzSingle(AuditInForEnum.IzSingleEnum.MULTIPLE.getKey());
        hrModificationContentRepository.insert(hrModificationContent);

    }

    /**
     * 申请修改内容--新增、删除
     *
     * @param modificationId    资料申请ID
     * @param modificationType  申请修改内容类型
     * @param modificationTitle 修改标题
     * @param objectMap         修改值
     */
    private void saveHrModificationContent(String modificationId, Integer modificationType, String modificationTitle, Map<String, Object> objectMap) {
        HrModificationContent hrModificationContent = new HrModificationContent();
        hrModificationContent.setModificationId(modificationId);
        hrModificationContent.setModificationTitle(modificationTitle);
        hrModificationContent.setModificationType(modificationType);
        hrModificationContent.setIzSingle(AuditInForEnum.IzSingleEnum.WHOLE.getKey());
        if (modificationType == AuditInForEnum.ModificationType.INSERT.getKey()) {//新增
            new JSONObject(objectMap);
            hrModificationContent.setModificationAfter(new JSONObject(objectMap).toString());
            hrModificationContent.setModificationBefore(new JSONObject().toString());
        } else {
            hrModificationContent.setModificationBefore(new JSONObject(objectMap).toString());
            hrModificationContent.setModificationAfter(new JSONObject().toString());
        }
        hrModificationContentRepository.insert(hrModificationContent);

    }

    /**
     * 实体对象转成Map
     *
     * @param obj 实体对象
     * @return
     */
    public Map<String, Object> objectToMap(Object obj) {
        Map<String, Object> map = new HashMap<>();
        if (obj == null) {
            return map;
        }
        Class clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                map.put(field.getName(), field.get(obj));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    /**
     * 批量审核拒绝
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity batchRejectModification(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> successList = new ArrayList<>();//成功集合
        List<String> errorStatusList = new ArrayList<>();//失败集合
        for (String applyId : batchOptDTO.getApplyIdList()) {
            HrDataModificationDTO hrDataModificationDTO = hrDataModificationRepository.findById(applyId);
            if (!hrDataModificationDTO.getApplyStatus().equals(AuditInForEnum.ApplyStatus.NOT_APPROVED.getKey())) {
                errorStatusList.add(hrDataModificationDTO.getName());
                continue;
            }
            hrDataModificationDTO.setApplyStatus(AuditInForEnum.ApplyStatus.APPROVED_REFUSE.getKey()).setSeeStatus(false);
            hrDataModificationRepository.updateById(hrDataModificationMapper.toEntity(hrDataModificationDTO));
            //添加审核信息
            hrApplyCheckerService.saveHrApplyChecker(hrDataModificationDTO.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName(),
                AuditInForEnum.CheckerResult.APPROVED_REFUSE.getKey(), batchOptDTO.getCheckerReason(), batchOptDTO.getApplyRemark());
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.DATA_MODIFICATION.getKey(), hrDataModificationDTO.getId(), hrDataModificationDTO.getStaffId(), "您发起的资料修改服务审核失败", false, null);
            hrUpcomingService.updateUpcoming(hrDataModificationDTO.getId());
            successList.add(hrDataModificationDTO.getName());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (!errorStatusList.isEmpty()) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态值不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 批量审核通过
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public ResponseEntity passModification(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> successList = new ArrayList<>();//成功集合
        List<String> errorStatusList = new ArrayList<>();//失败集合
        for (String applyId : batchOptDTO.getApplyIdList()) {
            HrDataModificationDTO hrDataModificationDTO = hrDataModificationRepository.findById(applyId);
            if (!hrDataModificationDTO.getApplyStatus().equals(AuditInForEnum.ApplyStatus.NOT_APPROVED.getKey())) {
                errorStatusList.add(hrDataModificationDTO.getName());
                continue;
            }
            hrDataModificationDTO.setApplyStatus(AuditInForEnum.ApplyStatus.APPROVED_PASS.getKey()).setSeeStatus(false);
            hrDataModificationRepository.updateById(hrDataModificationMapper.toEntity(hrDataModificationDTO));
            //添加审核信息
            hrApplyCheckerService.saveHrApplyChecker(hrDataModificationDTO.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName(),
                AuditInForEnum.CheckerResult.APPROVED_PASS.getKey(), batchOptDTO.getCheckerReason(), batchOptDTO.getApplyRemark());
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.DATA_MODIFICATION.getKey(), hrDataModificationDTO.getId(), hrDataModificationDTO.getStaffId(), "您发起的资料修改服务审核成功", false, null);
            hrUpcomingService.updateUpcoming(hrDataModificationDTO.getId());
            successList.add(hrDataModificationDTO.getName());
            switch (hrDataModificationDTO.getApplyType()) {
                case 1: //基本信息修改
                    //取出修改后内容
                    if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {//不为空修改数据
                        //查询修改内容表
                        String modificationContent = hrDataModificationDTO.getModificationContent();
                        HrTalentStaffDTO dto = JSON.parseObject(modificationContent, HrTalentStaffDTO.class);
                        HrTalentStaff hrTalentStaff = hrTalentStaffRepository.selectById(hrDataModificationDTO.getStaffId());
                        HrStaffSignCert hrStaffSignCert = this.hrStaffSignCertService.getByStaffId(hrDataModificationDTO.getStaffId());

                        List<HrModificationContent> modificationContentList = hrModificationContentRepository.selectList(new QueryWrapper<HrModificationContent>().eq("modification_id", hrDataModificationDTO.getId()));
                        for (HrModificationContent hrModificationContent : modificationContentList) {
                            String modificationAfter = hrModificationContent.getModificationAfter();
                            if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                continue;
                            }
                            HrTalentStaff hrStaff = JSON.parseObject(modificationAfter, HrTalentStaff.class);
                            hrStaff.setId(hrModificationContent.getInfoId());
                            if (!hrModificationContent.getModificationTitle().equals("基本信息--deptName")
                                && !hrModificationContent.getModificationTitle().equals("基本信息--salarySection")
                                && !hrModificationContent.getModificationTitle().equals("基本信息--workNature")
                                && !hrModificationContent.getModificationTitle().equals("基本信息--workLocation")) {
                                if (hrModificationContent.getModificationTitle().equals("基本信息--militaryDate")) {
                                    Map map = JSON.parseObject(modificationAfter, Map.class);
                                    List<String> militaryDate = (List<String>) map.get("militaryDate");
                                    militaryDate.removeAll(Collections.singleton(null));
                                    if (CollectionUtils.isNotEmpty(militaryDate)) {
                                        hrStaff.setMilitaryStartDate(LocalDate.parse(militaryDate.get(0), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                        hrStaff.setMilitaryEndDate(LocalDate.parse(militaryDate.get(1), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                        hrTalentStaffRepository.updateTalentStaff(hrStaff);
                                    }
                                } else {
                                    hrTalentStaffRepository.updateTalentStaff(hrStaff);
                                }
                                if (hrStaffSignCert != null) {
                                    // 若身份证没变,判断姓名手机号是否更改
                                    if (hrStaff.getCertificateNum() != null && hrTalentStaff.getCertificateNum().equals(hrStaff.getCertificateNum())) {
                                        // 姓名修改则更新姓名
                                        if (hrStaff.getName() != null && !hrStaff.getName().equals(hrTalentStaff.getName())) {
                                            // 更新证书主体名称
                                            this.hrStaffSignCertService.updateCertUserName(hrTalentStaff.getCertificateNum(), hrStaff.getName(), hrTalentStaff.getPhone(), hrStaffSignCert);
                                        }
                                        // 手机号修改则更新手机号
                                        if (hrStaff.getPhone() != null && !hrStaff.getPhone().equals(hrTalentStaff.getPhone())) {
                                            // 更新证书主体手机号
                                            this.hrStaffSignCertService.updateCertUserPhone(hrTalentStaff.getCertificateNum(), hrTalentStaff.getName(), hrStaff.getPhone(), hrStaffSignCert);
                                        }
                                    } else if (hrStaff.getCertificateNum() != null && !hrTalentStaff.getCertificateNum().equals(hrStaff.getCertificateNum())) {
                                        // 身份证信息改变 则删除证书数据
                                        this.hrStaffSignCertService.removeById(hrStaffSignCert.getId());
                                    }
                                }
                            }
                            //在职信息数据
                            HrStaffWorkExperience workExperience = JSON.parseObject(modificationAfter, HrStaffWorkExperience.class);
                            workExperience.setId(dto.getJobId()).setIzDefault(true);
                            hrStaffWorkExperienceService.updateById(workExperience);
                        }
                    }
                    break;
                default:
                    //查询对应的修改内容--新增/删除
                    List<HrModificationContent> modificationContentList = hrModificationContentRepository.selectList(new QueryWrapper<HrModificationContent>()
                        .eq("modification_id", hrDataModificationDTO.getId()).ne("modification_type", AuditInForEnum.ModificationType.UPDATE.getKey()));

                    List<HrModificationContent> list = hrModificationContentRepository.selectList(new QueryWrapper<HrModificationContent>()
                        .eq("modification_id", hrDataModificationDTO.getId()).eq("modification_type", AuditInForEnum.ModificationType.UPDATE.getKey()));
                    switch (hrDataModificationDTO.getApplyType()) {
                        case 2: //工作经历
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffWorkExperience entry = JSON.parseObject(modificationAfter, HrStaffWorkExperience.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffWorkExperienceService.updateById(entry);

                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modification = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modification = hrModificationContent.getModificationAfter();
                                    } else {
                                        modification = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffWorkExperience hrStaffWorkExperience = JSON.parseObject(modification, HrStaffWorkExperience.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffWorkExperienceService.save(hrStaffWorkExperience);
                                    } else {
                                        hrStaffWorkExperienceService.removeById(hrStaffWorkExperience.getId());
                                    }
                                }
                            }
                            break;
                        case 3://教育经历
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffEducation entry = JSON.parseObject(modificationAfter, HrStaffEducation.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffEducationService.updateById(entry);
                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modificationAfter = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modificationAfter = hrModificationContent.getModificationAfter();
                                    } else {
                                        modificationAfter = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffEducation entry = JSON.parseObject(modificationAfter, HrStaffEducation.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffEducationService.save(entry);
                                    } else {
                                        hrStaffEducationService.removeById(entry.getId());
                                    }
                                }
                            }
                            break;
                        case 4://应试经历
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffInterview entry = JSON.parseObject(modificationAfter, HrStaffInterview.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffInterviewService.updateById(entry);
                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modificationAfter = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modificationAfter = hrModificationContent.getModificationAfter();
                                    } else {
                                        modificationAfter = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffInterview entry = JSON.parseObject(modificationAfter, HrStaffInterview.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffInterviewService.save(entry);
                                    } else {
                                        hrStaffInterviewService.removeById(entry.getId());
                                    }
                                }
                            }
                            break;
                        case 5://家庭成员
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffFamily entry = JSON.parseObject(modificationAfter, HrStaffFamily.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffFamilyService.updateById(entry);
                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modificationAfter = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modificationAfter = hrModificationContent.getModificationAfter();
                                    } else {
                                        modificationAfter = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffFamily entry = JSON.parseObject(modificationAfter, HrStaffFamily.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffFamilyService.save(entry);
                                    } else {
                                        hrStaffFamilyService.removeById(entry.getId());
                                    }
                                }
                            }
                            break;
                        case 6://紧急联系人
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffContacts entry = JSON.parseObject(modificationAfter, HrStaffContacts.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffContactsService.updateById(entry);
                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modificationAfter = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modificationAfter = hrModificationContent.getModificationAfter();
                                    } else {
                                        modificationAfter = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffContacts entry = JSON.parseObject(modificationAfter, HrStaffContacts.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffContactsService.save(entry);
                                    } else {
                                        hrStaffContactsService.removeById(entry.getId());
                                    }
                                }
                            }
                            break;
                        case 7://语言能力
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffLanguage entry = JSON.parseObject(modificationAfter, HrStaffLanguage.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffLanguageService.updateById(entry);
                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modificationAfter = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modificationAfter = hrModificationContent.getModificationAfter();
                                    } else {
                                        modificationAfter = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffLanguage entry = JSON.parseObject(modificationAfter, HrStaffLanguage.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffLanguageService.save(entry);
                                    } else {
                                        hrStaffLanguageService.removeById(entry.getId());
                                    }
                                }
                            }
                            break;
                        case 8: //专业能力
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffProfession entry = JSON.parseObject(modificationAfter, HrStaffProfession.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffProfessionService.updateById(entry);
                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modificationAfter = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modificationAfter = hrModificationContent.getModificationAfter();
                                    } else {
                                        modificationAfter = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffProfession entry = JSON.parseObject(modificationAfter, HrStaffProfession.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffProfessionService.save(entry);
                                    } else {
                                        hrStaffProfessionService.removeById(entry.getId());
                                    }
                                }
                            }
                            break;
                        case 9: //职业(工种)资格
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffQualification entry = JSON.parseObject(modificationAfter, HrStaffQualification.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffQualificationService.updateById(entry);
                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modificationAfter = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modificationAfter = hrModificationContent.getModificationAfter();
                                    } else {
                                        modificationAfter = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffQualification entry = JSON.parseObject(modificationAfter, HrStaffQualification.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffQualificationService.save(entry);
                                    } else {
                                        hrStaffQualificationService.removeById(entry.getId());
                                    }
                                }
                            }
                            break;
                        case 10: //职业技术能力
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffTechnique entry = JSON.parseObject(modificationAfter, HrStaffTechnique.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffTechniqueService.updateById(entry);
                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modificationAfter = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modificationAfter = hrModificationContent.getModificationAfter();
                                    } else {
                                        modificationAfter = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffTechnique entry = JSON.parseObject(modificationAfter, HrStaffTechnique.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffTechniqueService.save(entry);
                                    } else {
                                        hrStaffTechniqueService.removeById(entry.getId());
                                    }
                                }
                            }
                            break;
                        case 11:  //证书
                            if (StringUtils.isNotBlank(hrDataModificationDTO.getModificationContent())) {
                                for (HrModificationContent hrModificationContent : list) {
                                    String modificationAfter = hrModificationContent.getModificationAfter();
                                    if (hrModificationContent.getModificationTitle().contains("Label") || modificationAfter.equals("{}")) {
                                        continue;
                                    }
                                    HrStaffCertificate entry = JSON.parseObject(modificationAfter, HrStaffCertificate.class);
                                    entry.setId(hrModificationContent.getInfoId());
                                    hrStaffCertificateService.updateById(entry);
                                }
                            }
                            if (!modificationContentList.isEmpty()) {
                                for (HrModificationContent hrModificationContent : modificationContentList) {
                                    String modificationAfter = "";
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        modificationAfter = hrModificationContent.getModificationAfter();
                                    } else {
                                        modificationAfter = hrModificationContent.getModificationBefore();
                                    }
                                    HrStaffCertificate entry = JSON.parseObject(modificationAfter, HrStaffCertificate.class);
                                    //是新增还是删除
                                    if (hrModificationContent.getModificationType().equals(AuditInForEnum.ModificationType.INSERT.getKey())) {
                                        hrStaffCertificateService.save(entry);
                                    } else {
                                        hrStaffCertificateService.removeById(entry.getId());
                                    }
                                }
                            }
                            break;
                        case 12: // 工资卡
                            Map<String, String> updateMap = new HashMap<>();
                            if (!modificationContentList.isEmpty()) {
                                modificationContentList.forEach(ls -> {
                                    Map map = JSON.parseObject(ls.getModificationAfter(), Map.class);
                                    updateMap.putAll(map);
                                });
                            }
                            if (!list.isEmpty()) {
                                list.forEach(ls -> {
                                    Map map = JSON.parseObject(ls.getModificationAfter(), Map.class);
                                    updateMap.putAll(map);
                                });
                            }
                            HrStaffEmolument hrStaffEmolument = new HrStaffEmolument();
                            hrStaffEmolument.setStaffId(hrDataModificationDTO.getStaffId());
                            hrStaffEmolument.setOwnedBank(updateMap.get("ownedBank"));
                            hrStaffEmolument.setSalaryCardNum(updateMap.get("salaryCardNum"));
                            hrStaffEmolument.setBankCardUrl(updateMap.get("bankCardUrl"));
                            this.hrStaffEmolumentService.updateStaffEmolumentByStaffId(hrStaffEmolument);
                            break;
                    }
                    break;
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (!errorStatusList.isEmpty()) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态值不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    /**
     * 资料修改申请记录
     *
     * @return
     */
    @Override
    public List<HrDataModificationDTO> dataApplicationRecord() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        List<HrDataModification> hrDataModifications = hrDataModificationRepository.selectList(new QueryWrapper<HrDataModification>()
            .eq("staff_id", jwtMiniDTO.getId()).eq("client_id", jwtMiniDTO.getClientId()).orderByDesc("created_date"));
        List<HrDataModificationDTO> hrDataModificationDTOS = hrDataModificationMapper.toDto(hrDataModifications);
        hrDataModificationDTOS.forEach(this::setDict);
        return hrDataModificationDTOS;
    }

    /**
     * 资料申请记录--詳情
     *
     * @param id 记录详情
     * @return
     */
    @Override
    public ResponseEntity dataApplicationRecordDetails(String id) {
        HrDataModification hrDataModification = hrDataModificationRepository.selectById(id);
        if (!hrDataModification.getSeeStatus()) {
            hrDataModification.setSeeStatus(true);
            hrDataModificationRepository.updateById(hrDataModification);
        }
        List<Object> beforeList = new ArrayList<>();
        List<Object> afterList = new ArrayList<>();
        if (hrDataModification.getApplyType().equals(AuditInForEnum.ApplyType.ESSENTIAL_INFOR.getKey()) || hrDataModification.getApplyType().equals(AuditInForEnum.ApplyType.SALARY_BANK.getKey())) {//基本信息
            JSONObject afterObject = JSON.parseObject(hrDataModification.getApplyContent());//修改后内容
            if (afterObject != null) {
                // 处理通信地址省市区
                this.dealContactAddressJson(afterObject);
                afterList.add(afterObject);
            }
            JSONObject beforeObject = JSON.parseObject(hrDataModification.getModificationBefore());//修改前内容
            if (beforeObject != null) {
                // 处理通信地址省市区
                this.dealContactAddressJson(beforeObject);
                beforeList.add(beforeObject);
            }
        } else {
            beforeList = JSON.parseArray(hrDataModification.getModificationBefore(), Object.class);//修改前
            List<Object> objects = JSON.parseArray(hrDataModification.getApplyContent(), Object.class);
            if (objects != null) {
                afterList = objects;//修改后
            }
        }
        //查询审核信息
        List<HrApplyCheckerDTO> applyCheckList = hrApplyCheckerRepository.findApplyCheckList(hrDataModification.getId());
        if (!applyCheckList.isEmpty()) {
            for (HrApplyCheckerDTO hrApplyCheckerDTO : applyCheckList) {
                if (hrApplyCheckerDTO.getCheckerResult() != null) {
                    hrApplyCheckerDTO.setCheckerResultLabel(AuditInForEnum.CheckerResult.getValueByKey(hrApplyCheckerDTO.getCheckerResult()));
                }
            }
        }
        JSONObject json = new JSONObject();
        json.put("beforeList", beforeList);
        json.put("afterList", afterList);
        json.put("applyCheckList", applyCheckList);
        json.put("applyTypeLabel", AuditInForEnum.ApplyType.getValueByKey(hrDataModification.getApplyType()));
        return ResponseUtil.buildSuccess(json);
    }

    private void dealContactAddressJson(JSONObject afterObject) {
        if (afterObject.getString("contactAddress") != null) {
            String contactAddress = afterObject.getString("contactAddress");
            Map<String, String> addressMap = StringUtil.addressResolution(contactAddress);
            if (addressMap != null && addressMap.size() > 0) {
                // 省市区
                String province = addressMap.get("province");
                String city = addressMap.get("city");
                String county = addressMap.get("county");
                String addressPrefix = (province == null ? "" : province) + "/" + (city == null ? "" : city) + "/" + (county == null ? "" : county);
                // 详细地址
                String town = addressMap.get("town");
                String village = addressMap.get("village");
                String houseNumber = addressMap.get("houseNumber");
                String addressDetail = (town == null ? "" : town) + (village == null ? "" : village) + (houseNumber == null ? "" : houseNumber);
                afterObject.put("contactAddressPrefix", addressPrefix);
                afterObject.put("contactAddressDetail", addressDetail);
            } else {
                afterObject.put("contactAddressDetail", contactAddress);
            }
        }
    }

    /**
     * 资料修改申请记录未读数量
     *
     * @return 未读数量
     */
    @Override
    public int dataApplicationUnreadNumber() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        return hrDataModificationRepository.selectCount(new QueryWrapper<HrDataModification>()
            .eq("staff_id", jwtMiniDTO.getId()).eq("client_id", jwtMiniDTO.getClientId()).eq("see_status", 0));
    }
}
