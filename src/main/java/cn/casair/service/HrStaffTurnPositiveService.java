package cn.casair.service;

import cn.casair.domain.HrStaffTurnPositive;
import cn.casair.dto.HrStaffTurnPositiveDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

/**
 * 员工转正（客户pc）服务类
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
public interface HrStaffTurnPositiveService extends IService<HrStaffTurnPositive> {


    /**
     * 创建员工转正（客户pc）
     *
     * @param hrStaffTurnPositiveDTO
     * @return
     */
    HrStaffTurnPositiveDTO createHrStaffTurnPositive(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO) throws InterruptedException;

    /**
     * 修改员工转正（客户pc）
     *
     * @param hrStaffTurnPositiveDTO
     * @return
     */
    Optional<HrStaffTurnPositiveDTO> updateHrStaffTurnPositive(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO);

    /**
     * 查询员工转正（客户pc）详情
     *
     * @param
     * @return
     */
    HrStaffTurnPositiveDTO getHrStaffTurnPositive(String staffId);

    /**
     * 删除员工转正（客户pc）
     *
     * @param id
     */
    void deleteHrStaffTurnPositive(String id);

    /**
     * 批量删除员工转正（客户pc）
     *
     * @param ids
     */
    void deleteHrStaffTurnPositive(List<String> ids);

    /**
     * 分页查询员工转正（客户pc）
     *
     * @param hrStaffTurnPositiveDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO, Long pageNumber, Long pageSize);

    /**
     * 导出
     *
     * @param hrStaffTurnPositiveDTO
     * @return
     */
    String export(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO);

    ResponseEntity<?> uodateHrStaffTurnBatchConsent(List<HrStaffTurnPositiveDTO> hrStaffTurnPositiveDTO, Integer status) throws InterruptedException;

    HrStaffTurnPositiveDTO staffTurnPositives(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO);

    List<HrStaffTurnPositiveDTO> getStaffTurnPositives(String staffId);

    HrStaffTurnPositiveDTO getStaffTurnPositivesProcess(String staffId, String serviceId);

    HrStaffTurnPositiveDTO staffTurnPositivestime(String staffId);

    /**
     * (PC)创建员工转正申请
     *
     * @param hrStaffTurnPositiveDTO
     * <AUTHOR> Lyric.Lin 2022/12/30 12:42
     */
    HrStaffTurnPositiveDTO createStaffTurnPositiveApply(HrStaffTurnPositiveDTO hrStaffTurnPositiveDTO);
}
