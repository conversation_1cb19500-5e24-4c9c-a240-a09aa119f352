package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.domain.HrNotificationMessage;
import cn.casair.domain.HrNotificationUser;
import cn.casair.dto.HrNotificationMessageDTO;
import cn.casair.dto.HrNotificationUserDTO;
import cn.casair.dto.HrProtocolDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.mapper.HrNotificationMessageMapper;
import cn.casair.mapper.HrNotificationUserMapper;
import cn.casair.repository.HrNotificationMessageRepository;
import cn.casair.repository.HrNotificationUserRepository;
import cn.casair.service.HrNotificationMessageService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 通知消息服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrNotificationMessageServiceImpl extends ServiceImpl<HrNotificationMessageRepository, HrNotificationMessage> implements HrNotificationMessageService {

    private static final Logger log = LoggerFactory.getLogger(HrNotificationMessageServiceImpl.class);
    private final HrNotificationUserRepository hrNotificationUserRepository;
    private final HrNotificationMessageRepository hrNotificationMessageRepository;
    private final HrNotificationUserMapper hrNotificationUserMapper;
    private final SysOperLogService sysOperLogService;

    private final HrNotificationMessageMapper hrNotificationMessageMapper;

    public HrNotificationMessageServiceImpl(HrNotificationUserRepository hrNotificationUserRepository, HrNotificationMessageRepository hrNotificationMessageRepository, HrNotificationUserMapper hrNotificationUserMapper, SysOperLogService sysOperLogService, HrNotificationMessageMapper hrNotificationMessageMapper) {
        this.hrNotificationUserRepository = hrNotificationUserRepository;
        this.hrNotificationMessageRepository = hrNotificationMessageRepository;
        this.hrNotificationUserMapper = hrNotificationUserMapper;
        this.sysOperLogService = sysOperLogService;
        this.hrNotificationMessageMapper = hrNotificationMessageMapper;
    }

    /**
     * 创建通知消息
     *
     * @param
     * @return
     */
    @Override
    public HrNotificationUserDTO createHrNotificationMessage(HrNotificationUserDTO hrNotificationUserDTO) {
        log.info("Create new HrNotificationMessage:{}", hrNotificationUserDTO);
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrNotificationUser hrNotificationUser = this.hrNotificationUserMapper.toEntity(hrNotificationUserDTO);
        hrNotificationUser.setUserId(jwtUserDTO.getId());
        for (String s : hrNotificationUserDTO.getNotificationContentIdList()) {
            hrNotificationUser.setId(null);
            hrNotificationUser.setNotificationContentId(s);

            this.hrNotificationUserRepository.insert(hrNotificationUser);
        }

        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.NOTICE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrNotificationUserDTO),
            HrNotificationUserDTO.class,
            null,
            JSON.toJSONString(hrNotificationUser)
        );
        return this.hrNotificationUserMapper.toDto(hrNotificationUser);


    }

    /**
     * 修改通知消息
     *
     * @param
     * @return
     */
    @Override
    public Optional<HrNotificationUserDTO> updateHrNotificationMessage(HrNotificationUserDTO hrNotificationUserDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrNotificationUser hrNotificationUser = this.hrNotificationUserMapper.toEntity(hrNotificationUserDTO);
        hrNotificationUser.setUserId(jwtUserDTO.getId());
        this.hrNotificationUserRepository.deleteUserid(hrNotificationUser);
        for (String s : hrNotificationUserDTO.getNotificationContentIdList()) {
            hrNotificationUser.setId(null);
            hrNotificationUser.setNotificationContentId(s);
            this.hrNotificationUserRepository.insert(hrNotificationUser);
        }
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.NOTICE.getValue(),
            BusinessTypeEnum.UPDATE.getKey(),
            JSON.toJSONString(hrNotificationUserDTO),
            HrNotificationUserDTO.class,
            null,
            JSON.toJSONString(hrNotificationUser)
        );
        return null;
    }

    /**
     * 查询通知消息详情
     *
     * @param id
     * @return
     */
    @Override
    public HrNotificationMessageDTO getHrNotificationMessage(String id) {
        log.info("Get HrNotificationMessage :{}", id);
        HrNotificationMessageDTO hrNotificationMessage = new HrNotificationMessageDTO();
        HrNotificationMessageDTO hrNotificationMessageDTOs = new HrNotificationMessageDTO();
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        hrNotificationMessageDTOs.setUserId(jwtUserDTO.getId());
        hrNotificationMessageDTOs.setNotificationId(id);
        hrNotificationMessage = this.hrNotificationMessageRepository.selectHrNotificationUser(hrNotificationMessageDTOs);
        if (hrNotificationMessage != null) {
            List<String> ListNotificationId = Arrays.asList(hrNotificationMessage.getReminderContent().split(","));
            hrNotificationMessage.setReminderContentid(ListNotificationId);
            return hrNotificationMessage;
        } else {
            HrNotificationMessageDTO hrNotificationMessages = new HrNotificationMessageDTO();
            return hrNotificationMessages;
        }


    }

    /**
     * 删除通知消息
     *
     * @param id
     */
    @Override
    public void deleteHrNotificationMessage(String id) {
        Optional.ofNullable(this.hrNotificationMessageRepository.selectById(id))
            .ifPresent(hrNotificationMessage -> {
                this.hrNotificationMessageRepository.deleteById(id);
                log.info("Delete HrNotificationMessage:{}", hrNotificationMessage);
            });
    }

    /**
     * 批量删除通知消息
     *
     * @param ids
     */
    @Override
    public void deleteHrNotificationMessage(List<String> ids) {
        log.info("Delete HrNotificationMessages:{}", ids);
        this.hrNotificationMessageRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询通知消息
     *
     * @param hrNotificationMessageDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrNotificationMessageDTO hrNotificationMessageDTO, Long pageNumber, Long pageSize) {
        if (hrNotificationMessageDTO.getField() != null) {
            if (hrNotificationMessageDTO.getField().equals("notification_name")) {
                hrNotificationMessageDTO.setField("notificationName");
            }
            if (hrNotificationMessageDTO.getField().equals("reminder_content")) {
                hrNotificationMessageDTO.setField("reminderContent");
            }
            if (hrNotificationMessageDTO.getField().equals("reminder_method")) {
                hrNotificationMessageDTO.setField("reminderMethod");
            }
            if (hrNotificationMessageDTO.getField().equals("states")) {
                hrNotificationMessageDTO.setField("states");
            }
        }
        Page<HrNotificationMessage> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrNotificationMessage> qw = new QueryWrapper<>();
        qw.orderByDesc("id");
        //token获取当前用户的id（客户PC）
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<HrNotificationMessageDTO> hrNotificationMessageDTOS = this.hrNotificationMessageRepository.selectUserId(jwtUserDTO.getId());
        if (hrNotificationMessageDTOS.size() != 0) {
            hrNotificationMessageDTO.setUserId(jwtUserDTO.getId());
        }
        if (hrNotificationMessageDTO.getReminderMethodList() != null){
            if ( hrNotificationMessageDTO.getReminderMethodList().size() != 0) {
                return this.getReminderMethod(hrNotificationMessageDTO, pageNumber, pageSize);
            }
        }


        IPage<HrNotificationMessageDTO> iPage = this.hrNotificationMessageRepository.Page(page, hrNotificationMessageDTO);


        return iPage;
    }

    private IPage getReminderMethod(HrNotificationMessageDTO hrNotificationMessageDTO, Long pageNumber, Long pageSize) {
        Page<HrNotificationMessage> page = new Page<>(pageNumber, pageSize);
        IPage<HrNotificationMessageDTO> iPage = this.hrNotificationMessageRepository.getPageReminderMethod(page, hrNotificationMessageDTO);
        return iPage;
    }


    //批量启用或者禁用
    @Override
    public void updeteHrNotificationMessage(List<HrNotificationMessageDTO> ids) {
        //token获取当前用户的id（客户PC）
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        for (HrNotificationMessageDTO hrNotificationMessageDTO : ids) {
            hrNotificationMessageDTO.setUserId(jwtUserDTO.getId());
            this.hrNotificationMessageRepository.updeteHrNotificationMessage(hrNotificationMessageDTO);
        }

    }

    //查询根据通知id获取全部的提醒内容
    @Override
    public List<HrNotificationMessageDTO> getHrNotificationMessageSelectContent(String id) {
        List<HrNotificationMessageDTO> hrNotificationMessageDTOS = this.hrNotificationMessageRepository.getHrNotificationMessageSelectContent(id);
        return hrNotificationMessageDTOS;
    }
}
