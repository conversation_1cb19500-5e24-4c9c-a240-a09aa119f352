package cn.casair.service;

import cn.casair.domain.*;
import cn.casair.dto.ContractAppendixDownloadDTO;
import cn.casair.dto.ElectricSignDTO;
import cn.casair.dto.HrContractAppendixDTO;
import cn.casair.dto.HrContractDTO;
import cn.casair.dto.formdata.TemplateDealParamsDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 合同列表服务类
 *
 * <AUTHOR>
 * @since 2021-09-09
 */
public interface HrContractService extends IService<HrContract> {

    /**
     * 手动处理合同新签旧数据
     *
     * @param hrContractDTO
     */
    void confirmationTelegramForNew(HrContractDTO hrContractDTO);

    /**
     * 根据最终合同信息更新员工相关信息
     *
     * @param hrContract
     * @param applyStaffId
     */
    void updateStaffRelatedInfo(HrContract hrContract, String applyStaffId);

    /**
     * 为前端处理电签合同项目显示列表
     *
     * @param hrContractAppendices
     * @param params
     */
    void dealContractAppendixList(List<HrContractAppendix> hrContractAppendices, TemplateDealParamsDTO params);

    /**
     * 保存人脸识别上送身份中间信息
     *
     * @param params
     * @return void
     * <AUTHOR>
     * @date 2022/1/12
     **/
    void saveFaceVerifyInfo(Map<String, Object> params);

    /**
     * 更新电签合同必传可传附件
     *
     * @param params
     * @return void
     * <AUTHOR>
     * @date 2021/9/30
     **/
    TemplateDealParamsDTO updateContractAppendix(TemplateDealParamsDTO params);

    /**
     * 发起电签合同项附件修改
     *
     * @param params
     * @return void
     * <AUTHOR>
     * @date 2021/9/29
     **/
    void contractAppendixUpdate(Map<String, Object> params);

    /**
     * 合同项 删除
     *
     * @param ids
     * @return int
     * <AUTHOR>
     * @date 2021/9/18
     **/
    int appendixDelete(List<String> ids);

    /**
     * 合同项确定按钮
     *
     * @param ids 合同项id组
     * @return void
     * <AUTHOR>
     * @date 2021/9/18
     **/
    void changeContractAppendixState(List<String> ids);

    /**
     * 一键生成-确定
     *
     * @param hrContractAppendixDTO
     * @return void
     * <AUTHOR>
     * @date 2021/9/17
     **/
    void generateContractConfirmation(HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 合同模板预处理
     *
     * @param params
     * @return void
     * <AUTHOR>
     * @date 2021/9/16
     **/
    TemplateDealParamsDTO templatePretreatment(TemplateDealParamsDTO params);

    /**
     * 获取员工待签订劳动合同列表
     *
     * @return java.util.List<cn.casair.dto.HrContractAppendixDTO>
     * <AUTHOR>
     * @date 2021/9/15
     * @param contractId
     */
    Map<String, Object> getStaffContractList(String contractId);

    /**
     * 确认电签
     *
     * @param hrContractDTO
     * @return void
     * <AUTHOR>
     * @date 2021/9/15
     **/
    void confirmationTelegram(HrContractDTO hrContractDTO);

    /**
     * 电签审核
     *
     * @param hrContractAppendixDTO
     * @return void
     * <AUTHOR>
     * @date 2021/9/14
     **/
    void checkContractAppendix(HrContractAppendixDTO hrContractAppendixDTO);

    /**
     * 获取员工确认电签合同列表
     *
     * @param clientId
     * @param staffId
     * @return java.util.List<cn.casair.dto.HrContractAppendixDTO>
     * <AUTHOR>
     * @date 2021/9/14
     **/
    List<HrContractAppendixDTO> getConfirmationTelegramList(String clientId, String staffId);

    /**
     * 确认发送电签
     *
     * @param electricSignDTO
     * @return void
     * <AUTHOR>
     * @date 2021/9/13
     **/
    void sendStaffContract(ElectricSignDTO electricSignDTO);

    /**
     * 员工劳动合同批量导入
     *
     * @param file Excel文件
     * @return cn.casair.common.utils.excel.ImportResultDTO
     * <AUTHOR>
     * @date 2021/9/12
     **/
    String importStaffContract(MultipartFile file);

    /**
     * 下载员工合同批量导入模板
     *
     * @return void
     * <AUTHOR>
     * @date 2021/9/12
     **/
    String downloadContractExportTemplate();

    /**
     * 获取最近10个历史合同
     *
     * @param params
     * @return java.util.List<cn.casair.dto.HrContractDTO>
     * <AUTHOR>
     * @date 2021/9/10
     */
    List<HrContractDTO> historyContract(Map<String, Object> params);

    /**
     * 定时任务更改员工合同状态
     *
     * @return void
     * <AUTHOR>
     * @date 2021/9/9
     **/
    void scheduleTaskToUpdateContractState();

    /**
     * 合同列表导出
     *
     * @param hrContractDTO
     * @return void
     * <AUTHOR>
     * @date 2021/9/9
     **/
    String exportContract(HrContractDTO hrContractDTO);

    /**
     * 创建合同列表
     *
     * @param hrContractDTO
     * @return
     */
    HrContractDTO createHrContract(HrContractDTO hrContractDTO);

    /**
     * 修改合同列表
     *
     * @param hrContractDTO
     * @return
     */
    Optional<HrContractDTO> updateHrContract(HrContractDTO hrContractDTO);

    /**
     * 查询合同列表详情
     *
     * @param id
     * @return
     */
    HrContractDTO getHrContract(String id);

    /**
     * 删除合同列表
     *
     * @param id
     */
    void deleteHrContract(String id);

    /**
     * 批量删除合同列表
     *
     * @param ids
     */
    void deleteHrContract(List<String> ids);

    /**
     * 分页查询合同列表
     *
     * @param hrContractDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrContractDTO> findPage(HrContractDTO hrContractDTO, Long pageNumber, Long pageSize);

    /**
     * 获取员工最新的劳动合同项
     *
     * @param contractId
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/9/30
     **/
    ContractAppendixDownloadDTO getNewestLaborContract(String contractId);

    /**
     * 劳动合同批量下载
     *
     * @param hrContractDTO
     * @return List
     * <AUTHOR>
     * @date 2021/9/30
     **/
    List<ContractAppendixDownloadDTO> downloadContractBatch(HrContractDTO hrContractDTO);

    /**
     * 处理模板合同生成
     *
     * @param hrContract         合同信息
     * @param contractTemplateId 合同模板id
     * @param flag               是否允许修改合同开始时间
     **/
    void dealContractTemplate(HrContract hrContract, String contractTemplateId, boolean flag);

    /**
     * 我的合同-查看合同信息
     * @param contractId 合同Id
     * @return
     */
    Map<String, Object> getContractInfo(String contractId);

    /**
     * 处理模板合同生成
     * @param hrContract         合同信息
     * @param hrContractTemplate 合同模板
     * @return void
     **/
    HrContractAppendixDTO dealContractTemplateExtend(HrContract hrContract, HrContractTemplate hrContractTemplate, boolean flag);

    /**
     * 入职合同作废
     * @param hrContractDTO 审核合同信息
     * @param hrContract 合同信息
     * @param hrApplyEntryStaff 入职信息
     * @param hrContractAppendixDTOList
     */
    void toVoidContract(HrContractDTO hrContractDTO, HrContract hrContract, HrApplyEntryStaff hrApplyEntryStaff, List<HrContractAppendixDTO> hrContractAppendixDTOList);

    /**
     * 入职合同通过
     * @param hrContract 合同信息
     * @param hrApplyEntryStaff 入职信息
     * @param hrTalentStaff 员工信息
     */
    void passContract(HrContract hrContract, HrApplyEntryStaff hrApplyEntryStaff, HrTalentStaff hrTalentStaff);

}
