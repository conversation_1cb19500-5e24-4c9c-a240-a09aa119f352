package cn.casair.service;
import cn.casair.domain.HrAccumulationFund;
import cn.casair.dto.HrAccumulationFundDTO;
import cn.casair.dto.HrClientDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 公积金类型管理服务类
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
public interface HrAccumulationFundService extends IService<HrAccumulationFund> {

    /**
     * 公积金类型下拉列表数据
     *
     * @return java.util.List<cn.casair.dto.HrAccumulationFundDTO>
     * <AUTHOR>
     * @date 2021/10/18
     **/
    List<HrAccumulationFundDTO> getAccumulationFundTypeList();

    /**
     * 创建公积金类型管理
     * @param hrAccumulationFundDTO
     * @return
     */
    HrAccumulationFundDTO createHrAccumulationFund(HrAccumulationFundDTO hrAccumulationFundDTO);

    /**
     * 修改公积金类型管理
     * @param hrAccumulationFundDTO
     * @return
     */
    Optional<List<String>> updateHrAccumulationFund(HrAccumulationFundDTO hrAccumulationFundDTO);

    /**
     * 查询公积金类型管理详情
     * @param id
     * @return
     */
    HrAccumulationFundDTO getHrAccumulationFund(String id);

    /**
     * 批量删除公积金类型管理
     * @param ids
     */
    void deleteHrAccumulationFund(List<String> ids);

    /**
     * 分页查询公积金类型管理
     * @param hrAccumulationFundDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrAccumulationFundDTO hrAccumulationFundDTO,Long pageNumber,Long pageSize);


    /**
     * POST /hr-accumulation-funds/page
     * <p>
     * 分页查询公积金类型管理下所有客户
     *
     * @param hrClientDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrClientDTO> findClientPage(HrClientDTO hrClientDTO, Long pageNumber, Long pageSize);

    String exportAccumulation(HrAccumulationFundDTO hrAccumulationFundDTO);

    String importAccumulation(MultipartFile file);

    String importAccumulationTemplate(HttpServletResponse response);
}
