package cn.casair.service;

import cn.casair.domain.HrRemoteMedicalRecord;
import cn.casair.dto.HrRemoteMedicalRecordDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 异地医疗备案记录服务类
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface HrRemoteMedicalRecordService extends IService<HrRemoteMedicalRecord> {


    /**
     * 创建异地医疗备案记录
     *
     * @param hrRemoteMedicalRecordDTO
     * @param requestFromWx 是否请求自微信小程序
     * @return
     */
    HrRemoteMedicalRecordDTO createHrRemoteMedicalRecord(HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO, Boolean requestFromWx);

    /**
     * 修改异地医疗备案记录
     *
     * @param hrRemoteMedicalRecordDTO
     * @return
     */
    Optional<HrRemoteMedicalRecordDTO> updateHrRemoteMedicalRecord(HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO);

    /**
     * 查询异地医疗备案记录详情
     *
     * @param id
     * @return
     */
    HrRemoteMedicalRecordDTO getHrRemoteMedicalRecord(String id);

    /**
     * 删除异地医疗备案记录
     *
     * @param id
     */
    void deleteHrRemoteMedicalRecord(String id);

    /**
     * 批量删除异地医疗备案记录
     *
     * @param ids
     */
    void deleteHrRemoteMedicalRecord(List<String> ids);

    /**
     * 分页查询异地医疗备案记录
     *
     * @param hrRemoteMedicalRecordDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO, Long pageNumber, Long pageSize);

    /**
     * @param hrRemoteMedicalRecordDTO
     * @return
     */
    String export(HrRemoteMedicalRecordDTO hrRemoteMedicalRecordDTO);

    /**
     * 不分页查询异地医疗备案记录
     *
     * @return
     */
    List<HrRemoteMedicalRecordDTO> findList();

}
