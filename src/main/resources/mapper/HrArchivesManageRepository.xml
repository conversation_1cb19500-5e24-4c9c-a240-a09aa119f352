<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrArchivesManageRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, staff_id, client_id, archives_num, archives_name, archives_type, archives_local, archives_status, warehouse_time,
        is_delete, created_by, last_modified_by, created_date, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrArchivesManage">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="archives_num" property="archivesNum"/>
        <result column="archives_name" property="archivesName"/>
        <result column="archives_type" property="archivesType"/>
        <result column="archives_local" property="archivesLocal"/>
        <result column="archives_status" property="archivesStatus"/>
        <result column="warehouse_time" property="warehouseTime"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <resultMap id="ArchivesManager" type="cn.casair.dto.HrArchivesManageDTO">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="archives_num" property="archivesNum"/>
        <result column="unit_number" property="unitNumber"/>
        <result column="empUnit" property="empUnit"/>
        <result column="system_num" property="systemNum"/>
        <result column="certificate_num" property="certificateNum"/>
        <result column="phone" property="phone"/>
        <result column="archives_name" property="archivesName"/>
        <result column="archives_type" property="archivesType"/>
        <result column="archives_local" property="archivesLocal"/>
        <result column="archives_status" property="archivesStatus"/>
        <result column="warehouse_time" property="warehouseTime"/>
        <result column="created_by" property="createdBy"/>
        <collection property="hrArchivesDetailList" javaType="java.util.ArrayList" ofType="cn.casair.dto.HrArchivesDetailDTO">
            <result column="detailId" property="id"/>
            <result column="archives_id" property="archivesId"/>
            <result column="detailName" property="name"/>
            <result column="state" property="state"/>
            <result column="type" property="type"/>
            <collection property="appendixList" javaType="java.util.ArrayList" ofType="cn.casair.dto.HrAppendixDTO">
                <result column="appendixId" property="id"/>
                <result column="appendixName" property="name"/>
                <result column="origin_name" property="originName"/>
                <result column="file_type" property="fileType"/>
                <result column="file_url" property="fileUrl"/>
            </collection>
        </collection>
    </resultMap>

    <resultMap id="ArchivesManagerList" type="cn.casair.dto.HrArchivesManageDTO">
        <id column="id" property="id"/>
        <result column="staff_id" property="staffId"/>
        <result column="client_id" property="clientId"/>
        <result column="archives_num" property="archivesNum"/>
        <result column="unit_number" property="unitNumber"/>
        <result column="empUnit" property="empUnit"/>
        <result column="system_num" property="systemNum"/>
        <result column="certificate_num" property="certificateNum"/>
        <result column="phone" property="phone"/>
        <result column="archives_name" property="archivesName"/>
        <result column="archives_type" property="archivesType"/>
        <result column="archives_local" property="archivesLocal"/>
        <result column="archives_status" property="archivesStatus"/>
        <result column="warehouse_time" property="warehouseTime"/>
        <result column="created_by" property="createdBy"/>
    </resultMap>

    <update id="updateStateByIds">
        UPDATE hr_archives_manage
        SET archives_status = #{state}
        WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <select id="selectCountByArchivesNum" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM hr_archives_manage
        WHERE archives_num = #{archivesNum}
          AND is_delete = 0
    </select>

    <select id="selectExistingList" resultType="java.lang.String">
        SELECT
               archives_num
        FROM hr_archives_manage
        WHERE is_delete = 0
    </select>

    <select id="selectListByIds" resultType="cn.casair.domain.HrArchivesManage">
        SELECT
            <include refid="Base_Column_List"/>
        FROM hr_archives_manage
        WHERE id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </select>

    <select id="getHrArchivesById" resultType="cn.casair.dto.HrArchivesManageDTO">
        SELECT ham.id,
               ham.staff_id,
               hc.id client_id,
               ham.archives_num,
               hc.client_name empUnit,
               hc.unit_number,
               hts.system_num,
               hts.certificate_num,
               ham.archives_name,
               hts.phone,
               ham.archives_type,
               ham.archives_local,
               ham.archives_status,
               ham.warehouse_time,
               ham.created_by,
               ham.created_date
        FROM hr_archives_manage ham
                 LEFT JOIN hr_talent_staff hts ON hts.id = ham.staff_id
                 LEFT JOIN hr_client hc ON hc.id = hts.client_id
        WHERE ham.is_delete = 0 AND hts.is_delete = 0
          AND ham.id = #{id}
    </select>

    <select id="selectArchivesManagePage" resultMap="ArchivesManagerList">
        SELECT a.*
        FROM (
                 SELECT ham.id,
                        ham.staff_id,
                        hc.id client_id,
                        ham.archives_num,
                        hc.client_name empUnit,
                        hc.unit_number,
                        hts.system_num,
                        hts.certificate_num,
                        ham.archives_name,
                        hts.phone,
                        hts.domicile_place,
                        ham.archives_type,
                        ham.archives_local,
                        ham.archives_status,
                        ham.warehouse_time,
                        ham.created_by,
                        ham.created_date
                 FROM hr_archives_manage ham
                 LEFT JOIN hr_talent_staff hts ON hts.id = ham.staff_id
                 LEFT JOIN hr_client hc ON hc.id = hts.client_id
                 WHERE ham.is_delete = 0 AND hts.is_delete = 0
                    AND ham.archives_type = 0
                    <if test="permissionClient!=null and permissionClient.size()>0">
                        AND hc.id IN
                        <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                            #{i}
                        </foreach>
                    </if>
                 UNION
                 SELECT ham.id,
                        ham.staff_id,
                        ham.client_id,
                        ham.archives_num,
                        hc.client_name empUnit,
                        hc.unit_number,
                        null,
                        null,
                        ham.archives_name,
                        null,
                        null,
                        ham.archives_type,
                        ham.archives_local,
                        ham.archives_status,
                        ham.warehouse_time,
                        ham.created_by,
                        ham.created_date
                 FROM hr_archives_manage ham
                 LEFT JOIN hr_client hc ON hc.id = ham.client_id
                 WHERE ham.is_delete = 0
                   AND ham.archives_type = 1
                    <if test="permissionClient!=null and permissionClient.size()>0">
                        AND hc.id IN
                        <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                            #{i}
                        </foreach>
                    </if>
             ) a
            <where>
                <if test="param.archivesNum != null and param.archivesNum != ''">
                    AND a.archives_num LIKE CONCAT('%',#{param.archivesNum},'%')
                </if>
                <if test="param.certificateNum != null and param.certificateNum != ''">
                    AND a.certificate_num LIKE CONCAT('%',#{param.certificateNum},'%')
                </if>
                <if test="param.clientId != null and param.clientId != ''">
                    AND a.client_id = #{param.clientId}
                </if>
                <if test="param.clientIds != null and param.clientIds.size > 0">
                    AND a.client_id IN
                    <foreach collection="param.clientIds" open="(" separator="," close=")" item="i">
                        #{i}
                    </foreach>
                </if>
                <if test="param.archivesName != null and param.archivesName != ''">
                    AND a.archives_name LIKE CONCAT('%',#{param.archivesName},'%')
                </if>
                <if test="param.archivesType != null">
                    AND a.archives_type = #{param.archivesType}
                </if>
                <if test="param.archivesLocal != null and param.archivesLocal != ''">
                    and a.archives_local LIKE CONCAT('%',#{param.archivesLocal},'%')
                </if>
                <if test="param.archivesStatus != null">
                    AND a.archives_status = #{param.archivesStatus}
                </if>
                <if test="param.domicilePlace!=null and param.domicilePlace!=''">
                    AND a.domicile_place = #{param.domicilePlace}
                </if>
                <if test="param.certificateNum!=null and param.certificateNum!=''">
                    AND a.certificate_num LIKE CONCAT('%',#{param.certificateNum},'%')
                </if>
            </where>
            <choose>
                <when test="param.field!=null and param.field!=''">
                    <choose>
                        <when test="param.field=='archives_type_str'">
                            ORDER BY a.archives_type ${param.order}
                        </when>
                        <when test="param.field=='archives_status_str'">
                            ORDER BY a.archives_status ${param.order}
                        </when>
                        <when test="param.field=='emp_unit'">
                            ORDER BY a.empUnit ${param.order}
                        </when>
                        <otherwise>
                            ORDER BY ${param.field} ${param.order}
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    ORDER BY
                    a.created_date DESC
                </otherwise>
            </choose>
    </select>

    <!--    分页查询未建档的员工-->
    <select id="findEmployeePage" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT
        hts.id,
        hts.`name`,
        hts.certificate_num,
        hc.client_name,
        hc.id clientId
        FROM
        hr_talent_staff hts
        LEFT JOIN hr_client hc ON hts.client_id = hc.id
        AND hc.is_delete = 0
        WHERE
        hts.is_delete = 0
        AND hts.`status` = 0
        AND hts.iz_default = 0
        AND hts.id NOT IN ( SELECT staff_id FROM hr_archives_manage WHERE is_delete != 1 AND archives_type = 0 )
        <if test="param.name != null and param.name != ''">
            AND hts.`name` LIKE CONCAT('%',#{param.name},'%')
        </if>
        ORDER BY
        hts.created_date DESC
    </select>

    <!--    分页查询未建档的客户-->
    <select id="findClientPage" resultType="cn.casair.dto.HrClientDTO">
        SELECT
        hc.id,
        hc.unit_number,
        hc.client_name
        FROM
        hr_client hc
        WHERE
        hc.is_delete = 0
        AND hc.`status` = 0
        AND hc.id NOT IN ( SELECT DISTINCT client_id FROM hr_archives_manage WHERE is_delete = 0 AND archives_type = 1 )
        <if test="param.clientName != null and param.clientName != ''">
            AND hc.client_name LIKE CONCAT('%',#{param.clientName},'%')
        </if>
        ORDER BY
        hc.created_date DESC
    </select>

    <select id="getCountByArchivesNum" resultType="java.lang.Integer">
        SELECT
            count( id )
        FROM
            hr_archives_manage
        WHERE
            is_delete = 0
          AND archives_num = #{archivesNum}
          AND id != #{id}
    </select>

    <select id="selectExportListByIds" resultType="cn.casair.dto.excel.HrArchivesManageExport">
        SELECT
            ham.id,
            ham.staff_id,
            ham.client_id,
            ham.archives_num,
            hc.unit_number,
            hc.client_name emp_unit,
            hts.system_num,
            hts.staff_status,
            hts.certificate_num,
            ham.archives_name,
            hts.phone,
            archives_type,
            ham.archives_local,
            ham.archives_status,
            ham.warehouse_time
        FROM hr_archives_manage ham
            LEFT JOIN hr_talent_staff hts ON hts.id = ham.staff_id
            LEFT JOIN hr_client hc ON hc.id = hts.client_id
        WHERE
            ham.is_delete = 0 AND hts.is_delete = 0
        <if test="param.ids!=null and param.ids.size()>0">
            AND ham.id IN
            <foreach collection="param.ids" open="(" close=")" separator="," item="i">
                #{i}
            </foreach>
        </if>
        <if test="permissionClient!=null and permissionClient.size()>0">
            AND hc.id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param.archivesNum != null and param.archivesNum != ''">
            AND ham.archives_num LIKE CONCAT('%',#{param.archivesNum},'%')
        </if>
        <if test="param.clientId != null and param.clientId != ''">
            AND hc.id = #{param.clientId}
        </if>
        <if test="param.archivesName != null and param.archivesName != ''">
            AND ham.archives_name LIKE CONCAT('%',#{param.archivesName},'%')
        </if>
        <if test="param.archivesType != null">
            AND ham.archives_type = #{param.archivesType}
        </if>
        <if test="param.archivesLocal != null and param.archivesLocal != ''">
            and ham.archives_local LIKE CONCAT('%',#{param.archivesLocal},'%')
        </if>
        <if test="param.archivesStatus != null">
            AND ham.archives_status = #{param.archivesStatus}
        </if>
        <if test="param.domicilePlace!=null and param.domicilePlace!=''">
            AND hts.domicile_place = #{param.domicilePlace}
        </if>
        ORDER BY
            ham.created_date DESC
    </select>

    <select id="getArchivesByTypeAndUnionId" resultType="cn.casair.dto.HrArchivesManageDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_archives_manage
        WHERE is_delete = 0
        AND archives_type = #{archivesType}
        <if test="archivesType==0">
            AND staff_id = #{unionId}
        </if>
        <if test="archivesType==1">
            AND client_id = #{unionId}
        </if>
    </select>

    <select id="getArchivesByStaffId" resultType="cn.casair.domain.HrArchivesManage">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            hr_archives_manage
        WHERE
            staff_id = #{staffId}
            AND is_delete = 0
    </select>

    <select id="getArchivesByClientIdAndStaffId" resultMap="ArchivesManager">
        SELECT
            ham.id,
            ham.staff_id,
            ham.client_id,
            ham.archives_num,
            hc.unit_number,
            hc.client_name empUnit,
            hts.system_num,
            hts.certificate_num,
            ham.archives_name,
            hts.phone,
            ham.archives_type,
            ham.archives_local,
            ham.archives_status,
            ham.warehouse_time,
            ham.created_by,
            had.id detailId,
            had.archives_id,
            had.`name` detailName,
            had.state,
            had.type,
            ha.id appendixId,
            ha.`name` appendixName,
            ha.origin_name,
            ha.file_type,
            ha.file_url
        FROM
            hr_archives_manage ham
                LEFT JOIN hr_talent_staff hts ON hts.id = ham.staff_id
                AND hts.is_delete = 0
                LEFT JOIN hr_client hc ON hc.id = hts.client_id
                AND hc.is_delete = 0
                LEFT JOIN hr_archives_detail had ON had.archives_id = ham.id
                AND had.is_delete = 0
                LEFT JOIN hr_appendix_union hau ON hau.union_id = had.id
                LEFT JOIN hr_appendix ha ON ha.id = hau.appendix_id
                AND ha.is_delete = 0
        WHERE
            ham.is_delete = 0 AND hts.is_delete = 0
            AND ham.archives_type = 0
            AND ham.staff_id = #{staffId}
    </select>
</mapper>
