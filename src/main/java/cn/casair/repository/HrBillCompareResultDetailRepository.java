package cn.casair.repository;

import cn.casair.domain.HrBillCompareResultDetail;
import cn.casair.dto.HrBillCompareResultDetailDTO;
import cn.casair.dto.HrBillReimbursementApplyDetailDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 导盘对账结果明细数据库操作类
 *
 * <AUTHOR>
 * @since 2022-09-22
 */
@Repository
public interface HrBillCompareResultDetailRepository extends BaseMapper<HrBillCompareResultDetail> {

    /**
     * 根据对账结果id获取对账结果明细
     *
     * @param compareResultId
     * @return
     */
    List<HrBillCompareResultDetailDTO> selectHrBillCompareResultDetailByBillCompareResultId(@Param("compareResultId") String compareResultId);

    /**
     * 根据对账结果id删除对账结果明细
     * @param resultIds
     */
    void delByResultId(@Param("resultIds") List<String> resultIds);

    /**
     * 查询差异明细
     * @param ids
     * @return
     */
    List<HrBillCompareResultDetailDTO> selectDiffBatchId(@Param("ids") List<String> ids);

    /**
     * 查看
     * @param resultDetailDTO
     * @return
     */
    List<HrBillCompareResultDetailDTO> findList(@Param("param") HrBillCompareResultDetailDTO resultDetailDTO);

    /**
     * 批量修改对账明细
     * @param updateBatchList
     */
    void updateBatch(@Param("list") List<HrBillCompareResultDetail> updateBatchList);

    /**
     * 批量添加对账明细
     * @param saveBatchList
     */
    void saveBatch(@Param("list") List<HrBillCompareResultDetail> saveBatchList);

    /**
     * 根据对账结果id获取对账结果明细
     *
     * @param resultIds
     * @return
     */
    List<HrBillCompareResultDetailDTO> selectResultDetailBatchResultId(@Param("resultIds") List<String> resultIds);

}
