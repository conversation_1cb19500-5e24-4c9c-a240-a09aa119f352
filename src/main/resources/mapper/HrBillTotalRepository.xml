<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillTotalRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , bill_id, staff_num, pay_year, pay_monthly, social_security_cardinal,social_security_cardinal_personal, medical_insurance_cardinal,
        medical_insurance_cardinal_personal, accumulation_fund_cardinal, unit_pension_total, unit_medical_total, unit_unemployment_total,
        work_injury_total, unit_subtotal, personal_pension_total, personal_medical_total, personal_unemployment_total, personal_subtotal,
        unit_social_security_make_up_total, personal_social_security_make_up_total, social_security_total, unit_accumulation_fund_total,
        personal_accumulation_fund_total, unit_accumulation_fund_make_up_total, personal_accumulation_fund_make_up_total, accumulation_fund_total,
        service_fee_total, make_up_total, total, real_salary_total, personal_tax_total, other_fee_total,personal_tax_make_up_total,total_occurrence,
        charge_total,refund_total,
        is_delete, created_by, last_modified_by, created_date, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillTotal">
        <id column="id" property="id"/>
        <result column="bill_id" property="billId"/>
        <result column="staff_num" property="staffNum"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="social_security_cardinal" property="socialSecurityCardinal"/>
        <result column="medical_insurance_cardinal" property="medicalInsuranceCardinal"/>
        <result column="social_security_cardinal_personal" property="socialSecurityCardinalPersonal"/>
        <result column="medical_insurance_cardinal_personal" property="medicalInsuranceCardinalPersonal"/>
        <result column="accumulation_fund_cardinal" property="accumulationFundCardinal"/>
        <result column="unit_pension_total" property="unitPensionTotal"/>
        <result column="unit_medical_total" property="unitMedicalTotal"/>
        <result column="unit_unemployment_total" property="unitUnemploymentTotal"/>
        <result column="work_injury_total" property="workInjuryTotal"/>
        <result column="unit_subtotal" property="unitSubtotal"/>
        <result column="personal_pension_total" property="personalPensionTotal"/>
        <result column="personal_medical_total" property="personalMedicalTotal"/>
        <result column="personal_unemployment_total" property="personalUnemploymentTotal"/>
        <result column="personal_subtotal" property="personalSubtotal"/>
        <result column="unit_social_security_make_up_total" property="unitSocialSecurityMakeUpTotal"/>
        <result column="personal_social_security_make_up_total" property="personalSocialSecurityMakeUpTotal"/>
        <result column="social_security_total" property="socialSecurityTotal"/>
        <result column="unit_accumulation_fund_total" property="unitAccumulationFundTotal"/>
        <result column="personal_accumulation_fund_total" property="personalAccumulationFundTotal"/>
        <result column="unit_accumulation_fund_make_up_total" property="unitAccumulationFundMakeUpTotal"/>
        <result column="personal_accumulation_fund_make_up_total" property="personalAccumulationFundMakeUpTotal"/>
        <result column="accumulation_fund_total" property="accumulationFundTotal"/>
        <result column="service_fee_total" property="serviceFeeTotal"/>
        <result column="last_month_make_up" property="lastMonthMakeUp"/>
        <result column="total" property="total"/>
        <result column="real_salary_total" property="realSalaryTotal"/>
        <result column="personal_tax_total" property="personalTaxTotal"/>
        <result column="personal_tax_make_up_total" property="personalTaxMakeUpTotal"/>
        <result column="other_fee_total" property="otherFeeTotal"/>
        <result column="total_occurrence" property="totalOccurrence"/>
        <result column="charge_total" property="chargeTotal"/>
        <result column="refund_total" property="refundTotal"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="find_list">
        WITH a AS (
            SELECT
                any_value ( hbt.id ) id,
                any_value ( hfr.client_id ) client_id,
                any_value ( hc.unit_number ) unit_number,
                any_value ( hc.client_name ) client_name,
                any_value ( hbt.pay_year ) pay_year,
                any_value ( hbt.pay_monthly ) pay_monthly,
                CONCAT(any_value ( hbt.pay_year ),'-',any_value ( hbt.pay_monthly )) payment_date,
                any_value ( hbt.staff_num ) staff_num,
                any_value ( hbt.social_security_total ) social_security_total,
                any_value ( hbt.accumulation_fund_total ) accumulation_fund_total,
                sum( hbd.salary ) salary_total,
                CONVERT ( sum( hbd.salary ) / any_value ( hbt.staff_num ), DECIMAL ( 10, 2 )) average_wage,
                any_value ( hbt.service_fee_total ) service_fee_total,
                any_value ( hbt.total ) total
            FROM
                hr_bill_total hbt
            LEFT JOIN hr_fee_review hfr ON hfr.id = hbt.bill_id
            LEFT JOIN hr_client hc ON hc.id = hfr.client_id
            LEFT JOIN hr_bill_detail hbd ON hbd.bill_id = hbt.bill_id AND hbd.is_delete = 0 AND is_used = 1
            WHERE hbt.is_delete = 0 AND hfr.`status` = 1
            <if test="permissionClient!=null and permissionClient.size()>0">
                AND hfr.client_id IN
                <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            GROUP BY hbt.id
        )SELECT
            ANY_VALUE ( id ) id,
            ANY_VALUE ( unit_number ) unit_number,
            ANY_VALUE ( client_id ) client_id,
            ANY_VALUE ( client_name ) client_name,
            ANY_VALUE ( pay_year ) pay_year,
            ANY_VALUE ( pay_monthly ) pay_monthly,
            ANY_VALUE ( payment_date ) payment_date,
            SUM(staff_num) staff_num,
            SUM(social_security_total) social_security_total,
            SUM(accumulation_fund_total) accumulation_fund_total,
            SUM(salary_total) salary_total,
            SUM(average_wage) average_wage,
            SUM(service_fee_total) service_fee_total,
            SUM(total) total
        FROM a
        <where>
            <if test="params.unitNumber!=null and params.unitNumber!=''">
                AND a.unit_number LIKE CONCAT('%', #{params.unitNumber}, '%')
            </if>
            <if test="params.clientIds!=null and params.clientIds.size()>0">
                AND a.client_id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.payYear!=null">
                AND a.pay_year = #{params.payYear}
            </if>
            <if test="params.payMonthly!=null">
                AND a.pay_monthly = #{params.payMonthly}
            </if>
            <if test="params.payYears!=null and params.payYears.size()>0">
                AND a.pay_year IN
                <foreach collection="params.payYears" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.payMonthlys!=null and params.payMonthlys.size()>0">
                AND a.pay_monthly IN
                <foreach collection="params.payMonthlys" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
        </where>
        GROUP BY client_id,payment_date
    </sql>

    <select id="getBillTotalExportList" resultType="cn.casair.dto.excel.HrBillTotalExport">
        <include refid="find_list"/>
    </select>

    <select id="getClientAnnualCurve" resultType="cn.casair.dto.HrBillTotalDTO">
        WITH b AS (
            WITH a AS (
                SELECT
                    hbt.id,
                    hbt.bill_id,
                    STR_TO_DATE(concat(hbt.pay_year, '-', hbt.pay_monthly, '-', 01), '%Y-%m-%d') paymentDate,
                    hbt.staff_num,
                    hbt.social_security_total,
                    hbt.accumulation_fund_total,
                    hbt.service_fee_total,
                    hbt.total,
                    hfr.client_id
                FROM
                    hr_bill_total hbt
                LEFT JOIN hr_fee_review hfr ON hbt.bill_id = hfr.id
                WHERE
                    hbt.is_delete = 0 AND hfr.status = 1
                    AND hfr.client_id = #{params.clientId}
                ORDER BY
                    hbt.pay_year,hbt.pay_monthly
            ) SELECT
                any_value (a.id) id,
                any_value (a.client_id) client_id,
                any_value (a.bill_id) billId,
                any_value (a.paymentDate) paymentDateSplicing,
                any_value (a.staff_num) staffNum,
                any_value (a.social_security_total) socialSecurityTotal,
                any_value (a.accumulation_fund_total) accumulationFundTotal,
                any_value (a.service_fee_total) serviceFeeTotal,
                sum(hbd.salary) salaryTotal,
                CONVERT (
                    sum(hbd.salary) / any_value (staff_num),
                    DECIMAL (10, 2)
                ) averageWage,
                any_value (a.total) total
            FROM
                a
            LEFT JOIN hr_bill_detail hbd ON hbd.bill_id = a.bill_id AND hbd.is_delete = 0 AND hbd.is_used = 1
            GROUP BY a.bill_id
        )SELECT
                any_value (id) id,
                any_value (client_id) client_id,
                any_value (billId) billId,
                any_value (paymentDateSplicing) paymentDateSplicing,
                SUM(staffNum) staffNum,
                SUM(socialSecurityTotal) socialSecurityTotal,
                SUM(accumulationFundTotal) accumulationFundTotal,
                SUM(averageWage) averageWage,
                SUM(serviceFeeTotal) serviceFeeTotal,
                sum(salaryTotal) salaryTotal,
                SUM(total) total
        FROM b
        WHERE paymentDateSplicing &gt;= #{params.paymentDateStart} AND paymentDateSplicing &lt;= #{params.paymentDateEnd}
        GROUP BY client_id,paymentDateSplicing
    </select>

    <select id="createByBillId" resultType="cn.casair.domain.HrBillTotal">
        SELECT COUNT(*)                                    AS staffNum,
               SUM(social_security_cardinal)               AS socialSecurityCardinal,
               SUM(medical_insurance_cardinal)             as medicalInsuranceCardinal,
               SUM(accumulation_fund_cardinal)             as accumulationFundCardinal,
               SUM(social_security_cardinal_personal)      as socialSecurityCardinalPersonal,
               SUM(medical_insurance_cardinal_personal)    as medicalInsuranceCardinalPersonal,
               SUM(unit_pension)                           as unitPensionTotal,
               SUM(unit_medical)                           as unitMedicalTotal,
               SUM(unit_unemployment)                      as unitUnemploymentTotal,
               SUM(work_injury)                            as workInjuryTotal,
               SUM(personal_pension)                       as personalPensionTotal,
               SUM(personal_medical)                       as personalMedicalTotal,
               SUM(personal_unemployment)                  as personalUnemploymentTotal,
               SUM(unit_social_security_make_up)           as unitSocialSecurityMakeUpTotal,
               SUM(personal_social_security_make_up)       as personalSocialSecurityMakeUpTotal,
               SUM(social_security_total)                  as socialSecurityTotal,
               SUM(unit_accumulation_fund)                 as unitAccumulationFundTotal,
               SUM(personal_accumulation_fund)             as personalAccumulationFundTotal,
               SUM(unit_accumulation_fund_make_up)         as unitAccumulationFundMakeUpTotal,
               SUM(personal_accumulation_fund_make_up)     as personalAccumulationFundMakeUpTotal,
               SUM(accumulation_fund_total)                as accumulationFundTotal,
               SUM(service_fee)                            as serviceFeeTotal,
               SUM(real_salary)							   as  real_salary_total,
               SUM(personal_tax)                           as  personal_tax_total,
               SUM(personal_tax_make_up )                  as personal_tax_make_up,
               SUM(other_salary)						   as  other_fee_total,
               SUM(unit_pension_cardinal)				   as  unitPensionCardinal,
               SUM(unit_unemployment_cardinal)			   as  unitUnemploymentCardinal,
               SUM(work_injury_cardinal)				   as  workInjuryCardinal,
               SUM(unit_maternity_cardinal)				   as  unitMaternityCardinal,
               SUM(personal_pension_cardinal)			   as  personalPensionCardinal,
               SUM(personal_unemployment_cardinal)		   as  personalUnemploymentCardinal,
               SUM(unit_maternity)				           as  unitMaternityTotal,
               SUM(unit_large_medical_expense)			   as  unitLargeMedicalExpenseTotal,
               SUM(replenish_work_injury_expense)		   as  replenishWorkInjuryExpenseTotal,
               SUM(unit_late_fee)						   as  unitLateFeeTotal,
               SUM(unit_other_fee)						   as  unitOtherFeeTotal,
               SUM(personal_large_medical_expense)		   as  personalLargeMedicalExpenseTotal,
               SUM(unit_enterprise_annuity)		           as  unitEnterpriseAnnuityTotal,
               SUM(personal_enterprise_annuity)		       as  personalEnterpriseAnnuityTotal,
               SUM(commercial_insurance)		           as  commercialInsuranceTotal,
               SUM(personal_other_fee)					   as  personalOtherFeeTotal,
               SUM(personal_maternity_cardinal)		       as  personalMaternityCardinal,
               SUM(personal_maternity)		               as  personalMaternityTotal
        FROM hr_bill_detail
        WHERE is_delete = 0
          AND is_used = 1
          AND bill_id = #{billId}
    </select>

    <select id="getClientPaymentDateTotalAmount" resultType="cn.casair.dto.HrBillTotalDTO">
        SELECT
            hbt.total
        FROM
            hr_fee_review hfr
            LEFT JOIN hr_bill_total hbt ON hbt.bill_id = hfr.bill_id
        WHERE
            hfr.is_delete = 0
          AND hfr.`status` = 1
          AND hbt.is_delete = 0
          AND hfr.client_id = #{params.clientId}
          AND hfr.pay_year = #{params.payYear}
          AND hfr.pay_monthly = #{params.payMonthly}
    </select>

    <select id="findPage" resultType="cn.casair.dto.HrBillTotalDTO">
        WITH b AS(
            WITH a AS (
                SELECT
                    ANY_VALUE ( hbt.id ) id,
                    ANY_VALUE ( hbt.bill_id ) bill_id,
                    ANY_VALUE ( hc.unit_number ) unit_number,
                    ANY_VALUE ( hfr.client_id ) client_id,
                    ANY_VALUE ( hc.client_name ) client_name,
                    ANY_VALUE ( hbt.pay_year ) pay_year,
                    ANY_VALUE ( hbt.pay_monthly ) pay_monthly,
                    STR_TO_DATE( CONCAT( ANY_VALUE ( hbt.pay_year ), '-', ANY_VALUE ( hbt.pay_monthly ), '-', 01 ), '%Y-%m-%d' ) payment_date,
                    ANY_VALUE ( hbt.staff_num ) staff_num,
                    ANY_VALUE ( hbt.social_security_total ) social_security_total,
                    ANY_VALUE ( hbt.accumulation_fund_total ) accumulation_fund_total,
                    SUM( hbd.salary ) salary_total,
                    ANY_VALUE ( hbt.service_fee_total ) service_fee_total,
                    ANY_VALUE ( hbt.total ) total,
                    ANY_VALUE ( hbt.created_date ) created_date
                FROM
                    hr_bill_total hbt
                LEFT JOIN hr_fee_review hfr ON hfr.id = hbt.bill_id
                LEFT JOIN hr_client hc ON hc.id = hfr.client_id
                LEFT JOIN hr_bill_detail hbd ON hbd.bill_id = hfr.id AND hbd.is_delete = 0 AND is_used = 1
                WHERE
                hbt.is_delete = 0 AND hfr.status = 1
                <if test="permissionClient!=null and permissionClient.size()>0">
                    AND hfr.client_id IN
                    <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                        #{i}
                    </foreach>
                </if>
                GROUP BY
                hbt.bill_id
            ) SELECT
                id,
                unit_number,
                client_id,
                client_name,
                pay_year,
                pay_monthly,
                staff_num,
                payment_date,
                social_security_total,
                accumulation_fund_total,
                salary_total,
                CONVERT ( salary_total / staff_num, DECIMAL ( 10, 2 )) average_wage,
                service_fee_total,
                total,
                created_date
            FROM a
        ) SELECT
            ANY_VALUE ( id ) id,
            ANY_VALUE ( unit_number ) unit_number,
            ANY_VALUE ( client_id ) client_id,
            ANY_VALUE ( client_name ) client_name,
            ANY_VALUE ( pay_year ) pay_year,
            ANY_VALUE ( pay_monthly ) pay_monthly,
            ANY_VALUE ( payment_date ) payment_date,
            ANY_VALUE ( created_date ) created_date,
            SUM(staff_num) staff_num,
            SUM(social_security_total) social_security_total,
            SUM(accumulation_fund_total) accumulation_fund_total,
            SUM(salary_total) salary_total,
            SUM(average_wage) average_wage,
            SUM(service_fee_total) service_fee_total,
            SUM(total) total
        FROM b
        <where>
            <if test="params.unitNumber!=null and params.unitNumber!=''">
                AND b.unit_number LIKE CONCAT('%', #{params.unitNumber}, '%')
            </if>
            <if test="params.clientIds!=null and params.clientIds.size()>0">
                AND b.client_id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.payYear!=null">
                AND b.pay_year = #{params.payYear}
            </if>
            <if test="params.payMonthly!=null">
                AND b.pay_monthly = #{params.payMonthly}
            </if>
            <if test="params.paymentDateStart!=null">
                AND b.payment_date &gt;= #{params.paymentDateStart}
            </if>
            <if test="params.paymentDateEnd!=null">
                AND b.payment_date &lt;= #{params.paymentDateEnd}
            </if>
        </where>
        GROUP BY client_id,payment_date
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY pay_year ${params.order}, pay_monthly ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
        </choose>
    </select>

    <select id="getBillStatistics" resultType="cn.casair.dto.HrBillTotalDTO">
        WITH b AS (
            WITH a AS (
                SELECT hbt.id,
                hbt.bill_id,
                STR_TO_DATE(concat(hbt.pay_year, '-', hbt.pay_monthly, '-', 01), '%Y-%m-%d') paymentDate,
                hbt.staff_num,
                hbt.social_security_total,
                hbt.accumulation_fund_total,
                hbt.service_fee_total,
                hbt.total,
                hfr.client_id
                FROM hr_bill_total hbt
                LEFT JOIN hr_fee_review hfr ON hfr.id = hbt.bill_id
                WHERE hbt.is_delete = 0 AND hfr.status = 1
                ORDER BY hbt.pay_year, hbt.pay_monthly
            )
            SELECT
            ANY_VALUE(a.bill_id) bill_id,
            ANY_VALUE(a.client_id) client_id,
            ANY_VALUE(hc.client_name) client_name,
            ANY_VALUE(a.staff_num) staff_num,
            ANY_VALUE(a.social_security_total) social_security_total,
            ANY_VALUE(a.accumulation_fund_total) accumulation_fund_total,
            ANY_VALUE(a.service_fee_total) service_fee_total,
            ANY_VALUE(a.total) total,
            SUM(hbd.salary) salary
            FROM
            a
        LEFT JOIN hr_client hc ON hc.id = a.client_id AND hc.is_delete = 0
        LEFT JOIN hr_bill_detail hbd ON hbd.bill_id = a.bill_id AND hbd.is_delete = 0
        WHERE
        hbd.is_used = 1
        <if test="permissionClient!=null and permissionClient.size()>0">
            AND hc.id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.paymentDateStart != null and params.paymentDateEnd != null ">
            AND a.paymentDate <![CDATA[>=]]> #{params.paymentDateStart}
            AND a.paymentDate <![CDATA[<=]]> #{params.paymentDateEnd}
        </if>
        <if test="params.clientIds != null and params.clientIds.size > 0 ">
            AND a.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        GROUP BY a.bill_id
        )
        SELECT
        ANY_VALUE(b.client_id) client_id,
        ANY_VALUE(b.client_name) client_name,
        SUM(b.staff_num) staff_num,
        SUM(b.social_security_total) social_security_total,
        SUM(b.accumulation_fund_total) accumulation_fund_total,
        SUM(b.service_fee_total) service_fee_total,
        SUM(b.total) total,
        SUM(b.salary) salary_total,
        CONVERT( SUM( b.salary ) / SUM( b.staff_num), DECIMAL ( 10, 2 )) AS averageWage
        FROM b
        GROUP BY b.client_id
    </select>

    <select id="getBillTotalPageByClient" resultType="cn.casair.dto.HrBillTotalDTO">
        SELECT
            hbt.id,
            hbt.pay_year,
            hbt.pay_monthly,
            hbt.bill_id,
            hbt.`social_security_total`,
            hbt.`accumulation_fund_total`,
            hbt.`service_fee_total`,
            hbt.`total`,
            hbt.`real_salary_total`,
            hbt.`personal_tax_total`,
            hbi.`invoice_status`,
            hc.`client_name`,
            hb.bill_state
        FROM
            hr_bill_total hbt
                LEFT JOIN hr_bill_invoice hbi ON hbi.`bill_id` = hbt.`bill_id` AND hbi.`is_delete` = 0
                LEFT JOIN hr_bill hb ON hb.id = hbt.bill_id AND hb.is_delete = 0
                LEFT JOIN hr_client hc ON hb.`client_id` = hc.`id` AND hc.`is_delete` = 0
        WHERE hb.`client_id` = #{clientId}
          AND hbt.is_delete = 0
          AND hb.is_official = 1
          AND hb.review_state = 1
        GROUP BY
            hbt.id,
            hbt.pay_year,
            hbt.pay_monthly,
            hbt.bill_id,
            hbt.`social_security_total`,
            hbt.`accumulation_fund_total`,
            hbt.service_fee_total,
            hbt.`total`,
            hbt.`real_salary_total`,
            hbt.`personal_tax_total`,
            hbi.`invoice_status`,
            hc.`client_name`,
            hb.bill_state
        ORDER BY
            hbt.pay_year DESC,
            hbt.pay_monthly DESC
    </select>

    <select id="getByClientIdAndDate" resultType="cn.casair.dto.HrBillTotalDTO">
        SELECT * FROM hr_bill_total hl
          LEFT JOIN hr_bill hb ON hl.bill_id = hb.id
        WHERE hl.is_delete = 0 and hb.is_official = 1
        AND hb.review_state = 1
        AND hb.is_delete = 0
        AND hb.client_id = #{clientId} AND hb.pay_year = #{payYear} AND hb.pay_monthly = #{payMonthly}
    </select>

    <select id="getBillTotalByBatchBill" resultType="cn.casair.dto.HrBillTotalDTO">
        SELECT
            SUM(staff_num) staff_num,
            SUM(social_security_cardinal) social_security_cardinal,
            SUM(social_security_cardinal_personal) social_security_cardinal_personal,
            SUM(medical_insurance_cardinal) medical_insurance_cardinal,
            SUM(medical_insurance_cardinal_personal) medical_insurance_cardinal_personal,
            SUM(accumulation_fund_cardinal) accumulation_fund_cardinal,
            SUM(unit_pension_total) unit_pension_total,
            SUM(unit_medical_total) unit_medical_total,
            SUM(unit_unemployment_total) unit_unemployment_total,
            SUM(work_injury_total) work_injury_total,
            SUM(unit_subtotal) unit_subtotal,
            SUM(personal_pension_total) personal_pension_total,
            SUM(personal_medical_total) personal_medical_total,
            SUM(personal_unemployment_total) personal_unemployment_total,
            SUM(personal_subtotal) personal_subtotal,
            SUM(unit_social_security_make_up_total) unit_social_security_make_up_total,
            SUM(personal_social_security_make_up_total) personal_social_security_make_up_total,
            SUM(social_security_total) social_security_total,
            SUM(unit_accumulation_fund_total) unit_accumulation_fund_total,
            SUM(personal_accumulation_fund_total) personal_accumulation_fund_total,
            SUM(unit_accumulation_fund_make_up_total) unit_accumulation_fund_make_up_total,
            SUM(personal_accumulation_fund_make_up_total) personal_accumulation_fund_make_up_total,
            SUM(accumulation_fund_total) accumulation_fund_total,
            SUM(service_fee_total) service_fee_total,
            SUM(taxation_fee_total) taxation_fee_total,
            SUM(last_month_make_up) last_month_make_up,
            SUM(total) total,
            SUM(real_salary_total) real_salary_total,
            SUM(personal_tax_total) personal_tax_total,
            SUM(personal_tax_make_up_total) personal_tax_make_up_total,
            SUM(other_fee_total) other_fee_total,
            SUM(unit_pension_cardinal)			        	   as  unitPensionCardinal,
            SUM(unit_unemployment_cardinal)		        	   as  unitUnemploymentCardinal,
            SUM(work_injury_cardinal)			        	   as  workInjuryCardinal,
            SUM(unit_maternity_cardinal)		        	   as  unitMaternityCardinal,
            SUM(personal_pension_cardinal)			           as  personalPensionCardinal,
            SUM(personal_unemployment_cardinal)		           as  personalUnemploymentCardinal,
            SUM(unit_maternity_total)				           as  unitMaternityTotal,
            SUM(unit_large_medical_expense_total)			   as  unitLargeMedicalExpenseTotal,
            SUM(replenish_work_injury_expense_total)		   as  replenishWorkInjuryExpenseTotal,
            SUM(unit_late_fee_total)						   as  unitLateFeeTotal,
            SUM(unit_other_fee_total)						   as  unitOtherFeeTotal,
            SUM(personal_large_medical_expense_total)		   as  personalLargeMedicalExpenseTotal,
            SUM(unit_enterprise_annuity_total)		           as  unitEnterpriseAnnuityTotal,
            SUM(personal_enterprise_annuity_total)		       as  personalEnterpriseAnnuityTotal,
            SUM(commercial_insurance_total)		               as  commercialInsuranceTotal,
            SUM(personal_other_fee_total)					   as  personalOtherFeeTotal,
            SUM(personal_maternity_cardinal)		           as  personalMaternityCardinal,
            SUM(personal_maternity_total)		               as  personalMaternityTotal
        FROM
            hr_bill_total
        WHERE
            is_delete = 0
        AND bill_id IN
        <foreach collection="billIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="saveByBillId" resultType="cn.casair.domain.HrBillTotal">
            SELECT COUNT(*)                                AS staffNum,
               SUM(social_security_cardinal)               AS socialSecurityCardinal,
               SUM(medical_insurance_cardinal)             as medicalInsuranceCardinal,
               SUM(accumulation_fund_cardinal)             as accumulationFundCardinal,
               SUM(social_security_cardinal_personal)      as socialSecurityCardinalPersonal,
               SUM(medical_insurance_cardinal_personal)    as medicalInsuranceCardinalPersonal,
               SUM(unit_pension)                           as unitPensionTotal,
               SUM(unit_medical)                           as unitMedicalTotal,
               SUM(unit_unemployment)                      as unitUnemploymentTotal,
               SUM(work_injury)                            as workInjuryTotal,
               SUM(personal_pension)                       as personalPensionTotal,
               SUM(personal_medical)                       as personalMedicalTotal,
               SUM(personal_unemployment)                  as personalUnemploymentTotal,
               SUM(unit_social_security_make_up)           as unitSocialSecurityMakeUpTotal,
               SUM(personal_social_security_make_up)       as personalSocialSecurityMakeUpTotal,
               SUM(social_security_total)                  as socialSecurityTotal,
               SUM(unit_accumulation_fund)                 as unitAccumulationFundTotal,
               SUM(personal_accumulation_fund)             as personalAccumulationFundTotal,
               SUM(unit_accumulation_fund_make_up)         as unitAccumulationFundMakeUpTotal,
               SUM(personal_accumulation_fund_make_up)     as personalAccumulationFundMakeUpTotal,
               SUM(accumulation_fund_total)                as accumulationFundTotal,
               SUM(service_fee)                            as serviceFeeTotal,
               SUM(real_salary)							   as  real_salary_total,
               SUM(personal_tax)                           as  personal_tax_total,
               SUM(other_salary)						   as  other_fee_total,
               SUM(total)						           as  total,
               SUM(unit_pension_cardinal)				   as  unitPensionCardinal,
               SUM(unit_unemployment_cardinal)			   as  unitUnemploymentCardinal,
               SUM(work_injury_cardinal)				   as  workInjuryCardinal,
               SUM(unit_maternity_cardinal)				   as  unitMaternityCardinal,
               SUM(personal_pension_cardinal)			   as  personalPensionCardinal,
               SUM(personal_unemployment_cardinal)		   as  personalUnemploymentCardinal,
               SUM(unit_maternity)				           as  unitMaternityTotal,
               SUM(unit_large_medical_expense)			   as  unitLargeMedicalExpenseTotal,
               SUM(replenish_work_injury_expense)		   as  replenishWorkInjuryExpenseTotal,
               SUM(unit_late_fee)						   as  unitLateFeeTotal,
               SUM(unit_other_fee)						   as  unitOtherFeeTotal,
               SUM(personal_large_medical_expense)		   as  personalLargeMedicalExpenseTotal,
               SUM(unit_enterprise_annuity)		            as  unitEnterpriseAnnuityTotal,
               SUM(personal_enterprise_annuity)		        as  personalEnterpriseAnnuityTotal,
               SUM(commercial_insurance)		            as  commercialInsuranceTotal,
               SUM(personal_other_fee)					   as  personalOtherFeeTotal,
               SUM(personal_maternity_cardinal)		       as  personalMaternityCardinal,
               SUM(personal_maternity)		               as  personalMaternityTotal
        FROM hr_bill_detail
        WHERE is_delete = 0
          AND is_used = 1
          AND bill_id = #{billId}
    </select>

    <select id="getClientAccountsReceivable" resultType="cn.casair.dto.HrBillTotalDTO">
        WITH a AS (
            SELECT
            har.client_id,
            hc.client_name client_name,
            har.pay_year,
            har.pay_monthly,
            STR_TO_DATE(concat(har.pay_year, '-', har.pay_monthly, '-', 01), '%Y-%m-%d') paymentDateSplicing,
            har.receivable_amount receivable_amount,
            har.total_arrival_amount total_arrival_amount,
            ANY_VALUE(har.is_delete) is_delete
            FROM
                hr_arrival_record har
            LEFT JOIN hr_client hc ON hc.id = har.client_id AND hc.is_delete = 0
            WHERE har.is_delete = 0
            <if test="params.clientIds!=null and params.clientIds.size()>0">
                AND har.client_id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
        )
        SELECT
            GROUP_CONCAT(client_id) client_id,
            GROUP_CONCAT(client_name) client_name,
            ANY_VALUE(pay_year) pay_year,
            ANY_VALUE(pay_monthly) pay_monthly,
            ANY_VALUE(paymentDateSplicing) paymentDateSplicing,
            IF(SUM(receivable_amount) IS NULL,0.00,SUM(receivable_amount)) receivableAmount,
            IF(SUM(total_arrival_amount) IS NULL,0.00,SUM(total_arrival_amount)) totalArrivalAmount,
            ANY_VALUE(is_delete) is_delete
        FROM
            a
        WHERE is_delete = 0
        <choose>
            <when test="params.filterMonth == 4">
                AND YEAR(paymentDateSplicing) &gt;= #{params.filterStartDate} AND YEAR(paymentDateSplicing) &lt;= #{params.filterEndDate}
            </when>
            <otherwise>
               AND paymentDateSplicing &gt;= #{params.paymentDateStart} AND paymentDateSplicing &lt;= #{params.paymentDateEnd}
            </otherwise>
        </choose>
        <choose>
            <when test="params.filterMonth == 4">
                GROUP BY pay_year
            </when>
            <otherwise>
                GROUP BY pay_year,pay_monthly
            </otherwise>
        </choose>
    </select>

    <select id="statisticCount" resultType="cn.casair.domain.HrBillTotal">
        SELECT
            COUNT(*) 					AS projectNum,
            SUM( people_num)            AS staffNum,
            SUM( total )  		        AS total,
            SUM( total_occurrence )  	AS totalOccurrence
        FROM
            hr_bill_detail
        WHERE
            is_delete = 0 AND is_used = 1 AND bill_id IN
            <foreach collection="billIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
            <if test="projectType != null">
                AND project_type = #{projectType}
            </if>
    </select>

    <select id="getTotalData" resultType="cn.casair.dto.HrBillTotalDTO">
        WITH b AS(
            <include refid="find_list"/>
        )SELECT
            SUM(social_security_total) social_security_total,
            SUM(accumulation_fund_total) accumulation_fund_total,
            SUM(salary_total) salary_total,
            SUM(average_wage) average_wage,
            SUM(service_fee_total) service_fee_total,
            SUM(total) total
        FROM b
    </select>

    <select id="getBillTotalByBillId" resultType="cn.casair.dto.HrBillTotalDTO">
        SELECT
            hc.client_name,
            hb.client_id,
            hbt.*
        FROM
            hr_bill_total hbt
        LEFT JOIN hr_bill hb ON hbt.bill_id = hb.id
        LEFT JOIN hr_client hc ON hb.client_id = hc.id
        WHERE hbt.is_delete = 0 AND hbt.bill_id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="getBillTotalBySalaryBill" resultType="cn.casair.dto.HrBillTotalDTO">
        SELECT
            SUM(total) total,
            SUM(real_salary_total) real_salary_total,
            SUM(personal_tax_total) personal_tax_total,
            SUM(other_fee_total) other_fee_total
            FROM
            hr_bill_total
        WHERE is_delete = 0 AND bill_id IN
        <foreach collection="billIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

</mapper>
