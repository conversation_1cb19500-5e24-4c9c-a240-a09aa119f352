<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrNotificationUserRepository">

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, notification_id, notification_content_id, user_id, reminder_method, states,
is_delete                    ,created_by                    ,created_date                    ,last_modified_by                    ,last_modified_date
        </sql>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="cn.casair.domain.HrNotificationUser">
                    <id column="id" property="id"/>
                    <result column="notification_id" property="notificationId"/>
                    <result column="notification_content_id" property="notificationContentId"/>
                    <result column="user_id" property="userId"/>
                    <result column="reminder_method" property="reminderMethod"/>
                    <result column="states" property="states"/>
                <result column="is_delete" property="isDelete"/>
                <result column="created_by" property="createdBy"/>
                <result column="created_date" property="createdDate"/>
                <result column="last_modified_by" property="lastModifiedBy"/>
                <result column="last_modified_date" property="lastModifiedDate"/>
        </resultMap>

        <update id="deleteUserid">
            UPDATE hr_notification_user set is_delete=1 where user_id=#{userId} and notification_id=#{notificationId}
        </update>

        <select id="selectNotificationUser" resultType="cn.casair.dto.HrNotificationMessageDTO">
            SELECT
            any_value ( hu.notification_id) AS id,
            group_concat( hc.reminder_content SEPARATOR '-' ) AS reminderContent,
            any_value ( hu.states ) AS states,
            any_value ( hu.reminder_method ) AS reminderMethod
            from
            hr_notification_user hu
            LEFT JOIN hr_notification_message_content hc on hc.id=hu.notification_content_id
            where hu.is_delete=0
            and hu.notification_id=#{param1.id}
            <if test="param1.userId != null and param1.userId != ''">
                and hu.user_id=#{param1.userId}
            </if>


            <if test="param1.reminderMethodList!=null and param1.reminderMethodList.size() > 0">
                AND  hu.reminder_method IN
                <foreach collection="param1.reminderMethodList" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>

            <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
                order by ${param1.field} ${param1.order}
            </if>
        </select>
</mapper>
