package cn.casair.repository;

import cn.casair.domain.HrApplyEntry;
import cn.casair.dto.HrApplyEntryDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 客户入职申请数据库操作类
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
@Repository
public interface HrApplyEntryRepository extends BaseMapper<HrApplyEntry> {

    /**
     * 入职申请分页查询
     * @param page
     * @param hrApplyEntryDTO
     * @return
     */
    IPage<HrApplyEntryDTO> findPage(Page<HrApplyEntry> page, @Param("param") HrApplyEntryDTO hrApplyEntryDTO);

    /**
     * 查询客户入职申请详情
     * @param id
     * @return
     */
    HrApplyEntryDTO getHrApplyEntryById(String id);
}
