package cn.casair.service;

import cn.casair.domain.HrAppletMessage;
import cn.casair.dto.HrAppletMessageDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 小程序-消息中心服务类
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface HrAppletMessageService extends IService<HrAppletMessage> {

    /**
     * 公共方法--添加小程序消息--发送微信通知
     * @param messageType 消息类别 枚举--ServiceCenterEnum
     * @param serviceId 服务ID
     * @param staffId 员工ID
     * @param progressDescription 服务进度说明
     * @param izNotify 是否发送微信通知 true--发送  false--不发送
     * @param serviceName 微信通知消息--izNotify==true 必传
     */
    void saveNoticeMessage(Integer messageType, String serviceId, String staffId, String progressDescription, Boolean izNotify, String serviceName);

    /**
     * 公共方法--添加小程序消息--发送微信通知
     * @param messageType 消息类别 枚举--ServiceCenterEnum
     * @param serviceId 服务ID
     * @param staffId 员工ID
     * @param progressDescription 服务进度说明
     * @param izNotify 是否发送微信通知 true--发送  false--不发送
     * @param serviceName 微信通知消息--izNotify==true 必传
     */
    void updateNoticeMessage(Integer messageType, String serviceId, String staffId, String progressDescription, boolean izNotify, String serviceName);


    /**
     * 创建小程序-消息中心
     *
     * @param hrAppletMessageDTO
     * @return
     */
    HrAppletMessageDTO createHrAppletMessage(HrAppletMessageDTO hrAppletMessageDTO);

    /**
     * 修改小程序-消息中心
     *
     * @param hrAppletMessageDTO
     * @return
     */
    Optional<HrAppletMessageDTO> updateHrAppletMessage(HrAppletMessageDTO hrAppletMessageDTO);

    /**
     * 查询小程序-消息中心详情
     *
     * @param id
     * @return
     */
    HrAppletMessageDTO getHrAppletMessage(String id);

    /**
     * 删除小程序-消息中心
     *
     * @param id
     */
    void deleteHrAppletMessage(String id);

    /**
     * 批量删除小程序-消息中心
     *
     * @param ids
     */
    void deleteHrAppletMessage(List<String> ids);

    /**
     * 分页查询小程序-消息中心
     *
     * @param hrAppletMessageDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrAppletMessageDTO hrAppletMessageDTO, Long pageNumber, Long pageSize);

    /**
     * 小程序查看我的消息
     * @return
     */
    List<HrAppletMessageDTO> getHrAppletMessageList();

    /**
     * 已查看服务
     * @param id 消息ID
     */
    void viewedHrAppletMessage(String id);

}
