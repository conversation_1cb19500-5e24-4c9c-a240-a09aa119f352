package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.BillEnum;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.BigDecimalCompare;
import cn.casair.common.utils.CalculateUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrWelfareCompensationExport;
import cn.casair.mapper.HrWelfareCompensationMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 员工福利补差统计服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrWelfareCompensationServiceImpl extends ServiceImpl<HrWelfareCompensationRepository, HrWelfareCompensation> implements HrWelfareCompensationService {

    private final HrAppendixService hrAppendixService;
    private final HrStaffWelfareRecordRepository hrStaffWelfareRecordRepository;
    private final HrSocialSecurityRepository hrSocialSecurityRepository;
    private final HrAccumulationFundRepository hrAccumulationFundRepository;
    private final RedisCache redisCache;
    private final HrStaffWorkExperienceRepository hrStaffWorkExperienceRepository;
    private final HrClientService hrClientService;
    private final HrStaffWelfareRecordService hrStaffWelfareRecordService;
    private final SysOperLogService sysOperLogService;
    private final HrFeeReviewRepository hrFeeReviewRepository;
    private final HrStaffEmolumentRepository hrStaffEmolumentRepository;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final HrWelfareCompensationMapper hrWelfareCompensationMapper;
    private final HrWelfareCompensationRecordService hrWelfareCompensationRecordService;
    private final HrWelfareCompensationRecordRepository hrWelfareCompensationRecordRepository;

    @Override
    public String exportWelfareCompensation(Map<String, String> params) {
        String staffId = params.get("staffId");
        if (StringUtils.isBlank(staffId)) {
            throw new CommonException("查询参数缺失！");
        }
        List<HrWelfareCompensationExport> list = this.hrWelfareCompensationRepository.exportWelfareCompensation(staffId);
        if (list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        int listSize = list.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "福利补差", HrWelfareCompensationExport.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.WELFARE_COMPENSATION.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(Collections.singletonList(staffId)), listSize, fileUrl);
        return fileUrl;
    }

    @Override
    public List<HrWelfareCompensationDTO> getStaffWelfareCompensation(HrWelfareCompensationDTO hrWelfareCompensationDTO) {
        List<HrWelfareCompensationDTO> list = this.hrWelfareCompensationRepository.getStaffWelfareCompensation(hrWelfareCompensationDTO);
        return list;
    }

    @Override
    public HrWelfareCompensationStatistics getStaffMakeUpStatistics(String clientId, String staffId) {
        return this.hrWelfareCompensationRepository.getStaffMakeUpStatistics(clientId, staffId);
    }

    /** 弃用 */
    @Override
    public List<HrWelfareCompensation> getStaffWelfareCompensationByPaymentDate(String clientId, String staffId) {
        return this.hrWelfareCompensationRepository.getStaffWelfareCompensationByPaymentDate(BillEnum.BillType.SALARY_BILL.getKey(), null);
    }

    /**
     * 创建员工福利补差统计
     *
     * @param hrWelfareCompensationDTO
     * @return
     */
    @Override
    public HrWelfareCompensationDTO createHrWelfareCompensation(HrWelfareCompensationDTO hrWelfareCompensationDTO) {
        log.info("Create new HrWelfareCompensation:{}", hrWelfareCompensationDTO);

        HrWelfareCompensation hrWelfareCompensation = this.hrWelfareCompensationMapper.toEntity(hrWelfareCompensationDTO);
        this.hrWelfareCompensationRepository.insert(hrWelfareCompensation);
        return this.hrWelfareCompensationMapper.toDto(hrWelfareCompensation);
    }

    /**
     * 修改员工福利补差统计
     *
     * @param hrWelfareCompensationDTO
     * @return
     */
    @Override
    public Optional<HrWelfareCompensationDTO> updateHrWelfareCompensation(HrWelfareCompensationDTO hrWelfareCompensationDTO) {
        return Optional.ofNullable(this.hrWelfareCompensationRepository.selectById(hrWelfareCompensationDTO.getId()))
            .map(roleTemp -> {
                HrWelfareCompensation hrWelfareCompensation = this.hrWelfareCompensationMapper.toEntity(hrWelfareCompensationDTO);
                this.hrWelfareCompensationRepository.updateById(hrWelfareCompensation);
                log.info("Update HrWelfareCompensation:{}", hrWelfareCompensationDTO);
                return hrWelfareCompensationDTO;
            });
    }

    /**
     * 查询员工福利补差统计详情
     *
     * @param id
     * @return
     */
    @Override
    public HrWelfareCompensationDTO getHrWelfareCompensation(String id) {
        log.info("Get HrWelfareCompensation :{}", id);

        HrWelfareCompensation hrWelfareCompensation = this.hrWelfareCompensationRepository.selectById(id);
        return this.hrWelfareCompensationMapper.toDto(hrWelfareCompensation);
    }

    /**
     * 删除员工福利补差统计
     *
     * @param id
     */
    @Override
    public void deleteHrWelfareCompensation(String id) {
        Optional.ofNullable(this.hrWelfareCompensationRepository.selectById(id))
            .ifPresent(hrWelfareCompensation -> {
                this.hrWelfareCompensationRepository.deleteById(id);
                log.info("Delete HrWelfareCompensation:{}", hrWelfareCompensation);
            });
    }

    /**
     * 批量删除员工福利补差统计
     *
     * @param ids
     */
    @Override
    public void deleteHrWelfareCompensation(List<String> ids) {
        log.info("Delete HrWelfareCompensations:{}", ids);
        // 员工福利配置回退到上次编辑之前
        if (!ids.isEmpty()) {
            // 福利基数补差
            List<String> staffIdList = this.hrWelfareCompensationRepository.selectStaffIdListByIds(ids);
            staffIdList.forEach(ls -> {
                // 基数
                String welfareBaseKey = RedisKeyEnum.currencyKey.WELFARE_BASE.getValue() + ls;
                Object welfareBaseObject = redisCache.getCacheObject(welfareBaseKey);
                if (welfareBaseObject != null) {
                    HrEmployeeWelfareDTO hrEmployeeWelfareOld = JSON.parseObject(welfareBaseObject.toString(), HrEmployeeWelfareDTO.class);
                    this.hrStaffEmolumentRepository.updateWelfareById(hrEmployeeWelfareOld);
                    redisCache.deleteObject(welfareBaseKey);
                }

                // 缴费年月
                String paymentDateKey = RedisKeyEnum.currencyKey.PAYMENT_DATE.getValue() + ls;
                Object paymentDateObject = redisCache.getCacheObject(paymentDateKey);
                if (paymentDateObject != null) {
                    HrEmployeeWelfareDTO hrEmployeeWelfareOld = JSON.parseObject(paymentDateObject.toString(), HrEmployeeWelfareDTO.class);
                    this.hrStaffEmolumentRepository.updateOldPaymentDate(hrEmployeeWelfareOld);
                    redisCache.deleteObject(paymentDateKey);
                }
            });

            // 公积金补差
            List<String> accumulationFundIdList = this.hrWelfareCompensationRepository.selectAccumulationFundIdListByIds(ids);
            accumulationFundIdList.forEach(ls -> {
                String accumulationFundKey = RedisKeyEnum.currencyKey.ACCUMULATION_FUND.getValue() + ls;
                Object accumulationFundObject = redisCache.getCacheObject(accumulationFundKey);
                if (accumulationFundObject != null) {
                    HrAccumulationFund hrAccumulationFund = JSON.parseObject(accumulationFundObject.toString(), HrAccumulationFund.class);
                    this.hrAccumulationFundRepository.updateById(hrAccumulationFund);
                }
            });

            // 社保补差
            List<String> socialSecurityIdList = this.hrWelfareCompensationRepository.selectSocialSecurityIdListByIds(ids);
            socialSecurityIdList.forEach(ls -> {
                String socialSecurityKey = RedisKeyEnum.currencyKey.SOCIAL_SECURITY.getValue() + ls;
                Object socialSecurityObject = redisCache.getCacheObject(socialSecurityKey);
                if (socialSecurityObject != null) {
                    HrSocialSecurity hrSocialSecurity = JSON.parseObject(socialSecurityObject.toString(), HrSocialSecurity.class);
                    this.hrSocialSecurityRepository.updateById(hrSocialSecurity);
                }
            });


            /*HrWelfareCompensation hrWelfareCompensation = this.hrWelfareCompensationRepository.selectById(ids.get(0));
            HrWelfareCompensationRecord hrWelfareCompensationRecord = this.hrWelfareCompensationRecordService.selectById(hrWelfareCompensation.getRecordId());
            HrStaffEmolument hrStaffEmolument = new HrStaffEmolument();
            hrStaffEmolument.setStaffId(hrWelfareCompensation.getStaffId());
            hrStaffEmolument.setSocialSecurityCardinal(hrWelfareCompensationRecord.getSocialSecurityCardinalBaseOld());
            hrStaffEmolument.setMedicalInsuranceCardinal(hrWelfareCompensationRecord.getMedicalInsuranceCardinalBaseOld());
            hrStaffEmolument.setAccumulationFundCardinal(hrWelfareCompensationRecord.getAccumulationFundCardinalBaseOld());
            hrStaffEmolument.setPayYear(hrWelfareCompensation.getPayYear());
            hrStaffEmolument.setPayMonthly(hrWelfareCompensation.getPayMonthly());
            this.hrStaffEmolumentRepository.rollBackStaffEmolumentByStaffId(hrStaffEmolument);*/
        }
        // 删除recordId
        this.hrWelfareCompensationRecordService.deleteByWelfareCompensationIds(ids);
        this.hrWelfareCompensationRepository.deleteBatchIds(ids);
    }

    @Override
    public void confirmHrWelfareCompensation(List<String> ids) {
        this.hrWelfareCompensationRepository.updateConfirmByIds(ids);
        // 新增或更新员工每月福利配置
        List<HrWelfareCompensation> hrWelfareCompensationList = this.hrWelfareCompensationRepository.selectByIds(ids);
        this.hrStaffWelfareRecordService.insertOrUpdateStaffWelfareRecord(hrWelfareCompensationList);

    }

    /**
     * 分页查询员工福利补差统计
     *
     * @param hrWelfareCompensationDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrWelfareCompensationDTO> findPage(HrWelfareCompensationDTO hrWelfareCompensationDTO, Long pageNumber, Long pageSize) {
        Page<HrWelfareCompensation> page = new Page<>(pageNumber, pageSize);
        return this.hrWelfareCompensationRepository.findPage(page, hrWelfareCompensationDTO);
    }

    @Override
    public List<String> updateEmployeeWelfare(HrEmployeeWelfareDTO hrEmployeeWelfareNew) {
        HrStaffEmolumentDTO hrStaffEmolument = this.hrStaffEmolumentRepository.getEmolumentByStaffId(hrEmployeeWelfareNew.getId());

        if (StringUtils.isBlank(hrEmployeeWelfareNew.getPaymentDate())) {
            throw new CommonException("缴费年月不能为空！");
        }

        String[] paymentDateSplit = hrEmployeeWelfareNew.getPaymentDate().split("-");
        int paymentYear = Integer.parseInt(paymentDateSplit[0]);
        int paymentMonth = Integer.parseInt(paymentDateSplit[1]);
        hrEmployeeWelfareNew.setPayYear(paymentYear);
        hrEmployeeWelfareNew.setPayMonthly(paymentMonth);

        // 检查数据是否更新(基数、缴费年月)
        if (this.checkIsUpdate(hrEmployeeWelfareNew, hrStaffEmolument)) {
            this.hrStaffEmolumentRepository.updateByStaffId(hrEmployeeWelfareNew);
            return null;
        }

        // 检查缴费年月是否为本年
        if (LocalDate.now().getYear() != paymentYear) {
            throw new CommonException("缴费年月只能选择本年年度！");
        }

        // 检查是否为大于本月
        if (paymentMonth > LocalDate.now().getMonthValue()) {
            throw new CommonException("缴费年月月份不能选择本月以后月份！");
        }
        // 检查用户薪酬参数是否完整
        // this.checkWelfareIsComplete(hrEmployeeWelfareNew);

        // 获取更新前薪酬参数信息
        HrEmployeeWelfareDTO hrEmployeeWelfareOld = this.hrTalentStaffRepository.findEmployeeWelfareByStaffId(hrEmployeeWelfareNew.getId());

        List<String> ids = new ArrayList<>();
        int loopEndMonth = 0;
        // 获取该员工公司的最近审核通过账单
        HrFeeReview hrFeeReview = this.hrFeeReviewRepository.selectNewestRecordByClientId(hrEmployeeWelfareNew.getClientId());
        if (hrFeeReview == null) {
            loopEndMonth = LocalDate.now().getMonthValue() - 1;
        } else {
            // 判断出账单缴费年月是否跨年
            if (hrFeeReview.getPayYear() == LocalDate.now().getYear()) {
                loopEndMonth = hrFeeReview.getPayMonthly();
            }
        }
        // 缴费年月与基数发生变动后，需要计算补差
        if (hrEmployeeWelfareOld.getPayMonthly() != null && loopEndMonth != 0) {
            // 检查新旧基数是否发生变动并添加日志记录
            HrWelfareCompensationRecord hrWelfareCompensationRecord = this.checkBaseChange(hrEmployeeWelfareNew, hrEmployeeWelfareOld);
            if (hrWelfareCompensationRecord != null) {
                // 获取员工入职日期（若选择补差缴费年月小于员工入职日期，只补（计算）员工个人缴纳部分的差额）
                HrStaffWorkExperience hrStaffWorkExperience = this.hrStaffWorkExperienceRepository.getStaffCurrentWorkExperience(hrEmployeeWelfareNew.getId());
                if (hrStaffWorkExperience == null) {
                    throw new CommonException("未查询到员工在职公司信息！");
                }

                String redisKey = RedisKeyEnum.currencyKey.WELFARE_BASE.getValue() + hrEmployeeWelfareOld.getId();
                this.redisCache.setCacheObject(redisKey, JSON.toJSONString(hrEmployeeWelfareOld), 24, TimeUnit.HOURS);

                for (int i = paymentMonth; i <= loopEndMonth; i++) {
                    HrWelfareCompensation hrWelfareCompensation = this.calculateCompensation(hrEmployeeWelfareOld, hrEmployeeWelfareNew, paymentYear, i, hrWelfareCompensationRecord.getId(), hrStaffWorkExperience.getBoardDate());
                    ids.add(hrWelfareCompensation.getId());
                }
            }
        }
        // 更新员工薪酬参数
        this.hrStaffEmolumentRepository.updateByStaffId(hrEmployeeWelfareNew);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_EMOLUMENT.getValue(),
            BusinessTypeEnum.UPDATE.getKey(),
            JSON.toJSONString(hrEmployeeWelfareNew),
            HrEmployeeWelfareDTO.class,
            null,
            JSON.toJSONString(hrStaffEmolument),
            JSON.toJSONString(hrEmployeeWelfareNew),
            null,
            HrEmployeeWelfareDTO.class
        );
        return ids;
    }

    /**
     * 检查基本数据是否修改
     *
     * @param hrEmployeeWelfareNew
     * @param hrStaffEmolument
     * @return
     */
    private boolean checkIsUpdate(HrEmployeeWelfareDTO hrEmployeeWelfareNew, HrStaffEmolumentDTO hrStaffEmolument) {
        return BigDecimalCompare.of(hrEmployeeWelfareNew.getUnitPensionCardinal()).eq(hrStaffEmolument.getUnitPensionCardinal())
            && BigDecimalCompare.of(hrEmployeeWelfareNew.getUnitUnemploymentCardinal()).eq(hrStaffEmolument.getUnitUnemploymentCardinal())
            && BigDecimalCompare.of(hrEmployeeWelfareNew.getWorkInjuryCardinal()).eq(hrStaffEmolument.getWorkInjuryCardinal())
            && BigDecimalCompare.of(hrEmployeeWelfareNew.getUnitMaternityCardinal()).eq(hrStaffEmolument.getUnitMaternityCardinal())
            && BigDecimalCompare.of(hrEmployeeWelfareNew.getMedicalInsuranceCardinal()).eq(hrStaffEmolument.getMedicalInsuranceCardinal())
            && BigDecimalCompare.of(hrEmployeeWelfareNew.getPersonalPensionCardinal()).eq(hrStaffEmolument.getPersonalPensionCardinal())
            && BigDecimalCompare.of(hrEmployeeWelfareNew.getPersonalUnemploymentCardinal()).eq(hrStaffEmolument.getPersonalUnemploymentCardinal())
            && BigDecimalCompare.of(hrEmployeeWelfareNew.getMedicalInsuranceCardinalPersonal()).eq(hrStaffEmolument.getMedicalInsuranceCardinalPersonal())
            && BigDecimalCompare.of(hrEmployeeWelfareNew.getAccumulationFundCardinal()).eq(hrStaffEmolument.getAccumulationFundCardinal())
            && BigDecimalCompare.of(hrEmployeeWelfareNew.getPersonalMaternityCardinal()).eq(hrStaffEmolument.getPersonalMaternityCardinal())
            && hrEmployeeWelfareNew.getPayYear().equals(hrStaffEmolument.getPayYear())
            && hrEmployeeWelfareNew.getPayMonthly().equals(hrStaffEmolument.getPayMonthly());
    }

    /**
     * 检查用户薪酬参数配置是否完整
     *
     * @param hrEmployeeWelfareNew
     * @return void
     * <AUTHOR>
     * @date 2021/10/29
     **/
    private void checkWelfareIsComplete(HrEmployeeWelfareDTO hrEmployeeWelfareNew) {
        if (hrEmployeeWelfareNew.getSocialSecurityCardinal() == null) {
            throw new CommonException("员工社保基数不能为空！");
        }
        if (hrEmployeeWelfareNew.getMedicalInsuranceCardinal() == null) {
            throw new CommonException("员工医保基数不能为空！");
        }
        if (hrEmployeeWelfareNew.getAccumulationFundCardinal() == null) {
            throw new CommonException("员工公积金基数不能为空！");
        }
    }

    @Override
    public List<String> batchUpdateEmployeeWelfare(HrEmployeeWelfareDTO hrEmployeeWelfareDTO) {
//        if (hrEmployeeWelfareDTO.getStaffIds().isEmpty()) {
//            throw new CommonException("请至少选择一个员工！");
//        }
        List<String> ids = new ArrayList<>();
        if (hrEmployeeWelfareDTO.getUnitPensionCardinal() == null
            && hrEmployeeWelfareDTO.getUnitUnemploymentCardinal() == null
            && hrEmployeeWelfareDTO.getWorkInjuryCardinal() == null
            && hrEmployeeWelfareDTO.getUnitMaternityCardinal() == null
            && hrEmployeeWelfareDTO.getMedicalInsuranceCardinal() == null
            && hrEmployeeWelfareDTO.getPersonalPensionCardinal() == null
            && hrEmployeeWelfareDTO.getPersonalUnemploymentCardinal() == null
            && hrEmployeeWelfareDTO.getPersonalMaternityCardinal() == null
            && hrEmployeeWelfareDTO.getMedicalInsuranceCardinalPersonal() == null
            && hrEmployeeWelfareDTO.getUnitLargeMedicalExpense() == null
            && hrEmployeeWelfareDTO.getReplenishWorkInjuryExpense() == null
            && hrEmployeeWelfareDTO.getPersonalLargeMedicalExpense() == null
            && hrEmployeeWelfareDTO.getAccumulationFundCardinal() == null
            && hrEmployeeWelfareDTO.getUnitEnterpriseAnnuity() == null
            && hrEmployeeWelfareDTO.getCommercialInsurance() == null
            && hrEmployeeWelfareDTO.getPersonalEnterpriseAnnuity() == null
            && hrEmployeeWelfareDTO.getPaymentDate() == null
        ){
            return ids;
        }
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        // 判断所选员工基数是否一致
        List<HrEmployeeWelfareDTO> hrEmployeeWelfareDTOS = this.hrTalentStaffRepository.findEmployeeWelfareList(hrEmployeeWelfareDTO,clientIds);
        if (hrEmployeeWelfareDTOS.isEmpty()) {
            throw new CommonException("未查询到员工薪酬参数数据！");
        }
        /*Map<BigDecimal, List<HrStaffEmolument>> socialSecurityCardinalCollect = hrStaffEmoluments.stream().filter(ls -> ls.getSocialSecurityCardinal() != null).collect(Collectors.groupingBy(HrStaffEmolument::getSocialSecurityCardinal));
        if (socialSecurityCardinalCollect.size() > 1) {
            throw new CommonException("当前所选员工社保基数不统一，无法批量修改，请重新选择！");
        }
        Map<BigDecimal, List<HrStaffEmolument>> medicalInsuranceBaseCollect = hrStaffEmoluments.stream().filter(ls -> ls.getMedicalInsuranceCardinal() != null).collect(Collectors.groupingBy(HrStaffEmolument::getMedicalInsuranceCardinal));
        if (medicalInsuranceBaseCollect.size() > 1) {
            throw new CommonException("当前所选员工医保基数不统一，无法批量修改，请重新选择！");
        }
        Map<BigDecimal, List<HrStaffEmolument>> providentFundBaseCollect = hrStaffEmoluments.stream().filter(ls -> ls.getAccumulationFundCardinal() != null).collect(Collectors.groupingBy(HrStaffEmolument::getAccumulationFundCardinal));
        if (providentFundBaseCollect.size() > 1) {
            throw new CommonException("当前所选员工公积金基数不统一，无法批量修改，请重新选择！");
        }*/
        List<String> staffIds = hrEmployeeWelfareDTOS.stream().map(HrEmployeeWelfareDTO::getId).collect(Collectors.toList());
        List<HrTalentStaff> hrTalentStaffList = this.hrTalentStaffRepository.selectBatchIds(staffIds);
        staffIds.forEach(ls -> {
            hrEmployeeWelfareDTO.setId(ls);
            if (StringUtils.isBlank(hrEmployeeWelfareDTO.getPaymentDate())) {
                this.updateEmployeeWelfareOnly(hrEmployeeWelfareDTO);
            } else {
                HrTalentStaff hrTalentStaff = hrTalentStaffList.stream().filter(lst -> lst.getId().equals(ls)).findAny().orElse(new HrTalentStaff());
                hrEmployeeWelfareDTO.setClientId(hrTalentStaff.getClientId());
                List<String> list = this.updateEmployeeWelfare(hrEmployeeWelfareDTO);
                if (list != null && !list.isEmpty()) {
                    ids.addAll(list);
                }
            }
        });
        return ids;
    }

    private void updateEmployeeWelfareOnly(HrEmployeeWelfareDTO hrEmployeeWelfareDTO) {
        HrStaffEmolument hrStaffEmolument = this.hrStaffEmolumentRepository.getByStaffId(hrEmployeeWelfareDTO.getId());
        this.hrStaffEmolumentRepository.updateByStaffId(hrEmployeeWelfareDTO);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_EMOLUMENT.getValue(),
            BusinessTypeEnum.UPDATE.getKey(),
            JSON.toJSONString(hrEmployeeWelfareDTO),
            HrEmployeeWelfareDTO.class,
            null,
            JSON.toJSONString(hrStaffEmolument),
            JSON.toJSONString(hrEmployeeWelfareDTO),
            null,
            HrEmployeeWelfareDTO.class
        );
    }

    @Override
    public IPage<HrWelfareCompensationDTO> findSalaryTaxPage(HrWelfareCompensationDTO hrWelfareCompensationDTO, Long pageNumber, Long pageSize) {
        Page<HrWelfareCompensationDTO> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        return this.hrWelfareCompensationRepository.findSalaryTaxPage(page, hrWelfareCompensationDTO, clientIds);
    }

    @Override
    public HrWelfareAdditionDTO getSalaryTaxDetail(String id) {
        HrWelfareAdditionDTO hrWelfareAdditionDTO = this.hrWelfareCompensationRepository.getSalaryTaxDetail(id);
        // 获取操作日志
        if (hrWelfareAdditionDTO != null) {
            HrWelfareCompensationRecordDTO recordDTO = this.hrWelfareCompensationRecordRepository.getById(hrWelfareAdditionDTO.getRecordId());
            hrWelfareAdditionDTO.setRecordList(Collections.singletonList(recordDTO));
            // 获取账单id =》 获取账单明细对应员工福利补差使用记录 =》 获取补差使用记录对应的福利补差统计 =》 获取对应补差操作日志
//            HrMakeUpUseRecord hrMakeUpUseRecord = this.hrMakeUpUseRecordRepository.selectByMakeUpId(id);
//            if (hrMakeUpUseRecord != null) {
//                List<HrWelfareCompensationRecordDTO> list = this.hrWelfareCompensationRecordService.selectByBillId(hrMakeUpUseRecord.getBillId());
//                hrWelfareAdditionDTO.setRecordList(list);
//            }
        }
        return hrWelfareAdditionDTO;
    }

    @Override
    public String salaryTaxExport(HrWelfareCompensationDTO hrWelfareCompensationDTO, HttpServletResponse response) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrWelfareCompensationDTO> list = this.hrWelfareCompensationRepository.selectSalaryTaxList(hrWelfareCompensationDTO,clientIds);
        if (list.isEmpty()) {
            throw new CommonException("未查询到相关数据！");
        }
        // 操作日志
        int listSize = list.size();
        List<String> ids = list.stream().map(HrWelfareCompensationDTO::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "薪金税差", HrWelfareCompensationDTO.class);
        // ExcelUtils.exportExcel(list, "薪金税差", HrWelfareCompensationDTO.class, response);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.SALARY_TAX.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 计算补差
     *
     * @param hrEmployeeWelfareOld
     * @param hrEmployeeWelfareNew
     * @param payYear
     * @param payMonthly
     * @param recordId
     * @param boardDate
     * @return cn.casair.domain.HrWelfareCompensation
     * <AUTHOR>
     * @date 2021/10/28
     **/
    private HrWelfareCompensation calculateCompensation(HrEmployeeWelfareDTO hrEmployeeWelfareOld, HrEmployeeWelfareDTO hrEmployeeWelfareNew, int payYear, int payMonthly, String recordId, LocalDate boardDate) {
        // 查询缴费年月员工福利记录
        Map<String, Object> query = new HashMap<>();
        query.put("staffId", hrEmployeeWelfareNew.getId());
        query.put("payYear", payYear);
        query.put("payMonthly", 0);
        HrStaffWelfareRecord benchmarkRecord = this.hrStaffWelfareRecordService.selectByObject(query);
        // 检查基准数据
        if (benchmarkRecord == null) {
            benchmarkRecord = new HrStaffWelfareRecord()
                .setStaffId(hrEmployeeWelfareOld.getId())
                .setPayYear(payYear)
                .setPayMonthly(0)
//                .setSocialSecurityCardinalBase(hrEmployeeWelfareOld.getSocialSecurityCardinal())
                .setUnitPensionCardinalBase(hrEmployeeWelfareOld.getUnitPensionCardinal())
                .setUnitUnemploymentCardinalBase(hrEmployeeWelfareOld.getUnitUnemploymentCardinal())
                .setWorkInjuryCardinalBase(hrEmployeeWelfareOld.getWorkInjuryCardinal())
                .setUnitMaternityCardinalBase(hrEmployeeWelfareOld.getUnitMaternityCardinal())
                .setMedicalInsuranceCardinalBase(hrEmployeeWelfareOld.getMedicalInsuranceCardinal())
//                .setSocialSecurityCardinalBasePersonal(hrEmployeeWelfareOld.getSocialSecurityCardinalPersonal())
                .setPersonalPensionCardinalBase(hrEmployeeWelfareOld.getPersonalPensionCardinal())
                .setPersonalUnemploymentCardinalBase(hrEmployeeWelfareOld.getPersonalUnemploymentCardinal())
                .setMedicalInsuranceCardinalBasePersonal(hrEmployeeWelfareOld.getMedicalInsuranceCardinalPersonal())
                .setAccumulationFundCardinalBase(hrEmployeeWelfareOld.getAccumulationFundCardinal())
                .setUnitLargeMedicalExpenseBase(hrEmployeeWelfareOld.getUnitLargeMedicalExpense())
                .setReplenishWorkInjuryExpenseBase(hrEmployeeWelfareOld.getReplenishWorkInjuryExpense())
                .setPersonalLargeMedicalExpenseBase(hrEmployeeWelfareOld.getPersonalLargeMedicalExpense())
                .setPersonalMaternityCardinalBase(hrEmployeeWelfareOld.getPersonalMaternityCardinal())
                .setPersonalMaternityScale(hrEmployeeWelfareOld.getPersonalMaternity())
                .setUnitPensionScale(hrEmployeeWelfareOld.getUnitPension())
                .setUnitMedicalScale(hrEmployeeWelfareOld.getUnitMedical())
                .setWorkInjuryScale(hrEmployeeWelfareOld.getWorkInjury())
                .setUnitUnemploymentScale(hrEmployeeWelfareOld.getUnitUnemployment())
                .setUnitMaternityScale(hrEmployeeWelfareOld.getUnitMaternity())
                .setPersonalPensionScale(hrEmployeeWelfareOld.getPersonalPension())
                .setPersonalMedicalScale(hrEmployeeWelfareOld.getPersonalMedical())
                .setPersonalUnemploymentScale(hrEmployeeWelfareOld.getPersonalUnemployment())
                .setUnitAccumulationFundScale(hrEmployeeWelfareOld.getUnitScale())
                .setPersonalAccumulationFundScale(hrEmployeeWelfareOld.getPersonageScale());
            this.hrStaffWelfareRecordRepository.insert(benchmarkRecord);
        }

        query.put("payMonthly", payMonthly);
        HrStaffWelfareRecord hrStaffWelfareRecord = this.hrStaffWelfareRecordService.selectByObject(query);

        // 补差是否计算标志
        boolean calculateFlag = payYear == boardDate.getYear() && payMonthly < boardDate.getMonthValue();
        // 若入职年份与补差年份为同一年 且入职月份大于当前循环补差月份，只补员工个人缴纳部分的差额

        BigDecimal unitPensionOld = BigDecimal.ZERO;
        BigDecimal unitPensionNew = BigDecimal.ZERO;
        BigDecimal unitUnemploymentOld = BigDecimal.ZERO;
        BigDecimal unitUnemploymentNew = BigDecimal.ZERO;
        BigDecimal unitMedicalOld = BigDecimal.ZERO;
        BigDecimal unitMedicalNew = BigDecimal.ZERO;
        BigDecimal unitWorkInjuryOld = BigDecimal.ZERO;
        BigDecimal unitWorkInjuryNew = BigDecimal.ZERO;
        BigDecimal unitMaternityOld = BigDecimal.ZERO;
        BigDecimal unitMaternityNew = BigDecimal.ZERO;
        BigDecimal unitAccumulationFundOld = BigDecimal.ZERO;
        BigDecimal unitAccumulationFundNew = BigDecimal.ZERO;
        BigDecimal personagePensionOld = BigDecimal.ZERO;
        BigDecimal personagePensionNew = BigDecimal.ZERO;
        BigDecimal personageUnemploymentOld = BigDecimal.ZERO;
        BigDecimal personageUnemploymentNew = BigDecimal.ZERO;
        BigDecimal personalMedicalOld = BigDecimal.ZERO;
        BigDecimal personalMedicalNew = BigDecimal.ZERO;
        BigDecimal personageAccumulationFundOld = BigDecimal.ZERO;
        BigDecimal personageAccumulationFundNew = BigDecimal.ZERO;
        BigDecimal unitLargeMedicalOld = BigDecimal.ZERO;
        BigDecimal unitLargeMedicalNew = BigDecimal.ZERO;
        BigDecimal replenishWorkInjuryOld = BigDecimal.ZERO;
        BigDecimal replenishWorkInjuryNew = BigDecimal.ZERO;
        BigDecimal personalLargeMedicalOld = BigDecimal.ZERO;
        BigDecimal personalLargeMedicalNew = BigDecimal.ZERO;
        BigDecimal personalMaternityOld = BigDecimal.ZERO;
        BigDecimal personalMaternityNew = BigDecimal.ZERO;

        if (hrStaffWelfareRecord == null) {
            // 单位补差
            if (hrEmployeeWelfareNew.getUnitPensionCardinal() != null) {
                // 养老
                unitPensionOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getUnitPensionCardinalBase(), benchmarkRecord.getUnitPensionScale(), 2);
                unitPensionNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getUnitPensionCardinal(), benchmarkRecord.getUnitPensionScale(), 2);
            }
            if (hrEmployeeWelfareNew.getUnitUnemploymentCardinal() != null) {
                // 失业
                unitUnemploymentOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getUnitUnemploymentCardinalBase(), benchmarkRecord.getUnitUnemploymentScale(), 2);
                unitUnemploymentNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getUnitUnemploymentCardinal(), benchmarkRecord.getUnitUnemploymentScale(), 2);
            }
            if (hrEmployeeWelfareNew.getMedicalInsuranceCardinal() != null) {
                // 医疗
                unitMedicalOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getMedicalInsuranceCardinalBase(), benchmarkRecord.getUnitMedicalScale(), 2);
                unitMedicalNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getMedicalInsuranceCardinal(), benchmarkRecord.getUnitMedicalScale(), 2);
            }
            if (hrEmployeeWelfareNew.getWorkInjuryCardinal() != null) {
                // 工伤
                unitWorkInjuryOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getWorkInjuryCardinalBase(), benchmarkRecord.getWorkInjuryScale(), 2);
                unitWorkInjuryNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getWorkInjuryCardinal(), benchmarkRecord.getWorkInjuryScale(), 2);
            }
            if (hrEmployeeWelfareNew.getUnitMaternityCardinal() != null) {
                // 生育
                unitMaternityOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getUnitMaternityCardinalBase(), benchmarkRecord.getUnitMaternityScale(), 2);
                unitMaternityNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getUnitMaternityCardinal(), benchmarkRecord.getUnitMaternityScale(), 2);
            }
            if (hrEmployeeWelfareNew.getAccumulationFundCardinal() != null) {
                // 住房公积金
                unitAccumulationFundOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getAccumulationFundCardinalBase(), benchmarkRecord.getUnitAccumulationFundScale(), 2);
                unitAccumulationFundNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getAccumulationFundCardinal(), benchmarkRecord.getUnitAccumulationFundScale(), 2);
            }
            if (hrEmployeeWelfareNew.getUnitLargeMedicalExpense() != null) {
                // 大额医疗
                unitLargeMedicalOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getUnitLargeMedicalExpenseBase(), new BigDecimal(1), 2);
                unitLargeMedicalNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getUnitLargeMedicalExpense(), new BigDecimal(1), 2);
            }
            if (hrEmployeeWelfareNew.getReplenishWorkInjuryExpense() != null) {
                // 补充工伤
                replenishWorkInjuryOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getReplenishWorkInjuryExpenseBase(), new BigDecimal(1), 2);
                replenishWorkInjuryNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getReplenishWorkInjuryExpense(), new BigDecimal(1), 2);
            }

            // 个人补差
            if (hrEmployeeWelfareNew.getPersonalPensionCardinal() != null) {
                // 养老
                personagePensionOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalPensionCardinalBase(), benchmarkRecord.getPersonalPensionScale(), 2);
                personagePensionNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getPersonalPensionCardinal(), benchmarkRecord.getPersonalPensionScale(), 2);
            }
            if (hrEmployeeWelfareNew.getPersonalUnemploymentCardinal() != null) {
                // 失业
                personageUnemploymentOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalUnemploymentCardinalBase(), benchmarkRecord.getPersonalUnemploymentScale(), 2);
                personageUnemploymentNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getPersonalUnemploymentCardinal(), benchmarkRecord.getPersonalUnemploymentScale(), 2);
            }
            if (hrEmployeeWelfareNew.getMedicalInsuranceCardinal() != null) {
                // 医疗
                personalMedicalOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getMedicalInsuranceCardinalBasePersonal(), benchmarkRecord.getPersonalMedicalScale(), 2);
                personalMedicalNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getMedicalInsuranceCardinal(), benchmarkRecord.getPersonalMedicalScale(), 2);
            }
            if (hrEmployeeWelfareNew.getAccumulationFundCardinal() != null) {
                // 住房公积金
                personageAccumulationFundOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getAccumulationFundCardinalBase(), benchmarkRecord.getPersonalAccumulationFundScale(), 2);
                personageAccumulationFundNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getAccumulationFundCardinal(), benchmarkRecord.getPersonalAccumulationFundScale(), 2);
            }
            if (hrEmployeeWelfareNew.getPersonalMaternityCardinal() != null) {
                // 生育
                personalMaternityOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalMaternityCardinalBase(), benchmarkRecord.getPersonalMaternityScale(), 2);
                personalMaternityNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getPersonalMaternityCardinal(), benchmarkRecord.getPersonalMaternityScale(), 2);
            }
            if (hrEmployeeWelfareNew.getPersonalLargeMedicalExpense() != null) {
                // 大额医疗
                personalLargeMedicalOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalLargeMedicalExpenseBase(), new BigDecimal(1), 2);
                personalLargeMedicalNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getPersonalLargeMedicalExpense(), new BigDecimal(1), 2);
            }
        } else {
            // 单位补差
            if (hrEmployeeWelfareNew.getUnitPensionCardinal() != null) {
                // 养老
                unitPensionOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getUnitPensionCardinalBase(), hrStaffWelfareRecord.getUnitPensionScale(), 2);
                unitPensionNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getUnitPensionCardinal(), hrStaffWelfareRecord.getUnitPensionScale(), 2);
            }
            if (hrEmployeeWelfareNew.getUnitUnemploymentCardinal() != null) {
                // 失业
                unitUnemploymentOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getUnitUnemploymentCardinalBase(), hrStaffWelfareRecord.getUnitUnemploymentScale(), 2);
                unitUnemploymentNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getUnitUnemploymentCardinal(), hrStaffWelfareRecord.getUnitUnemploymentScale(), 2);
            }
            if (hrEmployeeWelfareNew.getMedicalInsuranceCardinal() != null) {
                // 医疗
                unitMedicalOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getMedicalInsuranceCardinalBase(), hrStaffWelfareRecord.getUnitMedicalScale(), 2);
                unitMedicalNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getMedicalInsuranceCardinal(), hrStaffWelfareRecord.getUnitMedicalScale(), 2);
            }
            if (hrEmployeeWelfareNew.getWorkInjuryCardinal() != null) {
                // 工伤
                unitWorkInjuryOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getWorkInjuryCardinalBase(), hrStaffWelfareRecord.getWorkInjuryScale(), 2);
                unitWorkInjuryNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getWorkInjuryCardinal(), hrStaffWelfareRecord.getWorkInjuryScale(), 2);
            }
            if (hrEmployeeWelfareNew.getUnitMaternityCardinal() != null) {
                // 生育
                unitMaternityOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getUnitMaternityCardinalBase(), hrStaffWelfareRecord.getUnitMaternityScale(), 2);
                unitMaternityNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getUnitMaternityCardinal(), hrStaffWelfareRecord.getUnitMaternityScale(), 2);
            }
            if (hrEmployeeWelfareNew.getAccumulationFundCardinal() != null) {
                // 住房公积金
                unitAccumulationFundOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getAccumulationFundCardinalBase(), hrStaffWelfareRecord.getUnitAccumulationFundScale(), 2);
                unitAccumulationFundNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getAccumulationFundCardinal(), hrStaffWelfareRecord.getPersonalAccumulationFundScale(), 2);
            }
            if (hrEmployeeWelfareNew.getUnitLargeMedicalExpense() != null) {
                // 大额医疗
                unitLargeMedicalOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getUnitLargeMedicalExpenseBase(), new BigDecimal(1), 2);
                unitLargeMedicalNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getUnitLargeMedicalExpense(), new BigDecimal(1), 2);
            }
            if (hrEmployeeWelfareNew.getReplenishWorkInjuryExpense() != null) {
                // 补充工伤
                replenishWorkInjuryOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getReplenishWorkInjuryExpenseBase(), new BigDecimal(1), 2);
                replenishWorkInjuryNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getReplenishWorkInjuryExpense(), new BigDecimal(1), 2);
            }

            // 个人补差
            if (hrEmployeeWelfareNew.getPersonalPensionCardinal() != null) {
                // 养老
                personagePensionOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getPersonalPensionCardinalBase(), hrStaffWelfareRecord.getPersonalPensionScale(), 2);
                personagePensionNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getPersonalPensionCardinal(), hrStaffWelfareRecord.getPersonalPensionScale(), 2);
            }
            if (hrEmployeeWelfareNew.getPersonalUnemploymentCardinal() != null) {
                // 失业
                personageUnemploymentOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getPersonalUnemploymentCardinalBase(), hrStaffWelfareRecord.getPersonalUnemploymentScale(), 2);
                personageUnemploymentNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getPersonalUnemploymentCardinal(), hrStaffWelfareRecord.getPersonalUnemploymentScale(), 2);
            }
            if (hrEmployeeWelfareNew.getMedicalInsuranceCardinal() != null) {
                // 医疗
                personalMedicalOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getMedicalInsuranceCardinalBasePersonal(), hrStaffWelfareRecord.getPersonalMedicalScale(), 2);
                personalMedicalNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getMedicalInsuranceCardinal(), hrStaffWelfareRecord.getPersonalMedicalScale(), 2);
            }
            if (hrEmployeeWelfareNew.getAccumulationFundCardinal() != null) {
                // 住房公积金
                personageAccumulationFundOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getAccumulationFundCardinalBase(), hrStaffWelfareRecord.getPersonalAccumulationFundScale(), 2);
                personageAccumulationFundNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getAccumulationFundCardinal(), hrStaffWelfareRecord.getPersonalAccumulationFundScale(), 2);
            }
            if (hrEmployeeWelfareNew.getPersonalMaternityCardinal() != null) {
                // 生育
                personalMaternityOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(benchmarkRecord.getPersonalMaternityCardinalBase(), hrStaffWelfareRecord.getPersonalMaternityScale(), 2);
                personalMaternityNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getPersonalMaternityCardinal(), hrStaffWelfareRecord.getPersonalMaternityScale(), 2);
            }
            if (hrEmployeeWelfareNew.getPersonalLargeMedicalExpense() != null) {
                // 大额医疗
                personalLargeMedicalOld = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrStaffWelfareRecord.getPersonalLargeMedicalExpenseBase(), new BigDecimal(1), 2);
                personalLargeMedicalNew = calculateFlag ? BigDecimal.ZERO : CalculateUtils.decimalMultiply(hrEmployeeWelfareNew.getPersonalLargeMedicalExpense(), new BigDecimal(1), 2);
            }
        }

        // 更新员工每月福利配置
        // this.hrStaffWelfareRecordService.insertOrUpdateStaffWelfareRecord(hrStaffWelfareRecord, hrEmployeeWelfareNew, payYear, payMonthly);

        // 计算补差数据
        HrWelfareCompensation hrWelfareCompensation = new HrWelfareCompensation();
        hrWelfareCompensation.setIdNo(hrEmployeeWelfareOld.getCertificateNum());
        hrWelfareCompensation.setStaffId(hrEmployeeWelfareOld.getId());
        hrWelfareCompensation.setClientId(hrEmployeeWelfareOld.getClientId());
        hrWelfareCompensation.setPayYear(payYear);
        hrWelfareCompensation.setPayMonthly(payMonthly);
        // 单位补差
        hrWelfareCompensation.setUnitPension(CalculateUtils.decimalSubtraction(unitPensionNew, unitPensionOld));
        hrWelfareCompensation.setUnitUnemployment(CalculateUtils.decimalSubtraction(unitUnemploymentNew, unitUnemploymentOld));
        hrWelfareCompensation.setUnitMedical(CalculateUtils.decimalSubtraction(unitMedicalNew, unitMedicalOld));
        hrWelfareCompensation.setUnitInjury(CalculateUtils.decimalSubtraction(unitWorkInjuryNew, unitWorkInjuryOld));
        hrWelfareCompensation.setUnitMaternity(CalculateUtils.decimalSubtraction(unitMaternityNew, unitMaternityOld));
        hrWelfareCompensation.setUnitLargeMedical(CalculateUtils.decimalSubtraction(unitLargeMedicalNew, unitLargeMedicalOld));
        hrWelfareCompensation.setReplenishWorkInjury(CalculateUtils.decimalSubtraction(replenishWorkInjuryNew, replenishWorkInjuryOld));
        hrWelfareCompensation.setUnitSubtotal(CalculateUtils.decimalListAddition(hrWelfareCompensation.getUnitPension(), hrWelfareCompensation.getUnitUnemployment(),
            hrWelfareCompensation.getUnitMedical(), hrWelfareCompensation.getUnitInjury(), hrWelfareCompensation.getUnitMaternity()));
        // 个人补差
        hrWelfareCompensation.setPersonalPension(CalculateUtils.decimalSubtraction(personagePensionNew, personagePensionOld));
        hrWelfareCompensation.setPersonalUnemployment(CalculateUtils.decimalSubtraction(personageUnemploymentNew, personageUnemploymentOld));
        hrWelfareCompensation.setPersonalMedical(CalculateUtils.decimalSubtraction(personalMedicalNew, personalMedicalOld));
        hrWelfareCompensation.setPersonalLargeMedical(CalculateUtils.decimalSubtraction(personalLargeMedicalNew, personalLargeMedicalOld));
        hrWelfareCompensation.setPersonalMaternity(CalculateUtils.decimalSubtraction(personalMaternityNew, personalMaternityOld));
        hrWelfareCompensation.setPersonalSubtotal(CalculateUtils.decimalListAddition(hrWelfareCompensation.getPersonalPension(), hrWelfareCompensation.getPersonalUnemployment(),
            hrWelfareCompensation.getPersonalMedical(),hrWelfareCompensation.getPersonalMaternity()));
        // 社保补差总计
        hrWelfareCompensation.setSocialSecurityTotal(CalculateUtils.decimalListAddition(hrWelfareCompensation.getUnitSubtotal(), hrWelfareCompensation.getPersonalSubtotal()));
        // 住房公积金补差
        hrWelfareCompensation.setUnitAccumulationFund(CalculateUtils.decimalSubtraction(unitAccumulationFundNew, unitAccumulationFundOld));
        hrWelfareCompensation.setPersonalAccumulationFund(CalculateUtils.decimalSubtraction(personageAccumulationFundNew, personageAccumulationFundOld));
        // 公积金总计补差
        hrWelfareCompensation.setAccumulationFundTotal(CalculateUtils.decimalAddition(hrWelfareCompensation.getUnitAccumulationFund(), hrWelfareCompensation.getPersonalAccumulationFund()));

        hrWelfareCompensation.setIsUsed(-1);
        hrWelfareCompensation.setType(0);
        hrWelfareCompensation.setRecordId(recordId);

        this.hrWelfareCompensationRepository.insert(hrWelfareCompensation);
        return hrWelfareCompensation;
    }

    /**
     * 对比修改前后基数
     * 判断是否需要计算补差
     *
     * @param hrEmployeeWelfareNew
     * @param hrEmployeeWelfareOld
     * @return
     */
    private HrWelfareCompensationRecord checkBaseChange(HrEmployeeWelfareDTO hrEmployeeWelfareNew, HrEmployeeWelfareDTO hrEmployeeWelfareOld) {
        StringBuilder sb = new StringBuilder();
        boolean result = false;
//        if (!BigDecimalCompare.of(hrEmployeeWelfareOld.getSocialSecurityCardinal()).eq(hrEmployeeWelfareNew.getSocialSecurityCardinal())) {
//            sb.append("单位社保基数：修改前").append(hrEmployeeWelfareOld.getSocialSecurityCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getSocialSecurityCardinal()).append("。");
//            result = true;
//        }
        if (hrEmployeeWelfareNew.getUnitPensionCardinal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getUnitPensionCardinal()).eq(hrEmployeeWelfareNew.getUnitPensionCardinal())) {
            sb.append("单位养老基数：修改前").append(hrEmployeeWelfareOld.getUnitPensionCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getUnitPensionCardinal()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getUnitUnemploymentCardinal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getUnitUnemploymentCardinal()).eq(hrEmployeeWelfareNew.getUnitUnemploymentCardinal())) {
            sb.append("单位失业基数：修改前").append(hrEmployeeWelfareOld.getUnitUnemploymentCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getUnitUnemploymentCardinal()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getWorkInjuryCardinal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getWorkInjuryCardinal()).eq(hrEmployeeWelfareNew.getWorkInjuryCardinal())) {
            sb.append("单位工伤基数：修改前").append(hrEmployeeWelfareOld.getWorkInjuryCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getWorkInjuryCardinal()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getUnitMaternityCardinal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getUnitMaternityCardinal()).eq(hrEmployeeWelfareNew.getUnitMaternityCardinal())) {
            sb.append("单位生育基数：修改前").append(hrEmployeeWelfareOld.getUnitMaternityCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getUnitMaternityCardinal()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getMedicalInsuranceCardinal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getMedicalInsuranceCardinal()).eq(hrEmployeeWelfareNew.getMedicalInsuranceCardinal())) {
            sb.append("单位医疗基数：修改前").append(hrEmployeeWelfareOld.getMedicalInsuranceCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getMedicalInsuranceCardinal()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getUnitLargeMedicalExpense() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getUnitLargeMedicalExpense()).eq(hrEmployeeWelfareNew.getUnitLargeMedicalExpense())) {
            sb.append("单位大额医疗费用：修改前").append(hrEmployeeWelfareOld.getUnitLargeMedicalExpense()).append("，").append("修改后").append(hrEmployeeWelfareNew.getUnitLargeMedicalExpense()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getReplenishWorkInjuryExpense() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getReplenishWorkInjuryExpense()).eq(hrEmployeeWelfareNew.getReplenishWorkInjuryExpense())) {
            sb.append("单位补充工伤：修改前").append(hrEmployeeWelfareOld.getReplenishWorkInjuryExpense()).append("，").append("修改后").append(hrEmployeeWelfareNew.getReplenishWorkInjuryExpense()).append("。");
            result = true;
        }
//        if (!BigDecimalCompare.of(hrEmployeeWelfareOld.getSocialSecurityCardinalPersonal()).eq(hrEmployeeWelfareNew.getSocialSecurityCardinalPersonal())) {
//            sb.append("个人社保基数：修改前").append(hrEmployeeWelfareOld.getSocialSecurityCardinalPersonal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getSocialSecurityCardinalPersonal()).append("。");
//            result = true;
//        }
        if (hrEmployeeWelfareNew.getPersonalPensionCardinal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getPersonalPensionCardinal()).eq(hrEmployeeWelfareNew.getPersonalPensionCardinal())) {
            sb.append("个人养老基数：修改前").append(hrEmployeeWelfareOld.getPersonalPensionCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getPersonalPensionCardinal()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getPersonalUnemploymentCardinal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getPersonalUnemploymentCardinal()).eq(hrEmployeeWelfareNew.getPersonalUnemploymentCardinal())) {
            sb.append("个人失业基数：修改前").append(hrEmployeeWelfareOld.getPersonalUnemploymentCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getPersonalUnemploymentCardinal()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getMedicalInsuranceCardinalPersonal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getMedicalInsuranceCardinalPersonal()).eq(hrEmployeeWelfareNew.getMedicalInsuranceCardinalPersonal())) {
            sb.append("个人医疗基数：修改前").append(hrEmployeeWelfareOld.getMedicalInsuranceCardinalPersonal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getMedicalInsuranceCardinalPersonal()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getPersonalMaternityCardinal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getPersonalMaternityCardinal()).eq(hrEmployeeWelfareNew.getPersonalMaternityCardinal())) {
            sb.append("个人生育基数：修改前").append(hrEmployeeWelfareOld.getPersonalMaternityCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getPersonalMaternityCardinal()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getPersonalLargeMedicalExpense() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getPersonalLargeMedicalExpense()).eq(hrEmployeeWelfareNew.getPersonalLargeMedicalExpense())) {
            sb.append("个人大额医疗费用：修改前").append(hrEmployeeWelfareOld.getPersonalLargeMedicalExpense()).append("，").append("修改后").append(hrEmployeeWelfareNew.getPersonalLargeMedicalExpense()).append("。");
            result = true;
        }
        if (hrEmployeeWelfareNew.getAccumulationFundCardinal() != null && !BigDecimalCompare.of(hrEmployeeWelfareOld.getAccumulationFundCardinal()).eq(hrEmployeeWelfareNew.getAccumulationFundCardinal())) {
            sb.append("公积金基数：修改前").append(hrEmployeeWelfareOld.getAccumulationFundCardinal()).append("，").append("修改后").append(hrEmployeeWelfareNew.getAccumulationFundCardinal()).append("。");
            result = true;
        }

        // 产生变动 添加变动日志
        if (result) {
            return this.hrWelfareCompensationRecordService.addLog(hrEmployeeWelfareOld, hrEmployeeWelfareNew, sb.toString());
        }
        return null;
    }
}
