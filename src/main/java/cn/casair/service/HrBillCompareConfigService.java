package cn.casair.service;

import cn.casair.common.utils.excel.CellItem;
import cn.casair.domain.HrBillCompareConfig;
import cn.casair.dto.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 对账配置信息表服务类
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public interface HrBillCompareConfigService extends IService<HrBillCompareConfig> {

    /**
     * 根据账单核对类型获取费用类型项
     *
     * @param billCheckType
     * @return java.util.List<cn.casair.dto.EnumListDTO>
     * <AUTHOR>
     * @date 2021/11/9
     **/
    List<EnumDTO> getExpenseTypeItem(Integer billCheckType);

    /**
     * 创建对账配置信息表
     * @param hrBillCompareConfigDTO
     * @return
     */
    HrBillCompareConfigDTO createHrBillCompareConfig(HrBillCompareConfigDTO hrBillCompareConfigDTO);

    /**
     * 修改对账配置信息表
     * @param hrBillCompareConfigDTO
     * @return
     */
    Optional<HrBillCompareConfigDTO> updateHrBillCompareConfig(HrBillCompareConfigDTO hrBillCompareConfigDTO);

    /**
     * 查询对账配置信息表详情
     * @param id
     * @return
     */
    HrBillCompareConfigDTO getHrBillCompareConfig(String id);

    /**
     * 删除对账配置信息表
     * @param id
     */
    void deleteHrBillCompareConfig(String id);

    /**
     * 批量删除对账配置信息表
     * @param ids
     */
    void deleteHrBillCompareConfig(List<String> ids);

    /**
     * 分页查询对账配置信息表
     * @param hrBillCompareConfigDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(HrBillCompareConfigDTO hrBillCompareConfigDTO,Long pageNumber,Long pageSize);

    /**
     *  获取对账结果的表头
     * @param configId 对账配置id
     * @return
     */
    List<CellItem> getDynamicHeader(String configId);

    /**
     *  生成对账结果
     * @param hrBillCompareConfigDTO
     * @return
     */
    HrBillCompareConfigDTO generateBillCompareResult(HrBillCompareConfigDTO hrBillCompareConfigDTO);

    /**
     *  对账后保存补差
     * @param billConfigId 对账配置id
     * @param currentMonthUsed
     */
    void saveCompareMakeUp(String billConfigId, Integer currentMonthUsed);

    /**
     *  根据账单id和导盘类型删除对账配置和结果
     * @param billId 账单id
     * @param type 导盘类型 0 社保, 1 医保, 2 公积金,3 个税, 4第三方账单
     */
    void delByBillIdAndType(String billId, Integer type);

    /**
     * 所有客户是否存在账单
     * @param paymentDate 缴费年月
     * @param billType 账单类型
     * @return 客户信息
     */
    List<HrClientDTO> existenceSecurityBill(String paymentDate, Integer billType);

    /**
     * 海尔对账保存对账结果
     * @param billConfigIds
     */
    void haierSaveCompareResult(List<String> billConfigIds);

    /**
     * 导出不存在账单的客户信息
     * @param paymentDate
     * @param billType
     * @return
     */
    String existenceSecurityBillExport(String paymentDate, Integer billType);

    /**
     * 导出对账差异数据
     * @param hrBillCompareConfigDTO
     * @return
     */
    String exportBillCompareResult(HrBillCompareConfigDTO hrBillCompareConfigDTO);

    /**
     * 创建账单不可本月使用补差的客户
     * @param billConfigId
     * @return
     */
    List<HrBillDTO> getExitSecurityBill(String billConfigId);

    /**
     * 对账结果明细
     * @param resultDetailDTO
     * @return
     */
    Map<String, Object> findResultDetailList(HrBillCompareResultDetailDTO resultDetailDTO);

    /**
     * 导出对账结果明细
     *
     * @param resultDetailDTO
     * @return
     */
    String exportResultDetail(HrBillCompareResultDetailDTO resultDetailDTO);

    /**
     * 导出不可当月使用补差的客户信息
     *
     * @param hrBillDTO
     * @return
     */
    String exportExcludesClient(HrBillDTO hrBillDTO);

    /**
     * 查询薪酬账单
     * @param billConfigIds
     * @return
     */
    List<HrBillDTO> getExitTaxBill(List<String> billConfigIds);

    /**
     * 对账仅保存对账结果
     * @param billConfigIds 对账配置ID
     * @param lockStatus 是否锁定 0未锁定 1已锁定
     */
    void saveOnlyCompareResult(List<String> billConfigIds, Integer lockStatus);

    /**
     * 批量锁定对账结果或者批量保存补差
     *
     * @param hrBillCompareResultDTO
     */
    void batchLockOrSaveMakeUp(HrBillCompareResultDTO hrBillCompareResultDTO);

    /**
     * 批量保存补差
     *
     * @param list 对账结果
     * @param currentMonthUsed 是否当月使用补差
     */
    void saveBatchCompareMakeUp(List<HrBillCompareResultDTO> list, Integer currentMonthUsed);

    /**
     * 个税批量保存补差
     *
     * @param billConfigIds 对账配置ID
     * @param currentMonthUsed 是否当月使用补差
     */
    void savePersonalTaxMakeUp(List<String> billConfigIds, Integer currentMonthUsed);

}
