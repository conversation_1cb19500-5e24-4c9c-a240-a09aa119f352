package cn.casair.service;

import cn.casair.domain.Role;
import cn.casair.dto.RoleDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * 角色表
 * roleid 改为id
 * 添加 founder, choose, is_delete
 * 添加4个通用字段服务类
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
public interface RoleService extends IService<Role> {

    /**
     * 根据用户id获取用户角色
     *
     * @param userId 用户id
     * @return java.util.List<cn.casair.dto.RoleDTO>
     * <AUTHOR>
     * @date 2021/8/27
     **/
    List<RoleDTO> getUserRoleByUserId(String userId);

    /**
     * 创建角色表
     * roleid 改为id
     * 添加 founder, choose, is_delete
     * 添加4个通用字段
     *
     * @param RoleDTO
     * @return
     */
    RoleDTO createRole(RoleDTO RoleDTO);

    /**
     * 修改角色表
     * roleid 改为id
     * 添加 founder, choose, is_delete
     * 添加4个通用字段
     *
     * @param RoleDTO
     * @return
     */
    Optional<RoleDTO> updateRole(RoleDTO RoleDTO);

    /**
     * 查询角色表
     * roleid 改为id
     * 添加 founder, choose, is_delete
     * 添加4个通用字段详情
     *
     * @param id
     * @return
     */
    RoleDTO getRole(Integer id);

    /**
     * 删除角色表
     * roleid 改为id
     * 添加 founder, choose, is_delete
     * 添加4个通用字段
     *
     * @param id
     */
    void deleteRole(Integer id);

    /**
     * 批量删除角色表
     * roleid 改为id
     * 添加 founder, choose, is_delete
     * 添加4个通用字段
     *
     * @param ids
     */
    void deleteRole(List<Integer> ids);

    /**
     * 分页查询角色表
     * roleid 改为id
     * 添加 founder, choose, is_delete
     * 添加4个通用字段
     *
     * @param RoleDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage findPage(RoleDTO RoleDTO, Long pageNumber, Long pageSize);


    /**
     * 查询所有角色表
     *
     * @return
     */
    List<RoleDTO> findAll();

    /**
     * 给角色分配菜单
     *
     * @param roleId
     * @param menuIds
     * @return void
     * <AUTHOR> yanglei 2020/5/12 22:48
     */
    void assignMenu(Integer roleId, List<Integer> menuIds);

    /**
     * @param key
     * @return
     * @Description: 查询除传递key外的角色
     * @version: 1.0
     * @author: liuchenghao
     * @createDate: 2020/5/25 15:23
     */
    List<RoleDTO> listByNeKey(String key);

    /**
     * 根据ids获取id
     *
     * @param ids
     * @return
     */
    List<RoleDTO> getByRoleIds(List<String> ids);

    /**
     * 查询所有角色
     *
     * @return
     */
    List<RoleDTO> selectList();

    String getRolesByIds(List<String> id);
}
