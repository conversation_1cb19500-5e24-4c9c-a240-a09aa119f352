package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.ArchivesEnum;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.RandomUtil;
import cn.casair.domain.HrArchivesDetail;
import cn.casair.domain.HrArchivesManage;
import cn.casair.domain.HrTalentStaff;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrArchivesManageExport;
import cn.casair.mapper.HrArchivesDetailMapper;
import cn.casair.mapper.HrArchivesManageMapper;
import cn.casair.repository.HrArchivesBringRepository;
import cn.casair.repository.HrArchivesDetailRepository;
import cn.casair.repository.HrArchivesManageRepository;
import cn.casair.repository.HrTalentStaffRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrArchivesManageService;
import cn.casair.service.HrClientService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.component.asynchronous.ArchivesManageComponent;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 档案管理服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrArchivesManageServiceImpl extends ServiceImpl<HrArchivesManageRepository, HrArchivesManage> implements HrArchivesManageService {

    /**
     * 文件访问地址
     */
    @Value("${minio.excelPrefix}")
    private String excelPrefix;

    private final HrClientService hrClientService;
    private final SysOperLogService sysOperLogService;
    private final HrArchivesBringRepository hrArchivesBringRepository;
    private final HrArchivesManageRepository hrArchivesManageRepository;
    private final HrArchivesManageMapper hrArchivesManageMapper;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final ArchivesManageComponent archivesManageComponent;
    private final HrAppendixService hrAppendixService;
    private final RedisCache redisCache;
    private final HrArchivesDetailRepository hrArchivesDetailRepository;
    private final HrArchivesDetailMapper hrArchivesDetailMapper;

    @Override
    public void updateStateByIds(List<String> ids, Integer status) {
        if (ids == null || ids.size() == 0) {
            throw new CommonException("请至少选择一条数据！");
        }
        if (status == null) {
            throw new CommonException("请选择修改状态");
        }
        List<HrArchivesManage> hrArchivesManageList = this.hrArchivesManageRepository.selectListByIds(ids);
        this.hrArchivesManageRepository.updateStateByIds(ids, status);

        String archivesStateNew = ArchivesEnum.ArchivesState.getValueByKey(status);
        StringBuilder sb = new StringBuilder("批量修改档案状态：");
        hrArchivesManageList.forEach(ls -> {
            String archivesStateOld = ArchivesEnum.ArchivesState.getValueByKey(ls.getArchivesStatus());
            sb.append("档案名称：")
                    .append(ls.getArchivesName())
                    .append(", 档案编号：")
                    .append(ls.getArchivesNum())
                    .append(", 档案状态：")
                    .append(archivesStateOld)
                    .append("->")
                    .append(archivesStateNew)
            ;
        });
        this.sysOperLogService.insertSysOper(
                ModuleTypeEnum.ARCHIVES.getValue(),
                BusinessTypeEnum.UPDATE.getKey(),
                sb.toString(),
                JSON.toJSONString(ids),
                null,
                null,
                null,
                SecurityUtils.getCurrentUser().get()
        );
    }

    @Override
    public HrArchivesManageDTO getHrArchives(String id) {
        HrArchivesManageDTO hrArchivesManageDTO = this.hrArchivesManageRepository.getHrArchivesById(id);
        hrArchivesManageDTO.setHrArchivesDetailList(this.hrArchivesDetailRepository.getDetailByArchivesId(hrArchivesManageDTO.getId()));
        return hrArchivesManageDTO;
    }

    /**
     * 分页查询档案管理
     *
     * @param hrArchivesManageDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrArchivesManageDTO> findPage(HrArchivesManageDTO hrArchivesManageDTO, Long pageNumber, Long pageSize) {
        Page<HrArchivesManage> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        IPage<HrArchivesManageDTO> iPage = this.hrArchivesManageRepository.selectArchivesManagePage(page, hrArchivesManageDTO, clientIds);
        iPage.getRecords().forEach(ls -> {
            ls.setHrArchivesDetailList(this.hrArchivesDetailRepository.getDetailByArchivesId(ls.getId()));
            ls.setArchivesTypeStr(ArchivesEnum.ArchivesType.getValueByKey(ls.getArchivesType()));
            ls.setArchivesStatusStr(ArchivesEnum.ArchivesState.getValueByKey(ls.getArchivesStatus()));
        });
        return iPage;
    }

    /**
     * 创建档案管理
     *
     * @param hrArchivesManageDTO
     * @return
     */
    @Override
    public HrArchivesManageDTO createHrArchivesManage(HrArchivesManageDTO hrArchivesManageDTO) {
        // 检查档案编号是否存在
        int count = this.hrArchivesManageRepository.selectCountByArchivesNum(hrArchivesManageDTO.getArchivesNum());
        if (count > 0) {
            throw new CommonException("档案编号已经存在！");
        }
        // 保存档案信息
        if (hrArchivesManageDTO.getArchivesType().equals(ArchivesEnum.ArchivesType.STAFF.getKey())) {
            hrArchivesManageDTO.setClientId(null);
        }
        HrArchivesManage hrArchivesManage = this.hrArchivesManageMapper.toEntity(hrArchivesManageDTO);
        this.hrArchivesManageRepository.insert(hrArchivesManage);

        if (!hrArchivesManageDTO.getHrArchivesDetailList().isEmpty()) {
            hrArchivesManageDTO.getHrArchivesDetailList().forEach(ls -> {
                // 保存档案明细
                HrArchivesDetail hrArchivesDetail = this.hrArchivesDetailMapper.toEntity(ls);
                hrArchivesDetail.setArchivesId(hrArchivesManage.getId());
                hrArchivesDetail.setState(ArchivesEnum.DetailState.IN_GEAR.getKey());
                this.hrArchivesDetailRepository.insert(hrArchivesDetail);
                // 保存档案明细附件
                ls.getAppendixList().forEach(appendix -> {
                    this.hrAppendixService.saveAppendixUnion(appendix.getId(), hrArchivesDetail.getId());
                });
            });
        }
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
                ModuleTypeEnum.ARCHIVES.getValue(),
                BusinessTypeEnum.INSERT.getKey(),
                JSON.toJSONString(hrArchivesManageDTO),
                HrArchivesManageDTO.class,
                null,
                JSON.toJSONString(hrArchivesManage)
        );
        return this.hrArchivesManageMapper.toDto(hrArchivesManage);
    }

    /**
     * 修改档案管理
     *
     * @param hrArchivesManageDTO
     * @return
     */
    @Override
    public Optional<HrArchivesManageDTO> updateHrArchivesManage(HrArchivesManageDTO hrArchivesManageDTO) {
        return Optional.ofNullable(this.hrArchivesManageRepository.selectById(hrArchivesManageDTO.getId()))
            .map(roleTemp -> {
                // 档案编号唯一校验
                int count = this.hrArchivesManageRepository.getCountByArchivesNum(roleTemp.getId(), hrArchivesManageDTO.getArchivesNum());
                if (count > 0) {
                    throw new CommonException("该档案编号已经存在！");
                }
                HrArchivesManage hrArchivesManage = this.hrArchivesManageMapper.toEntity(hrArchivesManageDTO);
                this.hrArchivesManageRepository.updateById(hrArchivesManage);
                log.info("Update HrArchivesManage:{}", hrArchivesManageDTO);
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                        ModuleTypeEnum.ARCHIVES.getValue(),
                        BusinessTypeEnum.UPDATE.getKey(),
                        JSON.toJSONString(hrArchivesManageDTO),
                        HrArchivesManageDTO.class,
                        null,
                        JSON.toJSONString(roleTemp),
                        JSON.toJSONString(hrArchivesManageDTO),
                        null,
                        HrArchivesManageDTO.class
                );
                return hrArchivesManageDTO;
            });
    }

    /**
     * 批量删除档案管理
     *
     * @param ids
     */
    @Override
    public void deleteHrArchivesManage(List<String> ids) {
        log.info("Delete HrArchivesManages:{}" , ids);
        if (ids.isEmpty()) {
            throw new CommonException("请至少选择一条数据！");
        }
        this.hrArchivesManageRepository.deleteBatchIds(ids);
        // 删除明细
        this.hrArchivesDetailRepository.deleteBatchByArchivesId(ids);
        // 删除调入调出
        this.hrArchivesBringRepository.deleteBatchByArchivesId(ids);
        // 操作日志
        List<HrArchivesManage> hrArchivesManageList = this.hrArchivesManageRepository.selectListByIds(ids);
        StringBuilder sb = new StringBuilder("档案信息删除：[");
        hrArchivesManageList.forEach(ls -> sb.append("档案编号：").append(ls.getArchivesNum()).append("，档案名称：").append(ls.getArchivesName()).append("；"));
        sb.append("]");
        this.sysOperLogService.insertSysOper(
                ModuleTypeEnum.ARCHIVES.getValue(),
                BusinessTypeEnum.DELETE.getKey(),
                sb.toString(),
                JSON.toJSONString(ids),
                null,
                null,
                null,
                null
        );
    }


    /**
     * 导出档案 todo 测试
     *
     * @param
     * @param hrArchivesManageDTO
     * @return
     * @date 2021/9/6
     */
    @Override
    public String exportArchives(HrArchivesManageDTO hrArchivesManageDTO, HttpServletResponse httpServletResponse) {
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        List<HrArchivesManageExport> list = this.hrArchivesManageRepository.selectExportListByIds(hrArchivesManageDTO,clientIds);
        if (list.isEmpty()) {
            throw new CommonException("未查找到相关数据！");
        }
        List<String> ids = list.stream().map(HrArchivesManageExport::getId).collect(Collectors.toList());
        list.forEach(ls -> {
            // 获取档案明细
            List<HrArchivesDetailDTO> hrArchivesDetailList = this.hrArchivesDetailRepository.getAllArchivesDetailByArchivesId(ls.getId());
            if (!hrArchivesDetailList.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                hrArchivesDetailList.forEach(l -> sb.append(l.getName()).append(","));
                String s = sb.toString();
                ls.setDetailName(s.substring(0, s.length() - 1));
            }
        });
        int listSize = list.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "档案信息", HrArchivesManageExport.class);
        // 操作日志
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.ARCHIVES.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        // ExcelUtils.exportExcel(list, "档案信息", HrArchivesManageExport.class, httpServletResponse);
        return fileUrl;
    }

    /**
     * 分页查询未建档的员工
     *
     * @param hrTalentStaffDTO
     * @param pageNumber
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2021/9/10
     */
    @Override
    public IPage<HrTalentStaffDTO> findEmployeePage(HrTalentStaffDTO hrTalentStaffDTO, Long pageNumber, Long pageSize) {
        Page<HrTalentStaff> page = new Page<>(pageNumber, pageSize);
        return this.hrArchivesManageRepository.findEmployeePage(page, hrTalentStaffDTO);
    }

    /**
     * 分页查询未建档的客户
     *
     * @param hrClientDTO
     * @param pageNumber
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2021/9/2
     */
    @Override
    public IPage<HrClientDTO> findClientPage(HrClientDTO hrClientDTO, Long pageNumber, Long pageSize) {

        Page<HrClientDTO> page = new Page<>(pageNumber, pageSize);
        return this.hrArchivesManageRepository.findClientPage(page, hrClientDTO);
    }


    /**
     * 批量导入
     *
     * @param file
     * @return
     */
    @Override
    public String importArchivesManage(MultipartFile file) {
        // 设置进度条
        JWTUserDTO user = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.ARCHIVES_IMPORT.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.archivesManageComponent.dealArchivesImport(inputStream, redisKey, user);
        return redisKey;
    }

    @Override
    public String importArchivesManageTemplate() {
        /*List<HrArchivesManageTemplate> list = new ArrayList<>();
        list.add(new HrArchivesManageTemplate()
            .setArchivesNum("DA1631070128557")
            .setArchivesType(ArchivesEnum.ArchivesType.STAFF.getKey())
            .setUnitNumber("KH1631013429892")
            .setSystemNum("SY1631066890562")
            .setArchivesLocal("xx室xx柜xx箱")
            .setArchivesStatus(ArchivesEnum.ArchivesState.ARCHIVED.getKey())
            .setArchivesDetail("劳动合同、户口本、身份证")
            .setWarehouseTime(LocalDate.parse("2021-01-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")))
        );
        list.add(new HrArchivesManageTemplate()
            .setArchivesNum("DA1631070327556")
            .setArchivesType(ArchivesEnum.ArchivesType.CLIENT.getKey())
            .setUnitNumber("KH1631013429892")
            .setSystemNum(null)
            .setArchivesLocal("xx室xx柜xx箱")
            .setArchivesStatus(ArchivesEnum.ArchivesState.ARCHIVED.getKey())
            .setArchivesDetail("客户协议")
            .setWarehouseTime(LocalDate.parse("2021-01-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")))
        );
        ExcelUtils.exportExcelWithNoHeader(list, "档案管理导入模板", "sheet1", HrArchivesManageTemplate.class, response);*/
        return excelPrefix + "档案管理导入模板.xlsx";
    }

    @Override
    public int updateArchivesDetailAppendix(HrArchivesDetailDTO hrArchivesDetailDTO) {
        if (StringUtils.isBlank(hrArchivesDetailDTO.getId())) {
            throw new CommonException("档案明细id不能为空！");
        }
        hrArchivesDetailDTO.getAppendixList().forEach(ls -> this.hrAppendixService.saveAppendixUnion(ls.getId(), hrArchivesDetailDTO.getId()));
        return hrArchivesDetailDTO.getAppendixList().size();
    }

    @Override
    public HrArchivesManageDTO getStaffArchives(String clientId, String staffId) {
        HrArchivesManageDTO hrArchivesManageDTO = this.hrArchivesManageRepository.getArchivesByClientIdAndStaffId(staffId);
        // 检查员工是否存在档案
        if (hrArchivesManageDTO == null) {
            HrTalentStaffDTO hrTalentStaff = this.hrTalentStaffRepository.getStaffInfoById(clientId, staffId);
            if (hrTalentStaff == null) {
                throw new CommonException("未查询到该员工信息，请确认该员工为正常状态");
            }
            // 创建档案
            HrArchivesManage hrArchivesManage = new HrArchivesManage();
            hrArchivesManage.setStaffId(staffId);
            hrArchivesManage.setArchivesNum("DA-" + RandomStringUtils.randomNumeric(13));
            // hrArchivesManage.setEmpUnit(hrTalentStaff.getClientName());
            hrArchivesManage.setArchivesName(hrTalentStaff.getName());
            hrArchivesManage.setArchivesType(ArchivesEnum.ArchivesType.STAFF.getKey());
            hrArchivesManage.setArchivesStatus(ArchivesEnum.ArchivesState.ARCHIVED.getKey());
            hrArchivesManage.setWarehouseTime(LocalDate.now());
            this.hrArchivesManageRepository.insert(hrArchivesManage);
            hrArchivesManageDTO = this.hrArchivesManageMapper.toDto(hrArchivesManage);
        }
        return hrArchivesManageDTO;
    }
}


